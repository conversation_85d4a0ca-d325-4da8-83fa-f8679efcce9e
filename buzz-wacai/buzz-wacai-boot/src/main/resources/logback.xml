<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="STDOUT" class="ch.qos.logback.core.ConsoleAppender">
        <layout class="ch.qos.logback.classic.PatternLayout">
            <pattern>%date{ISO8601} %-5level [%thread] %logger{32}:%L - [%X{uid}] [%X{traceId}] [%X{custom}] %message%n</pattern>
        </layout>
    </appender>
    <appender name="rollingFile" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME:-${user.dir}}/app.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME:-${user.dir}}/app-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize> <!--没有针对一天内最大文件数量的限制这样的功能支持-->
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>3</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%date{ISO8601} %-5level [%thread] %logger{32}:%L - [%X{uid}] [%X{traceId}] [bds=%X{bdsTraceId}] %message%n</pattern>
        </encoder>
    </appender>

    <appender name="RED-ALERT-LOG" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <File>${LOG_HOME:-${user.dir}}/monitor/metrics.log</File>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${LOG_HOME:-${user.dir}}/monitor/metrics-%d{yyyy-MM-dd}.%i.log</fileNamePattern>
            <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
                <maxFileSize>100MB</maxFileSize> <!--没有针对一天内最大文件数量的限制这样的功能支持-->
            </timeBasedFileNamingAndTriggeringPolicy>
            <maxHistory>3</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>%message%n</pattern>
        </encoder>
    </appender>

    <root>
        <level value="INFO"/>
        <appender-ref ref="STDOUT"/>
    </root>

    <logger name="com.wacai.pt.redalert.api.reporter.LogReporter" level="INFO" additivity="false">
        <appender-ref ref="RED-ALERT-LOG" />
    </logger>

    <logger name="com.wacai.pt.redalert.api.collector" level="OFF" />
</configuration>