package com.buzz.wacai.boot.controller;

import com.wacai.common.redis.RedisClient;
import com.wacai.common.redis.RedisCluster;
import com.wacai.common.redis.RedisException;
import com.wacai.common.redis.RedisTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class IndexController {

    @Autowired
    private RedisTemplate redisTemplate;

    @RequestMapping("/")
    @ResponseBody
    public String index() throws RedisException {
        return "ok" + redisTemplate.get("test");
    }
}
