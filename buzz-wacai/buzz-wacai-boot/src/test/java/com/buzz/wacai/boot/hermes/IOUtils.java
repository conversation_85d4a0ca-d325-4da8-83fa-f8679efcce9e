package com.buzz.wacai.boot.hermes;


import java.io.*;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * clone from dubbo IOUtils, apache.commons.io.IOUtils
 *
 *
 */
public class IOUtils {

    public static List<String> readLines(String file) throws IOException {
        return readLines(new File(file));
    }
    /**
     * read lines.
     *
     * @param file file.
     * @return lines.
     * @throws IOException
     */
    public static List<String> readLines(File file) throws IOException {
        if (file == null || !file.exists() || !file.canRead())
            return Collections.EMPTY_LIST;

        return readLines(new FileInputStream(file));
    }

    /**
     * read lines.
     *
     * @param is input stream.
     * @return lines.
     * @throws IOException
     */
    public static List<String> readLines(InputStream is) throws IOException {
        List<String> lines = new ArrayList<String>();
        BufferedReader reader = new BufferedReader(new InputStreamReader(is));
        try {
            String line;
            while ((line = reader.readLine()) != null)
                lines.add(line);
            return lines;
        } finally {
            reader.close();
        }
    }

    public static String readFile(File file) throws IOException {
        return readFile(file,"UTF-8");
    }
    public static String readFile(String file) throws IOException {
        return readFile(new File(file));
    }

    public static String readFile(File file, String encode) throws IOException {
        if (!file.exists() || !file.isFile()) {
            return null;
        }

        InputStream is = null;
        try {
            is = new FileInputStream(file);
            return toString(is, encode);
        } finally {
            try {
                if (null != is) {
                    is.close();
                }
            } catch (IOException ioe) {
            }
        }
    }

    public static void deleteFile(File file) throws IOException {
        if (file == null) {
            return;
        }

        if (file.isDirectory()) {
            cleanDirectory(file);
        }
        file.delete();
    }

    public static void cleanDirectory(File directory) throws IOException {
        if (!directory.exists()) {
            String message = directory + " does not exist";
            throw new RuntimeException(message);
        }

        if (!directory.isDirectory()) {
            String message = directory + " is not a directory";
            throw new RuntimeException(message);
        }

        File[] files = directory.listFiles();
        if (files == null) {  // null if security restricted
            throw new IOException("Failed to list contents of " + directory);
        }

        IOException exception = null;
        for (File file : files) {
            try {
                deleteFile(file);
            } catch (IOException ioe) {
                exception = ioe;
            }
        }

        if (null != exception) {
            throw exception;
        }
    }

    public static void writeFile(String file, String content) throws IOException {
        writeFile(new File(file),content,"utf-8");
    }

    public static void writeFile(File file, String content, String encode) throws IOException {
        if (content == null) {
            throw new RuntimeException("content can not be null");
        }
        OutputStream os = null;
        try {
            os = new FileOutputStream(file);
            os.write(content.getBytes(encode));
        } finally {
            if (null != os) {
                os.close();
            }
        }
    }

    static public String toString(InputStream input, String encoding) throws IOException {
        return (null == encoding) ? toString(new InputStreamReader(input))
                : toString(new InputStreamReader(input, encoding));
    }

    static public String toString(Reader reader) throws IOException {
        CharArrayWriter sw = new CharArrayWriter();
        copy(reader, sw);
        return sw.toString();
    }

    static public long copy(Reader input, Writer output) throws IOException {
        char[] buffer = new char[1 << 12];
        long count = 0;
        for (int n = 0; (n = input.read(buffer)) >= 0; ) {
            output.write(buffer, 0, n);
            count += n;
        }
        return count;
    }

}
