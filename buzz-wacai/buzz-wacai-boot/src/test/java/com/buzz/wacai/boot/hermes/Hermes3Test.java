package com.buzz.wacai.boot.hermes;

import com.wacai.hermes.agent.config.HermesAppConfig;
import com.wacai.hermes.agent.config.HermesConsumerConfig;
import com.wacai.hermes.agent.config.HermesProducerConfig;
import com.wacai.hermes.agent.config.options.AccessModel;
import com.wacai.hermes.agent.consumer.HermesConsumer;
import com.wacai.hermes.agent.consumer.protocol.HermesCompositeConsumer;
import com.wacai.hermes.agent.producer.HermesProducer;
import com.wacai.hermes.agent.producer.ProducerBuilder;
import com.wacai.hermes.agent.producer.impl.HermesHttpProducer;
import com.wacai.hermes.bridge.schema.resp.Message;
import com.wacai.hermes.common.message.HermesMessage;
import com.wacai.hermes.common.message.MessageMeta;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CountDownLatch;

/**
 * 订阅binlog详见CanalServerTest
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class Hermes3Test {

    private static final CountDownLatch latch = new CountDownLatch(1);

    @Test
    public void testDirectModel() throws InterruptedException {
        HermesConsumerConfig config = new HermesConsumerConfig();
        config.setAccessModel(AccessModel.DIRECT);
        config.setTopic("bairen.test.2000");
        config.setGroupId("bairen.test");

        SinkerConsumer consumer = new SinkerConsumer(config);
        consumer.start();

        Thread.sleep(1000);
        consumer.stop();

        latch.await();

    }

    @Test
    public void testSend() {
        HermesAppConfig appConfig = HermesAppConfig.get();
        appConfig.setAppName("biz-app"); // uav上的应用

        //appConfig.setSecretKey("fC5wEUESHRWUm5LSCsTwAbqWvCMbIz3opRd+mOqL+OJx+e79/trR1fOEjtGKAB1O0znGU50HCDWXWYoaDsZV4w==");

        HermesProducerConfig config = new HermesProducerConfig();
        config.setAccessModel(AccessModel.DIRECT);  //必须设置
        HermesProducer textProducer = HermesHttpProducer.get(config); //ProducerBuilder.builder().config(config).build();

        String topic = "delete.friend.topic"; // 先在uav上创建topic
        String msg = "test";

        HermesMessage message = HermesMessage.builder() //
                .setTopic(topic) // 消息主题
                .setData(msg.getBytes()) // 消息内容
                .build();

        // 单个发送
        try {
            MessageMeta messageMeta = textProducer.produce(message);
            System.out.println("messageMeta=" + messageMeta);
        } catch (Exception e) {
            e.printStackTrace();
        }


        System.exit(-1);

    }


    @Test
    public void testFetch() {
        HermesAppConfig appConfig = HermesAppConfig.get();
        appConfig.setAppName("biz-app"); // uav上的应用

        HermesConsumerConfig config = new HermesConsumerConfig();
        config.setTopic("hchf.pass.user.login");
        config.setGroupId("biz-app-group");
        //config.setAccessModel(AccessModel.HTTP_PROXY);  //必须设置

        HermesConsumer consumer = new HermesCompositeConsumer(config) {
            @Override
            public void onMessageReceived(Message message) {
                String key = new String(message.getKey(), StandardCharsets.UTF_8);
                String value = new String(message.getValue(), StandardCharsets.UTF_8);
                System.out.println(String.format("key=%s,value=%s", key, value));
            }
        };

        consumer.start();


    }


    @Test
    public void testFetchLocal() throws Exception {
        HermesAppConfig appConfig = HermesAppConfig.get();
        appConfig.setAppName("biz-app"); // uav上的应用
        appConfig.setCenter("http://localhost:8081");
        HermesConsumerConfig config = new HermesConsumerConfig();
        config.setTopic("manyou.test");
        config.setGroupId("biz-app-group");

        HermesConsumer consumer = new HermesCompositeConsumer(config) {
            @Override
            public void onMessageReceived(Message message) {
                String key = new String(message.getKey(), StandardCharsets.UTF_8);
                String value = new String(message.getValue(), StandardCharsets.UTF_8);
                System.out.println(String.format("key=%s,value=%s", key, value));
            }
        };

        consumer.start();
        System.in.read();

    }

    @Test
    public void testMarsClient() throws IOException, InterruptedException {
        HermesConsumerConfig config = new HermesConsumerConfig();
//        config.setGroupId("zion-neo");
//        config.setTopic("db.wac_platform.config_entry");

        config.setGroupId("drc-sinker");
        config.setTopic("db.user_sharding.ucenter_account");

        HermesConsumer consumer = new MyHermesConsumer(config);
        consumer.start();
        latch.await();

    }

    @Test
    public void testSendMsg() throws Exception {
        //center设置为本地
        HermesAppConfig.get().setCenter("http://localhost:8081");
        HermesAppConfig.get().setAppName("drc-streams");

        HermesProducerConfig config = new HermesProducerConfig();
        config.setTopic("db.xhy_wac_loan_goblin.gbl_collection_case_ext");
        config.setAccessModel(AccessModel.DIRECT);
        HermesProducer hermesProducer = ProducerBuilder.builder()
                .config(config).build();
        HermesMessage message = HermesMessage.builder()
                .setTopic("db.xhy_wac_loan_goblin.gbl_collection_case_ext")
                .setData(("test").getBytes())
                .build();
        hermesProducer.produce(message);
    }

    @Test
    public void testSendLegionLog() throws Exception {

        String topic = "middleware.log.test";
        HermesProducerConfig config = new HermesProducerConfig();
        config.setTopic(topic);
        config.setAccessModel(AccessModel.DIRECT);
        HermesProducer hermesProducer = ProducerBuilder.builder()
                .config(config).build();

        List<String> list = IOUtils.readLines("/data/program/logs/legion-sinker-log/k2.ingress.log");
        String data = list.get(0);
        System.out.println("data=" + data);

        //Executors.newFixedThreadPool(2);

        for (int i = 0; i < 1000; ++i) {
            List<HermesMessage> msgList = new ArrayList();
            for (int j = 0; j < 100; ++j) {
                HermesMessage message = HermesMessage.builder().setTopic(topic).setData(data.getBytes()).build();
                msgList.add(message);
            }
            long begin = System.currentTimeMillis();
            hermesProducer.produce(msgList);
            long end = System.currentTimeMillis();
            log.info("send message cost:" + (end - begin));
        }

    }

    @Test
    public void testLegionSinker() throws Exception {
        //center设置为本地
        HermesAppConfig.get().setCenter("http://localhost:8081");
        HermesAppConfig.get().setAppName("drc-streams");

        HermesConsumerConfig config = new HermesConsumerConfig();
        //config.setClusterId("test");
        config.setGroupId("iceye-event-3");
        String topic = "db.xhy_wac_loan_goblin.gbl_collection_case_ext";
        config.setReset(0);
        config.setTopic(topic);
        HermesConsumer consumer = new MyHermesConsumer(config);
        consumer.start();


        System.out.println("started");
        latch.await();
    }

    private static class MyHermesConsumer extends HermesCompositeConsumer {

        public MyHermesConsumer(HermesConsumerConfig config) {
            super(config);
        }

        @Override
        public void onMessageReceived(List<Message> messages) {
            log.info("receive messages total:{}", messages.size());
            for (Message message : messages) {
                try {
                    String value = new String(message.getValue(), StandardCharsets.UTF_8);
                    log.info("onMessageReceived offset={},partition={},value={}", message.getOffset(),
                            message.getPartition(), value);
                } catch (Exception e) {
                    log.error("error", e);
                }
                break;
            }
            latch.countDown();
        }
    }

    private static class SinkerConsumer extends HermesCompositeConsumer {
        public SinkerConsumer(HermesConsumerConfig config) {
            super(config);
        }

        public void onMessageReceived(List<Message> messages) {
            log.info("receive offset={},size={}", messages.get(0).getOffset(), messages.size());

            for (Message message : messages) {
                log.info("process offset={}", message.getOffset());
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            }
        }
    }

}