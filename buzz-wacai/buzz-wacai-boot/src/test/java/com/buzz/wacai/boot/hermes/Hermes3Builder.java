package com.buzz.wacai.boot.hermes;

import com.wacai.hermes.agent.config.HermesConsumerConfig;
import com.wacai.hermes.agent.consumer.HermesConsumer;
import com.wacai.hermes.agent.consumer.protocol.HermesCompositeConsumer;
import com.wacai.hermes.bridge.schema.resp.Message;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class Hermes3Builder {

    public static Hermes3Builder builder() {
        return new Hermes3Builder();
    }

    private String topic = "bairen.test";
    private String group = "bairen-group";
    private String consumerId;
    private boolean batch=true;

    public Hermes3Builder topic(String topic) {
        this.topic = topic;
        return this;
    }

    public Hermes3Builder group(String group) {
        this.group = group;
        return this;
    }

    public Hermes3Builder consumerId(String consumerId) {
        this.consumerId = consumerId;
        return this;
    }

    public Hermes3Builder batch(boolean batch){
        this.batch = batch;
        return this;
    }

    public HermesConsumer buildHermesConsumer() {
        HermesConsumerConfig config = new HermesConsumerConfig();
        config.setGroupId(group);
        config.setTopic(topic);
        config.setFetchMax(3);
        //config.setMaxAttempts(0);
        //config.setMaxIntervalGap(0);
        config.setConsumerId(consumerId);
        if(batch){
            return new BatchHermesConsumer(config);
        }else{
            return new SingleHermesConsumer(config);
        }
    }



    private class SingleHermesConsumer extends HermesCompositeConsumer {

        public SingleHermesConsumer(HermesConsumerConfig config) {
            super(config);
        }

        public void onMessageReceived(Message message) {
            MessageProcessor.processMessage(message);
        }
    }

    private class BatchHermesConsumer extends HermesCompositeConsumer {

        public BatchHermesConsumer(HermesConsumerConfig config) {
            super(config);
        }

        public void onMessageReceived(List<Message> messages) {
            messages.stream().forEach(MessageProcessor::processMessage);
        }
    }
    private static class MessageProcessor {

        private static AtomicInteger counter = new AtomicInteger(1);

        private static void processMessage(Message message) {
            String value = new String(message.getValue(), StandardCharsets.UTF_8);
            log.info("onMessageReceived offset={},partition={},header={}, value={}",  message.getOffset(),
                    message.getPartition(), message.getHeaders(), value);

            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            if (counter.getAndIncrement() % 3 != 0) {
                throw new RuntimeException("Murphy error! offset=" + message.getOffset());
            }

            try {
                Files.write(Paths.get("/tmp/hermes.txt"), ("offset=" + message.getOffset() + "\r\n").getBytes(), StandardOpenOption.APPEND);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

}
