<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<!-- <parent>
		<groupId>com.buzz</groupId>
		<artifactId>buzz-wacai</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent> -->
	
	<artifactId>buzz-wacai-boot</artifactId>
	
    <parent>
        <groupId>com.wacai</groupId>
        <artifactId>wacai-boot-starter-parent</artifactId>
        <version>2.5.2</version>
        <relativePath/>
    </parent>
       
     <properties>
         <ninja.client.version>1.2.0</ninja.client.version>
         <netty4.version>4.1.43.Final</netty4.version>
         <spring-boot.version>2.1.4.RELEASE</spring-boot.version>
   </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>io.netty</groupId>
                <artifactId>netty-all</artifactId>
                <version>${netty4.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.wacai</groupId>
            <artifactId>wacai-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>com.wacai</groupId>
            <artifactId>wacai-boot-starter-dubbo</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
        </dependency>

		<dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpasyncclient</artifactId>
             <version>4.1.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.3</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
        </dependency>
    
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
        </dependency>

        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>com.wacai</groupId>
            <artifactId>ninja-client</artifactId>
            <version>1.1.9</version>
        </dependency>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.10</version>
            <scope>provided</scope>
        </dependency>

        <dependency>
            <groupId>com.wacai.middleware</groupId>
            <artifactId>hermes-agent</artifactId>
            <version>3.4.0</version>
        </dependency>

        <dependency>
            <groupId>com.wacai</groupId>
            <artifactId>spring-boot-starter-secure-ds</artifactId>
            <version>1.3.6</version>
        </dependency>

        <dependency>
            <groupId>com.wacai</groupId>
            <artifactId>wacai-boot-starter-cache</artifactId>
            <version>1.3.0</version>
        </dependency>

    </dependencies> 	
    
</project>