/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.loan.trike.expression;

public enum Operator {
	EQ("=", "等于"),

	NE("!=", "不等于"),

	LT("<", "小于"),

	GT(">", "大于"),

	GT_EQ(">=", "大于等于"),

	LT_EQ("<=", "小于等于"),

	IN("in", "多选"),

	NOT_IN("not_in", "不在所选项内"),

	BETWEEN("between", "在两个值之间");

	private String label;
	private String description;

	private Operator(String label, String description) {
		this.label = label;
		this.description = description;
	}

	public String getLabel() {
		return this.label;
	}

	public String getDescription() {
		return this.description;
	}
}
