/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.loan.trike.expression;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

@SuppressWarnings("rawtypes")
public final class ThresholdParserFactory {
	private static final ConcurrentMap<ThresholdType, ThresholdParser> PARSERS = new ConcurrentHashMap<ThresholdType, ThresholdParser>();
	private static final ThreadLocal<List<SimpleDateFormat>> DATE_FORMAT_LOCAL = ThreadLocal
			.withInitial(() -> Arrays.asList(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
					new SimpleDateFormat("yyyy-MM-dd"), new SimpleDateFormat("HH:mm:ss")));

	private ThresholdParserFactory() {
	}

	public static ThresholdParser getParser(ThresholdType thresholdType) {
		return (ThresholdParser) PARSERS.get( thresholdType);
	}

	public static void register(ThresholdType type, ThresholdParser parser) {
		ThresholdParserFactory.register(type, parser, false);
	}

	public static void register(ThresholdType type, ThresholdParser parser, boolean replace) {
		if (replace) {
			PARSERS.put(type, parser);
		} else {
			PARSERS.putIfAbsent(type, parser);
		}
	}

	static {
		ThresholdParserFactory.register(ThresholdType.BOOLEAN, new BooleanParser());
		ThresholdParserFactory.register(ThresholdType.DATE, new DateParser());
		ThresholdParserFactory.register(ThresholdType.NUMBER, new NumberParser());
		ThresholdParserFactory.register(ThresholdType.STRING, new StringParser());
	}

	static class StringParser implements ThresholdParser<String> {
		StringParser() {
		}

		public String parse(String value) {
			return value;
		}
	}

	static class DateParser implements ThresholdParser<Date> {
		DateParser() {
		}

		public Date parse(String value) {
			for (SimpleDateFormat format : DATE_FORMAT_LOCAL.get()) {
				try {
					return format.parse(value);
				} catch (ParseException parseException) {
				}
			}
			throw new IllegalArgumentException();
		}
	}

	static class NumberParser implements ThresholdParser<BigDecimal> {
		NumberParser() {
		}

		public BigDecimal parse(String value) {
			return new BigDecimal(value);
		}
	}

	static class BooleanParser implements ThresholdParser<Boolean> {
		BooleanParser() {
		}

		public Boolean parse(String value) {
			return Boolean.valueOf(value);
		}
	}
}
