/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSON
 */
package com.wacai.loan.trike.expression;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import com.alibaba.fastjson.JSON;

public final class OperatorEvaluatorFactory {
    private static final int BETWEEN_THRESHOLD_NUMS = 2;
    private static final ConcurrentMap<Operator, OperatorEvaluator> EVALUATORS = new ConcurrentHashMap<Operator, OperatorEvaluator>();

    public static OperatorEvaluator getEvaluator(Operator operator) {
        return (OperatorEvaluator)EVALUATORS.get(operator);
    }

    public static void register(Operator operator, OperatorEvaluator evaluator) {
        OperatorEvaluatorFactory.register(operator, evaluator, false);
    }

    public static void register(Operator operator, OperatorEvaluator evaluator, boolean replace) {
        if (replace) {
            EVALUATORS.put(operator, evaluator);
        } else {
            EVALUATORS.putIfAbsent(operator, evaluator);
        }
    }

    private static boolean equal(String value, String threshold, ThresholdType thresholdType) {
        if (Objects.equals(value, threshold)) {
            return true;
        }
        if (Objects.nonNull(value) && Objects.nonNull(threshold)) {
            Comparable expect;
            ThresholdParser parser = ThresholdParserFactory.getParser(thresholdType);
            Comparable origin = parser.parse(value);
            return origin.compareTo(expect = parser.parse(threshold)) == 0;
        }
        return false;
    }

    private static boolean lt(String value, String threshold, ThresholdType thresholdType) {
        if (OperatorEvaluatorFactory.anyNull(new Object[]{value, threshold, thresholdType})) {
            return false;
        }
        return OperatorEvaluatorFactory.compare(value, threshold, thresholdType) < 0;
    }

    private static boolean lte(String value, String threshold, ThresholdType thresholdType) {
        if (OperatorEvaluatorFactory.anyNull(new Object[]{value, threshold, thresholdType})) {
            return false;
        }
        return OperatorEvaluatorFactory.compare(value, threshold, thresholdType) <= 0;
    }

    private static boolean anyNull(Object ... objs) {
        if (Objects.isNull(objs)) {
            return true;
        }
        for (Object obj : objs) {
            if (!Objects.isNull(obj)) continue;
            return true;
        }
        return false;
    }

    private static int compare(String value, String threshold, ThresholdType thresholdType) {
        ThresholdParser parser = ThresholdParserFactory.getParser(thresholdType);
        Comparable origin = parser.parse(value);
        Comparable expect = parser.parse(threshold);
        return origin.compareTo(expect);
    }

    private static boolean in(String value, String threshold, ThresholdType thresholdType) {
        if (OperatorEvaluatorFactory.anyNull(new Object[]{value, threshold, thresholdType})) {
            return false;
        }
        List<String> thresholds = JSON.parseArray(threshold, String.class);
        ThresholdParser parser = ThresholdParserFactory.getParser(thresholdType);
        Comparable origin = parser.parse(value);
        for (String item : thresholds) {
            Comparable expect = parser.parse(item);
            if (origin.compareTo(expect) != 0) continue;
            return true;
        }
        return false;
    }

    static {
        OperatorEvaluatorFactory.register(Operator.EQ, new EqEvaluator());
        OperatorEvaluatorFactory.register(Operator.NE, new NeEvaluator());
        OperatorEvaluatorFactory.register(Operator.LT, new LtEvaluator());
        OperatorEvaluatorFactory.register(Operator.GT, new GtEvaluator());
        OperatorEvaluatorFactory.register(Operator.LT_EQ, new LtEqEvaluator());
        OperatorEvaluatorFactory.register(Operator.GT_EQ, new GtEqEvaluator());
        OperatorEvaluatorFactory.register(Operator.IN, new InEvaluator());
        OperatorEvaluatorFactory.register(Operator.NOT_IN, new NotInEvaluator());
        OperatorEvaluatorFactory.register(Operator.BETWEEN, new BetweenEvaluator());
    }

    static class BetweenEvaluator
    implements OperatorEvaluator {
        BetweenEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            if (OperatorEvaluatorFactory.anyNull(new Object[]{value, threshold, thresholdType})) {
                return false;
            }
            List range = JSON.parseArray((String)threshold, String.class);
            if (range.size() != 2) {
                return false;
            }
            String low = ((String)range.get(0)).trim();
            String high = ((String)range.get(1)).trim();
            ThresholdParser parser = ThresholdParserFactory.getParser(thresholdType);
            Comparable valueObj = parser.parse(value);
            Comparable lowObj = parser.parse(low);
            Comparable highObj = parser.parse(high);
            return valueObj.compareTo(lowObj) >= 0 && valueObj.compareTo(highObj) <= 0;
        }
    }

    static class NotInEvaluator
    implements OperatorEvaluator {
        NotInEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            return !OperatorEvaluatorFactory.in(value, threshold, thresholdType);
        }
    }

    static class InEvaluator
    implements OperatorEvaluator {
        InEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            return OperatorEvaluatorFactory.in(value, threshold, thresholdType);
        }
    }

    static class GtEqEvaluator
    implements OperatorEvaluator {
        GtEqEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            return !OperatorEvaluatorFactory.lt(value, threshold, thresholdType);
        }
    }

    static class GtEvaluator
    implements OperatorEvaluator {
        GtEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            return !OperatorEvaluatorFactory.lte(value, threshold, thresholdType);
        }
    }

    static class LtEqEvaluator
    implements OperatorEvaluator {
        LtEqEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            return OperatorEvaluatorFactory.lte(value, threshold, thresholdType);
        }
    }

    static class LtEvaluator
    implements OperatorEvaluator {
        LtEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            return OperatorEvaluatorFactory.lt(value, threshold, thresholdType);
        }
    }

    static class NeEvaluator
    implements OperatorEvaluator {
        NeEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            return !OperatorEvaluatorFactory.equal(value, threshold, thresholdType);
        }
    }

    static class EqEvaluator
    implements OperatorEvaluator {
        EqEvaluator() {
        }

        @Override
        public boolean evaluate(String value, String threshold, ThresholdType thresholdType) {
            return OperatorEvaluatorFactory.equal(value, threshold, thresholdType);
        }
    }
}

