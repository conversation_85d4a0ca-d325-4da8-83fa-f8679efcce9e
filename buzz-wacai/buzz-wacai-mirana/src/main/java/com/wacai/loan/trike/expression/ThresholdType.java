/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.loan.trike.expression;

public enum ThresholdType {
    BOOLEAN("\u5e03\u5c14"),
    STRING("\u5b57\u7b26\u4e32"),
    DATE("\u65e5\u671f"),
    NUMBER("\u6570\u5b57");

    private String description;

    private ThresholdType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return this.description;
    }
}

