package com.wacai.trike.mirana.resource;

import java.util.List;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.google.common.collect.Lists;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.service.FlowVarService;
import com.wacai.trike.mirana.service.FlowVariableDTO;
import com.wacai.trike.mirana.web.model.FlowVariableModel;

import io.swagger.annotations.ApiOperation;

@RestController
@RequestMapping({"/action"})
public class FlowVariableResource {
	private static final Logger log = LoggerFactory.getLogger(FlowVariableResource.class);
	@Autowired
	private FlowVarService flowVarService;

	@ApiOperation(value = "查询Expression", httpMethod = "GET")
	@GetMapping({"/expressions"})
	public ApiResponse<List<FlowVariableModel>> expressions() {
		List<FlowVariableDTO> query = this.flowVarService.query();
		return CollectionUtils.isEmpty(query)
				? ApiResponse.success(Lists.newArrayList())
				: ApiResponse.success(query.stream().map(FlowVariableDTO::toModel).collect(Collectors.toList()));
	}
}