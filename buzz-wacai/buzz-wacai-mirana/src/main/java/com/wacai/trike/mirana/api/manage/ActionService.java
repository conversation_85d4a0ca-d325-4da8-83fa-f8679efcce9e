/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.Page
 *  com.wacai.loan.trike.common.model.Query
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 */
package com.wacai.trike.mirana.api.manage;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.loan.trike.common.model.Query;
import com.wacai.trike.mirana.api.manage.model.ActionComboModel;
import com.wacai.trike.mirana.api.manage.model.ActionModel;
import com.wacai.trike.mirana.api.manage.model.ActionQueryModel;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping(value={"/action"})
public interface ActionService {
    @GetMapping
    public ApiResponse<Page<ActionModel>> queryActions(ActionQueryModel var1);

    @GetMapping(value={"/app"})
    public ApiResponse<Page<ActionModel>> queryActions(@RequestParam(value="appId") Long var1, Query var2);

    @PostMapping(value={"/save"})
    public ApiResponse<Long> create(@RequestBody ActionModel var1);

    @PostMapping(value={"/delete/{id}"})
    public ApiResponse<Void> delete(@PathVariable(value="id") Long var1);

    @GetMapping(value={"/combo"})
    public ApiResponse<List<ActionComboModel>> combo(ActionQueryModel var1);
}

