/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.trike.mirana.api.FlowProcessor
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  com.wacai.trike.mirana.api.model.FlowProcessRequest
 *  com.wacai.trike.mirana.api.model.FlowResponse
 *  com.wacai.trike.mirana.api.model.FlowStartRequest
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.Seraph;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.FlowProcessor;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.api.model.FlowProcessRequest;
import com.wacai.trike.mirana.api.model.FlowResponse;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.repository.DelayedEventRepository;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.EventAgency;
import com.wacai.trike.mirana.metrics.DelayedEventMetrics;
import com.wacai.trike.mirana.metrics.MetricsProvider;
import com.wacai.trike.mirana.util.ObjectMappers;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
public class DelayedEventExecutor {

	private static final Logger log = LoggerFactory.getLogger(DelayedEventExecutor.class);

	@Autowired
	private DelayedEventRepository eventRepository;

	@Autowired
	private FlowProcessor flowProcessor;

	@Autowired
	private MetricsProvider metricsProvider;

	@Autowired
	private EventAgency eventAgency;

	public void execute(DelayedEventPO delayedEvent) {
		try {
			ApiResponse response;
			int updated = this.eventRepository.updateStatusByIdAndStatus(DelayedEventStatus.FIRED, delayedEvent.getId(),
					DelayedEventStatus.LOADED);
			if (updated <= 0) {
				return;
			}
			if (FlowStartRequest.class.getName().equals(delayedEvent.getType())) {
				FlowStartRequest request = ObjectMappers.mustReadValue(delayedEvent.getParam(), FlowStartRequest.class);
				request.getParam().put("startDelayTimer", null);
				response = this.flowProcessor.start(request);
				this.metricsProvider.metricsDelayedEvent(
						DelayedEventMetrics.build(request, null, DelayedEventMetrics.Category.SUBSCRIBED));
			} else if (FlowProcessRequest.class.getName().equals(delayedEvent.getType())) {
				FlowProcessRequest request = ObjectMappers.mustReadValue(delayedEvent.getParam(),
						FlowProcessRequest.class);
				response = this.flowProcessor.process(request);
				this.metricsProvider.metricsDelayedEvent(
						DelayedEventMetrics.build(request, null, DelayedEventMetrics.Category.SUBSCRIBED));
			} else {
				//NodeDelayProcessingEvent
				Class<?> type = Class.forName(delayedEvent.getType());
				AbstractFlowEvent concreteEvent = (AbstractFlowEvent) BeanUtils.instantiateClass(type);
				BeanUtils.copyProperties( delayedEvent,  concreteEvent);

				concreteEvent.setParam(
						ObjectMappers.mustReadValue(delayedEvent.getParam(), new TypeReference<Map<String, String>>() {
						}));

				this.eventAgency.agent(concreteEvent, false);

				this.metricsProvider.metricsDelayedEvent(
						DelayedEventMetrics.build(concreteEvent, null, DelayedEventMetrics.Category.SUBSCRIBED));
				response = ApiResponse.success( new FlowResponse()
						.setInstanceId(concreteEvent.getInstanceUuid()).setStatus(InstanceStatus.RUNNING));
			}
			if (response.success()) {
				log.info("successfully delay event {} of {}",  delayedEvent.getUuid(),
						 delayedEvent.getInstanceUuid());
			} else {
				log.error("failure delay event {} of {}",  delayedEvent.getUuid(),
						 delayedEvent.getInstanceUuid());
			}
		} catch (Exception e) {
			log.error("delayedEvent execute error, uuid {}",  delayedEvent.getUuid(),  e);
		}
	}
}
