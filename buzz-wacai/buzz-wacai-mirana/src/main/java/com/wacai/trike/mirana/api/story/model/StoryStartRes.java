/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.story.model;

import com.wacai.trike.mirana.api.constant.InstanceStatus;
import java.io.Serializable;

public class StoryStartRes
implements Serializable {
    private String instanceId;
    private InstanceStatus status;

    public String getInstanceId() {
        return this.instanceId;
    }

    public InstanceStatus getStatus() {
        return this.status;
    }

    public StoryStartRes setInstanceId(String instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    public StoryStartRes setStatus(InstanceStatus status) {
        this.status = status;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryStartRes)) {
            return false;
        }
        StoryStartRes other = (StoryStartRes)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$instanceId = this.getInstanceId();
        String other$instanceId = other.getInstanceId();
        if (this$instanceId == null ? other$instanceId != null : !this$instanceId.equals(other$instanceId)) {
            return false;
        }
        InstanceStatus this$status = this.getStatus();
        InstanceStatus other$status = other.getStatus();
        return !(this$status == null ? other$status != null : !((this$status)).equals(other$status));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryStartRes;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $instanceId = this.getInstanceId();
        result = result * 59 + ($instanceId == null ? 43 : $instanceId.hashCode());
        InstanceStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
        return result;
    }

    public String toString() {
        return "StoryStartRes(instanceId=" + this.getInstanceId() + ", status=" + (this.getStatus()) + ")";
    }
}

