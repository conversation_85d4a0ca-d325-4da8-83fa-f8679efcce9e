/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.edge.repository;

import com.wacai.trike.mirana.common.enums.EdgeType;
import com.wacai.trike.mirana.domain.edge.po.EdgePO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface EdgeRepository
extends JpaRepository<EdgePO, Long> {
    public List<EdgePO> findByFromIdAndFromType(Long var1, EdgeType var2);

    public List<EdgePO> findByFlowId(Long var1);
}

