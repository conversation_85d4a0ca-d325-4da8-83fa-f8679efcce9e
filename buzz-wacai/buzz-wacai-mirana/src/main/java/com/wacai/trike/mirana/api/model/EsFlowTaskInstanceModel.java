/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.model.EsNodeEventValueModel;
import com.wacai.trike.mirana.api.model.EsNodeValueModel;
import java.io.Serializable;
import java.util.List;
import java.util.Map;

public class EsFlowTaskInstanceModel
implements Serializable {
    private Long flowId;
    private String flowVersion;
    private String flowCode;
    private Map<String, EsNodeValueModel> nodes;
    private List<EsNodeEventValueModel> events;

    public Long getFlowId() {
        return this.flowId;
    }

    public String getFlowVersion() {
        return this.flowVersion;
    }

    public String getFlowCode() {
        return this.flowCode;
    }

    public Map<String, EsNodeValueModel> getNodes() {
        return this.nodes;
    }

    public List<EsNodeEventValueModel> getEvents() {
        return this.events;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setFlowVersion(String flowVersion) {
        this.flowVersion = flowVersion;
    }

    public void setFlowCode(String flowCode) {
        this.flowCode = flowCode;
    }

    public void setNodes(Map<String, EsNodeValueModel> nodes) {
        this.nodes = nodes;
    }

    public void setEvents(List<EsNodeEventValueModel> events) {
        this.events = events;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EsFlowTaskInstanceModel)) {
            return false;
        }
        EsFlowTaskInstanceModel other = (EsFlowTaskInstanceModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$flowVersion = this.getFlowVersion();
        String other$flowVersion = other.getFlowVersion();
        if (this$flowVersion == null ? other$flowVersion != null : !this$flowVersion.equals(other$flowVersion)) {
            return false;
        }
        String this$flowCode = this.getFlowCode();
        String other$flowCode = other.getFlowCode();
        if (this$flowCode == null ? other$flowCode != null : !this$flowCode.equals(other$flowCode)) {
            return false;
        }
        Map<String, EsNodeValueModel> this$nodes = this.getNodes();
        Map<String, EsNodeValueModel> other$nodes = other.getNodes();
        if (this$nodes == null ? other$nodes != null : !(this$nodes).equals(other$nodes)) {
            return false;
        }
        List<EsNodeEventValueModel> this$events = this.getEvents();
        List<EsNodeEventValueModel> other$events = other.getEvents();
        return !(this$events == null ? other$events != null : !(this$events).equals(other$events));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EsFlowTaskInstanceModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $flowVersion = this.getFlowVersion();
        result = result * 59 + ($flowVersion == null ? 43 : $flowVersion.hashCode());
        String $flowCode = this.getFlowCode();
        result = result * 59 + ($flowCode == null ? 43 : $flowCode.hashCode());
        Map<String, EsNodeValueModel> $nodes = this.getNodes();
        result = result * 59 + ($nodes == null ? 43 : ($nodes).hashCode());
        List<EsNodeEventValueModel> $events = this.getEvents();
        result = result * 59 + ($events == null ? 43 : ($events).hashCode());
        return result;
    }

    public String toString() {
        return "EsFlowTaskInstanceModel(flowId=" + this.getFlowId() + ", flowVersion=" + this.getFlowVersion() + ", flowCode=" + this.getFlowCode() + ", nodes=" + this.getNodes() + ", events=" + this.getEvents() + ")";
    }
}

