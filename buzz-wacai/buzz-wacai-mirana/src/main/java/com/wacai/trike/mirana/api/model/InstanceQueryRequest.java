/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.api.model.NodeQueryRequest;
import java.util.List;
import java.util.Map;
import java.util.Set;

public class InstanceQueryRequest
extends NodeQueryRequest {
    private String operator;
    private Set<String> resources;
    private List<String> bzKeys;
    private List<String> instanceIds;
    private List<Long> nodeIds;
    private List<InstanceStatus> statuses;
    private Map<String, String> variables;

    public String getOperator() {
        return this.operator;
    }

    public Set<String> getResources() {
        return this.resources;
    }

    public List<String> getBzKeys() {
        return this.bzKeys;
    }

    public List<String> getInstanceIds() {
        return this.instanceIds;
    }

    public List<Long> getNodeIds() {
        return this.nodeIds;
    }

    public List<InstanceStatus> getStatuses() {
        return this.statuses;
    }

    public Map<String, String> getVariables() {
        return this.variables;
    }

    public InstanceQueryRequest setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public InstanceQueryRequest setResources(Set<String> resources) {
        this.resources = resources;
        return this;
    }

    public InstanceQueryRequest setBzKeys(List<String> bzKeys) {
        this.bzKeys = bzKeys;
        return this;
    }

    public InstanceQueryRequest setInstanceIds(List<String> instanceIds) {
        this.instanceIds = instanceIds;
        return this;
    }

    public InstanceQueryRequest setNodeIds(List<Long> nodeIds) {
        this.nodeIds = nodeIds;
        return this;
    }

    public InstanceQueryRequest setStatuses(List<InstanceStatus> statuses) {
        this.statuses = statuses;
        return this;
    }

    public InstanceQueryRequest setVariables(Map<String, String> variables) {
        this.variables = variables;
        return this;
    }

    @Override
    public String toString() {
        return "InstanceQueryRequest(operator=" + this.getOperator() + ", resources=" + this.getResources() + ", bzKeys=" + this.getBzKeys() + ", instanceIds=" + this.getInstanceIds() + ", nodeIds=" + this.getNodeIds() + ", statuses=" + this.getStatuses() + ", variables=" + this.getVariables() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstanceQueryRequest)) {
            return false;
        }
        InstanceQueryRequest other = (InstanceQueryRequest)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        Set<String> this$resources = this.getResources();
        Set<String> other$resources = other.getResources();
        if (this$resources == null ? other$resources != null : !(this$resources).equals(other$resources)) {
            return false;
        }
        List<String> this$bzKeys = this.getBzKeys();
        List<String> other$bzKeys = other.getBzKeys();
        if (this$bzKeys == null ? other$bzKeys != null : !(this$bzKeys).equals(other$bzKeys)) {
            return false;
        }
        List<String> this$instanceIds = this.getInstanceIds();
        List<String> other$instanceIds = other.getInstanceIds();
        if (this$instanceIds == null ? other$instanceIds != null : !(this$instanceIds).equals(other$instanceIds)) {
            return false;
        }
        List<Long> this$nodeIds = this.getNodeIds();
        List<Long> other$nodeIds = other.getNodeIds();
        if (this$nodeIds == null ? other$nodeIds != null : !(this$nodeIds).equals(other$nodeIds)) {
            return false;
        }
        List<InstanceStatus> this$statuses = this.getStatuses();
        List<InstanceStatus> other$statuses = other.getStatuses();
        if (this$statuses == null ? other$statuses != null : !(this$statuses).equals(other$statuses)) {
            return false;
        }
        Map<String, String> this$variables = this.getVariables();
        Map<String, String> other$variables = other.getVariables();
        return !(this$variables == null ? other$variables != null : !(this$variables).equals(other$variables));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof InstanceQueryRequest;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        Set<String> $resources = this.getResources();
        result = result * 59 + ($resources == null ? 43 : ($resources).hashCode());
        List<String> $bzKeys = this.getBzKeys();
        result = result * 59 + ($bzKeys == null ? 43 : ($bzKeys).hashCode());
        List<String> $instanceIds = this.getInstanceIds();
        result = result * 59 + ($instanceIds == null ? 43 : ($instanceIds).hashCode());
        List<Long> $nodeIds = this.getNodeIds();
        result = result * 59 + ($nodeIds == null ? 43 : ($nodeIds).hashCode());
        List<InstanceStatus> $statuses = this.getStatuses();
        result = result * 59 + ($statuses == null ? 43 : ($statuses).hashCode());
        Map<String, String> $variables = this.getVariables();
        result = result * 59 + ($variables == null ? 43 : ($variables).hashCode());
        return result;
    }
}

