/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.delay;

import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

public enum Phase {
    C(0L, "C"),
    M1(30L, "M1"),
    M2(60L, "M2"),
    M3(90L, "M3"),
    M4(120L, "M4"),
    M5(150L, "M5"),
    M6(180L, "M6"),
    M7(210L, "M7"),
    M8(240L, "M8"),
    M9(270L, "M9"),
    M10(300L, "M10"),
    M11(330L, "M11"),
    M12(360L, "M12"),
    M6_(Long.MAX_VALUE, "M6+");

    private Long maxDaysPastDue;
    private String value;
    private static final List<Phase> SORTED_PHASES;

    private Phase(Long maxDaysPastDue, String value) {
        this.maxDaysPastDue = maxDaysPastDue;
        this.value = value;
    }

    public static Phase getPhase(long daysPastDue) {
        for (Phase phase : SORTED_PHASES) {
            if (daysPastDue > phase.maxDaysPastDue) continue;
            return phase;
        }
        return M6_;
    }

    public static Phase getByValue(String value) {
        for (Phase item : Phase.values()) {
            if (!item.getValue().equals(value)) continue;
            return item;
        }
        throw new IllegalArgumentException("not support value : " + value);
    }

    public Long getMaxDaysPastDue() {
        return this.maxDaysPastDue;
    }

    public String getValue() {
        return this.value;
    }

    static {
        SORTED_PHASES = Arrays.stream(Phase.values()).sorted(Comparator.comparingLong(Phase::getMaxDaysPastDue)).collect(Collectors.toList());
    }
}

