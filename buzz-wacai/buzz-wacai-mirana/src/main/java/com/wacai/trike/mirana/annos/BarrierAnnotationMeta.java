/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.annos;

import com.wacai.trike.mirana.annos.Barrier;
import java.lang.reflect.Method;

public class BarrierAnnotationMeta {
    private Barrier barrierAnnotation;
    private String methodIndexName;
    private Method method;

    public Barrier getBarrierAnnotation() {
        return this.barrierAnnotation;
    }

    public String getMethodIndexName() {
        return this.methodIndexName;
    }

    public Method getMethod() {
        return this.method;
    }

    public void setBarrierAnnotation(Barrier barrierAnnotation) {
        this.barrierAnnotation = barrierAnnotation;
    }

    public void setMethodIndexName(String methodIndexName) {
        this.methodIndexName = methodIndexName;
    }

    public void setMethod(Method method) {
        this.method = method;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BarrierAnnotationMeta)) {
            return false;
        }
        BarrierAnnotationMeta other = (BarrierAnnotationMeta)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Barrier this$barrierAnnotation = this.getBarrierAnnotation();
        Barrier other$barrierAnnotation = other.getBarrierAnnotation();
        if (this$barrierAnnotation == null ? other$barrierAnnotation != null : !this$barrierAnnotation.equals(other$barrierAnnotation)) {
            return false;
        }
        String this$methodIndexName = this.getMethodIndexName();
        String other$methodIndexName = other.getMethodIndexName();
        if (this$methodIndexName == null ? other$methodIndexName != null : !this$methodIndexName.equals(other$methodIndexName)) {
            return false;
        }
        Method this$method = this.getMethod();
        Method other$method = other.getMethod();
        return !(this$method == null ? other$method != null : !(this$method).equals(other$method));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BarrierAnnotationMeta;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Barrier $barrierAnnotation = this.getBarrierAnnotation();
        result = result * 59 + ($barrierAnnotation == null ? 43 : $barrierAnnotation.hashCode());
        String $methodIndexName = this.getMethodIndexName();
        result = result * 59 + ($methodIndexName == null ? 43 : $methodIndexName.hashCode());
        Method $method = this.getMethod();
        result = result * 59 + ($method == null ? 43 : ($method).hashCode());
        return result;
    }

    public String toString() {
        return "BarrierAnnotationMeta(barrierAnnotation=" + this.getBarrierAnnotation() + ", methodIndexName=" + this.getMethodIndexName() + ", method=" + this.getMethod() + ")";
    }
}

