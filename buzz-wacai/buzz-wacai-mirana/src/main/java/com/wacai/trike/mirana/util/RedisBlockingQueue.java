/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSON
 *  com.wacai.common.redis.RedisException
 *  com.wacai.trike.cloud.proxy.redis.RedisProxy
 */
package com.wacai.trike.mirana.util;

import com.alibaba.fastjson.JSON;
import com.wacai.common.redis.RedisException;
import com.wacai.trike.cloud.proxy.redis.RedisProxy;
import java.util.AbstractQueue;
import java.util.Collection;
import java.util.Iterator;
import java.util.Objects;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

public class RedisBlockingQueue<E>
extends AbstractQueue<E>
implements BlockingQueue<E> {
    private final RedisProxy redisProxy;
    private final String key;
    private final ReentrantLock lock;
    private final Condition notEmpty;
    private final Class<? extends E> clazz;
    private final int timeout;

    public RedisBlockingQueue(RedisProxy redisProxy, String key, Class<? extends E> clazz) {
        this(redisProxy, key, clazz, 43200);
    }

    public RedisBlockingQueue(RedisProxy redisProxy, String key, Class<? extends E> clazz, int timeout) {
        this.redisProxy = Objects.requireNonNull(redisProxy);
        this.key = Objects.requireNonNull(key);
        this.lock = new ReentrantLock();
        this.notEmpty = this.lock.newCondition();
        this.clazz = Objects.requireNonNull(clazz);
        this.timeout = timeout;
    }

    @Override
    public int size() {
        try {
            return this.redisProxy.llen(this.key).intValue();
        }
        catch (RedisException e) {
            throw new RuntimeException(e);
        }
    }

    private void addFirst(String json) {
        this.lock.lock();
        try {
            this.redisProxy.rpush(this.key, new String[]{json});
            this.notEmpty.signal();
        }
        catch (RedisException ex) {
            throw new RuntimeException(ex);
        }
        finally {
            this.lock.unlock();
        }
    }

    private void addLast(String json) {
        this.lock.lock();
        try {
            this.redisProxy.lpush(this.key, new String[]{json});
            this.redisProxy.expire(this.key, this.timeout);
            this.notEmpty.signal();
        }
        catch (RedisException ex) {
            throw new RuntimeException(ex);
        }
        finally {
            this.lock.unlock();
        }
    }

    @Override
    public boolean offer(E e) {
        Objects.requireNonNull(e);
        try {
            this.addLast(JSON.toJSONString(e));
            return true;
        }
        catch (Exception ex) {
            return false;
        }
    }

    @Override
    public boolean offer(E e, long timeout, TimeUnit unit) throws InterruptedException {
        return this.offer(e);
    }

    @Override
    public E poll() {
        try {
            String result = this.redisProxy.rpop(this.key);
            if (Objects.isNull(result)) {
                return null;
            }
            return (E)JSON.parseObject((String)result, this.clazz);
        }
        catch (RedisException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public E poll(long timeout, TimeUnit unit) throws InterruptedException {
        String result;
        long nano = unit.toNanos(timeout);
        this.lock.lockInterruptibly();
        try {
            while (Objects.isNull(result = this.redisProxy.rpop(this.key))) {
                if (nano <= 0L) {
                    E e = null;
                    return e;
                }
                nano = this.notEmpty.awaitNanos(nano);
            }
        }
        catch (RedisException e) {
            throw new RuntimeException(e);
        }
        finally {
            this.lock.unlock();
        }
        return (E)JSON.parseObject((String)result, this.clazz);
    }

    @Override
    public void put(E e) throws InterruptedException {
        Objects.requireNonNull(e);
        this.addLast(JSON.toJSONString(e));
    }

    @Override
    public E take() throws InterruptedException {
        String result;
        this.lock.lockInterruptibly();
        try {
            while (Objects.isNull(result = this.redisProxy.rpop(this.key))) {
                this.notEmpty.await();
            }
        }
        catch (RedisException e) {
            throw new RuntimeException(e);
        }
        finally {
            this.lock.unlock();
        }
        return (E)JSON.parseObject((String)result, this.clazz);
    }

    @Override
    public E peek() {
        try {
            String result = this.redisProxy.rpop(this.key);
            if (Objects.isNull(result)) {
                return null;
            }
            this.addFirst(result);
            return (E)JSON.parseObject((String)result, this.clazz);
        }
        catch (RedisException e) {
            throw new RuntimeException(e);
        }
    }

    @Override
    public int remainingCapacity() {
        return Integer.MAX_VALUE;
    }

    @Override
    public boolean remove(Object o) {
        Objects.requireNonNull(o);
        if (!this.clazz.isInstance(o)) {
            throw new ClassCastException(o.toString());
        }
        try {
            return this.redisProxy.lrem(this.key, 0L, JSON.toJSONString(o)) > 0L;
        }
        catch (RedisException e) {
            return false;
        }
    }

    @Override
    public Iterator<E> iterator() {
        throw new UnsupportedOperationException("iterator");
    }

    @Override
    public int drainTo(Collection<? super E> c) {
        throw new UnsupportedOperationException("drainTo");
    }

    @Override
    public int drainTo(Collection<? super E> c, int maxElements) {
        throw new UnsupportedOperationException("drainTo");
    }
}

