/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.context;

import java.io.Serializable;

public class Step
implements Serializable {
    private Long id;
    private Long edgeId;
    private boolean dirty;

    public Long getId() {
        return this.id;
    }

    public Long getEdgeId() {
        return this.edgeId;
    }

    public boolean isDirty() {
        return this.dirty;
    }

    public Step setId(Long id) {
        this.id = id;
        return this;
    }

    public Step setEdgeId(Long edgeId) {
        this.edgeId = edgeId;
        return this;
    }

    public Step setDirty(boolean dirty) {
        this.dirty = dirty;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Step)) {
            return false;
        }
        Step other = (Step)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$edgeId = this.getEdgeId();
        Long other$edgeId = other.getEdgeId();
        if (this$edgeId == null ? other$edgeId != null : !(this$edgeId).equals(other$edgeId)) {
            return false;
        }
        return this.isDirty() == other.isDirty();
    }

    protected boolean canEqual(Object other) {
        return other instanceof Step;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $edgeId = this.getEdgeId();
        result = result * 59 + ($edgeId == null ? 43 : ($edgeId).hashCode());
        result = result * 59 + (this.isDirty() ? 79 : 97);
        return result;
    }

    public String toString() {
        return "Step(id=" + this.getId() + ", edgeId=" + this.getEdgeId() + ", dirty=" + this.isDirty() + ")";
    }
}

