/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.action.dao.repository;

import com.wacai.trike.mirana.domain.action.dao.po.GenericActionVariablePO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface GenericActionVariableRepository
extends JpaRepository<GenericActionVariablePO, Long> {
    public List<GenericActionVariablePO> findByActionIdAndActive(Long var1, boolean var2);
}

