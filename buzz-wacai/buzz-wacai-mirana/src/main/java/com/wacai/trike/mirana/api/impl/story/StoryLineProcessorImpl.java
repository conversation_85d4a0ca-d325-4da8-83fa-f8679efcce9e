/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.google.common.base.Strings
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.OrderSpecifier
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.JPQLQuery
 *  com.querydsl.jpa.JPQLQueryFactory
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.Page
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  com.wacai.trike.mirana.api.model.FlowCancelRequest
 *  com.wacai.trike.mirana.api.model.FlowProcessRequest
 *  com.wacai.trike.mirana.api.model.FlowResponse
 *  com.wacai.trike.mirana.api.model.FlowStartRequest
 *  com.wacai.trike.mirana.api.story.StoryLineProcessor
 *  com.wacai.trike.mirana.api.story.model.StoryCancelReq
 *  com.wacai.trike.mirana.api.story.model.StoryCancelRes
 *  com.wacai.trike.mirana.api.story.model.StoryEndReq
 *  com.wacai.trike.mirana.api.story.model.StoryEndRes
 *  com.wacai.trike.mirana.api.story.model.StoryModel
 *  com.wacai.trike.mirana.api.story.model.StoryProcessReq
 *  com.wacai.trike.mirana.api.story.model.StoryProcessRes
 *  com.wacai.trike.mirana.api.story.model.StoryQueryReq
 *  com.wacai.trike.mirana.api.story.model.StoryRedoReq
 *  com.wacai.trike.mirana.api.story.model.StoryRedoRes
 *  com.wacai.trike.mirana.api.story.model.StoryStartReq
 *  com.wacai.trike.mirana.api.story.model.StoryStartRes
 *  javax.validation.constraints.NotNull
 *  org.apache.commons.lang3.StringUtils
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.validation.annotation.Validated
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl.story;

import com.alibaba.dubbo.config.annotation.Service;
import com.google.common.base.Strings;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.JPQLQueryFactory;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.api.impl.FlowProcessorImpl;
import com.wacai.trike.mirana.api.model.FlowCancelRequest;
import com.wacai.trike.mirana.api.model.FlowProcessRequest;
import com.wacai.trike.mirana.api.model.FlowResponse;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import com.wacai.trike.mirana.api.story.StoryLineProcessor;
import com.wacai.trike.mirana.api.story.model.StoryCancelReq;
import com.wacai.trike.mirana.api.story.model.StoryCancelRes;
import com.wacai.trike.mirana.api.story.model.StoryEndReq;
import com.wacai.trike.mirana.api.story.model.StoryEndRes;
import com.wacai.trike.mirana.api.story.model.StoryModel;
import com.wacai.trike.mirana.api.story.model.StoryProcessReq;
import com.wacai.trike.mirana.api.story.model.StoryProcessRes;
import com.wacai.trike.mirana.api.story.model.StoryQueryReq;
import com.wacai.trike.mirana.api.story.model.StoryRedoReq;
import com.wacai.trike.mirana.api.story.model.StoryRedoRes;
import com.wacai.trike.mirana.api.story.model.StoryStartReq;
import com.wacai.trike.mirana.api.story.model.StoryStartRes;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.InstanceContextService;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.flow.po.FlowPO;
import com.wacai.trike.mirana.domain.flow.po.QFlowPO;
import com.wacai.trike.mirana.domain.instance.po.InstancePO;
import com.wacai.trike.mirana.domain.instance.po.QInstancePO;
import com.wacai.trike.mirana.domain.instance.repository.InstanceRepository;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.CancelInstanceEvent;
import com.wacai.trike.mirana.event.EventAgency;
import com.wacai.trike.mirana.event.ManualEndingEvent;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.task.TaskExecuteException;
import com.wacai.trike.mirana.task.TaskProcessor;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Service(interfaceClass=StoryLineProcessor.class)
@RestController
public class StoryLineProcessorImpl
implements StoryLineProcessor {
    private static final Logger log = LoggerFactory.getLogger(StoryLineProcessorImpl.class);
    @Autowired
    private InstanceRepository instanceRepository;
    @Autowired
    private InstanceContextService contextService;
    @Autowired
    private FlowProcessorImpl delegate;
    @Autowired
    private FlowGraphService graphService;
    @Autowired
    private List<TaskProcessor> taskProcessors;
    @Autowired
    private JPQLQueryFactory queryFactory;
    @Autowired
    private EventAgency eventAgency;

    public ApiResponse<StoryStartRes> start(@RequestBody @NotNull @Validated StoryStartReq req) {
        log.info("{}", req.toString());
        FlowStartRequest request = new FlowStartRequest();
        BeanUtils.copyProperties(req, request);
        request.setStory(true);
        return this.delegate.start(request).map(this::convert);
    }

    private StoryStartRes convert(FlowResponse r) {
        if (Objects.isNull(r)) {
            return null;
        }
        return new StoryStartRes().setInstanceId(r.getInstanceId()).setStatus(r.getStatus());
    }

    public ApiResponse<StoryProcessRes> process(@RequestBody @NotNull @Validated StoryProcessReq req) {
        FlowProcessRequest request = new FlowProcessRequest();
        BeanUtils.copyProperties(req, request);
        List<InstancePO> pos = this.instanceRepository.findByParentUuidAndStatus(request.getInstanceId(), InstanceStatus.RUNNING);
        if (pos.size() != 1) {
            InstanceContext context = this.contextService.load(request.getInstanceId());
            context.addVariable(request.getParam(), VariableType.MANUAL_INPUT);
            this.contextService.persistence(context);
            StoryProcessRes response = new StoryProcessRes().setStatus(context.getStatus()).setInstanceId(context.getUuid());
            return ApiResponse.success(response);
        }
        request.setInstanceId(pos.get(0).getUuid());
        return this.delegate.process(request).map(this::convertProcess);
    }

    private StoryProcessRes convertProcess(FlowResponse r) {
        if (Objects.isNull(r)) {
            return null;
        }
        return new StoryProcessRes().setInstanceId(r.getInstanceId()).setStatus(r.getStatus());
    }

    public ApiResponse<StoryEndRes> end(@RequestBody @NotNull @Validated StoryEndReq req) {
        List<InstancePO> pos = this.instanceRepository.findByParentUuid(req.getInstanceId()).stream().filter(p -> p.getStatus().isRunning()).collect(Collectors.toList());
        AbstractFlowEvent endedEvent = new ManualEndingEvent().setInstanceUuid(req.getInstanceId()).setOperator(req.getOperator()).setTaskId(req.getInstanceId()).setParam(req.getParam());
        this.eventAgency.agent(endedEvent, false);
        for (InstancePO po : pos) {
            endedEvent = ((CancelInstanceEvent)new ManualEndingEvent().setOperator(req.getOperator()).setParam(req.getParam())).setInstanceUuid(po.getUuid()).setTaskId(po.getUuid());
            this.eventAgency.agent(endedEvent, false);
            log.info("end story [instance: {}, sub-flow: {}] success", req.getInstanceId(), po.getUuid());
        }
        log.info("end story [instance: {}, reason: {}] success", req.getInstanceId(), req.getParam().get("endReason"));
        StoryEndRes res = new StoryEndRes().setInstanceId(req.getInstanceId()).setStatus(InstanceStatus.ENDED);
        return ApiResponse.success(res);
    }

    public ApiResponse<StoryCancelRes> cancel(@RequestBody @NotNull @Validated StoryCancelReq req) {
        List<InstancePO> pos = this.instanceRepository.findByParentUuid(req.getInstanceId()).stream().filter(p -> p.getStatus().isRunning()).collect(Collectors.toList());
        FlowCancelRequest request = new FlowCancelRequest();
        BeanUtils.copyProperties(req, request);
        ApiResponse<FlowResponse> response = this.delegate.cancel(request);
        for (InstancePO po : pos) {
            request = new FlowCancelRequest();
            BeanUtils.copyProperties(req, request);
            request.setInstanceId(po.getUuid());
            ApiResponse<FlowResponse> tmp = this.delegate.cancel(request);
            if (tmp.success()) continue;
            response.setCode(1);
            response.setError(response.getError() + "\n" + tmp.getError());
        }
        return response.map(r -> new StoryCancelRes().setStatus(r.getStatus()).setInstanceId(r.getInstanceId()));
    }

    public ApiResponse<StoryRedoRes> redo(@RequestBody @NotNull @Validated StoryRedoReq req) {
        Set<String> effectiveIds = this.loadValidIds(req);
        HashSet<String> errorIds = new HashSet<String>();
        for (String instanceId : req.getInstanceIds()) {
            if (!effectiveIds.contains(instanceId)) {
                errorIds.add(instanceId);
                log.warn("instance [{}] not found", instanceId);
                continue;
            }
            try {
                this.redo(instanceId);
            }
            catch (Exception e) {
                errorIds.add(instanceId);
                log.warn("redo task for instance [{}] failed", instanceId, e);
            }
        }
        return ApiResponse.success(new StoryRedoRes().setErrorInstanceIds(errorIds));
    }

    private Set<String> loadValidIds(StoryRedoReq redoRequest) {
        List effectiveIds = ((JPQLQuery)this.queryFactory.selectDistinct((Expression)QInstancePO.instancePO.uuid).from(new EntityPath[]{QInstancePO.instancePO}).join((EntityPath)QFlowPO.flowPO).on(new Predicate[]{QInstancePO.instancePO.flowId.eq(QFlowPO.flowPO.id)}).join((EntityPath)QApplicationPO.applicationPO).on(new Predicate[]{QApplicationPO.applicationPO.id.eq(QFlowPO.flowPO.appId)}).join((EntityPath)QBusinessPO.businessPO).on(new Predicate[]{QBusinessPO.businessPO.id.eq(QApplicationPO.applicationPO.buId)}).where(new Predicate[]{QInstancePO.instancePO.uuid.in((Collection)redoRequest.getInstanceIds()).and((Predicate)QFlowPO.flowPO.code.eq(redoRequest.getFlow())).and((Predicate)QApplicationPO.applicationPO.code.eq(redoRequest.getApp())).and((Predicate)QBusinessPO.businessPO.code.eq(redoRequest.getBu())).and((Predicate)QInstancePO.instancePO.status.eq(InstanceStatus.RUNNING))})).fetch();
        return new HashSet<String>(effectiveIds);
    }

    private void redo(String instanceId) throws TaskExecuteException {
        List<InstancePO> pos = this.instanceRepository.findByParentUuidAndStatus(instanceId, InstanceStatus.RUNNING);
        if (CollectionUtils.isEmpty(pos)) {
            this.redoTask(instanceId);
            return;
        }
        for (InstancePO po : pos) {
            this.redo(po.getUuid());
        }
    }

    private void redoTask(String instanceId) throws TaskExecuteException {
        InstanceContext context = this.contextService.load(instanceId);
        Node node = this.graphService.getFlowGraph(context.getFlowId()).getNode(context.getCurrentNodeId());
        log.info("redo instance [{}] node [{} {}] task {}", new Object[]{context.getUuid(), node.getName(), node.getId(), node.getTaskContent()});
        for (TaskProcessor taskProcessor : this.taskProcessors) {
            if (!taskProcessor.accept(node.getTaskType())) continue;
            context.addVariable(taskProcessor.execute(node, context), VariableType.TASK_OUTPUT);
            break;
        }
        this.contextService.persistence(context);
    }

    public ApiResponse<Page<StoryModel>> query(StoryQueryReq req) {
        return ApiResponse.success(this.findFlow(req));
    }

    private StoryModel buildStory(Object f) {
        StoryModel model = new StoryModel();
        BeanUtils.copyProperties(f, model);
        return model;
    }

    private Page<StoryModel> findFlow(StoryQueryReq req) {
        BooleanExpression where = QFlowPO.flowPO.active.isTrue();
        if (!Strings.isNullOrEmpty((String)req.getBu())) {
            where = where.and((Predicate)QBusinessPO.businessPO.code.eq(req.getBu()));
        }
        if (!Strings.isNullOrEmpty((String)req.getApp())) {
            where = where.and((Predicate)QApplicationPO.applicationPO.code.eq(req.getApp()));
        }
        if (!CollectionUtils.isEmpty((Collection)req.getTypes())) {
            where = where.and((Predicate)QFlowPO.flowPO.type.in((Collection)req.getTypes()));
        }
        if (!CollectionUtils.isEmpty((Collection)req.getStatuses())) {
            where = where.and((Predicate)QFlowPO.flowPO.status.in((Collection)req.getStatuses()));
        }
        if (StringUtils.isNotBlank((CharSequence)req.getCode())) {
            where = where.and((Predicate)QFlowPO.flowPO.code.eq(req.getCode().trim()));
        }
        if (StringUtils.isNotBlank((CharSequence)req.getName())) {
            where = where.and((Predicate)QFlowPO.flowPO.name.like("%" + req.getName().trim() + "%"));
        }
        
        JPQLQuery query = (JPQLQuery)this.queryFactory.selectFrom((EntityPath)QFlowPO.flowPO)
        		.join((EntityPath)QApplicationPO.applicationPO).on(new Predicate[]{QApplicationPO.applicationPO.id.eq(QFlowPO.flowPO.appId)})
        		.join((EntityPath)QBusinessPO.businessPO).on(new Predicate[]{QBusinessPO.businessPO.id.eq(QApplicationPO.applicationPO.buId)})
        		.where(new Predicate[]{where});
        long count = query.fetchCount();
        List models = Collections.emptyList();
        if (count > 0L && req.getOffset() <= count) {
//            models = query.orderBy(
//            		new OrderSpecifier[]{QFlowPO.flowPO.name.asc(), QFlowPO.flowPO.id.desc()}))
//            		.offset(req.getOffset())).limit(req.getPageSize())).fetch()
//            		.stream().map(this::buildStory).collect(Collectors.toList());
        }
        return new Page().setData(models).setTotal(count).setPage(Page.calcPages((long)count, (long)req.getPageSize()));
    }
}

