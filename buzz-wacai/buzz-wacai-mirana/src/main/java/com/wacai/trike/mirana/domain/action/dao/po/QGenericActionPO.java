/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.action.dao.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericActionPO;
import com.wacai.trike.mirana.domain.action.enums.ActionEnums;
import java.time.LocalDateTime;

public class QGenericActionPO
extends EntityPathBase<GenericActionPO> {
    private static final long serialVersionUID = -1399704268L;
    public static final QGenericActionPO genericActionPO = new QGenericActionPO("genericActionPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final NumberPath<Long> appId;
    public final StringPath callbackUrl;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final EnumPath<ActionEnums.Type> type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QGenericActionPO(String variable) {
        super(GenericActionPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.callbackUrl = this.createString("callbackUrl");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.type = this.createEnum("type", ActionEnums.Type.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QGenericActionPO(Path<? extends GenericActionPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.callbackUrl = this.createString("callbackUrl");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.type = this.createEnum("type", ActionEnums.Type.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QGenericActionPO(PathMetadata metadata) {
        super(GenericActionPO.class, metadata);
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.callbackUrl = this.createString("callbackUrl");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.type = this.createEnum("type", ActionEnums.Type.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

