/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Maps
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.core.annotation.AnnotationAwareOrderComparator
 *  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.event;

import java.util.List;
import java.util.Map;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.annotation.AnnotationAwareOrderComparator;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.google.common.collect.Maps;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.InstanceContextService;

@Component
public class EventAgency {
    private static final Logger log = LogManager.getLogger(EventAgency.class);
    private volatile boolean sorted = false;
    @Autowired
    private List<EventListener> listeners;
    @Autowired
    private InstanceContextService contextService;
    @Autowired
    private ThreadPoolTaskExecutor executor;
    private final Map<Class<AbstractFlowEvent>, EventListener> cache = Maps.newHashMap();

    public InstanceContext agent(AbstractFlowEvent event, boolean async) {
        if (event == null) {
            return null;
        }
        //返回的数据：flowId=@Long[407], flowCode=@String[WACAI_KUAIDAI_AUTO_CALL],bzKey=@String[34141595],
        InstanceContext context = this.loadContext(event);
        EventExchange exchange = new EventExchange(context, event);
        if (!async) {
            return this.handle(exchange);
        }
        this.executor.submit(() -> this.handle(exchange));
        return context;
    }

    private InstanceContext handle(EventExchange exchange) {
		EventExchange cursor = exchange;

		while (cursor != null) {
			log.info("process {} of instance {}", cursor.getCurrent().getClass().getSimpleName(),
					cursor.getContext().getUuid());
			EventListener filter = this.filter(cursor.getCurrent());
			if (filter == null) {
				log.error("not found any listener for event type {}", cursor.getCurrent().getClass().getSimpleName());
				break;
			}

			if (!filter.needProcess(cursor)) {
				log.info("this event of instance {} do not need process {}", cursor.getContext().getUuid(),
						filter.getClass().getSimpleName());
				break;
			}

			filter.doProcess(cursor);
			cursor = cursor.next();
			if (cursor != null) {
				exchange = cursor;
			}
		}

		return exchange.getContext();
	}

    private InstanceContext loadContext(AbstractFlowEvent event) {
        InstanceContext context = this.contextService.load(event.getInstanceUuid());
        if (context != null) {
            return context;
        }
        return new InstanceContext()
        		.setStatus(InstanceStatus.RUNNING)
        		.setUuid(event.getInstanceUuid())
        		.setCreator(event.getOperator()).setDirty(true);
    }

	private EventListener filter(AbstractFlowEvent event) {
		if (!this.sorted) {
			AnnotationAwareOrderComparator.sort(this.listeners);
			this.map();
			this.sorted = true;
		}

		EventListener found = (EventListener) this.cache.get(event.getClass());
		return found != null ? found : (EventListener) this.listeners.stream().filter((listener) -> {
			return event.getClass() == listener.accept();
		}).findFirst().orElse(null);
	}

    private void map() {
        if (CollectionUtils.isEmpty(this.listeners)) {
            log.error("not found any ele from listener collection");
            return;
        }
        this.listeners.forEach(listener -> this.cache.put(listener.accept(), (EventListener)listener));
    }
}

