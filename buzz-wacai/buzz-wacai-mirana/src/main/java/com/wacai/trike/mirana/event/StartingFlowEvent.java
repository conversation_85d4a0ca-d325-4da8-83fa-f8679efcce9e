/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.event.AbstractFlowEvent;
import java.time.LocalDateTime;
import java.util.Map;

public class StartingFlowEvent
extends AbstractFlowEvent {
    private String bu;
    private String app;
    private String flow;
    private String version;
    private String bzKey;
    private String parentUuid;
    private boolean async;
    private LocalDateTime timeout;
    private String uuid;
    private String instanceUuid;
    private String taskId;
    protected String operator;
    protected Map<String, String> param;

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getFlow() {
        return this.flow;
    }

    public String getVersion() {
        return this.version;
    }

    public String getBzKey() {
        return this.bzKey;
    }

    public String getParentUuid() {
        return this.parentUuid;
    }

    public boolean isAsync() {
        return this.async;
    }

    public LocalDateTime getTimeout() {
        return this.timeout;
    }

    @Override
    public String getUuid() {
        return this.uuid;
    }

    @Override
    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    @Override
    public String getTaskId() {
        return this.taskId;
    }

    @Override
    public String getOperator() {
        return this.operator;
    }

    @Override
    public Map<String, String> getParam() {
        return this.param;
    }

    public StartingFlowEvent setBu(String bu) {
        this.bu = bu;
        return this;
    }

    public StartingFlowEvent setApp(String app) {
        this.app = app;
        return this;
    }

    public StartingFlowEvent setFlow(String flow) {
        this.flow = flow;
        return this;
    }

    public StartingFlowEvent setVersion(String version) {
        this.version = version;
        return this;
    }

    public StartingFlowEvent setBzKey(String bzKey) {
        this.bzKey = bzKey;
        return this;
    }

    public StartingFlowEvent setParentUuid(String parentUuid) {
        this.parentUuid = parentUuid;
        return this;
    }

    public StartingFlowEvent setAsync(boolean async) {
        this.async = async;
        return this;
    }

    public StartingFlowEvent setTimeout(LocalDateTime timeout) {
        this.timeout = timeout;
        return this;
    }

    public StartingFlowEvent setUuid(String uuid) {
        this.uuid = uuid;
        return this;
    }

    public StartingFlowEvent setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
        return this;
    }

    public StartingFlowEvent setTaskId(String taskId) {
        this.taskId = taskId;
        return this;
    }

    public StartingFlowEvent setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    @Override
    public StartingFlowEvent setParam(Map<String, String> param) {
        this.param = param;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StartingFlowEvent)) {
            return false;
        }
        StartingFlowEvent other = (StartingFlowEvent)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        if (this$flow == null ? other$flow != null : !this$flow.equals(other$flow)) {
            return false;
        }
        String this$version = this.getVersion();
        String other$version = other.getVersion();
        if (this$version == null ? other$version != null : !this$version.equals(other$version)) {
            return false;
        }
        String this$bzKey = this.getBzKey();
        String other$bzKey = other.getBzKey();
        if (this$bzKey == null ? other$bzKey != null : !this$bzKey.equals(other$bzKey)) {
            return false;
        }
        String this$parentUuid = this.getParentUuid();
        String other$parentUuid = other.getParentUuid();
        if (this$parentUuid == null ? other$parentUuid != null : !this$parentUuid.equals(other$parentUuid)) {
            return false;
        }
        if (this.isAsync() != other.isAsync()) {
            return false;
        }
        LocalDateTime this$timeout = this.getTimeout();
        LocalDateTime other$timeout = other.getTimeout();
        if (this$timeout == null ? other$timeout != null : !(this$timeout).equals(other$timeout)) {
            return false;
        }
        String this$uuid = this.getUuid();
        String other$uuid = other.getUuid();
        if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
            return false;
        }
        String this$instanceUuid = this.getInstanceUuid();
        String other$instanceUuid = other.getInstanceUuid();
        if (this$instanceUuid == null ? other$instanceUuid != null : !this$instanceUuid.equals(other$instanceUuid)) {
            return false;
        }
        String this$taskId = this.getTaskId();
        String other$taskId = other.getTaskId();
        if (this$taskId == null ? other$taskId != null : !this$taskId.equals(other$taskId)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        Map<String, String> this$param = this.getParam();
        Map<String, String> other$param = other.getParam();
        return !(this$param == null ? other$param != null : !(this$param).equals(other$param));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StartingFlowEvent;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        String $version = this.getVersion();
        result = result * 59 + ($version == null ? 43 : $version.hashCode());
        String $bzKey = this.getBzKey();
        result = result * 59 + ($bzKey == null ? 43 : $bzKey.hashCode());
        String $parentUuid = this.getParentUuid();
        result = result * 59 + ($parentUuid == null ? 43 : $parentUuid.hashCode());
        result = result * 59 + (this.isAsync() ? 79 : 97);
        LocalDateTime $timeout = this.getTimeout();
        result = result * 59 + ($timeout == null ? 43 : ($timeout).hashCode());
        String $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        String $instanceUuid = this.getInstanceUuid();
        result = result * 59 + ($instanceUuid == null ? 43 : $instanceUuid.hashCode());
        String $taskId = this.getTaskId();
        result = result * 59 + ($taskId == null ? 43 : $taskId.hashCode());
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        Map<String, String> $param = this.getParam();
        result = result * 59 + ($param == null ? 43 : ($param).hashCode());
        return result;
    }

    public String toString() {
        return "StartingFlowEvent(bu=" + this.getBu() + ", app=" + this.getApp() + ", flow=" + this.getFlow() + ", version=" + this.getVersion() + ", bzKey=" + this.getBzKey() + ", parentUuid=" + this.getParentUuid() + ", async=" + this.isAsync() + ", timeout=" + this.getTimeout() + ", uuid=" + this.getUuid() + ", instanceUuid=" + this.getInstanceUuid() + ", taskId=" + this.getTaskId() + ", operator=" + this.getOperator() + ", param=" + this.getParam() + ")";
    }
}

