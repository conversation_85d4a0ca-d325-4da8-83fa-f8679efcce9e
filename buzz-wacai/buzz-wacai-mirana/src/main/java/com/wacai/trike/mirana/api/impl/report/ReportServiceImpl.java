/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.google.common.base.Strings
 *  com.google.common.collect.Lists
 *  com.querydsl.core.Tuple
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.Projections
 *  com.querydsl.core.types.QBean
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.core.types.dsl.Expressions
 *  com.querydsl.core.types.dsl.NumberExpression
 *  com.querydsl.core.types.dsl.StringTemplate
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  com.wacai.trike.mirana.api.report.ReportService
 *  com.wacai.trike.mirana.api.report.model.FlowReportModel
 *  com.wacai.trike.mirana.api.report.model.FlowReportQueryModel
 *  com.wacai.trike.mirana.api.report.model.FlowReportSummaryModel
 *  com.wacai.trike.mirana.api.report.model.NodeFlowReportModel
 *  javax.validation.Valid
 *  javax.validation.constraints.NotNull
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.validation.annotation.Validated
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl.report;

import com.alibaba.dubbo.config.annotation.Service;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.querydsl.core.Tuple;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.QBean;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.Expressions;
import com.querydsl.core.types.dsl.NumberExpression;
import com.querydsl.core.types.dsl.StringTemplate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.api.report.ReportService;
import com.wacai.trike.mirana.api.report.model.FlowReportModel;
import com.wacai.trike.mirana.api.report.model.FlowReportQueryModel;
import com.wacai.trike.mirana.api.report.model.FlowReportSummaryModel;
import com.wacai.trike.mirana.api.report.model.NodeFlowReportModel;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.context.po.QInstanceVariablePO;
import com.wacai.trike.mirana.domain.context.po.QTaskInstancePO;
import com.wacai.trike.mirana.domain.flow.po.QFlowPO;
import com.wacai.trike.mirana.domain.instance.po.QInstancePO;
import com.wacai.trike.mirana.domain.node.po.NodePO;
import com.wacai.trike.mirana.domain.node.po.QNodePO;
import com.wacai.trike.mirana.domain.node.repository.NodeRepository;
import com.wacai.trike.mirana.task.TaskStatus;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Service
@Validated
public class ReportServiceImpl
		implements ReportService {
	private static final Logger log = LoggerFactory.getLogger(ReportServiceImpl.class);
	@Autowired
	private JPAQueryFactory queryFactory;
	@Autowired
	private NodeRepository nodeRepository;

	public ApiResponse<List<NodeFlowReportModel>> flowReport(@Valid @NotNull FlowReportQueryModel queryModel) {
		NodePO node = this.nodeRepository.findById(queryModel.getCreateNodeId()).orElse(null);
		if (node == null) {
			return ApiResponse.error((String) ("not found node: " + queryModel.getCreateNodeId()));
		}
		if (node.getTaskType() != TaskType.START_SUB_FLOW) {
			return ApiResponse.error((String) ("current node " + queryModel.getCreateNodeId() + " is not a sub flow."));
		}
		if (Strings.isNullOrEmpty((String) node.getTaskContent())) {
			return ApiResponse.error((String) ("node " + queryModel.getCreateNodeId() + " flow code is null."));
		}
		Long flowId = this.getFlowId(queryModel.getBu(), queryModel.getApp(), node.getTaskContent());
		if (flowId == null) {
			return ApiResponse.error((String) ("not found sub flow: " + node.getTaskContent()));
		}
		return this.flowReport(flowId, queryModel.getDate(), queryModel.getCreateNodeId());
	}

	private Long getFlowId(String bu, String app, String flow) {
		return (Long) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) this.queryFactory.select(
				QFlowPO.flowPO.id).from((EntityPath) QFlowPO.flowPO)).join((EntityPath) QApplicationPO.applicationPO))
						.on((Predicate) QApplicationPO.applicationPO.id.eq(QFlowPO.flowPO.appId)))
								.join((EntityPath) QBusinessPO.businessPO)).on((Predicate) QBusinessPO.businessPO.id
										.eq(QApplicationPO.applicationPO.buId))).where((Predicate) QFlowPO.flowPO.code
												.eq(flow).and((Predicate) QApplicationPO.applicationPO.code
														.eq(app))
												.and((Predicate) QBusinessPO.businessPO.code.eq(bu)))).fetchOne();
	}

	public ApiResponse<List<NodeFlowReportModel>> flowReport(@RequestParam(value = "flowId") Long flowId,
			@RequestParam(value = "date", required = false) LocalDate date,
			@RequestParam(value = "createNodeId", required = false) Long createNodeId) {
		List reportQueries;
		if (Objects.isNull(flowId)) {
			return ApiResponse.error((String) "flowId can not be null");
		}
		date = Optional.ofNullable(date).orElse(LocalDate.now());
		LocalDateTime start = date.atStartOfDay().minusNanos(1L);
		LocalDateTime end = date.plusDays(1L).atStartOfDay();
		BooleanExpression expression = QTaskInstancePO.taskInstancePO.createdTime.between( start,
				 end);
		if (Objects.nonNull(createNodeId)) {
			expression = expression.and((Predicate) QInstanceVariablePO.instanceVariablePO.field.eq("createNodeId"))
					.and((Predicate) QInstanceVariablePO.instanceVariablePO.value.eq(String.valueOf(createNodeId)));
		}
		if (CollectionUtils.isEmpty(
				(Collection) (reportQueries = ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) this.queryFactory
						.select((Expression) Projections.fields(NodeFlowReportQuery.class,
								(Expression[]) new Expression[] { QNodePO.nodePO.id.as("nodeId"),
										QNodePO.nodePO.name.as("nodeName"), QNodePO.nodePO.code.as("nodeCode"),
										QTaskInstancePO.taskInstancePO.status.as("taskStatus"),
										QTaskInstancePO.taskInstancePO.id.count().as("taskCount") }))
						.from((EntityPath) QTaskInstancePO.taskInstancePO))
								.leftJoin((EntityPath) QInstanceVariablePO.instanceVariablePO))
										.on((Predicate) QInstanceVariablePO.instanceVariablePO.instanceUuid
												.eq((Expression) QTaskInstancePO.taskInstancePO.instanceUuid)))
														.leftJoin((EntityPath) QNodePO.nodePO))
																.on((Predicate) QTaskInstancePO.taskInstancePO.nodeId
																		.eq(QNodePO.nodePO.id)))
																				.where((Predicate) expression))
																						.groupBy(new Expression[] {
																								QNodePO.nodePO.id,
																								QTaskInstancePO.taskInstancePO.status }))
																										.orderBy(
																												QNodePO.nodePO.id
																														.asc())).fetch()))) {
			return ApiResponse.success(Lists.newArrayList());
		}
		ArrayList results = new ArrayList();
//		reportQueries.stream().collect(Collectors.groupingBy(NodeFlowReportQuery::getNodeId)).forEach((key, values) -> {
//			NodeFlowReportModel model = new NodeFlowReportModel();
//			model.setNodeId(key.longValue());
//			if (!CollectionUtils.isEmpty((Collection) values)) {
//				model.setCode(((NodeFlowReportQuery) values.get(0)).getNodeCode());
//				model.setName(((NodeFlowReportQuery) values.get(0)).getNodeName());
//				values.forEach(value -> {
//					if (((NodeFlowReportQuery) value).taskStatus == TaskStatus.COMPLETED) {
//						model.setCompleted(((NodeFlowReportQuery) value).taskCount.longValue());
//					}
//					if (((NodeFlowReportQuery) value).taskStatus == TaskStatus.RUNNING) {
//						model.setRunning(((NodeFlowReportQuery) value).taskCount.longValue());
//					}
//					if (((NodeFlowReportQuery) value).taskStatus == TaskStatus.FAILED) {
//						model.setFailed(((NodeFlowReportQuery) value).taskCount.longValue());
//					}
//					if (((NodeFlowReportQuery) value).taskStatus == TaskStatus.SKIP) {
//						model.setSkip(((NodeFlowReportQuery) value).taskCount.longValue());
//					}
//				});
//			}
//			results.add(model);
//		});
		return ApiResponse.success(results);
	}

	private QBean<FlowReportModel> create() {
		return Projections.fields(FlowReportModel.class, (Expression[]) new Expression[] { QNodePO.nodePO.code,
				QNodePO.nodePO.name, QInstancePO.instancePO.status, QInstancePO.instancePO.id.count().as("count") });
	}

	public ApiResponse<List<FlowReportModel>> storyReport(@Valid @NotNull FlowReportQueryModel queryModel) {
		Long flowId = this.getFlowId(queryModel.getBu(), queryModel.getApp(), queryModel.getFlow());
		return this.storyReport(flowId);
	}

	public ApiResponse<List<FlowReportModel>> storyReport(@RequestParam(value = "flowId") Long flowId) {
		List reportModels = ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) this.queryFactory
				.select(this.create()).from((EntityPath) QInstancePO.instancePO)).join((EntityPath) QNodePO.nodePO))
						.on((Predicate) QNodePO.nodePO.id.eq(QInstancePO.instancePO.currentNodeId)))
								.where((Predicate) QInstancePO.instancePO.flowId.eq(flowId)
										.and((Predicate) QInstancePO.instancePO.status.eq(InstanceStatus.RUNNING))))
												.groupBy(QInstancePO.instancePO.currentNodeId)).fetch();
		return ApiResponse.success(reportModels);
	}

	public ApiResponse<List<FlowReportSummaryModel>> flowReportSummary(
			@Valid @NotNull FlowReportQueryModel queryModel) {
		LocalDateTime end;
		LocalDateTime start;
		if (Objects.isNull(queryModel.getCreateNodeId())) {
			log.info("create node id must not bu null, query [{}]", queryModel);
			return ApiResponse.error((String) "create node id must not bu null");
		}
		NodePO node = this.nodeRepository.findById(queryModel.getCreateNodeId()).orElse(null);
		if (node == null) {
			log.info("node not exists, query [{}]", queryModel);
			return ApiResponse.error((String) "node not exists");
		}
		BooleanExpression expression = QTaskInstancePO.taskInstancePO.status
				.in(new TaskStatus[] { TaskStatus.RUNNING, TaskStatus.STOPPED, TaskStatus.COMPLETED })
				.and((Predicate) QTaskInstancePO.taskInstancePO.nodeId.eq(node.getId()));
		if (Objects.nonNull(queryModel.getDate())) {
			start = queryModel.getDate().minusDays(1L).atStartOfDay().minusNanos(1L);
			end = queryModel.getDate().atStartOfDay();
			expression = expression.and((Predicate) QTaskInstancePO.taskInstancePO.startTime.after( start))
					.and((Predicate) QTaskInstancePO.taskInstancePO.startTime.before( end));
		} else {
			LocalDate today = LocalDate.now();
			start = today.minusDays(8L).atStartOfDay().minusNanos(1L);
			end = today.plusDays(1L).atStartOfDay();
			expression = expression.and((Predicate) QTaskInstancePO.taskInstancePO.startTime.after( start))
					.and((Predicate) QTaskInstancePO.taskInstancePO.startTime.before( end));
		}
		StringTemplate startTime = Expressions.stringTemplate((String) "DATE_FORMAT({0},'%Y-%m-%d')",
				(Object[]) new Object[] { QTaskInstancePO.taskInstancePO.startTime });
		NumberExpression count = QTaskInstancePO.taskInstancePO.id.count();
		List tuples = ((JPAQuery) ((JPAQuery) ((JPAQuery) this.queryFactory
				.select(new Expression[] { startTime, count }).from((EntityPath) QTaskInstancePO.taskInstancePO))
						.where((Predicate) expression)).groupBy((Expression) startTime)).fetch();
		if (CollectionUtils.isEmpty((Collection) tuples)) {
			return ApiResponse.success(Lists.newArrayList());
		}
		ArrayList<FlowReportSummaryModel> results = new ArrayList<FlowReportSummaryModel>(tuples.size());
		for (int index = 0; index < tuples.size(); ++index) {
			FlowReportSummaryModel summaryModel = new FlowReportSummaryModel();
			summaryModel.setId(Long.valueOf((long) index + 1L));
			summaryModel.setCode(node.getCode());
			summaryModel.setName(node.getName());
			summaryModel.setCreateNodeId(node.getId());
			summaryModel.setDate(LocalDate.parse(
					(CharSequence) Objects.requireNonNull(((Tuple) tuples.get(index)).get((Expression) startTime)))
					.plusDays(1L));
			summaryModel.setCount((Long) ((Tuple) tuples.get(index)).get((Expression) count));
			results.add(summaryModel);
		}
		return ApiResponse.success(results);
	}

	public static class NodeFlowReportQuery {
		private Long nodeId;
		private String nodeName;
		private String nodeCode;
		private TaskStatus taskStatus;
		private Long taskCount;

		public Long getNodeId() {
			return this.nodeId;
		}

		public String getNodeName() {
			return this.nodeName;
		}

		public String getNodeCode() {
			return this.nodeCode;
		}

		public TaskStatus getTaskStatus() {
			return this.taskStatus;
		}

		public Long getTaskCount() {
			return this.taskCount;
		}

		public void setNodeId(Long nodeId) {
			this.nodeId = nodeId;
		}

		public void setNodeName(String nodeName) {
			this.nodeName = nodeName;
		}

		public void setNodeCode(String nodeCode) {
			this.nodeCode = nodeCode;
		}

		public void setTaskStatus(TaskStatus taskStatus) {
			this.taskStatus = taskStatus;
		}

		public void setTaskCount(Long taskCount) {
			this.taskCount = taskCount;
		}

		public boolean equals(Object o) {
			if (o == this) {
				return true;
			}
			if (!(o instanceof NodeFlowReportQuery)) {
				return false;
			}
			NodeFlowReportQuery other = (NodeFlowReportQuery) o;
			if (!other.canEqual(this)) {
				return false;
			}
			Long this$nodeId = this.getNodeId();
			Long other$nodeId = other.getNodeId();
			if (this$nodeId == null ? other$nodeId != null : !(this$nodeId).equals(other$nodeId)) {
				return false;
			}
			String this$nodeName = this.getNodeName();
			String other$nodeName = other.getNodeName();
			if (this$nodeName == null ? other$nodeName != null : !this$nodeName.equals(other$nodeName)) {
				return false;
			}
			String this$nodeCode = this.getNodeCode();
			String other$nodeCode = other.getNodeCode();
			if (this$nodeCode == null ? other$nodeCode != null : !this$nodeCode.equals(other$nodeCode)) {
				return false;
			}
			TaskStatus this$taskStatus = this.getTaskStatus();
			TaskStatus other$taskStatus = other.getTaskStatus();
			if (this$taskStatus == null ? other$taskStatus != null : !((this$taskStatus)).equals(other$taskStatus)) {
				return false;
			}
			Long this$taskCount = this.getTaskCount();
			Long other$taskCount = other.getTaskCount();
			return !(this$taskCount == null ? other$taskCount != null : !(this$taskCount).equals(other$taskCount));
		}

		protected boolean canEqual(Object other) {
			return other instanceof NodeFlowReportQuery;
		}

		public int hashCode() {
			int PRIME = 59;
			int result = 1;
			Long $nodeId = this.getNodeId();
			result = result * 59 + ($nodeId == null ? 43 : ($nodeId).hashCode());
			String $nodeName = this.getNodeName();
			result = result * 59 + ($nodeName == null ? 43 : $nodeName.hashCode());
			String $nodeCode = this.getNodeCode();
			result = result * 59 + ($nodeCode == null ? 43 : $nodeCode.hashCode());
			TaskStatus $taskStatus = this.getTaskStatus();
			result = result * 59 + ($taskStatus == null ? 43 : (($taskStatus)).hashCode());
			Long $taskCount = this.getTaskCount();
			result = result * 59 + ($taskCount == null ? 43 : ($taskCount).hashCode());
			return result;
		}

		public String toString() {
			return "ReportServiceImpl.NodeFlowReportQuery(nodeId=" + this.getNodeId() + ", nodeName="
					+ this.getNodeName() + ", nodeCode=" + this.getNodeCode() + ", taskStatus=" + (this.getTaskStatus())
					+ ", taskCount=" + this.getTaskCount() + ")";
		}
	}
}
