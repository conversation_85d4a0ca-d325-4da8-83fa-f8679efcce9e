/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import java.io.Serializable;

public class EsNodeEventValueModel
implements Serializable {
    private Integer value;
    private String rate;
    private String eventCode;
    private String eventName;

    public Integer getValue() {
        return this.value;
    }

    public String getRate() {
        return this.rate;
    }

    public String getEventCode() {
        return this.eventCode;
    }

    public String getEventName() {
        return this.eventName;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EsNodeEventValueModel)) {
            return false;
        }
        EsNodeEventValueModel other = (EsNodeEventValueModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Integer this$value = this.getValue();
        Integer other$value = other.getValue();
        if (this$value == null ? other$value != null : !(this$value).equals(other$value)) {
            return false;
        }
        String this$rate = this.getRate();
        String other$rate = other.getRate();
        if (this$rate == null ? other$rate != null : !this$rate.equals(other$rate)) {
            return false;
        }
        String this$eventCode = this.getEventCode();
        String other$eventCode = other.getEventCode();
        if (this$eventCode == null ? other$eventCode != null : !this$eventCode.equals(other$eventCode)) {
            return false;
        }
        String this$eventName = this.getEventName();
        String other$eventName = other.getEventName();
        return !(this$eventName == null ? other$eventName != null : !this$eventName.equals(other$eventName));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EsNodeEventValueModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Integer $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : ($value).hashCode());
        String $rate = this.getRate();
        result = result * 59 + ($rate == null ? 43 : $rate.hashCode());
        String $eventCode = this.getEventCode();
        result = result * 59 + ($eventCode == null ? 43 : $eventCode.hashCode());
        String $eventName = this.getEventName();
        result = result * 59 + ($eventName == null ? 43 : $eventName.hashCode());
        return result;
    }

    public String toString() {
        return "EsNodeEventValueModel(value=" + this.getValue() + ", rate=" + this.getRate() + ", eventCode=" + this.getEventCode() + ", eventName=" + this.getEventName() + ")";
    }

    public EsNodeEventValueModel(Integer value, String rate, String eventCode, String eventName) {
        this.value = value;
        this.rate = rate;
        this.eventCode = eventCode;
        this.eventName = eventName;
    }
}

