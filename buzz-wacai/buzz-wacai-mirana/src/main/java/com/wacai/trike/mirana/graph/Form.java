/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.FormType
 */
package com.wacai.trike.mirana.graph;

import com.wacai.trike.mirana.api.constant.FormType;
import java.io.Serializable;

public class Form
implements Serializable {
    private Long id;
    private FormType type;
    private String field;
    private String fieldType;
    private String value;

    public Long getId() {
        return this.id;
    }

    public FormType getType() {
        return this.type;
    }

    public String getField() {
        return this.field;
    }

    public String getFieldType() {
        return this.fieldType;
    }

    public String getValue() {
        return this.value;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setType(FormType type) {
        this.type = type;
    }

    public void setField(String field) {
        this.field = field;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Form)) {
            return false;
        }
        Form other = (Form)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        FormType this$type = this.getType();
        FormType other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$field = this.getField();
        String other$field = other.getField();
        if (this$field == null ? other$field != null : !this$field.equals(other$field)) {
            return false;
        }
        String this$fieldType = this.getFieldType();
        String other$fieldType = other.getFieldType();
        if (this$fieldType == null ? other$fieldType != null : !this$fieldType.equals(other$fieldType)) {
            return false;
        }
        String this$value = this.getValue();
        String other$value = other.getValue();
        return !(this$value == null ? other$value != null : !this$value.equals(other$value));
    }

    protected boolean canEqual(Object other) {
        return other instanceof Form;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        FormType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $field = this.getField();
        result = result * 59 + ($field == null ? 43 : $field.hashCode());
        String $fieldType = this.getFieldType();
        result = result * 59 + ($fieldType == null ? 43 : $fieldType.hashCode());
        String $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : $value.hashCode());
        return result;
    }

    public String toString() {
        return "Form(id=" + this.getId() + ", type=" + this.getType() + ", field=" + this.getField() + ", fieldType=" + this.getFieldType() + ", value=" + this.getValue() + ")";
    }
}

