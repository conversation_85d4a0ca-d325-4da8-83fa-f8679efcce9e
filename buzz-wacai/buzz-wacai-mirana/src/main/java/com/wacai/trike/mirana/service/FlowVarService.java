/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.service;

import java.util.List;

public interface FlowVarService {
    public FlowVariableDTO save(FlowVariableDTO var1);

    public int delete(Long var1);

    public FlowVariableDTO query(Long var1);

    public List<FlowVariableDTO> query(FlowVarRequest var1);

    public FlowVariableDTO query(Long var1, String var2);

    public FlowVariableDTO query(String var1);

    public List<FlowVariableDTO> query();
}

