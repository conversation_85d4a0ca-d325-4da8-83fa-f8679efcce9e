/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.domain.action.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.domain.action.dto.BaseDto;
import com.wacai.trike.mirana.domain.action.dto.GenericActionDto;
import com.wacai.trike.mirana.domain.action.dto.VariableValueDto;
import com.wacai.trike.mirana.web.model.GenericTemplateRequestModel;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class GenericTemplateDto
extends BaseDto {
    private String name;
    private Long actionId;
    private String genericActionName;
    private Long appId;
    private String appName;
    private GenericActionDto genericAction;
    private List<VariableValueDto> variableValueDto;
    private List<GenericTemplateRequestModel.VariableValue> varList;

    public String getName() {
        return this.name;
    }

    public Long getActionId() {
        return this.actionId;
    }

    public String getGenericActionName() {
        return this.genericActionName;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getAppName() {
        return this.appName;
    }

    public GenericActionDto getGenericAction() {
        return this.genericAction;
    }

    public List<VariableValueDto> getVariableValueDto() {
        return this.variableValueDto;
    }

    public List<GenericTemplateRequestModel.VariableValue> getVarList() {
        return this.varList;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public void setGenericActionName(String genericActionName) {
        this.genericActionName = genericActionName;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public void setGenericAction(GenericActionDto genericAction) {
        this.genericAction = genericAction;
    }

    public void setVariableValueDto(List<VariableValueDto> variableValueDto) {
        this.variableValueDto = variableValueDto;
    }

    public void setVarList(List<GenericTemplateRequestModel.VariableValue> varList) {
        this.varList = varList;
    }

    @Override
    public String toString() {
        return "GenericTemplateDto(name=" + this.getName() + ", actionId=" + this.getActionId() + ", genericActionName=" + this.getGenericActionName() + ", appId=" + this.getAppId() + ", appName=" + this.getAppName() + ", genericAction=" + this.getGenericAction() + ", variableValueDto=" + this.getVariableValueDto() + ", varList=" + this.getVarList() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericTemplateDto)) {
            return false;
        }
        GenericTemplateDto other = (GenericTemplateDto)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        Long this$actionId = this.getActionId();
        Long other$actionId = other.getActionId();
        if (this$actionId == null ? other$actionId != null : !(this$actionId).equals(other$actionId)) {
            return false;
        }
        String this$genericActionName = this.getGenericActionName();
        String other$genericActionName = other.getGenericActionName();
        if (this$genericActionName == null ? other$genericActionName != null : !this$genericActionName.equals(other$genericActionName)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$appName = this.getAppName();
        String other$appName = other.getAppName();
        if (this$appName == null ? other$appName != null : !this$appName.equals(other$appName)) {
            return false;
        }
        GenericActionDto this$genericAction = this.getGenericAction();
        GenericActionDto other$genericAction = other.getGenericAction();
        if (this$genericAction == null ? other$genericAction != null : !(this$genericAction).equals(other$genericAction)) {
            return false;
        }
        List<VariableValueDto> this$variableValueDto = this.getVariableValueDto();
        List<VariableValueDto> other$variableValueDto = other.getVariableValueDto();
        if (this$variableValueDto == null ? other$variableValueDto != null : !(this$variableValueDto).equals(other$variableValueDto)) {
            return false;
        }
        List<GenericTemplateRequestModel.VariableValue> this$varList = this.getVarList();
        List<GenericTemplateRequestModel.VariableValue> other$varList = other.getVarList();
        return !(this$varList == null ? other$varList != null : !(this$varList).equals(other$varList));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof GenericTemplateDto;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Long $actionId = this.getActionId();
        result = result * 59 + ($actionId == null ? 43 : ($actionId).hashCode());
        String $genericActionName = this.getGenericActionName();
        result = result * 59 + ($genericActionName == null ? 43 : $genericActionName.hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $appName = this.getAppName();
        result = result * 59 + ($appName == null ? 43 : $appName.hashCode());
        GenericActionDto $genericAction = this.getGenericAction();
        result = result * 59 + ($genericAction == null ? 43 : ($genericAction).hashCode());
        List<VariableValueDto> $variableValueDto = this.getVariableValueDto();
        result = result * 59 + ($variableValueDto == null ? 43 : ($variableValueDto).hashCode());
        List<GenericTemplateRequestModel.VariableValue> $varList = this.getVarList();
        result = result * 59 + ($varList == null ? 43 : ($varList).hashCode());
        return result;
    }
}

