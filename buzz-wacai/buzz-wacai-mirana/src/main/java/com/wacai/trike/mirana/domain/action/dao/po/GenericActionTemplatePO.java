/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Column
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.action.dao.po;

import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="generic_action_template")
public class GenericActionTemplatePO
extends BasePO {
    private static final long serialVersionUID = 1L;
    @Column(name="action_id", nullable=false)
    private Long actionId;
    @Column(name="name", nullable=false)
    private String name;

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericActionTemplatePO)) {
            return false;
        }
        GenericActionTemplatePO other = (GenericActionTemplatePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$actionId = this.getActionId();
        Long other$actionId = other.getActionId();
        if (this$actionId == null ? other$actionId != null : !(this$actionId).equals(other$actionId)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        return !(this$name == null ? other$name != null : !this$name.equals(other$name));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof GenericActionTemplatePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $actionId = this.getActionId();
        result = result * 59 + ($actionId == null ? 43 : ($actionId).hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        return result;
    }

    public Long getActionId() {
        return this.actionId;
    }

    public String getName() {
        return this.name;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "GenericActionTemplatePO(actionId=" + this.getActionId() + ", name=" + this.getName() + ")";
    }
}

