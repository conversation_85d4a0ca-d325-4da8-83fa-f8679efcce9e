/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.util.CollectionUtils
 *  org.springframework.util.StringUtils
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.constant.InstanceStatus;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

public class TaskContext
implements Serializable {
    private String flow;
    private String node;
    private String instanceUuid;
    private String taskInstanceUuid;
    private String bzKey;
    private String actionCode;
    private String tenant;
    private long loginTimeSecond;
    private InstanceStatus status;
    private Map<String, String> param;
    private List<String> tags;

    public TaskContext tag(List<String> tags) {
        if (CollectionUtils.isEmpty(tags)) {
            return this;
        }
        tags.forEach(this::tag);
        return this;
    }

    public TaskContext tag(String tag) {
        if (StringUtils.isEmpty(tag)) {
            return this;
        }
        if (this.tags == null) {
            this.tags = new ArrayList<String>();
        }
        this.tags.add(tag);
        return this;
    }

    public Map<String, Object> toJson() {
        HashMap<String, Object> map = new HashMap<String, Object>();
        map.put("flow", this.flow);
        map.put("node", this.node);
        map.put("instanceUuid", this.instanceUuid);
        map.put("taskInstanceUuid", this.taskInstanceUuid);
        map.put("bzKey", this.bzKey);
        map.put("status", this.status);
        map.put("loginTimeSecond", this.loginTimeSecond);
        if (!CollectionUtils.isEmpty(this.param)) {
            map.putAll(this.param);
            map.put("actionCode", this.param.get("actionCode"));
            map.put("tenant", this.param.getOrDefault("tenant", "XH-GOBLIN"));
        }
        map.put("param", this.param);
        map.put("extBusinessId", this.taskInstanceUuid);
        map.put("tags", this.tags);
        return map;
    }

    public String getFlow() {
        return this.flow;
    }

    public String getNode() {
        return this.node;
    }

    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    public String getTaskInstanceUuid() {
        return this.taskInstanceUuid;
    }

    public String getBzKey() {
        return this.bzKey;
    }

    public String getActionCode() {
        return this.actionCode;
    }

    public String getTenant() {
        return this.tenant;
    }

    public long getLoginTimeSecond() {
        return this.loginTimeSecond;
    }

    public InstanceStatus getStatus() {
        return this.status;
    }

    public Map<String, String> getParam() {
        return this.param;
    }

    public List<String> getTags() {
        return this.tags;
    }

    public TaskContext setFlow(String flow) {
        this.flow = flow;
        return this;
    }

    public TaskContext setNode(String node) {
        this.node = node;
        return this;
    }

    public TaskContext setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
        return this;
    }

    public TaskContext setTaskInstanceUuid(String taskInstanceUuid) {
        this.taskInstanceUuid = taskInstanceUuid;
        return this;
    }

    public TaskContext setBzKey(String bzKey) {
        this.bzKey = bzKey;
        return this;
    }

    public TaskContext setActionCode(String actionCode) {
        this.actionCode = actionCode;
        return this;
    }

    public TaskContext setTenant(String tenant) {
        this.tenant = tenant;
        return this;
    }

    public TaskContext setLoginTimeSecond(long loginTimeSecond) {
        this.loginTimeSecond = loginTimeSecond;
        return this;
    }

    public TaskContext setStatus(InstanceStatus status) {
        this.status = status;
        return this;
    }

    public TaskContext setParam(Map<String, String> param) {
        this.param = param;
        return this;
    }

    public TaskContext setTags(List<String> tags) {
        this.tags = tags;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof TaskContext)) {
            return false;
        }
        TaskContext other = (TaskContext)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        if (this$flow == null ? other$flow != null : !this$flow.equals(other$flow)) {
            return false;
        }
        String this$node = this.getNode();
        String other$node = other.getNode();
        if (this$node == null ? other$node != null : !this$node.equals(other$node)) {
            return false;
        }
        String this$instanceUuid = this.getInstanceUuid();
        String other$instanceUuid = other.getInstanceUuid();
        if (this$instanceUuid == null ? other$instanceUuid != null : !this$instanceUuid.equals(other$instanceUuid)) {
            return false;
        }
        String this$taskInstanceUuid = this.getTaskInstanceUuid();
        String other$taskInstanceUuid = other.getTaskInstanceUuid();
        if (this$taskInstanceUuid == null ? other$taskInstanceUuid != null : !this$taskInstanceUuid.equals(other$taskInstanceUuid)) {
            return false;
        }
        String this$bzKey = this.getBzKey();
        String other$bzKey = other.getBzKey();
        if (this$bzKey == null ? other$bzKey != null : !this$bzKey.equals(other$bzKey)) {
            return false;
        }
        String this$actionCode = this.getActionCode();
        String other$actionCode = other.getActionCode();
        if (this$actionCode == null ? other$actionCode != null : !this$actionCode.equals(other$actionCode)) {
            return false;
        }
        String this$tenant = this.getTenant();
        String other$tenant = other.getTenant();
        if (this$tenant == null ? other$tenant != null : !this$tenant.equals(other$tenant)) {
            return false;
        }
        if (this.getLoginTimeSecond() != other.getLoginTimeSecond()) {
            return false;
        }
        InstanceStatus this$status = this.getStatus();
        InstanceStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !((this$status)).equals(other$status)) {
            return false;
        }
        Map<String, String> this$param = this.getParam();
        Map<String, String> other$param = other.getParam();
        if (this$param == null ? other$param != null : !(this$param).equals(other$param)) {
            return false;
        }
        List<String> this$tags = this.getTags();
        List<String> other$tags = other.getTags();
        return !(this$tags == null ? other$tags != null : !(this$tags).equals(other$tags));
    }

    protected boolean canEqual(Object other) {
        return other instanceof TaskContext;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        String $node = this.getNode();
        result = result * 59 + ($node == null ? 43 : $node.hashCode());
        String $instanceUuid = this.getInstanceUuid();
        result = result * 59 + ($instanceUuid == null ? 43 : $instanceUuid.hashCode());
        String $taskInstanceUuid = this.getTaskInstanceUuid();
        result = result * 59 + ($taskInstanceUuid == null ? 43 : $taskInstanceUuid.hashCode());
        String $bzKey = this.getBzKey();
        result = result * 59 + ($bzKey == null ? 43 : $bzKey.hashCode());
        String $actionCode = this.getActionCode();
        result = result * 59 + ($actionCode == null ? 43 : $actionCode.hashCode());
        String $tenant = this.getTenant();
        result = result * 59 + ($tenant == null ? 43 : $tenant.hashCode());
        long $loginTimeSecond = this.getLoginTimeSecond();
        result = result * 59 + (int)($loginTimeSecond >>> 32 ^ $loginTimeSecond);
        InstanceStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
        Map<String, String> $param = this.getParam();
        result = result * 59 + ($param == null ? 43 : ($param).hashCode());
        List<String> $tags = this.getTags();
        result = result * 59 + ($tags == null ? 43 : ($tags).hashCode());
        return result;
    }

    public String toString() {
        return "TaskContext(flow=" + this.getFlow() + ", node=" + this.getNode() + ", instanceUuid=" + this.getInstanceUuid() + ", taskInstanceUuid=" + this.getTaskInstanceUuid() + ", bzKey=" + this.getBzKey() + ", actionCode=" + this.getActionCode() + ", tenant=" + this.getTenant() + ", loginTimeSecond=" + this.getLoginTimeSecond() + ", status=" + (this.getStatus()) + ", param=" + this.getParam() + ", tags=" + this.getTags() + ")";
    }
}

