/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.Query
 */
package com.wacai.trike.mirana.resource;

import com.wacai.loan.trike.common.model.Query;

public class StoryQueryModel
extends Query {
    private String bu;
    private String app;
    private String code;
    private String name;
    private String status;
    private Boolean enabled;

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getStatus() {
        return this.status;
    }

    public Boolean getEnabled() {
        return this.enabled;
    }

    public StoryQueryModel setBu(String bu) {
        this.bu = bu;
        return this;
    }

    public StoryQueryModel setApp(String app) {
        this.app = app;
        return this;
    }

    public StoryQueryModel setCode(String code) {
        this.code = code;
        return this;
    }

    public StoryQueryModel setName(String name) {
        this.name = name;
        return this;
    }

    public StoryQueryModel setStatus(String status) {
        this.status = status;
        return this;
    }

    public StoryQueryModel setEnabled(Boolean enabled) {
        this.enabled = enabled;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryQueryModel)) {
            return false;
        }
        StoryQueryModel other = (StoryQueryModel)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$status = this.getStatus();
        String other$status = other.getStatus();
        if (this$status == null ? other$status != null : !this$status.equals(other$status)) {
            return false;
        }
        Boolean this$enabled = this.getEnabled();
        Boolean other$enabled = other.getEnabled();
        return !(this$enabled == null ? other$enabled != null : !(this$enabled).equals(other$enabled));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryQueryModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : $status.hashCode());
        Boolean $enabled = this.getEnabled();
        result = result * 59 + ($enabled == null ? 43 : ($enabled).hashCode());
        return result;
    }

    public String toString() {
        return "StoryQueryModel(super=" + super.toString() + ", bu=" + this.getBu() + ", app=" + this.getApp() + ", code=" + this.getCode() + ", name=" + this.getName() + ", status=" + this.getStatus() + ", enabled=" + this.getEnabled() + ")";
    }
}

