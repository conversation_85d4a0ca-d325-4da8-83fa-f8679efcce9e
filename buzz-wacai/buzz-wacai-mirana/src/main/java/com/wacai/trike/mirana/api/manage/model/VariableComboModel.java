/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ComboBox
 *  javax.validation.constraints.NotBlank
 *  org.springframework.beans.BeanUtils
 */
package com.wacai.trike.mirana.api.manage.model;

import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.trike.mirana.api.manage.model.VariableModel;
import javax.validation.constraints.NotBlank;
import org.springframework.beans.BeanUtils;

public class VariableComboModel
extends ComboBox<String> {
    private Long id;
    @NotBlank
    private String code;
    private String dataType;
    private Object candidateContent;

    public static VariableComboModel of(VariableModel v) {
        VariableComboModel vc = new VariableComboModel();
        BeanUtils.copyProperties((v), (vc));
        return vc;
    }

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getDataType() {
        return this.dataType;
    }

    public Object getCandidateContent() {
        return this.candidateContent;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public void setCandidateContent(Object candidateContent) {
        this.candidateContent = candidateContent;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VariableComboModel)) {
            return false;
        }
        VariableComboModel other = (VariableComboModel)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$dataType = this.getDataType();
        String other$dataType = other.getDataType();
        if (this$dataType == null ? other$dataType != null : !this$dataType.equals(other$dataType)) {
            return false;
        }
        Object this$candidateContent = this.getCandidateContent();
        Object other$candidateContent = other.getCandidateContent();
        return !(this$candidateContent == null ? other$candidateContent != null : !this$candidateContent.equals(other$candidateContent));
    }

    protected boolean canEqual(Object other) {
        return other instanceof VariableComboModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $dataType = this.getDataType();
        result = result * 59 + ($dataType == null ? 43 : $dataType.hashCode());
        Object $candidateContent = this.getCandidateContent();
        result = result * 59 + ($candidateContent == null ? 43 : $candidateContent.hashCode());
        return result;
    }

    public String toString() {
        return "VariableComboModel(super=" + super.toString() + ", id=" + this.getId() + ", code=" + this.getCode() + ", dataType=" + this.getDataType() + ", candidateContent=" + this.getCandidateContent() + ")";
    }
}

