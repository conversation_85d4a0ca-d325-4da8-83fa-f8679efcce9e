/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class ExpressionModel
implements Serializable {
    private Long id;
    private String fieldType;
    private String field;
    private String operator;
    private List<String> threshold;
    private String function;
    private String args;

    public Long getId() {
        return this.id;
    }

    public String getFieldType() {
        return this.fieldType;
    }

    public String getField() {
        return this.field;
    }

    public String getOperator() {
        return this.operator;
    }

    public List<String> getThreshold() {
        return this.threshold;
    }

    public String getFunction() {
        return this.function;
    }

    public String getArgs() {
        return this.args;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public void setField(String field) {
        this.field = field;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public void setThreshold(List<String> threshold) {
        this.threshold = threshold;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public void setArgs(String args) {
        this.args = args;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ExpressionModel)) {
            return false;
        }
        ExpressionModel other = (ExpressionModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$fieldType = this.getFieldType();
        String other$fieldType = other.getFieldType();
        if (this$fieldType == null ? other$fieldType != null : !this$fieldType.equals(other$fieldType)) {
            return false;
        }
        String this$field = this.getField();
        String other$field = other.getField();
        if (this$field == null ? other$field != null : !this$field.equals(other$field)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        List<String> this$threshold = this.getThreshold();
        List<String> other$threshold = other.getThreshold();
        if (this$threshold == null ? other$threshold != null : !(this$threshold).equals(other$threshold)) {
            return false;
        }
        String this$function = this.getFunction();
        String other$function = other.getFunction();
        if (this$function == null ? other$function != null : !this$function.equals(other$function)) {
            return false;
        }
        String this$args = this.getArgs();
        String other$args = other.getArgs();
        return !(this$args == null ? other$args != null : !this$args.equals(other$args));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ExpressionModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $fieldType = this.getFieldType();
        result = result * 59 + ($fieldType == null ? 43 : $fieldType.hashCode());
        String $field = this.getField();
        result = result * 59 + ($field == null ? 43 : $field.hashCode());
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        List<String> $threshold = this.getThreshold();
        result = result * 59 + ($threshold == null ? 43 : ($threshold).hashCode());
        String $function = this.getFunction();
        result = result * 59 + ($function == null ? 43 : $function.hashCode());
        String $args = this.getArgs();
        result = result * 59 + ($args == null ? 43 : $args.hashCode());
        return result;
    }

    public String toString() {
        return "ExpressionModel(id=" + this.getId() + ", fieldType=" + this.getFieldType() + ", field=" + this.getField() + ", operator=" + this.getOperator() + ", threshold=" + this.getThreshold() + ", function=" + this.getFunction() + ", args=" + this.getArgs() + ")";
    }
}

