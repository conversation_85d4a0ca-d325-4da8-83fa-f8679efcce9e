/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Column
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.action.dao.po;

import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="generic_action_variable")
public class GenericActionVariablePO
extends BasePO {
    private static final long serialVersionUID = 1L;
    @Column(name="action_id", nullable=false)
    private Long actionId;
    @Column(name="variable_id", nullable=false)
    private Long variableId;

    public Long getActionId() {
        return this.actionId;
    }

    public Long getVariableId() {
        return this.variableId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public void setVariableId(Long variableId) {
        this.variableId = variableId;
    }

    @Override
    public String toString() {
        return "GenericActionVariablePO(actionId=" + this.getActionId() + ", variableId=" + this.getVariableId() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericActionVariablePO)) {
            return false;
        }
        GenericActionVariablePO other = (GenericActionVariablePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$actionId = this.getActionId();
        Long other$actionId = other.getActionId();
        if (this$actionId == null ? other$actionId != null : !(this$actionId).equals(other$actionId)) {
            return false;
        }
        Long this$variableId = this.getVariableId();
        Long other$variableId = other.getVariableId();
        return !(this$variableId == null ? other$variableId != null : !(this$variableId).equals(other$variableId));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof GenericActionVariablePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $actionId = this.getActionId();
        result = result * 59 + ($actionId == null ? 43 : ($actionId).hashCode());
        Long $variableId = this.getVariableId();
        result = result * 59 + ($variableId == null ? 43 : ($variableId).hashCode());
        return result;
    }
}

