/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.bu.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.bu.po.ApplicationPO;
import java.time.LocalDateTime;

public class QApplicationPO
extends EntityPathBase<ApplicationPO> {
    private static final long serialVersionUID = -1750753154L;
    public static final QApplicationPO applicationPO = new QApplicationPO("applicationPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final NumberPath<Long> buId;
    public final StringPath code;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final StringPath description;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QApplicationPO(String variable) {
        super(ApplicationPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.buId = this.createNumber("buId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.description = this.createString("description");
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QApplicationPO(Path<? extends ApplicationPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.buId = this.createNumber("buId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.description = this.createString("description");
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QApplicationPO(PathMetadata metadata) {
        super(ApplicationPO.class, metadata);
        this.active = this._super.active;
        this.buId = this.createNumber("buId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.description = this.createString("description");
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

