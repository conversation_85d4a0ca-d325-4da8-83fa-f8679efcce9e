/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.graph;

import java.util.List;

public interface FlowGraphService {
	public FlowGraph getFlowGraph(String bu, String app, String flowCode, String version);

	public FlowGraph getFlowGraph(Long flowId);

	public FlowGraph getFlowGraph(String flowCode);

	public List<String> versions(String flowCode);

	public void refreshFlowGraph(String flowCode);

	public FlowGraph refreshFlowGraph(String bu, String app, String flowCode);

	public FlowGraph refreshFlowGraph(Long flowId);

	public Node next(long flowId, long nodeId);
}
