/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.annos;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.context.InstanceContextService;
import com.wacai.trike.mirana.event.EventExchange;

@Component
public class InstanceContextSyncHandler implements MethodInvocationHandler {
	
	@Autowired
	protected InstanceContextService contextService;

	@Override
	public int order() {
		return 10;
	}

	@Override
	public void preHandle(MethodInvocation invocation) {
	}

	@Override
	public void postHandle(MethodInvocation invocation) {
		//保证参数只有一个，且为EventExchange
		if (!this.sanity(invocation)) {
			return;
		}

		EventExchange exchange = (EventExchange) invocation.getArgs()[0];
		this.contextService.persistence(exchange.getContext());
	}

	private boolean sanity(MethodInvocation invocation) {
		Object[] args = invocation.getArgs();
		if (args == null || args.length != 1 || !(args[0] instanceof EventExchange)) {
			return false;
		}
		EventExchange exchange = (EventExchange) args[0];
		return exchange.getCurrent() != null && exchange.getContext() != null;
	}
}
