/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import java.io.Serializable;

public class EventModel
implements Serializable {
    private String eventCode;
    private String eventName;

    public String getEventCode() {
        return this.eventCode;
    }

    public String getEventName() {
        return this.eventName;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public void setEventName(String eventName) {
        this.eventName = eventName;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EventModel)) {
            return false;
        }
        EventModel other = (EventModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$eventCode = this.getEventCode();
        String other$eventCode = other.getEventCode();
        if (this$eventCode == null ? other$eventCode != null : !this$eventCode.equals(other$eventCode)) {
            return false;
        }
        String this$eventName = this.getEventName();
        String other$eventName = other.getEventName();
        return !(this$eventName == null ? other$eventName != null : !this$eventName.equals(other$eventName));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EventModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $eventCode = this.getEventCode();
        result = result * 59 + ($eventCode == null ? 43 : $eventCode.hashCode());
        String $eventName = this.getEventName();
        result = result * 59 + ($eventName == null ? 43 : $eventName.hashCode());
        return result;
    }

    public String toString() {
        return "EventModel(eventCode=" + this.getEventCode() + ", eventName=" + this.getEventName() + ")";
    }

    public EventModel(String eventCode, String eventName) {
        this.eventCode = eventCode;
        this.eventName = eventName;
    }
}

