/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.elasticsearch.service.dto;

public class EsFlowTaskInstanceEventDTO {
    private String eventCode;
    private Long nodeId;

    public String getEventCode() {
        return this.eventCode;
    }

    public Long getNodeId() {
        return this.nodeId;
    }

    public void setEventCode(String eventCode) {
        this.eventCode = eventCode;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EsFlowTaskInstanceEventDTO)) {
            return false;
        }
        EsFlowTaskInstanceEventDTO other = (EsFlowTaskInstanceEventDTO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$eventCode = this.getEventCode();
        String other$eventCode = other.getEventCode();
        if (this$eventCode == null ? other$eventCode != null : !this$eventCode.equals(other$eventCode)) {
            return false;
        }
        Long this$nodeId = this.getNodeId();
        Long other$nodeId = other.getNodeId();
        return !(this$nodeId == null ? other$nodeId != null : !(this$nodeId).equals(other$nodeId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EsFlowTaskInstanceEventDTO;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $eventCode = this.getEventCode();
        result = result * 59 + ($eventCode == null ? 43 : $eventCode.hashCode());
        Long $nodeId = this.getNodeId();
        result = result * 59 + ($nodeId == null ? 43 : ($nodeId).hashCode());
        return result;
    }

    public String toString() {
        return "EsFlowTaskInstanceEventDTO(eventCode=" + this.getEventCode() + ", nodeId=" + this.getNodeId() + ")";
    }
}

