/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.event.EdgeExecutor;
import com.wacai.trike.mirana.event.EdgeProcessedEvent;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.EventListener;
import com.wacai.trike.mirana.event.NodeFinishedEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class NodeFinishedEventListener
implements EventListener<NodeFinishedEvent, EdgeProcessedEvent> {
    private static final Logger log = LogManager.getLogger(NodeFinishedEventListener.class);
    @Autowired
    private EdgeExecutor edgeExecutor;

    @Override
    public Class<NodeFinishedEvent> accept() {
        return NodeFinishedEvent.class;
    }

    @Override
    @Barrier(handlers={InstanceContextSyncHandler.class})
    public void doProcess(EventExchange<NodeFinishedEvent, EdgeProcessedEvent> exchange) {
        this.edgeExecutor.execute(exchange);
    }

    public int getOrder() {
        return 5;
    }
}

