/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSONObject
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.ocean.seraph.client.context.SeraphContext
 *  com.wacai.ocean.seraph.client.core.handler.IJobHandler
 *  com.wacai.ocean.seraph.client.core.handler.annotation.JobHandler
 *  com.wacai.trike.mirana.api.FlowProcessor
 *  com.wacai.trike.mirana.api.model.FlowProcessRequest
 *  com.wacai.trike.mirana.api.model.FlowStartRequest
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.Seraph;

import com.alibaba.fastjson.JSONObject;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.ocean.seraph.client.context.SeraphContext;
import com.wacai.ocean.seraph.client.core.handler.IJobHandler;
import com.wacai.ocean.seraph.client.core.handler.annotation.JobHandler;
import com.wacai.trike.mirana.api.FlowProcessor;
import com.wacai.trike.mirana.api.model.FlowProcessRequest;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.repository.DelayedEventRepository;
import com.wacai.trike.mirana.metrics.DelayedEventMetrics;
import com.wacai.trike.mirana.metrics.MetricsProvider;
import com.wacai.trike.mirana.util.ObjectMappers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

@Component
@JobHandler(ServiceCode="startDelayTimerHandler")
public class FlowDelayHandler
implements IJobHandler {
    private static final Logger log = LoggerFactory.getLogger(FlowDelayHandler.class);
    @Autowired
    private DelayedEventRepository eventRepository;
    @Autowired
    private FlowProcessor flowProcessor;
    @Autowired
    private MetricsProvider metricsProvider;
    @Autowired
    private ThreadPoolTaskExecutor executor;

    public void execute(SeraphContext context) throws Exception {
        JSONObject event = JSONObject.parseObject((String)context.getParam());
        String uuid = event.getString("uuid");
        DelayedEventPO delayedEvent = this.eventRepository.findByUuid(event.getString("uuid")).orElse(null);
        if (delayedEvent == null) {
            log.error("delayedEvent is null, uuid [{}]", uuid);
            return;
        }
        if (delayedEvent.getStatus() == DelayedEventStatus.CANCELED) {
            log.error("delayedEvent is CANCELED, uuid [{}]", uuid);
            return;
        }
        this.executor.submit(() -> this.handle(delayedEvent));
    }

    public void handle(DelayedEventPO delayedEvent) {
        try {
            ApiResponse response;
            int updated = this.eventRepository.updateStatusByIdAndStatus(DelayedEventStatus.FIRED, delayedEvent.getId(), DelayedEventStatus.LOADED);
            if (updated <= 0) {
                return;
            }
            log.info("fired event at instance [{}] , event [{}]", delayedEvent.getInstanceUuid(), delayedEvent.getUuid());
            if (FlowStartRequest.class.getName().equals(delayedEvent.getType())) {
                FlowStartRequest request = ObjectMappers.mustReadValue(delayedEvent.getParam(), FlowStartRequest.class);
                request.getParam().put("startDelayTimer", null);
                response = this.flowProcessor.start(request);
                this.metricsProvider.metricsDelayedEvent(DelayedEventMetrics.build(request, null, DelayedEventMetrics.Category.SUBSCRIBED));
            } else if (FlowProcessRequest.class.getName().equals(delayedEvent.getType())) {
                FlowProcessRequest request = ObjectMappers.mustReadValue(delayedEvent.getParam(), FlowProcessRequest.class);
                response = this.flowProcessor.process(request);
                this.metricsProvider.metricsDelayedEvent(DelayedEventMetrics.build(request, null, DelayedEventMetrics.Category.SUBSCRIBED));
            } else {
                response = ApiResponse.error((String)"Non-compliant class type");
            }
            if (response.success()) {
                log.info("successfully delay start flow {}", ObjectMappers.mustWriteValue(response.getData()));
            } else {
                log.error("failure delay start flow {}", ObjectMappers.mustWriteValue(delayedEvent));
            }
        }
        catch (Exception e) {
            log.error("delayedEvent execute error, uuid [{}]", delayedEvent.getUuid(), e);
        }
    }
}

