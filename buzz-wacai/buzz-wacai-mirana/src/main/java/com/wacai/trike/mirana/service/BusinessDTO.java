/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.service;

import com.wacai.trike.mirana.resource.BusinessModel;
import com.wacai.trike.mirana.service.BaseDTO;
import com.wacai.trike.mirana.util.ObjectUtils;

public class BusinessDTO
extends BaseDTO {
    private Long id;
    private String code;
    private String name;

    public BusinessModel toModel() {
        return ObjectUtils.convert(this, BusinessModel.class);
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BusinessDTO)) {
            return false;
        }
        BusinessDTO other = (BusinessDTO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        return !(this$name == null ? other$name != null : !this$name.equals(other$name));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof BusinessDTO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        return result;
    }

    @Override
    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "BusinessDTO(id=" + this.getId() + ", code=" + this.getCode() + ", name=" + this.getName() + ")";
    }
}

