/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.edge.po;

import com.wacai.trike.mirana.common.enums.EdgeType;
import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="edge")
public class EdgePO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private Long flowId;
    @Enumerated(value=EnumType.STRING)
    private EdgeType fromType;
    private Long fromId;
    @Enumerated(value=EnumType.STRING)
    private EdgeType toType;
    private Long toId;
    private String location;

    public Long getFlowId() {
        return this.flowId;
    }

    public EdgeType getFromType() {
        return this.fromType;
    }

    public Long getFromId() {
        return this.fromId;
    }

    public EdgeType getToType() {
        return this.toType;
    }

    public Long getToId() {
        return this.toId;
    }

    public String getLocation() {
        return this.location;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setFromType(EdgeType fromType) {
        this.fromType = fromType;
    }

    public void setFromId(Long fromId) {
        this.fromId = fromId;
    }

    public void setToType(EdgeType toType) {
        this.toType = toType;
    }

    public void setToId(Long toId) {
        this.toId = toId;
    }

    public void setLocation(String location) {
        this.location = location;
    }

    @Override
    public String toString() {
        return "EdgePO(flowId=" + this.getFlowId() + ", fromType=" + (this.getFromType()) + ", fromId=" + this.getFromId() + ", toType=" + (this.getToType()) + ", toId=" + this.getToId() + ", location=" + this.getLocation() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EdgePO)) {
            return false;
        }
        EdgePO other = (EdgePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        EdgeType this$fromType = this.getFromType();
        EdgeType other$fromType = other.getFromType();
        if (this$fromType == null ? other$fromType != null : !((this$fromType)).equals(other$fromType)) {
            return false;
        }
        Long this$fromId = this.getFromId();
        Long other$fromId = other.getFromId();
        if (this$fromId == null ? other$fromId != null : !(this$fromId).equals(other$fromId)) {
            return false;
        }
        EdgeType this$toType = this.getToType();
        EdgeType other$toType = other.getToType();
        if (this$toType == null ? other$toType != null : !((this$toType)).equals(other$toType)) {
            return false;
        }
        Long this$toId = this.getToId();
        Long other$toId = other.getToId();
        if (this$toId == null ? other$toId != null : !(this$toId).equals(other$toId)) {
            return false;
        }
        String this$location = this.getLocation();
        String other$location = other.getLocation();
        return !(this$location == null ? other$location != null : !this$location.equals(other$location));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof EdgePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        EdgeType $fromType = this.getFromType();
        result = result * 59 + ($fromType == null ? 43 : (($fromType)).hashCode());
        Long $fromId = this.getFromId();
        result = result * 59 + ($fromId == null ? 43 : ($fromId).hashCode());
        EdgeType $toType = this.getToType();
        result = result * 59 + ($toType == null ? 43 : (($toType)).hashCode());
        Long $toId = this.getToId();
        result = result * 59 + ($toId == null ? 43 : ($toId).hashCode());
        String $location = this.getLocation();
        result = result * 59 + ($location == null ? 43 : $location.hashCode());
        return result;
    }
}

