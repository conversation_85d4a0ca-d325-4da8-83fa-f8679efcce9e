/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.common.redis.RedisException
 *  com.wacai.trike.cloud.proxy.redis.RedisProxy
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.DisposableBean
 *  org.springframework.beans.factory.InitializingBean
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.beans.factory.annotation.Value
 *  org.springframework.stereotype.Service
 */
package com.wacai.trike.mirana.util;

import com.wacai.common.redis.RedisException;
import com.wacai.trike.cloud.proxy.redis.RedisProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

@Service
public class DistributeSequenceIdGenerator
implements InitializingBean,
DisposableBean {
    private static final Logger log = LoggerFactory.getLogger(DistributeSequenceIdGenerator.class);
    @Autowired
    private RedisProxy redisProxy;
    @Value(value="${spring.application.name}")
    String appName;
    private static final long initialStamp = 1420041600000L;
    private static final long workerIdBits = 5L;
    private static final long dataCenterIdBits = 5L;
    private static final long maxWorkerId = 31L;
    private static final long maxDataCenterId = 31L;
    private static final long sequenceBits = 12L;
    private static final long workerIdShift = 12L;
    private static final long dataCenterIdShift = 17L;
    private static final long timestampLeftShift = 22L;
    private static final long sequenceMask = 4095L;
    private long workerId;
    private long dataCenterId;
    private long sequence = 0L;
    private long lastTimestamp = -1L;

    public String nextID() {
        return String.valueOf(this.nextId());
    }

    public synchronized long nextId() {
        long timestamp = this.timeGen();
        if (timestamp < this.lastTimestamp) {
            throw new RuntimeException(String.format("Clock moved backwards.  Refusing to generate id for %d milliseconds", this.lastTimestamp - timestamp));
        }
        if (this.lastTimestamp == timestamp) {
            this.sequence = this.sequence + 1L & 0xFFFL;
            if (this.sequence == 0L) {
                timestamp = this.tilNextMillis(this.lastTimestamp);
            }
        } else {
            this.sequence = 0L;
        }
        this.lastTimestamp = timestamp;
        return timestamp - 1420041600000L << 22 | this.dataCenterId << 17 | this.workerId << 12 | this.sequence;
    }

    private long tilNextMillis(long lastTimestamp) {
        long timestamp = this.timeGen();
        while (timestamp <= lastTimestamp) {
            timestamp = this.timeGen();
        }
        return timestamp;
    }

    private long timeGen() {
        return System.currentTimeMillis();
    }

    public void afterPropertiesSet() throws Exception {
        this.dataCenterId = 0L;
        this.workerId = -1L;
        int i = 0;
        while ((long)i <= 31L) {
            if (this.redisProxy.setnx(this.appName + i, this.appName) == 1L) {
                this.redisProxy.expire(this.appName + i, 31536000);
                this.workerId = i;
                break;
            }
            ++i;
        }
        if (this.workerId > 31L || this.workerId < 0L) {
            throw new IllegalArgumentException(String.format("worker Id can't be greater than %d or less than 0", 31L));
        }
        if (this.dataCenterId > 31L || this.dataCenterId < 0L) {
            throw new IllegalArgumentException(String.format("dataCenter Id can't be greater than %d or less than 0", 31L));
        }
        log.info("{} \u521d\u59cb\u5316\u6210\u529f, dataCenterId={}, workerId={}", new Object[]{this.getClass().getSimpleName(), this.dataCenterId, this.workerId});
    }

    public void destroy() {
        try {
            this.redisProxy.del(new String[]{this.appName + this.workerId});
            log.info("workerId={} redis\u91ca\u653e", this.workerId);
        }
        catch (RedisException e) {
            log.error("workerId={} redis\u91ca\u653e\u5f02\u5e38", this.workerId);
        }
    }
}

