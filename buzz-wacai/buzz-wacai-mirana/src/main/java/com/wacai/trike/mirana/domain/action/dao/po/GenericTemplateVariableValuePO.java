/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Column
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.action.dao.po;

import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name = "generic_template_variable_value")
public class GenericTemplateVariableValuePO extends BasePO {
	@Column(name = "template_id", nullable = false)
	private Long templateId;
	@Column(name = "variable_id", nullable = false)
	private Long variableId;
	@Column(name = "variable_value")
	private String variableValue;

	public Long getTemplateId() {
		return this.templateId;
	}

	public Long getVariableId() {
		return this.variableId;
	}

	public String getVariableValue() {
		return this.variableValue;
	}

	public void setTemplateId(Long templateId) {
		this.templateId = templateId;
	}

	public void setVariableId(Long variableId) {
		this.variableId = variableId;
	}

	public void setVariableValue(String variableValue) {
		this.variableValue = variableValue;
	}

	@Override
	public String toString() {
		return "GenericTemplateVariableValuePO(templateId=" + this.getTemplateId() + ", variableId="
				+ this.getVariableId() + ", variableValue=" + this.getVariableValue() + ")";
	}

	@Override
	public boolean equals(Object o) {
		if (o == this) {
			return true;
		}
		if (!(o instanceof GenericTemplateVariableValuePO)) {
			return false;
		}
		GenericTemplateVariableValuePO other = (GenericTemplateVariableValuePO) o;
		if (!other.canEqual(this)) {
			return false;
		}
		if (!super.equals(o)) {
			return false;
		}
		Long this$templateId = this.getTemplateId();
		Long other$templateId = other.getTemplateId();
		if (this$templateId == null ? other$templateId != null : !( this$templateId).equals(other$templateId)) {
			return false;
		}
		Long this$variableId = this.getVariableId();
		Long other$variableId = other.getVariableId();
		if (this$variableId == null ? other$variableId != null : !( this$variableId).equals(other$variableId)) {
			return false;
		}
		String this$variableValue = this.getVariableValue();
		String other$variableValue = other.getVariableValue();
		return !(this$variableValue == null ? other$variableValue != null
				: !this$variableValue.equals(other$variableValue));
	}

	@Override
	protected boolean canEqual(Object other) {
		return other instanceof GenericTemplateVariableValuePO;
	}

	@Override
	public int hashCode() {
		int PRIME = 59;
		int result = super.hashCode();
		Long $templateId = this.getTemplateId();
		result = result * 59 + ($templateId == null ? 43 : ( $templateId).hashCode());
		Long $variableId = this.getVariableId();
		result = result * 59 + ($variableId == null ? 43 : ( $variableId).hashCode());
		String $variableValue = this.getVariableValue();
		result = result * 59 + ($variableValue == null ? 43 : $variableValue.hashCode());
		return result;
	}
}
