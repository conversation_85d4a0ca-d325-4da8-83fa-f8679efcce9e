/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.web.model;

public class GenericVariableRequest {
    private int pageSize = 12;
    private int pageIndex = 1;
    private String name;
    private String code;
    private Boolean active = Boolean.TRUE;

    public int getPageSize() {
        return this.pageSize;
    }

    public int getPageIndex() {
        return this.pageIndex;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public Boolean getActive() {
        return this.active;
    }

    public GenericVariableRequest setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public GenericVariableRequest setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
        return this;
    }

    public GenericVariableRequest setName(String name) {
        this.name = name;
        return this;
    }

    public GenericVariableRequest setCode(String code) {
        this.code = code;
        return this;
    }

    public GenericVariableRequest setActive(Boolean active) {
        this.active = active;
        return this;
    }

    public String toString() {
        return "GenericVariableRequest(pageSize=" + this.getPageSize() + ", pageIndex=" + this.getPageIndex() + ", name=" + this.getName() + ", code=" + this.getCode() + ", active=" + this.getActive() + ")";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericVariableRequest)) {
            return false;
        }
        GenericVariableRequest other = (GenericVariableRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (this.getPageSize() != other.getPageSize()) {
            return false;
        }
        if (this.getPageIndex() != other.getPageIndex()) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        Boolean this$active = this.getActive();
        Boolean other$active = other.getActive();
        return !(this$active == null ? other$active != null : !(this$active).equals(other$active));
    }

    protected boolean canEqual(Object other) {
        return other instanceof GenericVariableRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        result = result * 59 + this.getPageSize();
        result = result * 59 + this.getPageIndex();
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        Boolean $active = this.getActive();
        result = result * 59 + ($active == null ? 43 : ($active).hashCode());
        return result;
    }
}

