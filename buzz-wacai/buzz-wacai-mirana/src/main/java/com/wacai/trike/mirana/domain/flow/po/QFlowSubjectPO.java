/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.flow.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.flow.po.FlowSubjectPO;
import java.time.LocalDateTime;

public class QFlowSubjectPO
extends EntityPathBase<FlowSubjectPO> {
    private static final long serialVersionUID = -1008774255L;
    public static final QFlowSubjectPO flowSubjectPO = new QFlowSubjectPO("flowSubjectPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final StringPath flowCode;
    public final NumberPath<Long> id;
    public final StringPath subjectCode;
    public final NumberPath<Long> subjectId;
    public final StringPath subjectName;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QFlowSubjectPO(String variable) {
        super(FlowSubjectPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowCode = this.createString("flowCode");
        this.id = this._super.id;
        this.subjectCode = this.createString("subjectCode");
        this.subjectId = this.createNumber("subjectId", Long.class);
        this.subjectName = this.createString("subjectName");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QFlowSubjectPO(Path<? extends FlowSubjectPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowCode = this.createString("flowCode");
        this.id = this._super.id;
        this.subjectCode = this.createString("subjectCode");
        this.subjectId = this.createNumber("subjectId", Long.class);
        this.subjectName = this.createString("subjectName");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QFlowSubjectPO(PathMetadata metadata) {
        super(FlowSubjectPO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowCode = this.createString("flowCode");
        this.id = this._super.id;
        this.subjectCode = this.createString("subjectCode");
        this.subjectId = this.createNumber("subjectId", Long.class);
        this.subjectName = this.createString("subjectName");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

