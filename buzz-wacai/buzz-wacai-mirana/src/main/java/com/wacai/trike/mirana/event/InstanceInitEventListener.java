/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.annos.NodeInitializedHandler;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.InstanceContext;

@Component
public class InstanceInitEventListener implements EventListener<InstanceInitEvent, NodeInitEvent> {

	@Override
	public Class<InstanceInitEvent> accept() {
		return InstanceInitEvent.class;
	}

	@Override
	public boolean needProcess(EventExchange<InstanceInitEvent, NodeInitEvent> exchange) {
		return EventListener.super.checkTask(exchange);
	}

	@Override
	@Barrier(handlers = { NodeInitializedHandler.class, InstanceContextSyncHandler.class })
	public void doProcess(EventExchange<InstanceInitEvent, NodeInitEvent> exchange) {
		InstanceContext context = exchange.getContext();
		InstanceInitEvent current = exchange.getCurrent();
		context.addVariable(current.getParam(), VariableType.FLOW_ATTACHED);
		exchange.setNext(
				new NodeInitEvent()
				.setTaskId(context.getCurrentTaskInstance().getUuid())
				.setInstanceUuid(current.getInstanceUuid())
				.setOperator(current.getOperator())
				);
	}

	public int getOrder() {
		return 2;
	}
}
