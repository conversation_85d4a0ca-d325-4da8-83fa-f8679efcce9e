/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.databind.node.ObjectNode
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.ocean.seraph.client.context.SeraphContext
 *  com.wacai.trike.mirana.api.model.FlowStartRequest
 *  io.swagger.annotations.ApiOperation
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.web;

import com.fasterxml.jackson.databind.node.ObjectNode;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.ocean.seraph.client.context.SeraphContext;
import com.wacai.trike.mirana.Seraph.FlowDelayHandler;
import com.wacai.trike.mirana.Seraph.SeraphCallBackHandler;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.repository.DelayedEventRepository;
import com.wacai.trike.mirana.util.ObjectMappers;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/managed/delayed-event"})
public class DelayedEventController {
    private static final Logger log = LoggerFactory.getLogger(DelayedEventController.class);
    @Autowired
    private SeraphCallBackHandler jobHandler;
    @Autowired
    private FlowDelayHandler flowDelayHandler;
    @Autowired
    private DelayedEventRepository delayedEventRepository;

    @PostMapping(value={"/{uuid}"})
    @ApiOperation(value="手动触发延时事件")
    public ApiResponse<String> handle(@PathVariable(value="uuid") String uuid) {
        DelayedEventPO event = this.delayedEventRepository.findByUuid(uuid).orElse(null);
        if (event == null) {
            return ApiResponse.error((String)("not found " + uuid));
        }
        ObjectNode node = ObjectMappers.get().createObjectNode();
        node.put("uuid", uuid);
        SeraphContext context = new SeraphContext();
        context.setParam(ObjectMappers.mustWriteValue(node));
        try {
            if (event.getType().equals(FlowStartRequest.class.getName())) {
                this.flowDelayHandler.execute(context);
            } else {
                this.jobHandler.execute(context);
            }
            return ApiResponse.success(uuid);
        }
        catch (Exception e) {
            return ApiResponse.error((String)e.getMessage());
        }
    }
}

