/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.Page
 */
package com.wacai.trike.mirana.domain.action;

import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.dto.GenericActionDto;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.web.model.GenericActionRequest;
import java.util.List;

public interface GenericActionService {
    public void refreshAction();

    public GenericTemplateDto getTemplateByIdFromMemo(Long var1);

    public Page<GenericActionDto> page(GenericActionRequest var1);

    public GenericActionDto query(Long var1);

    public List<GenericActionDto> batch(GenericActionRequest var1);

    public GenericActionDto save(GenericActionDto var1);

    public int delete(Long var1);
}

