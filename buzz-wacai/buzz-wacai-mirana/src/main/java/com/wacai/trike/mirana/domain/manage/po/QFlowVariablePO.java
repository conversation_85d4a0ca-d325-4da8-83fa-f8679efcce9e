/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.manage.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.manage.po.FlowVariablePO;
import java.time.LocalDateTime;

public class QFlowVariablePO
extends EntityPathBase<FlowVariablePO> {
    private static final long serialVersionUID = 1180238860L;
    public static final QFlowVariablePO flowVariablePO = new QFlowVariablePO("flowVariablePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final NumberPath<Long> appId;
    public final StringPath candidates;
    public final StringPath candidatesUrl;
    public final StringPath code;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final StringPath sourceType;
    public final StringPath supportedOperators;
    public final StringPath type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QFlowVariablePO(String variable) {
        super(FlowVariablePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.candidates = this.createString("candidates");
        this.candidatesUrl = this.createString("candidatesUrl");
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.sourceType = this.createString("sourceType");
        this.supportedOperators = this.createString("supportedOperators");
        this.type = this.createString("type");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QFlowVariablePO(Path<? extends FlowVariablePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.candidates = this.createString("candidates");
        this.candidatesUrl = this.createString("candidatesUrl");
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.sourceType = this.createString("sourceType");
        this.supportedOperators = this.createString("supportedOperators");
        this.type = this.createString("type");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QFlowVariablePO(PathMetadata metadata) {
        super(FlowVariablePO.class, metadata);
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.candidates = this.createString("candidates");
        this.candidatesUrl = this.createString("candidatesUrl");
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.sourceType = this.createString("sourceType");
        this.supportedOperators = this.createString("supportedOperators");
        this.type = this.createString("type");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

