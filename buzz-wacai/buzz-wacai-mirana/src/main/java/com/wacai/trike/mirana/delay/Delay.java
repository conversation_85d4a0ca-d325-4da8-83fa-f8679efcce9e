/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.delay;

import com.wacai.trike.mirana.delay.DelayCalculator;
import com.wacai.trike.mirana.delay.SpecUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Objects;

public class Delay
implements Serializable,
DelayCalculator {
    private ChronoUnit unit;
    private Long value;
    private String spec;

    @Override
    public LocalDateTime expectStart(LocalDateTime referDate) {
        LocalDateTime expectStart = LocalDateTime.now();
        if (Objects.isNull(this.unit) || Objects.isNull(this.value)) {
            return expectStart;
        }
        expectStart = expectStart.plus(this.value, this.unit);
        expectStart = SpecUtil.setSpec(expectStart, this.spec);
        return expectStart;
    }

    @Override
    public LocalDateTime expectEnd(LocalDateTime referDate) {
        return this.expectStart(referDate);
    }

    public ChronoUnit getUnit() {
        return this.unit;
    }

    public Long getValue() {
        return this.value;
    }

    public String getSpec() {
        return this.spec;
    }

    public void setUnit(ChronoUnit unit) {
        this.unit = unit;
    }

    public void setValue(Long value) {
        this.value = value;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Delay)) {
            return false;
        }
        Delay other = (Delay)o;
        if (!other.canEqual(this)) {
            return false;
        }
        ChronoUnit this$unit = this.getUnit();
        ChronoUnit other$unit = other.getUnit();
        if (this$unit == null ? other$unit != null : !this$unit.equals(other$unit)) {
            return false;
        }
        Long this$value = this.getValue();
        Long other$value = other.getValue();
        if (this$value == null ? other$value != null : !(this$value).equals(other$value)) {
            return false;
        }
        String this$spec = this.getSpec();
        String other$spec = other.getSpec();
        return !(this$spec == null ? other$spec != null : !this$spec.equals(other$spec));
    }

    protected boolean canEqual(Object other) {
        return other instanceof Delay;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        ChronoUnit $unit = this.getUnit();
        result = result * 59 + ($unit == null ? 43 : $unit.hashCode());
        Long $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : ($value).hashCode());
        String $spec = this.getSpec();
        result = result * 59 + ($spec == null ? 43 : $spec.hashCode());
        return result;
    }

    public String toString() {
        return "Delay(unit=" + this.getUnit() + ", value=" + this.getValue() + ", spec=" + this.getSpec() + ")";
    }
}

