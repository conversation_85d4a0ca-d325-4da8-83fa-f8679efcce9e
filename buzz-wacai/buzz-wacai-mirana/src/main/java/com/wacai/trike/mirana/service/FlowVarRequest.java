/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Lists
 */
package com.wacai.trike.mirana.service;

import com.google.common.collect.Lists;
import java.util.List;

public class FlowVarRequest {
    private List<Long> ids;
    private List<Long> appIds;
    private List<String> codes;

    public FlowVarRequest addId(Long id) {
        if (this.ids == null) {
            this.ids = Lists.newArrayList();
        }
        if (!this.ids.contains(id)) {
            this.ids.add(id);
        }
        return this;
    }

    public FlowVarRequest addAppId(Long appId) {
        if (this.appIds == null) {
            this.appIds = Lists.newArrayList();
        }
        if (!this.appIds.contains(appId)) {
            this.appIds.add(appId);
        }
        return this;
    }

    public FlowVarRequest addCode(String code) {
        if (this.codes == null) {
            this.codes = Lists.newArrayList();
        }
        if (!this.codes.contains(code)) {
            this.codes.add(code);
        }
        return this;
    }

    public List<Long> getIds() {
        return this.ids;
    }

    public List<Long> getAppIds() {
        return this.appIds;
    }

    public List<String> getCodes() {
        return this.codes;
    }

    public FlowVarRequest setIds(List<Long> ids) {
        this.ids = ids;
        return this;
    }

    public FlowVarRequest setAppIds(List<Long> appIds) {
        this.appIds = appIds;
        return this;
    }

    public FlowVarRequest setCodes(List<String> codes) {
        this.codes = codes;
        return this;
    }

    public String toString() {
        return "FlowVarRequest(ids=" + this.getIds() + ", appIds=" + this.getAppIds() + ", codes=" + this.getCodes() + ")";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowVarRequest)) {
            return false;
        }
        FlowVarRequest other = (FlowVarRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        List<Long> this$ids = this.getIds();
        List<Long> other$ids = other.getIds();
        if (this$ids == null ? other$ids != null : !(this$ids).equals(other$ids)) {
            return false;
        }
        List<Long> this$appIds = this.getAppIds();
        List<Long> other$appIds = other.getAppIds();
        if (this$appIds == null ? other$appIds != null : !(this$appIds).equals(other$appIds)) {
            return false;
        }
        List<String> this$codes = this.getCodes();
        List<String> other$codes = other.getCodes();
        return !(this$codes == null ? other$codes != null : !(this$codes).equals(other$codes));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowVarRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        List<Long> $ids = this.getIds();
        result = result * 59 + ($ids == null ? 43 : ($ids).hashCode());
        List<Long> $appIds = this.getAppIds();
        result = result * 59 + ($appIds == null ? 43 : ($appIds).hashCode());
        List<String> $codes = this.getCodes();
        result = result * 59 + ($codes == null ? 43 : ($codes).hashCode());
        return result;
    }
}

