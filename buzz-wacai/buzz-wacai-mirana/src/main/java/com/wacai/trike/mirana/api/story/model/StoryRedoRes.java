/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.story.model;

import java.io.Serializable;
import java.util.Set;

public class StoryRedoRes
implements Serializable {
    private Set<String> errorInstanceIds;

    public Set<String> getErrorInstanceIds() {
        return this.errorInstanceIds;
    }

    public StoryRedoRes setErrorInstanceIds(Set<String> errorInstanceIds) {
        this.errorInstanceIds = errorInstanceIds;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryRedoRes)) {
            return false;
        }
        StoryRedoRes other = (StoryRedoRes)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Set<String> this$errorInstanceIds = this.getErrorInstanceIds();
        Set<String> other$errorInstanceIds = other.getErrorInstanceIds();
        return !(this$errorInstanceIds == null ? other$errorInstanceIds != null : !(this$errorInstanceIds).equals(other$errorInstanceIds));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryRedoRes;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Set<String> $errorInstanceIds = this.getErrorInstanceIds();
        result = result * 59 + ($errorInstanceIds == null ? 43 : ($errorInstanceIds).hashCode());
        return result;
    }

    public String toString() {
        return "StoryRedoRes(errorInstanceIds=" + this.getErrorInstanceIds() + ")";
    }
}

