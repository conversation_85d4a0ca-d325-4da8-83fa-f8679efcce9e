/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.action.dao.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericVariablePO;
import com.wacai.trike.mirana.domain.action.enums.VariableEnums;
import java.time.LocalDateTime;

public class QGenericVariablePO
extends EntityPathBase<GenericVariablePO> {
    private static final long serialVersionUID = -904598470L;
    public static final QGenericVariablePO genericVariablePO = new QGenericVariablePO("genericVariablePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath candidates;
    public final StringPath candidatesUrl;
    public final StringPath code;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final EnumPath<VariableEnums.DataType> dataType;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final EnumPath<VariableEnums.SourceType> sourceType;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QGenericVariablePO(String variable) {
        super(GenericVariablePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.candidates = this.createString("candidates");
        this.candidatesUrl = this.createString("candidatesUrl");
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.dataType = this.createEnum("dataType", VariableEnums.DataType.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.sourceType = this.createEnum("sourceType", VariableEnums.SourceType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QGenericVariablePO(Path<? extends GenericVariablePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.candidates = this.createString("candidates");
        this.candidatesUrl = this.createString("candidatesUrl");
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.dataType = this.createEnum("dataType", VariableEnums.DataType.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.sourceType = this.createEnum("sourceType", VariableEnums.SourceType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QGenericVariablePO(PathMetadata metadata) {
        super(GenericVariablePO.class, metadata);
        this.active = this._super.active;
        this.candidates = this.createString("candidates");
        this.candidatesUrl = this.createString("candidatesUrl");
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.dataType = this.createEnum("dataType", VariableEnums.DataType.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.sourceType = this.createEnum("sourceType", VariableEnums.SourceType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

