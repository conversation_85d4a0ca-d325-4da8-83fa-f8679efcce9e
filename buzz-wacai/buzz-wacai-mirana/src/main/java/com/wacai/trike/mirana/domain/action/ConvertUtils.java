/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.common.utils.StringUtils
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.google.common.base.Strings
 *  com.wacai.loan.trike.common.model.Page
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.domain.action;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.dao.po.GenericVariablePO;
import com.wacai.trike.mirana.domain.action.dto.GenericVariableDto;
import com.wacai.trike.mirana.domain.action.enums.VariableEnums;
import com.wacai.trike.mirana.util.HttpUtil;
import com.wacai.trike.mirana.util.ObjectMappers;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.web.model.GenericVariableBatchModel;
import com.wacai.trike.mirana.web.model.GenericVariablePageModel;
import com.wacai.trike.mirana.web.model.GenericVariableSaveModel;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public final class ConvertUtils {
    public GenericVariableDto toDto(GenericVariablePO po) {
        String candidates;
        GenericVariableDto variableDto = ObjectUtils.convertNotNull(po, GenericVariableDto.class);
        if (po.getSourceType() == VariableEnums.SourceType.DEFINITION && !Strings.isNullOrEmpty((String)po.getCandidates())) {
            variableDto.setCandidates(ObjectMappers.mustReadValue(po.getCandidates(), new TypeReference<List<String>>(){}));
        }
        if (po.getSourceType() == VariableEnums.SourceType.REMOTE && !Strings.isNullOrEmpty((String)(candidates = HttpUtil.get(po.getCandidatesUrl())))) {
            variableDto.setCandidates(ObjectMappers.mustReadValue(candidates, new TypeReference<List<String>>(){}));
        }
        return variableDto;
    }

    public GenericVariableDto toDto(GenericVariableSaveModel model) {
        return ObjectUtils.convertNotNull(model, GenericVariableDto.class);
    }

    public GenericVariablePO toPO(GenericVariableDto dto) {
        GenericVariablePO variablePO = ObjectUtils.convertNotNull(dto, GenericVariablePO.class);
        if (dto.getSourceType() == VariableEnums.SourceType.DEFINITION) {
            variablePO.setCandidates(ObjectMappers.mustWriteValue(dto.getCandidates()));
        }
        return variablePO;
    }

    public Page<GenericVariablePageModel> convert(Page<GenericVariableDto> page) {
        return new Page().setTotal(page.getTotal()).setPage(page.getPage()).setData(this.toPageModels(page.getData()));
    }

    public List<GenericVariablePageModel> toPageModels(List<GenericVariableDto> dtos) {
        return dtos.stream().map(this::toPageModel).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public GenericVariablePageModel toPageModel(GenericVariableDto dto) {
        GenericVariablePageModel model = ObjectUtils.convertNotNull(dto, GenericVariablePageModel.class);
        if (!CollectionUtils.isEmpty(dto.getCandidates())) {
            model.setCandidates(this.toString(dto.getCandidates()));
        }
        model.setDataType(dto.getDataType().getDesc());
        model.setSourceType(dto.getSourceType().getDesc());
        return model;
    }

    public List<GenericVariableBatchModel> toBatchModels(List<GenericVariableDto> dtos) {
        return dtos.stream().map(this::toBatchModel).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public GenericVariableBatchModel toBatchModel(GenericVariableDto dto) {
        return ObjectUtils.convertNotNull(dto, GenericVariableBatchModel.class);
    }

    private String toString(List<String> candidates) {
        return StringUtils.join(candidates, (String)",");
    }
}

