package com.wacai.trike.mirana.api.constant;

import lombok.Getter;

/**
 * 默认情况下一个app下的流程code支持多个版本{@link FlowStatus}, 流程状态规则限制如下：
 * <li> 1. 流程最多只存在一个{@code #EDITING}状态的版本；
 * <li> 2. 流程最多只存在一个{@code #ENABLED}状态的版本；
 *
 * <AUTHOR>
 * 2018/7/30
 */
public enum FlowStatus {
    /**
     * Flow新增默认状态，可在当前状态下不限次数保存{@code EDITING}，可切换至{@code DISABLED}
     */
    EDITING("编辑中") {
        @Override
        public boolean switchTo(FlowStatus target) {
            return target == EDITING || target == DISABLED || target == ENABLED;
        }
    },
    /**
     * 编辑完成，未启用状态，可切换至{@code ENABLED}
     * 可在当前状态下升级版本至{@code EDITING}
     */
    DISABLED("已停用") {
        @Override
        public boolean switchTo(FlowStatus target) {
            return target == EDITING || target == ENABLED;
        }
    },
    /**
     * 流程发布状态, 可切换至{@code DISABLED}
     * 可在当前状态下升级版本至{@code EDITING}
     */
    ENABLED("启用中") {
        @Override
        public boolean switchTo(FlowStatus target) {
            return target == EDITING || target == DISABLED;
        }
    };

    public abstract boolean switchTo(FlowStatus target);

    @Getter
    private String desc;

    FlowStatus(String desc) {
        this.desc = desc;
    }
}
