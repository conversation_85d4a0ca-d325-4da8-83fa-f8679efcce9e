/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.model.FlowBaseRequest;
import javax.validation.constraints.NotBlank;

public class FlowCancelRequest
extends FlowBaseRequest {
    @NotBlank
    private String instanceId;

    public String getInstanceId() {
        return this.instanceId;
    }

    public FlowCancelRequest setInstanceId(String instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    @Override
    public String toString() {
        return "FlowCancelRequest(instanceId=" + this.getInstanceId() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowCancelRequest)) {
            return false;
        }
        FlowCancelRequest other = (FlowCancelRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$instanceId = this.getInstanceId();
        String other$instanceId = other.getInstanceId();
        return !(this$instanceId == null ? other$instanceId != null : !this$instanceId.equals(other$instanceId));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowCancelRequest;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $instanceId = this.getInstanceId();
        result = result * 59 + ($instanceId == null ? 43 : $instanceId.hashCode());
        return result;
    }
}

