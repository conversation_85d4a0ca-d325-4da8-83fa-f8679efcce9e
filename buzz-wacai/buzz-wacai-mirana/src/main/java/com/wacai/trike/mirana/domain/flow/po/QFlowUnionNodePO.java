/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.flow.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.flow.po.FlowUnionNodePO;
import java.time.LocalDateTime;

public class QFlowUnionNodePO
extends EntityPathBase<FlowUnionNodePO> {
    private static final long serialVersionUID = -1838532330L;
    public static final QFlowUnionNodePO flowUnionNodePO = new QFlowUnionNodePO("flowUnionNodePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> flowId;
    public final NumberPath<Long> id;
    public final NumberPath<Long> nodeId;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QFlowUnionNodePO(String variable) {
        super(FlowUnionNodePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QFlowUnionNodePO(Path<? extends FlowUnionNodePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QFlowUnionNodePO(PathMetadata metadata) {
        super(FlowUnionNodePO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

