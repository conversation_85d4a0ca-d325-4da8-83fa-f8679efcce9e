/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.FlowStatus
 *  org.springframework.data.jpa.repository.JpaRepository
 *  org.springframework.data.jpa.repository.Query
 */
package com.wacai.trike.mirana.domain.flow.repository;

import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.domain.flow.po.FlowPO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface FlowRepository extends JpaRepository<FlowPO, Long> {
	public List<FlowPO> findByStatus(FlowStatus var1);

	public boolean existsByAppIdAndCode(Long var1, String var2);

	@Query(value = "select f from FlowPO f join ApplicationPO a on a.id = f.appId join BusinessPO b on b.id = a.buId where b.code = ?1 and a.code = ?2 and f.code = ?3 ")
	public Optional<FlowPO> findByBuAppCode(String var1, String var2, String var3);

	public List<FlowPO> findByCode(String var1);

	public FlowPO findByCodeAndVersionAndActiveIsTrue(String var1, String var2);

	public FlowPO findByCodeAndStatus(String var1, FlowStatus var2);
}
