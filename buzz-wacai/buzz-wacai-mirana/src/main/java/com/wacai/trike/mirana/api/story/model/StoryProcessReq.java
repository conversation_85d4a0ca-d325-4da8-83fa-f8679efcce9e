/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.story.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import javax.validation.constraints.NotBlank;

public class StoryProcessReq
implements Serializable {
    @NotBlank
    private String instanceId;
    private String taskInstanceId;
    @NotBlank
    private String bu;
    @NotBlank
    private String app;
    @NotBlank
    private String flow;
    @NotBlank
    private String operator;
    private boolean async = false;
    private Map<String, String> param = new HashMap<String, String>();

    public StoryProcessReq addParam(String key, String value) {
        this.param.put(Objects.requireNonNull(key), value);
        return this;
    }

    public String getInstanceId() {
        return this.instanceId;
    }

    public String getTaskInstanceId() {
        return this.taskInstanceId;
    }

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getFlow() {
        return this.flow;
    }

    public String getOperator() {
        return this.operator;
    }

    public boolean isAsync() {
        return this.async;
    }

    public Map<String, String> getParam() {
        return this.param;
    }

    public StoryProcessReq setInstanceId(String instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    public StoryProcessReq setTaskInstanceId(String taskInstanceId) {
        this.taskInstanceId = taskInstanceId;
        return this;
    }

    public StoryProcessReq setBu(String bu) {
        this.bu = bu;
        return this;
    }

    public StoryProcessReq setApp(String app) {
        this.app = app;
        return this;
    }

    public StoryProcessReq setFlow(String flow) {
        this.flow = flow;
        return this;
    }

    public StoryProcessReq setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public StoryProcessReq setAsync(boolean async) {
        this.async = async;
        return this;
    }

    public StoryProcessReq setParam(Map<String, String> param) {
        this.param = param;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryProcessReq)) {
            return false;
        }
        StoryProcessReq other = (StoryProcessReq)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$instanceId = this.getInstanceId();
        String other$instanceId = other.getInstanceId();
        if (this$instanceId == null ? other$instanceId != null : !this$instanceId.equals(other$instanceId)) {
            return false;
        }
        String this$taskInstanceId = this.getTaskInstanceId();
        String other$taskInstanceId = other.getTaskInstanceId();
        if (this$taskInstanceId == null ? other$taskInstanceId != null : !this$taskInstanceId.equals(other$taskInstanceId)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        if (this$flow == null ? other$flow != null : !this$flow.equals(other$flow)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        if (this.isAsync() != other.isAsync()) {
            return false;
        }
        Map<String, String> this$param = this.getParam();
        Map<String, String> other$param = other.getParam();
        return !(this$param == null ? other$param != null : !(this$param).equals(other$param));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryProcessReq;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $instanceId = this.getInstanceId();
        result = result * 59 + ($instanceId == null ? 43 : $instanceId.hashCode());
        String $taskInstanceId = this.getTaskInstanceId();
        result = result * 59 + ($taskInstanceId == null ? 43 : $taskInstanceId.hashCode());
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        result = result * 59 + (this.isAsync() ? 79 : 97);
        Map<String, String> $param = this.getParam();
        result = result * 59 + ($param == null ? 43 : ($param).hashCode());
        return result;
    }

    public String toString() {
        return "StoryProcessReq(instanceId=" + this.getInstanceId() + ", taskInstanceId=" + this.getTaskInstanceId() + ", bu=" + this.getBu() + ", app=" + this.getApp() + ", flow=" + this.getFlow() + ", operator=" + this.getOperator() + ", async=" + this.isAsync() + ", param=" + this.getParam() + ")";
    }
}

