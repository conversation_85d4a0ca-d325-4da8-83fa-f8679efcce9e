/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.cache.Cache
 *  com.google.common.cache.CacheBuilder
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  javax.validation.constraints.NotNull
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.service.impl;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.trike.mirana.domain.manage.po.FlowVariablePO;
import com.wacai.trike.mirana.domain.manage.po.QFlowVariablePO;
import com.wacai.trike.mirana.domain.manage.repository.FlowVariableRepository;
import com.wacai.trike.mirana.service.FlowVarRequest;
import com.wacai.trike.mirana.service.FlowVarService;
import com.wacai.trike.mirana.service.FlowVariableDTO;

@Service
public class FlowVarServiceImpl implements FlowVarService {
	private static final String JOINER_KEY = "/";
	@Autowired
	private FlowVariableRepository flowVariableRepository;
	@Autowired
	private JPAQueryFactory jpaQueryFactory;
	private final Cache<Long, FlowVariableDTO> ID_CACHE;
	private final Cache<String, FlowVariableDTO> APP_CODE_CACHE;
	private final String ALL_CACHE_KEY = "all_cache_key";
	private final Cache<String, List<FlowVariableDTO>> ALL_CACHE = CacheBuilder.newBuilder()
			.expireAfterWrite(10L, TimeUnit.MINUTES).initialCapacity(1).maximumSize(1L).recordStats().build();

	public FlowVarServiceImpl() {
		this.ID_CACHE = CacheBuilder.newBuilder().expireAfterAccess(30L, TimeUnit.MINUTES).initialCapacity(10)
				.maximumSize(50L).recordStats().build();
		this.APP_CODE_CACHE = CacheBuilder.newBuilder().expireAfterAccess(30L, TimeUnit.MINUTES).initialCapacity(10)
				.maximumSize(50L).recordStats().build();
	}

	@Override
	public FlowVariableDTO save(FlowVariableDTO flowVariableDTO) {
		FlowVariableDTO dto = ((FlowVariablePO) this.flowVariableRepository.save(flowVariableDTO.toPo())).toDto();
		this.cache(dto);
		return dto;
	}

	@Override
	public int delete(Long id) {
		FlowVariablePO variablePO = this.flowVariableRepository.findById(id).orElse(null);
		if (variablePO == null) {
			return 0;
		}
		variablePO.setActive(false);
		this.flowVariableRepository.save(variablePO);
		this.invalidate(variablePO.toDto());
		return 1;
	}

	@Override
	public FlowVariableDTO query(Long id) {
		FlowVariableDTO cached = (FlowVariableDTO) this.ID_CACHE.getIfPresent( id);
		if (cached != null) {
			return cached;
		}
		FlowVariablePO variablePO = this.flowVariableRepository.findById(id).orElse(null);
		if (variablePO == null) {
			return null;
		}
		this.cache(variablePO.toDto());
		return variablePO.toDto();
	}

	@Override
	public List<FlowVariableDTO> query(FlowVarRequest request) {
		BooleanExpression expression = QFlowVariablePO.flowVariablePO.active.isTrue();
		if (!CollectionUtils.isEmpty(request.getIds())) {
			expression = expression.and((Predicate) QFlowVariablePO.flowVariablePO.id.in(request.getIds()));
		} else {
			if (!CollectionUtils.isEmpty(request.getAppIds())) {
				expression = expression.and((Predicate) QFlowVariablePO.flowVariablePO.appId.in(request.getAppIds()));
			}
			if (!CollectionUtils.isEmpty(request.getCodes())) {
				expression = expression.and((Predicate) QFlowVariablePO.flowVariablePO.code.in(request.getCodes()));
			}
		}
		List<FlowVariableDTO> variables = (this.jpaQueryFactory.selectFrom(QFlowVariablePO.flowVariablePO)
				.where(expression)).fetch().stream().map(FlowVariablePO::toDto).collect(Collectors.toList());
		variables.forEach(this::cache);
		return variables;
	}

	@Override
	public FlowVariableDTO query(Long appId, String code) {
		FlowVariableDTO cached = (FlowVariableDTO) this.APP_CODE_CACHE
				.getIfPresent( this.cacheKey(appId, code));
		if (cached != null) {
			return cached;
		}
		List<FlowVariableDTO> flowVariables = this.query(new FlowVarRequest().addAppId(appId).addCode(code));
		if (CollectionUtils.isEmpty(flowVariables)) {
			return null;
		}
		this.cache(flowVariables.get(0));
		return flowVariables.get(0);
	}

	@Override
	public FlowVariableDTO query(String code) {
		List<FlowVariableDTO> variables = this.query(new FlowVarRequest().addCode(code));
		if (CollectionUtils.isEmpty(variables)) {
			return null;
		}
		return variables.get(0);
	}

	@Override
	public List<FlowVariableDTO> query() {
		List<FlowVariableDTO> exist =  this.ALL_CACHE.getIfPresent( "all_cache_key");
		if (exist != null) {
			return exist;
		}
		List<FlowVariablePO> all = this.flowVariableRepository.findAll();
		List<FlowVariableDTO> collect = all.stream().filter(Objects::nonNull).map(FlowVariablePO::toDto)
				.collect(Collectors.toList());
		this.ALL_CACHE.put( "all_cache_key", collect);
		return collect;
	}

	private void cache(FlowVariableDTO dto) {
		this.ID_CACHE.put( dto.getId(),  dto);
		this.APP_CODE_CACHE.put( this.cacheKey(dto.getAppId(), dto.getCode()),  dto);
	}

	private void invalidate(FlowVariableDTO dto) {
		this.ID_CACHE.invalidate( dto.getId());
		this.APP_CODE_CACHE.invalidate( this.cacheKey(dto.getAppId(), dto.getCode()));
	}

	private String cacheKey(@NotNull Long appId, String code) {
		return appId + JOINER_KEY + code;
	}
}
