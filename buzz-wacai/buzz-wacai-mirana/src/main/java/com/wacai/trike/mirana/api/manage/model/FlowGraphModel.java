/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 *  javax.validation.constraints.NotBlank
 *  javax.validation.constraints.NotNull
 */
package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.FlowType;
import com.wacai.trike.mirana.api.manage.model.EdgeModel;
import com.wacai.trike.mirana.api.manage.model.NodeModel;
import java.io.Serializable;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class FlowGraphModel
implements Serializable {
    private Long id;
    @NotNull
    private Long appId;
    @NotBlank
    private String name;
    @NotBlank
    private String code;
    @NotBlank
    private String version;
    @NotNull
    private FlowStatus status;
    private FlowType type;
    private List<NodeModel> nodes;
    private List<EdgeModel> edges;
    private List<String> versions;
    private boolean forking;

    public Long getId() {
        return this.id;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public String getVersion() {
        return this.version;
    }

    public FlowStatus getStatus() {
        return this.status;
    }

    public FlowType getType() {
        return this.type;
    }

    public List<NodeModel> getNodes() {
        return this.nodes;
    }

    public List<EdgeModel> getEdges() {
        return this.edges;
    }

    public List<String> getVersions() {
        return this.versions;
    }

    public boolean isForking() {
        return this.forking;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setStatus(FlowStatus status) {
        this.status = status;
    }

    public void setType(FlowType type) {
        this.type = type;
    }

    public void setNodes(List<NodeModel> nodes) {
        this.nodes = nodes;
    }

    public void setEdges(List<EdgeModel> edges) {
        this.edges = edges;
    }

    public void setVersions(List<String> versions) {
        this.versions = versions;
    }

    public void setForking(boolean forking) {
        this.forking = forking;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowGraphModel)) {
            return false;
        }
        FlowGraphModel other = (FlowGraphModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$version = this.getVersion();
        String other$version = other.getVersion();
        if (this$version == null ? other$version != null : !this$version.equals(other$version)) {
            return false;
        }
        FlowStatus this$status = this.getStatus();
        FlowStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !((this$status)).equals(other$status)) {
            return false;
        }
        FlowType this$type = this.getType();
        FlowType other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        List<NodeModel> this$nodes = this.getNodes();
        List<NodeModel> other$nodes = other.getNodes();
        if (this$nodes == null ? other$nodes != null : !(this$nodes).equals(other$nodes)) {
            return false;
        }
        List<EdgeModel> this$edges = this.getEdges();
        List<EdgeModel> other$edges = other.getEdges();
        if (this$edges == null ? other$edges != null : !(this$edges).equals(other$edges)) {
            return false;
        }
        List<String> this$versions = this.getVersions();
        List<String> other$versions = other.getVersions();
        if (this$versions == null ? other$versions != null : !(this$versions).equals(other$versions)) {
            return false;
        }
        return this.isForking() == other.isForking();
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowGraphModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $version = this.getVersion();
        result = result * 59 + ($version == null ? 43 : $version.hashCode());
        FlowStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
        FlowType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        List<NodeModel> $nodes = this.getNodes();
        result = result * 59 + ($nodes == null ? 43 : ($nodes).hashCode());
        List<EdgeModel> $edges = this.getEdges();
        result = result * 59 + ($edges == null ? 43 : ($edges).hashCode());
        List<String> $versions = this.getVersions();
        result = result * 59 + ($versions == null ? 43 : ($versions).hashCode());
        result = result * 59 + (this.isForking() ? 79 : 97);
        return result;
    }

    public String toString() {
        return "FlowGraphModel(id=" + this.getId() + ", appId=" + this.getAppId() + ", name=" + this.getName() + ", code=" + this.getCode() + ", version=" + this.getVersion() + ", status=" + (this.getStatus()) + ", type=" + (this.getType()) + ", nodes=" + this.getNodes() + ", edges=" + this.getEdges() + ", versions=" + this.getVersions() + ", forking=" + this.isForking() + ")";
    }
}

