/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.context.annotation.Configuration
 */
package com.wacai.trike.mirana.Seraph;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.Map;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Configuration
@ConfigurationProperties(prefix="mirana.seraph")
public class SeraphProperties {
    private String appCode;
    private String topic;
    private Map<String, Teeth> teeth;

    public String getAppCode() {
        return this.appCode;
    }

    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public Map<String, Teeth> getTeeth() {
        return this.teeth;
    }

    public void setTeeth(Map<String, Teeth> teeth) {
        this.teeth = teeth;
    }

    public String getTopic() {
        return this.topic;
    }

    public void setTopic(String topic) {
        this.topic = topic;
    }

    @JsonInclude
    public static class Teeth {
        private String template;

        public String getTemplate() {
            return this.template;
        }

        public void setTemplate(String template) {
            this.template = template;
        }
    }
}

