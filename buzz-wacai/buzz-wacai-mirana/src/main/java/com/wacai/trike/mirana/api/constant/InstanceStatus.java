package com.wacai.trike.mirana.api.constant;

public enum InstanceStatus {
	ELIMINATED("淘汰"), RUNNING("执行中"), ENDED("执行完成"),

	@Deprecated
	PASSED("执行完成"),

	@Deprecated
	REJECTED("执行完成"), CANCELED("已取消"), DELAY_START("延时启动"), ASYNC_START("异步启动");

	private String desc;

	private InstanceStatus(String desc) {
		this.desc = desc;
	}

	public boolean isEnded() {
		return PASSED == this || REJECTED == this || CANCELED == this || ENDED == this || ELIMINATED == this;
	}

	public boolean isRunning() {
		return RUNNING == this;
	}

	@Deprecated
	public boolean isPassed() {
		return PASSED == this;
	}

	@Deprecated
	public boolean isRejected() {
		return REJECTED == this;
	}

	public boolean isCanceled() {
		return CANCELED == this;
	}

	public String getDesc() {
		return this.desc;
	}
}