/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.apache.logging.log4j.util.Strings
 */
package com.wacai.trike.mirana.util;

import org.apache.logging.log4j.util.Strings;

public class VersionAttribution {
    private static final String SPLIT_DOT = "\\.";
    private static final String JOINER_DOT = ".";
    private static final String INITIALIZE_VERSION = "0.0.1";
    private final int first;
    private final int second;
    private final int third;

    public VersionAttribution() {
        this(INITIALIZE_VERSION);
    }

    public VersionAttribution(String version) {
        if (Strings.isBlank((String)version)) {
            throw new IllegalArgumentException("version must not be blank");
        }
        String[] vs = version.trim().split(SPLIT_DOT);
        if (vs.length != 3) {
            throw new IllegalArgumentException("Invalid version format: " + version);
        }
        int[] vars = new int[3];
        for (int i = 0; i < 3; ++i) {
            int v = Integer.parseInt(vs[i]);
            if (v < 0 || v > 9) {
                throw new IllegalArgumentException("Invalid version format: " + version);
            }
            vars[i] = v;
        }
        this.first = vars[0];
        this.second = vars[1];
        this.third = vars[2];
    }

    public String currentVersion() {
        return String.join((CharSequence)JOINER_DOT, String.valueOf(this.first), String.valueOf(this.second), String.valueOf(this.third));
    }

    public String nextVersion() {
        int[] snapshot = new int[]{this.first, this.second, this.third};
        int cur = this.third + 1;
        if (cur != 10) {
            snapshot[2] = cur;
        } else {
            snapshot[2] = 0;
            cur = this.second + 1;
            if (cur != 10) {
                snapshot[1] = cur;
            } else {
                snapshot[1] = 0;
                cur = this.first + 1;
                if (cur == 10) {
                    throw new RuntimeException("Should never occurred: version out of range.");
                }
                snapshot[0] = cur;
            }
        }
        return String.join((CharSequence)JOINER_DOT, String.valueOf(snapshot[0]), String.valueOf(snapshot[1]), String.valueOf(snapshot[2]));
    }
}

