/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSON
 *  com.wacai.hermes.agent.config.HermesConsumerConfig
 *  com.wacai.hermes.agent.consumer.protocol.HermesHttpConsumer
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.trike.mirana.api.FlowProcessor
 *  com.wacai.trike.mirana.api.model.FlowStartRequest
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 */
package com.wacai.trike.mirana.consumer;

import com.alibaba.fastjson.JSON;
import com.wacai.hermes.agent.config.HermesConsumerConfig;
import com.wacai.hermes.agent.consumer.protocol.HermesHttpConsumer;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.FlowProcessor;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class FlowAsyncConsumer
extends HermesHttpConsumer {
    private static final Logger log = LoggerFactory.getLogger(FlowAsyncConsumer.class);
    @Autowired
    private FlowProcessor flowProcessor;

    public FlowAsyncConsumer(HermesConsumerConfig config) {
        super(config);
    }

    public void onMessageReceived(long offset, String key, byte[] data) {
        String message = new String(data, StandardCharsets.UTF_8);
        FlowStartRequest request = (FlowStartRequest)JSON.parseObject((String)message, FlowStartRequest.class);
        log.info("FlowAsyncConsumer-receive,flow is {},uuid is {},request is {}", new Object[]{request.getFlow(), request.getUuid(), request});
        request.setAsyncStart(false);
        request.setAsync(true);
        ApiResponse response = this.flowProcessor.start(request);
        if (!response.success()) {
            log.error("FlowAsyncConsumer-flow {} ->> {} start failure", request.getFlow(), request.getUuid());
        } else {
            log.info("FlowAsyncConsumer-flow {} ->> {} start success", request.getFlow(), request.getUuid());
        }
    }
}

