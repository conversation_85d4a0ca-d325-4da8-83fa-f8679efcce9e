/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  com.wacai.trike.mirana.api.model.TaskContext
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.task;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.api.model.TaskContext;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.task.TaskExecuteException;
import com.wacai.trike.mirana.task.TaskProcessor;
import com.wacai.trike.mirana.util.HttpUtil;
import com.wacai.trike.mirana.util.ObjectMappers;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
class HttpTaskProcessor
implements TaskProcessor {
    private static final Logger log = LoggerFactory.getLogger(HttpTaskProcessor.class);

    HttpTaskProcessor() {
    }

    @Override
    public boolean accept(TaskType taskType) {
        return TaskType.HTTP == taskType;
    }

    @Override
    public Map<String, String> execute(Node node, InstanceContext context) throws TaskExecuteException {
        try {
            TaskContext taskContext = new TaskContext().setParam(context.getEffectiveVariables()).setInstanceUuid(context.getUuid()).setBzKey(context.getBzKey()).setFlow(context.getFlowCode());
            String url = node.getTaskContent();
            log.info("execute http task of [instance: {}, node: {}, task: {}, url: {}, body: {}]", new Object[]{context.getUuid(), node.getName(), node.getTaskContent(), url, ObjectMappers.mustWriteValue(taskContext)});
            if (url.endsWith("/flow/callback/robot/deduct")) {
                return Collections.emptyMap();
            }
            String resData = HttpUtil.post(url, taskContext, node.getTaskTimeout());
            log.info("received {} from remote {} of instance [instance:{}, node:{}, task-instance:{}]", new Object[]{resData, url, context.getUuid(), node.getName(), context.getCurrentTaskInstance() != null ? context.getCurrentTaskInstance().getUuid() : null});
            Map<String, String> result = ObjectMappers.mustReadValue(resData, new TypeReference<Map<String, String>>(){});
            return Optional.ofNullable(result).orElse(Collections.emptyMap());
        }
        catch (Exception e) {
            log.error("occurred exception when execute http task of [instance: {}, node: {}, task: {}]", new Object[]{context.getUuid(), node.getName(), node.getTaskContent()});
            throw new TaskExecuteException(e);
        }
    }
}

