/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.Page
 */
package com.wacai.trike.mirana.domain.action;

import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.web.model.GenericTemplateRequest;
import java.util.List;

public interface GenericTemplateService {
    public GenericTemplateDto create(GenericTemplateDto var1);

    public GenericTemplateDto update(GenericTemplateDto var1);

    public GenericTemplateDto query(Long var1);

    public Page<GenericTemplateDto> page(GenericTemplateRequest var1);

    public List<GenericTemplateDto> batch(GenericTemplateRequest var1);
}

