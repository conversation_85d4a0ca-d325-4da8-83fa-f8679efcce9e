/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.expression.Operator
 *  com.wacai.loan.trike.expression.OperatorEvaluatorFactory
 *  com.wacai.loan.trike.expression.ThresholdType
 *  com.wacai.trike.mirana.api.constant.Function
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.event;

import com.wacai.loan.trike.expression.Operator;
import com.wacai.loan.trike.expression.OperatorEvaluatorFactory;
import com.wacai.loan.trike.expression.ThresholdType;
import com.wacai.trike.mirana.api.constant.Function;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.Step;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.EdgeProcessedEvent;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.FunctionInvoker;
import com.wacai.trike.mirana.event.InstanceEndedEvent;
import com.wacai.trike.mirana.event.NodeFinishedEvent;
import com.wacai.trike.mirana.event.NodeSkipEvent;
import com.wacai.trike.mirana.graph.Edge;
import com.wacai.trike.mirana.graph.Expression;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.notify.NotifyService;
import com.wacai.trike.mirana.util.ObjectMappers;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class EdgeExecutor {
    private static final Logger log = LoggerFactory.getLogger(EdgeExecutor.class);
    @Autowired
    private FlowGraphService graphService;
    @Autowired
    private NotifyService notifyService;

    public void execute(EventExchange exchange) {
        if (!(exchange.getCurrent() instanceof NodeSkipEvent) && !(exchange.getCurrent() instanceof NodeFinishedEvent)) {
            log.info("Non-Expected NodeSkipEvent/NodeFinishedEvent {}", ObjectMappers.mustWriteValue(exchange.getCurrent()));
            return;
        }
        Object event = exchange.getCurrent();
        InstanceContext context = exchange.getContext();
        Node node = this.graphService.getFlowGraph(context.getFlowId()).getNode(context.getCurrentNodeId());
        Map<String, String> variables = context.getEffectiveVariables();
        this.notifyService.notifyNodeOut(context.copy(), node);
        List<Edge> edges = node.getEdges();
        if (CollectionUtils.isEmpty(edges)) {
            log.info("instance {} end at last node {} - {}", new Object[]{((AbstractFlowEvent)event).getInstanceUuid(), node.getName(), node.getId()});
            exchange.setNext(this.end(context, ((AbstractFlowEvent)event).getOperator()));
            return;
        }
        Edge edge = null;
        for (Edge e : edges) {
            if (!this.canPass(e, variables)) continue;
            edge = e;
            break;
        }
        if (Objects.isNull(edge)) {
            log.info("instance {} end when node edge passed at node {} - {}", new Object[]{((AbstractFlowEvent)event).getInstanceUuid(), node.getName(), node.getId()});
            exchange.setNext(this.end(context, ((AbstractFlowEvent)event).getOperator()));
            return;
        }
        context.getSteps().add(new Step().setEdgeId(edge.getId()).setDirty(true));
        context.setCurrentNodeId(edge.getToNode().getId());
        context.setDirty(true);
        this.endTask(context);
        exchange.setNext(new EdgeProcessedEvent()
        		.setInstanceUuid(((AbstractFlowEvent)event).getInstanceUuid())
        		.setTaskId(context.getCurrentNodeId().toString())
        		.setOperator(((AbstractFlowEvent)event).getOperator()));
    }

    private boolean canPass(Edge edge, Map<String, String> param) {
        for (Expression expression : edge.getExpressions()) {
            boolean result;
            ThresholdType thresholdType = ThresholdType.valueOf((String)expression.getFieldType());
            String field = param.get(expression.getField());
            String threshold = expression.getThreshold();
            String value = field;
            if (expression.getFunction() != null) {
                Function func = Function.valueOf((String)expression.getFunction());
                String args = expression.getArgs();
                if (func == Function.UNKNOWN) {
                    log.error("functionHandler error! function unknown expressionId={}", expression.getId());
                    return false;
                }
                value = FunctionInvoker.invoke(func, field, args);
            }
            if (result = OperatorEvaluatorFactory.getEvaluator((Operator)expression.getOperator()).evaluate(value, threshold, thresholdType)) continue;
            return false;
        }
        return true;
    }

    private AbstractFlowEvent end(InstanceContext context, String operator) {
        context.setStatus(InstanceStatus.ENDED);
        context.setDirty(true);
        this.endTask(context);
        return new InstanceEndedEvent().setInstanceUuid(context.getUuid()).setTaskId(context.getUuid()).setOperator(operator);
    }

    private void endTask(InstanceContext context) {
        context.getTaskInstances().add(context.getCurrentTaskInstance());
        context.setCurrentTaskInstance(null);
    }
}

