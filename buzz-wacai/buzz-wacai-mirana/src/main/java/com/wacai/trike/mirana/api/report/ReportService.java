/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  javax.validation.Valid
 *  javax.validation.constraints.NotNull
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 */
package com.wacai.trike.mirana.api.report;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.report.model.FlowReportModel;
import com.wacai.trike.mirana.api.report.model.FlowReportQueryModel;
import com.wacai.trike.mirana.api.report.model.FlowReportSummaryModel;
import com.wacai.trike.mirana.api.report.model.NodeFlowReportModel;
import java.time.LocalDate;
import java.util.List;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping(value={"/report"})
public interface ReportService {
    @GetMapping(value={"/flow/summary"})
    public ApiResponse<List<FlowReportSummaryModel>> flowReportSummary(@Valid @NotNull FlowReportQueryModel var1);

    @GetMapping(value={"/flow/code"})
    public ApiResponse<List<NodeFlowReportModel>> flowReport(@Valid @NotNull FlowReportQueryModel var1);

    @GetMapping(value={"/flow"})
    public ApiResponse<List<NodeFlowReportModel>> flowReport(@RequestParam(value="flowId") Long var1, @RequestParam(value="dueDate", required=false) LocalDate var2, @RequestParam(value="createNodeId", required=false) Long var3);

    @GetMapping(value={"/story/code"})
    public ApiResponse<List<FlowReportModel>> storyReport(@Valid @NotNull FlowReportQueryModel var1);

    @GetMapping(value={"/story"})
    public ApiResponse<List<FlowReportModel>> storyReport(@RequestParam(value="flowId") Long var1);
}

