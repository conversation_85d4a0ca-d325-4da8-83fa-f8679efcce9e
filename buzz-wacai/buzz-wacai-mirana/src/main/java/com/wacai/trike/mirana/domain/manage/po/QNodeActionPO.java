/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 *  com.wacai.trike.mirana.api.constant.TaskType
 */
package com.wacai.trike.mirana.domain.manage.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.manage.po.NodeActionPO;
import java.time.LocalDateTime;

public class QNodeActionPO
extends EntityPathBase<NodeActionPO> {
    private static final long serialVersionUID = 1954656858L;
    public static final QNodeActionPO nodeActionPO = new QNodeActionPO("nodeActionPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final NumberPath<Long> actionConfigId = this.createNumber("actionConfigId", Long.class);
    public final BooleanPath active;
    public final StringPath content;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> id;
    public final NumberPath<Long> nodeId;
    public final NumberPath<Integer> timeout;
    public final EnumPath<TaskType> type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QNodeActionPO(String variable) {
        super(NodeActionPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.content = this.createString("content");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.timeout = this.createNumber("timeout", Integer.class);
        this.type = this.createEnum("type", TaskType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QNodeActionPO(Path<? extends NodeActionPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.content = this.createString("content");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.timeout = this.createNumber("timeout", Integer.class);
        this.type = this.createEnum("type", TaskType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QNodeActionPO(PathMetadata metadata) {
        super(NodeActionPO.class, metadata);
        this.active = this._super.active;
        this.content = this.createString("content");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.timeout = this.createNumber("timeout", Integer.class);
        this.type = this.createEnum("type", TaskType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

