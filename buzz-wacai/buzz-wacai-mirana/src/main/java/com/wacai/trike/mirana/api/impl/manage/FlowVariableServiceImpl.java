/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.alibaba.fastjson.JSON
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.jpa.JPQLQuery
 *  com.querydsl.jpa.JPQLQueryFactory
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.trike.mirana.api.manage.FlowVariableService
 *  com.wacai.trike.mirana.api.manage.model.CandidateModel
 *  com.wacai.trike.mirana.api.manage.model.FlowVariableModel
 *  com.wacai.trike.mirana.api.manage.model.OperatorModel
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.util.StringUtils
 *  org.springframework.validation.annotation.Validated
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl.manage;

import com.alibaba.dubbo.config.annotation.Service;
import com.alibaba.fastjson.JSON;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.JPQLQueryFactory;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.manage.FlowVariableService;
import com.wacai.trike.mirana.api.manage.model.CandidateModel;
import com.wacai.trike.mirana.api.manage.model.FlowVariableModel;
import com.wacai.trike.mirana.api.manage.model.OperatorModel;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.manage.po.FlowVariablePO;
import com.wacai.trike.mirana.domain.manage.repository.FlowVariableRepository;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Service(interfaceClass = FlowVariableService.class)
@Validated
public class FlowVariableServiceImpl implements FlowVariableService {
	
	private static final Logger log = LoggerFactory.getLogger(FlowVariableServiceImpl.class);
	
	@Autowired
	private JPQLQueryFactory queryFactory;
	
	@Autowired
	private FlowVariableRepository flowVariableRepository;

	public ApiResponse<List<FlowVariableModel>> get(String bu, String app) {
		Long appId = (Long) ((JPQLQuery) this.queryFactory.select(QApplicationPO.applicationPO.id)
				.from(new EntityPath[] { QApplicationPO.applicationPO }).join((EntityPath) QBusinessPO.businessPO)
				.on(new Predicate[] { QApplicationPO.applicationPO.buId.eq(QBusinessPO.businessPO.id) })
				.where(new Predicate[] { QApplicationPO.applicationPO.code.eq( app)
						.and((Predicate) QBusinessPO.businessPO.code.eq( bu)) })).fetchOne();
		
		if (appId == null) {
			return ApiResponse.success(Collections.emptyList());
		}
		List<FlowVariablePO> list = this.flowVariableRepository.findAllByAppId(appId);
		if (CollectionUtils.isEmpty(list)) {
			return ApiResponse.success(Collections.emptyList());
		}
		List modelList = list.stream().map(this::convert).collect(Collectors.toList());
		return ApiResponse.success(modelList);
	}

	private FlowVariableModel convert(FlowVariablePO po) {
		FlowVariableModel model = new FlowVariableModel();
		model.setId(po.getId());
		model.setCode(po.getCode());
		model.setName(po.getName());
		model.setType(po.getType());
		if (!StringUtils.isEmpty( po.getCandidates())) {
			model.setCandidates(JSON.parseArray((String) po.getCandidates(), CandidateModel.class));
		}
		if (!StringUtils.isEmpty( po.getSupportedOperators())) {
			model.setOperators(JSON.parseArray((String) po.getSupportedOperators(), OperatorModel.class));
		}
		return model;
	}
}
