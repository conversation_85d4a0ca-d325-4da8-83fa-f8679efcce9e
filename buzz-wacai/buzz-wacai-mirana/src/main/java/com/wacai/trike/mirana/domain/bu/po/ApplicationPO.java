/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.bu.po;

import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.service.ApplicationDTO;
import com.wacai.trike.mirana.util.ObjectUtils;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="application")
public class ApplicationPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private Long buId;
    private String code;
    private String name;
    private String description;

    public ApplicationDTO toDTO() {
        return ObjectUtils.convert(this, ApplicationDTO.class);
    }

    public Long getBuId() {
        return this.buId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getDescription() {
        return this.description;
    }

    public void setBuId(Long buId) {
        this.buId = buId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public String toString() {
        return "ApplicationPO(buId=" + this.getBuId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", description=" + this.getDescription() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ApplicationPO)) {
            return false;
        }
        ApplicationPO other = (ApplicationPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$buId = this.getBuId();
        Long other$buId = other.getBuId();
        if (this$buId == null ? other$buId != null : !(this$buId).equals(other$buId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$description = this.getDescription();
        String other$description = other.getDescription();
        return !(this$description == null ? other$description != null : !this$description.equals(other$description));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof ApplicationPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $buId = this.getBuId();
        result = result * 59 + ($buId == null ? 43 : ($buId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $description = this.getDescription();
        result = result * 59 + ($description == null ? 43 : $description.hashCode());
        return result;
    }
}

