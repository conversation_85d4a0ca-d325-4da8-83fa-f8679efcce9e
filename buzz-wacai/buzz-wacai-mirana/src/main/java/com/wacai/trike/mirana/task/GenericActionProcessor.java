/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  com.wacai.trike.mirana.api.model.TaskContext
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.task;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.api.model.TaskContext;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.domain.action.GenericActionService;
import com.wacai.trike.mirana.domain.action.dto.GenericActionDto;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.domain.action.dto.VariableValueDto;
import com.wacai.trike.mirana.domain.action.enums.ActionEnums;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.task.TaskExecuteException;
import com.wacai.trike.mirana.task.TaskProcessor;
import com.wacai.trike.mirana.util.HttpUtil;
import com.wacai.trike.mirana.util.ObjectMappers;
import com.wacai.trike.mirana.util.PlaceholderResolver;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class GenericActionProcessor
implements TaskProcessor {
    private static final Logger log = LoggerFactory.getLogger(GenericActionProcessor.class);
    @Autowired
    private GenericActionService actionService;
    @Autowired
    private List<HTTPExecutor> executors;

    @Override
    public boolean accept(TaskType taskType) {
        return TaskType.GENERIC_ACTION == taskType;
    }

    @Override
    public Map<String, String> execute(Node node, InstanceContext context) throws TaskExecuteException {
        Long templateId = node.getTemplateId();
        GenericTemplateDto templateFromMemo = this.actionService.getTemplateByIdFromMemo(templateId);
        if (templateFromMemo == null) {
            throw new TaskExecuteException("Cannot get template, no such templateId");
        }
        GenericActionDto action = templateFromMemo.getGenericAction();
        HTTPExecutor executor = this.pick(action.getType());
        if (executor == null) {
            throw new TaskExecuteException("Should never occurred: not fount executor for " + action.getType().toString());
        }
        List<VariableValueDto> variableValueDto = templateFromMemo.getVariableValueDto();
        Map<String, String> param = context.getEffectiveVariables();
        if (!CollectionUtils.isEmpty(variableValueDto)) {
            for (VariableValueDto item : variableValueDto) {
                param.put(item.getVariable().getCode(), item.getValue());
            }
        }
        Map body = new TaskContext().setParam(param).setInstanceUuid(context.getUuid()).setTaskInstanceUuid(context.getCurrentTaskInstance().getUuid()).setBzKey(context.getBzKey()).setFlow(context.getFlowCode()).tag(String.valueOf(context.getFlowId())).tag(context.getFlowCode()).tag(context.getVersion()).tag(String.valueOf(node.getId())).tag(this.buildBirthMarks(param)).toJson();
        String path = action.getCallbackUrl();
        if (PlaceholderResolver.get().placeholder(path)) {
            path = PlaceholderResolver.get().resolveByMap(path, body);
        }
        HashMap<String, String> header = new HashMap<String, String>();
        header.put("tenantCode", (String)body.get("tenant"));
        String response = executor.execute(new Request.Builder().url(path).header(header).body(body).build());
        log.info("received {} from {} that execute generic action of {}", new Object[]{response, action.getCallbackUrl(), context.getUuid()});
        Map<String, String> result = ObjectMappers.mustReadValue(response, new TypeReference<Map<String, String>>(){});
        return Optional.ofNullable(result).orElse(Collections.emptyMap());
    }

    private List<String> buildBirthMarks(Map<String, String> param) {
        String birthMarks = param.getOrDefault("birthmarks", null);
        if (birthMarks == null) {
            return null;
        }
        return ObjectMappers.mustReadValue(birthMarks, new TypeReference<List<String>>(){});
    }

    private HTTPExecutor pick(ActionEnums.Type type) {
        if (CollectionUtils.isEmpty(this.executors)) {
            return null;
        }
        return this.executors.stream().filter(executor -> executor.support(type)).findFirst().orElse(null);
    }

    static class Request {
        public final String url;
        public final Map<String, String> header;
        public final Map<String, Object> body;

        private Request(Builder builder) {
            this.url = builder.url;
            this.header = builder.header;
            this.body = builder.body;
        }

        public static class Builder {
            private String url;
            private Map<String, String> header;
            private Map<String, Object> body;

            public Request build() {
                return new Request(this);
            }

            public Builder url(String url) {
                this.url = url;
                return this;
            }

            public Builder header(Map<String, String> header) {
                this.header = header;
                return this;
            }

            public Builder body(Map<String, Object> body) {
                this.body = body;
                return this;
            }
        }
    }

    @Component
    static class POSTExecutor
    implements HTTPExecutor {
        POSTExecutor() {
        }

        @Override
        public boolean support(ActionEnums.Type type) {
            return type == ActionEnums.Type.HTTP_POST || type == ActionEnums.Type.HTTP;
        }

        @Override
        public String execute(Request request) {
            return HttpUtil.post(request.url, request.header, request.body);
        }
    }

    @Component
    static class GETExecutor
    implements HTTPExecutor {
        GETExecutor() {
        }

        @Override
        public boolean support(ActionEnums.Type type) {
            return type == ActionEnums.Type.HTTP_GET;
        }

        @Override
        public String execute(Request request) {
            return HttpUtil.get(request.url, request.header);
        }
    }

    static interface HTTPExecutor {
        default public boolean support(ActionEnums.Type type) {
            log.error("Not support current executor: {}", type);
            return false;
        }

        public String execute(Request var1);
    }
}

