/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.report.model;

import java.io.Serializable;
import java.time.LocalDate;

public class FlowReportSummaryModel
implements Serializable {
    private Long id;
    private Long createNodeId;
    private String code;
    private String name;
    private Long count;
    private LocalDate date;

    public Long getId() {
        return this.id;
    }

    public Long getCreateNodeId() {
        return this.createNodeId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Long getCount() {
        return this.count;
    }

    public LocalDate getDate() {
        return this.date;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setCreateNodeId(Long createNodeId) {
        this.createNodeId = createNodeId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowReportSummaryModel)) {
            return false;
        }
        FlowReportSummaryModel other = (FlowReportSummaryModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$createNodeId = this.getCreateNodeId();
        Long other$createNodeId = other.getCreateNodeId();
        if (this$createNodeId == null ? other$createNodeId != null : !(this$createNodeId).equals(other$createNodeId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        Long this$count = this.getCount();
        Long other$count = other.getCount();
        if (this$count == null ? other$count != null : !(this$count).equals(other$count)) {
            return false;
        }
        LocalDate this$date = this.getDate();
        LocalDate other$date = other.getDate();
        return !(this$date == null ? other$date != null : !(this$date).equals(other$date));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowReportSummaryModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $createNodeId = this.getCreateNodeId();
        result = result * 59 + ($createNodeId == null ? 43 : ($createNodeId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Long $count = this.getCount();
        result = result * 59 + ($count == null ? 43 : ($count).hashCode());
        LocalDate $date = this.getDate();
        result = result * 59 + ($date == null ? 43 : ($date).hashCode());
        return result;
    }

    public String toString() {
        return "FlowReportSummaryModel(id=" + this.getId() + ", createNodeId=" + this.getCreateNodeId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", count=" + this.getCount() + ", date=" + this.getDate() + ")";
    }
}

