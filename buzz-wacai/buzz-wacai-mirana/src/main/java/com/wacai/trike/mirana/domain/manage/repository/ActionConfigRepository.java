/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.domain.Page
 *  org.springframework.data.domain.Pageable
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.manage.repository;

import com.wacai.trike.mirana.domain.manage.po.ActionConfigPO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ActionConfigRepository
extends JpaRepository<ActionConfigPO, Long> {
    public Page<ActionConfigPO> findByAppId(Long var1, Pageable var2);
}

