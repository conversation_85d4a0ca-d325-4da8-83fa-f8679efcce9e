/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.domain.action.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.domain.action.dto.GenericVariableDto;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class VariableValueDto {
    private GenericVariableDto variable;
    private String value;

    public GenericVariableDto getVariable() {
        return this.variable;
    }

    public String getValue() {
        return this.value;
    }

    public void setVariable(GenericVariableDto variable) {
        this.variable = variable;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VariableValueDto)) {
            return false;
        }
        VariableValueDto other = (VariableValueDto)o;
        if (!other.canEqual(this)) {
            return false;
        }
        GenericVariableDto this$variable = this.getVariable();
        GenericVariableDto other$variable = other.getVariable();
        if (this$variable == null ? other$variable != null : !(this$variable).equals(other$variable)) {
            return false;
        }
        String this$value = this.getValue();
        String other$value = other.getValue();
        return !(this$value == null ? other$value != null : !this$value.equals(other$value));
    }

    protected boolean canEqual(Object other) {
        return other instanceof VariableValueDto;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        GenericVariableDto $variable = this.getVariable();
        result = result * 59 + ($variable == null ? 43 : ($variable).hashCode());
        String $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : $value.hashCode());
        return result;
    }

    public String toString() {
        return "VariableValueDto(variable=" + this.getVariable() + ", value=" + this.getValue() + ")";
    }
}

