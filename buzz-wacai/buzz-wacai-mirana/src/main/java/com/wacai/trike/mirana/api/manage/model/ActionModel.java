/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ComboBox
 *  javax.validation.constraints.NotBlank
 *  javax.validation.constraints.NotNull
 */
package com.wacai.trike.mirana.api.manage.model;

import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.api.manage.model.VariableModel;
import java.util.List;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class ActionModel
extends ComboBox<String> {
    private Long id;
    private Long appId;
    private Long flowId;
    @NotBlank
    private String code;
    private String name;
    @NotNull
    private TaskType type;
    private String content;
    private List<VariableModel> requiredVariable;

    public Long getId() {
        return this.id;
    }

    public Long getAppId() {
        return this.appId;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public TaskType getType() {
        return this.type;
    }

    public String getContent() {
        return this.content;
    }

    public List<VariableModel> getRequiredVariable() {
        return this.requiredVariable;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(TaskType type) {
        this.type = type;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setRequiredVariable(List<VariableModel> requiredVariable) {
        this.requiredVariable = requiredVariable;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ActionModel)) {
            return false;
        }
        ActionModel other = (ActionModel)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        TaskType this$type = this.getType();
        TaskType other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        String this$content = this.getContent();
        String other$content = other.getContent();
        if (this$content == null ? other$content != null : !this$content.equals(other$content)) {
            return false;
        }
        List<VariableModel> this$requiredVariable = this.getRequiredVariable();
        List<VariableModel> other$requiredVariable = other.getRequiredVariable();
        return !(this$requiredVariable == null ? other$requiredVariable != null : !(this$requiredVariable).equals(other$requiredVariable));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ActionModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        TaskType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        String $content = this.getContent();
        result = result * 59 + ($content == null ? 43 : $content.hashCode());
        List<VariableModel> $requiredVariable = this.getRequiredVariable();
        result = result * 59 + ($requiredVariable == null ? 43 : ($requiredVariable).hashCode());
        return result;
    }

    public String toString() {
        return "ActionModel(super=" + super.toString() + ", id=" + this.getId() + ", appId=" + this.getAppId() + ", flowId=" + this.getFlowId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", type=" + (this.getType()) + ", content=" + this.getContent() + ", requiredVariable=" + this.getRequiredVariable() + ")";
    }
}

