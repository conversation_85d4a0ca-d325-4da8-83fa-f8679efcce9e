/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.event.CancelInstanceEvent;
import com.wacai.trike.mirana.event.ESProcessingFactory;
import com.wacai.trike.mirana.event.EventAgency;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.EventListener;
import com.wacai.trike.mirana.event.InstanceInitEvent;
import com.wacai.trike.mirana.event.StartingFlowEvent;
import com.wacai.trike.mirana.graph.FlowGraph;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.lifecycle.SubjectProvider;
import com.wacai.trike.mirana.metrics.FlowMetrics;
import com.wacai.trike.mirana.metrics.MetricsProvider;
import com.wacai.trike.mirana.util.DistributeSequenceIdGenerator;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class StartingFlowEventListener implements EventListener<StartingFlowEvent, InstanceInitEvent> {

	private static final Logger log = LogManager.getLogger(StartingFlowEventListener.class);
	
	@Autowired
	private FlowGraphService graphService;
	
	@Autowired
	private EventAgency eventAgency;
	
	@Autowired
	private DistributeSequenceIdGenerator idGenerator;
	
	@Autowired
	private MetricsProvider metricsProvider;
	
	@Autowired
	private ESProcessingFactory esProcessingFactory;
	
	@Autowired
	private SubjectProvider subjectProvider;

	public int getOrder() {
		return 1;
	}

	@Override
	public Class<StartingFlowEvent> accept() {
		return StartingFlowEvent.class;
	}

	@Override
	@Barrier(handlers = { InstanceContextSyncHandler.class })
	public void doProcess(EventExchange<StartingFlowEvent, InstanceInitEvent> exchange) {
		
		StartingFlowEvent event = exchange.getCurrent();
		//得到流程图
		FlowGraph graph = this.graphService.getFlowGraph(event.getBu(), event.getApp(), event.getFlow(),
				event.getVersion());
		
		InstanceContext context = exchange.getContext();
		//组装数据，设置超时时间
		this.blend(context, graph, event);
		this.submitCancelTask(context);
		
		this.esProcessingFactory.process(new ESProcessingFactory.Salad(ESProcessingFactory.Type.INIT, context));
		this.subjectProvider.subscribe(event.getFlow(), event.getInstanceUuid(), event.getBzKey());
		this.metricsProvider.metricsFlow(FlowMetrics.build(graph, context, "start"));
		
		//设置下一个步
		exchange.setNext(new InstanceInitEvent()
				.setInstanceUuid(context.getUuid())
				.setTaskId(context.getCurrentNodeId().toString())
				.setOperator(event.getOperator())
				.setParam(context.getEffectiveVariables()));
	}

	private void blend(InstanceContext context, FlowGraph graph, StartingFlowEvent event) {
		context.setFlowId(graph.getId()).setFlowCode(graph.getCode()).setVersion(graph.getVersion())
				.setStatus(InstanceStatus.RUNNING).setParentUuid(event.getParentUuid())
				.setCurrentNodeId(graph.getStartNode().getId()).setBzKey(event.getBzKey()).setAsync(event.isAsync())
				.setTimeout(Optional.ofNullable(event.getTimeout()).orElse(graph.getTimeout()));
		context.addVariable(event.getParam(), VariableType.FLOW_ATTACHED);
	}

	private void submitCancelTask(InstanceContext context) {
		if (Objects.isNull(context.getTimeout())) {
			return;
		}
		this.eventAgency.agent(new CancelInstanceEvent().setUuid(Long.toString(this.idGenerator.nextId()))
				.setInstanceUuid(context.getUuid()).setOperator("system").setTaskId(context.getUuid())
				.setExpectedTime(context.getTimeout()), true);
		log.info("instance {} will timeout at point {}",  context.getUuid(),  context.getTimeout());
	}
}
