/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.action.dao.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericActionVariablePO;
import java.time.LocalDateTime;

public class QGenericActionVariablePO
extends EntityPathBase<GenericActionVariablePO> {
    private static final long serialVersionUID = 164507600L;
    public static final QGenericActionVariablePO genericActionVariablePO = new QGenericActionVariablePO("genericActionVariablePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final NumberPath<Long> actionId = this.createNumber("actionId", Long.class);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> id;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final NumberPath<Long> variableId;

    public QGenericActionVariablePO(String variable) {
        super(GenericActionVariablePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.variableId = this.createNumber("variableId", Long.class);
    }

    public QGenericActionVariablePO(Path<? extends GenericActionVariablePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.variableId = this.createNumber("variableId", Long.class);
    }

    public QGenericActionVariablePO(PathMetadata metadata) {
        super(GenericActionVariablePO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.variableId = this.createNumber("variableId", Long.class);
    }
}

