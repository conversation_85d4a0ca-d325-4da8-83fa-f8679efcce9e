/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.task;

import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.task.TaskProcessor;
import java.util.Collections;
import java.util.Map;
import org.springframework.stereotype.Component;

@Component
class UserTaskProcessor
implements TaskProcessor {
    UserTaskProcessor() {
    }

    @Override
    public boolean accept(TaskType taskType) {
        return TaskType.USER == taskType;
    }

    @Override
    public Map<String, String> execute(Node node, InstanceContext context) {
        return Collections.emptyMap();
    }
}

