/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.TaskExecuteType
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.api.constant.TaskExecuteType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;

@Component
public class NodeInitEventListener implements EventListener<NodeInitEvent, NodeProcessedEvent> {
	
    private static final Logger log = LogManager.getLogger(NodeInitEventListener.class);
    @Autowired
    private FlowGraphService graphService;
    @Autowired
    private NodeExecutor nodeExecutor;

    @Override
    public Class<NodeInitEvent> accept() {
        return NodeInitEvent.class;
    }

    @Override
    @Barrier(handlers={InstanceContextSyncHandler.class})
    public void doProcess(EventExchange<NodeInitEvent, NodeProcessedEvent> exchange) {
        
    	InstanceContext context = exchange.getContext();
        Node node = this.graphService.getFlowGraph(context.getFlowId()).getNode(context.getCurrentNodeId());
        
        if (TaskExecuteType.AUTO != node.getTaskExecuteType()) {
            log.info("manual node waiting process, node {}-{} of instance {}", node.getName(), node.getId(), context.getUuid());
            return;
        }
        this.nodeExecutor.execute(exchange);
    }

    public int getOrder() {
        return 3;
    }
}

