/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.manage.model;

import com.wacai.trike.mirana.api.manage.model.CandidateModel;
import com.wacai.trike.mirana.api.manage.model.OperatorModel;
import java.io.Serializable;
import java.util.List;

public class FlowVariableModel
implements Serializable {
    private Long id;
    private String code;
    private String name;
    private String type;
    private List<CandidateModel> candidates;
    private List<OperatorModel> operators;

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getType() {
        return this.type;
    }

    public List<CandidateModel> getCandidates() {
        return this.candidates;
    }

    public List<OperatorModel> getOperators() {
        return this.operators;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setCandidates(List<CandidateModel> candidates) {
        this.candidates = candidates;
    }

    public void setOperators(List<OperatorModel> operators) {
        this.operators = operators;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowVariableModel)) {
            return false;
        }
        FlowVariableModel other = (FlowVariableModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$type = this.getType();
        String other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        List<CandidateModel> this$candidates = this.getCandidates();
        List<CandidateModel> other$candidates = other.getCandidates();
        if (this$candidates == null ? other$candidates != null : !(this$candidates).equals(other$candidates)) {
            return false;
        }
        List<OperatorModel> this$operators = this.getOperators();
        List<OperatorModel> other$operators = other.getOperators();
        return !(this$operators == null ? other$operators != null : !(this$operators).equals(other$operators));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowVariableModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        List<CandidateModel> $candidates = this.getCandidates();
        result = result * 59 + ($candidates == null ? 43 : ($candidates).hashCode());
        List<OperatorModel> $operators = this.getOperators();
        result = result * 59 + ($operators == null ? 43 : ($operators).hashCode());
        return result;
    }

    public String toString() {
        return "FlowVariableModel(id=" + this.getId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", type=" + this.getType() + ", candidates=" + this.getCandidates() + ", operators=" + this.getOperators() + ")";
    }
}

