/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.FormType
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.form.repository;

import com.wacai.trike.mirana.api.constant.FormType;
import com.wacai.trike.mirana.domain.form.po.FormPO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface FormRepository
extends JpaRepository<FormPO, Long> {
    public List<FormPO> findByNodeIdAndType(Long var1, FormType var2);

    public List<FormPO> findByNodeId(Long var1);
}

