/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 *  com.wacai.trike.mirana.api.constant.FlowStatus
 *  com.wacai.trike.mirana.api.constant.FlowType
 */
package com.wacai.trike.mirana.domain.flow.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.FlowType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.flow.po.FlowPO;
import java.time.LocalDateTime;

public class QFlowPO
extends EntityPathBase<FlowPO> {
    private static final long serialVersionUID = 745825593L;
    public static final QFlowPO flowPO = new QFlowPO("flowPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final NumberPath<Long> appId;
    public final StringPath code;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final EnumPath<FlowStatus> status;
    public final StringPath timeout;
    public final EnumPath<FlowType> type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final StringPath version;

    public QFlowPO(String variable) {
        super(FlowPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.status = this.createEnum("status", FlowStatus.class);
        this.timeout = this.createString("timeout");
        this.type = this.createEnum("type", FlowType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.version = this.createString("version");
    }

    public QFlowPO(Path<? extends FlowPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.status = this.createEnum("status", FlowStatus.class);
        this.timeout = this.createString("timeout");
        this.type = this.createEnum("type", FlowType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.version = this.createString("version");
    }

    public QFlowPO(PathMetadata metadata) {
        super(FlowPO.class, metadata);
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.name = this.createString("name");
        this.status = this.createEnum("status", FlowStatus.class);
        this.timeout = this.createString("timeout");
        this.type = this.createEnum("type", FlowType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.version = this.createString("version");
    }
}

