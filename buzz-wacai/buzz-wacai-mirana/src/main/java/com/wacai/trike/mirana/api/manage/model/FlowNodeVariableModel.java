/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class FlowNodeVariableModel
implements Serializable {
    private Long id;
    private Long parentFlowVariableId;
    private Long subFlowVariableId;

    public Long getId() {
        return this.id;
    }

    public Long getParentFlowVariableId() {
        return this.parentFlowVariableId;
    }

    public Long getSubFlowVariableId() {
        return this.subFlowVariableId;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setParentFlowVariableId(Long parentFlowVariableId) {
        this.parentFlowVariableId = parentFlowVariableId;
    }

    public void setSubFlowVariableId(Long subFlowVariableId) {
        this.subFlowVariableId = subFlowVariableId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowNodeVariableModel)) {
            return false;
        }
        FlowNodeVariableModel other = (FlowNodeVariableModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$parentFlowVariableId = this.getParentFlowVariableId();
        Long other$parentFlowVariableId = other.getParentFlowVariableId();
        if (this$parentFlowVariableId == null ? other$parentFlowVariableId != null : !(this$parentFlowVariableId).equals(other$parentFlowVariableId)) {
            return false;
        }
        Long this$subFlowVariableId = this.getSubFlowVariableId();
        Long other$subFlowVariableId = other.getSubFlowVariableId();
        return !(this$subFlowVariableId == null ? other$subFlowVariableId != null : !(this$subFlowVariableId).equals(other$subFlowVariableId));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowNodeVariableModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $parentFlowVariableId = this.getParentFlowVariableId();
        result = result * 59 + ($parentFlowVariableId == null ? 43 : ($parentFlowVariableId).hashCode());
        Long $subFlowVariableId = this.getSubFlowVariableId();
        result = result * 59 + ($subFlowVariableId == null ? 43 : ($subFlowVariableId).hashCode());
        return result;
    }

    public String toString() {
        return "FlowNodeVariableModel(id=" + this.getId() + ", parentFlowVariableId=" + this.getParentFlowVariableId() + ", subFlowVariableId=" + this.getSubFlowVariableId() + ")";
    }
}

