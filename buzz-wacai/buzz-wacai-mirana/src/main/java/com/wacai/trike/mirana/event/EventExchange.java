/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.event.AbstractFlowEvent;

public class EventExchange<E extends AbstractFlowEvent, R extends AbstractFlowEvent> {
    private final InstanceContext context;
    private final E current;
    private R next;

    public EventExchange(InstanceContext context, E event) {
        this.context = context;
        this.current = event;
    }

    public EventExchange<R, ? extends AbstractFlowEvent> next() {
        if (this.next == null) {
            return null;
        }
        return new EventExchange<R, R>(this.context, this.next);
    }

    public E getCurrent() {
        return this.current;
    }

    public InstanceContext getContext() {
        return this.context;
    }

    public R getNext() {
        return this.next;
    }

    public void setNext(R next) {
        this.next = next;
    }
}

