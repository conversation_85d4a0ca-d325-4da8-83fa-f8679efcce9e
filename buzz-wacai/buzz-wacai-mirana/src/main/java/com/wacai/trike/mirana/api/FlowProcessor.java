/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 */
package com.wacai.trike.mirana.api;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.model.FlowCancelRequest;
import com.wacai.trike.mirana.api.model.FlowProcessRequest;
import com.wacai.trike.mirana.api.model.FlowResponse;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface FlowProcessor {
    @PostMapping(value={"/flow/start"})
    public ApiResponse<FlowResponse> start(@RequestBody FlowStartRequest var1);

    @PostMapping(value={"/flow/process"})
    public ApiResponse<FlowResponse> process(@RequestBody FlowProcessRequest var1);

    @PostMapping(value={"/flow/cancel"})
    public ApiResponse<FlowResponse> cancel(@RequestBody FlowCancelRequest var1);
}

