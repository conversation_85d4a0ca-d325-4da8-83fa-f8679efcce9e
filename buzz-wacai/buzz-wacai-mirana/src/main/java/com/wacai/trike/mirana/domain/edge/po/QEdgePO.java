/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.edge.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.enums.EdgeType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.edge.po.EdgePO;
import java.time.LocalDateTime;

public class QEdgePO
extends EntityPathBase<EdgePO> {
    private static final long serialVersionUID = 515059671L;
    public static final QEdgePO edgePO = new QEdgePO("edgePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> flowId;
    public final NumberPath<Long> fromId;
    public final EnumPath<EdgeType> fromType;
    public final NumberPath<Long> id;
    public final StringPath location;
    public final NumberPath<Long> toId;
    public final EnumPath<EdgeType> toType;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QEdgePO(String variable) {
        super(EdgePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.fromId = this.createNumber("fromId", Long.class);
        this.fromType = this.createEnum("fromType", EdgeType.class);
        this.id = this._super.id;
        this.location = this.createString("location");
        this.toId = this.createNumber("toId", Long.class);
        this.toType = this.createEnum("toType", EdgeType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QEdgePO(Path<? extends EdgePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.fromId = this.createNumber("fromId", Long.class);
        this.fromType = this.createEnum("fromType", EdgeType.class);
        this.id = this._super.id;
        this.location = this.createString("location");
        this.toId = this.createNumber("toId", Long.class);
        this.toType = this.createEnum("toType", EdgeType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QEdgePO(PathMetadata metadata) {
        super(EdgePO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.fromId = this.createNumber("fromId", Long.class);
        this.fromType = this.createEnum("fromType", EdgeType.class);
        this.id = this._super.id;
        this.location = this.createString("location");
        this.toId = this.createNumber("toId", Long.class);
        this.toType = this.createEnum("toType", EdgeType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

