/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.graph;

import com.wacai.trike.mirana.common.enums.EdgeType;
import com.wacai.trike.mirana.graph.Expression;
import com.wacai.trike.mirana.graph.Node;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

public class Edge implements Serializable {
	
    private Long id;
    private EdgeType fromType;
    private Node fromNode;
    private EdgeType toType;
    private Node toNode;
    private List<Expression> expressions = new ArrayList<Expression>();

    public Long getId() {
        return this.id;
    }

    public EdgeType getFromType() {
        return this.fromType;
    }

    public Node getFromNode() {
        return this.fromNode;
    }

    public EdgeType getToType() {
        return this.toType;
    }

    public Node getToNode() {
        return this.toNode;
    }

    public List<Expression> getExpressions() {
        return this.expressions;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setFromType(EdgeType fromType) {
        this.fromType = fromType;
    }

    public void setFromNode(Node fromNode) {
        this.fromNode = fromNode;
    }

    public void setToType(EdgeType toType) {
        this.toType = toType;
    }

    public void setToNode(Node toNode) {
        this.toNode = toNode;
    }

    public void setExpressions(List<Expression> expressions) {
        this.expressions = expressions;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Edge)) {
            return false;
        }
        Edge other = (Edge)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        EdgeType this$fromType = this.getFromType();
        EdgeType other$fromType = other.getFromType();
        if (this$fromType == null ? other$fromType != null : !((this$fromType)).equals(other$fromType)) {
            return false;
        }
        Node this$fromNode = this.getFromNode();
        Node other$fromNode = other.getFromNode();
        if (this$fromNode == null ? other$fromNode != null : !(this$fromNode).equals(other$fromNode)) {
            return false;
        }
        EdgeType this$toType = this.getToType();
        EdgeType other$toType = other.getToType();
        if (this$toType == null ? other$toType != null : !((this$toType)).equals(other$toType)) {
            return false;
        }
        Node this$toNode = this.getToNode();
        Node other$toNode = other.getToNode();
        if (this$toNode == null ? other$toNode != null : !(this$toNode).equals(other$toNode)) {
            return false;
        }
        List<Expression> this$expressions = this.getExpressions();
        List<Expression> other$expressions = other.getExpressions();
        return !(this$expressions == null ? other$expressions != null : !(this$expressions).equals(other$expressions));
    }

    protected boolean canEqual(Object other) {
        return other instanceof Edge;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        EdgeType $fromType = this.getFromType();
        result = result * 59 + ($fromType == null ? 43 : (($fromType)).hashCode());
        Node $fromNode = this.getFromNode();
        result = result * 59 + ($fromNode == null ? 43 : ($fromNode).hashCode());
        EdgeType $toType = this.getToType();
        result = result * 59 + ($toType == null ? 43 : (($toType)).hashCode());
        Node $toNode = this.getToNode();
        result = result * 59 + ($toNode == null ? 43 : ($toNode).hashCode());
        List<Expression> $expressions = this.getExpressions();
        result = result * 59 + ($expressions == null ? 43 : ($expressions).hashCode());
        return result;
    }

    public String toString() {
        return "Edge(id=" + this.getId() + ", fromType=" + (this.getFromType()) + ", fromNode=" + this.getFromNode() + ", toType=" + (this.getToType()) + ", toNode=" + this.getToNode() + ", expressions=" + this.getExpressions() + ")";
    }
}

