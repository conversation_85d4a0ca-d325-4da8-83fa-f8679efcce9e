/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.InstanceCancelHandler;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.EventListener;
import com.wacai.trike.mirana.event.ManualEndingEvent;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.stereotype.Component;

@Component
public class ManualEndingEventListener
implements EventListener<ManualEndingEvent, AbstractFlowEvent> {
    private static final Logger log = LogManager.getLogger(ManualEndingEventListener.class);

    @Override
    public Class<ManualEndingEvent> accept() {
        return ManualEndingEvent.class;
    }

    @Override
    @Barrier(handlers={InstanceCancelHandler.class, InstanceContextSyncHandler.class})
    public void doProcess(EventExchange<ManualEndingEvent, AbstractFlowEvent> exchange) {
        exchange.getContext().setStatus(InstanceStatus.ENDED);
    }

    public int getOrder() {
        return 10;
    }
}

