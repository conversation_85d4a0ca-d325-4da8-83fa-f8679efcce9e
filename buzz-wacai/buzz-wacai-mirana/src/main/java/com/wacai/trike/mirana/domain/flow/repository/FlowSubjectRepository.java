/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.flow.repository;

import com.wacai.trike.mirana.domain.flow.po.FlowSubjectPO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface FlowSubjectRepository
extends JpaRepository<FlowSubjectPO, Long> {
    public List<FlowSubjectPO> findByFlowCode(String var1);

    public FlowSubjectPO findByFlowCodeAndSubjectCode(String var1, String var2);
}

