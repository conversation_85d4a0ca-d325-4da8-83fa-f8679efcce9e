/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.step.po;

import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="step")
public class StepPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private String instanceUuid;
    private Long edgeId;

    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    public Long getEdgeId() {
        return this.edgeId;
    }

    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }

    public void setEdgeId(Long edgeId) {
        this.edgeId = edgeId;
    }

    @Override
    public String toString() {
        return "StepPO(instanceUuid=" + this.getInstanceUuid() + ", edgeId=" + this.getEdgeId() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StepPO)) {
            return false;
        }
        StepPO other = (StepPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$instanceUuid = this.getInstanceUuid();
        String other$instanceUuid = other.getInstanceUuid();
        if (this$instanceUuid == null ? other$instanceUuid != null : !this$instanceUuid.equals(other$instanceUuid)) {
            return false;
        }
        Long this$edgeId = this.getEdgeId();
        Long other$edgeId = other.getEdgeId();
        return !(this$edgeId == null ? other$edgeId != null : !(this$edgeId).equals(other$edgeId));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof StepPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $instanceUuid = this.getInstanceUuid();
        result = result * 59 + ($instanceUuid == null ? 43 : $instanceUuid.hashCode());
        Long $edgeId = this.getEdgeId();
        result = result * 59 + ($edgeId == null ? 43 : ($edgeId).hashCode());
        return result;
    }
}

