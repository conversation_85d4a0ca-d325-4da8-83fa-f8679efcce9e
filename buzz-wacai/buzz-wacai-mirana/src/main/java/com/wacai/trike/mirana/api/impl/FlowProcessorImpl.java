/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.hermes.agent.producer.HermesProducer
 *  com.wacai.hermes.common.message.HermesMessage
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.trike.mirana.api.FlowProcessor
 *  com.wacai.trike.mirana.api.constant.FlowStatus
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  com.wacai.trike.mirana.api.model.FlowBaseRequest
 *  com.wacai.trike.mirana.api.model.FlowCancelRequest
 *  com.wacai.trike.mirana.api.model.FlowProcessRequest
 *  com.wacai.trike.mirana.api.model.FlowResponse
 *  com.wacai.trike.mirana.api.model.FlowStartRequest
 *  javax.validation.constraints.NotNull
 *  org.apache.logging.log4j.util.Strings
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.validation.annotation.Validated
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl;

import com.alibaba.dubbo.config.annotation.Service;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.hermes.agent.producer.HermesProducer;
import com.wacai.hermes.common.message.HermesMessage;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.Seraph.SeraphSender;
import com.wacai.trike.mirana.api.FlowProcessor;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.api.model.FlowBaseRequest;
import com.wacai.trike.mirana.api.model.FlowCancelRequest;
import com.wacai.trike.mirana.api.model.FlowProcessRequest;
import com.wacai.trike.mirana.api.model.FlowResponse;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import com.wacai.trike.mirana.common.enums.ErrorCode;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.context.po.QTaskInstancePO;
import com.wacai.trike.mirana.domain.context.po.TaskInstancePO;
import com.wacai.trike.mirana.domain.flow.po.FlowPO;
import com.wacai.trike.mirana.domain.flow.po.QFlowPO;
import com.wacai.trike.mirana.domain.instance.po.InstancePO;
import com.wacai.trike.mirana.domain.instance.po.QInstancePO;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.CancelInstanceEvent;
import com.wacai.trike.mirana.event.EventAgency;
import com.wacai.trike.mirana.event.ManualProcessingEvent;
import com.wacai.trike.mirana.event.StartingFlowEvent;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.util.DistributeRedisLock;
import com.wacai.trike.mirana.util.DistributeSequenceIdGenerator;
import com.wacai.trike.mirana.util.ObjectMappers;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collection;
import java.util.Objects;
import java.util.Random;
import javax.validation.constraints.NotNull;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
//dubbo注解
@Service(interfaceClass = FlowProcessor.class)
public class FlowProcessorImpl implements FlowProcessor {
	
	private static final Logger log = LoggerFactory.getLogger(FlowProcessorImpl.class);
	public static final String BZ_KEY = "bzKey";
	@Autowired
	private JPAQueryFactory queryFactory;
	@Autowired
	private DistributeRedisLock lock;
	@Autowired
	private DistributeSequenceIdGenerator idGenerator;
	@Autowired
	private SeraphSender seraphSender;
	@Autowired
	private FlowGraphService graphService;
	@Autowired
	private HermesProducer producer;
	@Autowired
	private EventAgency eventAgency;
	public static final String StartDelayTimer = "startDelayTimer";
	public static final String BirthMarks = "birthmarks";
	public static final String IntervalVal = "intervalVal";
	public static final String MANUAL_CODE = "code";
	public static final String MIRANA_FLOW_ASYNC_START_TOPIC = "mirana.flow.async.start";
	public static final String MIRANA_FLOW_PROCESS_TOPIC = "mirana.flow.process.async";
	public static final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
	private final Random random = new Random();

	public ApiResponse<FlowResponse> start(@RequestBody @NotNull @Validated FlowStartRequest request) {
		String uuid;
		FlowPO flow = this.find(request);
		if (Objects.isNull(flow)) {
			return ApiResponse.error((int) ErrorCode.FLOW_NOT_ENABLED.getCode(),
					 ErrorCode.FLOW_NOT_ENABLED.getMessage());
		}
		String string = uuid = request.getUuid() == null ? Long.toString(this.idGenerator.nextId()) : request.getUuid();
		if (request.isAsync()) {
			request.setUuid(uuid);
			try {
				HermesMessage message = HermesMessage.builder().setTopic(MIRANA_FLOW_ASYNC_START_TOPIC)
						.setData(ObjectMappers.mustWriteValue(request).getBytes()).build();
				this.producer.produce(message);
				log.info("flow-async-start: topic={},message={}", MIRANA_FLOW_ASYNC_START_TOPIC, request);
			} catch (Exception e) {
				log.error("flow-async-start异常: topic=mirana.flow.async.start,message=" + request, (Throwable) e);
			}
			return ApiResponse.success(this.genericResponse(uuid, InstanceStatus.ASYNC_START));
		}
		boolean isDelayStart = this.delay(request, uuid);
		if (isDelayStart) {
			return ApiResponse.success(this.genericResponse(uuid, InstanceStatus.DELAY_START));
		}
		ErrorCode code = this.sanityChecks(request);
		if (code != null) {
			return ApiResponse.error((int) code.getCode(),  code.getMessage());
		}
		StartingFlowEvent event = this.build(request, uuid, flow);
		log.info("FlowAsyncConsumer-event,flow is {},uuid is {},event is {}",
				new Object[] { request.getFlow(), request.getUuid(), event });
		InstanceContext agent = this.eventAgency.agent(event, request.isAsync());
		if (agent == null) {
			log.info("not init flow {} instance for {}", request.getFlow(), request.getBzKey());
			return ApiResponse.error(
					 String.format("not init flow %s instance for %s", request.getFlow(), request.getBzKey()));
		}
		log.info("flow {} - {} - {} start success", new Object[] { flow.getName(), flow.getCode(), flow.getVersion() });
		return ApiResponse.success(this.genericResponse(uuid, agent.getStatus()));
	}

	/*
	 * WARNING - Removed try catching itself - possible behaviour change.
	 */
	public ApiResponse<FlowResponse> process(@RequestBody @NotNull @Validated FlowProcessRequest request) {
		ApiResponse response;
		Discussion discussion = this.determineInstance(request);
		if (discussion.priority < 0) {
			return ApiResponse.error( discussion.message);
		}
		String instanceUUID = discussion.taskUUID;
		//如果是异步
		if (request.isAsyncStart()) {
			request.setInstanceUUID(instanceUUID);
			try {
				HermesMessage message = HermesMessage.builder().setTopic(MIRANA_FLOW_PROCESS_TOPIC)
						.setData(ObjectMappers.mustWriteValue( request).getBytes()).build();
				this.producer.produce(message);
				log.info("flow-async-process：topic={},message={}",  MIRANA_FLOW_PROCESS_TOPIC,
						 request);
			} catch (Exception e) {
				log.error("flow-async-process异常：topic=mirana.flow.process.async,message=" +  request,
						(Throwable) e);
			}
			return ApiResponse.success(this.genericResponse(instanceUUID, InstanceStatus.ASYNC_START));
		}
		//如果是同步
		try {
			InstanceContext agent;
			if (!this.lock.tryLock(instanceUUID)) {
				ApiResponse e = ApiResponse.error( "concurrency process instance error");
				return e;
			}
			//构造手动事件
			ManualProcessingEvent event = new ManualProcessingEvent()
					.setBu(request.getBu())
					.setApp(request.getApp())
					.setFlow(request.getFlow())
					.setInstanceUuid(instanceUUID)
					.setTaskId(request.getTaskInstanceId())
					.setOperator(request.getOperator()).setParam(request.getParam());
			if (request.getCode() != null) {
				event.getParam().put(MANUAL_CODE, String.valueOf(request.getCode()));
			}
			response = (agent = this.eventAgency.agent((AbstractFlowEvent) event, request.isAsync())) == null
					? ApiResponse.error( String.format("not found context of instance %s", instanceUUID))
					: ApiResponse.success( this.genericResponse(instanceUUID, agent.getStatus()));
		} catch (Exception e) {
			log.error("process instance failed, request {}",  request,  e);
			response = ApiResponse.error( e.getMessage());
		} finally {
			this.lock.tryUnLock(request.getInstanceId());
		}
		return response;
	}

	public ApiResponse<FlowResponse> cancel(@RequestBody @NotNull @Validated FlowCancelRequest request) {
		AbstractFlowEvent cancelEvent = new CancelInstanceEvent().setInstanceUuid(request.getInstanceId())
				.setOperator(request.getOperator()).setParam(request.getParam());
		InstanceContext agent = this.eventAgency.agent(cancelEvent, request.isAsync());
		if (agent == null) {
			return ApiResponse
					.error( String.format("not found context of instance %s", request.getInstanceId()));
		}
		log.info("instance {} cancel success", request.getInstanceId());
		return ApiResponse.success(this.genericResponse(request.getInstanceId(), InstanceStatus.CANCELED));
	}

	private Discussion determineInstance(FlowProcessRequest request) {
		if (request != null && Strings.isBlank(request.getTaskInstanceId())
				&& Strings.isNotBlank(request.getInstanceId())) {
			request.setTaskInstanceId(request.getInstanceId());
		}
		if (request == null || Strings.isBlank( request.getTaskInstanceId())) {
			return new Discussion("taskUUID must be not null");
		}
		TaskInstancePO taskInstancePO = (TaskInstancePO) ((JPAQuery) this.queryFactory
				.selectFrom((EntityPath) QTaskInstancePO.taskInstancePO)
				.where((Predicate) QTaskInstancePO.taskInstancePO.uuid.eq(request.getTaskInstanceId()))).fetchFirst();
		if (taskInstancePO == null) {
			log.error("not found taskUUID {}", request.getTaskInstanceId());
			return new Discussion("not found taskUUID " + request.getTaskInstanceId());
		}
		InstancePO instancePO = (InstancePO) ((JPAQuery) this.queryFactory
				.selectFrom((EntityPath) QInstancePO.instancePO)
				.where((Predicate) QInstancePO.instancePO.uuid.eq(taskInstancePO.getInstanceUuid()))).fetchFirst();
		if (instancePO == null) {
			log.error("not found instance of taskUUID {}", request.getTaskInstanceId());
			return new Discussion("not found instance of taskUUID " + request.getTaskInstanceId());
		}
		request.setInstanceId(instancePO.getUuid());
		Node node = this.graphService.next(instancePO.getFlowId(), taskInstancePO.getNodeId());
		if (node == null) {
			log.error("not found the correct manual node of taskUUID {}", request.getTaskInstanceId());
			return new Discussion("not found the correct manual node of taskUUID " + request.getTaskInstanceId());
		}
		TaskInstancePO manualIns = (TaskInstancePO) ((JPAQuery) this.queryFactory
				.selectFrom((EntityPath) QTaskInstancePO.taskInstancePO)
				.where((Predicate) QTaskInstancePO.taskInstancePO.instanceUuid.eq(instancePO.getUuid())
						.and((Predicate) QTaskInstancePO.taskInstancePO.nodeId.eq(node.getId())))).fetchFirst();
		if (manualIns == null) {
			log.info("Data-first situation: instance of taskUUID {}", request.getTaskInstanceId());
			this.seraphSender.publishDelayEvent(new SeraphSender.Metadata(
					LocalDateTime.now().plusMinutes(this.random.nextInt(10) + 3), (FlowBaseRequest) request));
			return new Discussion(1, "data-first situation", request.getInstanceId());
		}
		if (manualIns.getEndTime() != null) {
			log.error("current task {} instance had executed", request.getTaskInstanceId());
			return new Discussion("current task instance had executed");
		}
		return new Discussion("ok", taskInstancePO.getInstanceUuid());
	}

	private ErrorCode sanityChecks(FlowStartRequest request) {
		if (Strings.isBlank( request.getBzKey())) {
			return null;
		}
		try {
			int bzKey = Integer.parseInt(request.getBzKey());
			request.getParam().put(IntervalVal, String.valueOf(bzKey % 100));
			request.getParam().put(BZ_KEY, request.getBzKey());
			return null;
		} catch (Exception e) {
			log.error("failed to set interval val for instance {}", request.getUuid());
			return ErrorCode.FLOW_START_ERROR;
		}
	}

	private StartingFlowEvent build(FlowStartRequest request, String uuid, FlowPO flow) {
		if (!CollectionUtils.isEmpty((Collection) request.getBirthmarks())) {
			request.addParam(BirthMarks, ObjectMappers.mustWriteValue(request.getBirthmarks()));
		}
		return ((StartingFlowEvent) new StartingFlowEvent().setBu(request.getBu()).setApp(request.getApp())
				.setFlow(request.getFlow()).setBzKey(request.getBzKey()).setOperator(request.getOperator())
				.setParam(request.getParam())).setAsync(request.isAsync()).setVersion(flow.getVersion())
						.setInstanceUuid(uuid);
	}

	private boolean delay(FlowStartRequest request, String uuid) {
		String startDelayTimer = request.getParam().getOrDefault(StartDelayTimer, null);
		LocalDateTime localDateTime = null;
		if (startDelayTimer != null) {
			localDateTime = LocalDateTime.parse(startDelayTimer, dateTimeFormatter);
		}
		if (startDelayTimer == null || localDateTime.isBefore(LocalDateTime.now())) {
			if (request.isStory()) {
				request.getParam().put("dueDate", LocalDateTime.now().plusMinutes(3L).format(dateTimeFormatter));
			}
			return false;
		}
		if (request.isStory()) {
			request.getParam().put("dueDate", localDateTime.format(dateTimeFormatter));
		}
		request.setUuid(uuid);
		this.seraphSender.publishDelayEvent(new SeraphSender.Metadata(localDateTime, (FlowBaseRequest) request, true));
		return true;
	}

	private FlowPO find(FlowStartRequest request) {
		return (FlowPO) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) this.queryFactory
				.selectFrom((EntityPath) QFlowPO.flowPO).leftJoin((EntityPath) QApplicationPO.applicationPO))
						.on((Predicate) QApplicationPO.applicationPO.id.eq(QFlowPO.flowPO.appId)))
								.leftJoin((EntityPath) QBusinessPO.businessPO)).on(
										(Predicate) QApplicationPO.applicationPO.buId.eq(QBusinessPO.businessPO.id)))
												.where((Predicate) QBusinessPO.businessPO.code.eq(request.getBu())
														.and((Predicate) QApplicationPO.applicationPO.code
																.eq(request.getApp()))
														.and((Predicate) QFlowPO.flowPO.code.eq(request.getFlow()))
														.and((Predicate) QFlowPO.flowPO.status.eq(FlowStatus.ENABLED))))
																.orderBy(QFlowPO.flowPO.version.desc())).limit(1L))
																		.fetchFirst();
	}

	private FlowResponse genericResponse(String uuid, InstanceStatus status) {
		return new FlowResponse().setInstanceId(uuid).setStatus(status);
	}

	private static class Discussion {
		private final int priority;
		private final String message;
		private final String taskUUID;

		public Discussion(String message) {
			this(-1, message, null);
		}

		public Discussion(String message, String taskUUID) {
			this(0, message, taskUUID);
		}

		public Discussion(int priority, String message, String taskUUID) {
			this.priority = priority;
			this.message = message;
			this.taskUUID = taskUUID;
		}
	}
}
