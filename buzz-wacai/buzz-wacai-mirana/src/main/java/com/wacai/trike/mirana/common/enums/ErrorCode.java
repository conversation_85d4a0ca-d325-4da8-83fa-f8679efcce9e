/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.common.enums;

public enum ErrorCode {
    NOT_DELAY(-1, "\u4e0d\u9700\u8981\u5ef6\u65f6"),
    OK(0, "\u6b63\u5e38\u6267\u884c"),
    FLOW_NOT_ENABLED(1, "\u6d41\u7a0b\u672a\u542f\u7528"),
    FLOW_INSTANCE_ENDED(2, "\u6d41\u7a0b\u5b9e\u4f8b\u5df2\u7ed3\u675f"),
    FLOW_INSTANCE_NOT_FOUND(3, "\u6d41\u7a0b\u5b9e\u4f8b\u4e0d\u5b58\u5728"),
    FLOW_START_ERROR(4, "\u6d41\u7a0b\u542f\u52a8\u9519\u8bef"),
    FLOW_DELAY_ERROR(5, "\u6d41\u7a0b\u5ef6\u65f6\u542f\u52a8\u65f6\u95f4\u8fc7\u671f");

    private int code;
    private String message;

    private ErrorCode(int code, String message) {
        this.code = code;
        this.message = message;
    }

    public int getCode() {
        return this.code;
    }

    public String getMessage() {
        return this.message;
    }
}

