/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.google.common.base.Strings
 *  com.google.common.collect.Lists
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.loan.trike.common.model.Page
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.domain.action;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.ConvertUtils;
import com.wacai.trike.mirana.domain.action.GenericVariableService;
import com.wacai.trike.mirana.domain.action.dao.po.GenericVariablePO;
import com.wacai.trike.mirana.domain.action.dao.po.QGenericActionVariablePO;
import com.wacai.trike.mirana.domain.action.dao.po.QGenericVariablePO;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericVariableRepository;
import com.wacai.trike.mirana.domain.action.dto.GenericVariableDto;
import com.wacai.trike.mirana.domain.action.enums.VariableEnums;
import com.wacai.trike.mirana.domain.action.enums.VariableEnums.SourceType;
import com.wacai.trike.mirana.util.HttpUtil;
import com.wacai.trike.mirana.util.ObjectMappers;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.web.model.GenericVariableRequest;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
@SuppressWarnings({ "rawtypes", "unchecked" })
public class GenericVariableServiceImpl
implements GenericVariableService {
    private static final Logger log = LoggerFactory.getLogger(GenericVariableServiceImpl.class);
    @Autowired
    private JPAQueryFactory jpaQueryFactory;
    @Autowired
    private GenericVariableRepository variableRepository;
    @Autowired
    private ConvertUtils convertUtils;

    @Override
    public GenericVariableDto save(GenericVariableDto dto) {
        dto.setActive(Boolean.TRUE);
        //新增
        if (dto.getId() == null) {
            return this.convertUtils.toDto(this.variableRepository.save(this.convertUtils.toPO(dto)));
        }
        //更新
        GenericVariablePO variablePO = this.variableRepository.findById(dto.getId()).orElse(null);
        if (variablePO == null) {
            return this.convertUtils.toDto(this.variableRepository.save(this.convertUtils.toPO(dto)));
        }
        this.updating(dto, variablePO);
        return this.convertUtils.toDto(this.variableRepository.save(variablePO));
    }

    @Override
    public GenericVariableDto query(Long id) {
        GenericVariablePO variablePO = this.variableRepository.findById(id).orElse(null);
        if (variablePO == null) {
            return null;
        }
        return this.convertUtils.toDto(variablePO);
    }
	
	public Page<GenericVariableDto> page(GenericVariableRequest request) {
		Page<GenericVariableDto> page = new Page();
		BooleanExpression expression = this.bindCondition(request);
		page.setPage((long) request.getPageIndex());
		page.setTotal(
				((JPAQuery) ((JPAQuery) this.jpaQueryFactory.select(QGenericVariablePO.genericVariablePO.id.count())
						.from(QGenericVariablePO.genericVariablePO)).where(expression)).fetchCount());
		List<GenericVariableDto> result = (List) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) this.jpaQueryFactory
				.selectFrom(QGenericVariablePO.genericVariablePO).where(expression))
						.orderBy(QGenericVariablePO.genericVariablePO.createdTime.desc()))
								.offset((long) (request.getPageSize() * (request.getPageIndex() - 1))))
										.limit((long) request.getPageSize())).fetch().stream().filter(Objects::nonNull)
												.map((po) -> {
													//return this.convertUtils.toDto(po);
													return null;
												}).collect(Collectors.toList());
		page.setData(result);
		return page;
	}

	public List<GenericVariableDto> batch(GenericVariableRequest request) {
		BooleanExpression expression = this.bindCondition(request);
		return (List) ((JPAQuery) ((JPAQuery) this.jpaQueryFactory.selectFrom(QGenericVariablePO.genericVariablePO)
				.where(expression)).orderBy(QGenericVariablePO.genericVariablePO.createdTime.desc())).fetch().stream()
						.filter(Objects::nonNull).map((po) -> {
							//return this.convertUtils.toDto(po);
							return null;
						}).collect(Collectors.toList());
	}
	
    @Override
	public int delete(Long id) {
		GenericVariablePO variablePO = (GenericVariablePO) this.variableRepository.findById(id).orElse(null);
		if (variablePO == null) {
			return 0;
		} else if (!variablePO.getActive()) {
			return 0;
		} else if (((JPAQuery) this.jpaQueryFactory.selectFrom(QGenericActionVariablePO.genericActionVariablePO)
				.where(QGenericActionVariablePO.genericActionVariablePO.variableId.eq(id))).fetchCount() > 0L) {
			return 0;
		} else {
			variablePO.setActive(false);
			this.variableRepository.save(variablePO);
			return 1;
		}
	}

    @Override
    public List<GenericVariableDto> fetchCandidates(List<Long> ids) {
       
    	if (CollectionUtils.isEmpty(ids)) {
			return Lists.newArrayList();
		} else {
			List<GenericVariablePO> variablePOS = this.variableRepository.findAllById(ids);
			return (List) (CollectionUtils.isEmpty(variablePOS)
					? Lists.newArrayList()
					: (List) variablePOS.stream().map((po) -> {
						GenericVariableDto dto = (GenericVariableDto) ObjectUtils.convertNotNull(po,
								GenericVariableDto.class);
						dto.setCandidates(this.fetch(po.getSourceType(),
								po.getSourceType() == SourceType.REMOTE ? po.getCandidatesUrl() : po.getCandidates()));
						return dto;
					}).collect(Collectors.toList()));
		}
    	
    }

    private BooleanExpression bindCondition(GenericVariableRequest request) {
        BooleanExpression expression = QGenericVariablePO.genericVariablePO.active.eq(request.getActive());
        if (!Strings.isNullOrEmpty((String)request.getName())) {
            expression = expression.and((Predicate)QGenericVariablePO.genericVariablePO.name.like("%" + request.getName() + "%"));
        }
        if (!Strings.isNullOrEmpty((String)request.getCode())) {
            expression = expression.and((Predicate)QGenericVariablePO.genericVariablePO.code.like("%" + request.getCode() + "%"));
        }
        return expression;
    }

    private void updating(GenericVariableDto source, GenericVariablePO target) {
        target.setName(source.getName());
        target.setDataType(source.getDataType());
        target.setSourceType(source.getSourceType());
        target.setCandidates(ObjectMappers.mustWriteValue(source.getCandidates()));
        target.setCandidatesUrl(source.getCandidatesUrl());
    }

    private List<String> fetch(VariableEnums.SourceType type, String sugar) {
        switch (type) {
            case MANUAL_INPUT: {
                return Lists.newArrayList();
            }
            case DEFINITION: {
                return this.candidates(sugar);
            }
        }
        String candidates = HttpUtil.get(sugar);
        log.info("Got {} from {}", candidates, sugar);
        return this.candidates(candidates);
    }

    private List<String> candidates(String candidates) {
        if (Strings.isNullOrEmpty((String)candidates)) {
            return Lists.newArrayList();
        }
        try {
            return ObjectMappers.mustReadValue(candidates, new TypeReference<List<String>>(){});
        }
        catch (ObjectMappers.DataFormatException e) {
            return Lists.newArrayList();
        }
    }
}

