/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.resource;

import java.io.Serializable;

public class StoryQueryResponseModel
implements Serializable {
    private Long id;
    private String code;
    private String name;
    private String status;
    private Long updatedTime;

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getStatus() {
        return this.status;
    }

    public Long getUpdatedTime() {
        return this.updatedTime;
    }

    public StoryQueryResponseModel setId(Long id) {
        this.id = id;
        return this;
    }

    public StoryQueryResponseModel setCode(String code) {
        this.code = code;
        return this;
    }

    public StoryQueryResponseModel setName(String name) {
        this.name = name;
        return this;
    }

    public StoryQueryResponseModel setStatus(String status) {
        this.status = status;
        return this;
    }

    public StoryQueryResponseModel setUpdatedTime(Long updatedTime) {
        this.updatedTime = updatedTime;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryQueryResponseModel)) {
            return false;
        }
        StoryQueryResponseModel other = (StoryQueryResponseModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$status = this.getStatus();
        String other$status = other.getStatus();
        if (this$status == null ? other$status != null : !this$status.equals(other$status)) {
            return false;
        }
        Long this$updatedTime = this.getUpdatedTime();
        Long other$updatedTime = other.getUpdatedTime();
        return !(this$updatedTime == null ? other$updatedTime != null : !(this$updatedTime).equals(other$updatedTime));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryQueryResponseModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : $status.hashCode());
        Long $updatedTime = this.getUpdatedTime();
        result = result * 59 + ($updatedTime == null ? 43 : ($updatedTime).hashCode());
        return result;
    }

    public String toString() {
        return "StoryQueryResponseModel(id=" + this.getId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", status=" + this.getStatus() + ", updatedTime=" + this.getUpdatedTime() + ")";
    }
}

