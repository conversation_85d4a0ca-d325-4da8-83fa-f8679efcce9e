/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.service;

import com.wacai.trike.mirana.service.FlowNodeVariableDTO;
import com.wacai.trike.mirana.service.FlowNodeVariableRequest;
import java.util.List;

public interface FlowNodeVariableService {
    public FlowNodeVariableDTO save(FlowNodeVariableDTO var1);

    public int delete(Long var1);

    public int deleteByNodeId(Long var1);

    public FlowNodeVariableDTO query(Long var1);

    public FlowNodeVariableDTO query(Long var1, Long var2, Long var3, Long var4);

    public List<FlowNodeVariableDTO> query(FlowNodeVariableRequest var1);

    public List<FlowNodeVariableDTO> query(Long var1, Long var2);
}

