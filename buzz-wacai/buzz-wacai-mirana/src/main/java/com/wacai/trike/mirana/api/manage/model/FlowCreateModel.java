/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 *  javax.validation.constraints.NotNull
 */
package com.wacai.trike.mirana.api.manage.model;

import com.wacai.trike.mirana.api.constant.FlowType;
import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class FlowCreateModel
implements Serializable {
    private Long id;
    @NotBlank
    private String name;
    @NotBlank
    private String code;
    @NotNull
    private Long appId;
    @NotNull
    private FlowType type;
    private String timeout;

    public Long getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public String getCode() {
        return this.code;
    }

    public Long getAppId() {
        return this.appId;
    }

    public FlowType getType() {
        return this.type;
    }

    public String getTimeout() {
        return this.timeout;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setType(FlowType type) {
        this.type = type;
    }

    public void setTimeout(String timeout) {
        this.timeout = timeout;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowCreateModel)) {
            return false;
        }
        FlowCreateModel other = (FlowCreateModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        FlowType this$type = this.getType();
        FlowType other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        String this$timeout = this.getTimeout();
        String other$timeout = other.getTimeout();
        return !(this$timeout == null ? other$timeout != null : !this$timeout.equals(other$timeout));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowCreateModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        FlowType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        String $timeout = this.getTimeout();
        result = result * 59 + ($timeout == null ? 43 : $timeout.hashCode());
        return result;
    }

    public String toString() {
        return "FlowCreateModel(id=" + this.getId() + ", name=" + this.getName() + ", code=" + this.getCode() + ", appId=" + this.getAppId() + ", type=" + (this.getType()) + ", timeout=" + this.getTimeout() + ")";
    }
}

