/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel
 */
package com.wacai.trike.mirana.elasticsearch.dao.api;

import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel;
import com.wacai.trike.mirana.elasticsearch.dao.po.EsFlow;
import com.wacai.trike.mirana.elasticsearch.dao.po.EsFlowTaskInstance;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceQuery;
import java.util.List;

public interface EsSqlDao {
    public boolean insert(EsFlow var1);

    public EsFlow findOne(Long var1);

    public boolean insert(EsFlowTaskInstance var1);

    public boolean update(EsFlowTaskInstance var1);

    public List<EsFlowTaskInstance> list(Long var1);

    public EsFlowTaskInstance findEsFlowInstance(String var1);

    public EsFlowTaskInstanceModel sum(EsFlowTaskInstanceQuery var1);

    public List<EsFlowTaskInstance> list(EsFlowTaskInstanceQuery var1);
}

