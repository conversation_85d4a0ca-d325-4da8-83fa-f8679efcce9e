/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.manage.repository;

import com.wacai.trike.mirana.domain.manage.po.FlowVariablePO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface FlowVariableRepository
extends JpaRepository<FlowVariablePO, Long> {
    public List<FlowVariablePO> findAllByAppId(Long var1);
}

