/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.fasterxml.jackson.core.JsonProcessingException
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.fasterxml.jackson.databind.ObjectMapper
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.jpa.JPQLQuery
 *  com.querydsl.jpa.JPQLQueryFactory
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.Page
 *  com.wacai.loan.trike.common.model.Query
 *  com.wacai.trike.mirana.api.manage.ActionService
 *  com.wacai.trike.mirana.api.manage.VariableService
 *  com.wacai.trike.mirana.api.manage.model.ActionComboModel
 *  com.wacai.trike.mirana.api.manage.model.ActionModel
 *  com.wacai.trike.mirana.api.manage.model.ActionQueryModel
 *  com.wacai.trike.mirana.api.manage.model.VariableComboModel
 *  com.wacai.trike.mirana.api.manage.model.VariableModel
 *  com.wacai.trike.mirana.api.manage.model.VariableQueryModel
 *  javax.validation.Valid
 *  javax.validation.constraints.NotNull
 *  org.apache.commons.lang3.StringUtils
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.data.domain.Page
 *  org.springframework.data.domain.PageRequest
 *  org.springframework.data.domain.Pageable
 *  org.springframework.util.CollectionUtils
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl.manage;

import com.alibaba.dubbo.config.annotation.Service;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.JPQLQueryFactory;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.Query;
import com.wacai.trike.mirana.api.manage.ActionService;
import com.wacai.trike.mirana.api.manage.VariableService;
import com.wacai.trike.mirana.api.manage.model.ActionComboModel;
import com.wacai.trike.mirana.api.manage.model.ActionModel;
import com.wacai.trike.mirana.api.manage.model.ActionQueryModel;
import com.wacai.trike.mirana.api.manage.model.VariableComboModel;
import com.wacai.trike.mirana.api.manage.model.VariableModel;
import com.wacai.trike.mirana.api.manage.model.VariableQueryModel;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.manage.po.ActionConfigPO;
import com.wacai.trike.mirana.domain.manage.repository.ActionConfigRepository;
import java.util.Collection;
import java.util.List;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Service(interfaceClass = ActionService.class)
public class ActionServiceImpl implements ActionService {
	private static final Logger log = LoggerFactory.getLogger(ActionServiceImpl.class);
	private static final TypeReference<List<Long>> LONG_ARRAY = new TypeReference<List<Long>>() {
	};
	@Autowired
	private ObjectMapper objectMapper;
	@Autowired
	private ActionConfigRepository actionRepository;
	@Autowired
	private JPQLQueryFactory queryFactory;
	@Autowired
	private VariableService variableService;

	public ApiResponse<com.wacai.loan.trike.common.model.Page<ActionModel>> queryActions(ActionQueryModel queryModel) {
		if (StringUtils.isNotBlank((CharSequence) queryModel.getBu())
				&& StringUtils.isNotBlank((CharSequence) queryModel.getApp())) {
			long appId = (Long) ((JPQLQuery) this.queryFactory.select(QApplicationPO.applicationPO.id)
					.from(new EntityPath[] { QApplicationPO.applicationPO }).join((EntityPath) QBusinessPO.businessPO)
					.on(new Predicate[] { QApplicationPO.applicationPO.buId.eq(QBusinessPO.businessPO.id) })
					.where(new Predicate[] { QApplicationPO.applicationPO.code.eq( queryModel.getApp())
							.and((Predicate) QBusinessPO.businessPO.code.eq( queryModel.getBu())) }))
									.fetchOne();
			return this.queryActions(appId, (Query) queryModel);
		}
		if (!CollectionUtils.isEmpty((Collection) queryModel.getIds())) {
			List vms = this.actionRepository.findAllById(queryModel.getIds()).stream().map(this::convert)
					.collect(Collectors.toList());
			return ApiResponse.success(new com.wacai.loan.trike.common.model.Page((long) vms.size(), 1L, vms));
		}
		return ApiResponse.error((String) "query condition not meet");
	}

	public ApiResponse<com.wacai.loan.trike.common.model.Page<ActionModel>> queryActions(
			@RequestParam(value = "appId") Long appId, Query query) {
		try {
			PageRequest pageable = PageRequest.of((int) ((int) query.getPageIndex() - 1),
					(int) ((int) query.getPageSize()));
			Page<ActionConfigPO> variables = this.actionRepository.findByAppId(appId, (Pageable) pageable);
			return ApiResponse.success(this.convert(variables));
		} catch (Exception e) {
			log.error("get variables for app {} failed",  appId,  e);
			return ApiResponse.error((String) e.getMessage());
		}
	}

	private com.wacai.loan.trike.common.model.Page<ActionModel> convert(Page<ActionConfigPO> page) {
		Page pageModel = page.map(this::convert);
		return new com.wacai.loan.trike.common.model.Page(pageModel.getTotalElements(),
				(long) pageModel.getTotalPages(), pageModel.getContent());
	}

	private ActionModel convert(ActionConfigPO var) {
		ActionModel model = new ActionModel();
		BeanUtils.copyProperties( var,  model);
		model.setLabel(var.getName());
		model.setValue(var.getId().toString());
		if (StringUtils.isNotBlank((CharSequence) var.getRequiredVariable())) {
			try {
				List ids = (List) this.objectMapper.readValue(var.getRequiredVariable(), LONG_ARRAY);
				VariableQueryModel vqm = new VariableQueryModel();
				vqm.setPageIndex(1L);
				vqm.setPageSize(Integer.MAX_VALUE);
				vqm.setIds(ids);
				List vars = ((com.wacai.loan.trike.common.model.Page) this.variableService.queryVariables(vqm)
						.getData()).getData();
				model.setRequiredVariable(vars);
			} catch (Exception e) {
				log.error("load required variables failed, {} ", var, e);
				throw new RuntimeException(e);
			}
		}
		return model;
	}

	public ApiResponse<Long> create(@RequestBody @NotNull @Valid ActionModel model) {
		ActionConfigPO actionPO = this.actionRepository.findById(model.getId()).orElse(new ActionConfigPO());
		BeanUtils.copyProperties(model, actionPO);
		if (!CollectionUtils.isEmpty((Collection) model.getRequiredVariable())) {
			try {
				List ids = model.getRequiredVariable().stream().map(VariableModel::getId).collect(Collectors.toList());
				actionPO.setRequiredVariable(this.objectMapper.writeValueAsString(ids));
			} catch (JsonProcessingException e) {
				log.error("convert required variables to string failed, {} ", model, e);
				throw new RuntimeException(e);
			}
		}
		return null;
	}

	public ApiResponse<Void> delete(@PathVariable(value = "id") Long id) {
		this.actionRepository.deleteById(id);
		return ApiResponse.success(null);
	}

	public ApiResponse<List<ActionComboModel>> combo(ActionQueryModel queryModel) {
		queryModel.setPageSize(Integer.MAX_VALUE);
		queryModel.setPageIndex(1L);
		return this.queryActions(queryModel).map(com.wacai.loan.trike.common.model.Page::getData).map(this::convert);
	}

	private List<ActionComboModel> convert(List<ActionModel> models) {
		return models.stream().map(this::convert).collect(Collectors.toList());
	}

	private ActionComboModel convert(ActionModel model) {
		ActionComboModel c = new ActionComboModel();
		BeanUtils.copyProperties(model, c);
		if (!CollectionUtils.isEmpty((Collection) model.getRequiredVariable())) {
			List vcs = model.getRequiredVariable().stream().map(VariableComboModel::of).collect(Collectors.toList());
			c.setRequiredVariable(vcs);
		}
		return c;
	}
}
