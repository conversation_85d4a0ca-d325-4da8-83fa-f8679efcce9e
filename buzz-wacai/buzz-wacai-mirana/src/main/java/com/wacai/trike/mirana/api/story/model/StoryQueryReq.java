/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.Query
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.story.model;

import com.wacai.loan.trike.common.model.Query;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.FlowType;
import java.util.List;
import javax.validation.constraints.NotBlank;

public class StoryQueryReq
extends Query {
    @NotBlank
    private String bu;
    @NotBlank
    private String app;
    private String code;
    private String name;
    private List<FlowStatus> statuses;
    private List<FlowType> types;

    public static StoryQueryReqBuilder builder() {
        return new StoryQueryReqBuilder();
    }

    public StoryQueryReq() {
    }

    public StoryQueryReq(String bu, String app, String code, String name, List<FlowStatus> statuses, List<FlowType> types) {
        this.bu = bu;
        this.app = app;
        this.code = code;
        this.name = name;
        this.statuses = statuses;
        this.types = types;
    }

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public List<FlowStatus> getStatuses() {
        return this.statuses;
    }

    public List<FlowType> getTypes() {
        return this.types;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setStatuses(List<FlowStatus> statuses) {
        this.statuses = statuses;
    }

    public void setTypes(List<FlowType> types) {
        this.types = types;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryQueryReq)) {
            return false;
        }
        StoryQueryReq other = (StoryQueryReq)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        List<FlowStatus> this$statuses = this.getStatuses();
        List<FlowStatus> other$statuses = other.getStatuses();
        if (this$statuses == null ? other$statuses != null : !(this$statuses).equals(other$statuses)) {
            return false;
        }
        List<FlowType> this$types = this.getTypes();
        List<FlowType> other$types = other.getTypes();
        return !(this$types == null ? other$types != null : !(this$types).equals(other$types));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryQueryReq;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        List<FlowStatus> $statuses = this.getStatuses();
        result = result * 59 + ($statuses == null ? 43 : ($statuses).hashCode());
        List<FlowType> $types = this.getTypes();
        result = result * 59 + ($types == null ? 43 : ($types).hashCode());
        return result;
    }

    public String toString() {
        return "StoryQueryReq(super=" + super.toString() + ", bu=" + this.getBu() + ", app=" + this.getApp() + ", code=" + this.getCode() + ", name=" + this.getName() + ", statuses=" + this.getStatuses() + ", types=" + this.getTypes() + ")";
    }

    public static class StoryQueryReqBuilder {
        private String bu;
        private String app;
        private String code;
        private String name;
        private List<FlowStatus> statuses;
        private List<FlowType> types;

        StoryQueryReqBuilder() {
        }

        public StoryQueryReqBuilder bu(String bu) {
            this.bu = bu;
            return this;
        }

        public StoryQueryReqBuilder app(String app) {
            this.app = app;
            return this;
        }

        public StoryQueryReqBuilder code(String code) {
            this.code = code;
            return this;
        }

        public StoryQueryReqBuilder name(String name) {
            this.name = name;
            return this;
        }

        public StoryQueryReqBuilder statuses(List<FlowStatus> statuses) {
            this.statuses = statuses;
            return this;
        }

        public StoryQueryReqBuilder types(List<FlowType> types) {
            this.types = types;
            return this;
        }

        public StoryQueryReq build() {
            return new StoryQueryReq(this.bu, this.app, this.code, this.name, this.statuses, this.types);
        }

        public String toString() {
            return "StoryQueryReq.StoryQueryReqBuilder(bu=" + this.bu + ", app=" + this.app + ", code=" + this.code + ", name=" + this.name + ", statuses=" + this.statuses + ", types=" + this.types + ")";
        }
    }
}

