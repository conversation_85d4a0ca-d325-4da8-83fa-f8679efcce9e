/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.event.EdgeProcessedEvent;

public class InstanceEndedEvent
extends EdgeProcessedEvent {
    @Override
    public String toString() {
        return "InstanceEndedEvent()";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstanceEndedEvent)) {
            return false;
        }
        InstanceEndedEvent other = (InstanceEndedEvent)o;
        if (!other.canEqual(this)) {
            return false;
        }
        return super.equals(o);
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof InstanceEndedEvent;
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        return result;
    }
}

