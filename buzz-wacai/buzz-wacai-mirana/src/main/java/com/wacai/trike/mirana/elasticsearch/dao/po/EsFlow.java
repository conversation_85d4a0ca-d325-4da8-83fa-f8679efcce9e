/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.elasticsearch.dao.po;

import java.util.Map;

public class EsFlow {
    public static final String ES_INDEX = "mirana_flow";
    public static final String ES_TYPE = "EsFlow";
    private Long flowId;
    private String flowCode;
    private String flowVersion;
    Map<String, Long> nodes;

    public Long getFlowId() {
        return this.flowId;
    }

    public String getFlowCode() {
        return this.flowCode;
    }

    public String getFlowVersion() {
        return this.flowVersion;
    }

    public Map<String, Long> getNodes() {
        return this.nodes;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setFlowCode(String flowCode) {
        this.flowCode = flowCode;
    }

    public void setFlowVersion(String flowVersion) {
        this.flowVersion = flowVersion;
    }

    public void setNodes(Map<String, Long> nodes) {
        this.nodes = nodes;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EsFlow)) {
            return false;
        }
        EsFlow other = (EsFlow)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$flowCode = this.getFlowCode();
        String other$flowCode = other.getFlowCode();
        if (this$flowCode == null ? other$flowCode != null : !this$flowCode.equals(other$flowCode)) {
            return false;
        }
        String this$flowVersion = this.getFlowVersion();
        String other$flowVersion = other.getFlowVersion();
        if (this$flowVersion == null ? other$flowVersion != null : !this$flowVersion.equals(other$flowVersion)) {
            return false;
        }
        Map<String, Long> this$nodes = this.getNodes();
        Map<String, Long> other$nodes = other.getNodes();
        return !(this$nodes == null ? other$nodes != null : !(this$nodes).equals(other$nodes));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EsFlow;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $flowCode = this.getFlowCode();
        result = result * 59 + ($flowCode == null ? 43 : $flowCode.hashCode());
        String $flowVersion = this.getFlowVersion();
        result = result * 59 + ($flowVersion == null ? 43 : $flowVersion.hashCode());
        Map<String, Long> $nodes = this.getNodes();
        result = result * 59 + ($nodes == null ? 43 : ($nodes).hashCode());
        return result;
    }

    public String toString() {
        return "EsFlow(flowId=" + this.getFlowId() + ", flowCode=" + this.getFlowCode() + ", flowVersion=" + this.getFlowVersion() + ", nodes=" + this.getNodes() + ")";
    }
}

