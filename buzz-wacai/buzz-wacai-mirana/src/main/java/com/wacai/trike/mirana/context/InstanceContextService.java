/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.context;

import java.util.concurrent.Future;

public interface InstanceContextService {
    public InstanceContext load(String var1);

    public InstanceContext loadSub(String var1, long var2);

    public InstanceContext loadFromDb(String var1);

    public InstanceContext persistence(InstanceContext context);

    public InstanceContext cache(InstanceContext var1);

    public void removeCache(String var1);

    public void addContextFuture(String var1, Future var2);

    public void cancelContextFuture(String var1);

    public void mappingVariable(InstanceContext var1);
}

