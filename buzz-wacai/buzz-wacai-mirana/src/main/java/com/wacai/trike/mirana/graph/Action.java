/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.graph;

public class Action {
    private String action;
    private String actionType;

    public String getAction() {
        return this.action;
    }

    public String getActionType() {
        return this.actionType;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Action)) {
            return false;
        }
        Action other = (Action)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$action = this.getAction();
        String other$action = other.getAction();
        if (this$action == null ? other$action != null : !this$action.equals(other$action)) {
            return false;
        }
        String this$actionType = this.getActionType();
        String other$actionType = other.getActionType();
        return !(this$actionType == null ? other$actionType != null : !this$actionType.equals(other$actionType));
    }

    protected boolean canEqual(Object other) {
        return other instanceof Action;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $action = this.getAction();
        result = result * 59 + ($action == null ? 43 : $action.hashCode());
        String $actionType = this.getActionType();
        result = result * 59 + ($actionType == null ? 43 : $actionType.hashCode());
        return result;
    }

    public String toString() {
        return "Action(action=" + this.getAction() + ", actionType=" + this.getActionType() + ")";
    }
}

