/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSONObject
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.fasterxml.jackson.databind.ObjectMapper
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.beans.factory.annotation.Value
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.task;

import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.task.TaskExecuteException;
import com.wacai.trike.mirana.task.TaskProcessor;
import com.wacai.trike.mirana.util.HttpUtil;
import java.util.Collections;
import java.util.Map;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
class ActionTemplateProcessor
implements TaskProcessor {
    private static final Logger log = LoggerFactory.getLogger(ActionTemplateProcessor.class);
    private static final TypeReference TYPE_REFERENCE = new TypeReference<Map<String, String>>(){};
    @Autowired
    private ObjectMapper objectMapper;
    @Value(value="${cuiniao.action.template.url:http://cuiniao-web-api-pbkd.pbkd.k2.test.wacai.info/action/do}")
    private String url;

    ActionTemplateProcessor() {
    }

    @Override
    public boolean accept(TaskType taskType) {
        return TaskType.ACTION == taskType;
    }

    @Override
    public Map<String, String> execute(Node node, InstanceContext context) throws TaskExecuteException {
        log.debug("execute actionTemplate task of instance [{}], node [{}], task [{}]", new Object[]{context.getUuid(), node.getName(), node.getTaskContent()});
        try {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("collectionId", context.getBzKey());
            jsonObject.put("actionType", node.getAction().getActionType());
            jsonObject.put("action", node.getAction().getAction());
            String resData = HttpUtil.post(this.url, jsonObject, node.getTaskTimeout());
            Map result = (Map)this.objectMapper.readValue(resData, TYPE_REFERENCE);
            return Optional.ofNullable(result).orElse(Collections.emptyMap());
        }
        catch (Exception e) {
            throw new TaskExecuteException(e);
        }
    }
}

