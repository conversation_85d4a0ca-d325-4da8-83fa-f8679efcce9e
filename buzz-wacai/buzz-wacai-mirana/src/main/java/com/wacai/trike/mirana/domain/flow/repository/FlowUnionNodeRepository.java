/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.flow.repository;

import com.wacai.trike.mirana.domain.flow.po.FlowUnionNodePO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface FlowUnionNodeRepository
extends JpaRepository<FlowUnionNodePO, Long> {
    public List<FlowUnionNodePO> findByFlowId(Long var1);

    public FlowUnionNodePO findByFlowIdAndNodeId(Long var1, Long var2);

    public List<FlowUnionNodePO> findAllByFlowIdIn(List<Long> var1);
}

