/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSON
 *  com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel
 *  com.wacai.trike.mirana.api.model.EsNodeEventValueModel
 *  com.wacai.trike.mirana.api.model.EsNodeValueModel
 *  com.wacai.trike.mirana.api.model.EventModel
 *  org.apache.commons.lang3.StringUtils
 *  org.apache.http.Header
 *  org.elasticsearch.action.index.IndexRequest
 *  org.elasticsearch.action.index.IndexResponse
 *  org.elasticsearch.action.lucene.SearchRequest
 *  org.elasticsearch.action.update.UpdateRequest
 *  org.elasticsearch.action.update.UpdateResponse
 *  org.elasticsearch.client.RestHighLevelClient
 *  org.elasticsearch.common.xcontent.XContentType
 *  org.elasticsearch.index.query.BoolQueryBuilder
 *  org.elasticsearch.index.query.QueryBuilder
 *  org.elasticsearch.index.query.QueryBuilders
 *  org.elasticsearch.lucene.SearchHit
 *  org.elasticsearch.lucene.SearchHits
 *  org.elasticsearch.lucene.aggregations.AggregationBuilder
 *  org.elasticsearch.lucene.aggregations.AggregationBuilders
 *  org.elasticsearch.lucene.aggregations.Aggregations
 *  org.elasticsearch.lucene.aggregations.metrics.sum.Sum
 *  org.elasticsearch.lucene.aggregations.metrics.sum.SumAggregationBuilder
 *  org.elasticsearch.lucene.aggregations.metrics.valuecount.ValueCount
 *  org.elasticsearch.lucene.aggregations.metrics.valuecount.ValueCountAggregationBuilder
 *  org.elasticsearch.lucene.builder.SearchSourceBuilder
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.Assert
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.elasticsearch.dao.impl;

import com.alibaba.fastjson.JSON;
import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel;
import com.wacai.trike.mirana.api.model.EsNodeEventValueModel;
import com.wacai.trike.mirana.api.model.EsNodeValueModel;
import com.wacai.trike.mirana.api.model.EventModel;
import com.wacai.trike.mirana.domain.action.GenericActionService;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.domain.action.dto.VariableValueDto;
import com.wacai.trike.mirana.domain.node.po.NodePO;
import com.wacai.trike.mirana.domain.node.repository.NodeRepository;
import com.wacai.trike.mirana.elasticsearch.dao.api.EsSqlDao;
import com.wacai.trike.mirana.elasticsearch.dao.po.EsFlow;
import com.wacai.trike.mirana.elasticsearch.dao.po.EsFlowTaskInstance;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceQuery;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.Header;
import org.elasticsearch.action.index.IndexRequest;
import org.elasticsearch.action.index.IndexResponse;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.action.update.UpdateResponse;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.elasticsearch.search.aggregations.AggregationBuilder;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.elasticsearch.search.aggregations.metrics.sum.SumAggregationBuilder;
import org.elasticsearch.search.aggregations.metrics.valuecount.ValueCount;
import org.elasticsearch.search.aggregations.metrics.valuecount.ValueCountAggregationBuilder;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

@Service
public class EsSqlDaoImpl
implements EsSqlDao {
    private static final Logger log = LoggerFactory.getLogger(EsSqlDaoImpl.class);
    @Autowired
    private RestHighLevelClient client;
    @Autowired
    private NodeRepository nodeRepository;
    @Autowired
    private GenericActionService genericActionService;

    @Override
    public boolean insert(EsFlow esFlow) {
        if (esFlow == null) {
            log.error("Insert error: esFlow is null.");
            return false;
        }
        try {
            String jsonString = JSON.toJSONString(esFlow);
            IndexResponse indexResponse = this.client.index(new IndexRequest("mirana_flow", "EsFlow", esFlow.getFlowId().toString()).source(jsonString, XContentType.JSON), new Header[0]);
            log.info("Elasticsearch insert INDEX={} TYPE={} success, result:{}", new Object[]{"mirana_flow", "EsFlow", JSON.toJSONString(indexResponse)});
            return true;
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Elasticsearch insert INDEX={0} TYPE={1} error.", "mirana_flow", "EsFlow"), (Throwable)e);
            return false;
        }
    }

    @Override
    public boolean insert(EsFlowTaskInstance esFlowTaskInstance) {
        Assert.state((esFlowTaskInstance.getFlowInstanceId() != null ? 1 : 0) != 0, (String)"flowInstanceId can not be null.");
        if (esFlowTaskInstance == null) {
            log.error("Insert error: esFlowTaskInstance is null.");
            return false;
        }
        String jsonString = JSON.toJSONString(esFlowTaskInstance);
        try {
            IndexResponse indexResponse = this.client.index(new IndexRequest("mirana_flow_task_instance", "EsFlowTaskInstance", esFlowTaskInstance.getFlowInstanceId()).source(jsonString, XContentType.JSON), new Header[0]);
            log.info("Elasticsearch insert INDEX={} TYPE={} DATA={} success, result:{}", new Object[]{"mirana_flow_task_instance", "EsFlowTaskInstance", jsonString, JSON.toJSONString(indexResponse)});
            return true;
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Elasticsearch insert INDEX={0} TYPE={1} DATA={2} error.", "mirana_flow_task_instance", "EsFlowTaskInstance", jsonString), (Throwable)e);
            return false;
        }
    }

    @Override
    public boolean update(EsFlowTaskInstance esFlowTaskInstance) {
        Assert.state((esFlowTaskInstance.getFlowInstanceId() != null ? 1 : 0) != 0, (String)"flowInstanceId can not be null.");
        if (esFlowTaskInstance == null) {
            log.error("Update error: esFlowTaskInstance is null.");
            return false;
        }
        String jsonString = JSON.toJSONString(esFlowTaskInstance);
        try {
            UpdateResponse updateResponse = this.client.update(new UpdateRequest("mirana_flow_task_instance", "EsFlowTaskInstance", esFlowTaskInstance.getFlowInstanceId()).retryOnConflict(3).doc(jsonString, XContentType.JSON), new Header[0]);
            log.info("Elasticsearch update INDEX={} TYPE={} DATA={}, success, result:{}", new Object[]{"mirana_flow_task_instance", "EsFlowTaskInstance", jsonString, JSON.toJSONString(updateResponse)});
            return true;
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Elasticsearch update INDEX={0} TYPE={1} DATA={2} error.", "mirana_flow_task_instance", "EsFlowTaskInstance", jsonString), (Throwable)e);
            return false;
        }
    }

    @Override
    public List<EsFlowTaskInstance> list(Long flowId) {
        SearchRequest searchRequest = new SearchRequest(new String[]{"mirana_flow_task_instance"});
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must((QueryBuilder)QueryBuilders.termQuery((String)"flowId", flowId));
        searchSourceBuilder.query((QueryBuilder)boolQuery);
        searchRequest.source(searchSourceBuilder);
        try {
            SearchHits hits = this.client.search(searchRequest, new Header[0]).getHits();
            List<EsFlowTaskInstance> esList = Arrays.stream(hits.getHits()).map(this::convert2).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(esList)) {
                return null;
            }
            return esList;
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Elasticsearch query INDEX={0} TYPE={1} flowId={2} error.", "mirana_flow_task_instance", "EsFlowTaskInstance", flowId), (Throwable)e);
            return null;
        }
    }

    @Override
    public EsFlow findOne(Long flowId) {
        SearchRequest searchRequest = new SearchRequest(new String[]{"mirana_flow"});
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must((QueryBuilder)QueryBuilders.termQuery((String)"flowId", flowId));
        searchSourceBuilder.query((QueryBuilder)boolQuery);
        searchRequest.source(searchSourceBuilder);
        try {
            SearchHits hits = this.client.search(searchRequest, new Header[0]).getHits();
            List esFlowList = Arrays.stream(hits.getHits()).map(this::convert).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(esFlowList)) {
                return null;
            }
            return (EsFlow)esFlowList.get(0);
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Elasticsearch query INDEX={0} TYPE={1} flowId={2} error.", "mirana_flow", "EsFlow", flowId), (Throwable)e);
            return null;
        }
    }

    @Override
    public EsFlowTaskInstance findEsFlowInstance(String flowInstanceId) {
        SearchRequest searchRequest = new SearchRequest(new String[]{"mirana_flow_task_instance"});
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        boolQuery.must((QueryBuilder)QueryBuilders.termQuery((String)"flowInstanceId", (String)flowInstanceId));
        searchSourceBuilder.query((QueryBuilder)boolQuery);
        searchRequest.source(searchSourceBuilder);
        try {
            SearchHits hits = this.client.search(searchRequest, new Header[0]).getHits();
            List esList = Arrays.stream(hits.getHits()).map(this::convert2).filter(Objects::nonNull).collect(Collectors.toList());
            log.info(MessageFormat.format("Elasticsearch query INDEX={0} TYPE={1} flowInstanceId={2} resut={3}", "mirana_flow_task_instance", "EsFlowTaskInstance", flowInstanceId, JSON.toJSONString(esList)));
            if (CollectionUtils.isEmpty(esList)) {
                return null;
            }
            EsFlowTaskInstance result = (EsFlowTaskInstance)esList.get(0);
            return result;
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Elasticsearch query INDEX={0} TYPE={1} flowInstanceId={2} error.", "mirana_flow_task_instance", "EsFlowTaskInstance", flowInstanceId), (Throwable)e);
            return null;
        }
    }

    @Override
    public EsFlowTaskInstanceModel sum(EsFlowTaskInstanceQuery query) {
        SearchRequest searchRequest = new SearchRequest(new String[]{"mirana_flow_task_instance"});
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query((QueryBuilder)this.build(query));
        if (!CollectionUtils.isEmpty(query.getNodeIdList())) {
            for (Long nodeId : query.getNodeIdList()) {
                String nodeName = "node_" + nodeId;
                SumAggregationBuilder sumAggregationBuilder = (SumAggregationBuilder)AggregationBuilders.sum((String)nodeName).field("nodes." + nodeName);
                searchSourceBuilder.aggregation((AggregationBuilder)sumAggregationBuilder);
            }
        }
        if (!CollectionUtils.isEmpty(query.getEventList()) && StringUtils.isNotBlank((CharSequence)query.getStoryCode())) {
            for (EventModel eventModel : query.getEventList()) {
                String eventName = "story_" + query.getStoryCode() + "_event_" + eventModel.getEventCode();
                for (Long nodeId : query.getNodeIdList()) {
                    String eventNodeName = eventName + "_node_" + nodeId;
                    SumAggregationBuilder sumAggregationBuilder = (SumAggregationBuilder)AggregationBuilders.sum((String)eventNodeName).field("events." + eventNodeName);
                    searchSourceBuilder.aggregation((AggregationBuilder)sumAggregationBuilder);
                }
            }
        }
        ValueCountAggregationBuilder countAggregationBuilder = (ValueCountAggregationBuilder)AggregationBuilders.count((String)"total").field("loanId");
        searchSourceBuilder.aggregation((AggregationBuilder)countAggregationBuilder);
        searchRequest.source(searchSourceBuilder);
        try {
            Aggregations aggregations = this.client.search(searchRequest, new Header[0]).getAggregations();
            log.info(MessageFormat.format("Elasticsearch query INDEX={0} TYPE={1} EsFlowTaskInstanceQuery={2} RESULT={3}", "mirana_flow_task_instance", "EsFlowTaskInstance", JSON.toJSONString(query), JSON.toJSONString(aggregations)));
            if (aggregations == null) {
                return null;
            }
            HashMap<String, Integer> nodes = new HashMap<String, Integer>();
            HashMap<String, Integer> events = new HashMap<String, Integer>();
            HashMap<String, Integer> nodeEvents = new HashMap<String, Integer>();
            HashMap<String, String> eventMap = new HashMap<String, String>();
            for (Long nodeId : query.getNodeIdList()) {
                String nodeName = "node_" + nodeId;
                Sum sum = (Sum)aggregations.get(nodeName);
                int value = (int)sum.getValue();
                nodes.put(nodeName, value);
                if (CollectionUtils.isEmpty(query.getEventList()) || !StringUtils.isNotBlank((CharSequence)query.getStoryCode())) continue;
                for (EventModel eventModel : query.getEventList()) {
                    String eventNodeName = "story_" + query.getStoryCode() + "_event_" + eventModel.getEventCode() + "_" + nodeName;
                    String eventName = "event_" + eventModel.getEventCode() + "_" + nodeName;
                    Sum eventSum = (Sum)aggregations.get(eventNodeName);
                    int eventSumValue = (int)eventSum.getValue();
                    this.addCount(events, eventModel.getEventCode(), eventSumValue);
                    this.addCount(nodeEvents, eventName, eventSumValue);
                    eventMap.put(eventModel.getEventCode(), eventModel.getEventName());
                }
            }
            ValueCount valueCount = (ValueCount)aggregations.get("total");
            long total = valueCount.getValue();
            Map<String, EsNodeValueModel> nodeValues = nodes.entrySet().stream().collect(Collectors.toMap(e -> (String)e.getKey(), e -> new EsNodeValueModel((Integer)e.getValue(), this.getRate(total, (Integer)e.getValue()), this.getActionCode((String)e.getKey()), this.getNodeLabel((String)e.getKey()), this.getNodeEvents((String)e.getKey(), query.getEventList(), nodeEvents, events)), (e1, e2) -> e1));
            List eventTotalList = events.entrySet().stream().map(event -> new EsNodeEventValueModel((Integer)event.getValue(), "100%", (String)event.getKey(), (String)eventMap.get(event.getKey()))).collect(Collectors.toList());
            EsFlowTaskInstanceModel result = new EsFlowTaskInstanceModel();
            result.setFlowCode(query.getFlowCode());
            result.setFlowId(query.getFlowId());
            result.setFlowVersion(query.getFlowVersion());
            result.setNodes(nodeValues);
            result.setEvents(eventTotalList);
            return result;
        }
        catch (Exception e3) {
            log.error(MessageFormat.format("Elasticsearch sum INDEX={0} TYPE={1} EsFlowTaskInstanceQuery={2} error.", "mirana_flow_task_instance", "EsFlowTaskInstance", JSON.toJSONString(query)), (Throwable)e3);
            return null;
        }
    }

    private void addCount(Map<String, Integer> map, String key, int count) {
        if (CollectionUtils.isEmpty(map)) {
            map.put(key, count);
        } else {
            Integer old = map.get(key);
            if (old == null) {
                old = 0;
            }
            map.put(key, old + count);
        }
    }

    private String getActionCode(String nodeName) {
        Long nodeId = Long.valueOf(nodeName.substring(5));
        if (nodeId == null) {
            return null;
        }
        NodePO nodePO = this.nodeRepository.findById(nodeId).orElse(null);
        if (nodePO == null || nodePO.getActionTemplateId() == null) {
            return null;
        }
        GenericTemplateDto templateFromMemo = this.genericActionService.getTemplateByIdFromMemo(nodePO.getActionTemplateId());
        if (templateFromMemo == null) {
            return null;
        }
        List<VariableValueDto> variableValueDtoList = templateFromMemo.getVariableValueDto();
        if (CollectionUtils.isEmpty(variableValueDtoList)) {
            return null;
        }
        for (VariableValueDto item : variableValueDtoList) {
            if (!StringUtils.equals((CharSequence)item.getVariable().getCode(), (CharSequence)"actionCode")) continue;
            return item.getValue();
        }
        return null;
    }

    private List<EsNodeEventValueModel> getNodeEvents(String nodeName, List<EventModel> eventList, Map<String, Integer> nodeEvents, Map<String, Integer> events) {
        ArrayList<EsNodeEventValueModel> nodeEventList = new ArrayList<EsNodeEventValueModel>();
        if (CollectionUtils.isEmpty(eventList)) {
            return nodeEventList;
        }
        for (EventModel eventModel : eventList) {
            String eventNodeName = "event_" + eventModel.getEventCode() + "_" + nodeName;
            Integer eventSumValue = nodeEvents.get(eventNodeName);
            if (eventSumValue == null || eventSumValue <= 0) continue;
            Integer total = events.get(eventModel.getEventCode());
            EsNodeEventValueModel esNodeEventValueModel = new EsNodeEventValueModel(eventSumValue, this.getRate(total.intValue(), eventSumValue), eventModel.getEventCode(), eventModel.getEventName());
            nodeEventList.add(esNodeEventValueModel);
        }
        return nodeEventList;
    }

    private String getNodeLabel(String nodeName) {
        Long nodeId = Long.valueOf(nodeName.substring(5));
        if (nodeId == null) {
            return null;
        }
        NodePO nodePO = this.nodeRepository.findById(nodeId).orElse(null);
        if (nodePO == null) {
            return null;
        }
        return nodePO.getName();
    }

    private String getRate(long total, int value) {
        if (total == 0L || value == 0) {
            return "0%";
        }
        return new BigDecimal(value).multiply(new BigDecimal(100)).divide(new BigDecimal(total), 2, 4).toString() + "%";
    }

    private BoolQueryBuilder build(EsFlowTaskInstanceQuery query) {
        BoolQueryBuilder boolQuery = QueryBuilders.boolQuery();
        if (StringUtils.isNotBlank((CharSequence)query.getFlowCode())) {
            boolQuery.must((QueryBuilder)QueryBuilders.termQuery((String)"flowCode", (String)query.getFlowCode()));
        }
        if (query.getFlowId() != null && query.getFlowId() > 0L) {
            boolQuery.must((QueryBuilder)QueryBuilders.termQuery((String)"flowId", query.getFlowId()));
        }
        if (StringUtils.isNotBlank((CharSequence)query.getFlowVersion())) {
            boolQuery.must((QueryBuilder)QueryBuilders.termQuery((String)"flowVersion", (String)query.getFlowVersion()));
        }
        if (!CollectionUtils.isEmpty(query.getLoanIdList())) {
            boolQuery.must((QueryBuilder)QueryBuilders.termsQuery((String)"loanId", query.getLoanIdList()));
        }
        if (!CollectionUtils.isEmpty(query.getTags())) {
            BoolQueryBuilder boolMathQuery = QueryBuilders.boolQuery();
            for (String tag : query.getTags()) {
                boolMathQuery.must((QueryBuilder)QueryBuilders.termQuery((String)"tags", (String)tag));
            }
            boolQuery.must((QueryBuilder)boolMathQuery);
        }
        if (query.getStartDate() != null) {
            boolQuery.must((QueryBuilder)QueryBuilders.rangeQuery((String)"instanceTime").from(query.getStartDate().getTime()));
        }
        if (query.getEndDate() != null) {
            boolQuery.must((QueryBuilder)QueryBuilders.rangeQuery((String)"instanceTime").to(query.getEndDate().getTime()));
        }
        return boolQuery;
    }

    @Override
    public List<EsFlowTaskInstance> list(EsFlowTaskInstanceQuery query) {
        SearchRequest searchRequest = new SearchRequest(new String[]{"mirana_flow_task_instance"});
        SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
        searchSourceBuilder.query((QueryBuilder)this.build(query));
        searchRequest.source(searchSourceBuilder);
        try {
            SearchHits hits = this.client.search(searchRequest, new Header[0]).getHits();
            List<EsFlowTaskInstance> esList = Arrays.stream(hits.getHits()).map(this::convert2).filter(Objects::nonNull).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(esList)) {
                return null;
            }
            return esList;
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Elasticsearch query INDEX={0} TYPE={1} EsFlowTaskInstanceQuery={2} error.", "mirana_flow", "EsFlow", JSON.toJSONString(query)), (Throwable)e);
            return null;
        }
    }

    private EsFlow convert(SearchHit hit) {
        EsFlow result = null;
        try {
            result = (EsFlow)JSON.parseObject((String)hit.getSourceAsString(), EsFlow.class);
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Covert EsFlow from ElasticSearch hit={0} error.", hit.getSourceAsString()), (Throwable)e);
        }
        return result;
    }

    private EsFlowTaskInstance convert2(SearchHit hit) {
        EsFlowTaskInstance result = null;
        try {
            result = (EsFlowTaskInstance)JSON.parseObject((String)hit.getSourceAsString(), EsFlowTaskInstance.class);
        }
        catch (Exception e) {
            log.error(MessageFormat.format("Covert EsFlow from ElasticSearch hit={0} error.", hit.getSourceAsString()), (Throwable)e);
        }
        return result;
    }
}

