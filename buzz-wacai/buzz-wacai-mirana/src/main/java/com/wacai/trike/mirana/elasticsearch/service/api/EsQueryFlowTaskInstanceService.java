/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.model.EsFlowTaskInstanceListModel
 *  com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel
 */
package com.wacai.trike.mirana.elasticsearch.service.api;

import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceListModel;
import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceInsertDTO;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceQuery;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceUpdateDTO;
import java.util.List;

public interface EsQueryFlowTaskInstanceService {
    public boolean insert(EsFlowTaskInstanceInsertDTO var1);

    public EsFlowTaskInstanceInsertDTO find(String var1);

    public boolean update(EsFlowTaskInstanceUpdateDTO var1);

    public EsFlowTaskInstanceModel sum(EsFlowTaskInstanceQuery var1);

    public List<EsFlowTaskInstanceListModel> list(EsFlowTaskInstanceQuery var1);
}

