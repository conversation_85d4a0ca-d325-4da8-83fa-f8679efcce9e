/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.service;

import com.wacai.trike.mirana.lifecycle.SubjectProvider;
import com.wacai.trike.mirana.service.FlowSubjectDTO;
import java.util.List;

public interface FlowSubjectService {
    public List<FlowSubjectDTO> findByFlowCode(String var1);

    public List<SubjectProvider.Subject> subjects();

    public FlowSubjectDTO bind(FlowSubjectDTO var1);

    public boolean cancel(FlowSubjectDTO var1);
}

