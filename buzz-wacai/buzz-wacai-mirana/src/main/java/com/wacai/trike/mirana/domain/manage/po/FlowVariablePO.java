/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.core.type.TypeReference
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.manage.po;

import com.fasterxml.jackson.core.type.TypeReference;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.service.FlowVariableDTO;
import com.wacai.trike.mirana.util.ObjectMappers;
import com.wacai.trike.mirana.util.ObjectUtils;
import java.util.List;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="flow_variable")
public class FlowVariablePO
extends BasePO {
    private Long appId;
    private String code;
    private String name;
    private String type;
    private String sourceType;
    private String supportedOperators;
    private String candidates;
    private String candidatesUrl;

    public FlowVariableDTO toDto() {
        FlowVariableDTO variableDTO = ObjectUtils.convertNotNull(this, FlowVariableDTO.class);
        variableDTO.setSupportedOperators(ObjectMappers.mustReadValue(this.getSupportedOperators(), new TypeReference<List<FlowVariableDTO.SupportedOperator>>(){}));
        variableDTO.setCandidates(ObjectMappers.mustReadValue(this.getCandidates(), new TypeReference<List<FlowVariableDTO.Candidate>>(){}));
        return variableDTO;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getType() {
        return this.type;
    }

    public String getSourceType() {
        return this.sourceType;
    }

    public String getSupportedOperators() {
        return this.supportedOperators;
    }

    public String getCandidates() {
        return this.candidates;
    }

    public String getCandidatesUrl() {
        return this.candidatesUrl;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public void setSupportedOperators(String supportedOperators) {
        this.supportedOperators = supportedOperators;
    }

    public void setCandidates(String candidates) {
        this.candidates = candidates;
    }

    public void setCandidatesUrl(String candidatesUrl) {
        this.candidatesUrl = candidatesUrl;
    }

    @Override
    public String toString() {
        return "FlowVariablePO(appId=" + this.getAppId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", type=" + this.getType() + ", sourceType=" + this.getSourceType() + ", supportedOperators=" + this.getSupportedOperators() + ", candidates=" + this.getCandidates() + ", candidatesUrl=" + this.getCandidatesUrl() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowVariablePO)) {
            return false;
        }
        FlowVariablePO other = (FlowVariablePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$type = this.getType();
        String other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$sourceType = this.getSourceType();
        String other$sourceType = other.getSourceType();
        if (this$sourceType == null ? other$sourceType != null : !this$sourceType.equals(other$sourceType)) {
            return false;
        }
        String this$supportedOperators = this.getSupportedOperators();
        String other$supportedOperators = other.getSupportedOperators();
        if (this$supportedOperators == null ? other$supportedOperators != null : !this$supportedOperators.equals(other$supportedOperators)) {
            return false;
        }
        String this$candidates = this.getCandidates();
        String other$candidates = other.getCandidates();
        if (this$candidates == null ? other$candidates != null : !this$candidates.equals(other$candidates)) {
            return false;
        }
        String this$candidatesUrl = this.getCandidatesUrl();
        String other$candidatesUrl = other.getCandidatesUrl();
        return !(this$candidatesUrl == null ? other$candidatesUrl != null : !this$candidatesUrl.equals(other$candidatesUrl));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowVariablePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $sourceType = this.getSourceType();
        result = result * 59 + ($sourceType == null ? 43 : $sourceType.hashCode());
        String $supportedOperators = this.getSupportedOperators();
        result = result * 59 + ($supportedOperators == null ? 43 : $supportedOperators.hashCode());
        String $candidates = this.getCandidates();
        result = result * 59 + ($candidates == null ? 43 : $candidates.hashCode());
        String $candidatesUrl = this.getCandidatesUrl();
        result = result * 59 + ($candidatesUrl == null ? 43 : $candidatesUrl.hashCode());
        return result;
    }
}

