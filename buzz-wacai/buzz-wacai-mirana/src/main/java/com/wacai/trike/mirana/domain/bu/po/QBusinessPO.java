/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.bu.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.bu.po.BusinessPO;
import java.time.LocalDateTime;

public class QBusinessPO
extends EntityPathBase<BusinessPO> {
    private static final long serialVersionUID = 82893552L;
    public static final QBusinessPO businessPO = new QBusinessPO("businessPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath code;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final StringPath description;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QBusinessPO(String variable) {
        super(BusinessPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.description = this.createString("description");
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QBusinessPO(Path<? extends BusinessPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.description = this.createString("description");
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QBusinessPO(PathMetadata metadata) {
        super(BusinessPO.class, metadata);
        this.active = this._super.active;
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.description = this.createString("description");
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

