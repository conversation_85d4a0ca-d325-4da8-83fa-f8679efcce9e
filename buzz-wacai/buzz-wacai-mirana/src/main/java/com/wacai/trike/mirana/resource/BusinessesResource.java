/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.base.Strings
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.resource;

import com.google.common.base.Strings;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.resource.BusinessModel;
import com.wacai.trike.mirana.service.BusinessDTO;
import com.wacai.trike.mirana.service.BusinessRequest;
import com.wacai.trike.mirana.service.BusinessService;
import com.wacai.trike.mirana.util.TimeFormatUtil;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/managed/businesses"})
public class BusinessesResource {
    @Autowired
    private BusinessService businessService;

    @GetMapping
    public ApiResponse<List<BusinessModel>> listBusiness(@RequestParam(name="id", defaultValue="") List<Long> ids, @RequestParam(name="name", defaultValue="") List<String> names, @RequestParam(name="code", defaultValue="") List<String> codes, @RequestParam(name="start", defaultValue="") String start, @RequestParam(name="end", defaultValue="") String end) {
        BusinessRequest.BusinessRequestBuilder builder = BusinessRequest.builder().ids(ids).codes(codes).names(names);
        builder.start(Strings.isNullOrEmpty((String)start) ? TimeFormatUtil.MIN : TimeFormatUtil.mustParseCST(start));
        builder.end(Strings.isNullOrEmpty((String)end) ? TimeFormatUtil.MAX : TimeFormatUtil.mustParseCST(end));
        return ApiResponse.success(this.businessService.listAll(builder.build()).stream().map(BusinessDTO::toModel).collect(Collectors.toList()));
    }
}

