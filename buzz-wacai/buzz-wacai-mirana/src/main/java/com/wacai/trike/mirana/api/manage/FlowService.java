/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.ComboBox
 *  javax.validation.Valid
 *  javax.validation.constraints.NotNull
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 */
package com.wacai.trike.mirana.api.manage;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.trike.mirana.api.manage.model.FlowCreateModel;
import com.wacai.trike.mirana.api.manage.model.FlowGraphModel;
import com.wacai.trike.mirana.api.manage.model.FlowVersionModel;
import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceListModel;
import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel;
import java.util.List;
import java.util.Map;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping(value={"/flow"})
public interface FlowService {
    @PostMapping(value={"/save"})
    public ApiResponse<Long> create(@RequestBody @NotNull @Valid FlowCreateModel model);

    @PostMapping(value={"/draw"})
    public ApiResponse<Long> draw(@RequestBody @NotNull @Valid FlowGraphModel model);

    @PostMapping(value={"/story/draw"})
    public ApiResponse<Long> storyDraw(@RequestBody @NotNull @Valid FlowGraphModel model);

    @GetMapping(value={"/{id}"})
    public ApiResponse<FlowGraphModel> get(@PathVariable(value="id") Long id);

    @GetMapping(value={"/{code}/{version}"})
    public ApiResponse<FlowGraphModel> get(@PathVariable(value="code") String code, @PathVariable(value="version") String version);

    public ApiResponse<FlowVersionModel> versions(@RequestParam(value="app") Long appId, @RequestParam(value="flow") String code);

    @GetMapping(value={"/node/combo/{id}"})
    public ApiResponse<List<ComboBox<Long>>> getCombo(@PathVariable(value="id") Long var1);

    @GetMapping(value={"/node/combo"})
    public ApiResponse<List<ComboBox<Long>>> getCombo(@RequestParam(value="bu") String var1, @RequestParam(value="app") String var2, @RequestParam(value="flow") String var3);

    @GetMapping(value={"/delay/type/combo"})
    public ApiResponse<List<ComboBox<String>>> getDelayTypeCombo();

    @GetMapping(value={"/delay/unit/combo"})
    public ApiResponse<List<ComboBox<String>>> getDelayUnitCombo();

    @GetMapping(value={"/flow-status"})
    public ApiResponse<List<ComboBox<String>>> listFlowStatus();

    @GetMapping(value={"/sum"})
    public ApiResponse<EsFlowTaskInstanceModel> sum(@RequestParam(value="code", required=false) String var1, @RequestParam(value="version", required=false) String var2, @RequestParam(value="startTime", required=false) String var3, @RequestParam(value="endTime", required=false) String var4, @RequestParam(value="tags", required=false) List<String> var5, @RequestParam(value="loanIds", required=false) List<Long> var6, @RequestParam(value="storyCode", required=false) String var7);

    @GetMapping(value={"/sum/node"})
    public ApiResponse<Map<String, Object>> nodeSum(@RequestParam(value="code", required=false) String var1, @RequestParam(value="version", required=false) String var2, @RequestParam(value="nodeId", required=false) String var3, @RequestParam(value="actionCode", required=false) String var4, @RequestParam(value="startTime", required=false) String var5, @RequestParam(value="endTime", required=false) String var6, @RequestParam(value="tags", required=false) List<String> var7);

    @GetMapping(value={"/showList"})
    public ApiResponse<List<EsFlowTaskInstanceListModel>> listEsData(@RequestParam(value="code", required=false) String var1, @RequestParam(value="version", required=false) String var2, @RequestParam(value="startTime", required=false) String var3, @RequestParam(value="endTime", required=false) String var4, @RequestParam(value="flowId", required=false) Long var5, @RequestParam(value="nodeIds", required=false) List<Long> var6, @RequestParam(value="tags", required=false) List<String> var7, @RequestParam(value="loanIds", required=false) List<Long> var8);
}

