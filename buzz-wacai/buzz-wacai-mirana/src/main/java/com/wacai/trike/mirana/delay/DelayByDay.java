/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.delay;

import com.wacai.trike.mirana.delay.DelayCalculator;
import com.wacai.trike.mirana.delay.SpecUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;
import java.util.concurrent.ThreadLocalRandom;

public class DelayByDay
implements Serializable,
DelayCalculator {
    private Long in;
    private String inSpec = "06:00:00";
    private Long out;
    private String outSpec = "23:00:00";

    @Override
    public LocalDateTime expectStart(LocalDateTime referDate) {
        if (Objects.isNull(referDate)) {
            return null;
        }
        LocalDateTime inTime = referDate;
        if (Objects.nonNull(this.in)) {
            inTime = inTime.plusDays(this.in);
        }
        return SpecUtil.setSpecWithMinNano(inTime, this.inSpec).plusSeconds(ThreadLocalRandom.current().nextInt(3600));
    }

    @Override
    public LocalDateTime expectEnd(LocalDateTime referDate) {
        if (Objects.isNull(referDate)) {
            return null;
        }
        LocalDateTime outTime = referDate;
        if (Objects.nonNull(this.out)) {
            outTime = outTime.plusDays(this.out);
        }
        return SpecUtil.setSpecWithMinNano(outTime, this.outSpec).plusSeconds(ThreadLocalRandom.current().nextInt(3600));
    }

    public Long getIn() {
        return this.in;
    }

    public String getInSpec() {
        return this.inSpec;
    }

    public Long getOut() {
        return this.out;
    }

    public String getOutSpec() {
        return this.outSpec;
    }

    public void setIn(Long in) {
        this.in = in;
    }

    public void setInSpec(String inSpec) {
        this.inSpec = inSpec;
    }

    public void setOut(Long out) {
        this.out = out;
    }

    public void setOutSpec(String outSpec) {
        this.outSpec = outSpec;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DelayByDay)) {
            return false;
        }
        DelayByDay other = (DelayByDay)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$in = this.getIn();
        Long other$in = other.getIn();
        if (this$in == null ? other$in != null : !(this$in).equals(other$in)) {
            return false;
        }
        String this$inSpec = this.getInSpec();
        String other$inSpec = other.getInSpec();
        if (this$inSpec == null ? other$inSpec != null : !this$inSpec.equals(other$inSpec)) {
            return false;
        }
        Long this$out = this.getOut();
        Long other$out = other.getOut();
        if (this$out == null ? other$out != null : !(this$out).equals(other$out)) {
            return false;
        }
        String this$outSpec = this.getOutSpec();
        String other$outSpec = other.getOutSpec();
        return !(this$outSpec == null ? other$outSpec != null : !this$outSpec.equals(other$outSpec));
    }

    protected boolean canEqual(Object other) {
        return other instanceof DelayByDay;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $in = this.getIn();
        result = result * 59 + ($in == null ? 43 : ($in).hashCode());
        String $inSpec = this.getInSpec();
        result = result * 59 + ($inSpec == null ? 43 : $inSpec.hashCode());
        Long $out = this.getOut();
        result = result * 59 + ($out == null ? 43 : ($out).hashCode());
        String $outSpec = this.getOutSpec();
        result = result * 59 + ($outSpec == null ? 43 : $outSpec.hashCode());
        return result;
    }

    public String toString() {
        return "DelayByDay(in=" + this.getIn() + ", inSpec=" + this.getInSpec() + ", out=" + this.getOut() + ", outSpec=" + this.getOutSpec() + ")";
    }
}

