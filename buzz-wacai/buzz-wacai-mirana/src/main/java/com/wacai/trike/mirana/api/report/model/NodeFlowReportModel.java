/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.report.model;

import java.io.Serializable;

public class NodeFlowReportModel
implements Serializable {
    private long nodeId;
    private String code;
    private String name;
    private long running;
    private long completed;
    private long failed;
    private long skip;

    public long getNodeId() {
        return this.nodeId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public long getRunning() {
        return this.running;
    }

    public long getCompleted() {
        return this.completed;
    }

    public long getFailed() {
        return this.failed;
    }

    public long getSkip() {
        return this.skip;
    }

    public void setNodeId(long nodeId) {
        this.nodeId = nodeId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setRunning(long running) {
        this.running = running;
    }

    public void setCompleted(long completed) {
        this.completed = completed;
    }

    public void setFailed(long failed) {
        this.failed = failed;
    }

    public void setSkip(long skip) {
        this.skip = skip;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof NodeFlowReportModel)) {
            return false;
        }
        NodeFlowReportModel other = (NodeFlowReportModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (this.getNodeId() != other.getNodeId()) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        if (this.getRunning() != other.getRunning()) {
            return false;
        }
        if (this.getCompleted() != other.getCompleted()) {
            return false;
        }
        if (this.getFailed() != other.getFailed()) {
            return false;
        }
        return this.getSkip() == other.getSkip();
    }

    protected boolean canEqual(Object other) {
        return other instanceof NodeFlowReportModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        long $nodeId = this.getNodeId();
        result = result * 59 + (int)($nodeId >>> 32 ^ $nodeId);
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        long $running = this.getRunning();
        result = result * 59 + (int)($running >>> 32 ^ $running);
        long $completed = this.getCompleted();
        result = result * 59 + (int)($completed >>> 32 ^ $completed);
        long $failed = this.getFailed();
        result = result * 59 + (int)($failed >>> 32 ^ $failed);
        long $skip = this.getSkip();
        result = result * 59 + (int)($skip >>> 32 ^ $skip);
        return result;
    }

    public String toString() {
        return "NodeFlowReportModel(nodeId=" + this.getNodeId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", running=" + this.getRunning() + ", completed=" + this.getCompleted() + ", failed=" + this.getFailed() + ", skip=" + this.getSkip() + ")";
    }
}

