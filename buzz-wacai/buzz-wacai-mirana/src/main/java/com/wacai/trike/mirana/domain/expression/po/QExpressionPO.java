/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 *  com.wacai.loan.trike.expression.Operator
 */
package com.wacai.trike.mirana.domain.expression.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.loan.trike.expression.Operator;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.expression.po.ExpressionPO;
import java.time.LocalDateTime;

public class QExpressionPO
extends EntityPathBase<ExpressionPO> {
    private static final long serialVersionUID = 12779213L;
    public static final QExpressionPO expressionPO = new QExpressionPO("expressionPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath args;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> edgeId;
    public final StringPath field;
    public final StringPath fieldType;
    public final StringPath function;
    public final NumberPath<Long> id;
    public final EnumPath<Operator> operator;
    public final StringPath threshold;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QExpressionPO(String variable) {
        super(ExpressionPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.args = this.createString("args");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.edgeId = this.createNumber("edgeId", Long.class);
        this.field = this.createString("field");
        this.fieldType = this.createString("fieldType");
        this.function = this.createString("function");
        this.id = this._super.id;
        this.operator = this.createEnum("operator", Operator.class);
        this.threshold = this.createString("threshold");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QExpressionPO(Path<? extends ExpressionPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.args = this.createString("args");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.edgeId = this.createNumber("edgeId", Long.class);
        this.field = this.createString("field");
        this.fieldType = this.createString("fieldType");
        this.function = this.createString("function");
        this.id = this._super.id;
        this.operator = this.createEnum("operator", Operator.class);
        this.threshold = this.createString("threshold");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QExpressionPO(PathMetadata metadata) {
        super(ExpressionPO.class, metadata);
        this.active = this._super.active;
        this.args = this.createString("args");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.edgeId = this.createNumber("edgeId", Long.class);
        this.field = this.createString("field");
        this.fieldType = this.createString("fieldType");
        this.function = this.createString("function");
        this.id = this._super.id;
        this.operator = this.createEnum("operator", Operator.class);
        this.threshold = this.createString("threshold");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

