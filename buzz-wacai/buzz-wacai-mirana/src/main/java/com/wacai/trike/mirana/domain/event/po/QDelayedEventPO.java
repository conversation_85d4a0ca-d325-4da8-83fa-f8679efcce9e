/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.event.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import java.time.LocalDateTime;

public class QDelayedEventPO
extends EntityPathBase<DelayedEventPO> {
    private static final long serialVersionUID = 746021173L;
    public static final QDelayedEventPO delayedEventPO = new QDelayedEventPO("delayedEventPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final DateTimePath<LocalDateTime> expectFireTime;
    public final NumberPath<Long> id;
    public final StringPath instanceUuid;
    public final StringPath operator;
    public final StringPath param;
    public final EnumPath<DelayedEventStatus> status;
    public final StringPath taskId;
    public final StringPath type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final StringPath uuid;

    public QDelayedEventPO(String variable) {
        super(DelayedEventPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.expectFireTime = this.createDateTime("expectFireTime", LocalDateTime.class);
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.operator = this.createString("operator");
        this.param = this.createString("param");
        this.status = this.createEnum("status", DelayedEventStatus.class);
        this.taskId = this.createString("taskId");
        this.type = this.createString("type");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }

    public QDelayedEventPO(Path<? extends DelayedEventPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.expectFireTime = this.createDateTime("expectFireTime", LocalDateTime.class);
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.operator = this.createString("operator");
        this.param = this.createString("param");
        this.status = this.createEnum("status", DelayedEventStatus.class);
        this.taskId = this.createString("taskId");
        this.type = this.createString("type");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }

    public QDelayedEventPO(PathMetadata metadata) {
        super(DelayedEventPO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.expectFireTime = this.createDateTime("expectFireTime", LocalDateTime.class);
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.operator = this.createString("operator");
        this.param = this.createString("param");
        this.status = this.createEnum("status", DelayedEventStatus.class);
        this.taskId = this.createString("taskId");
        this.type = this.createString("type");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }
}

