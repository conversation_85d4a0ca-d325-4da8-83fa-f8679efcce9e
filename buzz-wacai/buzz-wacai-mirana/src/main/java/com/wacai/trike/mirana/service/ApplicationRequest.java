/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.service;

import java.time.LocalDateTime;
import java.util.List;

public class ApplicationRequest {
    private List<Long> ids;
    private List<Long> buIds;
    private List<String> codes;
    private List<String> names;
    private LocalDateTime start;
    private LocalDateTime end;

    ApplicationRequest(List<Long> ids, List<Long> buIds, List<String> codes, List<String> names, LocalDateTime start, LocalDateTime end) {
        this.ids = ids;
        this.buIds = buIds;
        this.codes = codes;
        this.names = names;
        this.start = start;
        this.end = end;
    }

    public static ApplicationRequestBuilder builder() {
        return new ApplicationRequestBuilder();
    }

    public List<Long> getIds() {
        return this.ids;
    }

    public List<Long> getBuIds() {
        return this.buIds;
    }

    public List<String> getCodes() {
        return this.codes;
    }

    public List<String> getNames() {
        return this.names;
    }

    public LocalDateTime getStart() {
        return this.start;
    }

    public LocalDateTime getEnd() {
        return this.end;
    }

    public static class ApplicationRequestBuilder {
        private List<Long> ids;
        private List<Long> buIds;
        private List<String> codes;
        private List<String> names;
        private LocalDateTime start;
        private LocalDateTime end;

        ApplicationRequestBuilder() {
        }

        public ApplicationRequestBuilder ids(List<Long> ids) {
            this.ids = ids;
            return this;
        }

        public ApplicationRequestBuilder buIds(List<Long> buIds) {
            this.buIds = buIds;
            return this;
        }

        public ApplicationRequestBuilder codes(List<String> codes) {
            this.codes = codes;
            return this;
        }

        public ApplicationRequestBuilder names(List<String> names) {
            this.names = names;
            return this;
        }

        public ApplicationRequestBuilder start(LocalDateTime start) {
            this.start = start;
            return this;
        }

        public ApplicationRequestBuilder end(LocalDateTime end) {
            this.end = end;
            return this;
        }

        public ApplicationRequest build() {
            return new ApplicationRequest(this.ids, this.buIds, this.codes, this.names, this.start, this.end);
        }

        public String toString() {
            return "ApplicationRequest.ApplicationRequestBuilder(ids=" + this.ids + ", buIds=" + this.buIds + ", codes=" + this.codes + ", names=" + this.names + ", start=" + this.start + ", end=" + this.end + ")";
        }
    }
}

