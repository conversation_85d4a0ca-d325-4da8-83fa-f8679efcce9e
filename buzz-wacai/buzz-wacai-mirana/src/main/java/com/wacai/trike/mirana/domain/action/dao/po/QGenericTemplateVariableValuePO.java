/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.action.dao.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericTemplateVariableValuePO;
import java.time.LocalDateTime;

public class QGenericTemplateVariableValuePO
extends EntityPathBase<GenericTemplateVariableValuePO> {
    private static final long serialVersionUID = 1328602331L;
    public static final QGenericTemplateVariableValuePO genericTemplateVariableValuePO = new QGenericTemplateVariableValuePO("genericTemplateVariableValuePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> id;
    public final NumberPath<Long> templateId;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final NumberPath<Long> variableId;
    public final StringPath variableValue;

    public QGenericTemplateVariableValuePO(String variable) {
        super(GenericTemplateVariableValuePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.templateId = this.createNumber("templateId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.variableId = this.createNumber("variableId", Long.class);
        this.variableValue = this.createString("variableValue");
    }

    public QGenericTemplateVariableValuePO(Path<? extends GenericTemplateVariableValuePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.templateId = this.createNumber("templateId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.variableId = this.createNumber("variableId", Long.class);
        this.variableValue = this.createString("variableValue");
    }

    public QGenericTemplateVariableValuePO(PathMetadata metadata) {
        super(GenericTemplateVariableValuePO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.templateId = this.createNumber("templateId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.variableId = this.createNumber("variableId", Long.class);
        this.variableValue = this.createString("variableValue");
    }
}

