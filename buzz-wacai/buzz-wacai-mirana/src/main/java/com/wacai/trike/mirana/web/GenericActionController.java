/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.Page
 *  io.swagger.annotations.ApiOperation
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.web;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.GenericActionService;
import com.wacai.trike.mirana.domain.action.GenericTemplateService;
import com.wacai.trike.mirana.domain.action.dto.GenericActionDto;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.domain.action.enums.ActionEnums;
import com.wacai.trike.mirana.domain.bu.ApplicationService;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.web.GenericTemplateController;
import com.wacai.trike.mirana.web.model.GenericActionModel;
import com.wacai.trike.mirana.web.model.GenericActionRequest;
import com.wacai.trike.mirana.web.model.GenericActionRequestModel;
import com.wacai.trike.mirana.web.model.GenericTemplateModel;
import com.wacai.trike.mirana.web.model.GenericTemplateRequest;
import io.swagger.annotations.ApiOperation;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/managed/generic-action"})
public class GenericActionController {
    private static final Logger log = LoggerFactory.getLogger(GenericActionController.class);
    @Autowired
    private GenericActionService genericActionService;
    @Autowired
    private ApplicationService applicationService;
    @Autowired
    private GenericTemplateService genericTemplateService;

    @ApiOperation(value="动作分页查询", notes="分页参数在uri中拼接")
    @GetMapping
    public ApiResponse<Page<GenericActionModel>> page(GenericActionRequest request) {
        if (request.getPageSize() <= 0 || request.getPageIndex() < 0) {
            return ApiResponse.error("Invalid paging input");
        }
        Page<GenericActionModel> result = this.convert(this.genericActionService.page(request));
        if (request.isIncludeTemplate()) {
            this.fillTemplates(result.getData());
        }
        return ApiResponse.success(result);
    }

    @ApiOperation(value="批量查询", notes="参数在uri中拼接")
    @GetMapping(value={"/batch"})
    public ApiResponse<List<GenericActionModel>> batchQuery(GenericActionRequest request) {
        List<GenericActionModel> result = this.convert(this.genericActionService.batch(request));
        if (request.isIncludeTemplate()) {
            this.fillTemplates(result);
        }
        return ApiResponse.success(result);
    }

    @ApiOperation(value="动作详情查询", notes="uri中传入变量id")
    @GetMapping(value={"/{id}"})
    public ApiResponse<GenericActionModel> query(@PathVariable(value="id") Long id) {
        GenericActionDto actionDto = this.genericActionService.query(id);
        if (actionDto == null) {
            return ApiResponse.error("Not found: " + id);
        }
        return ApiResponse.success(ObjectUtils.convert(actionDto, GenericActionModel.class));
    }

    @ApiOperation(value="新增或更新动作", notes="新增时不需要传入id")
    @PostMapping
    public ApiResponse<GenericActionModel> save(@RequestBody GenericActionRequestModel model) {
        if (this.applicationService.query(model.getAppId()) == null) {
            return ApiResponse.error("Not found Application: " + model.getAppId());
        }
        return ApiResponse.success(ObjectUtils.convert(this.genericActionService.save(ObjectUtils.convertNotNull(model, GenericActionDto.class)), GenericActionModel.class));
    }

    @ApiOperation(value="删除动作", notes="uri中传入变量id")
    @PostMapping(value={"/{id}"})
    public ApiResponse<Integer> delete(@PathVariable(value="id") Long id) {
        if (id == null) {
            return ApiResponse.error("Action id must be not null");
        }
        if (this.genericActionService.query(id) == null) {
            return ApiResponse.error(("Not found: " + id));
        }
        List<GenericTemplateDto> templates = this.genericTemplateService.batch(new GenericTemplateRequest().setGenericActionId(id));
        if (!CollectionUtils.isEmpty(templates)) {
            return ApiResponse.error("当前通用动作下存在动作模板，不能直接删除。");
        }
        return ApiResponse.success(this.genericActionService.delete(id));
    }

    @ApiOperation(value="\u901a\u7528\u52a8\u4f5c\u7c7b\u578b\u5217\u8868", notes="\u8fd4\u56de\u6240\u6709\u7c7b\u578b\u96c6\u5408")
    @GetMapping(value={"/types"})
    public ApiResponse<List<ActionEnums.Type>> actionTypes() {
        return ApiResponse.success(Arrays.asList(ActionEnums.Type.values()));
    }

    private void fillTemplates(List<GenericActionModel> actions) {
        actions.forEach(action -> {
            List<GenericTemplateDto> batch = this.genericTemplateService.batch(new GenericTemplateRequest().setPageSize(Integer.MAX_VALUE).setActive(true).setGenericActionId(action.getId()));
            if (!CollectionUtils.isEmpty(batch)) {
                List<GenericTemplateModel> templates = GenericTemplateController.convert(batch);
                templates.forEach(tem -> {
                    tem.setActionId(null);
                    tem.setAppId(null);
                    tem.setAppName(null);
                    tem.setGenericActionName(null);
                    tem.setVarList(null);
                });
                action.setAppId(null);
                action.setAppName(null);
                action.setCallbackUrl(null);
                action.setVariableIds(null);
                action.setType(null);
                action.setVariables(null);
                action.setTemplateModels(templates);
            }
        });
    }

    private Page<GenericActionModel> convert(Page<GenericActionDto> page) {
        return new Page().setTotal(page.getTotal()).setPage(page.getPage()).setData(this.convert(page.getData()));
    }

    private List<GenericActionModel> convert(List<GenericActionDto> actions) {
        return actions.stream().map(dto -> ObjectUtils.convert(dto, GenericActionModel.class)).filter(Objects::nonNull).collect(Collectors.toList());
    }
}

