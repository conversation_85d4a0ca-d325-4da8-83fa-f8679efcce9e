/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.ComboBox
 *  com.wacai.loan.trike.common.model.Page
 *  org.springframework.web.bind.annotation.GetMapping
 */
package com.wacai.trike.mirana.api;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.api.model.FlowModel;
import com.wacai.trike.mirana.api.model.FlowQueryRequest;
import com.wacai.trike.mirana.api.model.InstanceDetail;
import com.wacai.trike.mirana.api.model.InstanceDetailRequest;
import com.wacai.trike.mirana.api.model.InstanceQueryRequest;
import com.wacai.trike.mirana.api.model.InstanceQueryResponse;
import com.wacai.trike.mirana.api.model.NodeQueryRequest;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;

public interface FlowQueryService {
    @GetMapping(value={"/flow/nodes"})
    public ApiResponse<List<ComboBox<Long>>> listNode(NodeQueryRequest var1);

    @GetMapping(value={"/flow/instance/query"})
    public ApiResponse<Page<InstanceQueryResponse>> queryInstance(InstanceQueryRequest var1);

    @GetMapping(value={"/flow/instance/detail"})
    public ApiResponse<InstanceDetail> detail(InstanceDetailRequest var1);

    @GetMapping(value={"/flow/list"})
    public ApiResponse<List<FlowModel>> list(FlowQueryRequest var1);
}

