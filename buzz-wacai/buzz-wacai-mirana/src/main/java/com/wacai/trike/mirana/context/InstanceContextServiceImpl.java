/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.common.utils.CollectionUtils
 *  com.alibaba.dubbo.common.utils.ConcurrentHashSet
 *  com.alibaba.fastjson.JSON
 *  com.alibaba.fastjson.serializer.SerializerFeature
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.common.redis.RedisException
 *  com.wacai.trike.cloud.proxy.redis.RedisProxy
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  org.apache.curator.RetryPolicy
 *  org.apache.curator.framework.CuratorFramework
 *  org.apache.curator.framework.CuratorFrameworkFactory
 *  org.apache.curator.framework.api.ACLBackgroundPathAndBytesable
 *  org.apache.curator.framework.api.BackgroundPathable
 *  org.apache.curator.framework.api.CuratorWatcher
 *  org.apache.curator.retry.RetryForever
 *  org.apache.logging.log4j.util.Strings
 *  org.apache.zookeeper.CreateMode
 *  org.apache.zookeeper.WatchedEvent
 *  org.apache.zookeeper.Watcher$Event$EventType
 *  org.apache.zookeeper.data.Stat
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.beans.factory.annotation.Value
 *  org.springframework.stereotype.Service
 *  org.springframework.util.Assert
 */
package com.wacai.trike.mirana.context;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.ConcurrentHashSet;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.common.redis.RedisException;
import com.wacai.trike.cloud.proxy.redis.RedisProxy;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.InstanceContextService;
import com.wacai.trike.mirana.context.InstanceVariable;
import com.wacai.trike.mirana.context.Step;
import com.wacai.trike.mirana.context.TaskInstance;
import com.wacai.trike.mirana.domain.context.po.InstanceVariablePO;
import com.wacai.trike.mirana.domain.context.po.TaskInstancePO;
import com.wacai.trike.mirana.domain.context.repository.InstanceVariableRepository;
import com.wacai.trike.mirana.domain.context.repository.TaskInstanceRepository;
import com.wacai.trike.mirana.domain.flow.po.QFlowPO;
import com.wacai.trike.mirana.domain.instance.po.InstancePO;
import com.wacai.trike.mirana.domain.instance.repository.InstanceRepository;
import com.wacai.trike.mirana.domain.node.po.NodePO;
import com.wacai.trike.mirana.domain.node.po.QNodePO;
import com.wacai.trike.mirana.domain.step.po.StepPO;
import com.wacai.trike.mirana.domain.step.repository.StepRepository;
import com.wacai.trike.mirana.graph.FlowGraph;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.service.FlowNodeVariableDTO;
import com.wacai.trike.mirana.service.FlowNodeVariableService;
import com.wacai.trike.mirana.service.FlowVarService;
import com.wacai.trike.mirana.service.FlowVariableDTO;
import com.wacai.trike.mirana.util.ObjectUtils;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Future;
import java.util.stream.Collectors;
import javax.annotation.PostConstruct;
import org.apache.curator.RetryPolicy;
import org.apache.curator.framework.CuratorFramework;
import org.apache.curator.framework.CuratorFrameworkFactory;
import org.apache.curator.framework.api.ACLBackgroundPathAndBytesable;
import org.apache.curator.framework.api.BackgroundPathable;
import org.apache.curator.framework.api.CuratorWatcher;
import org.apache.curator.retry.RetryForever;
import org.apache.logging.log4j.util.Strings;
import org.apache.zookeeper.CreateMode;
import org.apache.zookeeper.WatchedEvent;
import org.apache.zookeeper.Watcher;
import org.apache.zookeeper.data.Stat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

@Service
class InstanceContextServiceImpl implements InstanceContextService {

	private static final Logger log = LoggerFactory.getLogger(InstanceContextServiceImpl.class);
	private static final String KEY_PREFIX = "ins";
	private static final int DEFAULT_EXPIRE_TIME = 86400;
	private static final String ZK_CANCEL_PATH = "/mirana/instance/cancel";
	@Value(value = "${middleware.lock.zk.servers}")
	private String zkServers;
	@Autowired
	private InstanceRepository instanceRepository;
	@Autowired
	private InstanceVariableRepository variableRepository;
	@Autowired
	private TaskInstanceRepository taskInstanceRepository;
	@Autowired
	private StepRepository stepRepository;
	@Autowired
	private RedisProxy redisProxy;
	private CuratorFramework curatorFramework;
	private Map<String, Set<Future>> futureMap = new ConcurrentHashMap<String, Set<Future>>();
	@Autowired
	private FlowGraphService flowGraphService;
	@Autowired
	private FlowNodeVariableService flowNodeVariableService;
	@Autowired
	private FlowVarService flowVarService;
	@Autowired
	private JPAQueryFactory jpaQueryFactory;

	InstanceContextServiceImpl() {
	}

	@Override
	public InstanceContext load(String uuid) {
		InstanceContext context = this.loadFromDb(uuid);
		return context;
	}

	@Override
	public InstanceContext loadSub(String uuid, long nodeId) {
		NodePO nodePO = (NodePO) ((JPAQuery) this.jpaQueryFactory.selectFrom((EntityPath) QNodePO.nodePO)
				.where((Predicate) QNodePO.nodePO.id.eq(nodeId))).fetchOne();
		if (nodePO == null) {
			return null;
		}
		if (nodePO.getTaskType() != TaskType.START_SUB_FLOW) {
			return null;
		}
		String flowCode = nodePO.getTaskContent();
		Long flowId = (Long) ((JPAQuery) ((JPAQuery) this.jpaQueryFactory.select(QFlowPO.flowPO.id)
				.from((EntityPath) QFlowPO.flowPO)).where((Predicate) QFlowPO.flowPO.code.eq(flowCode))).fetchFirst();
		InstancePO exist = this.instanceRepository.findByParentUuidAndFlowId(uuid, flowId);
		if (exist == null) {
			return null;
		}
		return this.load(exist.getUuid());
	}

	private String buildKey(String uuid) {
		return "ins:" + uuid;
	}

	@Override
	public InstanceContext loadFromDb(String uuid) {
		InstancePO instancePO = this.instanceRepository.findByUuid(uuid).orElse(null);
		if (Objects.isNull(instancePO)) {
			return null;
		}
		InstanceContext context = ObjectUtils.convert(instancePO, InstanceContext.class);
		Assert.notNull( context, (String) ("not found instance " + uuid));
		FlowGraph graph = this.flowGraphService.getFlowGraph(context.getFlowId());
		context.setFlowCode(graph.getCode());
		context.setVersion(graph.getVersion());
		context.setEffectiveVariables(this.loadInstanceVariable(uuid));
		TaskInstancePO taskInstance = this.taskInstanceRepository
				.findTop1ByInstanceUuidAndNodeIdOrderByCreatedTimeDesc(uuid, context.getCurrentNodeId());
		if (Objects.nonNull(taskInstance)) {
			TaskInstance instance = ObjectUtils.convert(taskInstance, TaskInstance.class);
			context.setCurrentTaskInstance(instance);
		}
		return context;
	}

	private Map<String, String> loadInstanceVariable(String instanceUuid) {
		List<InstanceVariablePO> variables = this.variableRepository.findByInstanceUuidOrderById(instanceUuid);
		HashMap<String, String> result = new HashMap<String, String>();
		for (InstanceVariablePO variable : variables) {
			result.put(variable.getField(), variable.getValue());
		}
		return result;
	}

	@Override
	public InstanceContext persistence(InstanceContext context) {
		log.info("persistence instance [{}]",  context.getUuid());
		this.persistenceTaskInstance(context);
		this.persistenceStep(context);
		this.persistenceInstance(context);
		return context;
	}

	private void persistenceInstance(InstanceContext context) {
		if (!context.isDirty()) {
			return;
		}
		InstancePO instancePO = Optional.ofNullable(context.getUuid())
				.map(this.instanceRepository::findByUuid)
				.filter(Optional::isPresent).map(Optional::get).orElse(new InstancePO());
		BeanUtils.copyProperties( context,  instancePO, (String[]) new String[] { "id" });
		if (Objects.nonNull(context.getCreator())) {
			instancePO.setCreatedBy(context.getCreator());
		}
		if (Objects.nonNull(context.getModifier())) {
			instancePO.setUpdatedBy(context.getModifier());
		}
		if (context.isDerailed()) {
			instancePO.setFlowId(context.getFlowId());
		}
		instancePO = (InstancePO) this.instanceRepository.save(instancePO);
		context.setId(instancePO.getId());
		context.setDirty(false);
	}

	private void persistenceTaskInstance(InstanceContext context) {
		//得到每个 TaskInstance
		for (TaskInstance taskInstance : context.getTaskInstances()) {
			this.doPersistenceTaskInstance(context, taskInstance);
		}
		this.doPersistenceTaskInstance(context, context.getCurrentTaskInstance());
	}

	private void doPersistenceTaskInstance(InstanceContext context, TaskInstance taskInstance) {
		if (Objects.isNull(taskInstance) || !taskInstance.isDirty()) {
			return;
		}
		TaskInstancePO taskInstancePO = Optional.ofNullable(taskInstance.getUuid())
				.map(this.taskInstanceRepository::findByUuid).filter(Optional::isPresent).map(Optional::get)
				.orElse(new TaskInstancePO());
		BeanUtils.copyProperties( taskInstance,  taskInstancePO);
		taskInstancePO.setInstanceUuid(context.getUuid());
		taskInstancePO.setUpdatedBy(taskInstance.getOperator());
		taskInstancePO = (TaskInstancePO) this.taskInstanceRepository.save(taskInstancePO);
		taskInstance.setId(taskInstancePO.getId());
		taskInstance.setDirty(false);
		context.setModifier(taskInstance.getOperator());
		context.setDirty(true);
		this.persistenceVariable(context.getUuid(), taskInstance.getUuid(), taskInstance.getVariables());
	}

	private void persistenceVariable(String instanceUuid, String taskUuid, List<InstanceVariable> variables) {
		variables = variables.stream().sorted(Comparator.comparingLong(InstanceVariable::getTimestamp))
				.collect(Collectors.toList());
		for (InstanceVariable variable : variables) {
			if (Strings.isBlank((String) variable.getField()) || Strings.isBlank((String) variable.getValue())
					|| !variable.isDirty())
				continue;
			InstanceVariablePO variablePO = ObjectUtils.convert(variable, InstanceVariablePO.class);
			assert (variablePO != null);
			variablePO.setInstanceUuid(instanceUuid);
			variablePO.setTaskUuid(taskUuid);
			variablePO = (InstanceVariablePO) this.variableRepository.save(variablePO);
			variable.setDirty(false);
			variable.setId(variablePO.getId());
		}
	}

	private void persistenceStep(InstanceContext context) {
		for (Step step : context.getSteps()) {
			if (!step.isDirty())
				continue;
			StepPO stepPO = new StepPO();
			stepPO.setEdgeId(step.getEdgeId());
			stepPO.setInstanceUuid(context.getUuid());
			stepPO = (StepPO) this.stepRepository.save(stepPO);
			step.setDirty(false);
			step.setId(stepPO.getId());
		}
	}

	@Override
	public InstanceContext cache(InstanceContext context) {
		try {
			this.redisProxy.setex(this.buildKey(context.getUuid()), 86400,
					JSON.toJSONBytes( context, (SerializerFeature[]) new SerializerFeature[0]));
			return context;
		} catch (RedisException e) {
			log.error("cache context to redis failed, context {} , error {}",  context,
					 e.getMessage());
			throw new RuntimeException(e);
		}
	}

	@Override
	public void removeCache(String uuid) {
		try {
			this.redisProxy.del(new String[] { this.buildKey(uuid) });
		} catch (RedisException e) {
			log.warn("remove cache from redis failed, uuid {}",  uuid,  e);
		}
	}

	@Override
	public void addContextFuture(String uuid, Future future) {
		this.futureMap.computeIfAbsent(uuid, k -> new ConcurrentHashSet()).add(future);
	}

	@Override
	public void cancelContextFuture(String uuid) {
		try {
			this.doCancel(uuid);
			this.curatorFramework.setData().forPath(ZK_CANCEL_PATH, uuid.getBytes());
		} catch (Exception e) {
			log.error("cancel instance failed", (Throwable) e);
		}
	}

	@Override
	public void mappingVariable(InstanceContext context) {
		InstancePO parent = this.instanceRepository.findByUuid(context.getParentUuid()).orElse(null);
		if (parent == null) {
			return;
		}
		List<NodePO> nodePOS = ((JPAQuery) ((JPAQuery) ((JPAQuery) this.jpaQueryFactory
				.selectFrom((EntityPath) QNodePO.nodePO)
				.leftJoin((EntityPath) QFlowPO.flowPO))
						.on((Predicate) QFlowPO.flowPO.code.eq((Expression) QNodePO.nodePO.taskContent)))
								.where((Predicate) QNodePO.nodePO.flowId.eq(parent.getFlowId())
										.and((Predicate) QNodePO.nodePO.taskType.eq(TaskType.START_SUB_FLOW))
										.and((Predicate) QFlowPO.flowPO.id.eq(context.getFlowId())))).fetch();
		if (CollectionUtils.isEmpty((Collection) nodePOS)) {
			log.error("Not found nodes of flow {}",  parent.getFlowId());
			return;
		}
		nodePOS.forEach(node -> {
			List<FlowNodeVariableDTO> variableDTOS = this.flowNodeVariableService.query(node.getId(),
					context.getFlowId());
			if (!CollectionUtils.isEmpty(variableDTOS)) {
				this.doMapping(context, variableDTOS);
			}
		});
	}

	private void doMapping(InstanceContext context, List<FlowNodeVariableDTO> variableDTOS) {
		for (FlowNodeVariableDTO variable : variableDTOS) {
			FlowVariableDTO parentVar = this.flowVarService.query(variable.getParentFlowVariableId());
			FlowVariableDTO subVar = this.flowVarService.query(variable.getSubFlowVariableId());
			if (parentVar == null || subVar == null) {
				log.error("Should never occurred: not found flow-variable {} / {}.",
						variable.getParentFlowVariableId(), variable.getSubFlowVariableId());
				continue;
			}
			List<InstanceVariablePO> vars = this.variableRepository.findByInstanceUuidAndField(context.getUuid(),
					subVar.getCode());
			vars.forEach(var -> {
				TaskInstancePO taskInstance = this.taskInstanceRepository
						.findTop1ByInstanceUuidAndNodeIdOrderByCreatedTimeDesc(context.getParentUuid(),
								variable.getNodeId());
				if (taskInstance == null) {
					log.error("Should never occurred: not found task-instance of [instance: {}, node: {}]",
							 context.getParentUuid(),  variable.getNodeId());
					return;
				}
				InstanceVariablePO po = new InstanceVariablePO();
				po.setInstanceUuid(context.getParentUuid());
				po.setTaskUuid(taskInstance.getUuid());
				po.setType(var.getType());
				po.setField(parentVar.getCode());
				po.setValue(var.getValue());
				this.variableRepository.save(po);
			});
		}
	}

	private void doCancel(String uuid) {
		int count = 0;
		for (Future future : this.futureMap.getOrDefault(uuid, Collections.emptySet())) {
			if (future.isDone())
				continue;
			future.cancel(true);
			++count;
		}
		this.futureMap.remove(uuid);
		if (count > 0) {
			log.info("cancel instance [{}] success, cancel future count [{}]",  uuid,  count);
		}
	}

	@PostConstruct
	public void initCurator() {
		this.curatorFramework = CuratorFrameworkFactory.newClient((String) this.zkServers,
				(RetryPolicy) new RetryForever(1000));
		this.curatorFramework.start();
		try {
			Stat stat = (Stat) this.curatorFramework.checkExists().forPath(ZK_CANCEL_PATH);
			if (stat == null) {
				((ACLBackgroundPathAndBytesable) this.curatorFramework.create().creatingParentsIfNeeded()
						.withMode(CreateMode.PERSISTENT)).forPath(ZK_CANCEL_PATH);
			}
			this.addWatcher();
		} catch (Exception e) {
			log.error("create cancel instance path failed ", (Throwable) e);
			throw new RuntimeException("create cancel instance path failed ", e);
		}
	}

	private void addWatcher() throws Exception {
		((BackgroundPathable) this.curatorFramework.getData()
				.usingWatcher((CuratorWatcher) new InstanceCancelWatcher())).forPath(ZK_CANCEL_PATH);
	}

	private class InstanceCancelWatcher
			implements CuratorWatcher {
		private InstanceCancelWatcher() {
		}

		public void process(WatchedEvent event) throws Exception {
			log.info("receive zookeeper event : {}, State : {}",  event.getType(),  event.getState());
			InstanceContextServiceImpl.this.addWatcher();
			if (event.getType() == Watcher.Event.EventType.NodeDataChanged) {
				byte[] data = (byte[]) InstanceContextServiceImpl.this.curatorFramework.getData()
						.forPath(InstanceContextServiceImpl.ZK_CANCEL_PATH);
				String uuid = new String(data);
				InstanceContextServiceImpl.this.doCancel(uuid);
			}
		}
	}
}
