/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.web.model;

import com.wacai.trike.mirana.domain.action.enums.ActionEnums;
import java.util.List;

public class GenericActionRequestModel {
    private Long id;
    private String name;
    private ActionEnums.Type type;
    private Long appId;
    private String callbackUrl;
    private List<Long> variableIds;

    public Long getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public ActionEnums.Type getType() {
        return this.type;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getCallbackUrl() {
        return this.callbackUrl;
    }

    public List<Long> getVariableIds() {
        return this.variableIds;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(ActionEnums.Type type) {
        this.type = type;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public void setVariableIds(List<Long> variableIds) {
        this.variableIds = variableIds;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericActionRequestModel)) {
            return false;
        }
        GenericActionRequestModel other = (GenericActionRequestModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        ActionEnums.Type this$type = this.getType();
        ActionEnums.Type other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$callbackUrl = this.getCallbackUrl();
        String other$callbackUrl = other.getCallbackUrl();
        if (this$callbackUrl == null ? other$callbackUrl != null : !this$callbackUrl.equals(other$callbackUrl)) {
            return false;
        }
        List<Long> this$variableIds = this.getVariableIds();
        List<Long> other$variableIds = other.getVariableIds();
        return !(this$variableIds == null ? other$variableIds != null : !(this$variableIds).equals(other$variableIds));
    }

    protected boolean canEqual(Object other) {
        return other instanceof GenericActionRequestModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        ActionEnums.Type $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $callbackUrl = this.getCallbackUrl();
        result = result * 59 + ($callbackUrl == null ? 43 : $callbackUrl.hashCode());
        List<Long> $variableIds = this.getVariableIds();
        result = result * 59 + ($variableIds == null ? 43 : ($variableIds).hashCode());
        return result;
    }

    public String toString() {
        return "GenericActionRequestModel(id=" + this.getId() + ", name=" + this.getName() + ", type=" + (this.getType()) + ", appId=" + this.getAppId() + ", callbackUrl=" + this.getCallbackUrl() + ", variableIds=" + this.getVariableIds() + ")";
    }
}

