/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.annos;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.ManualProcessingEvent;
import com.wacai.trike.mirana.event.NodeDelayProcessingEvent;
import com.wacai.trike.mirana.util.ObjectMappers;

@Component
public class DerailmentHandler
		implements MethodInvocationHandler {
	
	private static final Logger log = LoggerFactory.getLogger(DerailmentHandler.class);
	@Autowired
	private DerailmentDecider derailmentDecider;

	@Override
	public int order() {
		return -1;
	}

	@Override
	public void preHandle(MethodInvocation invocation) {
		if (!this.sanity(invocation)) {
			return;
		}
		EventExchange exchange = (EventExchange) invocation.getArgs()[0];
		this.derailmentDecider.handle(exchange.getContext());
	}

	@Override
	public void postHandle(MethodInvocation invocation) {
	}

	private boolean sanity(MethodInvocation invocation) {
		Object[] args = invocation.getArgs();
		if (args == null || args.length != 1 || !(args[0] instanceof EventExchange)) {
			return false;
		}
		EventExchange exchange = (EventExchange) args[0];
		if (exchange.getCurrent() == null || exchange.getContext() == null) {
			return false;
		}
		if (!(exchange.getCurrent() instanceof ManualProcessingEvent)
				&& !(exchange.getCurrent() instanceof NodeDelayProcessingEvent)) {
			log.info("Non-Expected ManualProcessingEvent/NodeDelayProcessingEvent {}",
					 ObjectMappers.mustWriteValue(exchange.getCurrent()));
			return false;
		}
		log.info("output event {}",  ObjectMappers.mustWriteValue(exchange.getCurrent()));
		return true;
	}
}
