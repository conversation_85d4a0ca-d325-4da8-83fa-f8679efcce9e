/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.context.repository;

import com.wacai.trike.mirana.domain.context.po.InstanceVariablePO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface InstanceVariableRepository
extends JpaRepository<InstanceVariablePO, Long> {
    public List<InstanceVariablePO> findByInstanceUuidOrderById(String var1);

    public List<InstanceVariablePO> findByInstanceUuidAndField(String var1, String var2);
}

