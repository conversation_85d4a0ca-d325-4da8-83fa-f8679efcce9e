/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.Query
 */
package com.wacai.trike.mirana.api.manage.model;

import com.wacai.loan.trike.common.model.Query;
import java.util.List;

public class ActionQueryModel
extends Query {
    private String bu;
    private String app;
    private List<Long> ids;
    private String code;
    private String name;

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public List<Long> getIds() {
        return this.ids;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public void setIds(List<Long> ids) {
        this.ids = ids;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ActionQueryModel)) {
            return false;
        }
        ActionQueryModel other = (ActionQueryModel)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        List<Long> this$ids = this.getIds();
        List<Long> other$ids = other.getIds();
        if (this$ids == null ? other$ids != null : !(this$ids).equals(other$ids)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        return !(this$name == null ? other$name != null : !this$name.equals(other$name));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ActionQueryModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        List<Long> $ids = this.getIds();
        result = result * 59 + ($ids == null ? 43 : ($ids).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        return result;
    }

    public String toString() {
        return "ActionQueryModel(super=" + super.toString() + ", bu=" + this.getBu() + ", app=" + this.getApp() + ", ids=" + this.getIds() + ", code=" + this.getCode() + ", name=" + this.getName() + ")";
    }
}

