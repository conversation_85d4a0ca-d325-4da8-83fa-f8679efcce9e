/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.trike.mirana.domain.bu.po.ApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.service.AppService;
import com.wacai.trike.mirana.service.ApplicationDTO;
import com.wacai.trike.mirana.service.ApplicationRequest;

@Service
public class AppServiceImpl implements AppService {
	@Autowired
	private JPAQueryFactory jpaQueryFactory;

	@Override
	public List<ApplicationDTO> listApplication(ApplicationRequest request) {
		BooleanExpression expression = QApplicationPO.applicationPO.createdTime.between(request.getStart(),
				request.getEnd());
		if (!CollectionUtils.isEmpty(request.getIds())) {
			expression = expression.and((Predicate) QApplicationPO.applicationPO.id.in(request.getIds()));
		} else {
			if (!CollectionUtils.isEmpty(request.getBuIds())) {
				expression = expression.and((Predicate) QApplicationPO.applicationPO.buId.in(request.getBuIds()));
			}
			if (!CollectionUtils.isEmpty(request.getCodes())) {
				expression = this.bind(expression, request.getCodes(), QApplicationPO.applicationPO.code, true);
			}
			if (!CollectionUtils.isEmpty(request.getNames())) {
				expression = this.bind(expression, request.getNames(), QApplicationPO.applicationPO.name, true);
			}
		}
		return this.jpaQueryFactory.selectFrom( QApplicationPO.applicationPO)
				.where(expression).fetch().stream().map(ApplicationPO::toDTO).collect(Collectors.toList());
	}
}
