/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.aspectj.lang.ProceedingJoinPoint
 *  org.aspectj.lang.Signature
 */
package com.wacai.trike.mirana.annos;

import java.util.Arrays;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;

public class MethodInvocation {
    private final long start = System.currentTimeMillis();
    private final ProceedingJoinPoint pjp;
    private final Object[] args;
    private final Signature signature;
    private final BarrierAnnotationMeta barrierMeta;
    private long end;
    private Throwable throwable;
    private Object result;

    public MethodInvocation(ProceedingJoinPoint pjp, BarrierAnnotationMeta barrierMeta) {
        this.pjp = pjp;
        this.args = pjp.getArgs();
        this.signature = pjp.getSignature();
        this.barrierMeta = barrierMeta;
    }

    public long getCost() {
        return this.end - this.start;
    }

    public String getMethodIndexName() {
        return this.barrierMeta.getMethodIndexName();
    }

    public long getStart() {
        return this.start;
    }

    public ProceedingJoinPoint getPjp() {
        return this.pjp;
    }

    public Object[] getArgs() {
        return this.args;
    }

    public Signature getSignature() {
        return this.signature;
    }

    public BarrierAnnotationMeta getBarrierMeta() {
        return this.barrierMeta;
    }

    public long getEnd() {
        return this.end;
    }

    public Throwable getThrowable() {
        return this.throwable;
    }

    public Object getResult() {
        return this.result;
    }

    public void setEnd(long end) {
        this.end = end;
    }

    public void setThrowable(Throwable throwable) {
        this.throwable = throwable;
    }

    public void setResult(Object result) {
        this.result = result;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof MethodInvocation)) {
            return false;
        }
        MethodInvocation other = (MethodInvocation)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (this.getStart() != other.getStart()) {
            return false;
        }
        ProceedingJoinPoint this$pjp = this.getPjp();
        ProceedingJoinPoint other$pjp = other.getPjp();
        if (this$pjp == null ? other$pjp != null : !this$pjp.equals(other$pjp)) {
            return false;
        }
        if (!Arrays.deepEquals(this.getArgs(), other.getArgs())) {
            return false;
        }
        Signature this$signature = this.getSignature();
        Signature other$signature = other.getSignature();
        if (this$signature == null ? other$signature != null : !this$signature.equals(other$signature)) {
            return false;
        }
        BarrierAnnotationMeta this$barrierMeta = this.getBarrierMeta();
        BarrierAnnotationMeta other$barrierMeta = other.getBarrierMeta();
        if (this$barrierMeta == null ? other$barrierMeta != null : !(this$barrierMeta).equals(other$barrierMeta)) {
            return false;
        }
        if (this.getEnd() != other.getEnd()) {
            return false;
        }
        Throwable this$throwable = this.getThrowable();
        Throwable other$throwable = other.getThrowable();
        if (this$throwable == null ? other$throwable != null : !this$throwable.equals(other$throwable)) {
            return false;
        }
        Object this$result = this.getResult();
        Object other$result = other.getResult();
        return !(this$result == null ? other$result != null : !this$result.equals(other$result));
    }

    protected boolean canEqual(Object other) {
        return other instanceof MethodInvocation;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        long $start = this.getStart();
        result = result * 59 + (int)($start >>> 32 ^ $start);
        ProceedingJoinPoint $pjp = this.getPjp();
        result = result * 59 + ($pjp == null ? 43 : $pjp.hashCode());
        result = result * 59 + Arrays.deepHashCode(this.getArgs());
        Signature $signature = this.getSignature();
        result = result * 59 + ($signature == null ? 43 : $signature.hashCode());
        BarrierAnnotationMeta $barrierMeta = this.getBarrierMeta();
        result = result * 59 + ($barrierMeta == null ? 43 : ($barrierMeta).hashCode());
        long $end = this.getEnd();
        result = result * 59 + (int)($end >>> 32 ^ $end);
        Throwable $throwable = this.getThrowable();
        result = result * 59 + ($throwable == null ? 43 : $throwable.hashCode());
        Object $result = this.getResult();
        result = result * 59 + ($result == null ? 43 : $result.hashCode());
        return result;
    }

    public String toString() {
        return "MethodInvocation(start=" + this.getStart() + ", pjp=" + this.getPjp() + ", args=" + Arrays.deepToString(this.getArgs()) + ", signature=" + this.getSignature() + ", barrierMeta=" + this.getBarrierMeta() + ", end=" + this.getEnd() + ", throwable=" + this.getThrowable() + ", result=" + this.getResult() + ")";
    }
}

