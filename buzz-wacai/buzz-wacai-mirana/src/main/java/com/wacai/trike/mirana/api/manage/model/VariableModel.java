/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ComboBox
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.manage.model;

import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.trike.mirana.api.constant.CandidateType;
import com.wacai.trike.mirana.api.constant.VariableCategory;
import javax.validation.constraints.NotBlank;

public class VariableModel
extends ComboBox<String> {
    private Long id;
    private Long appId;
    private Long flowId;
    @NotBlank
    private String code;
    private String name;
    private VariableCategory category;
    private String dataType;
    private CandidateType candidateType = CandidateType.NONE;
    private Object candidateContent;

    public Long getId() {
        return this.id;
    }

    public Long getAppId() {
        return this.appId;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public VariableCategory getCategory() {
        return this.category;
    }

    public String getDataType() {
        return this.dataType;
    }

    public CandidateType getCandidateType() {
        return this.candidateType;
    }

    public Object getCandidateContent() {
        return this.candidateContent;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCategory(VariableCategory category) {
        this.category = category;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public void setCandidateType(CandidateType candidateType) {
        this.candidateType = candidateType;
    }

    public void setCandidateContent(Object candidateContent) {
        this.candidateContent = candidateContent;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof VariableModel)) {
            return false;
        }
        VariableModel other = (VariableModel)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        VariableCategory this$category = this.getCategory();
        VariableCategory other$category = other.getCategory();
        if (this$category == null ? other$category != null : !((this$category)).equals(other$category)) {
            return false;
        }
        String this$dataType = this.getDataType();
        String other$dataType = other.getDataType();
        if (this$dataType == null ? other$dataType != null : !this$dataType.equals(other$dataType)) {
            return false;
        }
        CandidateType this$candidateType = this.getCandidateType();
        CandidateType other$candidateType = other.getCandidateType();
        if (this$candidateType == null ? other$candidateType != null : !((this$candidateType)).equals(other$candidateType)) {
            return false;
        }
        Object this$candidateContent = this.getCandidateContent();
        Object other$candidateContent = other.getCandidateContent();
        return !(this$candidateContent == null ? other$candidateContent != null : !this$candidateContent.equals(other$candidateContent));
    }

    protected boolean canEqual(Object other) {
        return other instanceof VariableModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        VariableCategory $category = this.getCategory();
        result = result * 59 + ($category == null ? 43 : (($category)).hashCode());
        String $dataType = this.getDataType();
        result = result * 59 + ($dataType == null ? 43 : $dataType.hashCode());
        CandidateType $candidateType = this.getCandidateType();
        result = result * 59 + ($candidateType == null ? 43 : (($candidateType)).hashCode());
        Object $candidateContent = this.getCandidateContent();
        result = result * 59 + ($candidateContent == null ? 43 : $candidateContent.hashCode());
        return result;
    }

    public String toString() {
        return "VariableModel(super=" + super.toString() + ", id=" + this.getId() + ", appId=" + this.getAppId() + ", flowId=" + this.getFlowId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", category=" + (this.getCategory()) + ", dataType=" + this.getDataType() + ", candidateType=" + (this.getCandidateType()) + ", candidateContent=" + this.getCandidateContent() + ")";
    }
}

