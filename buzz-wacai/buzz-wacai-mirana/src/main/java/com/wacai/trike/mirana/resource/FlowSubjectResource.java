package com.wacai.trike.mirana.resource;

import com.google.common.collect.Lists;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.InstanceContextService;
import com.wacai.trike.mirana.event.ESProcessingFactory;
import com.wacai.trike.mirana.event.ESProcessingFactory.Salad;
import com.wacai.trike.mirana.event.ESProcessingFactory.Type;
import com.wacai.trike.mirana.lifecycle.SubjectProvider.Metadata;
import com.wacai.trike.mirana.lifecycle.SubjectProvider.Request;
import com.wacai.trike.mirana.lifecycle.SubjectProvider.Subject;
import com.wacai.trike.mirana.service.FlowSubjectDTO;
import com.wacai.trike.mirana.service.FlowSubjectService;
import com.wacai.trike.mirana.util.ObjectMappers;
import com.wacai.trike.mirana.util.ObjectUtils;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping({"/flow-subject"})
public class FlowSubjectResource {
	private static final Logger log = LoggerFactory.getLogger(FlowSubjectResource.class);
	@Autowired
	private FlowSubjectService flowSubjectService;
	@Autowired
	private ESProcessingFactory esProcessingFactory;
	@Autowired
	private InstanceContextService instanceContextService;

	@GetMapping({"/subjects"})
	@ApiOperation("获取故事线下拉列表")
	public ApiResponse<List<Subject>> getSubjects() {
		return ApiResponse.success(this.flowSubjectService.subjects());
	}

	@GetMapping
	@ApiOperation("获取故事线已绑定列表")
	public ApiResponse<List<FlowSubjectModel>> getSubjects(
			@RequestParam(name = "flow", defaultValue = "") String flow) {
		List<FlowSubjectDTO> subjects = this.flowSubjectService.findByFlowCode(flow);
		return CollectionUtils.isEmpty(subjects)
				? ApiResponse.success(Lists.newArrayList())
				: ApiResponse.success(subjects.stream().map((s) -> {
					return (FlowSubjectModel) ObjectUtils.convert(s, FlowSubjectModel.class);
				}).collect(Collectors.toList()));
	}

	@PostMapping({"/bind"})
	@ApiOperation("流程绑定一个订阅项目")
	public ApiResponse<FlowSubjectModel> bind(@RequestBody FlowSubjectModel subject) {
		if (!Strings.isBlank(subject.getFlowCode()) && subject.getSubjectId() != null
				&& !Strings.isBlank(subject.getSubjectCode()) && !Strings.isBlank(subject.getSubjectName())) {
			FlowSubjectDTO bound = this.flowSubjectService
					.bind((FlowSubjectDTO) ObjectUtils.convert(subject, FlowSubjectDTO.class));
			return ApiResponse.success(ObjectUtils.convert(bound, FlowSubjectModel.class));
		} else {
			return ApiResponse.error("参数错误");
		}
	}

	@PostMapping({"/cancel"})
	@ApiOperation("流程解绑一个订阅项目")
	public ApiResponse<Boolean> cancel(@RequestBody FlowSubjectModel subject) {
		return !Strings.isBlank(subject.getFlowCode()) && !Strings.isBlank(subject.getSubjectCode())
				? ApiResponse.success(this.flowSubjectService
						.cancel((FlowSubjectDTO) ObjectUtils.convert(subject, FlowSubjectDTO.class)))
				: ApiResponse.error("参数错误");
	}

	@PostMapping({"/subscribe"})
	@ApiOperation("流程解绑一个订阅项目")
	public ApiResponse<Boolean> subscribe(@RequestBody Request request) {
		log.error("received {}", ObjectMappers.mustWriteValue(request));
		if (!Strings.isBlank(request.getBusinessCode()) && !Strings.isBlank(request.getAssociatedData())
				&& !Strings.isBlank(request.getSubscriptionCode())) {
			Metadata metadata = (Metadata) ObjectMappers.mustReadValue(request.getAssociatedData(), Metadata.class);
			metadata.setFlowCode(request.getBusinessCode());
			metadata.setSubscriptionCode(request.getSubscriptionCode());
			InstanceContext context = this.instanceContextService.load(metadata.getInstanceUUID());
			if (context == null) {
				return ApiResponse.error("当前数据未能找到相应的流程");
			} else {
				this.esProcessingFactory.process(new Salad(Type.UPDATE_SUBJECT, context, metadata));
				return ApiResponse.success(true);
			}
		} else {
			return ApiResponse.error("参数错误");
		}
	}
}