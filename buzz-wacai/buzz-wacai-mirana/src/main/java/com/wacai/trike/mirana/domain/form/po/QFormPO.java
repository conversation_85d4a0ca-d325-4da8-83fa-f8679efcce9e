/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 *  com.wacai.trike.mirana.api.constant.FormType
 */
package com.wacai.trike.mirana.domain.form.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.api.constant.FormType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.form.po.FormPO;
import java.time.LocalDateTime;

public class QFormPO
extends EntityPathBase<FormPO> {
    private static final long serialVersionUID = 1045359461L;
    public static final QFormPO formPO = new QFormPO("formPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final NumberPath<Long> actionId = this.createNumber("actionId", Long.class);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final StringPath field;
    public final StringPath fieldType;
    public final NumberPath<Long> id;
    public final NumberPath<Long> nodeId;
    public final EnumPath<FormType> type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final StringPath value;
    public final NumberPath<Long> variableConfigId;

    public QFormPO(String variable) {
        super(FormPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.field = this.createString("field");
        this.fieldType = this.createString("fieldType");
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.type = this.createEnum("type", FormType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.value = this.createString("value");
        this.variableConfigId = this.createNumber("variableConfigId", Long.class);
    }

    public QFormPO(Path<? extends FormPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.field = this.createString("field");
        this.fieldType = this.createString("fieldType");
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.type = this.createEnum("type", FormType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.value = this.createString("value");
        this.variableConfigId = this.createNumber("variableConfigId", Long.class);
    }

    public QFormPO(PathMetadata metadata) {
        super(FormPO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.field = this.createString("field");
        this.fieldType = this.createString("fieldType");
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.type = this.createEnum("type", FormType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.value = this.createString("value");
        this.variableConfigId = this.createNumber("variableConfigId", Long.class);
    }
}

