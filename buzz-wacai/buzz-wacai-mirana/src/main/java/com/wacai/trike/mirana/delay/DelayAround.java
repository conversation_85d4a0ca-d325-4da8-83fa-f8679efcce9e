/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.delay;

import com.wacai.trike.mirana.delay.Delay;
import com.wacai.trike.mirana.delay.DelayCalculator;
import com.wacai.trike.mirana.delay.SpecUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

public class DelayAround
implements Serializable,
DelayCalculator {
    private Delay before;
    private Delay after;

    @Override
    public LocalDateTime expectStart(LocalDateTime referDate) {
        LocalDateTime expectStart = LocalDateTime.now();
        if (Objects.isNull(this.before) || Objects.isNull(this.before.getUnit()) || Objects.isNull(this.before.getValue())) {
            return expectStart;
        }
        expectStart = expectStart.plus(this.before.getValue(), this.before.getUnit());
        expectStart = SpecUtil.setSpecWithMinNano(expectStart, this.before.getSpec());
        return expectStart;
    }

    @Override
    public LocalDateTime expectEnd(LocalDateTime referDate) {
        LocalDateTime expectEnd = LocalDateTime.now();
        if (Objects.isNull(this.after) || Objects.isNull(this.after.getUnit()) || Objects.isNull(this.after.getValue())) {
            return expectEnd;
        }
        expectEnd = expectEnd.plus(this.after.getValue(), this.after.getUnit());
        expectEnd = SpecUtil.setSpecWithMaxNano(expectEnd, this.after.getSpec());
        return expectEnd;
    }

    public Delay getBefore() {
        return this.before;
    }

    public Delay getAfter() {
        return this.after;
    }

    public void setBefore(Delay before) {
        this.before = before;
    }

    public void setAfter(Delay after) {
        this.after = after;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DelayAround)) {
            return false;
        }
        DelayAround other = (DelayAround)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Delay this$before = this.getBefore();
        Delay other$before = other.getBefore();
        if (this$before == null ? other$before != null : !(this$before).equals(other$before)) {
            return false;
        }
        Delay this$after = this.getAfter();
        Delay other$after = other.getAfter();
        return !(this$after == null ? other$after != null : !(this$after).equals(other$after));
    }

    protected boolean canEqual(Object other) {
        return other instanceof DelayAround;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Delay $before = this.getBefore();
        result = result * 59 + ($before == null ? 43 : ($before).hashCode());
        Delay $after = this.getAfter();
        result = result * 59 + ($after == null ? 43 : ($after).hashCode());
        return result;
    }

    public String toString() {
        return "DelayAround(before=" + this.getBefore() + ", after=" + this.getAfter() + ")";
    }
}

