/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.manage.po;

import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="action_template")
public class ActionTemplatePO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private String action;
    private String actionType;

    public String getAction() {
        return this.action;
    }

    public String getActionType() {
        return this.actionType;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    @Override
    public String toString() {
        return "ActionTemplatePO(action=" + this.getAction() + ", actionType=" + this.getActionType() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ActionTemplatePO)) {
            return false;
        }
        ActionTemplatePO other = (ActionTemplatePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$action = this.getAction();
        String other$action = other.getAction();
        if (this$action == null ? other$action != null : !this$action.equals(other$action)) {
            return false;
        }
        String this$actionType = this.getActionType();
        String other$actionType = other.getActionType();
        return !(this$actionType == null ? other$actionType != null : !this$actionType.equals(other$actionType));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof ActionTemplatePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $action = this.getAction();
        result = result * 59 + ($action == null ? 43 : $action.hashCode());
        String $actionType = this.getActionType();
        result = result * 59 + ($actionType == null ? 43 : $actionType.hashCode());
        return result;
    }
}

