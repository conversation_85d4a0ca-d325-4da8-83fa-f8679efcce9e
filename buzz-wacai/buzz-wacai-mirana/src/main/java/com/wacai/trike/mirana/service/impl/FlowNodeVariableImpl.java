/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.cache.Cache
 *  com.google.common.cache.CacheBuilder
 *  com.google.common.collect.Lists
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  javax.validation.constraints.NotNull
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.service.impl;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.trike.mirana.domain.flow.po.FlowNodeVariablePO;
import com.wacai.trike.mirana.domain.flow.po.QFlowNodeVariablePO;
import com.wacai.trike.mirana.domain.flow.repository.FlowNodeVariableRepository;
import com.wacai.trike.mirana.service.FlowNodeVariableDTO;
import com.wacai.trike.mirana.service.FlowNodeVariableRequest;
import com.wacai.trike.mirana.service.FlowNodeVariableService;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class FlowNodeVariableImpl implements FlowNodeVariableService {
	private static final Logger log = LoggerFactory.getLogger(FlowNodeVariableImpl.class);
	private static final String JOINER_KEY = "/";
	@Autowired
	private FlowNodeVariableRepository flowNodeVariableRepository;
	@Autowired
	private JPAQueryFactory jpaQueryFactory;
	private final Cache<Long, FlowNodeVariableDTO> ID_CACHE = CacheBuilder.newBuilder()
			.expireAfterAccess(30L, TimeUnit.MINUTES).initialCapacity(30).maximumSize(300L).recordStats().build();
	private final Cache<String, List<FlowNodeVariableDTO>> NODE_SUB_FLOW_CACHE = CacheBuilder.newBuilder()
			.expireAfterAccess(30L, TimeUnit.MINUTES).initialCapacity(10).maximumSize(300L).recordStats().build();

	@Override
	public FlowNodeVariableDTO save(FlowNodeVariableDTO dto) {
		FlowNodeVariableDTO variableDTO = ((FlowNodeVariablePO) this.flowNodeVariableRepository.save(dto.toPo()))
				.toDto();
		this.ID_CACHE.put(variableDTO.getId(), variableDTO);
		this.NODE_SUB_FLOW_CACHE.invalidate(this.cacheKey(variableDTO.getNodeId(), variableDTO.getSubFlowId()));
		return variableDTO;
	}

	@Override
	public int delete(Long id) {
		FlowNodeVariablePO variablePO = this.flowNodeVariableRepository.findById(id).orElse(null);
		if (variablePO == null) {
			return 0;
		}
		variablePO.setActive(false);
		this.flowNodeVariableRepository.save(variablePO);
		this.ID_CACHE.invalidate(id);
		this.NODE_SUB_FLOW_CACHE.invalidate(this.cacheKey(variablePO.getNodeId(), variablePO.getSubFlowId()));
		return 1;
	}

	@Override
	public int deleteByNodeId(Long nodeId) {
		if (nodeId <= 0L) {
			return 0;
		}
		List<FlowNodeVariableDTO> results = this.query(new FlowNodeVariableRequest().addNodeId(nodeId));
		if (CollectionUtils.isEmpty(results)) {
			return 0;
		}
		results.forEach(dto -> this.delete(dto.getId()));
		return results.size();
	}

	@Override
	public FlowNodeVariableDTO query(Long id) {
		FlowNodeVariableDTO cached = (FlowNodeVariableDTO) this.ID_CACHE.getIfPresent(id);
		if (cached != null) {
			return cached;
		}
		FlowNodeVariablePO variablePO = this.flowNodeVariableRepository.findById(id).orElse(null);
		if (variablePO == null) {
			return null;
		}
		FlowNodeVariableDTO variableDTO = variablePO.toDto();
		this.ID_CACHE.put(id, variableDTO);
		return variableDTO;
	}

	@Override
	public FlowNodeVariableDTO query(Long nodeId, Long subFlowId, Long parentFlowVariableId, Long subFlowVariableId) {
		List<FlowNodeVariableDTO> results = this
				.query(new FlowNodeVariableRequest().setNodeIds(Lists.newArrayList(new Long[] { nodeId }))
						.setSubFlowIds(Lists.newArrayList(new Long[] { subFlowId }))
						.setParentFlowVariableIds(Lists.newArrayList(new Long[] { parentFlowVariableId }))
						.setSubFlowVariableIds(Lists.newArrayList(new Long[] { subFlowVariableId })));
		return CollectionUtils.isEmpty(results) ? null : results.get(0);
	}

	@Override
	public List<FlowNodeVariableDTO> query(FlowNodeVariableRequest request) {
		BooleanExpression expression = QFlowNodeVariablePO.flowNodeVariablePO.active.isTrue();
		if (!CollectionUtils.isEmpty(request.getNodeIds())) {
			expression = expression
					.and((Predicate) QFlowNodeVariablePO.flowNodeVariablePO.nodeId.in(request.getNodeIds()));
		}
		if (!CollectionUtils.isEmpty(request.getSubFlowIds())) {
			expression = expression
					.and((Predicate) QFlowNodeVariablePO.flowNodeVariablePO.subFlowId.in(request.getSubFlowIds()));
		}
		if (!CollectionUtils.isEmpty(request.getParentFlowVariableIds())) {
			expression = expression.and((Predicate) QFlowNodeVariablePO.flowNodeVariablePO.parentFlowVariableId
					.in(request.getParentFlowVariableIds()));
		}
		if (!CollectionUtils.isEmpty(request.getSubFlowVariableIds())) {
			expression = expression.and((Predicate) QFlowNodeVariablePO.flowNodeVariablePO.subFlowVariableId
					.in(request.getSubFlowVariableIds()));
		}
		List<FlowNodeVariableDTO> results = (this.jpaQueryFactory
				.selectFrom(QFlowNodeVariablePO.flowNodeVariablePO).where(expression)).fetch()
						.stream().map(FlowNodeVariablePO::toDto).collect(Collectors.toList());
		results.forEach(r -> this.ID_CACHE.put(r.getId(), r));
		return results;
	}

	@Override
	public List<FlowNodeVariableDTO> query(Long nodeId, Long subFlowId) {
		String key = this.cacheKey(nodeId, subFlowId);
		List cached = (List) this.NODE_SUB_FLOW_CACHE.getIfPresent(key);
		if (!CollectionUtils.isEmpty((Collection) cached)) {
			log.info("NODE_SUB_FLOW_CACHE cached hit {}", this.NODE_SUB_FLOW_CACHE.stats().hitRate());
			return cached;
		}
		List<FlowNodeVariableDTO> variables = this
				.query(new FlowNodeVariableRequest().addNodeId(nodeId).addSubFlowId(subFlowId));
		if (!CollectionUtils.isEmpty(variables)) {
			this.NODE_SUB_FLOW_CACHE.put(key, variables);
		}
		return variables;
	}

	private String cacheKey(@NotNull Long nodeId, @NotNull Long subFlowId) {
		return nodeId + JOINER_KEY + subFlowId;
	}
}
