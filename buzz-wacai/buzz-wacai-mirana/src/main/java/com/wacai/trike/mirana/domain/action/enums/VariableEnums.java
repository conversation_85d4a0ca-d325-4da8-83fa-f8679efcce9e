package com.wacai.trike.mirana.domain.action.enums;

public interface VariableEnums {
	public enum DataType {
		STRING("字符型"), NUMBER("数值型"), BOOLEAN("浮点型"), DATE("日期型");

		private String desc;

		DataType(String desc) {
			this.desc = desc;
		}

		public String getDesc() {
			return this.desc;
		}
	}

	public enum SourceType {
		MANUAL_INPUT("手动输入"), REMOTE("远程获取"), DEFINITION("自定义");

		private String desc;

		SourceType(String desc) {
			this.desc = desc;
		}

		public String getDesc() {
			return this.desc;
		}
	}
}