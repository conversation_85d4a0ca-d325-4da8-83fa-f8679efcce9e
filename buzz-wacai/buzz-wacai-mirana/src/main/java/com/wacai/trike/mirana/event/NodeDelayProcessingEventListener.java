/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.DerailmentHandler;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.EventListener;
import com.wacai.trike.mirana.event.NodeDelayProcessingEvent;
import com.wacai.trike.mirana.event.NodeExecutor;
import com.wacai.trike.mirana.event.NodeProcessedEvent;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class NodeDelayProcessingEventListener implements EventListener<NodeDelayProcessingEvent, NodeProcessedEvent> {
	private static final Logger log = LogManager.getLogger(NodeDelayProcessingEventListener.class);
	@Autowired
	private NodeExecutor nodeExecutor;
	@Autowired
	private FlowGraphService graphService;

	@Override
	public Class<NodeDelayProcessingEvent> accept() {
		return NodeDelayProcessingEvent.class;
	}

	@Override
	@Barrier(handlers = { DerailmentHandler.class, InstanceContextSyncHandler.class })
	public void doProcess(EventExchange<NodeDelayProcessingEvent, NodeProcessedEvent> exchange) {
		InstanceContext context = exchange.getContext();
		Node node = this.graphService.getFlowGraph(context.getFlowId()).getNode(context.getCurrentNodeId());
		this.continueExecute(exchange, node);
	}

	public int getOrder() {
		return 7;
	}

	private void continueExecute(EventExchange exchange, Node node) {
		AbstractFlowEvent next = this.nodeExecutor.doProcess((AbstractFlowEvent) exchange.getCurrent(),
				exchange.getContext(), node);
		if (next == null) {
			return;
		}
		exchange.setNext(next);
	}
}
