/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.model.FlowBaseRequest;
import java.util.List;

public class FlowStartRequest
extends FlowBaseRequest {
    private String bzKey;
    private String uuid;
    private boolean story;
    private boolean asyncStart = false;
    private List<String> birthmarks;

    public String getBzKey() {
        return this.bzKey;
    }

    public String getUuid() {
        return this.uuid;
    }

    public boolean isStory() {
        return this.story;
    }

    public boolean isAsyncStart() {
        return this.asyncStart;
    }

    public List<String> getBirthmarks() {
        return this.birthmarks;
    }

    public FlowStartRequest setBzKey(String bzKey) {
        this.bzKey = bzKey;
        return this;
    }

    public FlowStartRequest setUuid(String uuid) {
        this.uuid = uuid;
        return this;
    }

    public FlowStartRequest setStory(boolean story) {
        this.story = story;
        return this;
    }

    public FlowStartRequest setAsyncStart(boolean asyncStart) {
        this.asyncStart = asyncStart;
        return this;
    }

    public FlowStartRequest setBirthmarks(List<String> birthmarks) {
        this.birthmarks = birthmarks;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowStartRequest)) {
            return false;
        }
        FlowStartRequest other = (FlowStartRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$bzKey = this.getBzKey();
        String other$bzKey = other.getBzKey();
        if (this$bzKey == null ? other$bzKey != null : !this$bzKey.equals(other$bzKey)) {
            return false;
        }
        String this$uuid = this.getUuid();
        String other$uuid = other.getUuid();
        if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
            return false;
        }
        if (this.isStory() != other.isStory()) {
            return false;
        }
        if (this.isAsyncStart() != other.isAsyncStart()) {
            return false;
        }
        List<String> this$birthmarks = this.getBirthmarks();
        List<String> other$birthmarks = other.getBirthmarks();
        return !(this$birthmarks == null ? other$birthmarks != null : !(this$birthmarks).equals(other$birthmarks));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowStartRequest;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $bzKey = this.getBzKey();
        result = result * 59 + ($bzKey == null ? 43 : $bzKey.hashCode());
        String $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        result = result * 59 + (this.isStory() ? 79 : 97);
        result = result * 59 + (this.isAsyncStart() ? 79 : 97);
        List<String> $birthmarks = this.getBirthmarks();
        result = result * 59 + ($birthmarks == null ? 43 : ($birthmarks).hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "FlowStartRequest(bzKey=" + this.getBzKey() + ", uuid=" + this.getUuid() + ", story=" + this.isStory() + ", asyncStart=" + this.isAsyncStart() + ", birthmarks=" + this.getBirthmarks() + ")";
    }
}

