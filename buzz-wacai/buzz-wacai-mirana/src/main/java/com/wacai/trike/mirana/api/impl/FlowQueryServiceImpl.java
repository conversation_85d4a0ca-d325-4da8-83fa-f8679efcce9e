/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.Order
 *  com.querydsl.core.types.OrderSpecifier
 *  com.querydsl.core.types.OrderSpecifier$NullHandling
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.Projections
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.JPQLQuery
 *  com.querydsl.jpa.JPQLQueryFactory
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.ComboBox
 *  com.wacai.loan.trike.common.model.Page
 *  com.wacai.trike.mirana.api.FlowQueryService
 *  com.wacai.trike.mirana.api.constant.FlowType
 *  com.wacai.trike.mirana.api.model.FlowModel
 *  com.wacai.trike.mirana.api.model.FlowQueryRequest
 *  com.wacai.trike.mirana.api.model.FlowResponse
 *  com.wacai.trike.mirana.api.model.InstanceDetail
 *  com.wacai.trike.mirana.api.model.InstanceDetailRequest
 *  com.wacai.trike.mirana.api.model.InstanceQueryRequest
 *  com.wacai.trike.mirana.api.model.InstanceQueryResponse
 *  com.wacai.trike.mirana.api.model.NodeQueryRequest
 *  com.wacai.trike.mirana.api.model.TaskInstanceDetail
 *  javax.validation.constraints.NotNull
 *  org.apache.commons.lang3.StringUtils
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.validation.annotation.Validated
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import javax.validation.constraints.NotNull;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.dubbo.config.annotation.Service;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Order;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.JPQLQueryFactory;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.api.FlowQueryService;
import com.wacai.trike.mirana.api.constant.FlowType;
import com.wacai.trike.mirana.api.model.FlowModel;
import com.wacai.trike.mirana.api.model.FlowQueryRequest;
import com.wacai.trike.mirana.api.model.InstanceDetail;
import com.wacai.trike.mirana.api.model.InstanceDetailRequest;
import com.wacai.trike.mirana.api.model.InstanceQueryRequest;
import com.wacai.trike.mirana.api.model.InstanceQueryResponse;
import com.wacai.trike.mirana.api.model.NodeQueryRequest;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.context.po.QInstanceVariablePO;
import com.wacai.trike.mirana.domain.context.po.QTaskInstancePO;
import com.wacai.trike.mirana.domain.context.po.TaskInstancePO;
import com.wacai.trike.mirana.domain.flow.po.QFlowPO;
import com.wacai.trike.mirana.domain.instance.po.QInstancePO;
import com.wacai.trike.mirana.domain.node.po.QNodePO;

@RestController
@Service(interfaceClass = FlowQueryService.class)
public class FlowQueryServiceImpl
		implements FlowQueryService {
	private static final Logger log = LoggerFactory.getLogger(FlowQueryServiceImpl.class);
	private JPQLQueryFactory queryFactory;

	@Autowired
	public FlowQueryServiceImpl(JPQLQueryFactory queryFactory) {
		this.queryFactory = queryFactory;
	}

	public ApiResponse<List<ComboBox<Long>>> listNode(@NotNull @Validated NodeQueryRequest request) {
		try {
//			BooleanExpression where = this.buildCondition(request);
//			List result = this.queryFactory
//					.select(new Expression[] { QNodePO.nodePO.id, QNodePO.nodePO.name })
//					.from(new EntityPath[] { QBusinessPO.businessPO }).join((EntityPath) QApplicationPO.applicationPO)
//					.on(new Predicate[] { QBusinessPO.businessPO.id.eq(QApplicationPO.applicationPO.buId) })
//					.join((EntityPath) QFlowPO.flowPO)
//					.on(new Predicate[] { QFlowPO.flowPO.appId.eq(QApplicationPO.applicationPO.id) })
//					.join((EntityPath) QNodePO.nodePO)
//					.on(new Predicate[] { QNodePO.nodePO.flowId.eq(QFlowPO.flowPO.id) })
//					.where(new Predicate[] { where })).fetch().stream()
//							.map(n -> ComboBox.of((String) ((String) n.get((Expression) QNodePO.nodePO.name)),
//									 n.get(QNodePO.nodePO.id)))
//							.collect(Collectors.toList());
//			return ApiResponse.success(result);
			return null;
		} catch (Exception e) {
			log.error("fetch node list failed, param : {}",  request,  e);
			return ApiResponse.error((String) "fetch node list failed");
		}
	}

	private BooleanExpression buildCondition(NodeQueryRequest request) {
		return QBusinessPO.businessPO.code.eq( request.getBu())
				.and((Predicate) QApplicationPO.applicationPO.code.eq( request.getApp()))
				.and((Predicate) QFlowPO.flowPO.code.eq( request.getFlow()));
	}

	public ApiResponse<Page<InstanceQueryResponse>> queryInstance(InstanceQueryRequest request) {
		try {
			BooleanExpression where = this.buildCondition((NodeQueryRequest) request);
			JPQLQuery query = this.buildQuery();
			if (!CollectionUtils.isEmpty((Collection) request.getBzKeys())) {
				where = where.and((Predicate) QInstancePO.instancePO.bzKey.in((Collection) request.getBzKeys()));
			}
			if (!CollectionUtils.isEmpty((Collection) request.getInstanceIds())) {
				where = where.and((Predicate) QInstancePO.instancePO.uuid.in((Collection) request.getInstanceIds()));
			}
			if (!CollectionUtils.isEmpty((Collection) request.getStatuses())) {
				where = where.and((Predicate) QInstancePO.instancePO.status.in((Collection) request.getStatuses()));
			}
			if (!CollectionUtils.isEmpty((Collection) request.getNodeIds())) {
				where = where
						.and((Predicate) QInstancePO.instancePO.currentNodeId.in((Collection) request.getNodeIds()));
			}
			if (!CollectionUtils.isEmpty((Collection) request.getResources())) {
				where = where.and((Predicate) QNodePO.nodePO.code.in((Collection) request.getResources()));
			}
			if (!CollectionUtils.isEmpty((Map) request.getVariables())) {
				BooleanExpression or = null;
				for (Map.Entry entry : request.getVariables().entrySet()) {
					String k = (String) entry.getKey();
					String v = (String) entry.getValue();
					BooleanExpression tmpCondition = QInstanceVariablePO.instanceVariablePO.field.eq( k)
							.and((Predicate) QInstanceVariablePO.instanceVariablePO.value.eq( v));
					or = Objects.isNull(or) ? tmpCondition : or.or((Predicate) tmpCondition);
				}
				where = where.and(or);
				query = query.join((EntityPath) QInstanceVariablePO.instanceVariablePO)
						.on(new Predicate[] { QInstanceVariablePO.instanceVariablePO.instanceUuid
								.endsWith((Expression) QInstancePO.instancePO.uuid) });
			}
			if (StringUtils.isNotBlank((CharSequence) request.getOperator())) {
				where = where
						.and((Predicate) QTaskInstancePO.taskInstancePO.updatedBy.eq( request.getOperator()));
				query = query.join((EntityPath) QTaskInstancePO.taskInstancePO).on(new Predicate[] {
						QTaskInstancePO.taskInstancePO.instanceUuid.eq((Expression) QInstancePO.instancePO.uuid) });
			}
			query = (JPQLQuery) query.where(new Predicate[] { where });
			if (StringUtils.isNotBlank((CharSequence) request.getOrderBy())) {
				Order order = Optional.ofNullable(request.getOrder()).map(String::toUpperCase).map(Order::valueOf)
						.orElse(Order.DESC);
				OrderSpecifier specifier = new OrderSpecifier(order, QInstancePO.instancePO.createdTime,
						OrderSpecifier.NullHandling.NullsLast);
				query = (JPQLQuery) query.orderBy(new OrderSpecifier[] { specifier });
			}
			long count = query.fetchCount();
			query = (JPQLQuery) ((JPQLQuery) query.offset((request.getPageIndex() - 1L) * request.getPageSize()))
					.limit(request.getPageSize());
			List result = query.fetch();
			this.fillParams(result);
			Page page = new Page().setPage((count - 1L) / request.getPageSize() + 1L).setTotal(count).setData(result);
			return ApiResponse.success( page);
		} catch (Exception e) {
			log.error("query instance failed, param : {}",  request,  e);
			return ApiResponse.error((String) "query instance failed");
		}
	}

	private JPQLQuery<InstanceQueryResponse> buildQuery() {
		return this.queryFactory
				.select((Expression) Projections.fields(InstanceQueryResponse.class,
						(Expression[]) new Expression[] { QInstancePO.instancePO.uuid.as("instanceId"),
								QInstancePO.instancePO.status, QInstancePO.instancePO.currentNodeId,
								QNodePO.nodePO.name.as("currentNodeName"), QNodePO.nodePO.code.as("currentNodeCode") }))
				.from(new EntityPath[] { QBusinessPO.businessPO }).join((EntityPath) QApplicationPO.applicationPO)
				.on(new Predicate[] { QBusinessPO.businessPO.id.eq(QApplicationPO.applicationPO.buId) })
				.join((EntityPath) QFlowPO.flowPO)
				.on(new Predicate[] { QFlowPO.flowPO.appId.eq(QApplicationPO.applicationPO.id) })
				.join((EntityPath) QInstancePO.instancePO)
				.on(new Predicate[] { QFlowPO.flowPO.id.eq(QInstancePO.instancePO.flowId) })
				.join((EntityPath) QNodePO.nodePO)
				.on(new Predicate[] { QNodePO.nodePO.id.eq(QInstancePO.instancePO.currentNodeId) });
	}

	private void fillParams(List<InstanceQueryResponse> result) {
		this.loanVariable(result);
		this.fillCurrentTaskInstance(result);
	}

	private void loanVariable(List<InstanceQueryResponse> result) {
//		Map responseMap = result.stream().collect(Collectors.toMap(FlowResponse::getInstanceId, Function.identity()));
//		((JPQLQuery) ((JPQLQuery) this.queryFactory.selectFrom((EntityPath) QInstanceVariablePO.instanceVariablePO)
//				.where(new Predicate[] {
//						QInstanceVariablePO.instanceVariablePO.instanceUuid.in(responseMap.keySet()) }))
//								.orderBy(new OrderSpecifier[] {
//										new OrderSpecifier(Order.ASC, QInstanceVariablePO.instanceVariablePO.id) }))
//												.fetch()
//												.forEach(v -> ((InstanceQueryResponse) responseMap
//														.get(v.getInstanceUuid())).getVariables().put(v.getField(),
//																v.getValue()));
	}

	private void fillCurrentTaskInstance(List<InstanceQueryResponse> result) {
		for (InstanceQueryResponse item : result) {
			String instanceId = item.getInstanceId();
			TaskInstancePO taskInstance = (TaskInstancePO) ((JPQLQuery) ((JPQLQuery) this.queryFactory
					.selectFrom((EntityPath) QTaskInstancePO.taskInstancePO)
					.where(new Predicate[] { QTaskInstancePO.taskInstancePO.instanceUuid.eq( instanceId) }))
							.orderBy(new OrderSpecifier[] {
									new OrderSpecifier(Order.DESC, QTaskInstancePO.taskInstancePO.id) })).fetchFirst();
			if (taskInstance == null)
				continue;
			item.setCurrentTaskInstanceId(taskInstance.getUuid());
		}
	}

	public ApiResponse<InstanceDetail> detail(@NotNull @Validated InstanceDetailRequest request) {
		try {
//			InstancePO instance = (InstancePO) ((JPQLQuery) this.queryFactory
//					.selectFrom((EntityPath) QInstancePO.instancePO)
//					.where(new Predicate[] { QInstancePO.instancePO.uuid.eq( request.getInstanceId()) }))
//							.fetchOne();
//			Map detailMap = ((JPQLQuery) this.queryFactory
//					.select((Expression) Projections.fields(TaskInstanceDetail.class,
//							(Expression[]) new Expression[] { QTaskInstancePO.taskInstancePO.id,
//									QTaskInstancePO.taskInstancePO.uuid, QNodePO.nodePO.name.as("taskName"),
//									QTaskInstancePO.taskInstancePO.updatedBy.as("operator"),
//									QTaskInstancePO.taskInstancePO.updatedTime.as("operateTime") }))
//					.from(new EntityPath[] { QTaskInstancePO.taskInstancePO })
//					.where(new Predicate[] {
//							QTaskInstancePO.taskInstancePO.instanceUuid.eq( request.getInstanceId()) })).fetch()
//									.stream()
//									.collect(Collectors.toMap(TaskInstanceDetail::getUuid, Function.identity()));
//			((JPQLQuery) this.queryFactory.selectFrom((EntityPath) QInstanceVariablePO.instanceVariablePO)
//					.where(new Predicate[] { QInstanceVariablePO.instanceVariablePO.taskUuid.in(detailMap.keySet()) }))
//							.fetch().forEach(v -> ((TaskInstanceDetail) detailMap.get(v.getTaskUuid())).getVariables()
//									.put(v.getField(), v.getValue()));
//			InstanceDetail detail = (InstanceDetail) new InstanceDetail()
//					.setTaskInstanceDetails(new ArrayList(detailMap.values())).setInstanceId(instance.getUuid())
//					.setStatus(instance.getStatus());
//			return ApiResponse.success( detail)
			return null;
		} catch (Exception e) {
			log.error("fetch instance detail failed, param : {}",  request,  e);
			return ApiResponse.error((String) "fetch instance detail failed");
		}
	}

	public ApiResponse<List<FlowModel>> list(FlowQueryRequest request) {
		try {
//			List result = ((JPQLQuery) this.queryFactory
//					.select(new Expression[] { QFlowPO.flowPO.code, QFlowPO.flowPO.name, QFlowPO.flowPO.type })
//					.from(new EntityPath[] { QBusinessPO.businessPO }).join((EntityPath) QApplicationPO.applicationPO)
//					.on(new Predicate[] { QBusinessPO.businessPO.id.eq(QApplicationPO.applicationPO.buId) })
//					.join((EntityPath) QFlowPO.flowPO)
//					.on(new Predicate[] { QFlowPO.flowPO.appId.eq(QApplicationPO.applicationPO.id) })
//					.where(new Predicate[] { this.buildWhereConditions(request) })).fetch().stream().map(n -> {
//						FlowModel flowModel = new FlowModel();
//						flowModel.setCode((String) n.get((Expression) QFlowPO.flowPO.code));
//						flowModel.setName((String) n.get((Expression) QFlowPO.flowPO.name));
//						flowModel.setType(((FlowType) n.get(QFlowPO.flowPO.type)).name());
//						return flowModel;
//					}).collect(Collectors.toList());
//			return ApiResponse.success(result);
			return null;
		} catch (Exception e) {
			log.error("fetch node list failed, param : {}",  request,  e);
			return ApiResponse.error((String) "fetch node list failed");
		}
	}

	private BooleanExpression buildWhereConditions(FlowQueryRequest request) {
		BooleanExpression conditions = QBusinessPO.businessPO.code.eq( request.getBu())
				.and((Predicate) QApplicationPO.applicationPO.code.eq( request.getApp()));
		if (StringUtils.isNotBlank((CharSequence) request.getType())) {
			conditions = conditions
					.and((Predicate) QFlowPO.flowPO.type.eq( FlowType.valueOf((String) request.getType())));
		}
		if (StringUtils.isNotBlank((CharSequence) request.getCode())) {
			conditions = conditions.and((Predicate) QFlowPO.flowPO.code.eq( request.getCode()));
		}
		return conditions;
	}
}
