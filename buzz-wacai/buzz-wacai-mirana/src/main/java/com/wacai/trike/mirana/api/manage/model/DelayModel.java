/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class DelayModel
implements Serializable {
    private boolean flag = false;
    private String type;
    private String unit;
    private String value;
    private String spec;
    private Integer in;
    private Integer out;

    public boolean updateFrom(DelayModel other) {
        if (this == other) {
            return false;
        }
        if (other == null) {
            return this.flag;
        }
        if (this.flag != other.flag) {
            return true;
        }
        if (this.type != null && !this.type.equals(other.type) || other.type != null && !other.type.equals(this.type)) {
            return true;
        }
        if (this.unit != null && !this.unit.equals(other.unit) || other.unit != null && !other.unit.equals(this.unit)) {
            return true;
        }
        if (this.value != null && !this.value.equals(other.value) || other.value != null && !other.value.equals(this.value)) {
            return true;
        }
        if (this.spec != null && !this.spec.equals(other.spec) || other.spec != null && !other.spec.equals(this.spec)) {
            return true;
        }
        if (this.in != null && !this.in.equals(other.in) || other.in != null && !other.in.equals(this.in)) {
            return true;
        }
        return this.out != null && !this.out.equals(other.out) || other.out != null && !other.out.equals(this.out);
    }

    public boolean isFlag() {
        return this.flag;
    }

    public String getType() {
        return this.type;
    }

    public String getUnit() {
        return this.unit;
    }

    public String getValue() {
        return this.value;
    }

    public String getSpec() {
        return this.spec;
    }

    public Integer getIn() {
        return this.in;
    }

    public Integer getOut() {
        return this.out;
    }

    public void setFlag(boolean flag) {
        this.flag = flag;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public void setIn(Integer in) {
        this.in = in;
    }

    public void setOut(Integer out) {
        this.out = out;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DelayModel)) {
            return false;
        }
        DelayModel other = (DelayModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (this.isFlag() != other.isFlag()) {
            return false;
        }
        String this$type = this.getType();
        String other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$unit = this.getUnit();
        String other$unit = other.getUnit();
        if (this$unit == null ? other$unit != null : !this$unit.equals(other$unit)) {
            return false;
        }
        String this$value = this.getValue();
        String other$value = other.getValue();
        if (this$value == null ? other$value != null : !this$value.equals(other$value)) {
            return false;
        }
        String this$spec = this.getSpec();
        String other$spec = other.getSpec();
        if (this$spec == null ? other$spec != null : !this$spec.equals(other$spec)) {
            return false;
        }
        Integer this$in = this.getIn();
        Integer other$in = other.getIn();
        if (this$in == null ? other$in != null : !(this$in).equals(other$in)) {
            return false;
        }
        Integer this$out = this.getOut();
        Integer other$out = other.getOut();
        return !(this$out == null ? other$out != null : !(this$out).equals(other$out));
    }

    protected boolean canEqual(Object other) {
        return other instanceof DelayModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        result = result * 59 + (this.isFlag() ? 79 : 97);
        String $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $unit = this.getUnit();
        result = result * 59 + ($unit == null ? 43 : $unit.hashCode());
        String $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : $value.hashCode());
        String $spec = this.getSpec();
        result = result * 59 + ($spec == null ? 43 : $spec.hashCode());
        Integer $in = this.getIn();
        result = result * 59 + ($in == null ? 43 : ($in).hashCode());
        Integer $out = this.getOut();
        result = result * 59 + ($out == null ? 43 : ($out).hashCode());
        return result;
    }

    public String toString() {
        return "DelayModel(flag=" + this.isFlag() + ", type=" + this.getType() + ", unit=" + this.getUnit() + ", value=" + this.getValue() + ", spec=" + this.getSpec() + ", in=" + this.getIn() + ", out=" + this.getOut() + ")";
    }
}

