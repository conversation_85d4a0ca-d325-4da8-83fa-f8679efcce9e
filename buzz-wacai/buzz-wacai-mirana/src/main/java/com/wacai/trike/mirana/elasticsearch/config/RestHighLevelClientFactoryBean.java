/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.apache.http.Header
 *  org.apache.http.HttpHost
 *  org.apache.http.client.config.RequestConfig$Builder
 *  org.apache.http.message.BasicHeader
 *  org.elasticsearch.client.RestClient
 *  org.elasticsearch.client.RestClientBuilder
 *  org.elasticsearch.client.RestClientBuilder$RequestConfigCallback
 *  org.elasticsearch.client.RestHighLevelClient
 *  org.springframework.beans.factory.FactoryBean
 *  org.springframework.beans.factory.annotation.Value
 */
package com.wacai.trike.mirana.elasticsearch.config;

import java.util.Arrays;
import org.apache.http.Header;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.message.BasicHeader;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestClientBuilder;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.annotation.Value;

public class RestHighLevelClientFactoryBean
implements FactoryBean<RestHighLevelClient> {
    @Value(value="${elasticsearch.gateway.url}")
    private String esUrl;
    @Value(value="${elasticsearch.gateway.token}")
    private String token;
    private static final int MAX_RETRY_TIMEOUT_MILLIS = 60000;
    private static final int CONNECT_TIMEOUT_MILLIS = 5000;
    private static final int SOCKET_TIMEOUT_MILLIS = 40000;
    private static final int CONNECTION_REQUEST_TIMEOUT_MILLIS = 1000;

    public RestHighLevelClient getObject() {
        HttpHost[] httpHosts = (HttpHost[])Arrays.stream(this.esUrl.split(",")).map(HttpHost::create).toArray(HttpHost[]::new);
        Header[] headers = new Header[]{new BasicHeader("token", this.token)};
        RestClientBuilder builder = RestClient.builder((HttpHost[])httpHosts).setDefaultHeaders(headers);
        builder.setMaxRetryTimeoutMillis(60000);
        builder.setRequestConfigCallback(new RestClientBuilder.RequestConfigCallback(){

            public RequestConfig.Builder customizeRequestConfig(RequestConfig.Builder requestConfigBuilder) {
                requestConfigBuilder.setConnectTimeout(5000);
                requestConfigBuilder.setSocketTimeout(40000);
                requestConfigBuilder.setConnectionRequestTimeout(1000);
                return requestConfigBuilder;
            }
        });
        return new RestHighLevelClient(builder.build());
    }

    public Class<?> getObjectType() {
        return RestHighLevelClient.class;
    }
}

