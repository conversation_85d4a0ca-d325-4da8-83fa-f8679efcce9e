/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.pt.redalert.api.common.BusinessUnit
 *  com.wacai.pt.redalert.api.utils.RedAlertUtils
 */
package com.wacai.trike.mirana.util;

import com.wacai.pt.redalert.api.common.BusinessUnit;
import com.wacai.pt.redalert.api.utils.RedAlertUtils;

public final class MetricsUtil {
    public static RedAlertUtils getFlowUtil() {
        return RedAlertUtils.build((BusinessUnit)BusinessUnit.loan, (String)"mirana").addDatabase("com.wacai.trike.mirana").addMeasurement("flow");
    }

    public static RedAlertUtils getNodeUtil() {
        return RedAlertUtils.build((BusinessUnit)BusinessUnit.loan, (String)"mirana").addDatabase("com.wacai.trike.mirana").addMeasurement("node");
    }

    public static RedAlertUtils getDelayedEventUtil() {
        return RedAlertUtils.build((BusinessUnit)BusinessUnit.loan, (String)"mirana").addDatabase("com.wacai.trike.mirana").addMeasurement("delayed_event");
    }
}

