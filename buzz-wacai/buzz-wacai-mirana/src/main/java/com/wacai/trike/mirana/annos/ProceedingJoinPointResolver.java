/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.aspectj.lang.ProceedingJoinPoint
 *  org.aspectj.lang.Signature
 *  org.aspectj.lang.reflect.MethodSignature
 */
package com.wacai.trike.mirana.annos;

import java.lang.reflect.Method;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.reflect.MethodSignature;

public class ProceedingJoinPointResolver {
    private static final ConcurrentMap<Method, BarrierAnnotationMeta> ANNOTATION_META_CACHE = new ConcurrentHashMap<Method, BarrierAnnotationMeta>();

    public static BarrierAnnotationMeta resolveBarrieAnnotation(ProceedingJoinPoint pjp) {
        Signature signature = pjp.getSignature();
        if (signature instanceof MethodSignature) {
            MethodSignature methodSignature = (MethodSignature)signature;
            Method method = methodSignature.getMethod();
            BarrierAnnotationMeta meta = (BarrierAnnotationMeta)ANNOTATION_META_CACHE.get(method);
            if (meta == null) {
                ANNOTATION_META_CACHE.putIfAbsent(method, ProceedingJoinPointResolver.create(method));
                meta = (BarrierAnnotationMeta)ANNOTATION_META_CACHE.get(method);
            }
            return meta;
        }
        throw new RuntimeException("Not method");
    }

    private static BarrierAnnotationMeta create(Method method) {
        String className = method.getDeclaringClass().getCanonicalName();
        String methodName = method.getName();
        String methodIndexName = className + "." + methodName;
        Barrier barrier = method.getAnnotation(Barrier.class);
        if (barrier == null) {
            barrier = method.getDeclaringClass().getAnnotation(Barrier.class);
            Class<? extends MethodInvocationHandler>[] classArray = barrier.handlers();
        }
        BarrierAnnotationMeta meta = new BarrierAnnotationMeta();
        meta.setBarrierAnnotation(barrier);
        meta.setMethodIndexName(methodIndexName);
        meta.setMethod(method);
        return meta;
    }
}

