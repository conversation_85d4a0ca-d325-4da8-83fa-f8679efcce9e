/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.expression.repository;

import com.wacai.trike.mirana.domain.expression.po.ExpressionPO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ExpressionRepository
extends JpaRepository<ExpressionPO, Long> {
    public List<ExpressionPO> findByEdgeId(Long var1);
}

