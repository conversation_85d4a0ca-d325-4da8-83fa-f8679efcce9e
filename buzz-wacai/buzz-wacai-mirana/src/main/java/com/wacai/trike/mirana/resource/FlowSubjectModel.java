/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.resource;

public class FlowSubjectModel {
    private Long id;
    private String flowCode;
    private Long subjectId;
    private String subjectCode;
    private String subjectName;

    public Long getId() {
        return this.id;
    }

    public String getFlowCode() {
        return this.flowCode;
    }

    public Long getSubjectId() {
        return this.subjectId;
    }

    public String getSubjectCode() {
        return this.subjectCode;
    }

    public String getSubjectName() {
        return this.subjectName;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setFlowCode(String flowCode) {
        this.flowCode = flowCode;
    }

    public void setSubjectId(Long subjectId) {
        this.subjectId = subjectId;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowSubjectModel)) {
            return false;
        }
        FlowSubjectModel other = (FlowSubjectModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$flowCode = this.getFlowCode();
        String other$flowCode = other.getFlowCode();
        if (this$flowCode == null ? other$flowCode != null : !this$flowCode.equals(other$flowCode)) {
            return false;
        }
        Long this$subjectId = this.getSubjectId();
        Long other$subjectId = other.getSubjectId();
        if (this$subjectId == null ? other$subjectId != null : !(this$subjectId).equals(other$subjectId)) {
            return false;
        }
        String this$subjectCode = this.getSubjectCode();
        String other$subjectCode = other.getSubjectCode();
        if (this$subjectCode == null ? other$subjectCode != null : !this$subjectCode.equals(other$subjectCode)) {
            return false;
        }
        String this$subjectName = this.getSubjectName();
        String other$subjectName = other.getSubjectName();
        return !(this$subjectName == null ? other$subjectName != null : !this$subjectName.equals(other$subjectName));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowSubjectModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $flowCode = this.getFlowCode();
        result = result * 59 + ($flowCode == null ? 43 : $flowCode.hashCode());
        Long $subjectId = this.getSubjectId();
        result = result * 59 + ($subjectId == null ? 43 : ($subjectId).hashCode());
        String $subjectCode = this.getSubjectCode();
        result = result * 59 + ($subjectCode == null ? 43 : $subjectCode.hashCode());
        String $subjectName = this.getSubjectName();
        result = result * 59 + ($subjectName == null ? 43 : $subjectName.hashCode());
        return result;
    }

    public String toString() {
        return "FlowSubjectModel(id=" + this.getId() + ", flowCode=" + this.getFlowCode() + ", subjectId=" + this.getSubjectId() + ", subjectCode=" + this.getSubjectCode() + ", subjectName=" + this.getSubjectName() + ")";
    }
}

