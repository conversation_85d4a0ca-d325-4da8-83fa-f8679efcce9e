/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.common.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import java.time.LocalDateTime;

public class QBasePO
extends EntityPathBase<BasePO> {
    private static final long serialVersionUID = 1233321577L;
    public static final QBasePO basePO = new QBasePO("basePO");
    public final BooleanPath active = this.createBoolean("active");
    public final StringPath createdBy = this.createString("createdBy");
    public final DateTimePath<LocalDateTime> createdTime = this.createDateTime("createdTime", LocalDateTime.class);
    public final NumberPath<Long> id = this.createNumber("id", Long.class);
    public final StringPath updatedBy = this.createString("updatedBy");
    public final DateTimePath<LocalDateTime> updatedTime = this.createDateTime("updatedTime", LocalDateTime.class);

    public QBasePO(String variable) {
        super(BasePO.class, PathMetadataFactory.forVariable((String)variable));
    }

    public QBasePO(Path<? extends BasePO> path) {
        super(path.getType(), path.getMetadata());
    }

    public QBasePO(PathMetadata metadata) {
        super(BasePO.class, metadata);
    }
}

