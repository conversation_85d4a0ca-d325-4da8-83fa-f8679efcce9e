/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.api.constant.NodeType;
import com.wacai.trike.mirana.api.constant.TaskExecuteType;
import com.wacai.trike.mirana.api.manage.model.ActionTemplateModel;
import com.wacai.trike.mirana.api.manage.model.DelayModel;
import com.wacai.trike.mirana.api.manage.model.FlowNodeVariableModel;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import javax.validation.constraints.NotBlank;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class NodeModel implements Serializable {
    public static final String ID_PREFIX = "node_";
    private String id;
    @NotBlank
    private String code;
    @NotBlank
    private String label;
    private NodeType type;
    private TaskExecuteType taskExecuteType;
    private Integer taskTimeout;
    private Double x;
    private Double y;
    private String shape;
    private List<Double> size;
    private DelayModel delay;
    private Long generatedId;
    private String subflowCode;
    private ActionTemplateModel action;
    private List<FlowNodeVariableModel> flowNodeVariables;
    private REBASE rebase;

    public void addFlowNodeVariableModel(FlowNodeVariableModel variableModel) {
        if (this.flowNodeVariables == null) {
            this.flowNodeVariables = new ArrayList<FlowNodeVariableModel>();
        }
        this.flowNodeVariables.add(variableModel);
    }

    private boolean delayUpdated(DelayModel other) {
        if (this.delay == other) {
            return false;
        }
        return this.delay != null && this.delay.updateFrom(other) || other != null && other.updateFrom(this.delay);
    }

    private boolean actionUpdated(ActionTemplateModel other) {
        if (this.action == other) {
            return false;
        }
        return this.action != null && this.action.updateFrom(other) || other != null && other.updateFrom(this.action);
    }

    public boolean detecting(NodeModel other) {
        if (other == null) {
            return true;
        }
        if (!this.getLabel().equals(other.getLabel())) {
            return true;
        }
        if (this.getType() != other.getType()) {
            return true;
        }
        if (this.getTaskExecuteType() != other.getTaskExecuteType()) {
            return true;
        }
        if (this.delayUpdated(other.getDelay())) {
            return true;
        }
        return this.actionUpdated(other.getAction());
    }

    public String getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getLabel() {
        return this.label;
    }

    public NodeType getType() {
        return this.type;
    }

    public TaskExecuteType getTaskExecuteType() {
        return this.taskExecuteType;
    }

    public Integer getTaskTimeout() {
        return this.taskTimeout;
    }

    public Double getX() {
        return this.x;
    }

    public Double getY() {
        return this.y;
    }

    public String getShape() {
        return this.shape;
    }

    public List<Double> getSize() {
        return this.size;
    }

    public DelayModel getDelay() {
        return this.delay;
    }

    public Long getGeneratedId() {
        return this.generatedId;
    }

    public String getSubflowCode() {
        return this.subflowCode;
    }

    public ActionTemplateModel getAction() {
        return this.action;
    }

    public List<FlowNodeVariableModel> getFlowNodeVariables() {
        return this.flowNodeVariables;
    }

    public REBASE getRebase() {
        return this.rebase;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public void setType(NodeType type) {
        this.type = type;
    }

    public void setTaskExecuteType(TaskExecuteType taskExecuteType) {
        this.taskExecuteType = taskExecuteType;
    }

    public void setTaskTimeout(Integer taskTimeout) {
        this.taskTimeout = taskTimeout;
    }

    public void setX(Double x) {
        this.x = x;
    }

    public void setY(Double y) {
        this.y = y;
    }

    public void setShape(String shape) {
        this.shape = shape;
    }

    public void setSize(List<Double> size) {
        this.size = size;
    }

    public void setDelay(DelayModel delay) {
        this.delay = delay;
    }

    public void setGeneratedId(Long generatedId) {
        this.generatedId = generatedId;
    }

    public void setSubflowCode(String subflowCode) {
        this.subflowCode = subflowCode;
    }

    public void setAction(ActionTemplateModel action) {
        this.action = action;
    }

    public void setFlowNodeVariables(List<FlowNodeVariableModel> flowNodeVariables) {
        this.flowNodeVariables = flowNodeVariables;
    }

    public void setRebase(REBASE rebase) {
        this.rebase = rebase;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof NodeModel)) {
            return false;
        }
        NodeModel other = (NodeModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$id = this.getId();
        String other$id = other.getId();
        if (this$id == null ? other$id != null : !this$id.equals(other$id)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$label = this.getLabel();
        String other$label = other.getLabel();
        if (this$label == null ? other$label != null : !this$label.equals(other$label)) {
            return false;
        }
        NodeType this$type = this.getType();
        NodeType other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        TaskExecuteType this$taskExecuteType = this.getTaskExecuteType();
        TaskExecuteType other$taskExecuteType = other.getTaskExecuteType();
        if (this$taskExecuteType == null ? other$taskExecuteType != null : !((this$taskExecuteType)).equals(other$taskExecuteType)) {
            return false;
        }
        Integer this$taskTimeout = this.getTaskTimeout();
        Integer other$taskTimeout = other.getTaskTimeout();
        if (this$taskTimeout == null ? other$taskTimeout != null : !(this$taskTimeout).equals(other$taskTimeout)) {
            return false;
        }
        Double this$x = this.getX();
        Double other$x = other.getX();
        if (this$x == null ? other$x != null : !(this$x).equals(other$x)) {
            return false;
        }
        Double this$y = this.getY();
        Double other$y = other.getY();
        if (this$y == null ? other$y != null : !(this$y).equals(other$y)) {
            return false;
        }
        String this$shape = this.getShape();
        String other$shape = other.getShape();
        if (this$shape == null ? other$shape != null : !this$shape.equals(other$shape)) {
            return false;
        }
        List<Double> this$size = this.getSize();
        List<Double> other$size = other.getSize();
        if (this$size == null ? other$size != null : !(this$size).equals(other$size)) {
            return false;
        }
        DelayModel this$delay = this.getDelay();
        DelayModel other$delay = other.getDelay();
        if (this$delay == null ? other$delay != null : !(this$delay).equals(other$delay)) {
            return false;
        }
        Long this$generatedId = this.getGeneratedId();
        Long other$generatedId = other.getGeneratedId();
        if (this$generatedId == null ? other$generatedId != null : !(this$generatedId).equals(other$generatedId)) {
            return false;
        }
        String this$subflowCode = this.getSubflowCode();
        String other$subflowCode = other.getSubflowCode();
        if (this$subflowCode == null ? other$subflowCode != null : !this$subflowCode.equals(other$subflowCode)) {
            return false;
        }
        ActionTemplateModel this$action = this.getAction();
        ActionTemplateModel other$action = other.getAction();
        if (this$action == null ? other$action != null : !(this$action).equals(other$action)) {
            return false;
        }
        List<FlowNodeVariableModel> this$flowNodeVariables = this.getFlowNodeVariables();
        List<FlowNodeVariableModel> other$flowNodeVariables = other.getFlowNodeVariables();
        if (this$flowNodeVariables == null ? other$flowNodeVariables != null : !(this$flowNodeVariables).equals(other$flowNodeVariables)) {
            return false;
        }
        REBASE this$rebase = this.getRebase();
        REBASE other$rebase = other.getRebase();
        return !(this$rebase == null ? other$rebase != null : !((this$rebase)).equals(other$rebase));
    }

    protected boolean canEqual(Object other) {
        return other instanceof NodeModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $id = this.getId();
        result = result * 59 + ($id == null ? 43 : $id.hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $label = this.getLabel();
        result = result * 59 + ($label == null ? 43 : $label.hashCode());
        NodeType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        TaskExecuteType $taskExecuteType = this.getTaskExecuteType();
        result = result * 59 + ($taskExecuteType == null ? 43 : (($taskExecuteType)).hashCode());
        Integer $taskTimeout = this.getTaskTimeout();
        result = result * 59 + ($taskTimeout == null ? 43 : ($taskTimeout).hashCode());
        Double $x = this.getX();
        result = result * 59 + ($x == null ? 43 : ($x).hashCode());
        Double $y = this.getY();
        result = result * 59 + ($y == null ? 43 : ($y).hashCode());
        String $shape = this.getShape();
        result = result * 59 + ($shape == null ? 43 : $shape.hashCode());
        List<Double> $size = this.getSize();
        result = result * 59 + ($size == null ? 43 : ($size).hashCode());
        DelayModel $delay = this.getDelay();
        result = result * 59 + ($delay == null ? 43 : ($delay).hashCode());
        Long $generatedId = this.getGeneratedId();
        result = result * 59 + ($generatedId == null ? 43 : ($generatedId).hashCode());
        String $subflowCode = this.getSubflowCode();
        result = result * 59 + ($subflowCode == null ? 43 : $subflowCode.hashCode());
        ActionTemplateModel $action = this.getAction();
        result = result * 59 + ($action == null ? 43 : ($action).hashCode());
        List<FlowNodeVariableModel> $flowNodeVariables = this.getFlowNodeVariables();
        result = result * 59 + ($flowNodeVariables == null ? 43 : ($flowNodeVariables).hashCode());
        REBASE $rebase = this.getRebase();
        result = result * 59 + ($rebase == null ? 43 : (($rebase)).hashCode());
        return result;
    }

    public String toString() {
        return "NodeModel(id=" + this.getId() + ", code=" + this.getCode() + ", label=" + this.getLabel() + ", type=" + (this.getType()) + ", taskExecuteType=" + (this.getTaskExecuteType()) + ", taskTimeout=" + this.getTaskTimeout() + ", x=" + this.getX() + ", y=" + this.getY() + ", shape=" + this.getShape() + ", size=" + this.getSize() + ", delay=" + this.getDelay() + ", generatedId=" + this.getGeneratedId() + ", subflowCode=" + this.getSubflowCode() + ", action=" + this.getAction() + ", flowNodeVariables=" + this.getFlowNodeVariables() + ", rebase=" + (this.getRebase()) + ")";
    }

    public static enum REBASE {
        NOTHING,
        BRAND_NEW,
        ORIGINAL,
        DELETING,
        COMPARED;

    }
}

