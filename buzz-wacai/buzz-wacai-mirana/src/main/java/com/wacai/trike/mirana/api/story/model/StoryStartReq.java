/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.story.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.validation.constraints.NotBlank;

public class StoryStartReq
implements Serializable {
    private String bzKey;
    @NotBlank
    private String bu;
    @NotBlank
    private String app;
    @NotBlank
    private String flow;
    @NotBlank
    private String operator;
    private int state;
    private String uuid;
    private List<String> birthmarks;
    private boolean async = false;
    private boolean asyncStart = false;
    private Map<String, String> param = new HashMap<String, String>();

    public StoryStartReq addParam(String key, String value) {
        this.param.put(Objects.requireNonNull(key), value);
        return this;
    }

    public String getBzKey() {
        return this.bzKey;
    }

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getFlow() {
        return this.flow;
    }

    public String getOperator() {
        return this.operator;
    }

    public int getState() {
        return this.state;
    }

    public String getUuid() {
        return this.uuid;
    }

    public List<String> getBirthmarks() {
        return this.birthmarks;
    }

    public boolean isAsync() {
        return this.async;
    }

    public boolean isAsyncStart() {
        return this.asyncStart;
    }

    public Map<String, String> getParam() {
        return this.param;
    }

    public StoryStartReq setBzKey(String bzKey) {
        this.bzKey = bzKey;
        return this;
    }

    public StoryStartReq setBu(String bu) {
        this.bu = bu;
        return this;
    }

    public StoryStartReq setApp(String app) {
        this.app = app;
        return this;
    }

    public StoryStartReq setFlow(String flow) {
        this.flow = flow;
        return this;
    }

    public StoryStartReq setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public StoryStartReq setState(int state) {
        this.state = state;
        return this;
    }

    public StoryStartReq setUuid(String uuid) {
        this.uuid = uuid;
        return this;
    }

    public StoryStartReq setBirthmarks(List<String> birthmarks) {
        this.birthmarks = birthmarks;
        return this;
    }

    public StoryStartReq setAsync(boolean async) {
        this.async = async;
        return this;
    }

    public StoryStartReq setAsyncStart(boolean asyncStart) {
        this.asyncStart = asyncStart;
        return this;
    }

    public StoryStartReq setParam(Map<String, String> param) {
        this.param = param;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryStartReq)) {
            return false;
        }
        StoryStartReq other = (StoryStartReq)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$bzKey = this.getBzKey();
        String other$bzKey = other.getBzKey();
        if (this$bzKey == null ? other$bzKey != null : !this$bzKey.equals(other$bzKey)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        if (this$flow == null ? other$flow != null : !this$flow.equals(other$flow)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        if (this.getState() != other.getState()) {
            return false;
        }
        String this$uuid = this.getUuid();
        String other$uuid = other.getUuid();
        if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
            return false;
        }
        List<String> this$birthmarks = this.getBirthmarks();
        List<String> other$birthmarks = other.getBirthmarks();
        if (this$birthmarks == null ? other$birthmarks != null : !(this$birthmarks).equals(other$birthmarks)) {
            return false;
        }
        if (this.isAsync() != other.isAsync()) {
            return false;
        }
        if (this.isAsyncStart() != other.isAsyncStart()) {
            return false;
        }
        Map<String, String> this$param = this.getParam();
        Map<String, String> other$param = other.getParam();
        return !(this$param == null ? other$param != null : !(this$param).equals(other$param));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryStartReq;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $bzKey = this.getBzKey();
        result = result * 59 + ($bzKey == null ? 43 : $bzKey.hashCode());
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        result = result * 59 + this.getState();
        String $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        List<String> $birthmarks = this.getBirthmarks();
        result = result * 59 + ($birthmarks == null ? 43 : ($birthmarks).hashCode());
        result = result * 59 + (this.isAsync() ? 79 : 97);
        result = result * 59 + (this.isAsyncStart() ? 79 : 97);
        Map<String, String> $param = this.getParam();
        result = result * 59 + ($param == null ? 43 : ($param).hashCode());
        return result;
    }

    public String toString() {
        return "StoryStartReq(bzKey=" + this.getBzKey() + ", bu=" + this.getBu() + ", app=" + this.getApp() + ", flow=" + this.getFlow() + ", operator=" + this.getOperator() + ", state=" + this.getState() + ", uuid=" + this.getUuid() + ", birthmarks=" + this.getBirthmarks() + ", async=" + this.isAsync() + ", asyncStart=" + this.isAsyncStart() + ", param=" + this.getParam() + ")";
    }
}

