/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.FormType
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.form.po;

import com.wacai.trike.mirana.api.constant.FormType;
import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="form")
public class FormPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private Long nodeId;
    private Long variableConfigId;
    private Long actionId;
    @Enumerated(value=EnumType.STRING)
    private FormType type;
    private String field;
    private String fieldType;
    private String value;

    public Long getNodeId() {
        return this.nodeId;
    }

    public Long getVariableConfigId() {
        return this.variableConfigId;
    }

    public Long getActionId() {
        return this.actionId;
    }

    public FormType getType() {
        return this.type;
    }

    public String getField() {
        return this.field;
    }

    public String getFieldType() {
        return this.fieldType;
    }

    public String getValue() {
        return this.value;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public void setVariableConfigId(Long variableConfigId) {
        this.variableConfigId = variableConfigId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public void setType(FormType type) {
        this.type = type;
    }

    public void setField(String field) {
        this.field = field;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public void setValue(String value) {
        this.value = value;
    }

    @Override
    public String toString() {
        return "FormPO(nodeId=" + this.getNodeId() + ", variableConfigId=" + this.getVariableConfigId() + ", actionId=" + this.getActionId() + ", type=" + this.getType() + ", field=" + this.getField() + ", fieldType=" + this.getFieldType() + ", value=" + this.getValue() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FormPO)) {
            return false;
        }
        FormPO other = (FormPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$nodeId = this.getNodeId();
        Long other$nodeId = other.getNodeId();
        if (this$nodeId == null ? other$nodeId != null : !(this$nodeId).equals(other$nodeId)) {
            return false;
        }
        Long this$variableConfigId = this.getVariableConfigId();
        Long other$variableConfigId = other.getVariableConfigId();
        if (this$variableConfigId == null ? other$variableConfigId != null : !(this$variableConfigId).equals(other$variableConfigId)) {
            return false;
        }
        Long this$actionId = this.getActionId();
        Long other$actionId = other.getActionId();
        if (this$actionId == null ? other$actionId != null : !(this$actionId).equals(other$actionId)) {
            return false;
        }
        FormType this$type = this.getType();
        FormType other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$field = this.getField();
        String other$field = other.getField();
        if (this$field == null ? other$field != null : !this$field.equals(other$field)) {
            return false;
        }
        String this$fieldType = this.getFieldType();
        String other$fieldType = other.getFieldType();
        if (this$fieldType == null ? other$fieldType != null : !this$fieldType.equals(other$fieldType)) {
            return false;
        }
        String this$value = this.getValue();
        String other$value = other.getValue();
        return !(this$value == null ? other$value != null : !this$value.equals(other$value));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FormPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $nodeId = this.getNodeId();
        result = result * 59 + ($nodeId == null ? 43 : ($nodeId).hashCode());
        Long $variableConfigId = this.getVariableConfigId();
        result = result * 59 + ($variableConfigId == null ? 43 : ($variableConfigId).hashCode());
        Long $actionId = this.getActionId();
        result = result * 59 + ($actionId == null ? 43 : ($actionId).hashCode());
        FormType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $field = this.getField();
        result = result * 59 + ($field == null ? 43 : $field.hashCode());
        String $fieldType = this.getFieldType();
        result = result * 59 + ($fieldType == null ? 43 : $fieldType.hashCode());
        String $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : $value.hashCode());
        return result;
    }
}

