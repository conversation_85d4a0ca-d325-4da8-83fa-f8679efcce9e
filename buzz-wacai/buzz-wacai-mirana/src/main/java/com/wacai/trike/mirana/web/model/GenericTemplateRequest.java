/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.web.model;

public class GenericTemplateRequest {
    private int pageSize = 12;
    private int pageIndex = 1;
    private String name;
    private Long genericActionId;
    private Boolean active = Boolean.TRUE;

    public int getPageSize() {
        return this.pageSize;
    }

    public int getPageIndex() {
        return this.pageIndex;
    }

    public String getName() {
        return this.name;
    }

    public Long getGenericActionId() {
        return this.genericActionId;
    }

    public Boolean getActive() {
        return this.active;
    }

    public GenericTemplateRequest setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public GenericTemplateRequest setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
        return this;
    }

    public GenericTemplateRequest setName(String name) {
        this.name = name;
        return this;
    }

    public GenericTemplateRequest setGenericActionId(Long genericActionId) {
        this.genericActionId = genericActionId;
        return this;
    }

    public GenericTemplateRequest setActive(Boolean active) {
        this.active = active;
        return this;
    }

    public String toString() {
        return "GenericTemplateRequest(pageSize=" + this.getPageSize() + ", pageIndex=" + this.getPageIndex() + ", name=" + this.getName() + ", genericActionId=" + this.getGenericActionId() + ", active=" + this.getActive() + ")";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericTemplateRequest)) {
            return false;
        }
        GenericTemplateRequest other = (GenericTemplateRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (this.getPageSize() != other.getPageSize()) {
            return false;
        }
        if (this.getPageIndex() != other.getPageIndex()) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        Long this$genericActionId = this.getGenericActionId();
        Long other$genericActionId = other.getGenericActionId();
        if (this$genericActionId == null ? other$genericActionId != null : !(this$genericActionId).equals(other$genericActionId)) {
            return false;
        }
        Boolean this$active = this.getActive();
        Boolean other$active = other.getActive();
        return !(this$active == null ? other$active != null : !(this$active).equals(other$active));
    }

    protected boolean canEqual(Object other) {
        return other instanceof GenericTemplateRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        result = result * 59 + this.getPageSize();
        result = result * 59 + this.getPageIndex();
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Long $genericActionId = this.getGenericActionId();
        result = result * 59 + ($genericActionId == null ? 43 : ($genericActionId).hashCode());
        Boolean $active = this.getActive();
        result = result * 59 + ($active == null ? 43 : ($active).hashCode());
        return result;
    }
}

