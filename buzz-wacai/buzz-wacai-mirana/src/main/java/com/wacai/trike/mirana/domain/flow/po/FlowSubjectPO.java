/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.flow.po;

import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="flow_subject")
public class FlowSubjectPO
extends BasePO {
    private String flowCode;
    private Long subjectId;
    private String subjectCode;
    private String subjectName;

    public String getFlowCode() {
        return this.flowCode;
    }

    public Long getSubjectId() {
        return this.subjectId;
    }

    public String getSubjectCode() {
        return this.subjectCode;
    }

    public String getSubjectName() {
        return this.subjectName;
    }

    public void setFlowCode(String flowCode) {
        this.flowCode = flowCode;
    }

    public void setSubjectId(Long subjectId) {
        this.subjectId = subjectId;
    }

    public void setSubjectCode(String subjectCode) {
        this.subjectCode = subjectCode;
    }

    public void setSubjectName(String subjectName) {
        this.subjectName = subjectName;
    }

    @Override
    public String toString() {
        return "FlowSubjectPO(flowCode=" + this.getFlowCode() + ", subjectId=" + this.getSubjectId() + ", subjectCode=" + this.getSubjectCode() + ", subjectName=" + this.getSubjectName() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowSubjectPO)) {
            return false;
        }
        FlowSubjectPO other = (FlowSubjectPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$flowCode = this.getFlowCode();
        String other$flowCode = other.getFlowCode();
        if (this$flowCode == null ? other$flowCode != null : !this$flowCode.equals(other$flowCode)) {
            return false;
        }
        Long this$subjectId = this.getSubjectId();
        Long other$subjectId = other.getSubjectId();
        if (this$subjectId == null ? other$subjectId != null : !(this$subjectId).equals(other$subjectId)) {
            return false;
        }
        String this$subjectCode = this.getSubjectCode();
        String other$subjectCode = other.getSubjectCode();
        if (this$subjectCode == null ? other$subjectCode != null : !this$subjectCode.equals(other$subjectCode)) {
            return false;
        }
        String this$subjectName = this.getSubjectName();
        String other$subjectName = other.getSubjectName();
        return !(this$subjectName == null ? other$subjectName != null : !this$subjectName.equals(other$subjectName));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowSubjectPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $flowCode = this.getFlowCode();
        result = result * 59 + ($flowCode == null ? 43 : $flowCode.hashCode());
        Long $subjectId = this.getSubjectId();
        result = result * 59 + ($subjectId == null ? 43 : ($subjectId).hashCode());
        String $subjectCode = this.getSubjectCode();
        result = result * 59 + ($subjectCode == null ? 43 : $subjectCode.hashCode());
        String $subjectName = this.getSubjectName();
        result = result * 59 + ($subjectName == null ? 43 : $subjectName.hashCode());
        return result;
    }
}

