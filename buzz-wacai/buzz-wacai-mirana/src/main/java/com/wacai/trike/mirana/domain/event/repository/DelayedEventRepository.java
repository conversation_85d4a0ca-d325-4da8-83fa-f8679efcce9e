/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 *  org.springframework.data.jpa.repository.Modifying
 *  org.springframework.data.jpa.repository.Query
 *  org.springframework.data.repository.query.Param
 *  org.springframework.transaction.annotation.Transactional
 */
package com.wacai.trike.mirana.domain.event.repository;

import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.transaction.annotation.Transactional;

public interface DelayedEventRepository
extends JpaRepository<DelayedEventPO, Long> {
    public Optional<DelayedEventPO> findByUuid(String var1);

    public List<DelayedEventPO> findByInstanceUuidAndStatusIn(String var1, List<DelayedEventStatus> var2);

    public List<DelayedEventPO> findByStatusAndExpectFireTimeBefore(DelayedEventStatus var1, LocalDateTime var2);

    public List<DelayedEventPO> findByStatus(DelayedEventStatus var1);

    @Modifying
    @Transactional
    @Query(value="update DelayedEventPO set status=:newStatus where id=:id and status=:oldStatus ")
    public int updateStatusByIdAndStatus(@Param(value="newStatus") DelayedEventStatus var1, @Param(value="id") Long var2, @Param(value="oldStatus") DelayedEventStatus var3);
}

