/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.service;

import java.io.Serializable;
import java.time.LocalDateTime;

public class BaseDTO
implements Serializable {
    protected Long id;
    protected Boolean active = Boolean.TRUE;
    protected String createdBy;
    protected String updatedBy;
    protected LocalDateTime updatedTime;
    protected LocalDateTime createdTime;

    public Long getId() {
        return this.id;
    }

    public Boolean getActive() {
        return this.active;
    }

    public String getCreatedBy() {
        return this.createdBy;
    }

    public String getUpdatedBy() {
        return this.updatedBy;
    }

    public LocalDateTime getUpdatedTime() {
        return this.updatedTime;
    }

    public LocalDateTime getCreatedTime() {
        return this.createdTime;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setActive(Boolean active) {
        this.active = active;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public void setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof BaseDTO)) {
            return false;
        }
        BaseDTO other = (BaseDTO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Boolean this$active = this.getActive();
        Boolean other$active = other.getActive();
        if (this$active == null ? other$active != null : !(this$active).equals(other$active)) {
            return false;
        }
        String this$createdBy = this.getCreatedBy();
        String other$createdBy = other.getCreatedBy();
        if (this$createdBy == null ? other$createdBy != null : !this$createdBy.equals(other$createdBy)) {
            return false;
        }
        String this$updatedBy = this.getUpdatedBy();
        String other$updatedBy = other.getUpdatedBy();
        if (this$updatedBy == null ? other$updatedBy != null : !this$updatedBy.equals(other$updatedBy)) {
            return false;
        }
        LocalDateTime this$updatedTime = this.getUpdatedTime();
        LocalDateTime other$updatedTime = other.getUpdatedTime();
        if (this$updatedTime == null ? other$updatedTime != null : !(this$updatedTime).equals(other$updatedTime)) {
            return false;
        }
        LocalDateTime this$createdTime = this.getCreatedTime();
        LocalDateTime other$createdTime = other.getCreatedTime();
        return !(this$createdTime == null ? other$createdTime != null : !(this$createdTime).equals(other$createdTime));
    }

    protected boolean canEqual(Object other) {
        return other instanceof BaseDTO;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Boolean $active = this.getActive();
        result = result * 59 + ($active == null ? 43 : ($active).hashCode());
        String $createdBy = this.getCreatedBy();
        result = result * 59 + ($createdBy == null ? 43 : $createdBy.hashCode());
        String $updatedBy = this.getUpdatedBy();
        result = result * 59 + ($updatedBy == null ? 43 : $updatedBy.hashCode());
        LocalDateTime $updatedTime = this.getUpdatedTime();
        result = result * 59 + ($updatedTime == null ? 43 : ($updatedTime).hashCode());
        LocalDateTime $createdTime = this.getCreatedTime();
        result = result * 59 + ($createdTime == null ? 43 : ($createdTime).hashCode());
        return result;
    }

    public String toString() {
        return "BaseDTO(id=" + this.getId() + ", active=" + this.getActive() + ", createdBy=" + this.getCreatedBy() + ", updatedBy=" + this.getUpdatedBy() + ", updatedTime=" + this.getUpdatedTime() + ", createdTime=" + this.getCreatedTime() + ")";
    }
}

