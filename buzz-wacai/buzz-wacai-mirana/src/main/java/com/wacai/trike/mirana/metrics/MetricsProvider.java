/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.cache.Cache
 *  com.google.common.cache.CacheBuilder
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.Projections
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.metrics;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.flow.po.QFlowPO;
import com.wacai.trike.mirana.metrics.DelayedEventMetrics;
import com.wacai.trike.mirana.metrics.FlowMetrics;
import com.wacai.trike.mirana.metrics.NodeMetrics;
import com.wacai.trike.mirana.util.MetricsUtil;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public final class MetricsProvider {
    private static final Logger log = LoggerFactory.getLogger(MetricsProvider.class);
    private final Cache<Long, MetricsAttribution> attributionCache = CacheBuilder.newBuilder().expireAfterAccess(30L, TimeUnit.MINUTES).initialCapacity(20).maximumSize(50L).recordStats().build();
    @Autowired
    private JPAQueryFactory jpaQueryFactory;

    public double hitRate() {
        return this.attributionCache.stats().hitRate();
    }

    public void metricsFlow(FlowMetrics target) {
        MetricsUtil.getFlowUtil().addTag("bu", target.getBu()).addTag("app", target.getApp()).addTag("flow", target.getFlow()).addTag("operate", target.getOperate()).addTag("node", target.getNode()).addField("instance_uuid", target.getUuid()).addField("operator", target.getOperator()).produce();
    }

    public void metricsNode(NodeMetrics target) {
        MetricsAttribution attribution = this.query(target.getFlowId()).orElse(null);
        if (attribution != null) {
            target = target.toBuilder().app(attribution.getApp()).bu(attribution.getBu()).build();
        }
        MetricsUtil.getNodeUtil().addTag("bu", target.getBu()).addTag("app", target.getApp()).addTag("flow", target.getFlow()).addTag("node", target.getNode()).addTag("status", target.getStatus()).addTag("task_type", target.getTaskType()).addTag("task_execute_type", target.getTaskExecuteType()).addTag("node_type", target.getNodeType()).addTag("occurred_date", target.getOccurredDate()).addField("instance_uuid", target.getInstanceUuid()).addField("task_instance_uuid", target.getTaskInstanceUuid()).addField("flow_id", target.getFlowId()).addField("node_id", target.getNodeId()).addField("remark", target.getRemark()).produce();
    }

    public void metricsDelayedEvent(DelayedEventMetrics target) {
        MetricsUtil.getDelayedEventUtil().addTag("category", target.getCategory().toString()).addField("uuid", target.getUuid()).addField("instance_uuid", target.getInstanceUuid()).addField("task_instance_uuid", target.getTaskInstanceUuid()).addField("expect_fire_time", target.getExpectFireTime()).addField("operator", target.getOperator()).produce();
    }

    private Optional<MetricsAttribution> query(Long flowId) {
        if (Objects.isNull(flowId)) {
            return Optional.empty();
        }
        MetricsAttribution cached = (MetricsAttribution)this.attributionCache.getIfPresent(flowId);
        if (cached != null) {
            return Optional.of(cached);
        }
        MetricsAttribution attribution = (MetricsAttribution)((JPAQuery)((JPAQuery)((JPAQuery)((JPAQuery)((JPAQuery)((JPAQuery)this.jpaQueryFactory.select((Expression)Projections.fields(MetricsAttribution.class, (Expression[])new Expression[]{QFlowPO.flowPO.id.as("flowId"), QBusinessPO.businessPO.code.as("bu"), QApplicationPO.applicationPO.code.as("app"), QFlowPO.flowPO.code.as("flow")})).from((EntityPath)QFlowPO.flowPO)).leftJoin((EntityPath)QApplicationPO.applicationPO)).on((Predicate)QFlowPO.flowPO.appId.eq(QApplicationPO.applicationPO.id))).leftJoin((EntityPath)QBusinessPO.businessPO)).on((Predicate)QApplicationPO.applicationPO.buId.eq(QBusinessPO.businessPO.id))).where((Predicate)QFlowPO.flowPO.id.eq(flowId))).fetchFirst();
        if (attribution != null) {
            this.attributionCache.put(attribution.getFlowId(), attribution);
        }
        return attribution == null ? Optional.empty() : Optional.of(attribution);
    }

    public static class MetricsAttribution {
        private Long flowId;
        private String bu;
        private String app;
        private String flow;

        public int hashCode() {
            return this.flowId.hashCode();
        }

        public Long getFlowId() {
            return this.flowId;
        }

        public String getBu() {
            return this.bu;
        }

        public String getApp() {
            return this.app;
        }

        public String getFlow() {
            return this.flow;
        }

        public void setFlowId(Long flowId) {
            this.flowId = flowId;
        }

        public void setBu(String bu) {
            this.bu = bu;
        }

        public void setApp(String app) {
            this.app = app;
        }

        public void setFlow(String flow) {
            this.flow = flow;
        }

        public String toString() {
            return "MetricsProvider.MetricsAttribution(flowId=" + this.getFlowId() + ", bu=" + this.getBu() + ", app=" + this.getApp() + ", flow=" + this.getFlow() + ")";
        }
    }
}

