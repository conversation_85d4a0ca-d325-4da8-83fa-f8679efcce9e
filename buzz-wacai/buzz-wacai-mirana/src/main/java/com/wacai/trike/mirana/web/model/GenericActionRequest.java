/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.web.model;

public class GenericActionRequest {
    private int pageSize = 12;
    private int pageIndex = 1;
    private String type;
    private String name;
    private long appId;
    private Boolean action = true;
    private boolean includeTemplate;

    public int getPageSize() {
        return this.pageSize;
    }

    public int getPageIndex() {
        return this.pageIndex;
    }

    public String getType() {
        return this.type;
    }

    public String getName() {
        return this.name;
    }

    public long getAppId() {
        return this.appId;
    }

    public Boolean getAction() {
        return this.action;
    }

    public boolean isIncludeTemplate() {
        return this.includeTemplate;
    }

    public GenericActionRequest setPageSize(int pageSize) {
        this.pageSize = pageSize;
        return this;
    }

    public GenericActionRequest setPageIndex(int pageIndex) {
        this.pageIndex = pageIndex;
        return this;
    }

    public GenericActionRequest setType(String type) {
        this.type = type;
        return this;
    }

    public GenericActionRequest setName(String name) {
        this.name = name;
        return this;
    }

    public GenericActionRequest setAppId(long appId) {
        this.appId = appId;
        return this;
    }

    public GenericActionRequest setAction(Boolean action) {
        this.action = action;
        return this;
    }

    public GenericActionRequest setIncludeTemplate(boolean includeTemplate) {
        this.includeTemplate = includeTemplate;
        return this;
    }

    public String toString() {
        return "GenericActionRequest(pageSize=" + this.getPageSize() + ", pageIndex=" + this.getPageIndex() + ", type=" + this.getType() + ", name=" + this.getName() + ", appId=" + this.getAppId() + ", action=" + this.getAction() + ", includeTemplate=" + this.isIncludeTemplate() + ")";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericActionRequest)) {
            return false;
        }
        GenericActionRequest other = (GenericActionRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (this.getPageSize() != other.getPageSize()) {
            return false;
        }
        if (this.getPageIndex() != other.getPageIndex()) {
            return false;
        }
        String this$type = this.getType();
        String other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        if (this.getAppId() != other.getAppId()) {
            return false;
        }
        Boolean this$action = this.getAction();
        Boolean other$action = other.getAction();
        if (this$action == null ? other$action != null : !(this$action).equals(other$action)) {
            return false;
        }
        return this.isIncludeTemplate() == other.isIncludeTemplate();
    }

    protected boolean canEqual(Object other) {
        return other instanceof GenericActionRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        result = result * 59 + this.getPageSize();
        result = result * 59 + this.getPageIndex();
        String $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        long $appId = this.getAppId();
        result = result * 59 + (int)($appId >>> 32 ^ $appId);
        Boolean $action = this.getAction();
        result = result * 59 + ($action == null ? 43 : ($action).hashCode());
        result = result * 59 + (this.isIncludeTemplate() ? 79 : 97);
        return result;
    }
}

