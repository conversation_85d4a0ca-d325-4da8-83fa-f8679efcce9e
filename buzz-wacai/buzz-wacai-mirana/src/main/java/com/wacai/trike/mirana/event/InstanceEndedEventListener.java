/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.InstanceContextService;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.repository.DelayedEventRepository;
import com.wacai.trike.mirana.event.ContextPersistenceEvent;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.EventListener;
import com.wacai.trike.mirana.event.InstanceEndedEvent;
import com.wacai.trike.mirana.graph.FlowGraph;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.metrics.FlowMetrics;
import com.wacai.trike.mirana.metrics.MetricsProvider;
import com.wacai.trike.mirana.notify.NotifyService;
import java.util.Arrays;
import java.util.List;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class InstanceEndedEventListener implements EventListener<InstanceEndedEvent, ContextPersistenceEvent> {
    private static final Logger log = LogManager.getLogger(InstanceEndedEventListener.class);
    @Autowired
    private InstanceContextService contextService;
    @Autowired
    private FlowGraphService graphService;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private MetricsProvider metricsProvider;
    @Autowired
    private DelayedEventRepository eventRepository;

    @Override
    public Class<InstanceEndedEvent> accept() {
        return InstanceEndedEvent.class;
    }

    @Override
    public boolean needProcess(EventExchange<InstanceEndedEvent, ContextPersistenceEvent> exchange) {
        return exchange.getContext().getStatus().isEnded();
    }

    @Override
    public void doProcess(EventExchange<InstanceEndedEvent, ContextPersistenceEvent> exchange) {
        InstanceContext context = exchange.getContext();
        InstanceEndedEvent event = exchange.getCurrent();
        this.cancel(event.getInstanceUuid());
        this.contextService.cancelContextFuture(event.getInstanceUuid());
        this.contextService.removeCache(event.getInstanceUuid());
        this.contextService.mappingVariable(context);
        FlowGraph graph = this.graphService.getFlowGraph(context.getFlowId());
        this.notifyService.notifyFlowEnd(context.copy());
        this.metricsProvider.metricsFlow(FlowMetrics.build(graph, context, "end"));
        exchange.setNext(new ContextPersistenceEvent()
        		.setInstanceUuid(event.getInstanceUuid())
        		.setTaskId(event.getInstanceUuid())
        		.setOperator(event.getOperator()));
    }

    public int getOrder() {
        return 6;
    }

    private void cancel(String instanceUuid) {
        List<DelayedEventStatus> statuses = Arrays.asList(DelayedEventStatus.WAITING, DelayedEventStatus.LOADED);
        List<DelayedEventPO> delayedEvents = this.eventRepository.findByInstanceUuidAndStatusIn(instanceUuid, statuses);
        for (DelayedEventPO delayedEvent : delayedEvents) {
            delayedEvent.setStatus(DelayedEventStatus.CANCELED);
            this.eventRepository.save(delayedEvent);
        }
        log.info("cancel delayed event for [{}], count {}", instanceUuid, delayedEvents.size());
    }
}

