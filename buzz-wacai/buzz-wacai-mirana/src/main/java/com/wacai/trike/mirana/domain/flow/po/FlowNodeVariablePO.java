/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.flow.po;

import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.service.FlowNodeVariableDTO;
import com.wacai.trike.mirana.util.ObjectUtils;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="variable_write_mapping")
public class FlowNodeVariablePO
extends BasePO {
    private Long nodeId;
    private Long parentFlowVariableId;
    private Long subFlowId;
    private Long subFlowVariableId;

    public FlowNodeVariableDTO toDto() {
        return ObjectUtils.convertNotNull(this, FlowNodeVariableDTO.class);
    }

    public Long getNodeId() {
        return this.nodeId;
    }

    public Long getParentFlowVariableId() {
        return this.parentFlowVariableId;
    }

    public Long getSubFlowId() {
        return this.subFlowId;
    }

    public Long getSubFlowVariableId() {
        return this.subFlowVariableId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public void setParentFlowVariableId(Long parentFlowVariableId) {
        this.parentFlowVariableId = parentFlowVariableId;
    }

    public void setSubFlowId(Long subFlowId) {
        this.subFlowId = subFlowId;
    }

    public void setSubFlowVariableId(Long subFlowVariableId) {
        this.subFlowVariableId = subFlowVariableId;
    }

    @Override
    public String toString() {
        return "FlowNodeVariablePO(nodeId=" + this.getNodeId() + ", parentFlowVariableId=" + this.getParentFlowVariableId() + ", subFlowId=" + this.getSubFlowId() + ", subFlowVariableId=" + this.getSubFlowVariableId() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowNodeVariablePO)) {
            return false;
        }
        FlowNodeVariablePO other = (FlowNodeVariablePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$nodeId = this.getNodeId();
        Long other$nodeId = other.getNodeId();
        if (this$nodeId == null ? other$nodeId != null : !(this$nodeId).equals(other$nodeId)) {
            return false;
        }
        Long this$parentFlowVariableId = this.getParentFlowVariableId();
        Long other$parentFlowVariableId = other.getParentFlowVariableId();
        if (this$parentFlowVariableId == null ? other$parentFlowVariableId != null : !(this$parentFlowVariableId).equals(other$parentFlowVariableId)) {
            return false;
        }
        Long this$subFlowId = this.getSubFlowId();
        Long other$subFlowId = other.getSubFlowId();
        if (this$subFlowId == null ? other$subFlowId != null : !(this$subFlowId).equals(other$subFlowId)) {
            return false;
        }
        Long this$subFlowVariableId = this.getSubFlowVariableId();
        Long other$subFlowVariableId = other.getSubFlowVariableId();
        return !(this$subFlowVariableId == null ? other$subFlowVariableId != null : !(this$subFlowVariableId).equals(other$subFlowVariableId));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowNodeVariablePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $nodeId = this.getNodeId();
        result = result * 59 + ($nodeId == null ? 43 : ($nodeId).hashCode());
        Long $parentFlowVariableId = this.getParentFlowVariableId();
        result = result * 59 + ($parentFlowVariableId == null ? 43 : ($parentFlowVariableId).hashCode());
        Long $subFlowId = this.getSubFlowId();
        result = result * 59 + ($subFlowId == null ? 43 : ($subFlowId).hashCode());
        Long $subFlowVariableId = this.getSubFlowVariableId();
        result = result * 59 + ($subFlowVariableId == null ? 43 : ($subFlowVariableId).hashCode());
        return result;
    }
}

