/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSONObject
 *  com.wacai.ocean.seraph.client.context.SeraphContext
 *  com.wacai.ocean.seraph.client.core.handler.IJobHandler
 *  com.wacai.ocean.seraph.client.core.handler.annotation.JobHandler
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 */
package com.wacai.trike.mirana.Seraph;

import com.alibaba.fastjson.JSONObject;
import com.wacai.ocean.seraph.client.context.SeraphContext;
import com.wacai.ocean.seraph.client.core.handler.IJobHandler;
import com.wacai.ocean.seraph.client.core.handler.annotation.JobHandler;
import com.wacai.trike.mirana.Seraph.DelayedEventExecutor;
import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.repository.DelayedEventRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
@JobHandler(ServiceCode="miranaDelay")
public class SeraphCallBackHandler
implements IJobHandler {
    private static final Logger log = LoggerFactory.getLogger(SeraphCallBackHandler.class);
    @Autowired
    private DelayedEventRepository eventRepository;
    @Autowired
    private DelayedEventExecutor delayedEventExecutor;

    public void execute(SeraphContext context) throws Exception {
        JSONObject event = JSONObject.parseObject((String)context.getParam());
        String uuid = event.getString("uuid");
        DelayedEventPO delayedEvent = this.eventRepository.findByUuid(event.getString("uuid")).orElse(null);
        if (delayedEvent == null) {
            log.error("delayedEvent is null, uuid [{}]", uuid);
            return;
        }
        if (delayedEvent.getStatus() == DelayedEventStatus.CANCELED) {
            log.error("delayedEvent is CANCELED, uuid [{}]", uuid);
            return;
        }
        this.delayedEventExecutor.execute(delayedEvent);
    }
}

