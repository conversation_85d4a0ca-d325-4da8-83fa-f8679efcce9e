/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.core.Ordered
 */
package com.wacai.trike.mirana.event;

import java.util.Objects;

import org.springframework.core.Ordered;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;

public interface EventListener<E extends AbstractFlowEvent, R extends AbstractFlowEvent> extends Ordered {
    
	public Class<E> accept();

    default public boolean needProcess(EventExchange<E, R> exchange) {
        return this.check(exchange);
    }

    default public boolean checkTask(EventExchange<E, R> exchange) {
        if (!this.check(exchange)) {
            return false;
        }
        return Objects.equals(((AbstractFlowEvent)exchange.getCurrent()).getTaskId(), exchange.getContext().getCurrentNodeId().toString());
    }

    @Barrier(handlers={InstanceContextSyncHandler.class})
    public void doProcess(EventExchange<E, R> var1);

    default public boolean check(EventExchange<E, R> exchange) {
        if (Objects.isNull(exchange)) {
            return false;
        }
        if (Objects.isNull(exchange.getCurrent()) || Objects.isNull(exchange.getContext())) {
            return false;
        }
        return exchange.getContext().getStatus() != null || exchange.getContext().getStatus().isRunning();
    }
}

