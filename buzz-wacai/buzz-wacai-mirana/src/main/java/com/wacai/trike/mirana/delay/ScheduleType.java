/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSON
 *  com.alibaba.fastjson.parser.Feature
 */
package com.wacai.trike.mirana.delay;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.wacai.trike.mirana.delay.Delay;
import com.wacai.trike.mirana.delay.DelayAround;
import com.wacai.trike.mirana.delay.DelayByDay;
import com.wacai.trike.mirana.delay.DelayByPhase;
import java.lang.reflect.Type;
import java.util.Arrays;
import java.util.List;

public enum ScheduleType {
    DELAY(Delay.class, "任务执行前延时"),
    DELAY_BEFORE(Delay.class, "任务执行前延时"),
    DELAY_AFTER(Delay.class, "任务执行后延时"),
    DELAY_AROUND(DelayAround.class, "任务执行前后都延时"),
    DELAY_DAYS(DelayByDay.class, "按天驱动"),
    DELAY_PHASE(DelayByPhase.class, "按阶段驱动");

    private static final List<ScheduleType> SHOULD_DELAY_BEFORE;
    private static final List<ScheduleType> SHOULD_DELAY_AFTER;
    private Type type;
    private String desc;

    public static boolean couldDelayBefore(ScheduleType type) {
        return SHOULD_DELAY_BEFORE.contains(type);
    }

    public static boolean couldDelayAfter(ScheduleType type) {
        return SHOULD_DELAY_AFTER.contains(type);
    }

    private ScheduleType(Class type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public <T> T parse(String duration) {
        return (T)JSON.parseObject((String)duration, (Type)this.type, (Feature[])new Feature[0]);
    }

    public Type getType() {
        return this.type;
    }

    public String getDesc() {
        return this.desc;
    }

    static {
        SHOULD_DELAY_BEFORE = Arrays.asList(DELAY, DELAY_BEFORE, DELAY_AROUND, DELAY_DAYS, DELAY_PHASE);
        SHOULD_DELAY_AFTER = Arrays.asList(DELAY_AFTER, DELAY_AROUND, DELAY_DAYS, DELAY_PHASE);
    }
}

