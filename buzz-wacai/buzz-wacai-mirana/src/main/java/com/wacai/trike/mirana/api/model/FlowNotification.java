/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.constant.FlowType;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import java.io.Serializable;
import java.time.LocalDateTime;

public class FlowNotification
implements Serializable {
    private String flow;
    private FlowType type;
    private String node;
    private Behaviour behaviour;
    private String instanceUuid;
    private String bzKey;
    private InstanceStatus status;
    private LocalDateTime timestamp;

    public String getFlow() {
        return this.flow;
    }

    public FlowType getType() {
        return this.type;
    }

    public String getNode() {
        return this.node;
    }

    public Behaviour getBehaviour() {
        return this.behaviour;
    }

    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    public String getBzKey() {
        return this.bzKey;
    }

    public InstanceStatus getStatus() {
        return this.status;
    }

    public LocalDateTime getTimestamp() {
        return this.timestamp;
    }

    public FlowNotification setFlow(String flow) {
        this.flow = flow;
        return this;
    }

    public FlowNotification setType(FlowType type) {
        this.type = type;
        return this;
    }

    public FlowNotification setNode(String node) {
        this.node = node;
        return this;
    }

    public FlowNotification setBehaviour(Behaviour behaviour) {
        this.behaviour = behaviour;
        return this;
    }

    public FlowNotification setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
        return this;
    }

    public FlowNotification setBzKey(String bzKey) {
        this.bzKey = bzKey;
        return this;
    }

    public FlowNotification setStatus(InstanceStatus status) {
        this.status = status;
        return this;
    }

    public FlowNotification setTimestamp(LocalDateTime timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowNotification)) {
            return false;
        }
        FlowNotification other = (FlowNotification)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        if (this$flow == null ? other$flow != null : !this$flow.equals(other$flow)) {
            return false;
        }
        FlowType this$type = this.getType();
        FlowType other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        String this$node = this.getNode();
        String other$node = other.getNode();
        if (this$node == null ? other$node != null : !this$node.equals(other$node)) {
            return false;
        }
        Behaviour this$behaviour = this.getBehaviour();
        Behaviour other$behaviour = other.getBehaviour();
        if (this$behaviour == null ? other$behaviour != null : !((this$behaviour)).equals(other$behaviour)) {
            return false;
        }
        String this$instanceUuid = this.getInstanceUuid();
        String other$instanceUuid = other.getInstanceUuid();
        if (this$instanceUuid == null ? other$instanceUuid != null : !this$instanceUuid.equals(other$instanceUuid)) {
            return false;
        }
        String this$bzKey = this.getBzKey();
        String other$bzKey = other.getBzKey();
        if (this$bzKey == null ? other$bzKey != null : !this$bzKey.equals(other$bzKey)) {
            return false;
        }
        InstanceStatus this$status = this.getStatus();
        InstanceStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !((this$status)).equals(other$status)) {
            return false;
        }
        LocalDateTime this$timestamp = this.getTimestamp();
        LocalDateTime other$timestamp = other.getTimestamp();
        return !(this$timestamp == null ? other$timestamp != null : !(this$timestamp).equals(other$timestamp));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowNotification;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        FlowType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        String $node = this.getNode();
        result = result * 59 + ($node == null ? 43 : $node.hashCode());
        Behaviour $behaviour = this.getBehaviour();
        result = result * 59 + ($behaviour == null ? 43 : (($behaviour)).hashCode());
        String $instanceUuid = this.getInstanceUuid();
        result = result * 59 + ($instanceUuid == null ? 43 : $instanceUuid.hashCode());
        String $bzKey = this.getBzKey();
        result = result * 59 + ($bzKey == null ? 43 : $bzKey.hashCode());
        InstanceStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
        LocalDateTime $timestamp = this.getTimestamp();
        result = result * 59 + ($timestamp == null ? 43 : ($timestamp).hashCode());
        return result;
    }

    public String toString() {
        return "FlowNotification(flow=" + this.getFlow() + ", type=" + (this.getType()) + ", node=" + this.getNode() + ", behaviour=" + (this.getBehaviour()) + ", instanceUuid=" + this.getInstanceUuid() + ", bzKey=" + this.getBzKey() + ", status=" + (this.getStatus()) + ", timestamp=" + this.getTimestamp() + ")";
    }

    public static enum Behaviour {
        FLOW_START,
        FLOW_END,
        NODE_IN,
        NODE_OUT;

    }
}

