/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.trike.mirana.domain.bu.po.BusinessPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.service.BusinessDTO;
import com.wacai.trike.mirana.service.BusinessRequest;
import com.wacai.trike.mirana.service.BusinessService;

@Service
public class BusinessServiceImpl implements BusinessService {
	@Autowired
	private JPAQueryFactory jpaQueryFactory;

	@Override
	public List<BusinessDTO> listAll(BusinessRequest request) {
		BooleanExpression expression = QBusinessPO.businessPO.createdTime.between(request.getStart(), request.getEnd());
		if (!CollectionUtils.isEmpty(request.getIds())) {
			expression = expression.and((Predicate) QBusinessPO.businessPO.id.in(request.getIds()));
		} else {
			if (!CollectionUtils.isEmpty(request.getCodes())) {
				expression = this.bind(expression, request.getCodes(), QBusinessPO.businessPO.code, true);
			}
			if (!CollectionUtils.isEmpty(request.getNames())) {
				expression = this.bind(expression, request.getNames(), QBusinessPO.businessPO.name, true);
			}
		}
		List<BusinessPO> businessPOS = this.jpaQueryFactory.selectFrom(QBusinessPO.businessPO)
				.where((Predicate) expression).fetch();
		return businessPOS.stream().map(BusinessPO::toDTO).collect(Collectors.toList());
	}
}
