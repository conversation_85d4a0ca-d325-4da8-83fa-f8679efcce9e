/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 *  com.wacai.hermes.agent.producer.HermesProducer
 *  com.wacai.hermes.common.message.HermesMessage
 *  com.wacai.ocean.seraph.manager.api.dto.SeraphJobDTO
 *  com.wacai.trike.mirana.api.model.FlowBaseRequest
 *  com.wacai.trike.mirana.api.model.FlowProcessRequest
 *  com.wacai.trike.mirana.api.model.FlowStartRequest
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 */
package com.wacai.trike.mirana.Seraph;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.hermes.agent.producer.HermesProducer;
import com.wacai.hermes.common.message.HermesMessage;
import com.wacai.ocean.seraph.manager.api.dto.SeraphJobDTO;
import com.wacai.trike.mirana.Seraph.SeraphProperties;
import com.wacai.trike.mirana.api.model.FlowBaseRequest;
import com.wacai.trike.mirana.api.model.FlowProcessRequest;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.repository.DelayedEventRepository;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.EventAgency;
import com.wacai.trike.mirana.metrics.DelayedEventMetrics;
import com.wacai.trike.mirana.metrics.MetricsProvider;
import com.wacai.trike.mirana.util.DistributeSequenceIdGenerator;
import com.wacai.trike.mirana.util.ObjectMappers;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Random;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class SeraphSender {
    private static final Logger log = LoggerFactory.getLogger(SeraphSender.class);
    @Autowired
    private HermesProducer producer;
    @Autowired
    private MetricsProvider metricsProvider;
    @Autowired
    private DelayedEventRepository eventRepository;
    @Autowired
    private SeraphProperties properties;
    @Autowired
    private DistributeSequenceIdGenerator idGenerator;
    @Autowired
    private EventAgency eventAgency;
    private final Random random = new Random();

    public void publishDelayEvent(Metadata metadata) {
        if (metadata.getType() == null) {
            return;
        }
        switch (metadata.getType()) {
            case NODE_EXECUTE_DELAY: {
                this.publishDelayEvent(metadata.getExpectTime(), metadata.getEvent());
                return;
            }
            case FLOW_START_DELAY: {
                this.publishDelayEvent(metadata.getExpectTime(), metadata.getRequest(), metadata.isBalance());
                return;
            }
        }
        log.error("should never occurred");
    }

    private void publishDelayEvent(LocalDateTime expectTime, FlowBaseRequest request, boolean balance) {
        long seconds = Duration.between(LocalDateTime.now(), expectTime).toMillis() / 1000L;
        if (seconds <= 0L) {
            log.info("Non-compliant delay time: discarded flow request {}", ObjectMappers.mustWriteValue(request));
            return;
        }
        if (!(request instanceof FlowStartRequest) && !(request instanceof FlowProcessRequest)) {
            log.error("Non-compliant class type: discarded flow request {}", ObjectMappers.mustWriteValue(request));
            return;
        }
        if (balance) {
            expectTime = expectTime.plusMinutes(this.random.nextInt(30));
        }
        log.info("delaying handler {} after {} seconds", ObjectMappers.mustWriteValue(request), seconds);
        SeraphJobDTO job = this.buildJob(Math.toIntExact(seconds), Type.FLOW_START_DELAY);
        job.setSeraphJobBody(ObjectMappers.mustWriteValue(request));
        this.persistence(request, job, expectTime);
        this.produce(job);
    }

    private void persistence(FlowBaseRequest request, SeraphJobDTO job, LocalDateTime expectTime) {
        DelayedEventPO delayedEvent = new DelayedEventPO();
        delayedEvent.setParam(ObjectMappers.mustWriteValue(request));
        delayedEvent.setExpectFireTime(expectTime);
        delayedEvent.setStatus(DelayedEventStatus.LOADED);
        delayedEvent.setType(request.getClass().getName());
        if (request instanceof FlowStartRequest) {
            FlowStartRequest start = (FlowStartRequest)request;
            job.setSeraphJobCode(start.getUuid());
            delayedEvent.setUuid(start.getUuid());
            delayedEvent.setInstanceUuid(start.getUuid());
            delayedEvent.setTaskId(start.getUuid());
        }
        if (request instanceof FlowProcessRequest) {
            FlowProcessRequest process = (FlowProcessRequest)request;
            job.setSeraphJobCode(process.getTaskInstanceId());
            delayedEvent.setUuid(String.valueOf(this.idGenerator.nextId()));
            delayedEvent.setInstanceUuid(process.getInstanceId());
            delayedEvent.setTaskId(process.getTaskInstanceId());
        }
        this.eventRepository.save(delayedEvent);
    }

    private void persistence(AbstractFlowEvent event, LocalDateTime expectTime) {
        DelayedEventPO delayedEvent = new DelayedEventPO();
        delayedEvent.setParam(ObjectMappers.mustWriteValue(event));
        delayedEvent.setExpectFireTime(expectTime);
        delayedEvent.setStatus(DelayedEventStatus.LOADED);
        delayedEvent.setType(event.getClass().getName());
        delayedEvent.setUuid(event.getUuid());
        delayedEvent.setInstanceUuid(event.getInstanceUuid());
        delayedEvent.setTaskId(event.getTaskId());
        this.eventRepository.save(delayedEvent);
    }

    public void publishDelayEvent(LocalDateTime expectTime, AbstractFlowEvent event) {
        try {
            long seconds = Duration.between(LocalDateTime.now(), expectTime).toMillis() / 1000L;
            if (seconds <= 0L) {
                this.eventAgency.agent(event, false);
                return;
            }
            log.info("delaying handler {} after {} seconds", ObjectMappers.mustWriteValue(event), seconds);
            SeraphJobDTO job = this.buildJob(Math.toIntExact(seconds), Type.NODE_EXECUTE_DELAY);
            job.setSeraphJobCode(event.getUuid());
            job.setSeraphJobBody(ObjectMappers.mustWriteValue(event));
            this.persistence(event, expectTime);
            this.produce(job);
            this.metrics(DelayedEventMetrics.build(event, expectTime, DelayedEventMetrics.Category.PUBLISHED));
            log.info("publishDelayEvent success! expectTime [{}], event [{}]", expectTime.toString(), event.toString());
        }
        catch (Exception e) {
            log.error("publishDelayEvent error!", (Throwable)e);
        }
    }

    private void produce(SeraphJobDTO job) {
        HermesMessage message = HermesMessage.builder().setTopic(this.properties.getTopic()).setData(ObjectMappers.mustWriteValue(job).getBytes()).build();
        this.producer.produce(message);
    }

    private void metrics(DelayedEventMetrics metrics) {
        this.metricsProvider.metricsDelayedEvent(metrics);
    }

    private SeraphJobDTO buildJob(int delay, Type type) {
        SeraphJobDTO job = new SeraphJobDTO();
        job.setSeraphAppCode(this.properties.getAppCode());
        job.setSeraphTemplateCode(this.properties.getTeeth().get(type.name()).getTemplate());
        job.setSeraphJobDelay(Integer.valueOf(delay));
        return job;
    }

    @JsonInclude(value=JsonInclude.Include.NON_NULL)
    public static class Metadata {
        private final Type type;
        private final LocalDateTime expectTime;
        private final AbstractFlowEvent event;
        private final FlowBaseRequest request;
        private final boolean balance;

        private Metadata(Type type, LocalDateTime expectTime, AbstractFlowEvent event, FlowBaseRequest request, boolean balance) {
            this.type = type;
            this.expectTime = expectTime;
            this.event = event;
            this.request = request;
            this.balance = balance;
        }

        public Metadata(LocalDateTime expectTime, AbstractFlowEvent event) {
            this(Type.NODE_EXECUTE_DELAY, expectTime, event, null, false);
        }

        public Metadata(LocalDateTime expectTime, FlowBaseRequest request) {
            this(Type.FLOW_START_DELAY, expectTime, null, request, false);
        }

        public Metadata(LocalDateTime expectTime, FlowBaseRequest request, boolean balance) {
            this(Type.FLOW_START_DELAY, expectTime, null, request, balance);
        }

        public Type getType() {
            return this.type;
        }

        public LocalDateTime getExpectTime() {
            return this.expectTime;
        }

        public AbstractFlowEvent getEvent() {
            return this.event;
        }

        public FlowBaseRequest getRequest() {
            return this.request;
        }

        public boolean isBalance() {
            return this.balance;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof Metadata)) {
                return false;
            }
            Metadata other = (Metadata)o;
            if (!other.canEqual(this)) {
                return false;
            }
            Type this$type = this.getType();
            Type other$type = other.getType();
            if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
                return false;
            }
            LocalDateTime this$expectTime = this.getExpectTime();
            LocalDateTime other$expectTime = other.getExpectTime();
            if (this$expectTime == null ? other$expectTime != null : !(this$expectTime).equals(other$expectTime)) {
                return false;
            }
            AbstractFlowEvent this$event = this.getEvent();
            AbstractFlowEvent other$event = other.getEvent();
            if (this$event == null ? other$event != null : !this$event.equals(other$event)) {
                return false;
            }
            FlowBaseRequest this$request = this.getRequest();
            FlowBaseRequest other$request = other.getRequest();
            if (this$request == null ? other$request != null : !this$request.equals(other$request)) {
                return false;
            }
            return this.isBalance() == other.isBalance();
        }

        protected boolean canEqual(Object other) {
            return other instanceof Metadata;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            Type $type = this.getType();
            result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
            LocalDateTime $expectTime = this.getExpectTime();
            result = result * 59 + ($expectTime == null ? 43 : ($expectTime).hashCode());
            AbstractFlowEvent $event = this.getEvent();
            result = result * 59 + ($event == null ? 43 : $event.hashCode());
            FlowBaseRequest $request = this.getRequest();
            result = result * 59 + ($request == null ? 43 : $request.hashCode());
            result = result * 59 + (this.isBalance() ? 79 : 97);
            return result;
        }

        public String toString() {
            return "SeraphSender.Metadata(type=" + (this.getType()) + ", expectTime=" + this.getExpectTime() + ", event=" + this.getEvent() + ", request=" + this.getRequest() + ", balance=" + this.isBalance() + ")";
        }
    }

    public static enum Type {
        FLOW_START_DELAY,
        NODE_EXECUTE_DELAY;

    }
}

