/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.context.repository;

import com.wacai.trike.mirana.domain.context.po.TaskInstancePO;
import com.wacai.trike.mirana.task.TaskStatus;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;

public interface TaskInstanceRepository
extends JpaRepository<TaskInstancePO, Long> {
    public List<TaskInstancePO> findByInstanceUuidAndNodeIdAndStatus(String var1, Long var2, TaskStatus var3);

    public List<TaskInstancePO> findByInstanceUuid(String var1);

    public TaskInstancePO findTop1ByInstanceUuidAndNodeIdOrderByCreatedTimeDesc(String var1, Long var2);

    public Optional<TaskInstancePO> findByUuid(String var1);
}

