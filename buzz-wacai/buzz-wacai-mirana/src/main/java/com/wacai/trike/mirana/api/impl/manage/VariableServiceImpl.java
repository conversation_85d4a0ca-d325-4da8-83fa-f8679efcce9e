/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.fasterxml.jackson.databind.ObjectMapper
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.jpa.JPQLQuery
 *  com.querydsl.jpa.JPQLQueryFactory
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.ComboBox
 *  com.wacai.loan.trike.common.model.Page
 *  com.wacai.loan.trike.common.model.Query
 *  com.wacai.trike.mirana.api.constant.CandidateType
 *  com.wacai.trike.mirana.api.constant.VariableCategory
 *  com.wacai.trike.mirana.api.manage.VariableService
 *  com.wacai.trike.mirana.api.manage.model.VariableComboModel
 *  com.wacai.trike.mirana.api.manage.model.VariableModel
 *  com.wacai.trike.mirana.api.manage.model.VariableQueryModel
 *  javax.validation.Valid
 *  javax.validation.constraints.NotNull
 *  org.apache.commons.lang3.StringUtils
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.data.domain.Page
 *  org.springframework.data.domain.PageRequest
 *  org.springframework.data.domain.Pageable
 *  org.springframework.util.CollectionUtils
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl.manage;

import com.alibaba.dubbo.config.annotation.Service;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.JPQLQueryFactory;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.loan.trike.common.model.Query;
import com.wacai.trike.mirana.api.constant.CandidateType;
import com.wacai.trike.mirana.api.constant.VariableCategory;
import com.wacai.trike.mirana.api.manage.VariableService;
import com.wacai.trike.mirana.api.manage.model.VariableComboModel;
import com.wacai.trike.mirana.api.manage.model.VariableModel;
import com.wacai.trike.mirana.api.manage.model.VariableQueryModel;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.manage.po.VariableConfigPO;
import com.wacai.trike.mirana.domain.manage.repository.VariableConfigRepository;
import com.wacai.trike.mirana.util.HttpUtil;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Service(interfaceClass=VariableService.class)
public class VariableServiceImpl implements VariableService {
    private static final Logger log = LoggerFactory.getLogger(VariableServiceImpl.class);
    private static final TypeReference COMBO_REFERENCE = new TypeReference<List<ComboBox<String>>>(){};
    @Autowired
    private ObjectMapper objectMapper;
    @Autowired
    private VariableConfigRepository repository;
    @Autowired
    private JPQLQueryFactory queryFactory;

    public ApiResponse<com.wacai.loan.trike.common.model.Page<VariableModel>> queryVariables(VariableQueryModel queryModel) {
        if (StringUtils.isNotBlank((CharSequence)queryModel.getBu()) && StringUtils.isNotBlank((CharSequence)queryModel.getApp())) {
			long appId = (Long) ((JPQLQuery) this.queryFactory.select(QApplicationPO.applicationPO.id)
					.from(new EntityPath[] { QApplicationPO.applicationPO }).join((EntityPath) QBusinessPO.businessPO)
					.on(new Predicate[] { QApplicationPO.applicationPO.buId.eq(QBusinessPO.businessPO.id) })
					.where(new Predicate[] { QApplicationPO.applicationPO.code
							.eq(queryModel.getApp()).and((Predicate)QBusinessPO.businessPO.code.eq(queryModel.getBu()))})).fetchOne();
            return this.queryVariable(appId, queryModel.getCategory(), (Query)queryModel);
        }
        if (!CollectionUtils.isEmpty((Collection)queryModel.getIds())) {
            List vms = this.repository.findAllById(queryModel.getIds()).stream().map(this::convert).collect(Collectors.toList());
            return ApiResponse.success(new com.wacai.loan.trike.common.model.Page((long)vms.size(), 1L, vms));
        }
        return ApiResponse.error((String)"query condition not meet");
    }

    public ApiResponse<com.wacai.loan.trike.common.model.Page<VariableModel>> queryVariable(Long appId, VariableCategory category, Query query) {
        try {
            PageRequest pageable = PageRequest.of((int)((int)query.getPageIndex() - 1), (int)((int)query.getPageSize()));
            Page<VariableConfigPO> variables = Objects.isNull(category) ? this.repository.findByAppId(appId, (Pageable)pageable) : this.repository.findByAppIdAndCategory(appId, category, (Pageable)pageable);
            return ApiResponse.success(this.convert(variables));
        }
        catch (Exception e) {
            log.error("get variables for app {} failed", appId, e);
            return ApiResponse.error((String)e.getMessage());
        }
    }

    private com.wacai.loan.trike.common.model.Page<VariableModel> convert(Page<VariableConfigPO> page) {
        Page pageModel = page.map(this::convert);
        return new com.wacai.loan.trike.common.model.Page(pageModel.getTotalElements(), (long)pageModel.getTotalPages(), pageModel.getContent());
    }

    private VariableModel convert(VariableConfigPO var) {
        VariableModel model = new VariableModel();
        BeanUtils.copyProperties(var, model);
        model.setDataType(Objects.toString(var.getDataType()));
        model.setLabel(var.getName());
        model.setValue(var.getId().toString());
        try {
            if (CandidateType.DEFINITION == var.getCandidateType()) {
                List combo = (List)this.objectMapper.readValue(var.getCandidateContent(), COMBO_REFERENCE);
                model.setCandidateContent(combo);
            } else if (CandidateType.REMOTE == var.getCandidateType()) {
                String content = HttpUtil.get(var.getCandidateContent());
                List combo = (List)this.objectMapper.readValue(content, COMBO_REFERENCE);
                model.setCandidateContent(combo);
            }
        }
        catch (Exception e) {
            log.error("convert variable to model failed, {} ", var, e);
            throw new RuntimeException(e);
        }
        return model;
    }

    public ApiResponse<VariableModel> get(@PathVariable(value="id") Long id) {
        return ApiResponse.success(this.repository.findById(id).map(this::convert).orElse(null));
    }

    public ApiResponse<VariableModel> get(@PathVariable(value="appId") Long appId, @PathVariable(value="code") String code) {
        return ApiResponse.success(this.repository.findByAppIdAndCode(appId, code).map(this::convert).orElse(null));
    }

    public ApiResponse<Long> create(@RequestBody @NotNull @Valid VariableModel model) {
        try {
            VariableConfigPO variable = this.validate(model);
            BeanUtils.copyProperties(model, variable);
            if (model.getCandidateType() == CandidateType.DEFINITION) {
                variable.setCandidateContent(this.objectMapper.writeValueAsString(model.getCandidateContent()));
            }
            return ApiResponse.success(((VariableConfigPO)this.repository.save(variable)).getId());
        }
        catch (Exception e) {
            log.error("create variable failed, {}", model, e);
            return ApiResponse.error((String)e.getMessage());
        }
    }

    private VariableConfigPO validate(VariableModel model) {
        if (Objects.isNull(model.getId())) {
            if (Objects.nonNull(model.getAppId()) && this.repository.existsByAppIdAndCode(model.getAppId(), model.getCode())) {
                throw new RuntimeException("\u53d8\u91cf\u5df2\u5b58\u5728");
            }
            if (Objects.nonNull(model.getFlowId()) && this.repository.existsByFlowIdAndCode(model.getFlowId(), model.getCode())) {
                throw new RuntimeException("\u53d8\u91cf\u5df2\u5b58\u5728");
            }
        }
        return Optional.ofNullable(model.getId()).map(arg_0 -> ((VariableConfigRepository)this.repository).findById(arg_0)).filter(Optional::isPresent).map(Optional::get).orElse(new VariableConfigPO());
    }

    public ApiResponse<Void> delete(@PathVariable(value="id") Long id) {
        this.repository.deleteById(id);
        return ApiResponse.success(null);
    }

    public ApiResponse<List<VariableComboModel>> combo(VariableQueryModel queryModel) {
        queryModel.setPageSize(Integer.MAX_VALUE);
        queryModel.setPageIndex(1L);
        ApiResponse<com.wacai.loan.trike.common.model.Page<VariableModel>> vm = this.queryVariables(queryModel);
        if (vm.success()) {
            return ApiResponse.success(this.convert(((com.wacai.loan.trike.common.model.Page)vm.getData()).getData()));
        }
        return ApiResponse.error((String)vm.getError());
    }

    private List<VariableComboModel> convert(List<VariableModel> models) {
        return models.stream().map(VariableComboModel::of).collect(Collectors.toList());
    }

    public ApiResponse<List<VariableComboModel>> actionCombo(@RequestParam(value="bu") String bu, @RequestParam(value="app") String app) {
        VariableQueryModel queryModel = new VariableQueryModel();
        queryModel.setBu(bu);
        queryModel.setApp(app);
        queryModel.setCategory(VariableCategory.ACTION);
        return this.combo(queryModel);
    }

    public ApiResponse<List<VariableComboModel>> actionCombo(@PathVariable(value="appId") Long appId) {
        Query query = new Query().setPageIndex(1L).setPageSize(Integer.MAX_VALUE);
        ApiResponse<com.wacai.loan.trike.common.model.Page<VariableModel>> vm = this.queryVariable(appId, VariableCategory.ACTION, query);
        if (vm.success()) {
            return ApiResponse.success(this.convert(((com.wacai.loan.trike.common.model.Page)vm.getData()).getData()));
        }
        return ApiResponse.error((String)vm.getError());
    }

    public ApiResponse<List<VariableComboModel>> expressionCombo(@RequestParam(value="bu") String bu, @RequestParam(value="app") String app) {
        VariableQueryModel queryModel = new VariableQueryModel();
        queryModel.setBu(bu);
        queryModel.setApp(app);
        queryModel.setCategory(VariableCategory.EXPRESSION);
        return this.combo(queryModel);
    }

    public ApiResponse<List<VariableComboModel>> expressionCombo(@PathVariable(value="appId") Long appId) {
        Query query = new Query().setPageIndex(1L).setPageSize(Integer.MAX_VALUE);
        ApiResponse<com.wacai.loan.trike.common.model.Page<VariableModel>> vm = this.queryVariable(appId, VariableCategory.EXPRESSION, query);
        if (vm.success()) {
            return ApiResponse.success(this.convert(((com.wacai.loan.trike.common.model.Page)vm.getData()).getData()));
        }
        return ApiResponse.error((String)vm.getError());
    }
}

