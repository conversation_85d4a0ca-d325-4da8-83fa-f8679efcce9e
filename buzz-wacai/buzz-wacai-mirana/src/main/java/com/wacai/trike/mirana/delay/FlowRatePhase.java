/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.delay;

import com.wacai.trike.mirana.delay.Phase;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public enum FlowRatePhase {
    C(0L, "C"),
    C_M1(1L, "C-M1"),
    M1_M2(2L, "M1-M2"),
    M2_M3(3L, "M2-M3"),
    M3_M4(4L, "M3-M4"),
    M4_M5(5L, "M4-M5"),
    M5_M6(5L, "M5-M6"),
    M6_(7L, "M6+"),
    M6_M7(8L, "M6-M7"),
    M7_M8(9L, "M7-M8"),
    M8_M9(10L, "M8-M9"),
    M9_M10(11L, "M9-M10"),
    M10_M11(12L, "M10-M11"),
    M11_M12(13L, "M11-M12"),
    M12_(14L, "M12+");

    private long order;
    private String value;
    private static Map<Phase, Long> maxOvdMap;

    private FlowRatePhase(long order, String value) {
        this.order = order;
        this.value = value;
    }

    public static FlowRatePhase getFlowRatePhase(long daysPastDue) {
        return FlowRatePhase.getFlowRatePhase(daysPastDue, LocalDateTime.now());
    }

    public static FlowRatePhase getFlowRatePhase(long daysPastDue, LocalDateTime date) {
        int nowDay = date.getDayOfMonth();
        int maxDay = date.getMonth().maxLength();
        return FlowRatePhase.getFlowRatePhase(daysPastDue, nowDay, maxDay);
    }

    private static FlowRatePhase getFlowRatePhase(long daysPastDue, int nowDay, int maxDay) {
        if (daysPastDue <= 0L) {
            return C;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M12) - (long)maxDay + (long)nowDay) {
            return M12_;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M11) - (long)maxDay + (long)nowDay) {
            return M11_M12;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M10) - (long)maxDay + (long)nowDay) {
            return M10_M11;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M9) - (long)maxDay + (long)nowDay) {
            return M9_M10;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M8) - (long)maxDay + (long)nowDay) {
            return M8_M9;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M7) - (long)maxDay + (long)nowDay) {
            return M7_M8;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M6) - (long)maxDay + (long)nowDay) {
            return M6_M7;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M5) - (long)maxDay + (long)nowDay) {
            return M5_M6;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M4) - (long)maxDay + (long)nowDay) {
            return M4_M5;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M3) - (long)maxDay + (long)nowDay) {
            return M3_M4;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M2) - (long)maxDay + (long)nowDay) {
            return M2_M3;
        }
        if (daysPastDue > maxOvdMap.get(Phase.M1) - (long)maxDay + (long)nowDay) {
            return M1_M2;
        }
        return C_M1;
    }

    public static FlowRatePhase getByValue(String value) {
        for (FlowRatePhase item : FlowRatePhase.values()) {
            if (!item.getValue().equals(value)) continue;
            return item;
        }
        throw new IllegalArgumentException("not support value : " + value);
    }

    public static FlowRatePhase getByKey(String key) {
        for (FlowRatePhase item : FlowRatePhase.values()) {
            if (!item.name().equals(key)) continue;
            return item;
        }
        return null;
    }

    public static long getMinDaysPastDue(Phase phase) {
        Long daysPastDue = maxOvdMap.get(phase);
        if (Objects.isNull(daysPastDue)) {
            return Long.MAX_VALUE;
        }
        return daysPastDue + 1L;
    }

    public static FlowRatePhase min() {
        return C;
    }

    public static FlowRatePhase max() {
        return M12_;
    }

    public long getOrder() {
        return this.order;
    }

    public String getValue() {
        return this.value;
    }

    static {
        maxOvdMap = new HashMap<Phase, Long>();
        maxOvdMap.put(Phase.M1, 29L);
        maxOvdMap.put(Phase.M2, 59L);
        maxOvdMap.put(Phase.M3, 89L);
        maxOvdMap.put(Phase.M4, 119L);
        maxOvdMap.put(Phase.M5, 149L);
        maxOvdMap.put(Phase.M6, 179L);
        maxOvdMap.put(Phase.M7, 209L);
        maxOvdMap.put(Phase.M8, 239L);
        maxOvdMap.put(Phase.M9, 269L);
        maxOvdMap.put(Phase.M10, 299L);
        maxOvdMap.put(Phase.M11, 329L);
        maxOvdMap.put(Phase.M12, 359L);
    }
}

