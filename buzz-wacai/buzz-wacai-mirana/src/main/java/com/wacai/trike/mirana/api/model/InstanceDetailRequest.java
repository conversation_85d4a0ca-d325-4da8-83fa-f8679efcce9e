/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.model.NodeQueryRequest;
import javax.validation.constraints.NotBlank;

public class InstanceDetailRequest
extends NodeQueryRequest {
    @NotBlank
    private String instanceId;

    public String getInstanceId() {
        return this.instanceId;
    }

    public InstanceDetailRequest setInstanceId(String instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    @Override
    public String toString() {
        return "InstanceDetailRequest(instanceId=" + this.getInstanceId() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstanceDetailRequest)) {
            return false;
        }
        InstanceDetailRequest other = (InstanceDetailRequest)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$instanceId = this.getInstanceId();
        String other$instanceId = other.getInstanceId();
        return !(this$instanceId == null ? other$instanceId != null : !this$instanceId.equals(other$instanceId));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof InstanceDetailRequest;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $instanceId = this.getInstanceId();
        result = result * 59 + ($instanceId == null ? 43 : $instanceId.hashCode());
        return result;
    }
}

