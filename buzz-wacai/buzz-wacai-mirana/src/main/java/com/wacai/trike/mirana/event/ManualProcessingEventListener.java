/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.TaskExecuteType
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.DerailmentHandler;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.api.constant.TaskExecuteType;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;

@Component
public class ManualProcessingEventListener
		implements EventListener<ManualProcessingEvent, NodeProcessedEvent> {
	
	private static final Logger log = LogManager.getLogger(ManualProcessingEventListener.class);
	@Autowired
	private FlowGraphService graphService;
	@Autowired
	private NodeExecutor nodeExecutor;

	@Override
	public Class<ManualProcessingEvent> accept() {
		return ManualProcessingEvent.class;
	}

	@Override
	@Barrier(handlers = { DerailmentHandler.class, InstanceContextSyncHandler.class })
	public void doProcess(EventExchange<ManualProcessingEvent, NodeProcessedEvent> exchange) {
		InstanceContext context = exchange.getContext();
		ManualProcessingEvent event = exchange.getCurrent();
		
		Node node = this.graphService.getFlowGraph(context.getFlowId()).getNode(context.getCurrentNodeId());
		if (TaskExecuteType.MANUAL != node.getTaskExecuteType()) {
			log.warn("this node is not manual execute, can not be manual process, uuid: [{}], node [{}-{}]",
					 context.getUuid(),  node.getName(),  node.getId());
			exchange.setNext(null);
			return;
		}
		
		context.addVariable(event.getParam(), VariableType.NODE_MANUAL);
		this.nodeExecutor.execute(exchange);
	}

	public int getOrder() {
		return 8;
	}
}
