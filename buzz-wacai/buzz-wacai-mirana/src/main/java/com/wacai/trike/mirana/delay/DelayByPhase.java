/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.delay;

import com.wacai.trike.mirana.delay.DelayCalculator;
import com.wacai.trike.mirana.delay.FlowRatePhase;
import com.wacai.trike.mirana.delay.SpecUtil;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;

public class DelayByPhase
implements Serializable,
DelayCalculator {
    private FlowRatePhase in;
    private String inSpec = "07:00:00";
    private FlowRatePhase out;
    private String outSpec = "23:59:59";

    @Override
    public LocalDateTime expectStart(LocalDateTime referDate) {
        if (Objects.isNull(referDate)) {
            return null;
        }
        FlowRatePhase min = FlowRatePhase.min();
        if (this.in.equals(min)) {
            return LocalDateTime.MIN;
        }
        LocalDateTime inTime = referDate;
        long daysPastDue = 0L;
        FlowRatePhase prePhase = min;
        while (!prePhase.equals(this.in)) {
            inTime = inTime.plusDays(1L);
            prePhase = FlowRatePhase.getFlowRatePhase(++daysPastDue, inTime);
        }
        return SpecUtil.setSpecWithMinNano(inTime, this.inSpec);
    }

    @Override
    public LocalDateTime expectEnd(LocalDateTime referDate) {
        if (Objects.isNull(referDate)) {
            return null;
        }
        if (this.out == FlowRatePhase.max()) {
            return LocalDateTime.MAX;
        }
        LocalDateTime outTime = referDate;
        long daysPastDue = 0L;
        FlowRatePhase prePhase = FlowRatePhase.min();
        while (prePhase.getOrder() <= this.out.getOrder()) {
            outTime = outTime.plusDays(1L);
            prePhase = FlowRatePhase.getFlowRatePhase(++daysPastDue, outTime);
        }
        outTime = outTime.minusDays(1L);
        return SpecUtil.setSpecWithMaxNano(outTime, this.outSpec);
    }

    public FlowRatePhase getIn() {
        return this.in;
    }

    public String getInSpec() {
        return this.inSpec;
    }

    public FlowRatePhase getOut() {
        return this.out;
    }

    public String getOutSpec() {
        return this.outSpec;
    }

    public void setIn(FlowRatePhase in) {
        this.in = in;
    }

    public void setInSpec(String inSpec) {
        this.inSpec = inSpec;
    }

    public void setOut(FlowRatePhase out) {
        this.out = out;
    }

    public void setOutSpec(String outSpec) {
        this.outSpec = outSpec;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DelayByPhase)) {
            return false;
        }
        DelayByPhase other = (DelayByPhase)o;
        if (!other.canEqual(this)) {
            return false;
        }
        FlowRatePhase this$in = this.getIn();
        FlowRatePhase other$in = other.getIn();
        if (this$in == null ? other$in != null : !((this$in)).equals(other$in)) {
            return false;
        }
        String this$inSpec = this.getInSpec();
        String other$inSpec = other.getInSpec();
        if (this$inSpec == null ? other$inSpec != null : !this$inSpec.equals(other$inSpec)) {
            return false;
        }
        FlowRatePhase this$out = this.getOut();
        FlowRatePhase other$out = other.getOut();
        if (this$out == null ? other$out != null : !((this$out)).equals(other$out)) {
            return false;
        }
        String this$outSpec = this.getOutSpec();
        String other$outSpec = other.getOutSpec();
        return !(this$outSpec == null ? other$outSpec != null : !this$outSpec.equals(other$outSpec));
    }

    protected boolean canEqual(Object other) {
        return other instanceof DelayByPhase;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        FlowRatePhase $in = this.getIn();
        result = result * 59 + ($in == null ? 43 : (($in)).hashCode());
        String $inSpec = this.getInSpec();
        result = result * 59 + ($inSpec == null ? 43 : $inSpec.hashCode());
        FlowRatePhase $out = this.getOut();
        result = result * 59 + ($out == null ? 43 : (($out)).hashCode());
        String $outSpec = this.getOutSpec();
        result = result * 59 + ($outSpec == null ? 43 : $outSpec.hashCode());
        return result;
    }

    public String toString() {
        return "DelayByPhase(in=" + (this.getIn()) + ", inSpec=" + this.getInSpec() + ", out=" + (this.getOut()) + ", outSpec=" + this.getOutSpec() + ")";
    }
}

