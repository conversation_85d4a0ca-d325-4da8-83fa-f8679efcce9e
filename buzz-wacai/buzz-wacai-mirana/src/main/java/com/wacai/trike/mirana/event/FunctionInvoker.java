/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.Function
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.api.constant.Function;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public final class FunctionInvoker {
    private static final ConcurrentMap<Function, Invoker> INVOKERS = new ConcurrentHashMap<Function, Invoker>();

    public static String invoke(Function function, String value, String args) {
        return ((Invoker)INVOKERS.get(function)).invoke(value, args);
    }

    static {
        INVOKERS.put(Function.MOD, new ModInvoker());
    }

    static class ModInvoker
    implements Invoker {
        ModInvoker() {
        }

        @Override
        public String invoke(String value, String args) {
            Long v = Long.valueOf(value);
            return Long.toString(v % Long.valueOf(args));
        }
    }

    static interface Invoker {
        public String invoke(String var1, String var2);
    }
}

