/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.model.EventModel
 */
package com.wacai.trike.mirana.elasticsearch.service.dto;

import com.wacai.trike.mirana.api.model.EventModel;
import java.io.Serializable;
import java.util.Date;
import java.util.List;

public class EsFlowTaskInstanceQuery
implements Serializable {
    private Date startDate;
    private Date endDate;
    private String flowVersion;
    private Long flowId;
    private String flowCode;
    private Long loanId;
    private List<String> tags;
    private String storyCode;
    private List<Long> loanIdList;
    private List<Long> nodeIdList;
    private List<EventModel> eventList;

    public Date getStartDate() {
        return this.startDate;
    }

    public Date getEndDate() {
        return this.endDate;
    }

    public String getFlowVersion() {
        return this.flowVersion;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public String getFlowCode() {
        return this.flowCode;
    }

    public Long getLoanId() {
        return this.loanId;
    }

    public List<String> getTags() {
        return this.tags;
    }

    public String getStoryCode() {
        return this.storyCode;
    }

    public List<Long> getLoanIdList() {
        return this.loanIdList;
    }

    public List<Long> getNodeIdList() {
        return this.nodeIdList;
    }

    public List<EventModel> getEventList() {
        return this.eventList;
    }

    public void setStartDate(Date startDate) {
        this.startDate = startDate;
    }

    public void setEndDate(Date endDate) {
        this.endDate = endDate;
    }

    public void setFlowVersion(String flowVersion) {
        this.flowVersion = flowVersion;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setFlowCode(String flowCode) {
        this.flowCode = flowCode;
    }

    public void setLoanId(Long loanId) {
        this.loanId = loanId;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public void setStoryCode(String storyCode) {
        this.storyCode = storyCode;
    }

    public void setLoanIdList(List<Long> loanIdList) {
        this.loanIdList = loanIdList;
    }

    public void setNodeIdList(List<Long> nodeIdList) {
        this.nodeIdList = nodeIdList;
    }

    public void setEventList(List<EventModel> eventList) {
        this.eventList = eventList;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EsFlowTaskInstanceQuery)) {
            return false;
        }
        EsFlowTaskInstanceQuery other = (EsFlowTaskInstanceQuery)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Date this$startDate = this.getStartDate();
        Date other$startDate = other.getStartDate();
        if (this$startDate == null ? other$startDate != null : !(this$startDate).equals(other$startDate)) {
            return false;
        }
        Date this$endDate = this.getEndDate();
        Date other$endDate = other.getEndDate();
        if (this$endDate == null ? other$endDate != null : !(this$endDate).equals(other$endDate)) {
            return false;
        }
        String this$flowVersion = this.getFlowVersion();
        String other$flowVersion = other.getFlowVersion();
        if (this$flowVersion == null ? other$flowVersion != null : !this$flowVersion.equals(other$flowVersion)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$flowCode = this.getFlowCode();
        String other$flowCode = other.getFlowCode();
        if (this$flowCode == null ? other$flowCode != null : !this$flowCode.equals(other$flowCode)) {
            return false;
        }
        Long this$loanId = this.getLoanId();
        Long other$loanId = other.getLoanId();
        if (this$loanId == null ? other$loanId != null : !(this$loanId).equals(other$loanId)) {
            return false;
        }
        List<String> this$tags = this.getTags();
        List<String> other$tags = other.getTags();
        if (this$tags == null ? other$tags != null : !(this$tags).equals(other$tags)) {
            return false;
        }
        String this$storyCode = this.getStoryCode();
        String other$storyCode = other.getStoryCode();
        if (this$storyCode == null ? other$storyCode != null : !this$storyCode.equals(other$storyCode)) {
            return false;
        }
        List<Long> this$loanIdList = this.getLoanIdList();
        List<Long> other$loanIdList = other.getLoanIdList();
        if (this$loanIdList == null ? other$loanIdList != null : !(this$loanIdList).equals(other$loanIdList)) {
            return false;
        }
        List<Long> this$nodeIdList = this.getNodeIdList();
        List<Long> other$nodeIdList = other.getNodeIdList();
        if (this$nodeIdList == null ? other$nodeIdList != null : !(this$nodeIdList).equals(other$nodeIdList)) {
            return false;
        }
        List<EventModel> this$eventList = this.getEventList();
        List<EventModel> other$eventList = other.getEventList();
        return !(this$eventList == null ? other$eventList != null : !(this$eventList).equals(other$eventList));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EsFlowTaskInstanceQuery;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Date $startDate = this.getStartDate();
        result = result * 59 + ($startDate == null ? 43 : ($startDate).hashCode());
        Date $endDate = this.getEndDate();
        result = result * 59 + ($endDate == null ? 43 : ($endDate).hashCode());
        String $flowVersion = this.getFlowVersion();
        result = result * 59 + ($flowVersion == null ? 43 : $flowVersion.hashCode());
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $flowCode = this.getFlowCode();
        result = result * 59 + ($flowCode == null ? 43 : $flowCode.hashCode());
        Long $loanId = this.getLoanId();
        result = result * 59 + ($loanId == null ? 43 : ($loanId).hashCode());
        List<String> $tags = this.getTags();
        result = result * 59 + ($tags == null ? 43 : ($tags).hashCode());
        String $storyCode = this.getStoryCode();
        result = result * 59 + ($storyCode == null ? 43 : $storyCode.hashCode());
        List<Long> $loanIdList = this.getLoanIdList();
        result = result * 59 + ($loanIdList == null ? 43 : ($loanIdList).hashCode());
        List<Long> $nodeIdList = this.getNodeIdList();
        result = result * 59 + ($nodeIdList == null ? 43 : ($nodeIdList).hashCode());
        List<EventModel> $eventList = this.getEventList();
        result = result * 59 + ($eventList == null ? 43 : ($eventList).hashCode());
        return result;
    }

    public String toString() {
        return "EsFlowTaskInstanceQuery(startDate=" + this.getStartDate() + ", endDate=" + this.getEndDate() + ", flowVersion=" + this.getFlowVersion() + ", flowId=" + this.getFlowId() + ", flowCode=" + this.getFlowCode() + ", loanId=" + this.getLoanId() + ", tags=" + this.getTags() + ", storyCode=" + this.getStoryCode() + ", loanIdList=" + this.getLoanIdList() + ", nodeIdList=" + this.getNodeIdList() + ", eventList=" + this.getEventList() + ")";
    }
}

