/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.NodeType
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.node.repository;

import com.wacai.trike.mirana.api.constant.NodeType;
import com.wacai.trike.mirana.domain.node.po.NodePO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface NodeRepository
extends JpaRepository<NodePO, Long> {
    public List<NodePO> findByFlowIdAndType(Long var1, NodeType var2);

    public List<NodePO> findByFlowId(Long var1);

    public List<NodePO> findDistinctByFlowIdIn(List<Long> var1);
}

