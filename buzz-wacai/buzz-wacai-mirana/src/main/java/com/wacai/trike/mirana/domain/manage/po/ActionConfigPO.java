/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.manage.po;

import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="action_config")
public class ActionConfigPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private Long appId;
    private Long flowId;
    private String code;
    private String name;
    @Enumerated(value=EnumType.STRING)
    private TaskType type;
    private String content;
    private String requiredVariable;

    public Long getAppId() {
        return this.appId;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public TaskType getType() {
        return this.type;
    }

    public String getContent() {
        return this.content;
    }

    public String getRequiredVariable() {
        return this.requiredVariable;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(TaskType type) {
        this.type = type;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setRequiredVariable(String requiredVariable) {
        this.requiredVariable = requiredVariable;
    }

    @Override
    public String toString() {
        return "ActionConfigPO(appId=" + this.getAppId() + ", flowId=" + this.getFlowId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", type=" + this.getType() + ", content=" + this.getContent() + ", requiredVariable=" + this.getRequiredVariable() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ActionConfigPO)) {
            return false;
        }
        ActionConfigPO other = (ActionConfigPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        TaskType this$type = this.getType();
        TaskType other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$content = this.getContent();
        String other$content = other.getContent();
        if (this$content == null ? other$content != null : !this$content.equals(other$content)) {
            return false;
        }
        String this$requiredVariable = this.getRequiredVariable();
        String other$requiredVariable = other.getRequiredVariable();
        return !(this$requiredVariable == null ? other$requiredVariable != null : !this$requiredVariable.equals(other$requiredVariable));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof ActionConfigPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        TaskType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $content = this.getContent();
        result = result * 59 + ($content == null ? 43 : $content.hashCode());
        String $requiredVariable = this.getRequiredVariable();
        result = result * 59 + ($requiredVariable == null ? 43 : $requiredVariable.hashCode());
        return result;
    }
}

