/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.base.Strings
 *  com.google.common.collect.Lists
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  io.swagger.annotations.ApiOperation
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.web.bind.annotation.DeleteMapping
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.web;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.service.FlowVarRequest;
import com.wacai.trike.mirana.service.FlowVarService;
import com.wacai.trike.mirana.service.FlowVariableDTO;
import com.wacai.trike.mirana.web.model.FlowVariableModel;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/managed/flow-variable"})
public class FlowVariableController {
    private static final Logger log = LoggerFactory.getLogger(FlowVariableController.class);
    @Autowired
    private FlowVarService flowVarService;

    @GetMapping(value={"/{id}"})
    @ApiOperation(value="查询变量详情", notes="uri中传入变量id")
    public ApiResponse<FlowVariableModel> query(@PathVariable(value="id") Long id) {
        FlowVariableDTO variableDTO = this.flowVarService.query(id);
        if (variableDTO == null) {
            return ApiResponse.error((String)("Not found: " + id));
        }
        return ApiResponse.success(variableDTO.toModel());
    }

    @GetMapping
    @ApiOperation(value="查询变量详情", notes="uri中传入变量id")
    public ApiResponse<List<FlowVariableModel>> batch(FlowVarRequest request) {
        return ApiResponse.success(this.convert(this.flowVarService.query(request)));
    }

    @PostMapping
    @ApiOperation(value="新增或修改流程变量的定义", notes="新增和修改合用，新增时不要传入id")
    public ApiResponse<FlowVariableModel> save(@RequestBody FlowVariableModel model) {
        if (Objects.isNull(model) || Strings.isNullOrEmpty((String)model.getCode())) {
            return ApiResponse.error((String)"Invalid input.");
        }
        log.info("save flow-variable {}", model.toString());
        return ApiResponse.success(this.flowVarService.save(model.toDto()).toModel());
    }

    @DeleteMapping(value={"/{id}"})
    @ApiOperation(value="删除流程变量", notes="uri中传入变量id")
    public ApiResponse<Integer> delete(@PathVariable(value="id") Long id) {
        if (id == null) {
            return ApiResponse.error((String)"Variable id must be not null");
        }
        if (this.flowVarService.query(id) == null) {
            return ApiResponse.error((String)("Not found: " + id));
        }
        return ApiResponse.success(this.flowVarService.delete(id));
    }

    private List<FlowVariableModel> convert(List<FlowVariableDTO> variableDTOS) {
        if (CollectionUtils.isEmpty(variableDTOS)) {
            return Lists.newArrayList();
        }
        return variableDTOS.stream().map(FlowVariableDTO::toModel).collect(Collectors.toList());
    }
}

