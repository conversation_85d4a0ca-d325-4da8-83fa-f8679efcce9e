/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.domain.action.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.domain.action.dto.BaseDto;
import com.wacai.trike.mirana.domain.action.dto.GenericVariableDto;
import com.wacai.trike.mirana.domain.action.enums.ActionEnums;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class GenericActionDto
extends BaseDto {
    private String name;
    private Long appId;
    private String appName;
    private ActionEnums.Type type;
    private String callbackUrl;
    private List<GenericVariableDto> variables;
    private List<Long> variableIds;

    public String getName() {
        return this.name;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getAppName() {
        return this.appName;
    }

    public ActionEnums.Type getType() {
        return this.type;
    }

    public String getCallbackUrl() {
        return this.callbackUrl;
    }

    public List<GenericVariableDto> getVariables() {
        return this.variables;
    }

    public List<Long> getVariableIds() {
        return this.variableIds;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public void setType(ActionEnums.Type type) {
        this.type = type;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public void setVariables(List<GenericVariableDto> variables) {
        this.variables = variables;
    }

    public void setVariableIds(List<Long> variableIds) {
        this.variableIds = variableIds;
    }

    @Override
    public String toString() {
        return "GenericActionDto(name=" + this.getName() + ", appId=" + this.getAppId() + ", appName=" + this.getAppName() + ", type=" + (this.getType()) + ", callbackUrl=" + this.getCallbackUrl() + ", variables=" + this.getVariables() + ", variableIds=" + this.getVariableIds() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericActionDto)) {
            return false;
        }
        GenericActionDto other = (GenericActionDto)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$appName = this.getAppName();
        String other$appName = other.getAppName();
        if (this$appName == null ? other$appName != null : !this$appName.equals(other$appName)) {
            return false;
        }
        ActionEnums.Type this$type = this.getType();
        ActionEnums.Type other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        String this$callbackUrl = this.getCallbackUrl();
        String other$callbackUrl = other.getCallbackUrl();
        if (this$callbackUrl == null ? other$callbackUrl != null : !this$callbackUrl.equals(other$callbackUrl)) {
            return false;
        }
        List<GenericVariableDto> this$variables = this.getVariables();
        List<GenericVariableDto> other$variables = other.getVariables();
        if (this$variables == null ? other$variables != null : !(this$variables).equals(other$variables)) {
            return false;
        }
        List<Long> this$variableIds = this.getVariableIds();
        List<Long> other$variableIds = other.getVariableIds();
        return !(this$variableIds == null ? other$variableIds != null : !(this$variableIds).equals(other$variableIds));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof GenericActionDto;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $appName = this.getAppName();
        result = result * 59 + ($appName == null ? 43 : $appName.hashCode());
        ActionEnums.Type $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        String $callbackUrl = this.getCallbackUrl();
        result = result * 59 + ($callbackUrl == null ? 43 : $callbackUrl.hashCode());
        List<GenericVariableDto> $variables = this.getVariables();
        result = result * 59 + ($variables == null ? 43 : ($variables).hashCode());
        List<Long> $variableIds = this.getVariableIds();
        result = result * 59 + ($variableIds == null ? 43 : ($variableIds).hashCode());
        return result;
    }
}

