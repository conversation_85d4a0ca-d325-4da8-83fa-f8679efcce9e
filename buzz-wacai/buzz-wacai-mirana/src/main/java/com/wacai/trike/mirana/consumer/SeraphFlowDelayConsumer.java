/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSONObject
 *  com.wacai.hermes.agent.config.HermesConsumerConfig
 *  com.wacai.hermes.agent.consumer.protocol.HermesHttpConsumer
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
 */
package com.wacai.trike.mirana.consumer;

import com.alibaba.fastjson.JSONObject;
import com.wacai.hermes.agent.config.HermesConsumerConfig;
import com.wacai.hermes.agent.consumer.protocol.HermesHttpConsumer;
import com.wacai.trike.mirana.Seraph.DelayedEventExecutor;
import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.repository.DelayedEventRepository;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

public class SeraphFlowDelayConsumer
extends HermesHttpConsumer {
    private static final Logger log = LoggerFactory.getLogger(SeraphFlowDelayConsumer.class);
    @Autowired
    private DelayedEventRepository eventRepository;
    @Autowired
    private ThreadPoolTaskExecutor executor;
    @Autowired
    private DelayedEventExecutor delayedEventExecutor;

    public SeraphFlowDelayConsumer(HermesConsumerConfig config) {
        super(config);
    }

    public void onMessageReceived(long offset, byte[] key, byte[] data) {
        JSONObject event = JSONObject.parseObject((String)new String(data, StandardCharsets.UTF_8));
        String uuid = event.getString("uuid");
        DelayedEventPO delayedEvent = this.eventRepository.findByUuid(uuid).orElse(null);
        if (delayedEvent == null) {
            log.error("delayedEvent is null, uuid [{}]", uuid);
            return;
        }
        if (delayedEvent.getStatus() == DelayedEventStatus.CANCELED) {
            log.error("delayedEvent is CANCELED, uuid [{}]", uuid);
            return;
        }
        log.info("fired event at instance [{}] , event [{}]", delayedEvent.getInstanceUuid(), delayedEvent.getUuid());
        this.executor.submit(() -> this.delayedEventExecutor.execute(delayedEvent));
    }
}

