/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.Page
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 */
package com.wacai.trike.mirana.api.manage;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.api.manage.model.VariableComboModel;
import com.wacai.trike.mirana.api.manage.model.VariableModel;
import com.wacai.trike.mirana.api.manage.model.VariableQueryModel;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping(value={"/variable"})
public interface VariableService {
    @GetMapping
    public ApiResponse<Page<VariableModel>> queryVariables(VariableQueryModel var1);

    @GetMapping(value={"/{id}"})
    public ApiResponse<VariableModel> get(@PathVariable(value="id") Long var1);

    @GetMapping(value={"/app/{appId}/{code}"})
    public ApiResponse<VariableModel> get(@PathVariable(value="appId") Long var1, @PathVariable(value="code") String var2);

    @PostMapping(value={"/save"})
    public ApiResponse<Long> create(@RequestBody VariableModel var1);

    @PostMapping(value={"/delete/{id}"})
    public ApiResponse<Void> delete(@PathVariable(value="id") Long var1);

    @GetMapping(value={"/combo"})
    public ApiResponse<List<VariableComboModel>> combo(VariableQueryModel var1);

    @GetMapping(value={"/combo/action"})
    public ApiResponse<List<VariableComboModel>> actionCombo(@RequestParam(value="bu") String var1, @RequestParam(value="app") String var2);

    @GetMapping(value={"/combo/action/{appId}"})
    public ApiResponse<List<VariableComboModel>> actionCombo(@PathVariable(value="appId") Long var1);

    @GetMapping(value={"/combo/expression"})
    public ApiResponse<List<VariableComboModel>> expressionCombo(@RequestParam(value="bu") String var1, @RequestParam(value="app") String var2);

    @GetMapping(value={"/combo/expression/{appId}"})
    public ApiResponse<List<VariableComboModel>> expressionCombo(@PathVariable(value="appId") Long var1);
}

