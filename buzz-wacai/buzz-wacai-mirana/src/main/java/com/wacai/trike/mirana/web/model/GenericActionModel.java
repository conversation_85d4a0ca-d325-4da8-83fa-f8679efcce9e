/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.web.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.domain.action.enums.ActionEnums;
import com.wacai.trike.mirana.web.model.GenericTemplateModel;
import com.wacai.trike.mirana.web.model.GenericVariableSaveModel;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class GenericActionModel {
    private Long id;
    private String name;
    private ActionEnums.Type type;
    private String appName;
    private Long appId;
    private String callbackUrl;
    private List<Long> variableIds;
    private List<GenericVariableSaveModel> variables;
    private List<GenericTemplateModel> templateModels;

    public Long getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public ActionEnums.Type getType() {
        return this.type;
    }

    public String getAppName() {
        return this.appName;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getCallbackUrl() {
        return this.callbackUrl;
    }

    public List<Long> getVariableIds() {
        return this.variableIds;
    }

    public List<GenericVariableSaveModel> getVariables() {
        return this.variables;
    }

    public List<GenericTemplateModel> getTemplateModels() {
        return this.templateModels;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(ActionEnums.Type type) {
        this.type = type;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    public void setVariableIds(List<Long> variableIds) {
        this.variableIds = variableIds;
    }

    public void setVariables(List<GenericVariableSaveModel> variables) {
        this.variables = variables;
    }

    public void setTemplateModels(List<GenericTemplateModel> templateModels) {
        this.templateModels = templateModels;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericActionModel)) {
            return false;
        }
        GenericActionModel other = (GenericActionModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        ActionEnums.Type this$type = this.getType();
        ActionEnums.Type other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        String this$appName = this.getAppName();
        String other$appName = other.getAppName();
        if (this$appName == null ? other$appName != null : !this$appName.equals(other$appName)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$callbackUrl = this.getCallbackUrl();
        String other$callbackUrl = other.getCallbackUrl();
        if (this$callbackUrl == null ? other$callbackUrl != null : !this$callbackUrl.equals(other$callbackUrl)) {
            return false;
        }
        List<Long> this$variableIds = this.getVariableIds();
        List<Long> other$variableIds = other.getVariableIds();
        if (this$variableIds == null ? other$variableIds != null : !(this$variableIds).equals(other$variableIds)) {
            return false;
        }
        List<GenericVariableSaveModel> this$variables = this.getVariables();
        List<GenericVariableSaveModel> other$variables = other.getVariables();
        if (this$variables == null ? other$variables != null : !(this$variables).equals(other$variables)) {
            return false;
        }
        List<GenericTemplateModel> this$templateModels = this.getTemplateModels();
        List<GenericTemplateModel> other$templateModels = other.getTemplateModels();
        return !(this$templateModels == null ? other$templateModels != null : !(this$templateModels).equals(other$templateModels));
    }

    protected boolean canEqual(Object other) {
        return other instanceof GenericActionModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        ActionEnums.Type $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        String $appName = this.getAppName();
        result = result * 59 + ($appName == null ? 43 : $appName.hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $callbackUrl = this.getCallbackUrl();
        result = result * 59 + ($callbackUrl == null ? 43 : $callbackUrl.hashCode());
        List<Long> $variableIds = this.getVariableIds();
        result = result * 59 + ($variableIds == null ? 43 : ($variableIds).hashCode());
        List<GenericVariableSaveModel> $variables = this.getVariables();
        result = result * 59 + ($variables == null ? 43 : ($variables).hashCode());
        List<GenericTemplateModel> $templateModels = this.getTemplateModels();
        result = result * 59 + ($templateModels == null ? 43 : ($templateModels).hashCode());
        return result;
    }

    public String toString() {
        return "GenericActionModel(id=" + this.getId() + ", name=" + this.getName() + ", type=" + (this.getType()) + ", appName=" + this.getAppName() + ", appId=" + this.getAppId() + ", callbackUrl=" + this.getCallbackUrl() + ", variableIds=" + this.getVariableIds() + ", variables=" + this.getVariables() + ", templateModels=" + this.getTemplateModels() + ")";
    }
}

