/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.model.EsFlowTaskInstanceListModel
 *  com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel
 *  org.apache.commons.lang3.StringUtils
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.Assert
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.elasticsearch.service.impl;

import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceListModel;
import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel;
import com.wacai.trike.mirana.elasticsearch.dao.api.EsSqlDao;
import com.wacai.trike.mirana.elasticsearch.dao.po.EsFlowTaskInstance;
import com.wacai.trike.mirana.elasticsearch.service.api.EsQueryFlowTaskInstanceService;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceInsertDTO;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceQuery;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceUpdateDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

@Service
public class EsQueryFlowTaskInstanceServiceImpl
implements EsQueryFlowTaskInstanceService {
    @Autowired
    private EsSqlDao esSqlDao;

    @Override
    public boolean insert(EsFlowTaskInstanceInsertDTO dto) {
        Assert.state((dto != null ? 1 : 0) != 0, (String)"esFlowTaskInstanceDTO can not be null.");
        Assert.state((dto.getFlowInstanceId() != null ? 1 : 0) != 0, (String)"flowInstanceId can not be null.");
        Assert.state((boolean)StringUtils.isNotBlank((CharSequence)dto.getFlowCode()), (String)"flowCode can not be empty.");
        Assert.state((boolean)StringUtils.isNotBlank((CharSequence)dto.getFlowVersion()), (String)"flowVersion can not be empty.");
        Assert.state((dto.getInstanceTime() != null ? 1 : 0) != 0, (String)"instanceTime can not be null.");
        EsFlowTaskInstance esFlowTaskInstance = new EsFlowTaskInstance();
        esFlowTaskInstance.setFlowId(dto.getFlowId());
        esFlowTaskInstance.setFlowCode(dto.getFlowCode());
        esFlowTaskInstance.setFlowVersion(dto.getFlowVersion());
        esFlowTaskInstance.setFlowInstanceId(dto.getFlowInstanceId());
        esFlowTaskInstance.setInstanceTime(dto.getInstanceTime());
        esFlowTaskInstance.setLoanId(dto.getLoanId());
        esFlowTaskInstance.setTags(dto.getTags());
        if (!CollectionUtils.isEmpty(dto.getNodeIdList())) {
            Map<String, Integer> nodes = dto.getNodeIdList().stream().collect(Collectors.toMap(nodeId -> "node_" + nodeId, nodeId -> 1, (e1, e2) -> e1));
            esFlowTaskInstance.setNodes(nodes);
        }
        return this.esSqlDao.insert(esFlowTaskInstance);
    }

    @Override
    public EsFlowTaskInstanceInsertDTO find(String flowInstanceId) {
        if (StringUtils.isBlank((CharSequence)flowInstanceId)) {
            return null;
        }
        EsFlowTaskInstance po = this.esSqlDao.findEsFlowInstance(flowInstanceId);
        if (po == null) {
            return null;
        }
        EsFlowTaskInstanceInsertDTO dto = new EsFlowTaskInstanceInsertDTO();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public boolean update(EsFlowTaskInstanceUpdateDTO dto) {
        Assert.state((dto != null ? 1 : 0) != 0, (String)"esFlowTaskInstanceDTO can not be null.");
        Assert.state((dto.getFlowInstanceId() != null ? 1 : 0) != 0, (String)"flowInstanceId can not be null.");
        Assert.state((!CollectionUtils.isEmpty(dto.getNodeIdList()) || !CollectionUtils.isEmpty(dto.getEventList()) ? 1 : 0) != 0, (String)"nodeIdList and eventList can not be all empty.");
        EsFlowTaskInstance esFlowTaskInstance = new EsFlowTaskInstance();
        esFlowTaskInstance.setFlowInstanceId(dto.getFlowInstanceId());
        esFlowTaskInstance.setFlowId(dto.getFlowId());
        if (!CollectionUtils.isEmpty(dto.getNodeIdList())) {
            Map<String, Integer> nodes = dto.getNodeIdList().stream().collect(Collectors.toMap(nodeId -> "node_" + nodeId, nodeId -> 1, (e1, e2) -> e1));
            esFlowTaskInstance.setNodes(nodes);
        }
        if (!CollectionUtils.isEmpty(dto.getEventList()) && StringUtils.isNotBlank((CharSequence)dto.getStoryCode())) {
            Map<String, Integer> events = dto.getEventList().stream().collect(Collectors.toMap(eventDTO -> "story_" + dto.getStoryCode() + "_event_" + eventDTO.getEventCode() + "_node_" + eventDTO.getNodeId(), eventDTO -> 1, (e1, e2) -> e1));
            esFlowTaskInstance.setEvents(events);
        }
        return this.esSqlDao.update(esFlowTaskInstance);
    }

    @Override
    public EsFlowTaskInstanceModel sum(EsFlowTaskInstanceQuery query) {
        Assert.state((query != null ? 1 : 0) != 0, (String)"esFlowTaskInstanceDTO can not be null.");
        Assert.state((!CollectionUtils.isEmpty(query.getNodeIdList()) ? 1 : 0) != 0, (String)"nodeIdList can not be empty.");
        return this.esSqlDao.sum(query);
    }

    @Override
    public List<EsFlowTaskInstanceListModel> list(EsFlowTaskInstanceQuery query) {
        Assert.state((query != null ? 1 : 0) != 0, (String)"esFlowTaskInstanceDTO can not be null.");
        List<EsFlowTaskInstance> list = this.esSqlDao.list(query);
        return this.convert(this.esSqlDao.list(query));
    }

    private EsFlowTaskInstanceListModel convert(EsFlowTaskInstance po) {
        if (po == null) {
            return null;
        }
        EsFlowTaskInstanceListModel model = new EsFlowTaskInstanceListModel();
        BeanUtils.copyProperties(po, model);
        return model;
    }

    private List<EsFlowTaskInstanceListModel> convert(List<EsFlowTaskInstance> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.EMPTY_LIST;
        }
        ArrayList<EsFlowTaskInstanceListModel> modelList = new ArrayList();
        modelList = poList.stream().map(this::convert).collect(Collectors.toList());
        return modelList;
    }
}

