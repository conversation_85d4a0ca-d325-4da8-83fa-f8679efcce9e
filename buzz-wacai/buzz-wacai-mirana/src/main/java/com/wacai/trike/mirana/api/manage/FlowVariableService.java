/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 */
package com.wacai.trike.mirana.api.manage;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.manage.model.FlowVariableModel;
import java.util.List;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping(value={"/flowVariable"})
public interface FlowVariableService {
    @GetMapping
    public ApiResponse<List<FlowVariableModel>> get(@RequestParam(value="bu") String var1, @RequestParam(value="app") String var2);
}

