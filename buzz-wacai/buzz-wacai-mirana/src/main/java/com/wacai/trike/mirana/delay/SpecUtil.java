/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.delay;

import java.time.LocalDateTime;
import java.time.temporal.ChronoField;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public final class SpecUtil {
    private static final Map<Integer, ChronoField> OFFSET_MAP = new HashMap<Integer, ChronoField>();

    public static LocalDateTime setSpec(LocalDateTime baseTime, String spec) {
        if (Objects.isNull(baseTime) || Objects.isNull(spec)) {
            return baseTime;
        }
        String[] specs = spec.split(":");
        for (int i = 0; i < specs.length; ++i) {
            ChronoField unitByOffset = OFFSET_MAP.get(specs.length - i);
            baseTime = baseTime.with(unitByOffset, Long.valueOf(specs[i]));
        }
        return baseTime;
    }

    public static LocalDateTime setSpecWithMaxNano(LocalDateTime baseTime, String spec) {
        if (Objects.isNull(baseTime) || Objects.isNull(spec)) {
            return baseTime;
        }
        return SpecUtil.withMaxNano(SpecUtil.setSpec(baseTime, spec));
    }

    public static LocalDateTime setSpecWithMinNano(LocalDateTime baseTime, String spec) {
        if (Objects.isNull(baseTime) || Objects.isNull(spec)) {
            return baseTime;
        }
        return SpecUtil.withMinNano(SpecUtil.setSpec(baseTime, spec));
    }

    public static LocalDateTime withMaxNano(LocalDateTime baseTime) {
        return baseTime.withNano(999999999);
    }

    public static LocalDateTime withMinNano(LocalDateTime baseTime) {
        return baseTime.withNano(0);
    }

    static {
        OFFSET_MAP.put(1, ChronoField.SECOND_OF_MINUTE);
        OFFSET_MAP.put(2, ChronoField.MINUTE_OF_HOUR);
        OFFSET_MAP.put(3, ChronoField.HOUR_OF_DAY);
        OFFSET_MAP.put(4, ChronoField.DAY_OF_MONTH);
        OFFSET_MAP.put(5, ChronoField.MONTH_OF_YEAR);
        OFFSET_MAP.put(6, ChronoField.YEAR);
    }
}

