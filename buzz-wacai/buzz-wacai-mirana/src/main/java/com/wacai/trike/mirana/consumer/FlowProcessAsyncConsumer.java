/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSON
 *  com.wacai.hermes.agent.config.HermesConsumerConfig
 *  com.wacai.hermes.agent.consumer.protocol.HermesHttpConsumer
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.trike.mirana.api.FlowProcessor
 *  com.wacai.trike.mirana.api.model.FlowProcessRequest
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 */
package com.wacai.trike.mirana.consumer;

import com.alibaba.fastjson.JSON;
import com.wacai.hermes.agent.config.HermesConsumerConfig;
import com.wacai.hermes.agent.consumer.protocol.HermesHttpConsumer;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.FlowProcessor;
import com.wacai.trike.mirana.api.model.FlowProcessRequest;
import java.nio.charset.StandardCharsets;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

public class FlowProcessAsyncConsumer
extends HermesHttpConsumer {
    private static final Logger log = LoggerFactory.getLogger(FlowProcessAsyncConsumer.class);
    @Autowired
    private FlowProcessor flowProcessor;

    public FlowProcessAsyncConsumer(HermesConsumerConfig config) {
        super(config);
    }

    public void onMessageReceived(long offset, String key, byte[] data) {
        String message = new String(data, StandardCharsets.UTF_8);
        FlowProcessRequest request = (FlowProcessRequest)JSON.parseObject((String)message, FlowProcessRequest.class);
        request.setAsyncStart(false);
        request.setAsync(true);
        ApiResponse response = this.flowProcessor.process(request);
        if (!response.success()) {
            log.error("FlowProcessAsyncConsumer-instance failed, request {}", request);
        } else {
            log.info("FlowProcessAsyncConsumer-instance success, request {}", request);
        }
    }
}

