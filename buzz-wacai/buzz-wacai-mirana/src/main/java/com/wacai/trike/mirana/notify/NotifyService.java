/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.model.FlowNotification
 *  com.wacai.trike.mirana.api.model.FlowNotification$Behaviour
 *  org.apache.commons.lang3.StringUtils
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.notify;

import java.time.LocalDateTime;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.api.model.FlowNotification;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.graph.FlowGraph;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.util.HttpUtil;

@Component
public class NotifyService {
    private static final Logger log = LoggerFactory.getLogger(NotifyService.class);
    @Autowired
    private FlowGraphService graphService;
    @Autowired
    private NotifyProperties notifyProperties;

    public void notify(InstanceContext context, Node node, FlowNotification.Behaviour behaviour, String url) {
        FlowGraph graph = this.graphService.getFlowGraph(context.getFlowId());
        try {
            FlowNotification notification = new FlowNotification().
            		setFlow(context.getFlowCode())
            		.setType(graph.getType())
            		.setNode(Objects.nonNull(node) ? node.getName() : null)
            		.setBehaviour(behaviour)
            		.setBzKey(context.getBzKey())
            		.setInstanceUuid(context.getUuid())
            		.setStatus(context.getStatus()).setTimestamp(LocalDateTime.now());
            HttpUtil.post(url, notification, null);
            log.info("notify instance [{}] {} [{}] to customer", new Object[]{context.getUuid(), behaviour, Objects.nonNull(node) ? node.getName() : null});
        }
        catch (Exception e) {
            log.warn("instance [{}] notify [{} {} {}] failed", new Object[]{context.getUuid(), graph.getBu(), graph.getApp(), url, e});
        }
    }

    public void notifyNodeIn(InstanceContext context, Node node) {
        String notifyUrl = this.notifyUrl(context.getFlowId());
        if (StringUtils.isBlank((CharSequence)notifyUrl)) {
            return;
        }
        this.notify(context, node, FlowNotification.Behaviour.NODE_IN, notifyUrl);
    }

    public void notifyNodeOut(InstanceContext context, Node node) {
        String notifyUrl = this.notifyUrl(context.getFlowId());
        if (StringUtils.isBlank((CharSequence)notifyUrl)) {
            return;
        }
        this.notify(context, node, FlowNotification.Behaviour.NODE_OUT, notifyUrl);
    }

    public void notifyFlowStart(InstanceContext context) {
    }

    public void notifyFlowEnd(InstanceContext context) {
        String notifyUrl = this.notifyUrl(context.getFlowId());
        if (StringUtils.isBlank((CharSequence)notifyUrl)) {
            return;
        }
        this.notify(context, null, FlowNotification.Behaviour.FLOW_END, notifyUrl);
    }

    private String notifyUrl(Long flowId) {
        FlowGraph graph = this.graphService.getFlowGraph(flowId);
        return this.notifyProperties.getNotifyUrl(graph.getBu(), graph.getApp());
    }
}

