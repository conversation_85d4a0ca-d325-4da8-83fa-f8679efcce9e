/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.event.AbstractFlowEvent;
import java.util.Map;

public class ContextPersistenceEvent extends AbstractFlowEvent {
	
	private String uuid;
	private String instanceUuid;
	private String taskId;
	protected String operator;
	protected Map<String, String> param;

	@Override
	public String getUuid() {
		return this.uuid;
	}

	@Override
	public String getInstanceUuid() {
		return this.instanceUuid;
	}

	@Override
	public String getTaskId() {
		return this.taskId;
	}

	@Override
	public String getOperator() {
		return this.operator;
	}

	@Override
	public Map<String, String> getParam() {
		return this.param;
	}

	public ContextPersistenceEvent setUuid(String uuid) {
		this.uuid = uuid;
		return this;
	}

	public ContextPersistenceEvent setInstanceUuid(String instanceUuid) {
		this.instanceUuid = instanceUuid;
		return this;
	}

	public ContextPersistenceEvent setTaskId(String taskId) {
		this.taskId = taskId;
		return this;
	}

	public ContextPersistenceEvent setOperator(String operator) {
		this.operator = operator;
		return this;
	}

	@Override
	public ContextPersistenceEvent setParam(Map<String, String> param) {
		this.param = param;
		return this;
	}

	public String toString() {
		return "ContextPersistenceEvent(uuid=" + this.getUuid() + ", instanceUuid=" + this.getInstanceUuid()
				+ ", taskId=" + this.getTaskId() + ", operator=" + this.getOperator() + ", param=" + this.getParam()
				+ ")";
	}

	public boolean equals(Object o) {
		if (o == this) {
			return true;
		}
		if (!(o instanceof ContextPersistenceEvent)) {
			return false;
		}
		ContextPersistenceEvent other = (ContextPersistenceEvent) o;
		if (!other.canEqual(this)) {
			return false;
		}
		if (!super.equals(o)) {
			return false;
		}
		String this$uuid = this.getUuid();
		String other$uuid = other.getUuid();
		if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
			return false;
		}
		String this$instanceUuid = this.getInstanceUuid();
		String other$instanceUuid = other.getInstanceUuid();
		if (this$instanceUuid == null ? other$instanceUuid != null : !this$instanceUuid.equals(other$instanceUuid)) {
			return false;
		}
		String this$taskId = this.getTaskId();
		String other$taskId = other.getTaskId();
		if (this$taskId == null ? other$taskId != null : !this$taskId.equals(other$taskId)) {
			return false;
		}
		String this$operator = this.getOperator();
		String other$operator = other.getOperator();
		if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
			return false;
		}
		Map<String, String> this$param = this.getParam();
		Map<String, String> other$param = other.getParam();
		return !(this$param == null ? other$param != null : !( this$param).equals(other$param));
	}

	protected boolean canEqual(Object other) {
		return other instanceof ContextPersistenceEvent;
	}

	public int hashCode() {
		int PRIME = 59;
		int result = super.hashCode();
		String $uuid = this.getUuid();
		result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
		String $instanceUuid = this.getInstanceUuid();
		result = result * 59 + ($instanceUuid == null ? 43 : $instanceUuid.hashCode());
		String $taskId = this.getTaskId();
		result = result * 59 + ($taskId == null ? 43 : $taskId.hashCode());
		String $operator = this.getOperator();
		result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
		Map<String, String> $param = this.getParam();
		result = result * 59 + ($param == null ? 43 : ( $param).hashCode());
		return result;
	}
}
