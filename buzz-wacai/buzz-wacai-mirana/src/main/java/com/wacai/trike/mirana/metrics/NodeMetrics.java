/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotNull
 */
package com.wacai.trike.mirana.metrics;

import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.TaskInstance;
import com.wacai.trike.mirana.graph.Node;
import java.time.LocalDate;
import javax.validation.constraints.NotNull;

public class NodeMetrics {
    private String app;
    private String bu;
    private String flow;
    private String node;
    private String status;
    private String taskType;
    private String taskExecuteType;
    private String nodeType;
    private String occurredDate;
    private String instanceUuid;
    private String taskInstanceUuid;
    private Long nodeId;
    private Long flowId;
    private String remark;

    NodeMetricsBuilder toBuilder() {
        return new NodeMetricsBuilder().app(this.getApp()).bu(this.getBu()).flow(this.getFlow()).node(this.getNode()).status(this.getStatus()).taskType(this.getTaskType()).taskExecuteType(this.getTaskExecuteType()).nodeType(this.getNodeType()).occurredDate(this.getOccurredDate()).instanceUuid(this.getInstanceUuid()).taskInstanceUuid(this.getTaskInstanceUuid()).nodeId(this.getNodeId()).flowId(this.getFlowId()).remark(this.getRemark());
    }

    public static NodeMetrics build(@NotNull InstanceContext context, @NotNull Node node) {
        TaskInstance instance = context.getCurrentTaskInstance();
        return new NodeMetricsBuilder().instanceUuid(context.getUuid()).flowId(context.getFlowId()).flow(context.getFlowCode()).nodeId(context.getCurrentNodeId()).status(instance.getStatus() == null ? null : instance.getStatus().name()).taskInstanceUuid(instance.getUuid()).node(node.getName()).taskType(node.getTaskType() == null ? null : node.getTaskType().name()).taskExecuteType(node.getTaskExecuteType() == null ? null : node.getTaskExecuteType().name()).nodeType(node.getType() == null ? null : node.getType().name()).remark(instance.getRemark()).occurredDate(LocalDate.now().toString()).build();
    }

    NodeMetrics(String app, String bu, String flow, String node, String status, String taskType, String taskExecuteType, String nodeType, String occurredDate, String instanceUuid, String taskInstanceUuid, Long nodeId, Long flowId, String remark) {
        this.app = app;
        this.bu = bu;
        this.flow = flow;
        this.node = node;
        this.status = status;
        this.taskType = taskType;
        this.taskExecuteType = taskExecuteType;
        this.nodeType = nodeType;
        this.occurredDate = occurredDate;
        this.instanceUuid = instanceUuid;
        this.taskInstanceUuid = taskInstanceUuid;
        this.nodeId = nodeId;
        this.flowId = flowId;
        this.remark = remark;
    }

    public static NodeMetricsBuilder builder() {
        return new NodeMetricsBuilder();
    }

    public String getApp() {
        return this.app;
    }

    public String getBu() {
        return this.bu;
    }

    public String getFlow() {
        return this.flow;
    }

    public String getNode() {
        return this.node;
    }

    public String getStatus() {
        return this.status;
    }

    public String getTaskType() {
        return this.taskType;
    }

    public String getTaskExecuteType() {
        return this.taskExecuteType;
    }

    public String getNodeType() {
        return this.nodeType;
    }

    public String getOccurredDate() {
        return this.occurredDate;
    }

    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    public String getTaskInstanceUuid() {
        return this.taskInstanceUuid;
    }

    public Long getNodeId() {
        return this.nodeId;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public String getRemark() {
        return this.remark;
    }

    public static class NodeMetricsBuilder {
        private String app;
        private String bu;
        private String flow;
        private String node;
        private String status;
        private String taskType;
        private String taskExecuteType;
        private String nodeType;
        private String occurredDate;
        private String instanceUuid;
        private String taskInstanceUuid;
        private Long nodeId;
        private Long flowId;
        private String remark;

        NodeMetricsBuilder() {
        }

        public NodeMetricsBuilder app(String app) {
            this.app = app;
            return this;
        }

        public NodeMetricsBuilder bu(String bu) {
            this.bu = bu;
            return this;
        }

        public NodeMetricsBuilder flow(String flow) {
            this.flow = flow;
            return this;
        }

        public NodeMetricsBuilder node(String node) {
            this.node = node;
            return this;
        }

        public NodeMetricsBuilder status(String status) {
            this.status = status;
            return this;
        }

        public NodeMetricsBuilder taskType(String taskType) {
            this.taskType = taskType;
            return this;
        }

        public NodeMetricsBuilder taskExecuteType(String taskExecuteType) {
            this.taskExecuteType = taskExecuteType;
            return this;
        }

        public NodeMetricsBuilder nodeType(String nodeType) {
            this.nodeType = nodeType;
            return this;
        }

        public NodeMetricsBuilder occurredDate(String occurredDate) {
            this.occurredDate = occurredDate;
            return this;
        }

        public NodeMetricsBuilder instanceUuid(String instanceUuid) {
            this.instanceUuid = instanceUuid;
            return this;
        }

        public NodeMetricsBuilder taskInstanceUuid(String taskInstanceUuid) {
            this.taskInstanceUuid = taskInstanceUuid;
            return this;
        }

        public NodeMetricsBuilder nodeId(Long nodeId) {
            this.nodeId = nodeId;
            return this;
        }

        public NodeMetricsBuilder flowId(Long flowId) {
            this.flowId = flowId;
            return this;
        }

        public NodeMetricsBuilder remark(String remark) {
            this.remark = remark;
            return this;
        }

        public NodeMetrics build() {
            return new NodeMetrics(this.app, this.bu, this.flow, this.node, this.status, this.taskType, this.taskExecuteType, this.nodeType, this.occurredDate, this.instanceUuid, this.taskInstanceUuid, this.nodeId, this.flowId, this.remark);
        }

        public String toString() {
            return "NodeMetrics.NodeMetricsBuilder(app=" + this.app + ", bu=" + this.bu + ", flow=" + this.flow + ", node=" + this.node + ", status=" + this.status + ", taskType=" + this.taskType + ", taskExecuteType=" + this.taskExecuteType + ", nodeType=" + this.nodeType + ", occurredDate=" + this.occurredDate + ", instanceUuid=" + this.instanceUuid + ", taskInstanceUuid=" + this.taskInstanceUuid + ", nodeId=" + this.nodeId + ", flowId=" + this.flowId + ", remark=" + this.remark + ")";
        }
    }
}

