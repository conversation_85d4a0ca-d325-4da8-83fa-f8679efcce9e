/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 *  com.wacai.loan.trike.expression.ThresholdType
 *  com.wacai.trike.mirana.api.constant.CandidateType
 *  com.wacai.trike.mirana.api.constant.VariableCategory
 */
package com.wacai.trike.mirana.domain.manage.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.loan.trike.expression.ThresholdType;
import com.wacai.trike.mirana.api.constant.CandidateType;
import com.wacai.trike.mirana.api.constant.VariableCategory;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.manage.po.VariableConfigPO;
import java.time.LocalDateTime;

public class QVariableConfigPO
extends EntityPathBase<VariableConfigPO> {
    private static final long serialVersionUID = 1356150112L;
    public static final QVariableConfigPO variableConfigPO = new QVariableConfigPO("variableConfigPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final NumberPath<Long> appId;
    public final StringPath candidateContent;
    public final EnumPath<CandidateType> candidateType;
    public final EnumPath<VariableCategory> category;
    public final StringPath code;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final EnumPath<ThresholdType> dataType;
    public final NumberPath<Long> flowId;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QVariableConfigPO(String variable) {
        super(VariableConfigPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.candidateContent = this.createString("candidateContent");
        this.candidateType = this.createEnum("candidateType", CandidateType.class);
        this.category = this.createEnum("category", VariableCategory.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.dataType = this.createEnum("dataType", ThresholdType.class);
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QVariableConfigPO(Path<? extends VariableConfigPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.candidateContent = this.createString("candidateContent");
        this.candidateType = this.createEnum("candidateType", CandidateType.class);
        this.category = this.createEnum("category", VariableCategory.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.dataType = this.createEnum("dataType", ThresholdType.class);
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QVariableConfigPO(PathMetadata metadata) {
        super(VariableConfigPO.class, metadata);
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.candidateContent = this.createString("candidateContent");
        this.candidateType = this.createEnum("candidateType", CandidateType.class);
        this.category = this.createEnum("category", VariableCategory.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.dataType = this.createEnum("dataType", ThresholdType.class);
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

