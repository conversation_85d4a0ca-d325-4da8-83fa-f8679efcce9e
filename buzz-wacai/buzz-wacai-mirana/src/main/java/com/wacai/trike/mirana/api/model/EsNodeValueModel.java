/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.model.EsNodeEventValueModel;
import java.io.Serializable;
import java.util.List;

public class EsNodeValueModel
implements Serializable {
    private Integer value;
    private String rate;
    private String actionCode;
    private String label;
    private List<EsNodeEventValueModel> events;

    public Integer getValue() {
        return this.value;
    }

    public String getRate() {
        return this.rate;
    }

    public String getActionCode() {
        return this.actionCode;
    }

    public String getLabel() {
        return this.label;
    }

    public List<EsNodeEventValueModel> getEvents() {
        return this.events;
    }

    public void setValue(Integer value) {
        this.value = value;
    }

    public void setRate(String rate) {
        this.rate = rate;
    }

    public void setActionCode(String actionCode) {
        this.actionCode = actionCode;
    }

    public void setLabel(String label) {
        this.label = label;
    }

    public void setEvents(List<EsNodeEventValueModel> events) {
        this.events = events;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EsNodeValueModel)) {
            return false;
        }
        EsNodeValueModel other = (EsNodeValueModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Integer this$value = this.getValue();
        Integer other$value = other.getValue();
        if (this$value == null ? other$value != null : !(this$value).equals(other$value)) {
            return false;
        }
        String this$rate = this.getRate();
        String other$rate = other.getRate();
        if (this$rate == null ? other$rate != null : !this$rate.equals(other$rate)) {
            return false;
        }
        String this$actionCode = this.getActionCode();
        String other$actionCode = other.getActionCode();
        if (this$actionCode == null ? other$actionCode != null : !this$actionCode.equals(other$actionCode)) {
            return false;
        }
        String this$label = this.getLabel();
        String other$label = other.getLabel();
        if (this$label == null ? other$label != null : !this$label.equals(other$label)) {
            return false;
        }
        List<EsNodeEventValueModel> this$events = this.getEvents();
        List<EsNodeEventValueModel> other$events = other.getEvents();
        return !(this$events == null ? other$events != null : !(this$events).equals(other$events));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EsNodeValueModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Integer $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : ($value).hashCode());
        String $rate = this.getRate();
        result = result * 59 + ($rate == null ? 43 : $rate.hashCode());
        String $actionCode = this.getActionCode();
        result = result * 59 + ($actionCode == null ? 43 : $actionCode.hashCode());
        String $label = this.getLabel();
        result = result * 59 + ($label == null ? 43 : $label.hashCode());
        List<EsNodeEventValueModel> $events = this.getEvents();
        result = result * 59 + ($events == null ? 43 : ($events).hashCode());
        return result;
    }

    public String toString() {
        return "EsNodeValueModel(value=" + this.getValue() + ", rate=" + this.getRate() + ", actionCode=" + this.getActionCode() + ", label=" + this.getLabel() + ", events=" + this.getEvents() + ")";
    }

    public EsNodeValueModel(Integer value, String rate, String actionCode, String label, List<EsNodeEventValueModel> events) {
        this.value = value;
        this.rate = rate;
        this.actionCode = actionCode;
        this.label = label;
        this.events = events;
    }
}

