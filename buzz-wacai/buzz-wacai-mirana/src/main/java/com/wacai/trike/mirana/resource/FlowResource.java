/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.base.Strings
 *  com.google.common.collect.Lists
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.ComboBox
 *  com.wacai.loan.trike.common.model.Page
 *  com.wacai.trike.mirana.api.constant.FlowStatus
 *  com.wacai.trike.mirana.api.constant.FlowType
 *  com.wacai.trike.mirana.api.manage.ActionService
 *  com.wacai.trike.mirana.api.manage.FlowService
 *  com.wacai.trike.mirana.api.manage.model.ActionComboModel
 *  com.wacai.trike.mirana.api.manage.model.ActionQueryModel
 *  com.wacai.trike.mirana.api.manage.model.FlowCreateModel
 *  com.wacai.trike.mirana.api.manage.model.FlowGraphModel
 *  com.wacai.trike.mirana.api.manage.model.FlowVariableModel
 *  com.wacai.trike.mirana.api.manage.model.FlowVersionModel
 *  com.wacai.trike.mirana.api.manage.model.OperatorModel
 *  com.wacai.trike.mirana.api.story.StoryLineProcessor
 *  com.wacai.trike.mirana.api.story.model.StoryModel
 *  com.wacai.trike.mirana.api.story.model.StoryQueryReq
 *  com.wacai.trike.mirana.api.story.model.StoryQueryReq$StoryQueryReqBuilder
 *  io.swagger.annotations.ApiOperation
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.resource;

import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.FlowType;
import com.wacai.trike.mirana.api.manage.ActionService;
import com.wacai.trike.mirana.api.manage.FlowService;
import com.wacai.trike.mirana.api.manage.model.ActionComboModel;
import com.wacai.trike.mirana.api.manage.model.ActionQueryModel;
import com.wacai.trike.mirana.api.manage.model.FlowCreateModel;
import com.wacai.trike.mirana.api.manage.model.FlowGraphModel;
import com.wacai.trike.mirana.api.manage.model.FlowVariableModel;
import com.wacai.trike.mirana.api.manage.model.FlowVersionModel;
import com.wacai.trike.mirana.api.manage.model.OperatorModel;
import com.wacai.trike.mirana.api.story.StoryLineProcessor;
import com.wacai.trike.mirana.api.story.model.StoryModel;
import com.wacai.trike.mirana.api.story.model.StoryQueryReq;
import com.wacai.trike.mirana.resource.StoryQueryModel;
import com.wacai.trike.mirana.resource.StoryQueryResponseModel;
import com.wacai.trike.mirana.service.FlowVarRequest;
import com.wacai.trike.mirana.service.FlowVarService;
import com.wacai.trike.mirana.service.FlowVariableDTO;
import io.swagger.annotations.ApiOperation;
import java.time.ZoneId;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/story"})
public class FlowResource {
    private static final Logger log = LoggerFactory.getLogger(FlowResource.class);
    @Autowired
    private StoryLineProcessor storyLineProcessor;
    @Autowired
    private FlowService flowService;
    @Autowired
    private FlowVarService flowVarService;
    @Autowired
    private ActionService actionService;

    @GetMapping(value={"/combo"})
    @ApiOperation(value="\u83b7\u53d6\u6545\u4e8b\u7ebf\u4e0b\u62c9\u5217\u8868")
    public ApiResponse<List<ComboBox<String>>> getStoryCombo(@RequestParam(name="bu", defaultValue="") String bu, @RequestParam(name="app", defaultValue="") String app) {
        StoryQueryReq req = StoryQueryReq.builder().bu(bu).app(app).statuses(Collections.singletonList(FlowStatus.ENABLED)).types(Collections.singletonList(FlowType.STORY_LINE)).build();
        ApiResponse result = this.storyLineProcessor.query(req);
        if (!result.success()) {
            return ApiResponse.error((int)result.getCode(), (String)result.getError());
        }
        return ApiResponse.success(((Page)result.getData()).map(this::buildCombo).getData());
    }

    @GetMapping(value={"/flow/combo"})
    @ApiOperation(value="\u83b7\u53d6\u6807\u51c6\u6d41\u7a0b\u4e0b\u62c9\u5217\u8868")
    public ApiResponse<List<ComboBox<String>>> getFlowCombo(@RequestParam(name="bu", defaultValue="") String bu, @RequestParam(name="app", defaultValue="") String app, @RequestParam(name="pageSize", defaultValue="") Integer pageSize) {
        StoryQueryReq req = StoryQueryReq.builder().bu(bu).app(app).statuses(Collections.singletonList(FlowStatus.ENABLED)).types(Collections.singletonList(FlowType.STANDARD)).build();
        req.setPageSize((long)pageSize.intValue());
        ApiResponse result = this.storyLineProcessor.query(req);
        if (!result.success()) {
            return ApiResponse.error((int)result.getCode(), (String)result.getError());
        }
        return ApiResponse.success(((Page)result.getData()).map(this::buildCombo).getData());
    }

    @GetMapping
    @ApiOperation(value="\u67e5\u8be2\u6545\u4e8b\u7ebf\u5217\u8868")
    public ApiResponse<Page<StoryQueryResponseModel>> queryStory(StoryQueryModel queryModel) {
        StoryQueryReq req = this.buildQueryRequest(queryModel, FlowType.STORY_LINE);
        return this.storyLineProcessor.query(req).map(this::convert);
    }

    @GetMapping(value={"/flow"})
    @ApiOperation(value="\u67e5\u8be2\u6807\u51c6\u6d41\u7a0b\u5217\u8868")
    public ApiResponse<Page<StoryQueryResponseModel>> queryFlow(StoryQueryModel queryModel) {
        StoryQueryReq req = this.buildQueryRequest(queryModel, FlowType.STANDARD);
        return this.storyLineProcessor.query(req).map(this::convert);
    }

    @PostMapping(value={"/save"})
    @ApiOperation(value="\u4fdd\u5b58\u6545\u4e8b\u7ebf")
    public ApiResponse<Long> save(@RequestBody FlowCreateModel flowCreateModel) {
        flowCreateModel.setType(FlowType.STORY_LINE);
        return this.flowService.create(flowCreateModel);
    }

    @GetMapping(value={"/flow-status"})
    @ApiOperation(value="\u83b7\u53d6\u6d41\u7a0b\u72b6\u6001\u96c6\u5408\u5217\u8868")
    public ApiResponse<List<ComboBox<String>>> getGraph() {
        return this.flowService.listFlowStatus();
    }

    @PostMapping(value={"/flow/save"})
    @ApiOperation(value="\u4fdd\u5b58\u6807\u51c6\u6d41\u7a0b\u56fe")
    public ApiResponse<Long> saveFlow(@RequestBody FlowCreateModel flowCreateModel) {
        flowCreateModel.setType(FlowType.STANDARD);
        return this.flowService.create(flowCreateModel);
    }

    @GetMapping(value={"/graph"})
    @ApiOperation(value="\u83b7\u53d6\u6545\u4e8b\u56fe")
    public ApiResponse<FlowGraphModel> getGraph(@RequestParam(value="id", defaultValue="0") Long id, @RequestParam(value="code", defaultValue="") String code, @RequestParam(value="version", defaultValue="") String version) {
        if (id != null && id > 0L) {
            return this.flowService.get(id);
        }
        return this.flowService.get(code, version);
    }

    @GetMapping(value={"/flow/versions"})
    @ApiOperation(value="\u83b7\u53d6\u6807\u51c6\u6d41\u7a0b\u56fe")
    public ApiResponse<FlowVersionModel> getFlowGraph(@RequestParam(value="appId", defaultValue="0") Long appId, @RequestParam(value="code", defaultValue="") String code) {
        return this.flowService.versions(appId, code);
    }

    @PostMapping(value={"/graph/draw"})
    @ApiOperation(value="\u4fdd\u5b58\u6545\u4e8b\u7ebf\u6d41\u7a0b\u56fe")
    public ApiResponse<Long> drawGraph(@RequestBody FlowGraphModel flowGraphModel) {
        return this.flowService.storyDraw(flowGraphModel);
    }

    @GetMapping(value={"/flow/graph"})
    @ApiOperation(value="\u83b7\u53d6\u6807\u51c6\u6d41\u7a0b\u56fe")
    public ApiResponse<FlowGraphModel> getFlowGraph(@RequestParam(value="id", defaultValue="0") Long id, @RequestParam(value="code", defaultValue="") String code, @RequestParam(value="version", defaultValue="") String version) {
        if (id != null && id > 0L) {
            return this.flowService.get(id);
        }
        return this.flowService.get(code, version);
    }

    @PostMapping(value={"/flow/graph/draw"})
    @ApiOperation(value="\u4fdd\u5b58\u6807\u51c6\u6d41\u7a0b\u56fe")
    public ApiResponse<Long> drawFlowGraph(@RequestBody FlowGraphModel flowGraphModel) {
        return this.flowService.draw(flowGraphModel);
    }

    @GetMapping(value={"/flow/action/combo"})
    @ApiOperation(value="\u83b7\u53d6\u6807\u51c6\u6d41\u7a0b\u64cd\u4f5c\u4e0b\u62c9\u5217\u8868")
    public ApiResponse<List<ActionComboModel>> getFlowActionCombo() {
        ActionQueryModel queryModel = new ActionQueryModel();
        return this.actionService.combo(queryModel);
    }

    @GetMapping(value={"/flow/variable/combo"})
    @ApiOperation(value="\u83b7\u53d6flow variable\u4e0b\u62c9\u5217\u8868")
    public ApiResponse<List<FlowVariableModel>> getFlowVariableCombo() {
        return ApiResponse.success(this.build(this.flowVarService.query(new FlowVarRequest())));
    }

    private StoryQueryReq buildQueryRequest(StoryQueryModel queryModel, FlowType type) {
        StoryQueryReq.StoryQueryReqBuilder builder = StoryQueryReq.builder().bu(queryModel.getBu()).app(queryModel.getApp()).code(queryModel.getCode()).name(queryModel.getName()).types(Collections.singletonList(type));
        if (!Strings.isNullOrEmpty((String)queryModel.getStatus())) {
            builder.statuses((List)Lists.newArrayList((Object[])new FlowStatus[]{FlowStatus.valueOf((String)queryModel.getStatus())}));
        } else {
            builder.statuses((List)Lists.newArrayList((Object[])new FlowStatus[]{FlowStatus.EDITING, FlowStatus.ENABLED}));
        }
        StoryQueryReq req = builder.build();
        req.setPageIndex(queryModel.getPageIndex());
        req.setPageSize(queryModel.getPageSize());
        req.setOrder(queryModel.getOrder());
        req.setOrderBy(queryModel.getOrderBy());
        return req;
    }

    private Page<StoryQueryResponseModel> convert(Page<StoryModel> page) {
        return page.map(s -> new StoryQueryResponseModel().setId(s.getId()).setCode(s.getCode()).setName(s.getName()).setStatus(s.getStatus().getDesc()).setUpdatedTime(s.getUpdatedTime().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli()));
    }

    private ComboBox<String> buildCombo(StoryModel model) {
        return ComboBox.of((String)model.getName(), model.getCode());
    }

    private List<FlowVariableModel> build(List<FlowVariableDTO> variables) {
        if (CollectionUtils.isEmpty(variables)) {
            return Lists.newArrayList();
        }
        return variables.stream().map(this::convert).collect(Collectors.toList());
    }

    private FlowVariableModel convert(FlowVariableDTO source) {
        FlowVariableModel target = (FlowVariableModel)BeanUtils.instantiateClass(FlowVariableModel.class);
        BeanUtils.copyProperties(source, target);
        if (!CollectionUtils.isEmpty(source.getSupportedOperators())) {
            List ops = source.getSupportedOperators().stream().map(op -> {
                OperatorModel model = (OperatorModel)BeanUtils.instantiateClass(OperatorModel.class);
                BeanUtils.copyProperties(op, model);
                model.setCode(op.getCode().name());
                return model;
            }).collect(Collectors.toList());
            target.setOperators(ops);
        }
        return target;
    }
}

