/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.NodeType
 *  com.wacai.trike.mirana.api.constant.TaskExecuteType
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.node.po;

import com.wacai.trike.mirana.api.constant.NodeType;
import com.wacai.trike.mirana.api.constant.TaskExecuteType;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.delay.ScheduleType;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="node")
public class NodePO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private Long appId;
    private Long flowId;
    private String code;
    private String name;
    @Enumerated(value=EnumType.STRING)
    private NodeType type;
    @Enumerated(value=EnumType.STRING)
    private TaskType taskType = TaskType.USER;
    @Enumerated(value=EnumType.STRING)
    private TaskExecuteType taskExecuteType;
    private String taskContent;
    private Integer taskTimeout;
    @Enumerated(value=EnumType.STRING)
    private ScheduleType scheduleType;
    private String scheduleContent;
    private Double x;
    private Double y;
    private Long actionTemplateId;

    public Long getAppId() {
        return this.appId;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public NodeType getType() {
        return this.type;
    }

    public TaskType getTaskType() {
        return this.taskType;
    }

    public TaskExecuteType getTaskExecuteType() {
        return this.taskExecuteType;
    }

    public String getTaskContent() {
        return this.taskContent;
    }

    public Integer getTaskTimeout() {
        return this.taskTimeout;
    }

    public ScheduleType getScheduleType() {
        return this.scheduleType;
    }

    public String getScheduleContent() {
        return this.scheduleContent;
    }

    public Double getX() {
        return this.x;
    }

    public Double getY() {
        return this.y;
    }

    public Long getActionTemplateId() {
        return this.actionTemplateId;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(NodeType type) {
        this.type = type;
    }

    public void setTaskType(TaskType taskType) {
        this.taskType = taskType;
    }

    public void setTaskExecuteType(TaskExecuteType taskExecuteType) {
        this.taskExecuteType = taskExecuteType;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public void setTaskTimeout(Integer taskTimeout) {
        this.taskTimeout = taskTimeout;
    }

    public void setScheduleType(ScheduleType scheduleType) {
        this.scheduleType = scheduleType;
    }

    public void setScheduleContent(String scheduleContent) {
        this.scheduleContent = scheduleContent;
    }

    public void setX(Double x) {
        this.x = x;
    }

    public void setY(Double y) {
        this.y = y;
    }

    public void setActionTemplateId(Long actionTemplateId) {
        this.actionTemplateId = actionTemplateId;
    }

    @Override
    public String toString() {
        return "NodePO(appId=" + this.getAppId() + ", flowId=" + this.getFlowId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", type=" + this.getType() + ", taskType=" + this.getTaskType() + ", taskExecuteType=" + this.getTaskExecuteType() + ", taskContent=" + this.getTaskContent() + ", taskTimeout=" + this.getTaskTimeout() + ", scheduleType=" + (this.getScheduleType()) + ", scheduleContent=" + this.getScheduleContent() + ", x=" + this.getX() + ", y=" + this.getY() + ", actionTemplateId=" + this.getActionTemplateId() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof NodePO)) {
            return false;
        }
        NodePO other = (NodePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        NodeType this$type = this.getType();
        NodeType other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        TaskType this$taskType = this.getTaskType();
        TaskType other$taskType = other.getTaskType();
        if (this$taskType == null ? other$taskType != null : !this$taskType.equals(other$taskType)) {
            return false;
        }
        TaskExecuteType this$taskExecuteType = this.getTaskExecuteType();
        TaskExecuteType other$taskExecuteType = other.getTaskExecuteType();
        if (this$taskExecuteType == null ? other$taskExecuteType != null : !this$taskExecuteType.equals(other$taskExecuteType)) {
            return false;
        }
        String this$taskContent = this.getTaskContent();
        String other$taskContent = other.getTaskContent();
        if (this$taskContent == null ? other$taskContent != null : !this$taskContent.equals(other$taskContent)) {
            return false;
        }
        Integer this$taskTimeout = this.getTaskTimeout();
        Integer other$taskTimeout = other.getTaskTimeout();
        if (this$taskTimeout == null ? other$taskTimeout != null : !(this$taskTimeout).equals(other$taskTimeout)) {
            return false;
        }
        ScheduleType this$scheduleType = this.getScheduleType();
        ScheduleType other$scheduleType = other.getScheduleType();
        if (this$scheduleType == null ? other$scheduleType != null : !((this$scheduleType)).equals(other$scheduleType)) {
            return false;
        }
        String this$scheduleContent = this.getScheduleContent();
        String other$scheduleContent = other.getScheduleContent();
        if (this$scheduleContent == null ? other$scheduleContent != null : !this$scheduleContent.equals(other$scheduleContent)) {
            return false;
        }
        Double this$x = this.getX();
        Double other$x = other.getX();
        if (this$x == null ? other$x != null : !(this$x).equals(other$x)) {
            return false;
        }
        Double this$y = this.getY();
        Double other$y = other.getY();
        if (this$y == null ? other$y != null : !(this$y).equals(other$y)) {
            return false;
        }
        Long this$actionTemplateId = this.getActionTemplateId();
        Long other$actionTemplateId = other.getActionTemplateId();
        return !(this$actionTemplateId == null ? other$actionTemplateId != null : !(this$actionTemplateId).equals(other$actionTemplateId));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof NodePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        NodeType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        TaskType $taskType = this.getTaskType();
        result = result * 59 + ($taskType == null ? 43 : $taskType.hashCode());
        TaskExecuteType $taskExecuteType = this.getTaskExecuteType();
        result = result * 59 + ($taskExecuteType == null ? 43 : $taskExecuteType.hashCode());
        String $taskContent = this.getTaskContent();
        result = result * 59 + ($taskContent == null ? 43 : $taskContent.hashCode());
        Integer $taskTimeout = this.getTaskTimeout();
        result = result * 59 + ($taskTimeout == null ? 43 : ($taskTimeout).hashCode());
        ScheduleType $scheduleType = this.getScheduleType();
        result = result * 59 + ($scheduleType == null ? 43 : (($scheduleType)).hashCode());
        String $scheduleContent = this.getScheduleContent();
        result = result * 59 + ($scheduleContent == null ? 43 : $scheduleContent.hashCode());
        Double $x = this.getX();
        result = result * 59 + ($x == null ? 43 : ($x).hashCode());
        Double $y = this.getY();
        result = result * 59 + ($y == null ? 43 : ($y).hashCode());
        Long $actionTemplateId = this.getActionTemplateId();
        result = result * 59 + ($actionTemplateId == null ? 43 : ($actionTemplateId).hashCode());
        return result;
    }
}

