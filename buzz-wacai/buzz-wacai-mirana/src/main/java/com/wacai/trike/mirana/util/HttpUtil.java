/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSON
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.fasterxml.jackson.databind.ObjectMapper
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  okhttp3.Call
 *  okhttp3.HttpUrl
 *  okhttp3.HttpUrl$Builder
 *  okhttp3.MediaType
 *  okhttp3.OkHttpClient
 *  okhttp3.Request
 *  okhttp3.Request$Builder
 *  okhttp3.RequestBody
 *  okhttp3.Response
 *  org.apache.commons.lang3.StringUtils
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeansException
 *  org.springframework.beans.factory.BeanFactory
 *  org.springframework.beans.factory.BeanFactoryAware
 *  org.springframework.beans.factory.InitializingBean
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.util;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wacai.loan.trike.common.model.ApiResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import okhttp3.Call;
import okhttp3.HttpUrl;
import okhttp3.MediaType;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.RequestBody;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class HttpUtil implements InitializingBean, BeanFactoryAware {
	private static final Logger log = LoggerFactory.getLogger(HttpUtil.class);
	private static final MediaType APPLICATION_JSON = MediaType.parse((String) "application/json; charset=utf-8");
	private static final TypeReference OBJECT_REFERENCE = new TypeReference<ApiResponse<Object>>() {
	};
	private static OkHttpClient client;
	private static ObjectMapper objectMapper;
	private BeanFactory beanFactory;
	private static final int DEFAULT_TIMEOUT = 180;

	public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
		this.beanFactory = beanFactory;
	}

	public void afterPropertiesSet() throws Exception {
		client = (OkHttpClient) this.beanFactory.getBean(OkHttpClient.class);
		objectMapper = (ObjectMapper) this.beanFactory.getBean(ObjectMapper.class);
	}

	public static String post(String url, Object body, Integer timeout) {
		try {
			Request post = new Request.Builder().url(url).post(
					RequestBody.create((MediaType) APPLICATION_JSON, (byte[]) objectMapper.writeValueAsBytes(body)))
					.build();
			return HttpUtil.execute(timeout, post);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static String post(String url, Map<String, String> header, Map<String, Object> body) {
		try {
			Request.Builder post = new Request.Builder().url(url).post(
					RequestBody.create((MediaType) APPLICATION_JSON, (byte[]) objectMapper.writeValueAsBytes(body)));
			if (!CollectionUtils.isEmpty(header)) {
				for (Map.Entry<String, String> entry : header.entrySet()) {
					post.addHeader(entry.getKey(), entry.getValue());
				}
			}
			return HttpUtil.execute(180, post.build());
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static String directPost(String url) {
		try {
			Request post = new Request.Builder().url(url)
					.post(RequestBody.create((MediaType) APPLICATION_JSON, (String) "")).build();
			Call call = client.newCall(post);
			return call.execute().body().string();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static <T> T post(String url, Object body, Integer timeout, Class<T> clazz) {
		String data = HttpUtil.post(url, body, timeout);
		try {
			return (T) objectMapper.readValue(data, clazz);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static String get(String url) {
		return HttpUtil.get(url, 180);
	}

	public static String get(String url, Integer timeout) {
		try {
			Request get = new Request.Builder().url(url).build();
			return HttpUtil.execute(timeout, get);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static String get(String url, Map<String, String> header) {
		try {
			Request.Builder get = new Request.Builder().url(url);
			if (!CollectionUtils.isEmpty(header)) {
				for (Map.Entry<String, String> entry : header.entrySet()) {
					get.addHeader(entry.getKey(), entry.getValue());
				}
			}
			return HttpUtil.execute(180, get.build());
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}

	public static String getUrl(String url, Map<String, String> pathVariables, Map<String, Object> params) {
		if (!CollectionUtils.isEmpty(pathVariables)) {
			for (String string : pathVariables.keySet()) {
				url = url.replace("{" + string + "}", pathVariables.get(string));
			}
		}
		HttpUrl.Builder builder = HttpUrl.parse((String) url).newBuilder();
		if (!CollectionUtils.isEmpty(params)) {
			for (String key : params.keySet()) {
				builder.addQueryParameter(key, HttpUtil.asString(params.get(key)));
			}
		}
		String string = builder.build().toString();
		return string;
	}

	private static String asString(Object value) {
		return value instanceof String ? (String) value
				: (value instanceof Date ? String.valueOf(((Date) value).getTime())
						: (value instanceof List ? StringUtils.join((Object[]) ((List) value).toArray(), (String) ",")
								: HttpUtil.toStr(value)));
	}

	private static String toStr(Object value) {
		if (value == null) {
			return "";
		}
		return JSON.toJSONString( value);
	}

	public static String get(String url, Map<String, String> header, Map<String, String> pathVariables,
			Map<String, Object> params) {
		String newUrl = null;
		try {
			newUrl = HttpUtil.getUrl(url, pathVariables, params);
			Request.Builder get = new Request.Builder().url(newUrl);
			if (!CollectionUtils.isEmpty(header)) {
				for (Map.Entry<String, String> entry : header.entrySet()) {
					get.addHeader(entry.getKey(), entry.getValue());
				}
			}
			return HttpUtil.execute(180, get.build());
		} catch (Exception e) {
			String error = "alitar error caused by get url: " + newUrl;
			log.error(error, (Throwable) e);
			return null;
		}
	}

	/*
	 * WARNING - Removed try catching itself - possible behaviour change.
	 */
	private static String execute(Integer timeout, Request get) throws IOException {
//		try (Response response = null;) {
//			InputStream is;
//			ApiResponse apiResponse;
//			Call call = client.newCall(get);
//			if (Objects.nonNull(timeout) && timeout > 0) {
//				call.timeout().timeout((long) timeout.intValue(), TimeUnit.SECONDS);
//			}
//			if (!(apiResponse =objectMapper.readValue(
//					is = Objects.requireNonNull((response = call.execute()).body()).byteStream(), OBJECT_REFERENCE))
//							.success()) {
//				throw new RuntimeException(apiResponse.getError());
//			}
//			String string = objectMapper.writeValueAsString(apiResponse.getData());
//			return string;
//		}
		return null;
	}
}
