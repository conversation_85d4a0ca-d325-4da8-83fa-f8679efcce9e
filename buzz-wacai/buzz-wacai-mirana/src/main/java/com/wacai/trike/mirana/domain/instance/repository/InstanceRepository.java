/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  org.springframework.data.jpa.repository.JpaRepository
 *  org.springframework.data.jpa.repository.Query
 */
package com.wacai.trike.mirana.domain.instance.repository;

import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.domain.instance.po.InstancePO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;

public interface InstanceRepository
extends JpaRepository<InstancePO, Long> {
    public Optional<InstancePO> findByUuid(String var1);

    public List<InstancePO> findByParentUuid(String var1);

    public List<InstancePO> findByFlowIdAndStatus(Long var1, InstanceStatus var2);

    public List<InstancePO> findByParentUuidAndStatus(String var1, InstanceStatus var2);

    public InstancePO findByParentUuidAndFlowId(String var1, Long var2);

    @Query(value="select uuid from InstancePO")
    public List<String> loadAllUuid();
}

