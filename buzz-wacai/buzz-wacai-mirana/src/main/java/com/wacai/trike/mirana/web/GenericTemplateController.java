/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.Page
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.web;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.GenericActionService;
import com.wacai.trike.mirana.domain.action.GenericTemplateService;
import com.wacai.trike.mirana.domain.action.dto.GenericActionDto;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.web.model.GenericTemplateModel;
import com.wacai.trike.mirana.web.model.GenericTemplateRequest;
import com.wacai.trike.mirana.web.model.GenericTemplateRequestModel;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/managed/action-template"})
public class GenericTemplateController {
    private static final Logger log = LoggerFactory.getLogger(GenericTemplateController.class);
    @Autowired
    private GenericActionService actionService;
    @Autowired
    private GenericTemplateService templateService;

    @PostMapping
    public ApiResponse<GenericTemplateModel> create(@RequestBody GenericTemplateRequestModel model) {
        if (model.getGenericActionId() == null) {
            return ApiResponse.error((String)"\u5173\u8054GenericActionId\u4e0d\u80fd\u4e3a\u7a7a");
        }
        GenericActionDto action = this.actionService.query(model.getGenericActionId());
        if (action == null) {
            return ApiResponse.error((String)"\u5173\u8054GenericAction\u4e0d\u5b58\u5728");
        }
        if (this.templateService.query(model.getId()) == null) {
            return ApiResponse.success(ObjectUtils.convert(this.templateService.create(this.convert(model)), GenericTemplateModel.class));
        }
        return ApiResponse.success(ObjectUtils.convert(this.templateService.update(this.convert(model)), GenericTemplateModel.class));
    }

    @GetMapping(value={"/{id}"})
    public ApiResponse<GenericTemplateModel> query(@PathVariable(value="id") Long id) {
        GenericTemplateDto templateDto = this.templateService.query(id);
        if (templateDto == null) {
            return ApiResponse.error((String)("Not found: " + id));
        }
        return ApiResponse.success(ObjectUtils.convert(templateDto, GenericTemplateModel.class));
    }

    @GetMapping
    public ApiResponse<Page<GenericTemplateModel>> page(GenericTemplateRequest request) {
        if (request.getPageSize() <= 0 || request.getPageIndex() < 0) {
            return ApiResponse.error((String)"Invalid paging input");
        }
        return ApiResponse.success(this.convert(this.templateService.page(request)));
    }

    @GetMapping(value={"/batch"})
    public ApiResponse<List<GenericTemplateModel>> batchQuery(GenericTemplateRequest request) {
        return ApiResponse.success(GenericTemplateController.convert(this.templateService.batch(request)));
    }

    private GenericTemplateDto convert(GenericTemplateRequestModel model) {
        GenericTemplateDto templateDto = ObjectUtils.convertNotNull(model, GenericTemplateDto.class);
        if (model.getGenericActionId() == null || model.getGenericActionId() < 0L) {
            model.setGenericActionId(model.getActionId());
        }
        templateDto.setActionId(model.getGenericActionId());
        templateDto.setVarList(model.getVarList());
        return templateDto;
    }

    private Page<GenericTemplateModel> convert(Page<GenericTemplateDto> page) {
        return new Page().setTotal(page.getTotal()).setPage(page.getPage()).setData(GenericTemplateController.convert(page.getData()));
    }

    public static List<GenericTemplateModel> convert(List<GenericTemplateDto> variables) {
        return variables.stream().map(dto -> ObjectUtils.convert(dto, GenericTemplateModel.class)).filter(Objects::nonNull).collect(Collectors.toList());
    }
}

