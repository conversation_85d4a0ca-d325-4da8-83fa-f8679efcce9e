/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Lists
 *  org.apache.logging.log4j.util.Strings
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.service.impl;

import com.google.common.collect.Lists;
import com.wacai.trike.mirana.domain.flow.po.FlowSubjectPO;
import com.wacai.trike.mirana.domain.flow.repository.FlowSubjectRepository;
import com.wacai.trike.mirana.lifecycle.SubjectProvider;
import com.wacai.trike.mirana.service.FlowSubjectDTO;
import com.wacai.trike.mirana.service.FlowSubjectService;
import com.wacai.trike.mirana.util.ObjectUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

@Service
public class FlowSubjectServiceImpl
implements FlowSubjectService {
    private static final Logger log = LoggerFactory.getLogger(FlowSubjectServiceImpl.class);
    @Autowired
    private FlowSubjectRepository subjectRepository;
    @Autowired
    private SubjectProvider subjectProvider;

    @Override
    public List<FlowSubjectDTO> findByFlowCode(String flowCode) {
        if (Strings.isBlank((String)flowCode)) {
            return Lists.newArrayList();
        }
        List<FlowSubjectPO> subjects = this.subjectRepository.findByFlowCode(flowCode);
        if (CollectionUtils.isEmpty(subjects)) {
            return Lists.newArrayList();
        }
        return subjects.stream().filter(Objects::nonNull).map(s -> ObjectUtils.convert(s, FlowSubjectDTO.class)).collect(Collectors.toList());
    }

    @Override
    public List<SubjectProvider.Subject> subjects() {
        return this.subjectProvider.getSubjects();
    }

    @Override
    public FlowSubjectDTO bind(FlowSubjectDTO subject) {
        FlowSubjectPO exist = this.subjectRepository.findByFlowCodeAndSubjectCode(subject.getFlowCode(), subject.getSubjectCode());
        if (exist != null) {
            return ObjectUtils.convert(exist, FlowSubjectDTO.class);
        }
        boolean bound = this.subjectProvider.bind(subject);
        if (!bound) {
            log.error("failed to bind subject {} for {}", subject.getSubjectCode(), subject.getFlowCode());
            return null;
        }
        FlowSubjectPO subjectPO = (FlowSubjectPO)BeanUtils.instantiateClass(FlowSubjectPO.class);
        BeanUtils.copyProperties(subject, subjectPO);
        FlowSubjectPO saved = (FlowSubjectPO)this.subjectRepository.save(subjectPO);
        return ObjectUtils.convert(saved, FlowSubjectDTO.class);
    }

    @Override
    public boolean cancel(FlowSubjectDTO subject) {
        boolean canceled = this.subjectProvider.cancel(subject);
        if (!canceled) {
            log.error("failed to cancel subject {} for {}", subject.getSubjectCode(), subject.getFlowCode());
            return false;
        }
        FlowSubjectPO exist = this.subjectRepository.findByFlowCodeAndSubjectCode(subject.getFlowCode(), subject.getSubjectCode());
        if (exist == null) {
            return false;
        }
        this.subjectRepository.deleteById(exist.getId());
        return true;
    }
}

