/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.context.po;

import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="instance_variable")
public class InstanceVariablePO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private String instanceUuid;
    private String taskUuid;
    private String field;
    private String value;
    @Enumerated(value=EnumType.STRING)
    private VariableType type;

    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    public String getTaskUuid() {
        return this.taskUuid;
    }

    public String getField() {
        return this.field;
    }

    public String getValue() {
        return this.value;
    }

    public VariableType getType() {
        return this.type;
    }

    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }

    public void setTaskUuid(String taskUuid) {
        this.taskUuid = taskUuid;
    }

    public void setField(String field) {
        this.field = field;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public void setType(VariableType type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "InstanceVariablePO(instanceUuid=" + this.getInstanceUuid() + ", taskUuid=" + this.getTaskUuid() + ", field=" + this.getField() + ", value=" + this.getValue() + ", type=" + (this.getType()) + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstanceVariablePO)) {
            return false;
        }
        InstanceVariablePO other = (InstanceVariablePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$instanceUuid = this.getInstanceUuid();
        String other$instanceUuid = other.getInstanceUuid();
        if (this$instanceUuid == null ? other$instanceUuid != null : !this$instanceUuid.equals(other$instanceUuid)) {
            return false;
        }
        String this$taskUuid = this.getTaskUuid();
        String other$taskUuid = other.getTaskUuid();
        if (this$taskUuid == null ? other$taskUuid != null : !this$taskUuid.equals(other$taskUuid)) {
            return false;
        }
        String this$field = this.getField();
        String other$field = other.getField();
        if (this$field == null ? other$field != null : !this$field.equals(other$field)) {
            return false;
        }
        String this$value = this.getValue();
        String other$value = other.getValue();
        if (this$value == null ? other$value != null : !this$value.equals(other$value)) {
            return false;
        }
        VariableType this$type = this.getType();
        VariableType other$type = other.getType();
        return !(this$type == null ? other$type != null : !((this$type)).equals(other$type));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof InstanceVariablePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $instanceUuid = this.getInstanceUuid();
        result = result * 59 + ($instanceUuid == null ? 43 : $instanceUuid.hashCode());
        String $taskUuid = this.getTaskUuid();
        result = result * 59 + ($taskUuid == null ? 43 : $taskUuid.hashCode());
        String $field = this.getField();
        result = result * 59 + ($field == null ? 43 : $field.hashCode());
        String $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : $value.hashCode());
        VariableType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        return result;
    }
}

