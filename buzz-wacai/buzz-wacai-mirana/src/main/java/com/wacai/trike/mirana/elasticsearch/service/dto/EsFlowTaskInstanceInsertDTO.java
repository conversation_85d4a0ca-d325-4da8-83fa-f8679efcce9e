/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.elasticsearch.service.dto;

import java.util.Date;
import java.util.List;

public class EsFlowTaskInstanceInsertDTO {
    private Long flowId;
    private String flowVersion;
    private String flowCode;
    private String flowInstanceId;
    private Date instanceTime;
    private Long loanId;
    private List<String> tags;
    private List<Long> nodeIdList;

    public Long getFlowId() {
        return this.flowId;
    }

    public String getFlowVersion() {
        return this.flowVersion;
    }

    public String getFlowCode() {
        return this.flowCode;
    }

    public String getFlowInstanceId() {
        return this.flowInstanceId;
    }

    public Date getInstanceTime() {
        return this.instanceTime;
    }

    public Long getLoanId() {
        return this.loanId;
    }

    public List<String> getTags() {
        return this.tags;
    }

    public List<Long> getNodeIdList() {
        return this.nodeIdList;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setFlowVersion(String flowVersion) {
        this.flowVersion = flowVersion;
    }

    public void setFlowCode(String flowCode) {
        this.flowCode = flowCode;
    }

    public void setFlowInstanceId(String flowInstanceId) {
        this.flowInstanceId = flowInstanceId;
    }

    public void setInstanceTime(Date instanceTime) {
        this.instanceTime = instanceTime;
    }

    public void setLoanId(Long loanId) {
        this.loanId = loanId;
    }

    public void setTags(List<String> tags) {
        this.tags = tags;
    }

    public void setNodeIdList(List<Long> nodeIdList) {
        this.nodeIdList = nodeIdList;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EsFlowTaskInstanceInsertDTO)) {
            return false;
        }
        EsFlowTaskInstanceInsertDTO other = (EsFlowTaskInstanceInsertDTO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$flowVersion = this.getFlowVersion();
        String other$flowVersion = other.getFlowVersion();
        if (this$flowVersion == null ? other$flowVersion != null : !this$flowVersion.equals(other$flowVersion)) {
            return false;
        }
        String this$flowCode = this.getFlowCode();
        String other$flowCode = other.getFlowCode();
        if (this$flowCode == null ? other$flowCode != null : !this$flowCode.equals(other$flowCode)) {
            return false;
        }
        String this$flowInstanceId = this.getFlowInstanceId();
        String other$flowInstanceId = other.getFlowInstanceId();
        if (this$flowInstanceId == null ? other$flowInstanceId != null : !this$flowInstanceId.equals(other$flowInstanceId)) {
            return false;
        }
        Date this$instanceTime = this.getInstanceTime();
        Date other$instanceTime = other.getInstanceTime();
        if (this$instanceTime == null ? other$instanceTime != null : !(this$instanceTime).equals(other$instanceTime)) {
            return false;
        }
        Long this$loanId = this.getLoanId();
        Long other$loanId = other.getLoanId();
        if (this$loanId == null ? other$loanId != null : !(this$loanId).equals(other$loanId)) {
            return false;
        }
        List<String> this$tags = this.getTags();
        List<String> other$tags = other.getTags();
        if (this$tags == null ? other$tags != null : !(this$tags).equals(other$tags)) {
            return false;
        }
        List<Long> this$nodeIdList = this.getNodeIdList();
        List<Long> other$nodeIdList = other.getNodeIdList();
        return !(this$nodeIdList == null ? other$nodeIdList != null : !(this$nodeIdList).equals(other$nodeIdList));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EsFlowTaskInstanceInsertDTO;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $flowVersion = this.getFlowVersion();
        result = result * 59 + ($flowVersion == null ? 43 : $flowVersion.hashCode());
        String $flowCode = this.getFlowCode();
        result = result * 59 + ($flowCode == null ? 43 : $flowCode.hashCode());
        String $flowInstanceId = this.getFlowInstanceId();
        result = result * 59 + ($flowInstanceId == null ? 43 : $flowInstanceId.hashCode());
        Date $instanceTime = this.getInstanceTime();
        result = result * 59 + ($instanceTime == null ? 43 : ($instanceTime).hashCode());
        Long $loanId = this.getLoanId();
        result = result * 59 + ($loanId == null ? 43 : ($loanId).hashCode());
        List<String> $tags = this.getTags();
        result = result * 59 + ($tags == null ? 43 : ($tags).hashCode());
        List<Long> $nodeIdList = this.getNodeIdList();
        result = result * 59 + ($nodeIdList == null ? 43 : ($nodeIdList).hashCode());
        return result;
    }

    public String toString() {
        return "EsFlowTaskInstanceInsertDTO(flowId=" + this.getFlowId() + ", flowVersion=" + this.getFlowVersion() + ", flowCode=" + this.getFlowCode() + ", flowInstanceId=" + this.getFlowInstanceId() + ", instanceTime=" + this.getInstanceTime() + ", loanId=" + this.getLoanId() + ", tags=" + this.getTags() + ", nodeIdList=" + this.getNodeIdList() + ")";
    }
}

