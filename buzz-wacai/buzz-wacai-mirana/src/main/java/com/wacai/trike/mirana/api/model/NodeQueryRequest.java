/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.Query
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.loan.trike.common.model.Query;
import javax.validation.constraints.NotBlank;

public class NodeQueryRequest
extends Query {
    @NotBlank
    private String bu;
    @NotBlank
    private String app;
    @NotBlank
    private String flow;

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getFlow() {
        return this.flow;
    }

    public NodeQueryRequest setBu(String bu) {
        this.bu = bu;
        return this;
    }

    public NodeQueryRequest setApp(String app) {
        this.app = app;
        return this;
    }

    public NodeQueryRequest setFlow(String flow) {
        this.flow = flow;
        return this;
    }

    public String toString() {
        return "NodeQueryRequest(bu=" + this.getBu() + ", app=" + this.getApp() + ", flow=" + this.getFlow() + ")";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof NodeQueryRequest)) {
            return false;
        }
        NodeQueryRequest other = (NodeQueryRequest)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        return !(this$flow == null ? other$flow != null : !this$flow.equals(other$flow));
    }

    protected boolean canEqual(Object other) {
        return other instanceof NodeQueryRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        return result;
    }
}

