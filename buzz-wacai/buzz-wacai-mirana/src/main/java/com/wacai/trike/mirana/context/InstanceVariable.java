/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.context;

import com.wacai.trike.mirana.common.enums.VariableType;
import java.io.Serializable;

public class InstanceVariable
implements Serializable {
    private Long id;
    private String field;
    private String value;
    private VariableType type;
    private long timestamp;
    private boolean dirty;

    public Long getId() {
        return this.id;
    }

    public String getField() {
        return this.field;
    }

    public String getValue() {
        return this.value;
    }

    public VariableType getType() {
        return this.type;
    }

    public long getTimestamp() {
        return this.timestamp;
    }

    public boolean isDirty() {
        return this.dirty;
    }

    public InstanceVariable setId(Long id) {
        this.id = id;
        return this;
    }

    public InstanceVariable setField(String field) {
        this.field = field;
        return this;
    }

    public InstanceVariable setValue(String value) {
        this.value = value;
        return this;
    }

    public InstanceVariable setType(VariableType type) {
        this.type = type;
        return this;
    }

    public InstanceVariable setTimestamp(long timestamp) {
        this.timestamp = timestamp;
        return this;
    }

    public InstanceVariable setDirty(boolean dirty) {
        this.dirty = dirty;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstanceVariable)) {
            return false;
        }
        InstanceVariable other = (InstanceVariable)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$field = this.getField();
        String other$field = other.getField();
        if (this$field == null ? other$field != null : !this$field.equals(other$field)) {
            return false;
        }
        String this$value = this.getValue();
        String other$value = other.getValue();
        if (this$value == null ? other$value != null : !this$value.equals(other$value)) {
            return false;
        }
        VariableType this$type = this.getType();
        VariableType other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        if (this.getTimestamp() != other.getTimestamp()) {
            return false;
        }
        return this.isDirty() == other.isDirty();
    }

    protected boolean canEqual(Object other) {
        return other instanceof InstanceVariable;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $field = this.getField();
        result = result * 59 + ($field == null ? 43 : $field.hashCode());
        String $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : $value.hashCode());
        VariableType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        long $timestamp = this.getTimestamp();
        result = result * 59 + (int)($timestamp >>> 32 ^ $timestamp);
        result = result * 59 + (this.isDirty() ? 79 : 97);
        return result;
    }

    public String toString() {
        return "InstanceVariable(id=" + this.getId() + ", field=" + this.getField() + ", value=" + this.getValue() + ", type=" + (this.getType()) + ", timestamp=" + this.getTimestamp() + ", dirty=" + this.isDirty() + ")";
    }
}

