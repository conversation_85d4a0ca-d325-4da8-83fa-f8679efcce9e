/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.service;

import java.time.LocalDateTime;
import java.util.List;

public class BusinessRequest {
    private List<String> codes;
    private List<String> names;
    private List<Long> ids;
    private LocalDateTime start;
    private LocalDateTime end;

    BusinessRequest(List<String> codes, List<String> names, List<Long> ids, LocalDateTime start, LocalDateTime end) {
        this.codes = codes;
        this.names = names;
        this.ids = ids;
        this.start = start;
        this.end = end;
    }

    public static BusinessRequestBuilder builder() {
        return new BusinessRequestBuilder();
    }

    public List<String> getCodes() {
        return this.codes;
    }

    public List<String> getNames() {
        return this.names;
    }

    public List<Long> getIds() {
        return this.ids;
    }

    public LocalDateTime getStart() {
        return this.start;
    }

    public LocalDateTime getEnd() {
        return this.end;
    }

    public static class BusinessRequestBuilder {
        private List<String> codes;
        private List<String> names;
        private List<Long> ids;
        private LocalDateTime start;
        private LocalDateTime end;

        BusinessRequestBuilder() {
        }

        public BusinessRequestBuilder codes(List<String> codes) {
            this.codes = codes;
            return this;
        }

        public BusinessRequestBuilder names(List<String> names) {
            this.names = names;
            return this;
        }

        public BusinessRequestBuilder ids(List<Long> ids) {
            this.ids = ids;
            return this;
        }

        public BusinessRequestBuilder start(LocalDateTime start) {
            this.start = start;
            return this;
        }

        public BusinessRequestBuilder end(LocalDateTime end) {
            this.end = end;
            return this;
        }

        public BusinessRequest build() {
            return new BusinessRequest(this.codes, this.names, this.ids, this.start, this.end);
        }

        public String toString() {
            return "BusinessRequest.BusinessRequestBuilder(codes=" + this.codes + ", names=" + this.names + ", ids=" + this.ids + ", start=" + this.start + ", end=" + this.end + ")";
        }
    }
}

