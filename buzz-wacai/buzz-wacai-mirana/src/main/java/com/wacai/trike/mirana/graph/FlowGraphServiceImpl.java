/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.cache.Cache
 *  com.google.common.cache.CacheBuilder
 *  com.google.common.collect.Lists
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.OrderSpecifier
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.Projections
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.JPQLQuery
 *  com.querydsl.jpa.JPQLQueryFactory
 *  com.wacai.trike.mirana.api.constant.FlowStatus
 *  com.wacai.trike.mirana.api.constant.FormType
 *  com.wacai.trike.mirana.api.constant.NodeType
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  javax.validation.constraints.NotNull
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.boot.context.event.ApplicationStartedEvent
 *  org.springframework.context.ApplicationListener
 *  org.springframework.stereotype.Service
 *  org.springframework.util.Assert
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.graph;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.validation.constraints.NotNull;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.OrderSpecifier;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.Projections;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.JPQLQuery;
import com.querydsl.jpa.JPQLQueryFactory;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.FormType;
import com.wacai.trike.mirana.api.constant.NodeType;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;
import com.wacai.trike.mirana.domain.edge.po.EdgePO;
import com.wacai.trike.mirana.domain.edge.repository.EdgeRepository;
import com.wacai.trike.mirana.domain.expression.po.ExpressionPO;
import com.wacai.trike.mirana.domain.expression.repository.ExpressionRepository;
import com.wacai.trike.mirana.domain.flow.po.FlowUnionNodePO;
import com.wacai.trike.mirana.domain.flow.po.QFlowPO;
import com.wacai.trike.mirana.domain.flow.repository.FlowUnionNodeRepository;
import com.wacai.trike.mirana.domain.form.po.FormPO;
import com.wacai.trike.mirana.domain.form.repository.FormRepository;
import com.wacai.trike.mirana.domain.manage.po.ActionTemplatePO;
import com.wacai.trike.mirana.domain.manage.repository.ActionTemplateRepository;
import com.wacai.trike.mirana.domain.node.po.NodePO;
import com.wacai.trike.mirana.domain.node.repository.NodeRepository;
import com.wacai.trike.mirana.util.ObjectUtils;

@Service
public class FlowGraphServiceImpl implements FlowGraphService,ApplicationListener<ApplicationStartedEvent> {
	
    private static final Logger log = LoggerFactory.getLogger(FlowGraphServiceImpl.class);
    
    private static final String FLOW_CACHE_JOINER = "/";
    
    @Autowired
    private JPQLQueryFactory queryFactory;
    @Autowired
    private NodeRepository nodeRepository;
    @Autowired
    private FlowUnionNodeRepository flowUnionNodeRepository;
    @Autowired
    private EdgeRepository edgeRepository;
    @Autowired
    private ExpressionRepository expressionRepository;
    @Autowired
    private FormRepository formRepository;
    @Autowired
    private ActionTemplateRepository actionTemplateRepository;
    
    private volatile boolean init = false;
    
    private Lock lock = new ReentrantLock();
    
    private Condition condition = this.lock.newCondition();
    
    private final Cache<String, FlowGraph> FLOW_VERSION_CACHE = CacheBuilder.newBuilder().expireAfterAccess(30L, TimeUnit.MINUTES).initialCapacity(20).maximumSize(50L).recordStats().build();
    private final Cache<String, FlowGraph> Flow_CODE_CACHE;
    private final Cache<Long, FlowGraph> FLOW_ID_CACHE = CacheBuilder.newBuilder().expireAfterAccess(30L, TimeUnit.MINUTES).initialCapacity(20).maximumSize(50L).recordStats().build();

    public FlowGraphServiceImpl() {
        this.Flow_CODE_CACHE = CacheBuilder.newBuilder().expireAfterAccess(30L, TimeUnit.MINUTES).initialCapacity(20).maximumSize(50L).recordStats().build();
    }


    public void onApplicationEvent(ApplicationStartedEvent event) {
		this.lock.lock();
        try {
            log.info("start load flow graph");
            List<FlowGraph> flowGraphs = (this.buildQuery().where(new Predicate[]{QFlowPO.flowPO.status.eq(FlowStatus.ENABLED).and((Predicate)QFlowPO.flowPO.active.isTrue())})).fetch();
            for (FlowGraph graph : flowGraphs) {
                try {
                    this.initGraph(graph);
                    this.cache(graph, true);
                    log.info("flow [{} - {} - {} - {}] loaded", new Object[]{graph.getBu(), graph.getApp(), graph.getCode(), graph.getName()});
                }
                catch (Exception e) {
                    log.error("load graph failed for [{} - {}]", new Object[]{graph.getCode(), graph.getName(), e});
                }
            }
            this.init = true;
            log.info("load flow graphs completed");
        }
        catch (Exception e) {
            log.error("loan graphs failed", (Throwable)e);
        }
        finally {
            this.condition.signalAll();
            this.lock.unlock();
        }
    }

    @Override
    public FlowGraph getFlowGraph(String bu, String app, String flowCode, String version) {
        if (!this.init) {
            this.waitInit();
        }
        String key = this.flowVersionKey(bu, app, flowCode, version);
        FlowGraph cached = this.FLOW_VERSION_CACHE.getIfPresent(key);
        if (cached != null) {
            return cached;
        }
        FlowGraph graph = (FlowGraph)(this.buildQuery()
        		.where(new Predicate[]{QBusinessPO.businessPO.code.eq(bu)
        				.and((Predicate)QApplicationPO.applicationPO.code.eq(app))
        				.and((Predicate)QFlowPO.flowPO.code.eq(flowCode))
        				.and((Predicate)QFlowPO.flowPO.version.eq(version))
        				.and((Predicate)QFlowPO.flowPO.status.eq(FlowStatus.ENABLED))})).fetchFirst();
        if (graph == null) {
            return null;
        }
        this.cache(graph, false);
        return graph;
    }

    @Override
    public FlowGraph getFlowGraph(Long flowId) {
        FlowGraph cached;
        if (!this.init) {
            this.waitInit();
        }
        if ((cached = (FlowGraph)this.FLOW_ID_CACHE.getIfPresent(flowId)) != null) {
            return cached;
        }
        FlowGraph graph = (FlowGraph)((JPQLQuery)this.buildQuery().where(new Predicate[]{QFlowPO.flowPO.id.eq(flowId)})).fetchFirst();
        if (graph == null) {
            log.info("Not found graph {}", flowId);
            return null;
        }
        this.cache(graph, false);
        return graph;
    }

    @Override
    public FlowGraph getFlowGraph(String flowCode) {
        FlowGraph cached = (FlowGraph)this.Flow_CODE_CACHE.getIfPresent(flowCode);
        if (cached != null) {
            return cached;
        }
        FlowGraph graph = (FlowGraph)((JPQLQuery)((JPQLQuery)((JPQLQuery)this.buildQuery().where(new Predicate[]{QFlowPO.flowPO.code.eq(flowCode)})).orderBy(new OrderSpecifier[]{QFlowPO.flowPO.version.desc()})).limit(1L)).fetchFirst();
        if (graph == null) {
            log.info("Not found graph {}", flowCode);
            return null;
        }
        this.Flow_CODE_CACHE.put(flowCode, graph);
        log.info("Flow_CODE_CACHE Cache hit rate: {}", this.Flow_CODE_CACHE.stats().hitRate());
        return graph;
    }

    @Override
    public List<String> versions(String flowCode) {
        return Collections.unmodifiableList(((JPQLQuery)((JPQLQuery)this.queryFactory.select((com.querydsl.core.types.Expression)QFlowPO.flowPO.version).from(new EntityPath[]{QFlowPO.flowPO}).where(new Predicate[]{QFlowPO.flowPO.code.eq(flowCode)})).orderBy(new OrderSpecifier[]{QFlowPO.flowPO.version.desc()})).fetch());
    }

    @Override
    public void refreshFlowGraph(String flowCode) {
        FlowGraph latest = (FlowGraph)((JPQLQuery)((JPQLQuery)((JPQLQuery)this.buildQuery().where(new Predicate[]{QFlowPO.flowPO.code.eq(flowCode).and((Predicate)QFlowPO.flowPO.status.eq(FlowStatus.ENABLED))})).orderBy(new OrderSpecifier[]{QFlowPO.flowPO.version.desc()})).limit(1L)).fetchFirst();
        if (latest == null) {
            log.warn("No enable status flows of {}", flowCode);
            return;
        }
        this.cache(latest, false);
        log.info("flow {} latest enable version {}", flowCode, latest.getVersion());
        this.Flow_CODE_CACHE.put(latest.getCode(), latest);
    }

    private void cache(@NotNull FlowGraph graph, boolean initialized) {
        if (!initialized) {
            this.initGraph(graph);
        }
        this.FLOW_ID_CACHE.put(graph.getId(), graph);
        log.info("FLOW_ID_CACHE Cache hit rate: {}", this.FLOW_ID_CACHE.stats().hitRate());
        this.FLOW_VERSION_CACHE.put(this.flowVersionKey(graph.getBu(), graph.getApp(), graph.getCode(), graph.getVersion()), graph);
        log.info("FLOW_VERSION_CACHE Cache hit rate: {}", this.FLOW_VERSION_CACHE.stats().hitRate());
    }

    @Override
    public FlowGraph refreshFlowGraph(String bu, String app, String flowCode) {
        if (!this.init) {
            this.waitInit();
        }
        BooleanExpression where = QBusinessPO.businessPO.code.eq(bu).and((Predicate)QApplicationPO.applicationPO.code.eq(app)).and((Predicate)QFlowPO.flowPO.code.eq(flowCode)).and((Predicate)QFlowPO.flowPO.status.eq(FlowStatus.ENABLED));
        FlowGraph graph = (FlowGraph)((JPQLQuery)this.buildQuery().where(new Predicate[]{where})).fetchOne();
        if (Objects.isNull(graph)) {
            throw new RuntimeException("流程未启用");
        }
        this.initGraph(graph);
        return graph;
    }

    @Override
    public FlowGraph refreshFlowGraph(Long flowId) {
        FlowGraph graph;
        if (!this.init) {
            this.waitInit();
        }
        if (Objects.isNull(graph = (FlowGraph)((JPQLQuery)this.buildQuery().where(new Predicate[]{QFlowPO.flowPO.id.eq(flowId).and((Predicate)QFlowPO.flowPO.status.eq(FlowStatus.ENABLED))})).fetchOne())) {
            throw new RuntimeException("流程未启用");
        }
        this.initGraph(graph);
        return graph;
    }

    @Override
    public Node next(long flowId, long nodeId) {
        if (flowId <= 0L || nodeId <= 0L) {
            return null;
        }
        FlowGraph graph = this.getFlowGraph(flowId);
        if (graph == null) {
            log.info("Not found graph {}", flowId);
            return null;
        }
        Node node = graph.getNode(nodeId);
        if (node == null || CollectionUtils.isEmpty(node.getEdges()) || node.getEdges().size() != 1) {
            log.info("Not found node or edge {}", nodeId);
            return null;
        }
        return node.getEdges().get(0).getToNode();
    }

    private JPQLQuery<FlowGraph> buildQuery() {
        return this.queryFactory.select((com.querydsl.core.types.Expression)Projections.fields(FlowGraph.class, (com.querydsl.core.types.Expression[])new com.querydsl.core.types.Expression[]{QBusinessPO.businessPO.code.as("bu"), QApplicationPO.applicationPO.code.as("app"), QFlowPO.flowPO.code, QFlowPO.flowPO.id, QFlowPO.flowPO.timeout, QFlowPO.flowPO.type, QFlowPO.flowPO.name, QFlowPO.flowPO.version})).from(new EntityPath[]{QFlowPO.flowPO}).join((EntityPath)QApplicationPO.applicationPO).on(new Predicate[]{QApplicationPO.applicationPO.id.eq(QFlowPO.flowPO.appId)}).join((EntityPath)QBusinessPO.businessPO).on(new Predicate[]{QBusinessPO.businessPO.id.eq(QApplicationPO.applicationPO.buId)});
    }

    private void initGraph(FlowGraph graph) {
        this.initNodes(graph);
    }

    private String flowVersionKey(String bu, String app, String code, String version) {
        return String.join(FLOW_CACHE_JOINER, Lists.newArrayList(new String[]{bu, app, code, version}));
    }

    private void initNodes(FlowGraph flowGraph) {
        List<FlowUnionNodePO> unionNodes = this.flowUnionNodeRepository.findByFlowId(flowGraph.getId());
        if (CollectionUtils.isEmpty(unionNodes)) {
            this.unionEstablishing(flowGraph.getId());
            unionNodes = this.flowUnionNodeRepository.findByFlowId(flowGraph.getId());
        }
        if (CollectionUtils.isEmpty(unionNodes)) {
            log.warn("not found any node of flow {}", flowGraph.getId());
            return;
        }
        Map<Long, Node> nodeMap = unionNodes.stream().map(union -> {
            NodePO n = this.nodeRepository.findById(union.getNodeId()).orElse(null);
            Assert.notNull(n, (String)("not found node " + union.getNodeId()));
            Node node = ObjectUtils.convert(n, Node.class);
            Assert.notNull(node, (String)("not found node " + n.getId()));
            if (n.getActionTemplateId() != null) {
                node.setTemplateId(n.getActionTemplateId());
                Action action = this.build(n.getActionTemplateId());
                if (action != null) {
                    node.setAction(action);
                }
            }
            if (n.getTaskType() == TaskType.START_SUB_FLOW) {
                node.setSubflowCode(n.getTaskContent());
            }
            return node;
        }).collect(Collectors.toMap(Node::getId, Function.identity()));
        Node startNode = nodeMap.values().stream().filter(n -> NodeType.START == n.getType()).findFirst().orElseThrow(RuntimeException::new);
        flowGraph.setStartNode(startNode);
        flowGraph.setNodeMap(nodeMap);
        this.initEdges(flowGraph);
    }

    private void unionEstablishing(long flowId) {
        List<NodePO> nodes = this.nodeRepository.findByFlowId(flowId);
        if (CollectionUtils.isEmpty(nodes)) {
            log.warn("not found any node of flow {}", flowId);
            return;
        }
        nodes.forEach(node -> {
            FlowUnionNodePO union = new FlowUnionNodePO();
            union.setFlowId(flowId);
            union.setNodeId(node.getId());
            this.flowUnionNodeRepository.save(union);
            log.info("established union for flow {} node {}", flowId, node.getId());
        });
    }

    private Action build(long actionTmpId) {
        ActionTemplatePO actionTemplatePO = this.actionTemplateRepository.findById(actionTmpId).orElse(null);
        if (actionTemplatePO == null) {
            log.error("not found action template {}", actionTmpId);
            return null;
        }
        Action action = new Action();
        action.setAction(actionTemplatePO.getAction());
        action.setActionType(actionTemplatePO.getActionType());
        return action;
    }

    private void initEdges(FlowGraph flowGraph) {
        Map<Long, List<EdgePO>> edges = this.edgeRepository.findByFlowId(flowGraph.getId()).stream().collect(Collectors.groupingBy(EdgePO::getFromId));
        this.initEdge(flowGraph, flowGraph.getStartNode(), edges);
    }

    private void initEdge(FlowGraph flowGraph, Node node, Map<Long, List<EdgePO>> edges) {
        if (node.isInit()) {
            return;
        }
        List<Edge> edge = edges.getOrDefault(node.getId(), Collections.emptyList()).stream().map(e -> this.convert((EdgePO)e, flowGraph)).collect(Collectors.toList());
        this.initForms(node);
        node.setEdges(edge);
        node.setInit(true);
        for (Edge e2 : edge) {
            this.initEdge(flowGraph, e2.getToNode(), edges);
        }
    }

    private Edge convert(EdgePO ep, FlowGraph flowGraph) {
        Edge e = Objects.requireNonNull(ObjectUtils.convert(ep, Edge.class));
        e.setFromNode(flowGraph.getNode(ep.getFromId()));
        e.setToNode(flowGraph.getNode(ep.getToId()));
        this.initExpressions(e);
        return e;
    }

    private void initExpressions(Edge edge) {
        List<ExpressionPO> exps = this.expressionRepository.findByEdgeId(edge.getId());
        List<Expression> exp = exps.stream().filter(Objects::nonNull).map(e -> ObjectUtils.convert(e, Expression.class)).collect(Collectors.toList());
        edge.setExpressions(exp);
    }

    private void initForms(Node node) {
        List<FormPO> forms = this.formRepository.findByNodeId(node.getId());
        for (FormPO f : forms) {
            if (f.getType() == FormType.INPUT) {
                node.getInputForms().put(f.getField(), f.getValue());
                continue;
            }
            if (f.getType() != FormType.OUTPUT) continue;
            node.getOutputForms().put(f.getField(), f.getValue());
        }
    }

    private void waitInit() {
        this.lock.lock();
        try {
            if (!this.init && !this.condition.await(30L, TimeUnit.SECONDS)) {
                throw new InterruptedException("wait time out");
            }
        }
        catch (InterruptedException e) {
            log.error("wait init graph failed", (Throwable)e);
            throw new RuntimeException(e);
        }
        finally {
            this.lock.unlock();
        }
    }
}

