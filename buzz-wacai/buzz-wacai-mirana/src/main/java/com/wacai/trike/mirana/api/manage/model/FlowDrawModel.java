/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 *  javax.validation.constraints.NotNull
 */
package com.wacai.trike.mirana.api.manage.model;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

public class FlowDrawModel
implements Serializable {
    @NotBlank
    private Long flowId;
    @NotBlank
    private String code;
    @NotNull
    private Long appId;
    private String timeout;

    public Long getFlowId() {
        return this.flowId;
    }

    public String getCode() {
        return this.code;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getTimeout() {
        return this.timeout;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setTimeout(String timeout) {
        this.timeout = timeout;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowDrawModel)) {
            return false;
        }
        FlowDrawModel other = (FlowDrawModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$timeout = this.getTimeout();
        String other$timeout = other.getTimeout();
        return !(this$timeout == null ? other$timeout != null : !this$timeout.equals(other$timeout));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowDrawModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $timeout = this.getTimeout();
        result = result * 59 + ($timeout == null ? 43 : $timeout.hashCode());
        return result;
    }

    public String toString() {
        return "FlowDrawModel(flowId=" + this.getFlowId() + ", code=" + this.getCode() + ", appId=" + this.getAppId() + ", timeout=" + this.getTimeout() + ")";
    }
}

