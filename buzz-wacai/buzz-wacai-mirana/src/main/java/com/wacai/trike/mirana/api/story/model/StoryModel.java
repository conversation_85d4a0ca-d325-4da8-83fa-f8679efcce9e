/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.story.model;

import com.wacai.trike.mirana.api.constant.FlowStatus;
import java.io.Serializable;
import java.time.LocalDateTime;

public class StoryModel
implements Serializable {
    private Long id;
    private String code;
    private String name;
    private String version;
    private FlowStatus status;
    private LocalDateTime updatedTime;

    public static StoryModelBuilder builder() {
        return new StoryModelBuilder();
    }

    public StoryModel() {
    }

    public StoryModel(Long id, String code, String name, String version, FlowStatus status, LocalDateTime updatedTime) {
        this.id = id;
        this.code = code;
        this.name = name;
        this.version = version;
        this.status = status;
        this.updatedTime = updatedTime;
    }

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getVersion() {
        return this.version;
    }

    public FlowStatus getStatus() {
        return this.status;
    }

    public LocalDateTime getUpdatedTime() {
        return this.updatedTime;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setStatus(FlowStatus status) {
        this.status = status;
    }

    public void setUpdatedTime(LocalDateTime updatedTime) {
        this.updatedTime = updatedTime;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryModel)) {
            return false;
        }
        StoryModel other = (StoryModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$version = this.getVersion();
        String other$version = other.getVersion();
        if (this$version == null ? other$version != null : !this$version.equals(other$version)) {
            return false;
        }
        FlowStatus this$status = this.getStatus();
        FlowStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !((this$status)).equals(other$status)) {
            return false;
        }
        LocalDateTime this$updatedTime = this.getUpdatedTime();
        LocalDateTime other$updatedTime = other.getUpdatedTime();
        return !(this$updatedTime == null ? other$updatedTime != null : !(this$updatedTime).equals(other$updatedTime));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $version = this.getVersion();
        result = result * 59 + ($version == null ? 43 : $version.hashCode());
        FlowStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
        LocalDateTime $updatedTime = this.getUpdatedTime();
        result = result * 59 + ($updatedTime == null ? 43 : ($updatedTime).hashCode());
        return result;
    }

    public String toString() {
        return "StoryModel(id=" + this.getId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", version=" + this.getVersion() + ", status=" + (this.getStatus()) + ", updatedTime=" + this.getUpdatedTime() + ")";
    }

    public static class StoryModelBuilder {
        private Long id;
        private String code;
        private String name;
        private String version;
        private FlowStatus status;
        private LocalDateTime updatedTime;

        StoryModelBuilder() {
        }

        public StoryModelBuilder id(Long id) {
            this.id = id;
            return this;
        }

        public StoryModelBuilder code(String code) {
            this.code = code;
            return this;
        }

        public StoryModelBuilder name(String name) {
            this.name = name;
            return this;
        }

        public StoryModelBuilder version(String version) {
            this.version = version;
            return this;
        }

        public StoryModelBuilder status(FlowStatus status) {
            this.status = status;
            return this;
        }

        public StoryModelBuilder updatedTime(LocalDateTime updatedTime) {
            this.updatedTime = updatedTime;
            return this;
        }

        public StoryModel build() {
            return new StoryModel(this.id, this.code, this.name, this.version, this.status, this.updatedTime);
        }

        public String toString() {
            return "StoryModel.StoryModelBuilder(id=" + this.id + ", code=" + this.code + ", name=" + this.name + ", version=" + this.version + ", status=" + (this.status) + ", updatedTime=" + this.updatedTime + ")";
        }
    }
}

