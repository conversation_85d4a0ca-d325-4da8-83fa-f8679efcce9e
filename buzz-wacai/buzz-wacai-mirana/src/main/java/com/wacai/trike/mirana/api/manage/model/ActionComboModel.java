/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 *  com.wacai.loan.trike.common.model.ComboBox
 */
package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.trike.mirana.api.manage.model.VariableComboModel;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class ActionComboModel
extends ComboBox<String> {
    private Long id;
    private List<VariableComboModel> requiredVariable;

    public Long getId() {
        return this.id;
    }

    public List<VariableComboModel> getRequiredVariable() {
        return this.requiredVariable;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setRequiredVariable(List<VariableComboModel> requiredVariable) {
        this.requiredVariable = requiredVariable;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ActionComboModel)) {
            return false;
        }
        ActionComboModel other = (ActionComboModel)(o);
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        List<VariableComboModel> this$requiredVariable = this.getRequiredVariable();
        List<VariableComboModel> other$requiredVariable = other.getRequiredVariable();
        return !(this$requiredVariable == null ? other$requiredVariable != null : !(this$requiredVariable).equals(other$requiredVariable));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ActionComboModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        List<VariableComboModel> $requiredVariable = this.getRequiredVariable();
        result = result * 59 + ($requiredVariable == null ? 43 : ($requiredVariable).hashCode());
        return result;
    }

    public String toString() {
        return "ActionComboModel(super=" + super.toString() + ", id=" + this.getId() + ", requiredVariable=" + this.getRequiredVariable() + ")";
    }
}

