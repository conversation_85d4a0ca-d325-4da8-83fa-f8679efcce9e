/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.criteria.CriteriaBuilder
 *  javax.persistence.criteria.CriteriaBuilder$In
 *  javax.persistence.criteria.Expression
 *  javax.persistence.criteria.Predicate
 *  javax.persistence.criteria.Root
 */
package com.wacai.trike.mirana.util;

import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.Expression;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;

public class CriteriaBuilderUtils {
    public static CriteriaBuilder.In buildString(Root<?> root, CriteriaBuilder builder, String property, List<String> values) {
        CriteriaBuilder.In inClause = builder.in((Expression)root.get(property));
        for (String value : values) {
            inClause.value(value);
        }
        return inClause;
    }

    public static CriteriaBuilder.In buildLong(Root<?> root, CriteriaBuilder builder, String property, List<Long> values) {
        CriteriaBuilder.In inClause = builder.in((Expression)root.get(property));
        for (Long value : values) {
            inClause.value(value);
        }
        return inClause;
    }

    public static Predicate excludeNoAction(Root<?> root, CriteriaBuilder builder, boolean active) {
        return builder.equal(root.get("active").as(Integer.class), (active ? 1 : 0));
    }
}

