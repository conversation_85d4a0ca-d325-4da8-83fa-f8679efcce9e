/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.google.common.collect.Lists
 *  com.wacai.common.redis.RedisException
 *  com.wacai.trike.cloud.proxy.redis.RedisProxy
 *  org.apache.logging.log4j.util.Strings
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.event;

import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.wacai.common.redis.RedisException;
import com.wacai.trike.cloud.proxy.redis.RedisProxy;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.InstanceContextService;
import com.wacai.trike.mirana.domain.context.po.TaskInstancePO;
import com.wacai.trike.mirana.domain.context.repository.TaskInstanceRepository;
import com.wacai.trike.mirana.elasticsearch.service.api.EsQueryFlowTaskInstanceService;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceEventDTO;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceInsertDTO;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceUpdateDTO;
import com.wacai.trike.mirana.graph.FlowGraph;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.lifecycle.SubjectProvider;
import com.wacai.trike.mirana.util.ObjectMappers;
import java.sql.Date;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class ESProcessingFactory {
    private static final Logger log = LoggerFactory.getLogger(ESProcessingFactory.class);
    @Autowired
    private EsQueryFlowTaskInstanceService esService;
    @Autowired
    private FlowGraphService graphService;
    @Autowired
    private TaskInstanceRepository taskInstanceRepository;
    @Autowired
    private ThreadPoolTaskExecutor taskExecutor;
    @Autowired
    private RedisProxy redisProxy;
    @Autowired
    private InstanceContextService instanceContextService;
    private static final String ES_RECOUP_KEY = "mirana_es_recoup_key";

    public void process(Salad salad) {
        if (salad.type == Type.INIT) {
            this.init(salad.context);
        }
        if (salad.type == Type.UPDATE_NODE) {
            this.taskExecutor.submit(() -> this.update(salad.context, salad.node));
        }
        if (salad.type == Type.UPDATE_SUBJECT) {
            this.taskExecutor.submit(() -> this.update(salad.context, salad.metadata));
        }
    }

    public void recoup() {
        try {
            Long len = this.redisProxy.llen(ES_RECOUP_KEY);
            log.info("current len {}", len);
            if (len == null || len == 0L) {
                return;
            }
            while (len > 0L) {
                String value = this.redisProxy.lpop(ES_RECOUP_KEY);
                String[] vs = value.split("_");
                EsFlowTaskInstanceUpdateDTO update = new EsFlowTaskInstanceUpdateDTO();
                update.setFlowInstanceId(vs[0]);
                update.setNodeIdList(Lists.newArrayList((Object[])new Long[]{Long.valueOf(vs[1])}));
                boolean success = this.esService.update(update);
                if (success) {
                    log.info("success to recoup {}", value);
                } else {
                    this.init(this.instanceContextService.load(update.getFlowInstanceId()));
                    this.redisProxy.rpush(ES_RECOUP_KEY, new String[]{value});
                    log.error("should never occurred: retry to recoup drop-data {} to redis", value);
                }
                Long l = len;
                Long l2 = len = Long.valueOf(len - 1L);
            }
        }
        catch (RedisException e) {
            log.error("occurred exception: recoup drop-data from redis", (Throwable)e);
        }
    }

    private void init(InstanceContext context) {
        EsFlowTaskInstanceInsertDTO insert = new EsFlowTaskInstanceInsertDTO();
        insert.setFlowId(context.getFlowId());
        insert.setFlowCode(context.getFlowCode());
        FlowGraph graph = this.graphService.getFlowGraph(context.getFlowId());
        if (graph == null) {
            log.error("should never occurred: not found graph of {}", context.getFlowId());
        } else {
            insert.setFlowVersion(graph.getVersion());
        }
        insert.setFlowInstanceId(context.getUuid());
        if (Strings.isNotBlank((String)context.getBzKey())) {
            insert.setLoanId(Long.valueOf(context.getBzKey()));
        }
        if (context.getCreatedTime() != null) {
            insert.setInstanceTime(Date.from(context.getCreatedTime().atZone(ZoneId.systemDefault()).toInstant()));
        } else {
            insert.setInstanceTime(Date.from(LocalDateTime.now().atZone(ZoneId.systemDefault()).toInstant()));
        }
        String birthMarks = context.getEffectiveVariables().getOrDefault("birthmarks", null);
        if (Strings.isNotBlank((String)birthMarks)) {
            insert.setTags(ObjectMappers.mustReadValue(birthMarks, new TypeReference<List<String>>(){}));
        }
        log.info("drop off instance {}", ObjectMappers.mustWriteValue(insert));
        this.esService.insert(insert);
        context.setEsSync(true);
    }

    private void update(InstanceContext context, Node node) {
        this.update(context, node, null, null);
    }

    private void update(InstanceContext context, SubjectProvider.Metadata metadata) {
        this.update(context, null, metadata, context.getFlowCode());
        InstanceContext sub = this.instanceContextService.loadSub(context.getUuid(), context.getCurrentNodeId());
        if (sub == null) {
            log.warn("not found sub instance of parent {} node {}", context.getUuid(), context.getCurrentNodeId());
            return;
        }
        this.update(sub, null, metadata, context.getFlowCode());
        HashMap<String, String> vars = new HashMap<String, String>();
        vars.put("event_subscribe", ObjectMappers.mustWriteValue(metadata));
        sub.addVariable(vars, VariableType.EVENT_SUBSCRIBE);
        sub.setDirty(true);
        this.instanceContextService.persistence(sub);
    }

    private void update(InstanceContext context, Node node, SubjectProvider.Metadata metadata, String storyCode) {
        EsFlowTaskInstanceUpdateDTO update = new EsFlowTaskInstanceUpdateDTO();
        update.setFlowInstanceId(context.getUuid());
        if (node != null) {
            update.setNodeIdList(Lists.newArrayList((Object[])new Long[]{node.getId()}));
        }
        if (metadata != null) {
            EsFlowTaskInstanceEventDTO event = new EsFlowTaskInstanceEventDTO();
            event.setEventCode(metadata.getSubscriptionCode());
            event.setNodeId(context.getCurrentNodeId());
            update.setEventList(Lists.newArrayList((Object[])new EsFlowTaskInstanceEventDTO[]{event}));
            update.setStoryCode(storyCode);
        }
        if (context.isDerailed()) {
            update.setFlowId(context.getFlowId());
            update.setFlowVersion(context.getVersion());
        }
        if (!context.isEsSync()) {
            log.info("not found {} from ES, try to init", context.getUuid());
            this.init(context);
            List<TaskInstancePO> histories = this.taskInstanceRepository.findByInstanceUuid(context.getUuid());
            if (!CollectionUtils.isEmpty(histories)) {
                update.getNodeIdList().addAll(histories.stream().map(TaskInstancePO::getNodeId).collect(Collectors.toList()));
            }
        }
        boolean success = this.esService.update(update);
        if (node == null) {
            return;
        }
        if (success) {
            log.info("drop off node task {}", ObjectMappers.mustWriteValue(update));
            TaskInstancePO task = this.taskInstanceRepository.findTop1ByInstanceUuidAndNodeIdOrderByCreatedTimeDesc(context.getUuid(), node.getId());
            if (task == null) {
                log.error("should never occurred: not found task instance of {} {}", context.getUuid(), node.getId());
                return;
            }
            task.setEsSync(true);
            this.taskInstanceRepository.save(task);
        } else {
            String value = context.getUuid() + "_" + node.getId();
            log.warn("request es server occurred exception: will store current node drop-data {} to redis List", value);
            try {
                this.redisProxy.rpush(ES_RECOUP_KEY, new String[]{value});
            }
            catch (RedisException e) {
                log.error("occurred exception: rpush {} to redis", value, e);
            }
        }
    }

    public static class Salad {
        private final Type type;
        private final InstanceContext context;
        private final Node node;
        private final SubjectProvider.Metadata metadata;

        public Salad(Type type, InstanceContext context) {
            this(type, context, null, null);
        }

        public Salad(Type type, InstanceContext context, Node node) {
            this(type, context, node, null);
        }

        public Salad(Type type, InstanceContext context, SubjectProvider.Metadata metadata) {
            this(type, context, null, metadata);
        }

        public Salad(Type type, InstanceContext context, Node node, SubjectProvider.Metadata metadata) {
            this.type = type;
            this.context = context;
            this.node = node;
            this.metadata = metadata;
        }
    }

    public static enum Type {
        INIT,
        UPDATE_NODE,
        UPDATE_SUBJECT;

    }
}

