/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.context;

import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.InstanceVariable;
import com.wacai.trike.mirana.task.TaskStatus;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.util.CollectionUtils;

public class TaskInstance implements Serializable {
	
	private Long id;
	private String uuid;
	private Long nodeId;
	private TaskStatus status;
	private String remark;
	private LocalDateTime startTime;
	private LocalDateTime endTime;
	private String operator;
	private boolean dirty;
	private List<InstanceVariable> variables = new ArrayList<InstanceVariable>();

	public void addVariable(Map<String, String> param, VariableType type) {
		if (CollectionUtils.isEmpty(param) || Objects.isNull( type)) {
			return;
		}
		param.forEach((k, v) -> {
			InstanceVariable variable = new InstanceVariable().setField((String) k).setValue((String) v).setType(type)
					.setTimestamp(System.currentTimeMillis()).setDirty(true);
			this.variables.add(variable);
		});
	}

	public Long getId() {
		return this.id;
	}

	public String getUuid() {
		return this.uuid;
	}

	public Long getNodeId() {
		return this.nodeId;
	}

	public TaskStatus getStatus() {
		return this.status;
	}

	public String getRemark() {
		return this.remark;
	}

	public LocalDateTime getStartTime() {
		return this.startTime;
	}

	public LocalDateTime getEndTime() {
		return this.endTime;
	}

	public String getOperator() {
		return this.operator;
	}

	public boolean isDirty() {
		return this.dirty;
	}

	public List<InstanceVariable> getVariables() {
		return this.variables;
	}

	public TaskInstance setId(Long id) {
		this.id = id;
		return this;
	}

	public TaskInstance setUuid(String uuid) {
		this.uuid = uuid;
		return this;
	}

	public TaskInstance setNodeId(Long nodeId) {
		this.nodeId = nodeId;
		return this;
	}

	public TaskInstance setStatus(TaskStatus status) {
		this.status = status;
		return this;
	}

	public TaskInstance setRemark(String remark) {
		this.remark = remark;
		return this;
	}

	public TaskInstance setStartTime(LocalDateTime startTime) {
		this.startTime = startTime;
		return this;
	}

	public TaskInstance setEndTime(LocalDateTime endTime) {
		this.endTime = endTime;
		return this;
	}

	public TaskInstance setOperator(String operator) {
		this.operator = operator;
		return this;
	}

	public TaskInstance setDirty(boolean dirty) {
		this.dirty = dirty;
		return this;
	}

	public TaskInstance setVariables(List<InstanceVariable> variables) {
		this.variables = variables;
		return this;
	}

	public boolean equals(Object o) {
		if (o == this) {
			return true;
		}
		if (!(o instanceof TaskInstance)) {
			return false;
		}
		TaskInstance other = (TaskInstance) o;
		if (!other.canEqual(this)) {
			return false;
		}
		Long this$id = this.getId();
		Long other$id = other.getId();
		if (this$id == null ? other$id != null : !( this$id).equals(other$id)) {
			return false;
		}
		String this$uuid = this.getUuid();
		String other$uuid = other.getUuid();
		if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
			return false;
		}
		Long this$nodeId = this.getNodeId();
		Long other$nodeId = other.getNodeId();
		if (this$nodeId == null ? other$nodeId != null : !( this$nodeId).equals(other$nodeId)) {
			return false;
		}
		TaskStatus this$status = this.getStatus();
		TaskStatus other$status = other.getStatus();
		if (this$status == null ? other$status != null
				: !( ( this$status)).equals( other$status)) {
			return false;
		}
		String this$remark = this.getRemark();
		String other$remark = other.getRemark();
		if (this$remark == null ? other$remark != null : !this$remark.equals(other$remark)) {
			return false;
		}
		LocalDateTime this$startTime = this.getStartTime();
		LocalDateTime other$startTime = other.getStartTime();
		if (this$startTime == null ? other$startTime != null : !( this$startTime).equals(other$startTime)) {
			return false;
		}
		LocalDateTime this$endTime = this.getEndTime();
		LocalDateTime other$endTime = other.getEndTime();
		if (this$endTime == null ? other$endTime != null : !( this$endTime).equals(other$endTime)) {
			return false;
		}
		String this$operator = this.getOperator();
		String other$operator = other.getOperator();
		if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
			return false;
		}
		if (this.isDirty() != other.isDirty()) {
			return false;
		}
		List<InstanceVariable> this$variables = this.getVariables();
		List<InstanceVariable> other$variables = other.getVariables();
		return !(this$variables == null ? other$variables != null : !( this$variables).equals(other$variables));
	}

	protected boolean canEqual(Object other) {
		return other instanceof TaskInstance;
	}

	public int hashCode() {
		int PRIME = 59;
		int result = 1;
		Long $id = this.getId();
		result = result * 59 + ($id == null ? 43 : ( $id).hashCode());
		String $uuid = this.getUuid();
		result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
		Long $nodeId = this.getNodeId();
		result = result * 59 + ($nodeId == null ? 43 : ( $nodeId).hashCode());
		TaskStatus $status = this.getStatus();
		result = result * 59 + ($status == null ? 43 : ( ( $status)).hashCode());
		String $remark = this.getRemark();
		result = result * 59 + ($remark == null ? 43 : $remark.hashCode());
		LocalDateTime $startTime = this.getStartTime();
		result = result * 59 + ($startTime == null ? 43 : ( $startTime).hashCode());
		LocalDateTime $endTime = this.getEndTime();
		result = result * 59 + ($endTime == null ? 43 : ( $endTime).hashCode());
		String $operator = this.getOperator();
		result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
		result = result * 59 + (this.isDirty() ? 79 : 97);
		List<InstanceVariable> $variables = this.getVariables();
		result = result * 59 + ($variables == null ? 43 : ( $variables).hashCode());
		return result;
	}

	public String toString() {
		return "TaskInstance(id=" + this.getId() + ", uuid=" + this.getUuid() + ", nodeId=" + this.getNodeId()
				+ ", status=" +  ( this.getStatus()) + ", remark=" + this.getRemark() + ", startTime="
				+ this.getStartTime() + ", endTime=" + this.getEndTime() + ", operator=" + this.getOperator()
				+ ", dirty=" + this.isDirty() + ", variables=" + this.getVariables() + ")";
	}
}
