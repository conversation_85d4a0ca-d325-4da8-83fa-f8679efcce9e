/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.boot.context.properties.bind.Bindable
 *  org.springframework.boot.context.properties.bind.Binder
 *  org.springframework.core.env.Environment
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.notify;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.ConcurrentHashMap;
import javax.annotation.PostConstruct;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.bind.Bindable;
import org.springframework.boot.context.properties.bind.Binder;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Component;

@Component
public class NotifyProperties
implements Serializable {
    @Autowired
    private Environment environment;
    private Map<String, Map<String, String>> url = new ConcurrentHashMap<String, Map<String, String>>();

    public String getNotifyUrl(String bu, String app) {
        return Optional.ofNullable(this.url.get(bu)).map(m -> (String)m.get(app)).orElse(null);
    }

    @PostConstruct
    public void init() {
        Binder binder = Binder.get((Environment)this.environment);
        Map urls = (Map)binder.bind("notify.url", Bindable.mapOf(String.class, HashMap.class)).get();
        urls.forEach((k, v) -> {
            ConcurrentHashMap appUrl = new ConcurrentHashMap(v.size());
            v.forEach((a, u) -> appUrl.put(Objects.toString(a, null), Objects.toString(u, null)));
            this.url.put((String)k, appUrl);
        });
    }
}

