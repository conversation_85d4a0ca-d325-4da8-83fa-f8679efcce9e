/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.event.NodeProcessedEvent;

public class NodeDelayProcessingEvent extends NodeProcessedEvent {
    @Override
    public String toString() {
        return "NodeDelayProcessingEvent()";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof NodeDelayProcessingEvent)) {
            return false;
        }
        NodeDelayProcessingEvent other = (NodeDelayProcessingEvent)o;
        if (!other.canEqual(this)) {
            return false;
        }
        return super.equals(o);
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof NodeDelayProcessingEvent;
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        return result;
    }
}

