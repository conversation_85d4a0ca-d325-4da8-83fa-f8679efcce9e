/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.delay;

public class ScheduleContent {
    private String unit;
    private String value;
    private String spec;
    private Integer in;
    private Integer out;

    public String getUnit() {
        return this.unit;
    }

    public String getValue() {
        return this.value;
    }

    public String getSpec() {
        return this.spec;
    }

    public Integer getIn() {
        return this.in;
    }

    public Integer getOut() {
        return this.out;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public void setSpec(String spec) {
        this.spec = spec;
    }

    public void setIn(Integer in) {
        this.in = in;
    }

    public void setOut(Integer out) {
        this.out = out;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ScheduleContent)) {
            return false;
        }
        ScheduleContent other = (ScheduleContent)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$unit = this.getUnit();
        String other$unit = other.getUnit();
        if (this$unit == null ? other$unit != null : !this$unit.equals(other$unit)) {
            return false;
        }
        String this$value = this.getValue();
        String other$value = other.getValue();
        if (this$value == null ? other$value != null : !this$value.equals(other$value)) {
            return false;
        }
        String this$spec = this.getSpec();
        String other$spec = other.getSpec();
        if (this$spec == null ? other$spec != null : !this$spec.equals(other$spec)) {
            return false;
        }
        Integer this$in = this.getIn();
        Integer other$in = other.getIn();
        if (this$in == null ? other$in != null : !(this$in).equals(other$in)) {
            return false;
        }
        Integer this$out = this.getOut();
        Integer other$out = other.getOut();
        return !(this$out == null ? other$out != null : !(this$out).equals(other$out));
    }

    protected boolean canEqual(Object other) {
        return other instanceof ScheduleContent;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $unit = this.getUnit();
        result = result * 59 + ($unit == null ? 43 : $unit.hashCode());
        String $value = this.getValue();
        result = result * 59 + ($value == null ? 43 : $value.hashCode());
        String $spec = this.getSpec();
        result = result * 59 + ($spec == null ? 43 : $spec.hashCode());
        Integer $in = this.getIn();
        result = result * 59 + ($in == null ? 43 : ($in).hashCode());
        Integer $out = this.getOut();
        result = result * 59 + ($out == null ? 43 : ($out).hashCode());
        return result;
    }

    public String toString() {
        return "ScheduleContent(unit=" + this.getUnit() + ", value=" + this.getValue() + ", spec=" + this.getSpec() + ", in=" + this.getIn() + ", out=" + this.getOut() + ")";
    }
}

