/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.VariableCategory
 *  org.springframework.data.domain.Page
 *  org.springframework.data.domain.Pageable
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.manage.repository;

import com.wacai.trike.mirana.api.constant.VariableCategory;
import com.wacai.trike.mirana.domain.manage.po.VariableConfigPO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;

public interface VariableConfigRepository
extends JpaRepository<VariableConfigPO, Long> {
    public List<VariableConfigPO> findByAppId(Long var1);

    public Optional<VariableConfigPO> findByAppIdAndCode(Long var1, String var2);

    public Page<VariableConfigPO> findByAppId(Long var1, Pageable var2);

    public Page<VariableConfigPO> findByAppIdAndCategory(Long var1, VariableCategory var2, Pageable var3);

    public List<VariableConfigPO> findByFlowId(Long var1);

    public boolean existsByAppIdAndCode(Long var1, String var2);

    public boolean existsByFlowIdAndCode(Long var1, String var2);
}

