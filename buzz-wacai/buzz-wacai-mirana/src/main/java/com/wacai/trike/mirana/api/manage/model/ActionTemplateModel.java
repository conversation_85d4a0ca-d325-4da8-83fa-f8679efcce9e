/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.io.Serializable;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class ActionTemplateModel
implements Serializable {
    private String action;
    private String actionType;
    private String targetUrl;
    private long genericTemplateId;
    private long genericActionId;

    public boolean updateFrom(ActionTemplateModel other) {
        if (this == other) {
            return false;
        }
        if (other == null) {
            return true;
        }
        return this.genericTemplateId != other.genericTemplateId || this.genericActionId != other.genericActionId;
    }

    public String getAction() {
        return this.action;
    }

    public String getActionType() {
        return this.actionType;
    }

    public String getTargetUrl() {
        return this.targetUrl;
    }

    public long getGenericTemplateId() {
        return this.genericTemplateId;
    }

    public long getGenericActionId() {
        return this.genericActionId;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public void setActionType(String actionType) {
        this.actionType = actionType;
    }

    public void setTargetUrl(String targetUrl) {
        this.targetUrl = targetUrl;
    }

    public void setGenericTemplateId(long genericTemplateId) {
        this.genericTemplateId = genericTemplateId;
    }

    public void setGenericActionId(long genericActionId) {
        this.genericActionId = genericActionId;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ActionTemplateModel)) {
            return false;
        }
        ActionTemplateModel other = (ActionTemplateModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$action = this.getAction();
        String other$action = other.getAction();
        if (this$action == null ? other$action != null : !this$action.equals(other$action)) {
            return false;
        }
        String this$actionType = this.getActionType();
        String other$actionType = other.getActionType();
        if (this$actionType == null ? other$actionType != null : !this$actionType.equals(other$actionType)) {
            return false;
        }
        String this$targetUrl = this.getTargetUrl();
        String other$targetUrl = other.getTargetUrl();
        if (this$targetUrl == null ? other$targetUrl != null : !this$targetUrl.equals(other$targetUrl)) {
            return false;
        }
        if (this.getGenericTemplateId() != other.getGenericTemplateId()) {
            return false;
        }
        return this.getGenericActionId() == other.getGenericActionId();
    }

    protected boolean canEqual(Object other) {
        return other instanceof ActionTemplateModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $action = this.getAction();
        result = result * 59 + ($action == null ? 43 : $action.hashCode());
        String $actionType = this.getActionType();
        result = result * 59 + ($actionType == null ? 43 : $actionType.hashCode());
        String $targetUrl = this.getTargetUrl();
        result = result * 59 + ($targetUrl == null ? 43 : $targetUrl.hashCode());
        long $genericTemplateId = this.getGenericTemplateId();
        result = result * 59 + (int)($genericTemplateId >>> 32 ^ $genericTemplateId);
        long $genericActionId = this.getGenericActionId();
        result = result * 59 + (int)($genericActionId >>> 32 ^ $genericActionId);
        return result;
    }

    public String toString() {
        return "ActionTemplateModel(action=" + this.getAction() + ", actionType=" + this.getActionType() + ", targetUrl=" + this.getTargetUrl() + ", genericTemplateId=" + this.getGenericTemplateId() + ", genericActionId=" + this.getGenericActionId() + ")";
    }
}

