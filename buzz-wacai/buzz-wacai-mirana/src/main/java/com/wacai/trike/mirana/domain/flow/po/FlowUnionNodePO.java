/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.flow.po;

import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.Table;

@Entity
@Table(name="flow_union_node")
public class FlowUnionNodePO
extends BasePO {
    private Long flowId;
    private Long nodeId;

    public Long getFlowId() {
        return this.flowId;
    }

    public Long getNodeId() {
        return this.nodeId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    @Override
    public String toString() {
        return "FlowUnionNodePO(flowId=" + this.getFlowId() + ", nodeId=" + this.getNodeId() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowUnionNodePO)) {
            return false;
        }
        FlowUnionNodePO other = (FlowUnionNodePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        Long this$nodeId = this.getNodeId();
        Long other$nodeId = other.getNodeId();
        return !(this$nodeId == null ? other$nodeId != null : !(this$nodeId).equals(other$nodeId));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowUnionNodePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        Long $nodeId = this.getNodeId();
        result = result * 59 + ($nodeId == null ? 43 : ($nodeId).hashCode());
        return result;
    }
}

