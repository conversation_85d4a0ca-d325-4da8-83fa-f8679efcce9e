/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.base.Strings
 *  com.google.common.cache.Cache
 *  com.google.common.cache.CacheBuilder
 *  com.google.common.collect.Lists
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.loan.trike.common.model.Page
 *  javax.validation.constraints.NotNull
 *  org.apache.commons.collections.CollectionUtils
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.boot.context.event.ApplicationStartedEvent
 *  org.springframework.context.ApplicationListener
 *  org.springframework.stereotype.Service
 */
package com.wacai.trike.mirana.domain.action;

import com.google.common.base.Strings;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.collect.Lists;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.ConvertUtils;
import com.wacai.trike.mirana.domain.action.GenericActionService;
import com.wacai.trike.mirana.domain.action.dao.po.GenericActionPO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericActionTemplatePO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericActionVariablePO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericTemplateVariableValuePO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericVariablePO;
import com.wacai.trike.mirana.domain.action.dao.po.QGenericActionPO;
import com.wacai.trike.mirana.domain.action.dao.po.QGenericActionVariablePO;
import com.wacai.trike.mirana.domain.action.dao.po.QGenericVariablePO;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericActionRepository;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericActionTemplateRepository;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericActionVariableRepository;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericTemplateVariableValueRepository;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericVariableRepository;
import com.wacai.trike.mirana.domain.action.dto.BaseDto;
import com.wacai.trike.mirana.domain.action.dto.GenericActionDto;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.domain.action.dto.GenericVariableDto;
import com.wacai.trike.mirana.domain.action.dto.VariableValueDto;
import com.wacai.trike.mirana.domain.action.enums.ActionEnums;
import com.wacai.trike.mirana.domain.bu.repository.ApplicationRepository;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.web.model.GenericActionRequest;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import javax.validation.constraints.NotNull;
import org.apache.commons.collections.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.event.ApplicationStartedEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Service;

@Service
public class GenericActionServiceImpl
		implements GenericActionService,
		ApplicationListener<ApplicationStartedEvent> {
	private static final Logger log = LoggerFactory.getLogger(GenericActionServiceImpl.class);
	@Autowired
	private JPAQueryFactory jpaQueryFactory;
	@Autowired
	private GenericActionRepository actionRepository;
	@Autowired
	private GenericActionTemplateRepository templateRepository;
	@Autowired
	private GenericVariableRepository variableRepository;
	@Autowired
	private GenericActionVariableRepository actionVariableRepository;
	@Autowired
	private GenericTemplateVariableValueRepository valueRepository;
	@Autowired
	private ApplicationRepository applicationRepository;
	@Autowired
	private ConvertUtils convertUtils;
	private final Cache<Long, GenericTemplateDto> idTemplateMap = CacheBuilder.newBuilder()
			.expireAfterWrite(15L, TimeUnit.MINUTES).initialCapacity(50).recordStats().maximumSize(100L).build();
	private final Cache<Long, GenericVariableDto> idVariableMap = CacheBuilder.newBuilder()
			.expireAfterWrite(15L, TimeUnit.MINUTES).initialCapacity(50).recordStats().maximumSize(100L).build();
	private final Cache<Long, GenericActionDto> idActionMap = CacheBuilder.newBuilder()
			.expireAfterWrite(15L, TimeUnit.MINUTES).initialCapacity(50).recordStats().maximumSize(100L).build();
	private boolean ACTIVE = true;

	@Override
	public void refreshAction() {
		this.initTemplate();
	}

	@Override
	public GenericTemplateDto getTemplateByIdFromMemo(Long templateId) {
		return this.getGenericTemplate(templateId);
	}

	public void onApplicationEvent(ApplicationStartedEvent event) {
		this.initTemplate();
	}

	private void initTemplate() {
		log.info(" [Generic Action] Start loading template...");
		this.initVariable();
		this.initAction();
		List<GenericActionTemplatePO> templatePOS = this.templateRepository.findByActive(this.ACTIVE);
		if (CollectionUtils.isEmpty(templatePOS)) {
			log.error(" [Generic Action] No Template load.");
			return;
		}
		templatePOS.forEach(po -> {
			GenericTemplateDto dto = this.fillTemplateValueInfo((GenericActionTemplatePO) po);
			this.idTemplateMap.put(dto.getId(), dto);
			log.info("Caching generic template {}", dto.getId());
		});
		log.info(" [Generic Action] Load template successfully!");
	}

	private GenericTemplateDto getGenericTemplate(Long id) {
		GenericTemplateDto exist = (GenericTemplateDto) this.idTemplateMap.getIfPresent(id);
		if (exist != null) {
			return exist;
		}
		GenericActionTemplatePO po = this.templateRepository.findById(id).orElse(null);
		GenericTemplateDto dto = this.fillTemplateValueInfo(po);
		if (po == null || dto == null) {
			log.error("Not found {}", id);
			return null;
		}
		this.idTemplateMap.put(id, dto);
		return dto;
	}

	private void initAction() {
		log.info(" [Generic Action] Start loading action...");
		List<GenericActionPO> actionPOS = this.actionRepository.findByActive(this.ACTIVE);
		if (CollectionUtils.isEmpty(actionPOS)) {
			log.error(" [Generic Action] No Actions load.");
			return;
		}
		actionPOS.forEach(actionPO -> {
			GenericActionDto action = ObjectUtils.convertNotNull(actionPO, GenericActionDto.class);
			this.fillGenericAction(action);
			this.idActionMap.put(action.getId(), action);
			log.info("Caching generic action {}", action.getId());
		});
		log.info(" [Generic Action] Load action successfully!");
	}

	private void fillGenericAction(GenericActionDto action) {
		List<GenericActionVariablePO> actionVariablePOS = this.actionVariableRepository
				.findByActionIdAndActive(action.getId(), this.ACTIVE);
		if (CollectionUtils.isNotEmpty(actionVariablePOS)) {
			ArrayList<GenericVariableDto> variableList = new ArrayList<GenericVariableDto>();
			for (GenericActionVariablePO po : actionVariablePOS) {
				GenericVariableDto genericVariable = this.getGenericVariable(po.getVariableId());
				if (!Objects.nonNull(genericVariable))
					continue;
				variableList.add(genericVariable);
			}
			action.setVariables(variableList);
		}
	}

	private GenericActionDto getGenericAction(Long id) {
		GenericActionDto exist = (GenericActionDto) this.idActionMap.getIfPresent(id);
		if (exist != null) {
			return exist;
		}
		GenericActionPO po = this.actionRepository.findById(id).orElse(null);
		GenericActionDto dto = ObjectUtils.convertNotNull(po, GenericActionDto.class);
		if (po == null || dto == null) {
			log.error("Not found {}", id);
			return null;
		}
		this.fillGenericAction(dto);
		this.idActionMap.put(id, dto);
		return dto;
	}

	private void initVariable() {
		log.info(" [Generic Action] Start loading variable...");
		List<GenericVariablePO> variablePOS = this.variableRepository.findByActive(this.ACTIVE);
		if (CollectionUtils.isEmpty(variablePOS)) {
			log.error(" [Generic Action] No Variables load.");
			return;
		}
		variablePOS.forEach(po -> {
			GenericVariableDto dto = ObjectUtils.convert(po, GenericVariableDto.class);
			if (dto != null && dto.getId() != null) {
				this.idVariableMap.put(dto.getId(), dto);
				log.info("Caching generic variable {}", dto.getId());
			}
		});
		log.info(" [Generic Action] Load variable successfully!");
	}

	private GenericVariableDto getGenericVariable(Long id) {
		GenericVariableDto exist = (GenericVariableDto) this.idVariableMap.getIfPresent(id);
		if (exist != null) {
			return exist;
		}
		GenericVariablePO po = this.variableRepository.findById(id).orElse(null);
		GenericVariableDto dto = ObjectUtils.convert(po, GenericVariableDto.class);
		if (po == null || dto == null) {
			log.error("Not found {}", id);
			return null;
		}
		this.idVariableMap.put(id, dto);
		return dto;
	}

	private GenericTemplateDto fillTemplateValueInfo(GenericActionTemplatePO templatePO) {
		if (templatePO == null) {
			return null;
		}
		GenericTemplateDto template = new GenericTemplateDto();
		template.setId(templatePO.getId());
		template.setName(templatePO.getName());
		GenericActionDto action = this.getGenericAction(templatePO.getActionId());
		template.setGenericAction(action);
		if (action != null && CollectionUtils.isNotEmpty(action.getVariables())) {
			ArrayList<VariableValueDto> value = new ArrayList<VariableValueDto>();
			for (GenericVariableDto variable : action.getVariables()) {
				VariableValueDto variableValueDto = new VariableValueDto();
				variableValueDto.setVariable(variable);
				GenericTemplateVariableValuePO valuePO = this.valueRepository
						.findByTemplateIdAndVariableIdAndActive(templatePO.getId(), variable.getId(), this.ACTIVE);
				if (valuePO != null) {
					variableValueDto.setValue(valuePO.getVariableValue());
				}
				value.add(variableValueDto);
			}
			template.setVariableValueDto(value);
		}
		return template;
	}

	@Override
    public Page<GenericActionDto> page(GenericActionRequest request) {
        Page page = new Page();
        BooleanExpression expression = this.bindCondition(request);
        page.setPage((long)request.getPageIndex());
        page.setTotal(((JPAQuery)((JPAQuery)this.jpaQueryFactory.select((Expression)QGenericActionPO.genericActionPO.id.count()).from((EntityPath)QGenericActionPO.genericActionPO)).where((Predicate)expression)).fetchCount());
        List<GenericActionDto> result = this.jpaQueryFactory.selectFrom((EntityPath)QGenericActionPO.genericActionPO).where((Predicate)expression).orderBy(QGenericActionPO.genericActionPO.createdTime.desc())).offset((long)(request.getPageSize() * (request.getPageIndex() - 1)))).limit((long)request.getPageSize())).fetch().stream().map(obj -> ObjectUtils.convert(obj, GenericActionDto.class)).filter(Objects::nonNull).collect(Collectors.toList());
        this.fillAppName(result);
        result.forEach(action -> action.setVariables(this.fetchVariables(action.getId())));
        page.setData(result);
        return page;
    }

	@Override
	public GenericActionDto query(Long id) {
		GenericActionPO actionPO = this.actionRepository.findById(id).orElse(null);
		if (actionPO == null) {
			return null;
		}
		GenericActionDto actionDto = ObjectUtils.convertNotNull(actionPO, GenericActionDto.class);
		actionDto.setVariables(this.fetchVariables(id));
		actionDto.setVariableIds(actionDto.getVariables().stream().map(BaseDto::getId).collect(Collectors.toList()));
		this.applicationRepository.findById(actionDto.getAppId()).ifPresent(app -> actionDto.setAppName(app.getName()));
		return actionDto;
	}

	@Override
    public List<GenericActionDto> batch(GenericActionRequest request) {
        BooleanExpression expression = this.bindCondition(request);
        List<GenericActionDto> actions = ((JPAQuery)this.jpaQueryFactory.selectFrom((EntityPath)QGenericActionPO.genericActionPO).where((Predicate)expression)).orderBy(QGenericActionPO.genericActionPO.createdTime.desc())).fetch().stream().map(obj -> ObjectUtils.convert(obj, GenericActionDto.class)).filter(Objects::nonNull).collect(Collectors.toList());
        this.fillAppName(actions);
        actions.forEach(action -> action.setVariables(this.fetchVariables(action.getId())));
        return actions;
    }

	@Override
	public GenericActionDto save(GenericActionDto dto) {
		if (dto.getId() == null) {
			return this.create(dto);
		}
		GenericActionPO actionPO = this.actionRepository.findById(dto.getId()).orElse(null);
		if (actionPO == null) {
			return this.create(dto);
		}
		return this.update(dto, actionPO);
	}

	@Override
	public int delete(Long id) {
		GenericActionPO exist = this.actionRepository.findById(id).orElse(null);
		if (exist == null) {
			return 0;
		}
		if (!exist.getActive().booleanValue()) {
			return 0;
		}
		exist.setActive(false);
		this.actionRepository.save(exist);
		List actionVariables = ((JPAQuery) this.jpaQueryFactory
				.selectFrom((EntityPath) QGenericActionVariablePO.genericActionVariablePO)
				.where((Predicate) QGenericActionVariablePO.genericActionVariablePO.actionId.eq(id))).fetch();
		if (CollectionUtils.isNotEmpty((Collection) actionVariables)) {
			actionVariables.forEach(av -> av.setActive(Boolean.FALSE));
		}
		this.actionVariableRepository.saveAll(actionVariables);
		return 1;
	}

	public GenericActionDto create(GenericActionDto dto) {
		GenericActionPO actionPO = ObjectUtils.convertNotNull(dto, GenericActionPO.class);
		actionPO.setActive(true);
		GenericActionPO saved = (GenericActionPO) this.actionRepository.save(actionPO);
		ArrayList<GenericVariableDto> variables = new ArrayList<GenericVariableDto>();
		if (!CollectionUtils.isEmpty(dto.getVariableIds())) {
			dto.getVariableIds().forEach(variableId -> {
				GenericActionVariablePO po = (GenericActionVariablePO) this.actionVariableRepository
						.save(this.build(saved.getId(), (Long) variableId));
				variables.add(ObjectUtils.convert(po, GenericVariableDto.class));
			});
		}
		GenericActionDto actionDto = ObjectUtils.convertNotNull(saved, GenericActionDto.class);
		actionDto.setVariables(variables);
		return actionDto;
	}

	private GenericActionDto update(GenericActionDto source, GenericActionPO target) {
		this.updating(source, target);
		GenericActionPO actionPO = (GenericActionPO) this.actionRepository.save(target);
		ArrayList<Long> waitingRemove = new ArrayList<Long>();
		ArrayList<Long> waitingAdd = new ArrayList<Long>();
		ArrayList waitingActive = new ArrayList();
		List actionVariables = ((JPAQuery) this.jpaQueryFactory
				.selectFrom((EntityPath) QGenericActionVariablePO.genericActionVariablePO)
				.where((Predicate) QGenericActionVariablePO.genericActionVariablePO.actionId.eq(target.getId())))
						.fetch();
		List<Long> exists = actionVariables.stream().map(GenericActionVariablePO::getVariableId)
				.collect(Collectors.toList());
		List<Long> actives = source.getVariableIds();
		if (CollectionUtils.isNotEmpty(exists)) {
			if (CollectionUtils.isEmpty(actives)) {
				waitingRemove.addAll(exists);
			} else {
				exists.forEach(exist -> {
					if (actives.stream().noneMatch(varId -> varId.equals(exist))) {
						waitingRemove.add((Long) exist);
					}
					if (actives.contains(exist) && actionVariables.stream()
							.anyMatch(av -> av.getActive() == false && av.getVariableId().equals(exist))) {
						waitingActive.add(exist);
					}
				});
			}
		}
		if (CollectionUtils.isNotEmpty(actives)) {
			if (CollectionUtils.isEmpty(exists)) {
				waitingAdd.addAll(actives);
			} else {
				actives.forEach(active -> {
					if (exists.stream().noneMatch(varId -> varId.equals(active))) {
						waitingAdd.add((Long) active);
					}
				});
			}
		}
		if (CollectionUtils.isNotEmpty(waitingRemove)) {
			waitingRemove.forEach(remove -> {
				GenericActionVariablePO po = actionVariables.stream().filter(av -> av.getVariableId().equals(remove))
						.findFirst().orElse(null);
				if (po != null) {
					po.setActive(Boolean.FALSE);
					this.actionVariableRepository.save(po);
				}
			});
		}
		if (CollectionUtils.isNotEmpty(waitingAdd)) {
			waitingAdd.forEach(add -> {
				GenericActionVariablePO cfr_ignored_0 = (GenericActionVariablePO) this.actionVariableRepository
						.save(this.build(target.getId(), (Long) add));
			});
		}
		if (CollectionUtils.isNotEmpty(waitingActive)) {
			waitingActive.forEach(active -> {
				GenericActionVariablePO po = actionVariables.stream().filter(av -> av.getVariableId().equals(active))
						.findFirst().orElse(null);
				if (po != null) {
					po.setActive(Boolean.TRUE);
					this.actionVariableRepository.save(po);
				}
			});
		}
		return ObjectUtils.convert(actionPO, GenericActionDto.class);
	}

	private GenericActionVariablePO build(Long actionId, Long variableId) {
		GenericActionVariablePO po = new GenericActionVariablePO();
		po.setActionId(actionId);
		po.setVariableId(variableId);
		po.setActive(true);
		po.setCreatedTime(LocalDateTime.now());
		po.setUpdatedTime(LocalDateTime.now());
		po.preUpdate();
		return po;
	}

	private void fillAppName(List<GenericActionDto> actions) {
		actions.forEach(action -> this.applicationRepository.findById(action.getAppId())
				.ifPresent(app -> action.setAppName(app.getName())));
	}

	private List<GenericVariableDto> fetchVariables(Long actionId) {
		List variablePOS = ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) ((JPAQuery) this.jpaQueryFactory
				.selectFrom((EntityPath) QGenericVariablePO.genericVariablePO)
				.leftJoin((EntityPath) QGenericActionVariablePO.genericActionVariablePO))
						.on((Predicate) QGenericActionVariablePO.genericActionVariablePO.variableId
								.eq(QGenericVariablePO.genericVariablePO.id))).where(
										(Predicate) QGenericActionVariablePO.genericActionVariablePO.active.isTrue()))
												.where((Predicate) QGenericVariablePO.genericVariablePO.active
														.isTrue())).where(
																(Predicate) QGenericActionVariablePO.genericActionVariablePO.actionId
																		.eq(actionId))).fetch();
		if (CollectionUtils.isNotEmpty((Collection) variablePOS)) {
			variablePOS.forEach(po -> {
				po.setActive(null);
				po.setUpdatedBy(null);
				po.setUpdatedTime(null);
				po.setCreatedBy(null);
				po.setCreatedTime(null);
			});
			return variablePOS.stream().filter(Objects::nonNull)
					.map(po -> this.convertUtils.toDto((GenericVariablePO) po)).collect(Collectors.toList());
		}
		return Lists.newArrayList();
	}

	private BooleanExpression bindCondition(GenericActionRequest request) {
		BooleanExpression expression = QGenericActionPO.genericActionPO.active.eq(Boolean.valueOf(true));
		if (!Strings.isNullOrEmpty((String) request.getName())) {
			expression = expression
					.and((Predicate) QGenericActionPO.genericActionPO.name.like("%" + request.getName() + "%"));
		}
		if (!Strings.isNullOrEmpty((String) request.getType())) {
			expression = expression.and(
					(Predicate) QGenericActionPO.genericActionPO.type.eq(ActionEnums.Type.valueOf(request.getType())));
		}
		if (request.getAppId() > 0L) {
			expression = expression.and((Predicate) QGenericActionPO.genericActionPO.appId.eq(request.getAppId()));
		}
		return expression;
	}

	private void updating(@NotNull GenericActionDto source, @NotNull GenericActionPO target) {
		if (source.getAppId() != null && !target.getAppId().equals(source.getAppId())) {
			target.setAppId(source.getAppId());
		}
		if (source.getType() != null && !target.getType().equals(source.getType())) {
			target.setType(source.getType());
		}
		if (source.getName() != null && !target.getName().equals(source.getName())) {
			target.setName(source.getName());
		}
		if (source.getCallbackUrl() != null && !target.getCallbackUrl().equals(source.getCallbackUrl())) {
			target.setCallbackUrl(source.getCallbackUrl());
		}
	}
}
