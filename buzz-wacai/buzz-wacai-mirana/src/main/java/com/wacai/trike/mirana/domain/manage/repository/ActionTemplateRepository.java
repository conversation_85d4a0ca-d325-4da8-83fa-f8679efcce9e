/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.manage.repository;

import com.wacai.trike.mirana.domain.manage.po.ActionTemplatePO;
import org.springframework.data.jpa.repository.JpaRepository;

public interface ActionTemplateRepository
extends JpaRepository<ActionTemplatePO, Long> {
    public ActionTemplatePO findFirstByActionAndActionType(String var1, String var2);
}

