/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.manage.po;

import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="node_action")
public class NodeActionPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private Long nodeId;
    private Long actionConfigId;
    @Enumerated(value=EnumType.STRING)
    private TaskType type = TaskType.USER;
    private String content;
    private Integer timeout;

    public Long getNodeId() {
        return this.nodeId;
    }

    public Long getActionConfigId() {
        return this.actionConfigId;
    }

    public TaskType getType() {
        return this.type;
    }

    public String getContent() {
        return this.content;
    }

    public Integer getTimeout() {
        return this.timeout;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public void setActionConfigId(Long actionConfigId) {
        this.actionConfigId = actionConfigId;
    }

    public void setType(TaskType type) {
        this.type = type;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public void setTimeout(Integer timeout) {
        this.timeout = timeout;
    }

    @Override
    public String toString() {
        return "NodeActionPO(nodeId=" + this.getNodeId() + ", actionConfigId=" + this.getActionConfigId() + ", type=" + this.getType() + ", content=" + this.getContent() + ", timeout=" + this.getTimeout() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof NodeActionPO)) {
            return false;
        }
        NodeActionPO other = (NodeActionPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$nodeId = this.getNodeId();
        Long other$nodeId = other.getNodeId();
        if (this$nodeId == null ? other$nodeId != null : !(this$nodeId).equals(other$nodeId)) {
            return false;
        }
        Long this$actionConfigId = this.getActionConfigId();
        Long other$actionConfigId = other.getActionConfigId();
        if (this$actionConfigId == null ? other$actionConfigId != null : !(this$actionConfigId).equals(other$actionConfigId)) {
            return false;
        }
        TaskType this$type = this.getType();
        TaskType other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$content = this.getContent();
        String other$content = other.getContent();
        if (this$content == null ? other$content != null : !this$content.equals(other$content)) {
            return false;
        }
        Integer this$timeout = this.getTimeout();
        Integer other$timeout = other.getTimeout();
        return !(this$timeout == null ? other$timeout != null : !(this$timeout).equals(other$timeout));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof NodeActionPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $nodeId = this.getNodeId();
        result = result * 59 + ($nodeId == null ? 43 : ($nodeId).hashCode());
        Long $actionConfigId = this.getActionConfigId();
        result = result * 59 + ($actionConfigId == null ? 43 : ($actionConfigId).hashCode());
        TaskType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $content = this.getContent();
        result = result * 59 + ($content == null ? 43 : $content.hashCode());
        Integer $timeout = this.getTimeout();
        result = result * 59 + ($timeout == null ? 43 : ($timeout).hashCode());
        return result;
    }
}

