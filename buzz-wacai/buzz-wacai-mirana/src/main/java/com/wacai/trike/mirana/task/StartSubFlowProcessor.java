/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Maps
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.task;

import com.google.common.collect.Maps;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.delay.DelayCalculator;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.EventAgency;
import com.wacai.trike.mirana.event.StartingFlowEvent;
import com.wacai.trike.mirana.graph.FlowGraph;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.task.TaskProcessor;
import com.wacai.trike.mirana.util.DistributeSequenceIdGenerator;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class StartSubFlowProcessor implements TaskProcessor {
	
	private static final Logger log = LoggerFactory.getLogger(StartSubFlowProcessor.class);
	@Autowired
	private EventAgency eventAgency;
	@Autowired
	private FlowGraphService graphService;
	@Autowired
	private DistributeSequenceIdGenerator idGenerator;

	@Override
	public boolean accept(TaskType taskType) {
		return taskType == TaskType.START_SUB_FLOW;
	}

	@Override
	public Map<String, String> execute(Node node, InstanceContext context) {
		HashMap param = Maps.newHashMap();
		param.put("createNodeId", node.getId().toString());
		FlowGraph graph = this.graphService.getFlowGraph(context.getFlowId());
		if (graph == null) {
			log.error("Should never occurred: not found {}",  context.getFlowId());
			return param;
		}
		FlowGraph subFlowGraph = this.graphService.getFlowGraph(node.getTaskContent());
		if (subFlowGraph == null) {
			log.error("Should never occurred: not found {}",  node.getTaskContent());
			return param;
		}
		if (context.getEffectiveVariables().containsKey("birthmarks")) {
			param.put("birthmarks", context.getEffectiveVariables().get("birthmarks"));
		}
		AbstractFlowEvent event = new StartingFlowEvent()
				.setBu(subFlowGraph.getBu())
				.setApp(subFlowGraph.getApp())
				.setFlow(node.getTaskContent())
				.setVersion(subFlowGraph.getVersion())
				.setBzKey(context.getBzKey())
				.setInstanceUuid(Long.toString(this.idGenerator.nextId())).setParentUuid(context.getUuid())
				.setOperator("system").setAsync(context.isAsync()).setTimeout(this.calcTimeout(node, context))
				.setParam((Map) param);
		this.eventAgency.agent(event, false);
		log.info("started sub-flow {} of story-line {} : {}",
				new Object[] { ((StartingFlowEvent) event).getInstanceUuid(),
						((StartingFlowEvent) event).getParentUuid(), ((StartingFlowEvent) event).toString() });
		return Collections.emptyMap();
	}

	private LocalDateTime calcTimeout(Node node, InstanceContext context) {
		if (node.delayNotConfig()) {
			return null;
		}
		DelayCalculator calculator = node.getDelayCalculator();
		return calculator.expectEnd(context.getDueDate());
	}
}
