/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.report.model;

import com.wacai.trike.mirana.api.constant.InstanceStatus;
import java.io.Serializable;

public class FlowReportModel
implements Serializable {
    private String code;
    private String name;
    private Long count;
    private InstanceStatus status;

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public Long getCount() {
        return this.count;
    }

    public InstanceStatus getStatus() {
        return this.status;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setCount(Long count) {
        this.count = count;
    }

    public void setStatus(InstanceStatus status) {
        this.status = status;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowReportModel)) {
            return false;
        }
        FlowReportModel other = (FlowReportModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        Long this$count = this.getCount();
        Long other$count = other.getCount();
        if (this$count == null ? other$count != null : !(this$count).equals(other$count)) {
            return false;
        }
        InstanceStatus this$status = this.getStatus();
        InstanceStatus other$status = other.getStatus();
        return !(this$status == null ? other$status != null : !((this$status)).equals(other$status));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowReportModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Long $count = this.getCount();
        result = result * 59 + ($count == null ? 43 : ($count).hashCode());
        InstanceStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
        return result;
    }

    public String toString() {
        return "FlowReportModel(code=" + this.getCode() + ", name=" + this.getName() + ", count=" + this.getCount() + ", status=" + (this.getStatus()) + ")";
    }
}

