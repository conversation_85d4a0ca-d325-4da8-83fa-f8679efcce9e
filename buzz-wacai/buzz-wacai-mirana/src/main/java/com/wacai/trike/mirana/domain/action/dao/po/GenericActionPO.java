/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Column
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.action.dao.po;

import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.domain.action.enums.ActionEnums;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="generic_action")
public class GenericActionPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    @Column(name="name", nullable=false)
    private String name;
    @Column(name="app_id", nullable=false)
    private Long appId;
    @Column(name="type", nullable=false)
    @Enumerated(value=EnumType.STRING)
    private ActionEnums.Type type;
    @Column(name="callback_url")
    private String callbackUrl;

    public String getName() {
        return this.name;
    }

    public Long getAppId() {
        return this.appId;
    }

    public ActionEnums.Type getType() {
        return this.type;
    }

    public String getCallbackUrl() {
        return this.callbackUrl;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setType(ActionEnums.Type type) {
        this.type = type;
    }

    public void setCallbackUrl(String callbackUrl) {
        this.callbackUrl = callbackUrl;
    }

    @Override
    public String toString() {
        return "GenericActionPO(name=" + this.getName() + ", appId=" + this.getAppId() + ", type=" + (this.getType()) + ", callbackUrl=" + this.getCallbackUrl() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericActionPO)) {
            return false;
        }
        GenericActionPO other = (GenericActionPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        ActionEnums.Type this$type = this.getType();
        ActionEnums.Type other$type = other.getType();
        if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
            return false;
        }
        String this$callbackUrl = this.getCallbackUrl();
        String other$callbackUrl = other.getCallbackUrl();
        return !(this$callbackUrl == null ? other$callbackUrl != null : !this$callbackUrl.equals(other$callbackUrl));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof GenericActionPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        ActionEnums.Type $type = this.getType();
        result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
        String $callbackUrl = this.getCallbackUrl();
        result = result * 59 + ($callbackUrl == null ? 43 : $callbackUrl.hashCode());
        return result;
    }
}

