/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Lists
 */
package com.wacai.trike.mirana.service;

import com.google.common.collect.Lists;
import java.util.List;

public class FlowNodeVariableRequest {
    private List<Long> nodeIds;
    private List<Long> subFlowIds;
    private List<Long> parentFlowVariableIds;
    private List<Long> subFlowVariableIds;

    public FlowNodeVariableRequest addNodeId(Long nodeId) {
        if (this.nodeIds == null) {
            this.nodeIds = Lists.newArrayList();
        }
        if (!this.nodeIds.contains(nodeId)) {
            this.nodeIds.add(nodeId);
        }
        return this;
    }

    public FlowNodeVariableRequest addSubFlowId(Long subFlowId) {
        if (this.subFlowIds == null) {
            this.subFlowIds = Lists.newArrayList();
        }
        if (!this.subFlowIds.contains(subFlowId)) {
            this.subFlowIds.add(subFlowId);
        }
        return this;
    }

    public FlowNodeVariableRequest addParentFlowVariableId(Long parentFlowVariableId) {
        if (this.parentFlowVariableIds == null) {
            this.parentFlowVariableIds = Lists.newArrayList();
        }
        if (!this.parentFlowVariableIds.contains(parentFlowVariableId)) {
            this.parentFlowVariableIds.add(parentFlowVariableId);
        }
        return this;
    }

    public FlowNodeVariableRequest addSubFlowVariableId(Long subFlowVariableId) {
        if (this.subFlowVariableIds == null) {
            this.subFlowVariableIds = Lists.newArrayList();
        }
        if (!this.subFlowVariableIds.contains(subFlowVariableId)) {
            this.subFlowVariableIds.add(subFlowVariableId);
        }
        return this;
    }

    public List<Long> getNodeIds() {
        return this.nodeIds;
    }

    public List<Long> getSubFlowIds() {
        return this.subFlowIds;
    }

    public List<Long> getParentFlowVariableIds() {
        return this.parentFlowVariableIds;
    }

    public List<Long> getSubFlowVariableIds() {
        return this.subFlowVariableIds;
    }

    public FlowNodeVariableRequest setNodeIds(List<Long> nodeIds) {
        this.nodeIds = nodeIds;
        return this;
    }

    public FlowNodeVariableRequest setSubFlowIds(List<Long> subFlowIds) {
        this.subFlowIds = subFlowIds;
        return this;
    }

    public FlowNodeVariableRequest setParentFlowVariableIds(List<Long> parentFlowVariableIds) {
        this.parentFlowVariableIds = parentFlowVariableIds;
        return this;
    }

    public FlowNodeVariableRequest setSubFlowVariableIds(List<Long> subFlowVariableIds) {
        this.subFlowVariableIds = subFlowVariableIds;
        return this;
    }

    public String toString() {
        return "FlowNodeVariableRequest(nodeIds=" + this.getNodeIds() + ", subFlowIds=" + this.getSubFlowIds() + ", parentFlowVariableIds=" + this.getParentFlowVariableIds() + ", subFlowVariableIds=" + this.getSubFlowVariableIds() + ")";
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowNodeVariableRequest)) {
            return false;
        }
        FlowNodeVariableRequest other = (FlowNodeVariableRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        List<Long> this$nodeIds = this.getNodeIds();
        List<Long> other$nodeIds = other.getNodeIds();
        if (this$nodeIds == null ? other$nodeIds != null : !(this$nodeIds).equals(other$nodeIds)) {
            return false;
        }
        List<Long> this$subFlowIds = this.getSubFlowIds();
        List<Long> other$subFlowIds = other.getSubFlowIds();
        if (this$subFlowIds == null ? other$subFlowIds != null : !(this$subFlowIds).equals(other$subFlowIds)) {
            return false;
        }
        List<Long> this$parentFlowVariableIds = this.getParentFlowVariableIds();
        List<Long> other$parentFlowVariableIds = other.getParentFlowVariableIds();
        if (this$parentFlowVariableIds == null ? other$parentFlowVariableIds != null : !(this$parentFlowVariableIds).equals(other$parentFlowVariableIds)) {
            return false;
        }
        List<Long> this$subFlowVariableIds = this.getSubFlowVariableIds();
        List<Long> other$subFlowVariableIds = other.getSubFlowVariableIds();
        return !(this$subFlowVariableIds == null ? other$subFlowVariableIds != null : !(this$subFlowVariableIds).equals(other$subFlowVariableIds));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowNodeVariableRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        List<Long> $nodeIds = this.getNodeIds();
        result = result * 59 + ($nodeIds == null ? 43 : ($nodeIds).hashCode());
        List<Long> $subFlowIds = this.getSubFlowIds();
        result = result * 59 + ($subFlowIds == null ? 43 : ($subFlowIds).hashCode());
        List<Long> $parentFlowVariableIds = this.getParentFlowVariableIds();
        result = result * 59 + ($parentFlowVariableIds == null ? 43 : ($parentFlowVariableIds).hashCode());
        List<Long> $subFlowVariableIds = this.getSubFlowVariableIds();
        result = result * 59 + ($subFlowVariableIds == null ? 43 : ($subFlowVariableIds).hashCode());
        return result;
    }
}

