/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.action.dao.repository;

import java.util.List;

import org.springframework.data.jpa.repository.JpaRepository;

import com.wacai.trike.mirana.domain.action.dao.po.GenericVariablePO;

public interface GenericVariableRepository extends JpaRepository<GenericVariablePO, Long> {
    public List<GenericVariablePO> findByActive(boolean var1);
}

