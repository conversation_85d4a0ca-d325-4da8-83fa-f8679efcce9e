/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.base.Strings
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.resource;

import com.google.common.base.Strings;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.resource.ApplicationModel;
import com.wacai.trike.mirana.service.AppService;
import com.wacai.trike.mirana.service.ApplicationDTO;
import com.wacai.trike.mirana.service.ApplicationRequest;
import com.wacai.trike.mirana.util.TimeFormatUtil;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/managed/applications"})
public class ApplicationsResource {
    @Autowired
    private AppService appService;

    @GetMapping
    public ApiResponse<List<ApplicationModel>> listApplication(@RequestParam(name="id", defaultValue="") List<Long> ids, @RequestParam(name="buId", defaultValue="") List<Long> buIds, @RequestParam(name="name", defaultValue="") List<String> names, @RequestParam(name="code", defaultValue="") List<String> codes, @RequestParam(name="start", defaultValue="") String start, @RequestParam(name="end", defaultValue="") String end) {
        ApplicationRequest.ApplicationRequestBuilder builder = ApplicationRequest.builder().ids(ids).buIds(buIds).names(names).codes(codes);
        builder.start(Strings.isNullOrEmpty((String)start) ? TimeFormatUtil.MIN : TimeFormatUtil.mustParseCST(start));
        builder.end(Strings.isNullOrEmpty((String)end) ? TimeFormatUtil.MAX : TimeFormatUtil.mustParseCST(end));
        return ApiResponse.success(this.appService.listApplication(builder.build()).stream().map(ApplicationDTO::toModel).collect(Collectors.toList()));
    }
}

