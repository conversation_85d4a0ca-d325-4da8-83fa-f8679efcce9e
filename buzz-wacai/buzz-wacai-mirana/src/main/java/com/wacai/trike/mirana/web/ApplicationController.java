/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  io.swagger.annotations.ApiOperation
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.web;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.domain.bu.ApplicationService;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.web.model.ApplicationModel;
import io.swagger.annotations.ApiOperation;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value = { "/managed/application" })
public class ApplicationController {
	@Autowired
	private ApplicationService applicationService;

	@GetMapping(value = { "/batch" })
	@ApiOperation(value = "App批量获取", notes = "暂时不支持条件查询")
	public ApiResponse<List<ApplicationModel>> batchQuery() {
		return ApiResponse.success(
				this.applicationService.batch().stream().map(dto -> ObjectUtils.convert(dto, ApplicationModel.class))
						.filter(Objects::nonNull).collect(Collectors.toList()));
	}
}
