/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.expression.Operator
 *  javax.persistence.Column
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.expression.po;

import com.wacai.loan.trike.expression.Operator;
import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="expression")
public class ExpressionPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private Long edgeId;
    private String fieldType;
    private String field;
    @Column(name="operator", nullable=false)
    @Enumerated(value=EnumType.STRING)
    private Operator operator;
    private String threshold;
    private String function;
    private String args;

    public Long getEdgeId() {
        return this.edgeId;
    }

    public String getFieldType() {
        return this.fieldType;
    }

    public String getField() {
        return this.field;
    }

    public Operator getOperator() {
        return this.operator;
    }

    public String getThreshold() {
        return this.threshold;
    }

    public String getFunction() {
        return this.function;
    }

    public String getArgs() {
        return this.args;
    }

    public void setEdgeId(Long edgeId) {
        this.edgeId = edgeId;
    }

    public void setFieldType(String fieldType) {
        this.fieldType = fieldType;
    }

    public void setField(String field) {
        this.field = field;
    }

    public void setOperator(Operator operator) {
        this.operator = operator;
    }

    public void setThreshold(String threshold) {
        this.threshold = threshold;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public void setArgs(String args) {
        this.args = args;
    }

    @Override
    public String toString() {
        return "ExpressionPO(edgeId=" + this.getEdgeId() + ", fieldType=" + this.getFieldType() + ", field=" + this.getField() + ", operator=" + this.getOperator() + ", threshold=" + this.getThreshold() + ", function=" + this.getFunction() + ", args=" + this.getArgs() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ExpressionPO)) {
            return false;
        }
        ExpressionPO other = (ExpressionPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$edgeId = this.getEdgeId();
        Long other$edgeId = other.getEdgeId();
        if (this$edgeId == null ? other$edgeId != null : !(this$edgeId).equals(other$edgeId)) {
            return false;
        }
        String this$fieldType = this.getFieldType();
        String other$fieldType = other.getFieldType();
        if (this$fieldType == null ? other$fieldType != null : !this$fieldType.equals(other$fieldType)) {
            return false;
        }
        String this$field = this.getField();
        String other$field = other.getField();
        if (this$field == null ? other$field != null : !this$field.equals(other$field)) {
            return false;
        }
        Operator this$operator = this.getOperator();
        Operator other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        String this$threshold = this.getThreshold();
        String other$threshold = other.getThreshold();
        if (this$threshold == null ? other$threshold != null : !this$threshold.equals(other$threshold)) {
            return false;
        }
        String this$function = this.getFunction();
        String other$function = other.getFunction();
        if (this$function == null ? other$function != null : !this$function.equals(other$function)) {
            return false;
        }
        String this$args = this.getArgs();
        String other$args = other.getArgs();
        return !(this$args == null ? other$args != null : !this$args.equals(other$args));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof ExpressionPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $edgeId = this.getEdgeId();
        result = result * 59 + ($edgeId == null ? 43 : ($edgeId).hashCode());
        String $fieldType = this.getFieldType();
        result = result * 59 + ($fieldType == null ? 43 : $fieldType.hashCode());
        String $field = this.getField();
        result = result * 59 + ($field == null ? 43 : $field.hashCode());
        Operator $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        String $threshold = this.getThreshold();
        result = result * 59 + ($threshold == null ? 43 : $threshold.hashCode());
        String $function = this.getFunction();
        result = result * 59 + ($function == null ? 43 : $function.hashCode());
        String $args = this.getArgs();
        result = result * 59 + ($args == null ? 43 : $args.hashCode());
        return result;
    }
}

