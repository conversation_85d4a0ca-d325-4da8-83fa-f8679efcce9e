/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class FlowBaseRequest
implements Serializable {
    private String bu;
    private String app;
    private String flow;
    private String operator;
    private boolean async = false;
    private int state;
    private Map<String, String> param = new HashMap<String, String>();

    public FlowBaseRequest addParam(String key, String value) {
        this.param.put(Objects.requireNonNull(key), value);
        return this;
    }

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getFlow() {
        return this.flow;
    }

    public String getOperator() {
        return this.operator;
    }

    public boolean isAsync() {
        return this.async;
    }

    public int getState() {
        return this.state;
    }

    public Map<String, String> getParam() {
        return this.param;
    }

    public FlowBaseRequest setBu(String bu) {
        this.bu = bu;
        return this;
    }

    public FlowBaseRequest setApp(String app) {
        this.app = app;
        return this;
    }

    public FlowBaseRequest setFlow(String flow) {
        this.flow = flow;
        return this;
    }

    public FlowBaseRequest setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public FlowBaseRequest setAsync(boolean async) {
        this.async = async;
        return this;
    }

    public FlowBaseRequest setState(int state) {
        this.state = state;
        return this;
    }

    public FlowBaseRequest setParam(Map<String, String> param) {
        this.param = param;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowBaseRequest)) {
            return false;
        }
        FlowBaseRequest other = (FlowBaseRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        if (this$flow == null ? other$flow != null : !this$flow.equals(other$flow)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        if (this.isAsync() != other.isAsync()) {
            return false;
        }
        if (this.getState() != other.getState()) {
            return false;
        }
        Map<String, String> this$param = this.getParam();
        Map<String, String> other$param = other.getParam();
        return !(this$param == null ? other$param != null : !(this$param).equals(other$param));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowBaseRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        result = result * 59 + (this.isAsync() ? 79 : 97);
        result = result * 59 + this.getState();
        Map<String, String> $param = this.getParam();
        result = result * 59 + ($param == null ? 43 : ($param).hashCode());
        return result;
    }

    public String toString() {
        return "FlowBaseRequest(bu=" + this.getBu() + ", app=" + this.getApp() + ", flow=" + this.getFlow() + ", operator=" + this.getOperator() + ", async=" + this.isAsync() + ", state=" + this.getState() + ", param=" + this.getParam() + ")";
    }
}

