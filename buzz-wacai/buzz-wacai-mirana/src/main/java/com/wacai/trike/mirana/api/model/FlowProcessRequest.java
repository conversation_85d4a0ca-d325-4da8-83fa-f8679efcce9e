/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.model.FlowBaseRequest;
import javax.validation.constraints.NotBlank;

public class FlowProcessRequest
extends FlowBaseRequest {
    @NotBlank
    private String instanceId;
    private String taskInstanceId;
    private Integer code;
    private boolean asyncStart = false;
    private String instanceUUID;

    public String getInstanceId() {
        return this.instanceId;
    }

    public String getTaskInstanceId() {
        return this.taskInstanceId;
    }

    public Integer getCode() {
        return this.code;
    }

    public boolean isAsyncStart() {
        return this.asyncStart;
    }

    public String getInstanceUUID() {
        return this.instanceUUID;
    }

    public FlowProcessRequest setInstanceId(String instanceId) {
        this.instanceId = instanceId;
        return this;
    }

    public FlowProcessRequest setTaskInstanceId(String taskInstanceId) {
        this.taskInstanceId = taskInstanceId;
        return this;
    }

    public FlowProcessRequest setCode(Integer code) {
        this.code = code;
        return this;
    }

    public FlowProcessRequest setAsyncStart(boolean asyncStart) {
        this.asyncStart = asyncStart;
        return this;
    }

    public FlowProcessRequest setInstanceUUID(String instanceUUID) {
        this.instanceUUID = instanceUUID;
        return this;
    }

    @Override
    public String toString() {
        return "FlowProcessRequest(instanceId=" + this.getInstanceId() + ", taskInstanceId=" + this.getTaskInstanceId() + ", code=" + this.getCode() + ", asyncStart=" + this.isAsyncStart() + ", instanceUUID=" + this.getInstanceUUID() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowProcessRequest)) {
            return false;
        }
        FlowProcessRequest other = (FlowProcessRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$instanceId = this.getInstanceId();
        String other$instanceId = other.getInstanceId();
        if (this$instanceId == null ? other$instanceId != null : !this$instanceId.equals(other$instanceId)) {
            return false;
        }
        String this$taskInstanceId = this.getTaskInstanceId();
        String other$taskInstanceId = other.getTaskInstanceId();
        if (this$taskInstanceId == null ? other$taskInstanceId != null : !this$taskInstanceId.equals(other$taskInstanceId)) {
            return false;
        }
        Integer this$code = this.getCode();
        Integer other$code = other.getCode();
        if (this$code == null ? other$code != null : !(this$code).equals(other$code)) {
            return false;
        }
        if (this.isAsyncStart() != other.isAsyncStart()) {
            return false;
        }
        String this$instanceUUID = this.getInstanceUUID();
        String other$instanceUUID = other.getInstanceUUID();
        return !(this$instanceUUID == null ? other$instanceUUID != null : !this$instanceUUID.equals(other$instanceUUID));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowProcessRequest;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $instanceId = this.getInstanceId();
        result = result * 59 + ($instanceId == null ? 43 : $instanceId.hashCode());
        String $taskInstanceId = this.getTaskInstanceId();
        result = result * 59 + ($taskInstanceId == null ? 43 : $taskInstanceId.hashCode());
        Integer $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : ($code).hashCode());
        result = result * 59 + (this.isAsyncStart() ? 79 : 97);
        String $instanceUUID = this.getInstanceUUID();
        result = result * 59 + ($instanceUUID == null ? 43 : $instanceUUID.hashCode());
        return result;
    }
}

