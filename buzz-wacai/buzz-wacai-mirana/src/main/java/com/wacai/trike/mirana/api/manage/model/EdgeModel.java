package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@JsonInclude(JsonInclude.Include.NON_NULL)
public class EdgeModel implements Serializable {
    public static final String ID_PREFIX = "edge_";
    private String id;
    private String source;
    private String target;
    private String label;
    private List<ExpressionModel>  expressions = new ArrayList<>();
}
