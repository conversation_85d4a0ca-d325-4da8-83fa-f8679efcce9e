/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.beans.BeanUtils
 */
package com.wacai.trike.mirana.util;

import java.util.Objects;
import org.springframework.beans.BeanUtils;

public final class ObjectUtils {
    public static boolean anyNull(Object ... objs) {
        if (Objects.isNull(objs)) {
            return true;
        }
        for (Object obj : objs) {
            if (!Objects.isNull(obj)) continue;
            return true;
        }
        return false;
    }

    public static <T> T convert(Object origin, Class<T> clazz) {
        if (ObjectUtils.anyNull(origin, clazz)) {
            return null;
        }
        try {
            T target = clazz.newInstance();
            BeanUtils.copyProperties(origin, target);
            return target;
        }
        catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static <T> T convertNotNull(Object origin, Class<T> clazz) {
        if (ObjectUtils.anyNull(origin, clazz)) {
            throw new IllegalArgumentException("Input parameter must be not null");
        }
        try {
            T target = clazz.newInstance();
            BeanUtils.copyProperties(origin, target);
            return target;
        }
        catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}

