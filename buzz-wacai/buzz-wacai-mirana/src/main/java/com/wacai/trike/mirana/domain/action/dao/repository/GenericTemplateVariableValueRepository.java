/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.action.dao.repository;

import com.wacai.trike.mirana.domain.action.dao.po.GenericTemplateVariableValuePO;
import org.springframework.data.jpa.repository.JpaRepository;

public interface GenericTemplateVariableValueRepository
extends JpaRepository<GenericTemplateVariableValuePO, Long> {
    public GenericTemplateVariableValuePO findByTemplateIdAndVariableIdAndActive(Long var1, Long var2, boolean var3);
}

