/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.Page
 */
package com.wacai.trike.mirana.domain.action;

import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.dto.GenericVariableDto;
import com.wacai.trike.mirana.web.model.GenericVariableRequest;
import java.util.List;

public interface GenericVariableService {
    public GenericVariableDto save(GenericVariableDto var1);

    public GenericVariableDto query(Long var1);

    public Page<GenericVariableDto> page(GenericVariableRequest var1);

    public List<GenericVariableDto> batch(GenericVariableRequest var1);

    public int delete(Long var1);

    public List<GenericVariableDto> fetchCandidates(List<Long> var1);
}

