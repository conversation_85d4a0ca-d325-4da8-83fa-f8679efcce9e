/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.util;

import java.time.DateTimeException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Optional;

public class TimeFormatUtil {
    private static final ZoneId ZONE_ID_CST = ZoneId.of("Asia/Shanghai");
    private static final String DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";
    private static final String DEFAULT_MIN = "0001-01-01 00:00:00";
    private static final String DEFAULT_MAX = "9999-12-31 23:59:59";
    private static DateTimeFormatter DEFAULT_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final LocalDateTime MIN = TimeFormatUtil.mustParseCST("0001-01-01 00:00:00");
    public static final LocalDateTime MAX = TimeFormatUtil.mustParseCST("9999-12-31 23:59:59");

    public static LocalDateTime mustParseCST(String ts) {
        Optional<LocalDateTime> localDateTime = TimeFormatUtil.parseCST(ts);
        if (!localDateTime.isPresent()) {
            throw new IllegalArgumentException("Unable to parse timestamp " + ts);
        }
        return localDateTime.get();
    }

    private static Optional<LocalDateTime> parseCST(String ts) {
        return TimeFormatUtil.parseCST(ts, DEFAULT_FORMATTER);
    }

    private static Optional<LocalDateTime> parseCST(String ts, DateTimeFormatter formatter) {
        try {
            return Optional.of(LocalDateTime.parse(ts, formatter.withZone(ZONE_ID_CST)));
        }
        catch (DateTimeException e) {
            return Optional.empty();
        }
    }
}

