/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.web.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.service.FlowVariableDTO;
import com.wacai.trike.mirana.util.ObjectUtils;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class FlowVariableModel {
    private Long id;
    private Long appId;
    private String code;
    private String name;
    private String type;
    private String sourceType;
    private List<FlowVariableDTO.SupportedOperator> supportedOperators;
    private List<FlowVariableDTO.Candidate> candidates;
    private String candidatesUrl;

    public FlowVariableDTO toDto() {
        return ObjectUtils.convertNotNull(this, FlowVariableDTO.class);
    }

    public Long getId() {
        return this.id;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getType() {
        return this.type;
    }

    public String getSourceType() {
        return this.sourceType;
    }

    public List<FlowVariableDTO.SupportedOperator> getSupportedOperators() {
        return this.supportedOperators;
    }

    public List<FlowVariableDTO.Candidate> getCandidates() {
        return this.candidates;
    }

    public String getCandidatesUrl() {
        return this.candidatesUrl;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public void setSupportedOperators(List<FlowVariableDTO.SupportedOperator> supportedOperators) {
        this.supportedOperators = supportedOperators;
    }

    public void setCandidates(List<FlowVariableDTO.Candidate> candidates) {
        this.candidates = candidates;
    }

    public void setCandidatesUrl(String candidatesUrl) {
        this.candidatesUrl = candidatesUrl;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowVariableModel)) {
            return false;
        }
        FlowVariableModel other = (FlowVariableModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$type = this.getType();
        String other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$sourceType = this.getSourceType();
        String other$sourceType = other.getSourceType();
        if (this$sourceType == null ? other$sourceType != null : !this$sourceType.equals(other$sourceType)) {
            return false;
        }
        List<FlowVariableDTO.SupportedOperator> this$supportedOperators = this.getSupportedOperators();
        List<FlowVariableDTO.SupportedOperator> other$supportedOperators = other.getSupportedOperators();
        if (this$supportedOperators == null ? other$supportedOperators != null : !(this$supportedOperators).equals(other$supportedOperators)) {
            return false;
        }
        List<FlowVariableDTO.Candidate> this$candidates = this.getCandidates();
        List<FlowVariableDTO.Candidate> other$candidates = other.getCandidates();
        if (this$candidates == null ? other$candidates != null : !(this$candidates).equals(other$candidates)) {
            return false;
        }
        String this$candidatesUrl = this.getCandidatesUrl();
        String other$candidatesUrl = other.getCandidatesUrl();
        return !(this$candidatesUrl == null ? other$candidatesUrl != null : !this$candidatesUrl.equals(other$candidatesUrl));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowVariableModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $sourceType = this.getSourceType();
        result = result * 59 + ($sourceType == null ? 43 : $sourceType.hashCode());
        List<FlowVariableDTO.SupportedOperator> $supportedOperators = this.getSupportedOperators();
        result = result * 59 + ($supportedOperators == null ? 43 : ($supportedOperators).hashCode());
        List<FlowVariableDTO.Candidate> $candidates = this.getCandidates();
        result = result * 59 + ($candidates == null ? 43 : ($candidates).hashCode());
        String $candidatesUrl = this.getCandidatesUrl();
        result = result * 59 + ($candidatesUrl == null ? 43 : $candidatesUrl.hashCode());
        return result;
    }

    public String toString() {
        return "FlowVariableModel(id=" + this.getId() + ", appId=" + this.getAppId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", type=" + this.getType() + ", sourceType=" + this.getSourceType() + ", supportedOperators=" + this.getSupportedOperators() + ", candidates=" + this.getCandidates() + ", candidatesUrl=" + this.getCandidatesUrl() + ")";
    }
}

