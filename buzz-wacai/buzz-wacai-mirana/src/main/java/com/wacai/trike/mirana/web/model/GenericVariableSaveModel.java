/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.web.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.domain.action.enums.VariableEnums;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class GenericVariableSaveModel {
    private Long id;
    private String code;
    private String name;
    private VariableEnums.DataType dataType;
    private VariableEnums.SourceType sourceType;
    private List<String> candidates;
    private String candidatesUrl;

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public VariableEnums.DataType getDataType() {
        return this.dataType;
    }

    public VariableEnums.SourceType getSourceType() {
        return this.sourceType;
    }

    public List<String> getCandidates() {
        return this.candidates;
    }

    public String getCandidatesUrl() {
        return this.candidatesUrl;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDataType(VariableEnums.DataType dataType) {
        this.dataType = dataType;
    }

    public void setSourceType(VariableEnums.SourceType sourceType) {
        this.sourceType = sourceType;
    }

    public void setCandidates(List<String> candidates) {
        this.candidates = candidates;
    }

    public void setCandidatesUrl(String candidatesUrl) {
        this.candidatesUrl = candidatesUrl;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericVariableSaveModel)) {
            return false;
        }
        GenericVariableSaveModel other = (GenericVariableSaveModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        VariableEnums.DataType this$dataType = this.getDataType();
        VariableEnums.DataType other$dataType = other.getDataType();
        if (this$dataType == null ? other$dataType != null : !((this$dataType)).equals(other$dataType)) {
            return false;
        }
        VariableEnums.SourceType this$sourceType = this.getSourceType();
        VariableEnums.SourceType other$sourceType = other.getSourceType();
        if (this$sourceType == null ? other$sourceType != null : !((this$sourceType)).equals(other$sourceType)) {
            return false;
        }
        List<String> this$candidates = this.getCandidates();
        List<String> other$candidates = other.getCandidates();
        if (this$candidates == null ? other$candidates != null : !(this$candidates).equals(other$candidates)) {
            return false;
        }
        String this$candidatesUrl = this.getCandidatesUrl();
        String other$candidatesUrl = other.getCandidatesUrl();
        return !(this$candidatesUrl == null ? other$candidatesUrl != null : !this$candidatesUrl.equals(other$candidatesUrl));
    }

    protected boolean canEqual(Object other) {
        return other instanceof GenericVariableSaveModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        VariableEnums.DataType $dataType = this.getDataType();
        result = result * 59 + ($dataType == null ? 43 : (($dataType)).hashCode());
        VariableEnums.SourceType $sourceType = this.getSourceType();
        result = result * 59 + ($sourceType == null ? 43 : (($sourceType)).hashCode());
        List<String> $candidates = this.getCandidates();
        result = result * 59 + ($candidates == null ? 43 : ($candidates).hashCode());
        String $candidatesUrl = this.getCandidatesUrl();
        result = result * 59 + ($candidatesUrl == null ? 43 : $candidatesUrl.hashCode());
        return result;
    }

    public String toString() {
        return "GenericVariableSaveModel(id=" + this.getId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", dataType=" + (this.getDataType()) + ", sourceType=" + (this.getSourceType()) + ", candidates=" + this.getCandidates() + ", candidatesUrl=" + this.getCandidatesUrl() + ")";
    }
}

