/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.Map;

public class TaskInstanceDetail
implements Serializable {
    @Deprecated
    private Long id;
    private String uuid;
    private String taskName;
    private String operator;
    private String operateTime;
    private Map<String, String> variables = new HashMap<String, String>();

    @Deprecated
    public Long getId() {
        return this.id;
    }

    public String getUuid() {
        return this.uuid;
    }

    public String getTaskName() {
        return this.taskName;
    }

    public String getOperator() {
        return this.operator;
    }

    public String getOperateTime() {
        return this.operateTime;
    }

    public Map<String, String> getVariables() {
        return this.variables;
    }

    @Deprecated
    public TaskInstanceDetail setId(Long id) {
        this.id = id;
        return this;
    }

    public TaskInstanceDetail setUuid(String uuid) {
        this.uuid = uuid;
        return this;
    }

    public TaskInstanceDetail setTaskName(String taskName) {
        this.taskName = taskName;
        return this;
    }

    public TaskInstanceDetail setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public TaskInstanceDetail setOperateTime(String operateTime) {
        this.operateTime = operateTime;
        return this;
    }

    public TaskInstanceDetail setVariables(Map<String, String> variables) {
        this.variables = variables;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof TaskInstanceDetail)) {
            return false;
        }
        TaskInstanceDetail other = (TaskInstanceDetail)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$uuid = this.getUuid();
        String other$uuid = other.getUuid();
        if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
            return false;
        }
        String this$taskName = this.getTaskName();
        String other$taskName = other.getTaskName();
        if (this$taskName == null ? other$taskName != null : !this$taskName.equals(other$taskName)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        String this$operateTime = this.getOperateTime();
        String other$operateTime = other.getOperateTime();
        if (this$operateTime == null ? other$operateTime != null : !this$operateTime.equals(other$operateTime)) {
            return false;
        }
        Map<String, String> this$variables = this.getVariables();
        Map<String, String> other$variables = other.getVariables();
        return !(this$variables == null ? other$variables != null : !(this$variables).equals(other$variables));
    }

    protected boolean canEqual(Object other) {
        return other instanceof TaskInstanceDetail;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        String $taskName = this.getTaskName();
        result = result * 59 + ($taskName == null ? 43 : $taskName.hashCode());
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        String $operateTime = this.getOperateTime();
        result = result * 59 + ($operateTime == null ? 43 : $operateTime.hashCode());
        Map<String, String> $variables = this.getVariables();
        result = result * 59 + ($variables == null ? 43 : ($variables).hashCode());
        return result;
    }

    public String toString() {
        return "TaskInstanceDetail(id=" + this.getId() + ", uuid=" + this.getUuid() + ", taskName=" + this.getTaskName() + ", operator=" + this.getOperator() + ", operateTime=" + this.getOperateTime() + ", variables=" + this.getVariables() + ")";
    }
}

