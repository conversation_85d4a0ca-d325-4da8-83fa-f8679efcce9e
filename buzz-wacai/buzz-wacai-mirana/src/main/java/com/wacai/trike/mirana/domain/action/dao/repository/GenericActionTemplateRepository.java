/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.criteria.CriteriaBuilder
 *  javax.persistence.criteria.CriteriaQuery
 *  javax.persistence.criteria.Predicate
 *  javax.persistence.criteria.Root
 *  org.springframework.data.domain.Page
 *  org.springframework.data.domain.PageRequest
 *  org.springframework.data.domain.Pageable
 *  org.springframework.data.domain.Sort
 *  org.springframework.data.domain.Sort$Direction
 *  org.springframework.data.jpa.domain.Specification
 *  org.springframework.data.jpa.repository.JpaRepository
 *  org.springframework.data.jpa.repository.JpaSpecificationExecutor
 */
package com.wacai.trike.mirana.domain.action.dao.repository;

import com.wacai.trike.mirana.domain.action.dao.po.GenericActionTemplatePO;
import com.wacai.trike.mirana.util.CriteriaBuilderUtils;
import com.wacai.trike.mirana.web.model.GenericTemplateRequest;
import java.util.ArrayList;
import java.util.List;
import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

public interface GenericActionTemplateRepository
extends JpaRepository<GenericActionTemplatePO, Long>,
JpaSpecificationExecutor<GenericActionTemplatePO> {
    public List<GenericActionTemplatePO> findByActive(boolean var1);

    default public Page<GenericActionTemplatePO> findAll(GenericTemplateRequest request) {
        PageRequest pageable = PageRequest.of((int)request.getPageIndex(), (int)request.getPageSize(), (Sort)Sort.by((Sort.Direction)Sort.Direction.DESC, (String[])new String[]{"createdTime"}));
        return this.findAll(new DefaultSpecification(request), (Pageable)pageable);
    }

    public static class DefaultSpecification
    implements Specification<GenericActionTemplatePO> {
        private final GenericTemplateRequest request;

        DefaultSpecification(GenericTemplateRequest request) {
            this.request = request;
        }

        public Predicate toPredicate(Root<GenericActionTemplatePO> root, CriteriaQuery<?> query, CriteriaBuilder builder) {
            ArrayList<Predicate> predicates = new ArrayList<Predicate>();
            if (this.request.getActive() != null) {
                predicates.add(CriteriaBuilderUtils.excludeNoAction(root, builder, this.request.getActive()));
            }
            Predicate[] pre = new Predicate[predicates.size()];
            return query.where(predicates.toArray(pre)).getRestriction();
        }
    }
}

