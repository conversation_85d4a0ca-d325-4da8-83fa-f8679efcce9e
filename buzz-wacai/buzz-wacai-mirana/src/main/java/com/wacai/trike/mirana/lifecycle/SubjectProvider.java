/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.google.common.collect.Lists
 *  org.apache.logging.log4j.util.Strings
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.boot.context.properties.ConfigurationProperties
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.lifecycle;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.wacai.trike.mirana.service.FlowSubjectDTO;
import com.wacai.trike.mirana.service.FlowSubjectService;
import com.wacai.trike.mirana.util.HttpUtil;
import com.wacai.trike.mirana.util.ObjectMappers;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.logging.log4j.util.Strings;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@ConfigurationProperties(prefix="alitar.subject.guardian.api")
public class SubjectProvider {
    private static final Logger log = LoggerFactory.getLogger(SubjectProvider.class);
    private Config config;
    @Autowired
    private FlowSubjectService flowSubjectService;

    public boolean bind(FlowSubjectDTO subject) {
        Request request = new Request();
        request.setBusinessCode(subject.getFlowCode());
        request.setSubscriptionCode(subject.getSubjectCode());
        try {
            String body = HttpUtil.post(this.config.bindPath(subject.getSubjectCode()), this.header(), this.transfer(request));
            log.info("response for bind operate {}", body);
        }
        catch (Exception e) {
            log.error("occurred exception: request bind operate", (Throwable)e);
            return false;
        }
        return true;
    }

    public boolean cancel(FlowSubjectDTO subject) {
        Request request = new Request();
        request.setBusinessCode(subject.getFlowCode());
        request.setSubscriptionCode(subject.getSubjectCode());
        try {
            String body = HttpUtil.post(this.config.cancelPath(subject.getSubjectCode()), this.header(), this.transfer(request));
            log.info("response for cancel operate {}", body);
        }
        catch (Exception e) {
            log.error("occurred exception: request cancel operate", (Throwable)e);
            return false;
        }
        return true;
    }

    public boolean subscribe(String flowCode, String instanceUUID, String caseId) {
        List<FlowSubjectDTO> subscribes = this.flowSubjectService.findByFlowCode(flowCode);
        if (CollectionUtils.isEmpty(subscribes)) {
            return false;
        }
        Request request = new Request();
        request.setBusinessCode(flowCode);
        Metadata metadata = new Metadata();
        metadata.setBzKey(caseId);
        metadata.setFlowCode(flowCode);
        metadata.setInstanceUUID(instanceUUID);
        request.setAssociatedData(ObjectMappers.mustWriteValue(metadata));
        try {
            String body = HttpUtil.post(this.config.subscribePath(caseId), this.header(), this.transfer(request));
            log.info("response for subscribe operate {}", body);
        }
        catch (Exception e) {
            log.error("occurred exception: request subscribe operate", (Throwable)e);
            return false;
        }
        return true;
    }

    private Map<String, String> header() {
        HashMap<String, String> header = new HashMap<String, String>();
        header.put("tenantCode", "XH-GOBLIN");
        return header;
    }

    private Map<String, Object> transfer(Object obj) {
        return ObjectMappers.mustReadValue(ObjectMappers.mustWriteValue(obj), new TypeReference<Map<String, Object>>(){});
    }

    private List<Subject> fetching() {
        String body = HttpUtil.get(this.config.listPath(), this.header());
        log.info("fetched {} from alitar", body);
        if (Strings.isBlank((String)body)) {
            return Lists.newArrayList();
        }
        return ObjectMappers.mustReadValue(body, new TypeReference<List<Subject>>(){});
    }

    public List<Subject> getSubjects() {
        return this.fetching();
    }

    public void setConfig(Config config) {
        this.config = config;
    }

    public static class Config {
        private String prefixURI = "http://alitar-web-api.xh.k2.test.wacai.info";
        private String listPath = "/subscription/list";

        public String listPath() {
            return this.prefixURI + this.listPath;
        }

        public String bindPath(String subscriptionCode) {
            return this.prefixURI + "/subscription/config/" + subscriptionCode + "/subscribe";
        }

        public String cancelPath(String subscriptionCode) {
            return this.prefixURI + "/subscription/config/" + subscriptionCode + "/unsubscribe";
        }

        public String subscribePath(String caseId) {
            return this.prefixURI + "/actions/MESSAGE_SUBSCRIPTION/do/" + caseId;
        }

        public void setPrefixURI(String prefixURI) {
            this.prefixURI = prefixURI;
        }

        public void setListPath(String listPath) {
            this.listPath = listPath;
        }
    }

    @JsonInclude(value=JsonInclude.Include.NON_NULL)
    public static class Request {
        private String businessCode;
        private String subscriptionCode;
        private String caseId;
        private String associatedData;

        public String getBusinessCode() {
            return this.businessCode;
        }

        public String getSubscriptionCode() {
            return this.subscriptionCode;
        }

        public String getCaseId() {
            return this.caseId;
        }

        public String getAssociatedData() {
            return this.associatedData;
        }

        public void setBusinessCode(String businessCode) {
            this.businessCode = businessCode;
        }

        public void setSubscriptionCode(String subscriptionCode) {
            this.subscriptionCode = subscriptionCode;
        }

        public void setCaseId(String caseId) {
            this.caseId = caseId;
        }

        public void setAssociatedData(String associatedData) {
            this.associatedData = associatedData;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof Request)) {
                return false;
            }
            Request other = (Request)o;
            if (!other.canEqual(this)) {
                return false;
            }
            String this$businessCode = this.getBusinessCode();
            String other$businessCode = other.getBusinessCode();
            if (this$businessCode == null ? other$businessCode != null : !this$businessCode.equals(other$businessCode)) {
                return false;
            }
            String this$subscriptionCode = this.getSubscriptionCode();
            String other$subscriptionCode = other.getSubscriptionCode();
            if (this$subscriptionCode == null ? other$subscriptionCode != null : !this$subscriptionCode.equals(other$subscriptionCode)) {
                return false;
            }
            String this$caseId = this.getCaseId();
            String other$caseId = other.getCaseId();
            if (this$caseId == null ? other$caseId != null : !this$caseId.equals(other$caseId)) {
                return false;
            }
            String this$associatedData = this.getAssociatedData();
            String other$associatedData = other.getAssociatedData();
            return !(this$associatedData == null ? other$associatedData != null : !this$associatedData.equals(other$associatedData));
        }

        protected boolean canEqual(Object other) {
            return other instanceof Request;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            String $businessCode = this.getBusinessCode();
            result = result * 59 + ($businessCode == null ? 43 : $businessCode.hashCode());
            String $subscriptionCode = this.getSubscriptionCode();
            result = result * 59 + ($subscriptionCode == null ? 43 : $subscriptionCode.hashCode());
            String $caseId = this.getCaseId();
            result = result * 59 + ($caseId == null ? 43 : $caseId.hashCode());
            String $associatedData = this.getAssociatedData();
            result = result * 59 + ($associatedData == null ? 43 : $associatedData.hashCode());
            return result;
        }

        public String toString() {
            return "SubjectProvider.Request(businessCode=" + this.getBusinessCode() + ", subscriptionCode=" + this.getSubscriptionCode() + ", caseId=" + this.getCaseId() + ", associatedData=" + this.getAssociatedData() + ")";
        }
    }

    @JsonInclude(value=JsonInclude.Include.NON_NULL)
    public static class Metadata {
        private String instanceUUID;
        private String flowCode;
        private String bzKey;
        private String subscriptionCode;

        public String getInstanceUUID() {
            return this.instanceUUID;
        }

        public String getFlowCode() {
            return this.flowCode;
        }

        public String getBzKey() {
            return this.bzKey;
        }

        public String getSubscriptionCode() {
            return this.subscriptionCode;
        }

        public void setInstanceUUID(String instanceUUID) {
            this.instanceUUID = instanceUUID;
        }

        public void setFlowCode(String flowCode) {
            this.flowCode = flowCode;
        }

        public void setBzKey(String bzKey) {
            this.bzKey = bzKey;
        }

        public void setSubscriptionCode(String subscriptionCode) {
            this.subscriptionCode = subscriptionCode;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof Metadata)) {
                return false;
            }
            Metadata other = (Metadata)o;
            if (!other.canEqual(this)) {
                return false;
            }
            String this$instanceUUID = this.getInstanceUUID();
            String other$instanceUUID = other.getInstanceUUID();
            if (this$instanceUUID == null ? other$instanceUUID != null : !this$instanceUUID.equals(other$instanceUUID)) {
                return false;
            }
            String this$flowCode = this.getFlowCode();
            String other$flowCode = other.getFlowCode();
            if (this$flowCode == null ? other$flowCode != null : !this$flowCode.equals(other$flowCode)) {
                return false;
            }
            String this$bzKey = this.getBzKey();
            String other$bzKey = other.getBzKey();
            if (this$bzKey == null ? other$bzKey != null : !this$bzKey.equals(other$bzKey)) {
                return false;
            }
            String this$subscriptionCode = this.getSubscriptionCode();
            String other$subscriptionCode = other.getSubscriptionCode();
            return !(this$subscriptionCode == null ? other$subscriptionCode != null : !this$subscriptionCode.equals(other$subscriptionCode));
        }

        protected boolean canEqual(Object other) {
            return other instanceof Metadata;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            String $instanceUUID = this.getInstanceUUID();
            result = result * 59 + ($instanceUUID == null ? 43 : $instanceUUID.hashCode());
            String $flowCode = this.getFlowCode();
            result = result * 59 + ($flowCode == null ? 43 : $flowCode.hashCode());
            String $bzKey = this.getBzKey();
            result = result * 59 + ($bzKey == null ? 43 : $bzKey.hashCode());
            String $subscriptionCode = this.getSubscriptionCode();
            result = result * 59 + ($subscriptionCode == null ? 43 : $subscriptionCode.hashCode());
            return result;
        }

        public String toString() {
            return "SubjectProvider.Metadata(instanceUUID=" + this.getInstanceUUID() + ", flowCode=" + this.getFlowCode() + ", bzKey=" + this.getBzKey() + ", subscriptionCode=" + this.getSubscriptionCode() + ")";
        }
    }

    public static class Subject {
        private long id;
        private String name;
        private String code;

        public long getId() {
            return this.id;
        }

        public String getName() {
            return this.name;
        }

        public String getCode() {
            return this.code;
        }

        public void setId(long id) {
            this.id = id;
        }

        public void setName(String name) {
            this.name = name;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof Subject)) {
                return false;
            }
            Subject other = (Subject)o;
            if (!other.canEqual(this)) {
                return false;
            }
            if (this.getId() != other.getId()) {
                return false;
            }
            String this$name = this.getName();
            String other$name = other.getName();
            if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
                return false;
            }
            String this$code = this.getCode();
            String other$code = other.getCode();
            return !(this$code == null ? other$code != null : !this$code.equals(other$code));
        }

        protected boolean canEqual(Object other) {
            return other instanceof Subject;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            long $id = this.getId();
            result = result * 59 + (int)($id >>> 32 ^ $id);
            String $name = this.getName();
            result = result * 59 + ($name == null ? 43 : $name.hashCode());
            String $code = this.getCode();
            result = result * 59 + ($code == null ? 43 : $code.hashCode());
            return result;
        }

        public String toString() {
            return "SubjectProvider.Subject(id=" + this.getId() + ", name=" + this.getName() + ", code=" + this.getCode() + ")";
        }
    }
}

