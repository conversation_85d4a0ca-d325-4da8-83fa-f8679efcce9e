/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.domain.bu.dto;

import com.wacai.trike.mirana.domain.action.dto.BaseDto;

public class ApplicationDto
extends BaseDto {
    private Long buId;
    private String code;
    private String name;
    private String description;

    public Long getBuId() {
        return this.buId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getDescription() {
        return this.description;
    }

    public void setBuId(Long buId) {
        this.buId = buId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ApplicationDto)) {
            return false;
        }
        ApplicationDto other = (ApplicationDto)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$buId = this.getBuId();
        Long other$buId = other.getBuId();
        if (this$buId == null ? other$buId != null : !(this$buId).equals(other$buId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$description = this.getDescription();
        String other$description = other.getDescription();
        return !(this$description == null ? other$description != null : !this$description.equals(other$description));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof ApplicationDto;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $buId = this.getBuId();
        result = result * 59 + ($buId == null ? 43 : ($buId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $description = this.getDescription();
        result = result * 59 + ($description == null ? 43 : $description.hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "ApplicationDto(buId=" + this.getBuId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", description=" + this.getDescription() + ")";
    }
}

