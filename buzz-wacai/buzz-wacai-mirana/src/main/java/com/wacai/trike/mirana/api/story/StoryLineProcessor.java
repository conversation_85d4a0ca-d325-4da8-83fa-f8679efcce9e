/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.Page
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 */
package com.wacai.trike.mirana.api.story;

import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.api.story.model.StoryCancelReq;
import com.wacai.trike.mirana.api.story.model.StoryCancelRes;
import com.wacai.trike.mirana.api.story.model.StoryEndReq;
import com.wacai.trike.mirana.api.story.model.StoryEndRes;
import com.wacai.trike.mirana.api.story.model.StoryModel;
import com.wacai.trike.mirana.api.story.model.StoryProcessReq;
import com.wacai.trike.mirana.api.story.model.StoryProcessRes;
import com.wacai.trike.mirana.api.story.model.StoryQueryReq;
import com.wacai.trike.mirana.api.story.model.StoryRedoReq;
import com.wacai.trike.mirana.api.story.model.StoryRedoRes;
import com.wacai.trike.mirana.api.story.model.StoryStartReq;
import com.wacai.trike.mirana.api.story.model.StoryStartRes;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;

public interface StoryLineProcessor {
    @PostMapping(value={"/story/start"})
    public ApiResponse<StoryStartRes> start(@RequestBody StoryStartReq var1);

    @PostMapping(value={"/story/process"})
    public ApiResponse<StoryProcessRes> process(@RequestBody StoryProcessReq var1);

    @PostMapping(value={"/story/end"})
    public ApiResponse<StoryEndRes> end(@RequestBody StoryEndReq var1);

    @PostMapping(value={"/story/cancel"})
    public ApiResponse<StoryCancelRes> cancel(@RequestBody StoryCancelReq var1);

    @PostMapping(value={"/story/redo"})
    public ApiResponse<StoryRedoRes> redo(@RequestBody StoryRedoReq var1);

    @GetMapping(value={"/story/query"})
    public ApiResponse<Page<StoryModel>> query(StoryQueryReq var1);
}

