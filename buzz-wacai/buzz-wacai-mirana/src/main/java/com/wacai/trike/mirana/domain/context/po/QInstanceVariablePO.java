/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.context.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.context.po.InstanceVariablePO;
import java.time.LocalDateTime;

public class QInstanceVariablePO
extends EntityPathBase<InstanceVariablePO> {
    private static final long serialVersionUID = 1025910883L;
    public static final QInstanceVariablePO instanceVariablePO = new QInstanceVariablePO("instanceVariablePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final StringPath field;
    public final NumberPath<Long> id;
    public final StringPath instanceUuid;
    public final StringPath taskUuid;
    public final EnumPath<VariableType> type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final StringPath value;

    public QInstanceVariablePO(String variable) {
        super(InstanceVariablePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.field = this.createString("field");
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.taskUuid = this.createString("taskUuid");
        this.type = this.createEnum("type", VariableType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.value = this.createString("value");
    }

    public QInstanceVariablePO(Path<? extends InstanceVariablePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.field = this.createString("field");
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.taskUuid = this.createString("taskUuid");
        this.type = this.createEnum("type", VariableType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.value = this.createString("value");
    }

    public QInstanceVariablePO(PathMetadata metadata) {
        super(InstanceVariablePO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.field = this.createString("field");
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.taskUuid = this.createString("taskUuid");
        this.type = this.createEnum("type", VariableType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.value = this.createString("value");
    }
}

