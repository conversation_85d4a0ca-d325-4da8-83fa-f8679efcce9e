/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.metrics;

import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.graph.FlowGraph;

public class FlowMetrics {
    private String bu;
    private String app;
    private String flow;
    private String node;
    private String operate;
    private String uuid;
    private String operator;

    public static FlowMetrics build(FlowGraph graph, InstanceContext context, String operate) {
        return new FlowMetricsBuilder().uuid(context.getUuid()).bu(graph.getBu()).app(graph.getApp()).flow(graph.getCode()).node(graph.getNode(context.getCurrentNodeId()).getName()).operate(operate).operator(context.getModifier()).build();
    }

    FlowMetrics(String bu, String app, String flow, String node, String operate, String uuid, String operator) {
        this.bu = bu;
        this.app = app;
        this.flow = flow;
        this.node = node;
        this.operate = operate;
        this.uuid = uuid;
        this.operator = operator;
    }

    public static FlowMetricsBuilder builder() {
        return new FlowMetricsBuilder();
    }

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getFlow() {
        return this.flow;
    }

    public String getNode() {
        return this.node;
    }

    public String getOperate() {
        return this.operate;
    }

    public String getUuid() {
        return this.uuid;
    }

    public String getOperator() {
        return this.operator;
    }

    public static class FlowMetricsBuilder {
        private String bu;
        private String app;
        private String flow;
        private String node;
        private String operate;
        private String uuid;
        private String operator;

        FlowMetricsBuilder() {
        }

        public FlowMetricsBuilder bu(String bu) {
            this.bu = bu;
            return this;
        }

        public FlowMetricsBuilder app(String app) {
            this.app = app;
            return this;
        }

        public FlowMetricsBuilder flow(String flow) {
            this.flow = flow;
            return this;
        }

        public FlowMetricsBuilder node(String node) {
            this.node = node;
            return this;
        }

        public FlowMetricsBuilder operate(String operate) {
            this.operate = operate;
            return this;
        }

        public FlowMetricsBuilder uuid(String uuid) {
            this.uuid = uuid;
            return this;
        }

        public FlowMetricsBuilder operator(String operator) {
            this.operator = operator;
            return this;
        }

        public FlowMetrics build() {
            return new FlowMetrics(this.bu, this.app, this.flow, this.node, this.operate, this.uuid, this.operator);
        }

        public String toString() {
            return "FlowMetrics.FlowMetricsBuilder(bu=" + this.bu + ", app=" + this.app + ", flow=" + this.flow + ", node=" + this.node + ", operate=" + this.operate + ", uuid=" + this.uuid + ", operator=" + this.operator + ")";
        }
    }
}

