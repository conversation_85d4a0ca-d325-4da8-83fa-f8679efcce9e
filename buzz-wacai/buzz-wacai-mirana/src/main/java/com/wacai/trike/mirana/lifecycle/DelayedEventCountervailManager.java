/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSONObject
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.platform.prophet.client.annotations.JobDesc
 *  com.wacai.platform.prophet.client.context.ExecuteContext
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.lifecycle;

import com.alibaba.fastjson.JSONObject;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.platform.prophet.client.annotations.JobDesc;
import com.wacai.platform.prophet.client.context.ExecuteContext;
import com.wacai.trike.mirana.Seraph.DelayedEventExecutor;
import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.po.QDelayedEventPO;
import com.wacai.trike.mirana.event.ESProcessingFactory;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
@JobDesc(jobCode="mirana_delayed_event_countervail_job")
public class DelayedEventCountervailManager {
    private static final Logger log = LoggerFactory.getLogger(DelayedEventCountervailManager.class);
    @Autowired
    private DelayedEventExecutor delayedEventExecutor;
    @Autowired
    private JPAQueryFactory jpaQueryFactory;
    @Autowired
    private ESProcessingFactory esProcessingFactory;
    private volatile boolean terminated = true;

    public void execute(ExecuteContext context) {
        if (!this.terminated) {
            log.info("The last task {} was not executed at {}", this.getClass().getSimpleName(), LocalDateTime.now());
            return;
        }
        this.esProcessingFactory.recoup();
        this.mark();
        JSONObject param = JSONObject.parseObject((String)context.getJobParameters());
        long intervals = Optional.ofNullable(param.getLong("intervals")).orElse(300L);
        long pageSize = Optional.ofNullable(param.getLong("pageSize")).orElse(5000L);
        log.info("starting prophet task to countervail delayed event at {}", LocalDateTime.now());
        long count = pageSize;
        while (count == pageSize) {
            count = this.run(intervals, pageSize);
        }
        this.terminate();
    }

    private long run(long intervals, long pageSize) {
        LocalDateTime beforeTime = LocalDateTime.now().minusSeconds(intervals);
        BooleanExpression expression = QDelayedEventPO.delayedEventPO.status.eq(DelayedEventStatus.LOADED).and((Predicate)QDelayedEventPO.delayedEventPO.expectFireTime.before((Comparable)beforeTime));
        List events = ((JPAQuery)((JPAQuery)this.jpaQueryFactory.selectFrom((EntityPath)QDelayedEventPO.delayedEventPO).where((Predicate)expression)).limit(pageSize)).fetch();
        if (CollectionUtils.isEmpty((Collection)events)) {
            return 0L;
        }
        events.forEach(po -> {
            this.delayedEventExecutor.execute((DelayedEventPO)po);
            log.info("countervailed firing delayed event {} of instance {}", po.getUuid(), po.getInstanceUuid());
        });
        return events.size();
    }

    private synchronized void mark() {
        if (this.terminated) {
            this.terminated = false;
        }
    }

    private synchronized void terminate() {
        if (!this.terminated) {
            this.terminated = true;
        }
    }
}

