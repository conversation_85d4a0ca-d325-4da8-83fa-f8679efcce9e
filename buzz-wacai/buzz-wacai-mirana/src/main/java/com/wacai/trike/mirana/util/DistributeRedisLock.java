/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.common.redis.RedisException
 *  com.wacai.trike.cloud.proxy.redis.RedisProxy
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.util;

import com.wacai.common.redis.RedisException;
import com.wacai.trike.cloud.proxy.redis.RedisProxy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class DistributeRedisLock {
    private static final Logger log = LoggerFactory.getLogger(DistributeRedisLock.class);
    private static final Long OK = 1L;
    private static final int timeout = 60;
    @Autowired
    private RedisProxy redisProxy;

    public boolean tryLock(String lock) {
        try {
            boolean result = OK.equals(this.redisProxy.setnx(lock, lock));
            if (result) {
                this.redisProxy.expire(lock, 60);
            }
            return result;
        }
        catch (RedisException e) {
            return false;
        }
    }

    public boolean tryUnLock(String lock) {
        try {
            return OK.equals(this.redisProxy.del(new String[]{lock}));
        }
        catch (RedisException e) {
            return false;
        }
    }
}

