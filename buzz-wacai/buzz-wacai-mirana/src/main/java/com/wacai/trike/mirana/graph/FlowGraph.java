/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.FlowType
 */
package com.wacai.trike.mirana.graph;

import com.wacai.trike.mirana.api.constant.FlowType;
import com.wacai.trike.mirana.delay.DelayCalculator;
import com.wacai.trike.mirana.delay.ScheduleType;
import com.wacai.trike.mirana.graph.Node;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

public class FlowGraph {
    private String bu;
    private String app;
    private String code;
    private String name;
    private String version;
    private String timeout;
    private Long id;
    private FlowType type;
    private Node startNode;
    private Map<Long, Node> nodeMap = new HashMap<Long, Node>();

    public Node getNode(Long nodeId) {
        return Objects.requireNonNull(this.nodeMap.get(nodeId));
    }

    public LocalDateTime getTimeout() {
        if (Objects.isNull(this.timeout)) {
            return null;
        }
        DelayCalculator calculator = (DelayCalculator)ScheduleType.DELAY_BEFORE.parse(this.timeout);
        return calculator.expectEnd(null);
    }

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getVersion() {
        return this.version;
    }

    public Long getId() {
        return this.id;
    }

    public FlowType getType() {
        return this.type;
    }

    public Node getStartNode() {
        return this.startNode;
    }

    public Map<Long, Node> getNodeMap() {
        return this.nodeMap;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setTimeout(String timeout) {
        this.timeout = timeout;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setType(FlowType type) {
        this.type = type;
    }

    public void setStartNode(Node startNode) {
        this.startNode = startNode;
    }

    public void setNodeMap(Map<Long, Node> nodeMap) {
        this.nodeMap = nodeMap;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowGraph)) {
            return false;
        }
        FlowGraph other = (FlowGraph)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$version = this.getVersion();
        String other$version = other.getVersion();
        if (this$version == null ? other$version != null : !this$version.equals(other$version)) {
            return false;
        }
        LocalDateTime this$timeout = this.getTimeout();
        LocalDateTime other$timeout = other.getTimeout();
        if (this$timeout == null ? other$timeout != null : !(this$timeout).equals(other$timeout)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        FlowType this$type = this.getType();
        FlowType other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        Node this$startNode = this.getStartNode();
        Node other$startNode = other.getStartNode();
        if (this$startNode == null ? other$startNode != null : !(this$startNode).equals(other$startNode)) {
            return false;
        }
        Map<Long, Node> this$nodeMap = this.getNodeMap();
        Map<Long, Node> other$nodeMap = other.getNodeMap();
        return !(this$nodeMap == null ? other$nodeMap != null : !(this$nodeMap).equals(other$nodeMap));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowGraph;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $version = this.getVersion();
        result = result * 59 + ($version == null ? 43 : $version.hashCode());
        LocalDateTime $timeout = this.getTimeout();
        result = result * 59 + ($timeout == null ? 43 : ($timeout).hashCode());
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        FlowType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        Node $startNode = this.getStartNode();
        result = result * 59 + ($startNode == null ? 43 : ($startNode).hashCode());
        Map<Long, Node> $nodeMap = this.getNodeMap();
        result = result * 59 + ($nodeMap == null ? 43 : ($nodeMap).hashCode());
        return result;
    }

    public String toString() {
        return "FlowGraph(bu=" + this.getBu() + ", app=" + this.getApp() + ", code=" + this.getCode() + ", name=" + this.getName() + ", version=" + this.getVersion() + ", timeout=" + this.getTimeout() + ", id=" + this.getId() + ", type=" + this.getType() + ", startNode=" + this.getStartNode() + ", nodeMap=" + this.getNodeMap() + ")";
    }
}

