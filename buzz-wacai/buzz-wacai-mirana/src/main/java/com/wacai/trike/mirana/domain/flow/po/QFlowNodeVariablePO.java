/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.flow.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.flow.po.FlowNodeVariablePO;
import java.time.LocalDateTime;

public class QFlowNodeVariablePO
extends EntityPathBase<FlowNodeVariablePO> {
    private static final long serialVersionUID = -1780368009L;
    public static final QFlowNodeVariablePO flowNodeVariablePO = new QFlowNodeVariablePO("flowNodeVariablePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> id;
    public final NumberPath<Long> nodeId;
    public final NumberPath<Long> parentFlowVariableId;
    public final NumberPath<Long> subFlowId;
    public final NumberPath<Long> subFlowVariableId;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QFlowNodeVariablePO(String variable) {
        super(FlowNodeVariablePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.parentFlowVariableId = this.createNumber("parentFlowVariableId", Long.class);
        this.subFlowId = this.createNumber("subFlowId", Long.class);
        this.subFlowVariableId = this.createNumber("subFlowVariableId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QFlowNodeVariablePO(Path<? extends FlowNodeVariablePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.parentFlowVariableId = this.createNumber("parentFlowVariableId", Long.class);
        this.subFlowId = this.createNumber("subFlowId", Long.class);
        this.subFlowVariableId = this.createNumber("subFlowVariableId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QFlowNodeVariablePO(PathMetadata metadata) {
        super(FlowNodeVariablePO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.id = this._super.id;
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.parentFlowVariableId = this.createNumber("parentFlowVariableId", Long.class);
        this.subFlowId = this.createNumber("subFlowId", Long.class);
        this.subFlowVariableId = this.createNumber("subFlowVariableId", Long.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

