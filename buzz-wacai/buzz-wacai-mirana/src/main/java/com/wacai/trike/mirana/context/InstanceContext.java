/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  org.springframework.beans.BeanUtils
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.context;

import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.api.impl.FlowProcessorImpl;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.Step;
import com.wacai.trike.mirana.context.TaskInstance;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;

public class InstanceContext implements Serializable {
    public static final String DUE_DATE_KEY = "dueDate";
    public static final String CREATE_NODE_ID_KEY = "createNodeId";
    private Long id;
    private Long flowId;
    private String flowCode;
    private String version;
    private String uuid;
    private String parentUuid;
    private String bzKey;
    private InstanceStatus status;
    private String creator;
    private String modifier;
    private Long currentNodeId;
    private TaskInstance currentTaskInstance;
    private LocalDateTime timeout;
    private List<TaskInstance> taskInstances = new ArrayList<TaskInstance>();
    private List<Step> steps = new ArrayList<Step>();
    private Map<String, String> effectiveVariables = new HashMap<String, String>();
    private boolean dirty;
    protected boolean async;
    private boolean derailed;
    private boolean esSync;
    private LocalDateTime createdTime;

    public InstanceContext addVariable(Map<String, String> variable, VariableType type) {
        if (!CollectionUtils.isEmpty(variable) && Objects.nonNull(type)) {
            this.effectiveVariables.putAll(variable);
            if (Objects.nonNull(this.currentTaskInstance)) {
                this.currentTaskInstance.addVariable(variable, type);
            }
        }
        return this;
    }

    public LocalDateTime getDueDate() {
        String dueDateStr = this.getEffectiveVariables().get(DUE_DATE_KEY);
        if (Objects.isNull(dueDateStr)) {
            return null;
        }
        return LocalDateTime.parse(dueDateStr, FlowProcessorImpl.dateTimeFormatter);
    }

    public InstanceContext copy() {
        InstanceContext copy = new InstanceContext();
        BeanUtils.copyProperties(this, copy);
        return copy;
    }

    public Long getId() {
        return this.id;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public String getFlowCode() {
        return this.flowCode;
    }

    public String getVersion() {
        return this.version;
    }

    public String getUuid() {
        return this.uuid;
    }

    public String getParentUuid() {
        return this.parentUuid;
    }

    public String getBzKey() {
        return this.bzKey;
    }

    public InstanceStatus getStatus() {
        return this.status;
    }

    public String getCreator() {
        return this.creator;
    }

    public String getModifier() {
        return this.modifier;
    }

    public Long getCurrentNodeId() {
        return this.currentNodeId;
    }

    public TaskInstance getCurrentTaskInstance() {
        return this.currentTaskInstance;
    }

    public LocalDateTime getTimeout() {
        return this.timeout;
    }

    public List<TaskInstance> getTaskInstances() {
        return this.taskInstances;
    }

    public List<Step> getSteps() {
        return this.steps;
    }

    public Map<String, String> getEffectiveVariables() {
        return this.effectiveVariables;
    }

    public boolean isDirty() {
        return this.dirty;
    }

    public boolean isAsync() {
        return this.async;
    }

    public boolean isDerailed() {
        return this.derailed;
    }

    public boolean isEsSync() {
        return this.esSync;
    }

    public LocalDateTime getCreatedTime() {
        return this.createdTime;
    }

    public InstanceContext setId(Long id) {
        this.id = id;
        return this;
    }

    public InstanceContext setFlowId(Long flowId) {
        this.flowId = flowId;
        return this;
    }

    public InstanceContext setFlowCode(String flowCode) {
        this.flowCode = flowCode;
        return this;
    }

    public InstanceContext setVersion(String version) {
        this.version = version;
        return this;
    }

    public InstanceContext setUuid(String uuid) {
        this.uuid = uuid;
        return this;
    }

    public InstanceContext setParentUuid(String parentUuid) {
        this.parentUuid = parentUuid;
        return this;
    }

    public InstanceContext setBzKey(String bzKey) {
        this.bzKey = bzKey;
        return this;
    }

    public InstanceContext setStatus(InstanceStatus status) {
        this.status = status;
        return this;
    }

    public InstanceContext setCreator(String creator) {
        this.creator = creator;
        return this;
    }

    public InstanceContext setModifier(String modifier) {
        this.modifier = modifier;
        return this;
    }

    public InstanceContext setCurrentNodeId(Long currentNodeId) {
        this.currentNodeId = currentNodeId;
        return this;
    }

    public InstanceContext setCurrentTaskInstance(TaskInstance currentTaskInstance) {
        this.currentTaskInstance = currentTaskInstance;
        return this;
    }

    public InstanceContext setTimeout(LocalDateTime timeout) {
        this.timeout = timeout;
        return this;
    }

    public InstanceContext setTaskInstances(List<TaskInstance> taskInstances) {
        this.taskInstances = taskInstances;
        return this;
    }

    public InstanceContext setSteps(List<Step> steps) {
        this.steps = steps;
        return this;
    }

    public InstanceContext setEffectiveVariables(Map<String, String> effectiveVariables) {
        this.effectiveVariables = effectiveVariables;
        return this;
    }

    public InstanceContext setDirty(boolean dirty) {
        this.dirty = dirty;
        return this;
    }

    public InstanceContext setAsync(boolean async) {
        this.async = async;
        return this;
    }

    public InstanceContext setDerailed(boolean derailed) {
        this.derailed = derailed;
        return this;
    }

    public InstanceContext setEsSync(boolean esSync) {
        this.esSync = esSync;
        return this;
    }

    public InstanceContext setCreatedTime(LocalDateTime createdTime) {
        this.createdTime = createdTime;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstanceContext)) {
            return false;
        }
        InstanceContext other = (InstanceContext)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$flowCode = this.getFlowCode();
        String other$flowCode = other.getFlowCode();
        if (this$flowCode == null ? other$flowCode != null : !this$flowCode.equals(other$flowCode)) {
            return false;
        }
        String this$version = this.getVersion();
        String other$version = other.getVersion();
        if (this$version == null ? other$version != null : !this$version.equals(other$version)) {
            return false;
        }
        String this$uuid = this.getUuid();
        String other$uuid = other.getUuid();
        if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
            return false;
        }
        String this$parentUuid = this.getParentUuid();
        String other$parentUuid = other.getParentUuid();
        if (this$parentUuid == null ? other$parentUuid != null : !this$parentUuid.equals(other$parentUuid)) {
            return false;
        }
        String this$bzKey = this.getBzKey();
        String other$bzKey = other.getBzKey();
        if (this$bzKey == null ? other$bzKey != null : !this$bzKey.equals(other$bzKey)) {
            return false;
        }
        InstanceStatus this$status = this.getStatus();
        InstanceStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !this$status.equals(other$status)) {
            return false;
        }
        String this$creator = this.getCreator();
        String other$creator = other.getCreator();
        if (this$creator == null ? other$creator != null : !this$creator.equals(other$creator)) {
            return false;
        }
        String this$modifier = this.getModifier();
        String other$modifier = other.getModifier();
        if (this$modifier == null ? other$modifier != null : !this$modifier.equals(other$modifier)) {
            return false;
        }
        Long this$currentNodeId = this.getCurrentNodeId();
        Long other$currentNodeId = other.getCurrentNodeId();
        if (this$currentNodeId == null ? other$currentNodeId != null : !(this$currentNodeId).equals(other$currentNodeId)) {
            return false;
        }
        TaskInstance this$currentTaskInstance = this.getCurrentTaskInstance();
        TaskInstance other$currentTaskInstance = other.getCurrentTaskInstance();
        if (this$currentTaskInstance == null ? other$currentTaskInstance != null : !(this$currentTaskInstance).equals(other$currentTaskInstance)) {
            return false;
        }
        LocalDateTime this$timeout = this.getTimeout();
        LocalDateTime other$timeout = other.getTimeout();
        if (this$timeout == null ? other$timeout != null : !(this$timeout).equals(other$timeout)) {
            return false;
        }
        List<TaskInstance> this$taskInstances = this.getTaskInstances();
        List<TaskInstance> other$taskInstances = other.getTaskInstances();
        if (this$taskInstances == null ? other$taskInstances != null : !(this$taskInstances).equals(other$taskInstances)) {
            return false;
        }
        List<Step> this$steps = this.getSteps();
        List<Step> other$steps = other.getSteps();
        if (this$steps == null ? other$steps != null : !(this$steps).equals(other$steps)) {
            return false;
        }
        Map<String, String> this$effectiveVariables = this.getEffectiveVariables();
        Map<String, String> other$effectiveVariables = other.getEffectiveVariables();
        if (this$effectiveVariables == null ? other$effectiveVariables != null : !(this$effectiveVariables).equals(other$effectiveVariables)) {
            return false;
        }
        if (this.isDirty() != other.isDirty()) {
            return false;
        }
        if (this.isAsync() != other.isAsync()) {
            return false;
        }
        if (this.isDerailed() != other.isDerailed()) {
            return false;
        }
        if (this.isEsSync() != other.isEsSync()) {
            return false;
        }
        LocalDateTime this$createdTime = this.getCreatedTime();
        LocalDateTime other$createdTime = other.getCreatedTime();
        return !(this$createdTime == null ? other$createdTime != null : !(this$createdTime).equals(other$createdTime));
    }

    protected boolean canEqual(Object other) {
        return other instanceof InstanceContext;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $flowCode = this.getFlowCode();
        result = result * 59 + ($flowCode == null ? 43 : $flowCode.hashCode());
        String $version = this.getVersion();
        result = result * 59 + ($version == null ? 43 : $version.hashCode());
        String $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        String $parentUuid = this.getParentUuid();
        result = result * 59 + ($parentUuid == null ? 43 : $parentUuid.hashCode());
        String $bzKey = this.getBzKey();
        result = result * 59 + ($bzKey == null ? 43 : $bzKey.hashCode());
        InstanceStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : $status.hashCode());
        String $creator = this.getCreator();
        result = result * 59 + ($creator == null ? 43 : $creator.hashCode());
        String $modifier = this.getModifier();
        result = result * 59 + ($modifier == null ? 43 : $modifier.hashCode());
        Long $currentNodeId = this.getCurrentNodeId();
        result = result * 59 + ($currentNodeId == null ? 43 : ($currentNodeId).hashCode());
        TaskInstance $currentTaskInstance = this.getCurrentTaskInstance();
        result = result * 59 + ($currentTaskInstance == null ? 43 : ($currentTaskInstance).hashCode());
        LocalDateTime $timeout = this.getTimeout();
        result = result * 59 + ($timeout == null ? 43 : ($timeout).hashCode());
        List<TaskInstance> $taskInstances = this.getTaskInstances();
        result = result * 59 + ($taskInstances == null ? 43 : ($taskInstances).hashCode());
        List<Step> $steps = this.getSteps();
        result = result * 59 + ($steps == null ? 43 : ($steps).hashCode());
        Map<String, String> $effectiveVariables = this.getEffectiveVariables();
        result = result * 59 + ($effectiveVariables == null ? 43 : ($effectiveVariables).hashCode());
        result = result * 59 + (this.isDirty() ? 79 : 97);
        result = result * 59 + (this.isAsync() ? 79 : 97);
        result = result * 59 + (this.isDerailed() ? 79 : 97);
        result = result * 59 + (this.isEsSync() ? 79 : 97);
        LocalDateTime $createdTime = this.getCreatedTime();
        result = result * 59 + ($createdTime == null ? 43 : ($createdTime).hashCode());
        return result;
    }

    public String toString() {
        return "InstanceContext(id=" + this.getId() + ", flowId=" + this.getFlowId() + ", flowCode=" + this.getFlowCode() + ", version=" + this.getVersion() + ", uuid=" + this.getUuid() + ", parentUuid=" + this.getParentUuid() + ", bzKey=" + this.getBzKey() + ", status=" + this.getStatus() + ", creator=" + this.getCreator() + ", modifier=" + this.getModifier() + ", currentNodeId=" + this.getCurrentNodeId() + ", currentTaskInstance=" + this.getCurrentTaskInstance() + ", timeout=" + this.getTimeout() + ", taskInstances=" + this.getTaskInstances() + ", steps=" + this.getSteps() + ", effectiveVariables=" + this.getEffectiveVariables() + ", dirty=" + this.isDirty() + ", async=" + this.isAsync() + ", derailed=" + this.isDerailed() + ", esSync=" + this.isEsSync() + ", createdTime=" + this.getCreatedTime() + ")";
    }
}

