/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.google.common.base.Strings
 *  com.google.common.collect.Lists
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.ComboBox
 *  com.wacai.loan.trike.expression.Operator
 *  com.wacai.loan.trike.expression.ThresholdType
 *  com.wacai.trike.mirana.api.constant.FlowStatus
 *  com.wacai.trike.mirana.api.constant.FormType
 *  com.wacai.trike.mirana.api.constant.NodeType
 *  com.wacai.trike.mirana.api.constant.TaskExecuteType
 *  com.wacai.trike.mirana.api.constant.TaskType
 *  com.wacai.trike.mirana.api.manage.FlowService
 *  com.wacai.trike.mirana.api.manage.model.ActionTemplateModel
 *  com.wacai.trike.mirana.api.manage.model.DelayModel
 *  com.wacai.trike.mirana.api.manage.model.EdgeModel
 *  com.wacai.trike.mirana.api.manage.model.ExpressionModel
 *  com.wacai.trike.mirana.api.manage.model.FlowCreateModel
 *  com.wacai.trike.mirana.api.manage.model.FlowGraphModel
 *  com.wacai.trike.mirana.api.manage.model.FlowNodeVariableModel
 *  com.wacai.trike.mirana.api.manage.model.FlowVersionModel
 *  com.wacai.trike.mirana.api.manage.model.FlowVersionModel$VersionModel
 *  com.wacai.trike.mirana.api.manage.model.NodeModel
 *  com.wacai.trike.mirana.api.manage.model.NodeModel$REBASE
 *  com.wacai.trike.mirana.api.model.EsFlowTaskInstanceListModel
 *  com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel
 *  com.wacai.trike.mirana.api.model.EventModel
 *  javax.validation.Valid
 *  javax.validation.constraints.NotNull
 *  org.apache.commons.lang3.EnumUtils
 *  org.apache.commons.lang3.StringUtils
 *  org.apache.curator.shaded.com.google.common.collect.ImmutableList
 *  org.joda.time.DateTime
 *  org.joda.time.format.DateTimeFormat
 *  org.joda.time.format.DateTimeFormatter
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.BeanUtils
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.beans.factory.annotation.Value
 *  org.springframework.util.CollectionUtils
 *  org.springframework.validation.annotation.Validated
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl.manage;

import com.alibaba.dubbo.config.annotation.Service;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.ComboBox;
import com.wacai.loan.trike.expression.Operator;
import com.wacai.loan.trike.expression.ThresholdType;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.FormType;
import com.wacai.trike.mirana.api.constant.NodeType;
import com.wacai.trike.mirana.api.constant.TaskExecuteType;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.api.manage.FlowService;
import com.wacai.trike.mirana.api.manage.model.ActionTemplateModel;
import com.wacai.trike.mirana.api.manage.model.DelayModel;
import com.wacai.trike.mirana.api.manage.model.EdgeModel;
import com.wacai.trike.mirana.api.manage.model.ExpressionModel;
import com.wacai.trike.mirana.api.manage.model.FlowCreateModel;
import com.wacai.trike.mirana.api.manage.model.FlowGraphModel;
import com.wacai.trike.mirana.api.manage.model.FlowNodeVariableModel;
import com.wacai.trike.mirana.api.manage.model.FlowVersionModel;
import com.wacai.trike.mirana.api.manage.model.NodeModel;
import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceListModel;
import com.wacai.trike.mirana.api.model.EsFlowTaskInstanceModel;
import com.wacai.trike.mirana.api.model.EventModel;
import com.wacai.trike.mirana.api.model.FlowModel;
import com.wacai.trike.mirana.common.enums.EdgeType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.context.InstanceContextService;
import com.wacai.trike.mirana.delay.ScheduleContent;
import com.wacai.trike.mirana.delay.ScheduleType;
import com.wacai.trike.mirana.domain.action.GenericTemplateService;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.domain.edge.po.EdgePO;
import com.wacai.trike.mirana.domain.edge.repository.EdgeRepository;
import com.wacai.trike.mirana.domain.expression.po.ExpressionPO;
import com.wacai.trike.mirana.domain.expression.repository.ExpressionRepository;
import com.wacai.trike.mirana.domain.flow.po.FlowPO;
import com.wacai.trike.mirana.domain.flow.po.FlowUnionNodePO;
import com.wacai.trike.mirana.domain.flow.po.QFlowPO;
import com.wacai.trike.mirana.domain.flow.repository.FlowRepository;
import com.wacai.trike.mirana.domain.flow.repository.FlowUnionNodeRepository;
import com.wacai.trike.mirana.domain.form.po.FormPO;
import com.wacai.trike.mirana.domain.form.repository.FormRepository;
import com.wacai.trike.mirana.domain.instance.repository.InstanceRepository;
import com.wacai.trike.mirana.domain.manage.po.ActionConfigPO;
import com.wacai.trike.mirana.domain.manage.po.ActionTemplatePO;
import com.wacai.trike.mirana.domain.manage.po.NodeActionPO;
import com.wacai.trike.mirana.domain.manage.po.VariableConfigPO;
import com.wacai.trike.mirana.domain.manage.repository.ActionConfigRepository;
import com.wacai.trike.mirana.domain.manage.repository.ActionTemplateRepository;
import com.wacai.trike.mirana.domain.manage.repository.NodeActionRepository;
import com.wacai.trike.mirana.domain.manage.repository.VariableConfigRepository;
import com.wacai.trike.mirana.domain.node.po.NodePO;
import com.wacai.trike.mirana.domain.node.repository.NodeRepository;
import com.wacai.trike.mirana.elasticsearch.service.api.EsQueryFlowTaskInstanceService;
import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceQuery;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.service.FlowNodeVariableDTO;
import com.wacai.trike.mirana.service.FlowNodeVariableRequest;
import com.wacai.trike.mirana.service.FlowNodeVariableService;
import com.wacai.trike.mirana.service.FlowSubjectDTO;
import com.wacai.trike.mirana.service.FlowSubjectService;
import com.wacai.trike.mirana.service.FlowVarRequest;
import com.wacai.trike.mirana.service.FlowVarService;
import com.wacai.trike.mirana.service.FlowVariableDTO;
import com.wacai.trike.mirana.util.DistributeRedisLock;
import com.wacai.trike.mirana.util.HttpUtil;
import com.wacai.trike.mirana.util.ObjectMappers;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.util.VersionAttribution;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import org.apache.commons.lang3.EnumUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.ImmutableList;
import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Service(interfaceClass = FlowService.class)
@Validated
public class FlowServiceImpl implements FlowService {

	private static final Logger log = LoggerFactory.getLogger(FlowServiceImpl.class);
	private static final List<ComboBox<String>> DELAY_TYPE_COMBO = Arrays.asList(
			ComboBox.of(ScheduleType.DELAY_BEFORE.getDesc(), ScheduleType.DELAY_BEFORE.name()),
			ComboBox.of(ScheduleType.DELAY_AFTER.getDesc(), ScheduleType.DELAY_AFTER.name()));
	private static final List<ComboBox<String>> DELAY_UNIT_COMBO = Arrays.asList(
			ComboBox.of("\u79d2", ChronoUnit.SECONDS.name()), ComboBox.of("\u5206", ChronoUnit.MINUTES.name()),
			ComboBox.of("\u65f6", ChronoUnit.HOURS.name()));
	private static final List<Operator> MULTIPLE_OPTIONS = ImmutableList.of(Operator.IN, Operator.NOT_IN);
	private static final String JOINER = "_";
	private static final String INITIALIZE_VERSION = new VersionAttribution().currentVersion();

	@Autowired
	private JPAQueryFactory queryFactory;
	@Autowired
	private FlowRepository flowRepository;
	@Autowired
	private NodeRepository nodeRepository;
	@Autowired
	private EdgeRepository edgeRepository;
	@Autowired
	private FormRepository formRepository;
	@Autowired
	private ExpressionRepository expressionRepository;
	@Autowired
	private NodeActionRepository actionRepository;
	@Autowired
	private ActionConfigRepository actionConfigRepository;
	@Autowired
	private VariableConfigRepository variableConfigRepository;
	@Autowired
	private ActionTemplateRepository actionTemplateRepository;
	@Autowired
	private FlowGraphService graphService;
	@Autowired
	private InstanceRepository instanceRepository;
	@Autowired
	private InstanceContextService instanceContextService;
	@Autowired
	private FlowNodeVariableService flowNodeVariableService;
	@Autowired
	private FlowVarService flowVarService;
	@Autowired
	private FlowUnionNodeRepository flowUnionNodeRepository;
	@Autowired
	private GenericTemplateService genericTemplateService;
	@Autowired
	private DistributeRedisLock distributeRedisLock;
	@Autowired
	private EsQueryFlowTaskInstanceService esQueryFlowTaskInstanceService;
	@Autowired
	private FlowSubjectService flowSubjectService;
	@Value(value = "${alitar.sum.url}")
	private String alitarSumUrl;
	private final Pattern pattern = Pattern.compile("[0-9]*");

	public ApiResponse<Long> create(@RequestBody @NotNull @Valid FlowCreateModel model) {
		if (this.pattern.matcher(model.getCode()).matches()) {
			return ApiResponse.error("\u6d41\u7a0b\u7f16\u53f7\u4e0d\u80fd\u4e3a\u7eaf\u6570\u5b57");
		}
		if (this.flowRepository.existsByAppIdAndCode(model.getAppId(), model.getCode())) {
			return ApiResponse.error("\u6d41\u7a0b\u5df2\u5b58\u5728");
		}
		FlowPO flowPO = ObjectUtils.convertNotNull(model, FlowPO.class);
		flowPO.setStatus(FlowStatus.EDITING);
		flowPO.setVersion(INITIALIZE_VERSION);
		return ApiResponse.success(this.flowRepository.save(flowPO).getId());
	}

	public ApiResponse<Long> draw(@NotNull @Valid FlowGraphModel model) {
		return this.savingFlow(model, false);
	}

	public ApiResponse<Long> storyDraw(@NotNull @Valid FlowGraphModel model) {
		return this.savingFlow(model, true);
	}

	private ApiResponse<Long> savingFlow(@NotNull FlowGraphModel model, boolean storyLine) {
		FlowPO flowPO;
		if (model.isForking()) {
			FlowCreateModel createModel = ObjectUtils.convertNotNull(model, FlowCreateModel.class);
			createModel.setId(null);
			ApiResponse<Long> response = this.create(createModel);
			if (!response.success()) {
				return response;
			}
			model.setId((Long) response.getData());
			model.setStatus(FlowStatus.EDITING);
		}
		if ((flowPO = (FlowPO) this.flowRepository.findById(model.getId()).orElse(null)) == null) {
			return ApiResponse.error((String) ("Not found " + model.getId()));
		}
		Detector detector = this.sanity(flowPO.getStatus(), model);
		if (!detector.success) {
			return ApiResponse.error(detector.message);
		}
		return this.savingFlow(flowPO, model, storyLine);
	}

	/*
	 * WARNING - Removed try catching itself - possible behaviour change.
	 */
	private ApiResponse<Long> savingFlow(FlowPO flowPO, FlowGraphModel model, boolean storyLine) {
		String flowLockCode = this.flowLockCode(model.getAppId(), model.getCode());
		try {
			if (!this.distributeRedisLock.tryLock(flowLockCode)) {
				return ApiResponse.error("当前流程" + flowLockCode + "正在被他人修改，请稍后重试。");
			}
			if (flowPO.getStatus() == FlowStatus.EDITING) {
				if (model.getStatus() == FlowStatus.EDITING) {
					this.juxtapose((FlowGraphModel) this.get(flowPO.getId()).getData(), model);
					this.cleaningFlowGraph(model.getId(), storyLine);
					this.savingByFlow(model, storyLine);
					this.savingFlowStatus(flowPO, model.getStatus());
				}
				if (model.getStatus() == FlowStatus.ENABLED || model.getStatus() == FlowStatus.DISABLED) {
					this.offlineFlows(model.getCode(), flowPO.getId());
					this.graphService.refreshFlowGraph(model.getCode());
					this.savingFlowStatus(flowPO, model.getStatus());
				}
			}
			if (flowPO.getStatus() == FlowStatus.DISABLED || flowPO.getStatus() == FlowStatus.ENABLED) {
				if (model.getStatus() == FlowStatus.EDITING) {
					FlowPO factor = this.nextFlowVersion(model);
					model.setId(factor.getId());
					this.juxtapose((FlowGraphModel) this.get(flowPO.getId()).getData(), model);
					this.savingByFlow(model, storyLine);
				}
				if (model.getStatus() == FlowStatus.ENABLED || model.getStatus() == FlowStatus.DISABLED) {
					if (model.getStatus() == FlowStatus.ENABLED) {
						this.offlineFlows(model.getCode(), flowPO.getId());
						this.graphService.refreshFlowGraph(model.getCode());
					}
					this.savingFlowStatus(flowPO, model.getStatus());
				}
			}
			return ApiResponse.success( model.getId());
		} finally {
			this.distributeRedisLock.tryUnLock(flowLockCode);
		}
	}

	private void juxtapose(FlowGraphModel comparision, FlowGraphModel graph) {
		graph.getNodes().forEach(n -> {
			String currentNodeId = n.getId();
			NodeModel node = this.pick(comparision, (NodeModel) n);
			if (node != null) {
				n.setRebase(NodeModel.REBASE.ORIGINAL);
				n.setId(node.getId());
				n.setGeneratedId(n.getGeneratedId());
				this.organisingGraph(graph, currentNodeId, node.getId());
				node.setRebase(NodeModel.REBASE.COMPARED);
				return;
			}
			NodeModel reusing = this.reusing((NodeModel) n, graph.getCode());
			if (reusing == null) {
				n.setRebase(NodeModel.REBASE.BRAND_NEW);
			} else {
				n.setRebase(NodeModel.REBASE.ORIGINAL);
				n.setId(reusing.getId());
				n.setGeneratedId(reusing.getGeneratedId());
				this.organisingGraph(graph, currentNodeId, reusing.getId());
			}
		});
		if (comparision.getStatus() == FlowStatus.EDITING && graph.getStatus() == FlowStatus.EDITING) {
			comparision.getNodes().forEach(n -> {
				if (n.getRebase() == NodeModel.REBASE.COMPARED) {
					return;
				}
				n.setRebase(NodeModel.REBASE.DELETING);
				graph.getNodes().add(n);
			});
		}
	}

	private void organisingGraph(FlowGraphModel graph, String oldNode, String newNode) {
		List<EdgeModel> edges = graph.getEdges();
		if (CollectionUtils.isEmpty(edges)) {
			return;
		}
		edges.forEach(edge -> {
			if (edge.getSource().equals(oldNode)) {
				edge.setSource(newNode);
			}
			if (edge.getTarget().equals(oldNode)) {
				edge.setTarget(newNode);
			}
		});
	}

	private NodeModel reusing(NodeModel node, String flowCode) {
		List<FlowPO> flows = this.flowRepository.findByCode(flowCode);
		if (CollectionUtils.isEmpty(flows)) {
			return null;
		}
		List<NodeModel> nodes = this.nodeRepository
				.findDistinctByFlowIdIn(
						flows.stream().map(BasePO::getId).sorted((a, b) -> a < b ? 1 : 0).collect(Collectors.toList()))
				.stream().map(this::convert).collect(Collectors.toList());
		if (CollectionUtils.isEmpty(nodes)) {
			return null;
		}
		for (NodeModel n : nodes) {
			if (node.detecting(n))
				continue;
			return n;
		}
		return null;
	}

	private NodeModel pick(FlowGraphModel comparision, NodeModel input) {
		if (comparision == null || CollectionUtils.isEmpty( comparision.getNodes())) {
			log.error("should never occurred: graph comparision node collection is empty");
			return null;
		}
		return comparision.getNodes().stream().filter(n -> !n.detecting(input)).findFirst().orElse(null);
	}

	private void offlineFlows(String flowCode, Long excludeFlowId) {
		List<FlowPO> flows = ((JPAQuery) this.queryFactory.selectFrom((EntityPath) QFlowPO.flowPO)
				.where((Predicate) QFlowPO.flowPO.code.eq(flowCode)
						.and((Predicate) QFlowPO.flowPO.status.eq(FlowStatus.ENABLED))
						.and((Predicate) QFlowPO.flowPO.id.ne(excludeFlowId)))).fetch();

		if (!CollectionUtils.isEmpty(flows)) {
			flows.forEach(flow -> flow.setStatus(FlowStatus.DISABLED));
			this.flowRepository.saveAll(flows);
			this.graphService.refreshFlowGraph(flowCode);
		}
	}

	private Detector sanity(FlowStatus current, FlowGraphModel graph) {
		boolean editing;
		if (graph == null) {
			return new Detector("数据格式不正确");
		}
		if (!current.switchTo(graph.getStatus())) {
			return new Detector(String.format("操作失败，流程状态%s不能保存至", current, graph.getStatus()));
		}
		boolean bl = editing = current == FlowStatus.EDITING && graph.getStatus() == FlowStatus.EDITING
				|| current == FlowStatus.DISABLED && graph.getStatus() == FlowStatus.EDITING
				|| current == FlowStatus.ENABLED && graph.getStatus() == FlowStatus.EDITING;
		if (editing && !CollectionUtils.isEmpty( graph.getNodes())) {
			ArrayList nodes = Lists.newArrayList( graph.getNodes());
			for (int i = 0; i < nodes.size(); ++i) {
				NodeModel found = this.exist(graph.getNodes(), (NodeModel) nodes.get(i), i);
				if (found == null)
					continue;
				return new Detector(String.format(
						"当前流程图中存在完全一样的节点%s-%s，需要适当修改节点属性。",
						((NodeModel) nodes.get(i)).getLabel(), found.getLabel()));
			}
		}
		return new Detector();
	}

	private NodeModel exist(List<NodeModel> nodes, NodeModel ele, int excludeIndex) {
		return nodes.stream().skip(excludeIndex + 1).filter(n -> !n.detecting(ele)).findFirst().orElse(null);
	}

	private String flowLockCode(@NotNull Long appId, @NotNull String code) {
		return String.join((CharSequence) JOINER, Arrays.asList(String.valueOf(appId), code));
	}

	private FlowPO nextFlowVersion(FlowGraphModel model) {
		FlowPO factor;
		FlowPO flowPO = (FlowPO) ((JPAQuery) ((JPAQuery) ((JPAQuery) this.queryFactory
				.selectFrom((EntityPath) QFlowPO.flowPO)
				.where((Predicate) QFlowPO.flowPO.appId.eq(model.getAppId())
						.and((Predicate) QFlowPO.flowPO.code.eq(model.getCode()))))
								.orderBy(QFlowPO.flowPO.version.desc())).limit(1L)).fetchFirst();
		if (flowPO == null) {
			factor = ObjectUtils.convertNotNull(model, FlowPO.class);
			factor.setVersion(INITIALIZE_VERSION);
		} else {
			factor = ObjectUtils.convertNotNull(flowPO, FlowPO.class);
			factor.setStatus(FlowStatus.EDITING);
			factor.setVersion(
					new VersionAttribution(Optional.ofNullable(flowPO.getVersion()).orElse(INITIALIZE_VERSION))
							.nextVersion());
		}
		FlowPO editing = (FlowPO) ((JPAQuery) this.queryFactory.selectFrom((EntityPath) QFlowPO.flowPO)
				.where((Predicate) QFlowPO.flowPO.appId.eq(model.getAppId())
						.and((Predicate) QFlowPO.flowPO.code.eq(model.getCode()))
						.and((Predicate) QFlowPO.flowPO.status.eq(FlowStatus.EDITING)))).fetchFirst();
		if (editing != null) {
			editing.setStatus(FlowStatus.DISABLED);
			this.flowRepository.save(editing);
			log.info("update {} from {} flow to {} of flow {}",
					new Object[] { editing.getId(), FlowStatus.EDITING, FlowStatus.DISABLED, model.getCode() });
		}
		factor.setId(null);
		this.flowRepository.save(factor);
		return factor;
	}

	private void savingByFlow(@NotNull FlowGraphModel model, boolean storyLine) {
		List<NodeModel> nodes = model.getNodes();
		List<EdgeModel> edges = model.getEdges();
		this.settingStartNode(nodes, edges);
		HashMap<String, NodeModel> nodeModelMap = new HashMap<String, NodeModel>();
		nodes.forEach(nodeModel -> {
			if (nodeModel.getRebase() == NodeModel.REBASE.DELETING) {
				nodeModel.setGeneratedId(Long.valueOf(nodeModel.getId().substring("node_".length())));
				this.deleteUnion(model.getId(), nodeModel.getGeneratedId());
				return;
			}
			if (nodeModel.getRebase() == NodeModel.REBASE.ORIGINAL) {
				nodeModelMap.put(nodeModel.getId(), (NodeModel) nodeModel);
				nodeModel.setGeneratedId(Long.valueOf(nodeModel.getId().substring("node_".length())));
				this.buildUnion(model.getId(), nodeModel.getGeneratedId());
				return;
			}
			String currentNodeId = nodeModel.getId();
			NodePO nodePO = new NodePO();
			nodePO.setFlowId(model.getId());
			nodePO.setCode(nodeModel.getCode());
			nodePO.setName(nodeModel.getLabel());
			nodePO.setType(nodeModel.getType());
			if (storyLine) {
				nodePO.setTaskExecuteType(TaskExecuteType.AUTO);
				nodePO.setTaskType(TaskType.START_SUB_FLOW);
				nodePO.setTaskContent(nodeModel.getSubflowCode());
			} else {
				nodePO.setTaskExecuteType(nodeModel.getTaskExecuteType());
				if (nodeModel.getAction() != null) {
					this.savingGenericAction(nodePO, nodeModel.getAction());
				}
			}
			DelayModel delayModel = nodeModel.getDelay();
			if (delayModel != null && delayModel.isFlag()) {
				ScheduleContent scheduleContent = new ScheduleContent();
				BeanUtils.copyProperties( delayModel,  scheduleContent);
				nodePO.setScheduleType(storyLine ? ScheduleType.DELAY_DAYS : ScheduleType.DELAY_BEFORE);
				nodePO.setScheduleContent(ObjectMappers.mustWriteValue(scheduleContent));
			}
			this.nodeRepository.save(nodePO);
			nodeModel.setId("node_" + nodePO.getId());
			nodeModel.setGeneratedId(nodePO.getId());
			this.organisingGraph(model, currentNodeId, nodeModel.getId());
			nodeModelMap.put(nodeModel.getId(), (NodeModel) nodeModel);
			this.buildUnion(model.getId(), nodePO.getId());
			this.savingFlowNodeVariables(nodePO, nodeModel.getFlowNodeVariables());
		});
		this.savingEdges(model.getAppId(), model.getId(), edges, nodeModelMap);
	}

	private void deleteUnion(long flowId, long nodeId) {
		FlowUnionNodePO union = this.flowUnionNodeRepository.findByFlowIdAndNodeId(flowId, nodeId);
		if (union == null) {
			log.error("should never occurred: not found union of flow {} and node {}",  flowId,
					 nodeId);
			return;
		}
		this.flowUnionNodeRepository.delete(union);
	}

	private void buildUnion(long flowId, long nodeId) {
		FlowUnionNodePO union = this.flowUnionNodeRepository.findByFlowIdAndNodeId(flowId, nodeId);
		if (union != null) {
			return;
		}
		union = new FlowUnionNodePO();
		union.setFlowId(flowId);
		union.setNodeId(nodeId);
		this.flowUnionNodeRepository.save(union);
	}

	private void savingFlowStatus(@NotNull FlowPO flowPO, FlowStatus target) {
		flowPO.setStatus(target);
		this.flowRepository.save(flowPO);
	}

	private void cleaningFlowGraph(@NotNull Long flowId, boolean storyLine) {
		List<FlowUnionNodePO> nodePOList = this.flowUnionNodeRepository.findByFlowId(flowId);
		if (CollectionUtils.isEmpty(nodePOList)) {
			return;
		}
		nodePOList.forEach(po -> {
			if (storyLine) {
				this.flowNodeVariableService.deleteByNodeId(po.getNodeId());
			}
		});
		List<EdgePO> edgePOList = this.edgeRepository.findByFlowId(flowId);
		if (CollectionUtils.isEmpty(edgePOList)) {
			return;
		}
		edgePOList.forEach(po -> {
			this.edgeRepository.deleteById(po.getId());
			List<ExpressionPO> expressionPOList = this.expressionRepository.findByEdgeId(po.getId());
			if (!CollectionUtils.isEmpty(expressionPOList)) {
				expressionPOList.forEach(expressionPO -> this.expressionRepository.deleteById(expressionPO.getId()));
			}
		});
	}

	private void settingStartNode(List<NodeModel> nodeModels, List<EdgeModel> edgeModels) {
		if (CollectionUtils.isEmpty(nodeModels)) {
			return;
		}
		if (CollectionUtils.isEmpty(edgeModels)) {
			nodeModels.get(0).setType(NodeType.START);
			return;
		}
		HashSet toNodeIds = new HashSet();
		HashSet fromNodeIds = new HashSet();
		edgeModels.forEach(item -> {
			toNodeIds.add(item.getTarget());
			fromNodeIds.add(item.getSource());
		});
		nodeModels.forEach(item -> {
			if (!toNodeIds.contains(item.getId()) && fromNodeIds.contains(item.getId())) {
				item.setType(NodeType.START);
			} else {
				item.setType(NodeType.GENERIC);
			}
		});
	}

	private void savingEdges(@NotNull Long appId, @NotNull Long flowId, List<EdgeModel> edgeModels,
			Map<String, NodeModel> nodeModelMap) {
		edgeModels.forEach(edgeModel -> {
			EdgePO edgePO = new EdgePO();
			edgePO.setFlowId(flowId);
			edgePO.setFromId(((NodeModel) nodeModelMap.get(edgeModel.getSource())).getGeneratedId());
			edgePO.setFromType(EdgeType.NODE);
			edgePO.setToId(((NodeModel) nodeModelMap.get(edgeModel.getTarget())).getGeneratedId());
			edgePO.setToType(EdgeType.NODE);
			this.edgeRepository.save(edgePO);
			this.savingExpression(appId, edgePO.getId(), edgeModel.getExpressions());
		});
	}

	private void savingExpression(@NotNull Long appId, @NotNull Long edgeId, List<ExpressionModel> expressions) {
		if (CollectionUtils.isEmpty(expressions)) {
			return;
		}
		expressions.forEach(expressionModel -> {
			ExpressionPO expressionPO = new ExpressionPO();
			BeanUtils.copyProperties( expressionModel,  expressionPO);
			expressionPO
					.setOperator((Operator) EnumUtils.getEnum(Operator.class, (String) expressionModel.getOperator()));
			expressionPO.setEdgeId(edgeId);
			if (Strings.isNullOrEmpty((String) expressionPO.getFieldType())) {
				FlowVarRequest request = new FlowVarRequest();
				request.setCodes(Lists.newArrayList(new String[] { expressionPO.getField() }));
				List<FlowVariableDTO> variables = this.flowVarService.query(request);
				if (CollectionUtils.isEmpty(variables)) {
					expressionPO.setFieldType("STRING");
				} else {
					expressionPO.setFieldType(variables.get(0).getType());
				}
			}
			if (MULTIPLE_OPTIONS.contains(expressionPO.getOperator())) {
				expressionPO.setThreshold(ObjectMappers.mustWriteValue(expressionModel.getThreshold()));
			} else if (expressionPO.getField().equals("intervalVal")) {
				expressionPO.setFieldType("NUMBER");
				expressionPO.setThreshold((String) expressionModel.getThreshold().get(0));
			} else {
				expressionPO.setThreshold(String.join((CharSequence) ",", expressionModel.getThreshold()));
			}
			if (Strings.isNullOrEmpty((String) expressionPO.getFieldType())) {
				FlowVariableDTO variableDTO = this.flowVarService.query(appId, expressionPO.getField());
				if (variableDTO == null) {
					variableDTO = this.flowVarService.query(expressionPO.getField());
				}
				if (variableDTO != null) {
					expressionPO.setFieldType(variableDTO.getType());
				}
			}
			expressionPO.setId(null);
			this.expressionRepository.save(expressionPO);
		});
	}

	private void savingFlowNodeVariables(NodePO nodePO, List<FlowNodeVariableModel> variableModels) {
		if (CollectionUtils.isEmpty(variableModels)) {
			return;
		}
		variableModels.forEach(variable -> {
			FlowNodeVariableDTO dto = ObjectUtils.convertNotNull(variable, FlowNodeVariableDTO.class);
			dto.setNodeId(nodePO.getId());
			dto.setActive(true);
			List<FlowPO> flowPOS = this.flowRepository.findByCode(nodePO.getTaskContent());
			if (CollectionUtils.isEmpty(flowPOS)) {
				return;
			}
			dto.setSubFlowId(flowPOS.get(0).getId());
			dto.setId(null);
			dto = this.flowNodeVariableService.save(dto);
			log.info("saved flow-node-variable {} for node {}",  dto.toString(),  nodePO.getId());
		});
	}

	private void savingGenericAction(NodePO nodePO, ActionTemplateModel actionModel) {
		if (actionModel.getGenericTemplateId() > 0L) {
			nodePO.setActionTemplateId(actionModel.getGenericTemplateId());
			nodePO.setTaskType(TaskType.GENERIC_ACTION);
			return;
		}
		String action = actionModel.getAction();
		String actionType = actionModel.getActionType();
		if (StringUtils.isNotEmpty((CharSequence) action) && StringUtils.isNotEmpty((CharSequence) actionType)) {
			ActionTemplatePO actionTemplatePO = this.actionTemplateRepository.findFirstByActionAndActionType(action,
					actionType);
			if (actionTemplatePO == null) {
				actionTemplatePO = new ActionTemplatePO();
				actionTemplatePO.setAction(action);
				actionTemplatePO.setActionType(actionType);
				this.actionTemplateRepository.save(actionTemplatePO);
			}
			nodePO.setActionTemplateId(actionTemplatePO.getId());
			nodePO.setTaskType(TaskType.HTTP);
			if (!Strings.isNullOrEmpty((String) actionModel.getTargetUrl())) {
				nodePO.setTaskContent(actionModel.getTargetUrl());
			}
		}
	}

	public ApiResponse<FlowGraphModel> get(@PathVariable(value = "id") Long id) {
		Optional flowOpt = this.flowRepository.findById(id);
		if (!flowOpt.isPresent()) {
			return ApiResponse.error((String) "\u6d41\u7a0b\u4e0d\u5b58\u5728");
		}
		return ApiResponse.success( this.buildFlowGraph((FlowPO) flowOpt.get()));
	}

	public ApiResponse<EsFlowTaskInstanceModel> sum(String code, String version, String startTime, String endTime,
			List<String> tags, List<Long> loanIds, String storyCode) {
		if (StringUtils.isBlank((CharSequence) code)) {
			return ApiResponse.error((String) "Flow Code can not be blank.");
		}
		Long flowId = null;
		List<NodePO> nodePOList = new ArrayList();
		if (StringUtils.isNotBlank((CharSequence) version)) {
			FlowPO flowPO = this.flowRepository.findByCodeAndVersionAndActiveIsTrue(code, version);
			if (flowPO == null) {
				return ApiResponse.error((String) String.format("Not found flow by %s %s", code, version));
			}
			List<FlowUnionNodePO> flowUnionNodePOList = this.flowUnionNodeRepository.findByFlowId(flowPO.getId());
			if (CollectionUtils.isEmpty(flowUnionNodePOList)) {
				return ApiResponse
						.error((String) String.format("Not found node by %s %s %s", flowPO.getId(), code, version));
			}
			nodePOList = this.nodeRepository.findAllById(
					flowUnionNodePOList.stream().map(FlowUnionNodePO::getNodeId).collect(Collectors.toSet()));
			flowId = flowPO.getId();
		} else {
			List<FlowPO> flowPOList = this.flowRepository.findByCode(code);
			if (CollectionUtils.isEmpty(flowPOList)) {
				return ApiResponse.error((String) String.format("Not found flow by %s %s", code, version));
			}
			List<Long> flowIdList = flowPOList.stream().map(BasePO::getId).collect(Collectors.toList());
			List<FlowUnionNodePO> flowUnionNodePOList = this.flowUnionNodeRepository.findAllByFlowIdIn(flowIdList);
			if (CollectionUtils.isEmpty(flowUnionNodePOList)) {
				return ApiResponse.error((String) String.format("Not found node by %s ", code));
			}
			nodePOList = this.nodeRepository.findAllById(
					flowUnionNodePOList.stream().map(FlowUnionNodePO::getNodeId).collect(Collectors.toSet()));
		}
		List<Long> nodeIdList = nodePOList.stream().map(BasePO::getId).collect(Collectors.toList());
		EsFlowTaskInstanceQuery query = new EsFlowTaskInstanceQuery();
		query.setFlowCode(code);
		query.setFlowVersion(version);
		query.setFlowId(flowId);
		query.setNodeIdList(nodeIdList);
		query.setTags(tags);
		query.setLoanIdList(loanIds);
		if (StringUtils.isNotBlank((CharSequence) startTime)) {
			query.setStartDate(DateTime.parse((String) startTime,
					(DateTimeFormatter) DateTimeFormat.forPattern((String) "yyyy-MM-dd HH:ss:mm")).toDate());
		}
		if (StringUtils.isNotBlank((CharSequence) endTime)) {
			query.setEndDate(DateTime.parse((String) endTime,
					(DateTimeFormatter) DateTimeFormat.forPattern((String) "yyyy-MM-dd HH:ss:mm")).toDate());
		}
		if (StringUtils.isNotBlank((CharSequence) storyCode)) {
			List<FlowSubjectDTO> subjectDTOS = this.flowSubjectService.findByFlowCode(storyCode);
			if (!CollectionUtils.isEmpty(subjectDTOS)) {
				List<EventModel> eventModelList = subjectDTOS.stream()
						.map(flowSubjectDTO -> new EventModel(flowSubjectDTO.getSubjectCode(),
								flowSubjectDTO.getSubjectName()))
						.collect(Collectors.toList());
				query.setEventList(eventModelList);
			}
			query.setStoryCode(storyCode);
		}
		EsFlowTaskInstanceModel result = this.esQueryFlowTaskInstanceService.sum(query);
		return ApiResponse.success( result);
	}

	public ApiResponse<Map<String, Object>> nodeSum(String code, String version, String nodeId, String actionCode,
			String startTime, String endTime, List<String> tags) {
		if (StringUtils.isBlank((CharSequence) code)) {
			return ApiResponse.error((String) "flow code can not be blank.");
		}
		if (StringUtils.isBlank((CharSequence) nodeId)) {
			return ApiResponse.error((String) "nodeId can not be blank.");
		}
		if (StringUtils.isBlank((CharSequence) actionCode)) {
			return ApiResponse.error((String) "actionCode can not be blank.");
		}
		HashMap<String, String> pathVariables = new HashMap<String, String>();
		pathVariables.put("actionCode", actionCode);
		if (CollectionUtils.isEmpty(tags)) {
			tags = new ArrayList<String>();
		}
		if (StringUtils.isNotBlank((CharSequence) code)) {
			tags.add(code);
		}
		if (StringUtils.isNotBlank((CharSequence) version)) {
			tags.add(version);
		}
		nodeId = nodeId.replace("node_", "");
		tags.add(nodeId);
		HashMap<String, Object> params = new HashMap<String, Object>();
		params.put("tags", tags);
		params.put("begin", startTime);
		params.put("end", endTime);
		String response = HttpUtil.get(this.alitarSumUrl, null, pathVariables, params);
		log.info("alitar sum result:{}",  response);
		if (StringUtils.isBlank((CharSequence) response)) {
			return ApiResponse.error((String) "alitar\u83b7\u53d6\u6570\u636e\u5f02\u5e38");
		}
		Map<String, Object> result = ObjectMappers.mustReadValue(response, new TypeReference<Map<String, Object>>() {
		});
		return ApiResponse.success(result);
	}

	public ApiResponse<List<EsFlowTaskInstanceListModel>> listEsData(String code, String version, String startTime,
			String endTime, Long flowId, List<Long> nodeIds, List<String> tags, List<Long> loanIds) {
		EsFlowTaskInstanceQuery query = new EsFlowTaskInstanceQuery();
		query.setFlowCode(code);
		query.setFlowVersion(version);
		query.setFlowId(flowId);
		query.setNodeIdList(nodeIds);
		query.setTags(tags);
		query.setLoanIdList(loanIds);
		if (StringUtils.isNotBlank((CharSequence) startTime)) {
			query.setStartDate(DateTime.parse((String) startTime,
					(DateTimeFormatter) DateTimeFormat.forPattern((String) "yyyy-MM-dd HH:ss:mm")).toDate());
		}
		if (StringUtils.isNotBlank((CharSequence) endTime)) {
			query.setEndDate(DateTime.parse((String) endTime,
					(DateTimeFormatter) DateTimeFormat.forPattern((String) "yyyy-MM-dd HH:ss:mm")).toDate());
		}
		List<EsFlowTaskInstanceListModel> result = this.esQueryFlowTaskInstanceService.list(query);
		return ApiResponse.success(result);
	}

	public ApiResponse<FlowGraphModel> get(String code, String version) {
		FlowPO flowPO;
		BooleanExpression expression = QFlowPO.flowPO.code.eq( code);
		if (!Strings.isNullOrEmpty((String) version)) {
			expression = expression.and((Predicate) QFlowPO.flowPO.version.eq( version));
		}
		if ((flowPO = (FlowPO) ((JPAQuery) ((JPAQuery) this.queryFactory.selectFrom((EntityPath) QFlowPO.flowPO)
				.where((Predicate) expression)).orderBy(QFlowPO.flowPO.version.desc())).fetchFirst()) == null) {
			return ApiResponse.error((String) String.format("Not found %s %s", code, version));
		}
		return ApiResponse.success( this.buildFlowGraph(flowPO));
	}

	public ApiResponse<FlowVersionModel> versions(Long appId, String code) {
		if (Strings.isNullOrEmpty((String) code)) {
			return ApiResponse.error((String) "Invalid flow code");
		}
		BooleanExpression expression = QFlowPO.flowPO.code.eq( code);
		if (appId != null && appId > 0L) {
			expression = expression.and(QFlowPO.flowPO.appId.eq( appId));
		}
		List<FlowPO> flows = this.queryFactory.selectFrom(QFlowPO.flowPO).where(expression).fetch();
		FlowVersionModel resultModel = new FlowVersionModel();
		if (CollectionUtils.isEmpty(flows)) {
			return ApiResponse.success( resultModel);
		}
		List models = flows.stream().map(po -> {
			FlowVersionModel.VersionModel version = ObjectUtils.convertNotNull(po, FlowVersionModel.VersionModel.class);
			ApiResponse<FlowGraphModel> response = this.get(version.getId());
			if (response.success()) {
				version.setGraph((FlowGraphModel) response.getData());
			}
			return version;
		}).filter(version -> Objects.nonNull(version.getGraph())).collect(Collectors.toList());
		resultModel.setVersions(models);
		return ApiResponse.success( resultModel);
	}

	private FlowGraphModel buildFlowGraph(FlowPO flowPO) {
		FlowGraphModel model = new FlowGraphModel();
		BeanUtils.copyProperties( flowPO,  model);
		List<NodeModel> nodeModels = this.fetchNodes(flowPO.getId());
		this.fillFlowNodeVariables(nodeModels);
		List edgeModels = this.edgeRepository.findByFlowId(flowPO.getId()).stream().map(this::convert)
				.collect(Collectors.toList());
		model.setEdges(edgeModels);
		model.setNodes(nodeModels);
		model.setVersions(this.graphService.versions(model.getCode()));
		return model;
	}

	private List<NodeModel> fetchNodes(long flowId) {
		List<FlowUnionNodePO> unions = this.flowUnionNodeRepository.findByFlowId(flowId);
		if (CollectionUtils.isEmpty(unions)) {
			return Lists.newArrayList();
		}
		return unions.stream().map(union -> this.nodeRepository.findById(union.getNodeId()).orElse(null))
				.filter(Objects::nonNull).map(this::convert).collect(Collectors.toList());
	}

	private void fillFlowNodeVariables(List<NodeModel> nodeModels) {
		if (CollectionUtils.isEmpty(nodeModels)) {
			return;
		}
		nodeModels.forEach(nodeModel -> {
			if (Strings.isNullOrEmpty((String) nodeModel.getId())) {
				return;
			}
			String nodeId = nodeModel.getId().replace("node_", "");
			if (Strings.isNullOrEmpty((String) nodeId)) {
				return;
			}
			List<FlowNodeVariableDTO> variables = this.flowNodeVariableService
					.query(new FlowNodeVariableRequest().setNodeIds(Collections.singletonList(Long.valueOf(nodeId))));
			if (!CollectionUtils.isEmpty(variables)) {
				variables.forEach(variable -> nodeModel
						.addFlowNodeVariableModel(ObjectUtils.convertNotNull(variable, FlowNodeVariableModel.class)));
			}
		});
	}

	private NodeModel convert(NodePO nodePO) {
		NodeModel model = new NodeModel();
		BeanUtils.copyProperties( nodePO,  model);
		model.setLabel(nodePO.getName());
		model.setId("node_" + nodePO.getId());
		this.fillAction(model, nodePO);
		return model;
	}

	private void fillAction(NodeModel model, NodePO po) {
		String scheduleContent;
		ActionTemplatePO actionTemplatePO;
		if (po.getTaskType() == TaskType.GENERIC_ACTION && po.getActionTemplateId() != null
				&& po.getActionTemplateId() > 0L) {
			ActionTemplateModel action = new ActionTemplateModel();
			action.setGenericTemplateId(po.getActionTemplateId().longValue());
			GenericTemplateDto template = this.genericTemplateService.query(po.getActionTemplateId());
			if (template != null) {
				action.setGenericActionId(template.getActionId().longValue());
			}
			model.setAction(action);
		} else if (po.getActionTemplateId() != null
				&& (actionTemplatePO = (ActionTemplatePO) this.actionTemplateRepository
						.findById(po.getActionTemplateId()).orElse(null)) != null) {
			ActionTemplateModel actionTemplateModel = new ActionTemplateModel();
			BeanUtils.copyProperties( actionTemplatePO,  actionTemplateModel);
			actionTemplateModel.setTargetUrl(po.getTaskContent());
			model.setAction(actionTemplateModel);
		}
		if (po.getTaskType() == TaskType.START_SUB_FLOW) {
			model.setSubflowCode(po.getTaskContent());
		}
		if (StringUtils.isNotEmpty((CharSequence) (scheduleContent = po.getScheduleContent()))) {
			DelayModel delayModel = ObjectMappers.mustReadValue(scheduleContent, DelayModel.class);
			ScheduleType scheduleType = po.getScheduleType();
			if (scheduleType != null) {
				delayModel.setType(scheduleType.name());
			}
			delayModel.setFlag(true);
			model.setDelay(delayModel);
		}
	}

	private EdgeModel convert(EdgePO po) {
		EdgeModel model = new EdgeModel();
		model.setId("edge_" + po.getId());
		model.setSource("node_" + po.getFromId());
		model.setTarget("node_" + po.getToId());
		List expressionModels = this.expressionRepository.findByEdgeId(po.getId()).stream().filter(Objects::nonNull)
				.map(e -> {
					ExpressionModel expression = ObjectUtils.convertNotNull(e, ExpressionModel.class);
					expression.setOperator(e.getOperator().name());
					if (Strings.isNullOrEmpty((String) e.getThreshold())) {
						return expression;
					}
					if (MULTIPLE_OPTIONS.contains(e.getOperator())) {
						expression.setThreshold(
								ObjectMappers.mustReadValue(e.getThreshold(), new TypeReference<List<String>>() {
								}));
					} else if (expression.getField().equals("intervalVal")) {
						expression
								.setThreshold((List) Lists.newArrayList((Object[]) new String[] { e.getThreshold() }));
					} else {
						expression.setThreshold((List) Lists.newArrayList((Object[]) e.getThreshold().split(",")));
					}
					return expression;
				}).collect(Collectors.toList());
		model.setExpressions(expressionModels);
		return model;
	}

	public ApiResponse<List<ComboBox<Long>>> getCombo(Long id) {
		if (Objects.isNull(id)) {
			return ApiResponse.error((String) "flow id must not be null");
		}
		List<FlowUnionNodePO> unions = this.flowUnionNodeRepository.findByFlowId(id);
		if (CollectionUtils.isEmpty(unions)) {
			return ApiResponse.success( Lists.newArrayList());
		}
		return ApiResponse.success(unions.stream()
				.map(union -> this.nodeRepository.findById(union.getNodeId()).orElse(null)).filter(Objects::nonNull)
				.map(node -> ComboBox.of((String) node.getName(),  node.getId())).collect(Collectors.toList()));
	}

	public ApiResponse<List<ComboBox<Long>>> getCombo(String bu, String app, String flow) {
		Optional<FlowPO> flowOpt = this.flowRepository.findByBuAppCode(bu, app, flow);
		if (!flowOpt.isPresent()) {
			return ApiResponse.error((String) "flow not exists");
		}
		return this.getCombo(flowOpt.get().getId());
	}

	public ApiResponse<List<ComboBox<String>>> getDelayTypeCombo() {
		return ApiResponse.success(DELAY_TYPE_COMBO);
	}

	public ApiResponse<List<ComboBox<String>>> getDelayUnitCombo() {
		return ApiResponse.success(DELAY_UNIT_COMBO);
	}

	public ApiResponse listFlowStatus() {
		List<ComboBox> status = new ArrayList<ComboBox>(FlowStatus.values().length);
		for (FlowStatus s : FlowStatus.values()) {
			status.add(ComboBox.of((String) s.getDesc(),  s.name()));
		}
		return ApiResponse.success(status);
	}

	@GetMapping(value = { "/refresh/{id}" })
	public ApiResponse<Void> refresh(@PathVariable(value = "id") Long id) {
		try {
			this.graphService.refreshFlowGraph(id);
			return ApiResponse.success(null);
		} catch (Exception e) {
			log.error("refresh flow failed, id : {}",  id,  e);
			return ApiResponse.error((String) e.getMessage());
		}
	}

	@GetMapping(value = { "/convert/node/action" })
	public void convert() {
		Map configs = this.actionConfigRepository.findAll().stream()
				.collect(Collectors.toMap(ActionConfigPO::getContent, Function.identity()));
		Map<String, Long> variables = this.variableConfigRepository.findAll().stream()
				.collect(Collectors.toMap(VariableConfigPO::getCode, BasePO::getId));
		this.nodeRepository.findAll().stream().filter(n -> StringUtils.isNotBlank((CharSequence) n.getTaskContent()))
				.forEach(n -> this.addAction((NodePO) n, configs, variables));
	}

	private void addAction(NodePO node, Map<String, ActionConfigPO> configs, Map<String, Long> variables) {
		Long actionConfigId = Optional.ofNullable(configs.get(node.getTaskContent())).map(BasePO::getId).orElse(null);
		if (node.getTaskType() == TaskType.START_SUB_FLOW) {
			actionConfigId = Optional.ofNullable(configs.get("standardFlow")).map(BasePO::getId).orElse(null);
		}
		if (Objects.isNull(actionConfigId)) {
			log.error("action config not found for node : {}",  node);
			return;
		}
		NodeActionPO actionPO = new NodeActionPO();
		actionPO.setActionConfigId(actionConfigId);
		actionPO.setNodeId(node.getId());
		actionPO.setTimeout(node.getTaskTimeout());
		actionPO.setType(node.getTaskType());
		actionPO.setContent(node.getTaskContent());
		if (node.getTaskType() == TaskType.START_SUB_FLOW) {
			actionPO.setContent("standardFlow");
		}
		actionPO = (NodeActionPO) this.actionRepository.save(actionPO);
		long actionId = actionPO.getId();
		if (node.getTaskType() == TaskType.START_SUB_FLOW) {
			FormPO form = new FormPO();
			form.setActionId(actionPO.getId());
			form.setVariableConfigId(7L);
			form.setField("standardFlow");
			form.setFieldType(ThresholdType.STRING.name());
			form.setNodeId(node.getId());
			form.setType(FormType.INPUT);
			form.setValue(node.getTaskContent());
			this.formRepository.save(form);
		}
		this.formRepository.findByNodeId(node.getId()).forEach(f -> this.updateForm((FormPO) f, actionId, variables));
	}

	private void updateForm(FormPO form, Long actionId, Map<String, Long> variables) {
		form.setVariableConfigId(variables.get(form.getField()));
		form.setActionId(actionId);
		this.formRepository.save(form);
	}

	@GetMapping(value = { "/expire/cache" })
	public void expireCache() {
		this.instanceRepository.loadAllUuid().forEach(this.instanceContextService::removeCache);
	}

	private static class Detector {
		private final boolean success;
		private final String message;

		private Detector(boolean success, String message) {
			this.success = success;
			this.message = message;
		}

		public Detector() {
			this(true, "ok");
		}

		public Detector(String message) {
			this(false, message);
		}

		public boolean isSuccess() {
			return this.success;
		}

		public String getMessage() {
			return this.message;
		}
	}
}
