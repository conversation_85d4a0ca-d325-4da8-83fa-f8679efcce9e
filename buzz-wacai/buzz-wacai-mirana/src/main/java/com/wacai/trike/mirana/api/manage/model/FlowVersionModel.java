/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.api.manage.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.FlowType;
import com.wacai.trike.mirana.api.manage.model.FlowGraphModel;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class FlowVersionModel
implements Serializable {
    private List<VersionModel> versions;

    public List<VersionModel> getVersions() {
        return this.versions;
    }

    public void setVersions(List<VersionModel> versions) {
        this.versions = versions;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowVersionModel)) {
            return false;
        }
        FlowVersionModel other = (FlowVersionModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        List<VersionModel> this$versions = this.getVersions();
        List<VersionModel> other$versions = other.getVersions();
        return !(this$versions == null ? other$versions != null : !(this$versions).equals(other$versions));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowVersionModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        List<VersionModel> $versions = this.getVersions();
        result = result * 59 + ($versions == null ? 43 : ($versions).hashCode());
        return result;
    }

    public String toString() {
        return "FlowVersionModel(versions=" + this.getVersions() + ")";
    }

    @JsonInclude(value=JsonInclude.Include.NON_NULL)
    public static class VersionModel
    implements Serializable {
        private long id;
        private long appId;
        private String code;
        private String name;
        private String version;
        private FlowStatus status;
        private FlowType type;
        private LocalDateTime updatedTime;
        private String updatedBy;
        private FlowGraphModel graph;

        public long getId() {
            return this.id;
        }

        public long getAppId() {
            return this.appId;
        }

        public String getCode() {
            return this.code;
        }

        public String getName() {
            return this.name;
        }

        public String getVersion() {
            return this.version;
        }

        public FlowStatus getStatus() {
            return this.status;
        }

        public FlowType getType() {
            return this.type;
        }

        public LocalDateTime getUpdatedTime() {
            return this.updatedTime;
        }

        public String getUpdatedBy() {
            return this.updatedBy;
        }

        public FlowGraphModel getGraph() {
            return this.graph;
        }

        public void setId(long id) {
            this.id = id;
        }

        public void setAppId(long appId) {
            this.appId = appId;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public void setName(String name) {
            this.name = name;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public void setStatus(FlowStatus status) {
            this.status = status;
        }

        public void setType(FlowType type) {
            this.type = type;
        }

        public void setUpdatedTime(LocalDateTime updatedTime) {
            this.updatedTime = updatedTime;
        }

        public void setUpdatedBy(String updatedBy) {
            this.updatedBy = updatedBy;
        }

        public void setGraph(FlowGraphModel graph) {
            this.graph = graph;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof VersionModel)) {
                return false;
            }
            VersionModel other = (VersionModel)o;
            if (!other.canEqual(this)) {
                return false;
            }
            if (this.getId() != other.getId()) {
                return false;
            }
            if (this.getAppId() != other.getAppId()) {
                return false;
            }
            String this$code = this.getCode();
            String other$code = other.getCode();
            if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
                return false;
            }
            String this$name = this.getName();
            String other$name = other.getName();
            if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
                return false;
            }
            String this$version = this.getVersion();
            String other$version = other.getVersion();
            if (this$version == null ? other$version != null : !this$version.equals(other$version)) {
                return false;
            }
            FlowStatus this$status = this.getStatus();
            FlowStatus other$status = other.getStatus();
            if (this$status == null ? other$status != null : !((this$status)).equals(other$status)) {
                return false;
            }
            FlowType this$type = this.getType();
            FlowType other$type = other.getType();
            if (this$type == null ? other$type != null : !((this$type)).equals(other$type)) {
                return false;
            }
            LocalDateTime this$updatedTime = this.getUpdatedTime();
            LocalDateTime other$updatedTime = other.getUpdatedTime();
            if (this$updatedTime == null ? other$updatedTime != null : !(this$updatedTime).equals(other$updatedTime)) {
                return false;
            }
            String this$updatedBy = this.getUpdatedBy();
            String other$updatedBy = other.getUpdatedBy();
            if (this$updatedBy == null ? other$updatedBy != null : !this$updatedBy.equals(other$updatedBy)) {
                return false;
            }
            FlowGraphModel this$graph = this.getGraph();
            FlowGraphModel other$graph = other.getGraph();
            return !(this$graph == null ? other$graph != null : !(this$graph).equals(other$graph));
        }

        protected boolean canEqual(Object other) {
            return other instanceof VersionModel;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            long $id = this.getId();
            result = result * 59 + (int)($id >>> 32 ^ $id);
            long $appId = this.getAppId();
            result = result * 59 + (int)($appId >>> 32 ^ $appId);
            String $code = this.getCode();
            result = result * 59 + ($code == null ? 43 : $code.hashCode());
            String $name = this.getName();
            result = result * 59 + ($name == null ? 43 : $name.hashCode());
            String $version = this.getVersion();
            result = result * 59 + ($version == null ? 43 : $version.hashCode());
            FlowStatus $status = this.getStatus();
            result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
            FlowType $type = this.getType();
            result = result * 59 + ($type == null ? 43 : (($type)).hashCode());
            LocalDateTime $updatedTime = this.getUpdatedTime();
            result = result * 59 + ($updatedTime == null ? 43 : ($updatedTime).hashCode());
            String $updatedBy = this.getUpdatedBy();
            result = result * 59 + ($updatedBy == null ? 43 : $updatedBy.hashCode());
            FlowGraphModel $graph = this.getGraph();
            result = result * 59 + ($graph == null ? 43 : ($graph).hashCode());
            return result;
        }

        public String toString() {
            return "FlowVersionModel.VersionModel(id=" + this.getId() + ", appId=" + this.getAppId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", version=" + this.getVersion() + ", status=" + (this.getStatus()) + ", type=" + (this.getType()) + ", updatedTime=" + this.getUpdatedTime() + ", updatedBy=" + this.getUpdatedBy() + ", graph=" + this.getGraph() + ")";
        }
    }
}

