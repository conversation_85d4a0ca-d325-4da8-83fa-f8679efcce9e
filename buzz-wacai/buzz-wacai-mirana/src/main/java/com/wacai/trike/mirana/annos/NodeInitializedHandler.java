/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.annos;

import java.time.LocalDateTime;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.TaskInstance;
import com.wacai.trike.mirana.event.EdgeProcessedEvent;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.InstanceInitEvent;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.metrics.MetricsProvider;
import com.wacai.trike.mirana.metrics.NodeMetrics;
import com.wacai.trike.mirana.notify.NotifyService;
import com.wacai.trike.mirana.task.TaskExecuteException;
import com.wacai.trike.mirana.task.TaskStatus;
import com.wacai.trike.mirana.util.DistributeSequenceIdGenerator;
import com.wacai.trike.mirana.util.ObjectMappers;

@Component
public class NodeInitializedHandler implements MethodInvocationHandler {
    private static final Logger log = LoggerFactory.getLogger(NodeInitializedHandler.class);
    @Autowired
    private FlowGraphService graphService;
    @Autowired
    private DistributeSequenceIdGenerator idGenerator;
    @Autowired
    private NotifyService notifyService;
    @Autowired
    private MetricsProvider metricsProvider;

    @Override
    public int order() {
        return 0;
    }

    @Override
    public void preHandle(MethodInvocation invocation) {
        if (!this.sanity(invocation)) {
            return;
        }
        EventExchange exchange = (EventExchange)invocation.getArgs()[0];
        InstanceContext context = exchange.getContext();
        Node node = this.graphService.getFlowGraph(context.getFlowId()).getNode(context.getCurrentNodeId());
        if (node == null) {
            throw new TaskExecuteException(String.format("not found node %d of flow %d", context.getCurrentNodeId(), context.getFlowId()));
        }
        TaskInstance instance = new TaskInstance().setNodeId(context.getCurrentNodeId()).setUuid(this.idGenerator.nextID()).setStatus(TaskStatus.RUNNING).setStartTime(LocalDateTime.now()).setDirty(true);
        context.setCurrentTaskInstance(instance);
        this.notifyService.notifyNodeIn(context.copy(), node);
        this.metricsProvider.metricsNode(NodeMetrics.build(context, node));
        log.info("init node {}-{} of instance {}", new Object[]{node.getName(), node.getId(), context.getUuid()});
    }

    private boolean sanity(MethodInvocation invocation) {
        Object[] args = invocation.getArgs();
        if (args == null || args.length != 1 || !(args[0] instanceof EventExchange)) {
            return false;
        }
        EventExchange exchange = (EventExchange)args[0];
        if (exchange.getCurrent() == null || exchange.getContext() == null) {
            return false;
        }
        if (!(exchange.getCurrent() instanceof InstanceInitEvent) && !(exchange.getCurrent() instanceof EdgeProcessedEvent)) {
            log.info("Non-Expected InstanceInitEvent/EdgeProcessedEvent {}", ObjectMappers.mustWriteValue(args[0]));
            return false;
        }
        return true;
    }
}

