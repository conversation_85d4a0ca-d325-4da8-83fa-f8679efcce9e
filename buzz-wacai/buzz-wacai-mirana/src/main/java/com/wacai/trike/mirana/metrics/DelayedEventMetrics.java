/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.model.FlowProcessRequest
 *  com.wacai.trike.mirana.api.model.FlowStartRequest
 */
package com.wacai.trike.mirana.metrics;

import com.wacai.trike.mirana.api.model.FlowProcessRequest;
import com.wacai.trike.mirana.api.model.FlowStartRequest;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import java.time.LocalDateTime;

public class DelayedEventMetrics {
    private Category category;
    private String uuid;
    private String instanceUuid;
    private String taskInstanceUuid;
    private String expectFireTime;
    private String operator;

    DelayedEventMetricsBuilder toBuilder() {
        return new DelayedEventMetricsBuilder().category(this.getCategory()).uuid(this.getUuid()).instanceUuid(this.getInstanceUuid()).taskInstanceUuid(this.getTaskInstanceUuid()).expectFireTime(this.getExpectFireTime()).operator(this.getOperator());
    }

    public static DelayedEventMetrics build(AbstractFlowEvent event, LocalDateTime expectTime, Category category) {
        return new DelayedEventMetricsBuilder().uuid(event.getUuid()).instanceUuid(event.getInstanceUuid()).taskInstanceUuid(event.getTaskId()).expectFireTime(expectTime != null ? expectTime.toString() : LocalDateTime.now().toString()).operator(event.getOperator()).category(category).build();
    }

    public static DelayedEventMetrics build(FlowStartRequest request, LocalDateTime expectTime, Category category) {
        return new DelayedEventMetricsBuilder().uuid(request.getUuid()).instanceUuid(request.getUuid()).expectFireTime(expectTime != null ? expectTime.toString() : LocalDateTime.now().toString()).operator(request.getOperator()).category(category).build();
    }

    public static DelayedEventMetrics build(FlowProcessRequest request, LocalDateTime expectTime, Category category) {
        return new DelayedEventMetricsBuilder().uuid(request.getTaskInstanceId()).instanceUuid(request.getInstanceId()).expectFireTime(expectTime != null ? expectTime.toString() : LocalDateTime.now().toString()).operator(request.getOperator()).category(category).build();
    }

    DelayedEventMetrics(Category category, String uuid, String instanceUuid, String taskInstanceUuid, String expectFireTime, String operator) {
        this.category = category;
        this.uuid = uuid;
        this.instanceUuid = instanceUuid;
        this.taskInstanceUuid = taskInstanceUuid;
        this.expectFireTime = expectFireTime;
        this.operator = operator;
    }

    public static DelayedEventMetricsBuilder builder() {
        return new DelayedEventMetricsBuilder();
    }

    public Category getCategory() {
        return this.category;
    }

    public String getUuid() {
        return this.uuid;
    }

    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    public String getTaskInstanceUuid() {
        return this.taskInstanceUuid;
    }

    public String getExpectFireTime() {
        return this.expectFireTime;
    }

    public String getOperator() {
        return this.operator;
    }

    public static class DelayedEventMetricsBuilder {
        private Category category;
        private String uuid;
        private String instanceUuid;
        private String taskInstanceUuid;
        private String expectFireTime;
        private String operator;

        DelayedEventMetricsBuilder() {
        }

        public DelayedEventMetricsBuilder category(Category category) {
            this.category = category;
            return this;
        }

        public DelayedEventMetricsBuilder uuid(String uuid) {
            this.uuid = uuid;
            return this;
        }

        public DelayedEventMetricsBuilder instanceUuid(String instanceUuid) {
            this.instanceUuid = instanceUuid;
            return this;
        }

        public DelayedEventMetricsBuilder taskInstanceUuid(String taskInstanceUuid) {
            this.taskInstanceUuid = taskInstanceUuid;
            return this;
        }

        public DelayedEventMetricsBuilder expectFireTime(String expectFireTime) {
            this.expectFireTime = expectFireTime;
            return this;
        }

        public DelayedEventMetricsBuilder operator(String operator) {
            this.operator = operator;
            return this;
        }

        public DelayedEventMetrics build() {
            return new DelayedEventMetrics(this.category, this.uuid, this.instanceUuid, this.taskInstanceUuid, this.expectFireTime, this.operator);
        }

        public String toString() {
            return "DelayedEventMetrics.DelayedEventMetricsBuilder(category=" + (this.category) + ", uuid=" + this.uuid + ", instanceUuid=" + this.instanceUuid + ", taskInstanceUuid=" + this.taskInstanceUuid + ", expectFireTime=" + this.expectFireTime + ", operator=" + this.operator + ")";
        }
    }

    public static enum Category {
        PUBLISHED,
        SUBSCRIBED,
        COUNTERVAILED;

    }
}

