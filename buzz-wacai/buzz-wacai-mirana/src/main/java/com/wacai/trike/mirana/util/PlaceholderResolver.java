/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.util;

import java.util.Map;
import java.util.function.Function;

public class PlaceholderResolver {
    private static final String DEFAULT_PLACEHOLDER_PREFIX = "{";
    private static final String DEFAULT_PLACEHOLDER_SUFFIX = "}";
    private static PlaceholderResolver defaultResolver = new PlaceholderResolver();
    private String placeholderPrefix = "{";
    private String placeholderSuffix = "}";

    private PlaceholderResolver() {
    }

    private PlaceholderResolver(String placeholderPrefix, String placeholderSuffix) {
        this.placeholderPrefix = placeholderPrefix;
        this.placeholderSuffix = placeholderSuffix;
    }

    public static PlaceholderResolver get() {
        return defaultResolver;
    }

    public boolean placeholder(String content) {
        return content.contains(this.placeholderPrefix);
    }

    public String resolve(String content, String ... values) {
        int start = content.indexOf(this.placeholderPrefix);
        if (start == -1) {
            return content;
        }
        int valueIndex = 0;
        StringBuilder result = new StringBuilder(content);
        while (start != -1) {
            int end = result.indexOf(this.placeholderSuffix);
            String replaceContent = values[valueIndex++];
            result.replace(start, end + this.placeholderSuffix.length(), replaceContent);
            start = result.indexOf(this.placeholderPrefix, start + replaceContent.length());
        }
        return result.toString();
    }

    public String resolveByRule(String content, Function<String, String> rule) {
        int start = content.indexOf(this.placeholderPrefix);
        if (start == -1) {
            return content;
        }
        StringBuilder result = new StringBuilder(content);
        while (start != -1) {
            int end = result.indexOf(this.placeholderSuffix, start);
            String placeholder = result.substring(start + this.placeholderPrefix.length(), end);
            String replaceContent = placeholder.trim().isEmpty() ? "" : rule.apply(placeholder);
            result.replace(start, end + this.placeholderSuffix.length(), replaceContent);
            start = result.indexOf(this.placeholderPrefix, start + replaceContent.length());
        }
        return result.toString();
    }

    public String resolveByMap(String content, Map<String, Object> valueMap) {
        return this.resolveByRule(content, placeholderValue -> String.valueOf(valueMap.get(placeholderValue)));
    }
}

