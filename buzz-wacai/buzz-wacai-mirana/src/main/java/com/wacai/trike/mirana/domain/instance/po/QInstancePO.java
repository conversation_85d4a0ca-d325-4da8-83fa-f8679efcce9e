/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 */
package com.wacai.trike.mirana.domain.instance.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.instance.po.InstancePO;
import java.time.LocalDateTime;

public class QInstancePO
extends EntityPathBase<InstancePO> {
    private static final long serialVersionUID = 1298100615L;
    public static final QInstancePO instancePO = new QInstancePO("instancePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final BooleanPath async;
    public final StringPath bzKey;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> currentNodeId;
    public final BooleanPath esSync;
    public final NumberPath<Long> flowId;
    public final NumberPath<Long> id;
    public final StringPath parentUuid;
    public final EnumPath<InstanceStatus> status;
    public final DateTimePath<LocalDateTime> timeout;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final StringPath uuid;

    public QInstancePO(String variable) {
        super(InstancePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.async = this.createBoolean("async");
        this.bzKey = this.createString("bzKey");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.currentNodeId = this.createNumber("currentNodeId", Long.class);
        this.esSync = this.createBoolean("esSync");
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.parentUuid = this.createString("parentUuid");
        this.status = this.createEnum("status", InstanceStatus.class);
        this.timeout = this.createDateTime("timeout", LocalDateTime.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }

    public QInstancePO(Path<? extends InstancePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.async = this.createBoolean("async");
        this.bzKey = this.createString("bzKey");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.currentNodeId = this.createNumber("currentNodeId", Long.class);
        this.esSync = this.createBoolean("esSync");
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.parentUuid = this.createString("parentUuid");
        this.status = this.createEnum("status", InstanceStatus.class);
        this.timeout = this.createDateTime("timeout", LocalDateTime.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }

    public QInstancePO(PathMetadata metadata) {
        super(InstancePO.class, metadata);
        this.active = this._super.active;
        this.async = this.createBoolean("async");
        this.bzKey = this.createString("bzKey");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.currentNodeId = this.createNumber("currentNodeId", Long.class);
        this.esSync = this.createBoolean("esSync");
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.parentUuid = this.createString("parentUuid");
        this.status = this.createEnum("status", InstanceStatus.class);
        this.timeout = this.createDateTime("timeout", LocalDateTime.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }
}

