/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.annos.NodeInitializedHandler;
import com.wacai.trike.mirana.common.enums.VariableType;

@Component
public class EdgeProcessedEventListener implements EventListener<EdgeProcessedEvent, NodeInitEvent> {

    @Override
    public Class<EdgeProcessedEvent> accept() {
        return EdgeProcessedEvent.class;
    }

    @Override
    public boolean needProcess(EventExchange<EdgeProcessedEvent, NodeInitEvent> exchange) {
        return EventListener.super.checkTask(exchange);
    }

    @Override
    @Barrier(handlers={NodeInitializedHandler.class, InstanceContextSyncHandler.class})
    public void doProcess(EventExchange<EdgeProcessedEvent, NodeInitEvent> exchange) {
    	
        exchange.getContext().addVariable(exchange.getCurrent().getParam(), VariableType.EDGE_CAL);
        exchange.setNext(new NodeInitEvent()
        		.setTaskId(exchange.getContext().getCurrentTaskInstance().getUuid())
        		.setInstanceUuid(exchange.getCurrent().getInstanceUuid())
        		.setOperator(exchange.getCurrent().getOperator()));
    }

    public int getOrder() {
        return 6;
    }
}

