/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 *  com.wacai.trike.mirana.api.constant.TaskType
 */
package com.wacai.trike.mirana.domain.manage.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.manage.po.ActionConfigPO;
import java.time.LocalDateTime;

public class QActionConfigPO
extends EntityPathBase<ActionConfigPO> {
    private static final long serialVersionUID = 544811994L;
    public static final QActionConfigPO actionConfigPO = new QActionConfigPO("actionConfigPO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final NumberPath<Long> appId;
    public final StringPath code;
    public final StringPath content;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> flowId;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final StringPath requiredVariable;
    public final EnumPath<TaskType> type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;

    public QActionConfigPO(String variable) {
        super(ActionConfigPO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.content = this.createString("content");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.requiredVariable = this.createString("requiredVariable");
        this.type = this.createEnum("type", TaskType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QActionConfigPO(Path<? extends ActionConfigPO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.content = this.createString("content");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.requiredVariable = this.createString("requiredVariable");
        this.type = this.createEnum("type", TaskType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }

    public QActionConfigPO(PathMetadata metadata) {
        super(ActionConfigPO.class, metadata);
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.content = this.createString("content");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.requiredVariable = this.createString("requiredVariable");
        this.type = this.createEnum("type", TaskType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
    }
}

