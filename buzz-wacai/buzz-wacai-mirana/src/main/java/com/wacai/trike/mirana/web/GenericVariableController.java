/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.base.Strings
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.loan.trike.common.model.Page
 *  io.swagger.annotations.ApiOperation
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.util.CollectionUtils
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.PathVariable
 *  org.springframework.web.bind.annotation.PostMapping
 *  org.springframework.web.bind.annotation.RequestBody
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.web;

import com.google.common.base.Strings;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.ConvertUtils;
import com.wacai.trike.mirana.domain.action.GenericVariableService;
import com.wacai.trike.mirana.domain.action.dto.GenericVariableDto;
import com.wacai.trike.mirana.domain.action.enums.VariableEnums;
import com.wacai.trike.mirana.web.model.GenericVariableBatchModel;
import com.wacai.trike.mirana.web.model.GenericVariablePageModel;
import com.wacai.trike.mirana.web.model.GenericVariableRequest;
import com.wacai.trike.mirana.web.model.GenericVariableSaveModel;
import io.swagger.annotations.ApiOperation;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping(value={"/managed/generic-variable"})
public class GenericVariableController {
    private static final Logger log = LoggerFactory.getLogger(GenericVariableController.class);
    @Autowired
    private GenericVariableService variableService;
    @Autowired
    private ConvertUtils convertUtils;

    @GetMapping(value={"/{id}"})
    @ApiOperation(value="\u67e5\u8be2\u53d8\u91cf\u8be6\u60c5", notes="uri\u4e2d\u4f20\u5165\u53d8\u91cfid")
    public ApiResponse<GenericVariablePageModel> query(@PathVariable(value="id") Long id) {
        GenericVariableDto variableDto = this.variableService.query(id);
        if (variableDto == null) {
            return ApiResponse.error((String)("Not found: " + id));
        }
        return ApiResponse.success(this.convertUtils.toPageModel(variableDto));
    }

    @ApiOperation(value="\u53d8\u91cf\u5206\u9875\u67e5\u8be2", notes="\u6761\u4ef6\u53c2\u6570\u62fc\u63a5\u5728uri\u4e2d")
    @GetMapping
    public ApiResponse<Page<GenericVariablePageModel>> page(GenericVariableRequest request) {
        if (request.getPageSize() <= 0 || request.getPageIndex() < 0) {
            return ApiResponse.error((String)"Invalid paging input");
        }
        return ApiResponse.success(this.convert(this.variableService.page(request)));
    }

    @ApiOperation(value="\u53d8\u91cf\u6279\u91cf\u67e5\u8be2", notes="\u6761\u4ef6\u53c2\u6570\u62fc\u63a5\u5728uri\u4e2d")
    @GetMapping(value={"/batch"})
    public ApiResponse<List<GenericVariableBatchModel>> batchQuery(GenericVariableRequest request) {
        return ApiResponse.success(this.convertUtils.toBatchModels(this.variableService.batch(request)));
    }

    @ApiOperation(value="\u65b0\u589e\u6216\u66f4\u65b0\u53d8\u91cf", notes="\u65b0\u589e\u65f6\u4e0d\u9700\u8981\u4f20\u5165id")
    @PostMapping
    public ApiResponse<GenericVariableBatchModel> save(@RequestBody GenericVariableSaveModel model) {
        if (model.getSourceType() == VariableEnums.SourceType.REMOTE && Strings.isNullOrEmpty((String)model.getCandidatesUrl())) {
            return ApiResponse.error((String)"candidatesUrl\u4e0d\u80fd\u4e3a\u7a7a");
        }
        if (model.getSourceType() == VariableEnums.SourceType.DEFINITION && CollectionUtils.isEmpty(model.getCandidates())) {
            return ApiResponse.error((String)"candidates\u4e0d\u80fd\u4e3a\u7a7a");
        }
        return ApiResponse.success(this.convertUtils.toBatchModel(this.variableService.save(this.convertUtils.toDto(model))));
    }

    @ApiOperation(value="\u5220\u9664\u53d8\u91cf", notes="uri\u4e2d\u4f20\u5165\u53d8\u91cfid")
    @PostMapping(value={"/{id}"})
    public ApiResponse<Integer> delete(@PathVariable(value="id") Long id) {
        if (id == null) {
            return ApiResponse.error((String)"Variable id must be not null");
        }
        if (this.variableService.query(id) == null) {
            return ApiResponse.error((String)("Not found: " + id));
        }
        return ApiResponse.success(this.variableService.delete(id));
    }

    @ApiOperation(value="\u53d8\u91cf\u503c\u7c7b\u578b\u5217\u8868", notes="\u8fd4\u56de\u6240\u6709\u7c7b\u578b\u96c6\u5408")
    @GetMapping(value={"/data-types"})
    public ApiResponse<List<VariableEnums.DataType>> dataTypes() {
        return ApiResponse.success(Arrays.asList(VariableEnums.DataType.values()));
    }

    @ApiOperation(value="\u53d8\u91cf\u503c\u6765\u6e90\u7c7b\u578b\u5217\u8868", notes="\u8fd4\u56de\u6240\u6709\u7c7b\u578b\u96c6\u5408")
    @GetMapping(value={"/source-types"})
    public ApiResponse<List<VariableEnums.SourceType>> sourceTypes() {
        return ApiResponse.success(Arrays.asList(VariableEnums.SourceType.values()));
    }

    @ApiOperation(value="\u6839\u636e\u53d8\u91cfid\u83b7\u53d6\u53d8\u91cf\u5019\u9009\u503c\u5217\u8868", notes="MANUAL_INPUT\u7c7b\u578b\u53ea\u80fd\u624b\u52a8\u8f93\u5165")
    @GetMapping(value={"/fetch-candidates"})
    public ApiResponse<List<GenericVariableBatchModel>> fetchCandidates(@RequestParam(value="id") List<Long> id) {
        if (id == null) {
            return ApiResponse.error((String)"Variable id must be not null");
        }
        return ApiResponse.success(this.variableService.fetchCandidates(id).stream().map(dto -> {
            GenericVariableBatchModel model = new GenericVariableBatchModel();
            model.setId(dto.getId());
            model.setCandidates(dto.getCandidates());
            return model;
        }).collect(Collectors.toList()));
    }

    private Page<GenericVariablePageModel> convert(Page<GenericVariableDto> page) {
        return new Page().setTotal(page.getTotal()).setPage(page.getPage()).setData(this.convertUtils.toPageModels(page.getData()));
    }
}

