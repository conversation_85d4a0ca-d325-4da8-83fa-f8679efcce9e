/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.web.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wacai.trike.mirana.web.model.GenericTemplateRequestModel;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class GenericTemplateModel {
    private Long id;
    private String name;
    private Long appId;
    private String appName;
    private Long actionId;
    private String genericActionName;
    private List<GenericTemplateRequestModel.VariableValue> varList;

    public Long getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getAppName() {
        return this.appName;
    }

    public Long getActionId() {
        return this.actionId;
    }

    public String getGenericActionName() {
        return this.genericActionName;
    }

    public List<GenericTemplateRequestModel.VariableValue> getVarList() {
        return this.varList;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setAppName(String appName) {
        this.appName = appName;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public void setGenericActionName(String genericActionName) {
        this.genericActionName = genericActionName;
    }

    public void setVarList(List<GenericTemplateRequestModel.VariableValue> varList) {
        this.varList = varList;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericTemplateModel)) {
            return false;
        }
        GenericTemplateModel other = (GenericTemplateModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$appName = this.getAppName();
        String other$appName = other.getAppName();
        if (this$appName == null ? other$appName != null : !this$appName.equals(other$appName)) {
            return false;
        }
        Long this$actionId = this.getActionId();
        Long other$actionId = other.getActionId();
        if (this$actionId == null ? other$actionId != null : !(this$actionId).equals(other$actionId)) {
            return false;
        }
        String this$genericActionName = this.getGenericActionName();
        String other$genericActionName = other.getGenericActionName();
        if (this$genericActionName == null ? other$genericActionName != null : !this$genericActionName.equals(other$genericActionName)) {
            return false;
        }
        List<GenericTemplateRequestModel.VariableValue> this$varList = this.getVarList();
        List<GenericTemplateRequestModel.VariableValue> other$varList = other.getVarList();
        return !(this$varList == null ? other$varList != null : !(this$varList).equals(other$varList));
    }

    protected boolean canEqual(Object other) {
        return other instanceof GenericTemplateModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $appName = this.getAppName();
        result = result * 59 + ($appName == null ? 43 : $appName.hashCode());
        Long $actionId = this.getActionId();
        result = result * 59 + ($actionId == null ? 43 : ($actionId).hashCode());
        String $genericActionName = this.getGenericActionName();
        result = result * 59 + ($genericActionName == null ? 43 : $genericActionName.hashCode());
        List<GenericTemplateRequestModel.VariableValue> $varList = this.getVarList();
        result = result * 59 + ($varList == null ? 43 : ($varList).hashCode());
        return result;
    }

    public String toString() {
        return "GenericTemplateModel(id=" + this.getId() + ", name=" + this.getName() + ", appId=" + this.getAppId() + ", appName=" + this.getAppName() + ", actionId=" + this.getActionId() + ", genericActionName=" + this.getGenericActionName() + ", varList=" + this.getVarList() + ")";
    }
}

