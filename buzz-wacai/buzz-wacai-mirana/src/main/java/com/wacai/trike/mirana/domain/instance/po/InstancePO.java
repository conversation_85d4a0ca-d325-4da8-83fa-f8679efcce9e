/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.instance.po;

import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.common.po.BasePO;
import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="instance")
public class InstancePO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private String uuid;
    private String parentUuid;
    private String bzKey;
    private Long flowId;
    private Long currentNodeId;
    @Enumerated(value=EnumType.STRING)
    private InstanceStatus status;
    private LocalDateTime timeout;
    private Boolean async;
    private Boolean esSync;

    public String getUuid() {
        return this.uuid;
    }

    public String getParentUuid() {
        return this.parentUuid;
    }

    public String getBzKey() {
        return this.bzKey;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public Long getCurrentNodeId() {
        return this.currentNodeId;
    }

    public InstanceStatus getStatus() {
        return this.status;
    }

    public LocalDateTime getTimeout() {
        return this.timeout;
    }

    public Boolean getAsync() {
        return this.async;
    }

    public Boolean getEsSync() {
        return this.esSync;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setParentUuid(String parentUuid) {
        this.parentUuid = parentUuid;
    }

    public void setBzKey(String bzKey) {
        this.bzKey = bzKey;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setCurrentNodeId(Long currentNodeId) {
        this.currentNodeId = currentNodeId;
    }

    public void setStatus(InstanceStatus status) {
        this.status = status;
    }

    public void setTimeout(LocalDateTime timeout) {
        this.timeout = timeout;
    }

    public void setAsync(Boolean async) {
        this.async = async;
    }

    public void setEsSync(Boolean esSync) {
        this.esSync = esSync;
    }

    @Override
    public String toString() {
        return "InstancePO(uuid=" + this.getUuid() + ", parentUuid=" + this.getParentUuid() + ", bzKey=" + this.getBzKey() + ", flowId=" + this.getFlowId() + ", currentNodeId=" + this.getCurrentNodeId() + ", status=" + this.getStatus() + ", timeout=" + this.getTimeout() + ", async=" + this.getAsync() + ", esSync=" + this.getEsSync() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstancePO)) {
            return false;
        }
        InstancePO other = (InstancePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$uuid = this.getUuid();
        String other$uuid = other.getUuid();
        if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
            return false;
        }
        String this$parentUuid = this.getParentUuid();
        String other$parentUuid = other.getParentUuid();
        if (this$parentUuid == null ? other$parentUuid != null : !this$parentUuid.equals(other$parentUuid)) {
            return false;
        }
        String this$bzKey = this.getBzKey();
        String other$bzKey = other.getBzKey();
        if (this$bzKey == null ? other$bzKey != null : !this$bzKey.equals(other$bzKey)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        Long this$currentNodeId = this.getCurrentNodeId();
        Long other$currentNodeId = other.getCurrentNodeId();
        if (this$currentNodeId == null ? other$currentNodeId != null : !(this$currentNodeId).equals(other$currentNodeId)) {
            return false;
        }
        InstanceStatus this$status = this.getStatus();
        InstanceStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !this$status.equals(other$status)) {
            return false;
        }
        LocalDateTime this$timeout = this.getTimeout();
        LocalDateTime other$timeout = other.getTimeout();
        if (this$timeout == null ? other$timeout != null : !(this$timeout).equals(other$timeout)) {
            return false;
        }
        Boolean this$async = this.getAsync();
        Boolean other$async = other.getAsync();
        if (this$async == null ? other$async != null : !(this$async).equals(other$async)) {
            return false;
        }
        Boolean this$esSync = this.getEsSync();
        Boolean other$esSync = other.getEsSync();
        return !(this$esSync == null ? other$esSync != null : !(this$esSync).equals(other$esSync));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof InstancePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        String $parentUuid = this.getParentUuid();
        result = result * 59 + ($parentUuid == null ? 43 : $parentUuid.hashCode());
        String $bzKey = this.getBzKey();
        result = result * 59 + ($bzKey == null ? 43 : $bzKey.hashCode());
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        Long $currentNodeId = this.getCurrentNodeId();
        result = result * 59 + ($currentNodeId == null ? 43 : ($currentNodeId).hashCode());
        InstanceStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : $status.hashCode());
        LocalDateTime $timeout = this.getTimeout();
        result = result * 59 + ($timeout == null ? 43 : ($timeout).hashCode());
        Boolean $async = this.getAsync();
        result = result * 59 + ($async == null ? 43 : ($async).hashCode());
        Boolean $esSync = this.getEsSync();
        result = result * 59 + ($esSync == null ? 43 : ($esSync).hashCode());
        return result;
    }
}

