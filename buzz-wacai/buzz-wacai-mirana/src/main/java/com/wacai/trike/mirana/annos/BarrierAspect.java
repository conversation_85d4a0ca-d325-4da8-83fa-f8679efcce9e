/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.aspectj.lang.ProceedingJoinPoint
 *  org.aspectj.lang.annotation.Around
 *  org.aspectj.lang.annotation.Aspect
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.context.ApplicationContext
 *  org.springframework.core.Ordered
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.annos;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.core.Ordered;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class BarrierAspect
		implements Ordered {
	@Autowired
	private ApplicationContext ctx;

	@Around(value = "@annotation(com.wacai.trike.mirana.annos.Barrier) ||@within(com.wacai.trike.mirana.annos.Barrier)")
	public Object intercept(ProceedingJoinPoint pjp) throws Throwable {
		//解析出AnnotationMeta
		BarrierAnnotationMeta barrierMeta = ProceedingJoinPointResolver.resolveBarrieAnnotation(pjp);
		if (barrierMeta.getBarrierAnnotation() == null) {
			return pjp.proceed();
		}
		Class<? extends MethodInvocationHandler>[] handles = barrierMeta.getBarrierAnnotation().handlers();
		//没有handles则跳过
		if (handles.length == 0) {
			return pjp.proceed();
		}

		return this.doIntercept(pjp, handles, barrierMeta);
	}

	private Object doIntercept(ProceedingJoinPoint pjp, Class<? extends MethodInvocationHandler>[] handles,
			BarrierAnnotationMeta barrierMeta) throws Throwable {
		//保存调用信息
		MethodInvocation invocation = new MethodInvocation(pjp, barrierMeta);
		List<MethodInvocationHandler> handlerInstanceList = Arrays.stream(handles)
				.map(h -> (MethodInvocationHandler) this.ctx.getBean(h)).collect(Collectors.toList());
		//前置处理
		for (MethodInvocationHandler handlerInstance : handlerInstanceList) {
			handlerInstance.preHandle(invocation);
		}
		try {
			Object result = pjp.proceed();
			invocation.setResult(result);
		} catch (Throwable t) {
			invocation.setThrowable(t);
		} finally {
			invocation.setEnd(System.currentTimeMillis());
		}
		//后置处理
		for (MethodInvocationHandler handlerInstance : handlerInstanceList) {
			handlerInstance.postHandle(invocation);
		}
		//抛出业务异常
		if (invocation.getThrowable() != null) {
			throw invocation.getThrowable();
		}
		return invocation.getResult();
	}

	public int getOrder() {
		return Integer.MAX_VALUE;
	}
}
