/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.base.Strings
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Expression
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.loan.trike.common.model.Page
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.domain.action;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import com.google.common.base.Strings;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Expression;
import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.loan.trike.common.model.Page;
import com.wacai.trike.mirana.domain.action.dao.po.GenericActionTemplatePO;
import com.wacai.trike.mirana.domain.action.dao.po.GenericTemplateVariableValuePO;
import com.wacai.trike.mirana.domain.action.dao.po.QGenericActionTemplatePO;
import com.wacai.trike.mirana.domain.action.dao.po.QGenericTemplateVariableValuePO;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericActionRepository;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericActionTemplateRepository;
import com.wacai.trike.mirana.domain.action.dao.repository.GenericTemplateVariableValueRepository;
import com.wacai.trike.mirana.domain.action.dto.GenericTemplateDto;
import com.wacai.trike.mirana.domain.bu.repository.ApplicationRepository;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.web.model.GenericTemplateRequest;
import com.wacai.trike.mirana.web.model.GenericTemplateRequestModel;
import com.wacai.trike.mirana.web.model.GenericTemplateRequestModel.VariableValue;

@Service
public class GenericTemplateServiceImpl implements GenericTemplateService {
	private static final Logger log = LoggerFactory.getLogger(GenericTemplateServiceImpl.class);
	@Autowired
	private JPAQueryFactory jpaQueryFactory;
	@Autowired
	private GenericActionTemplateRepository templateRepository;
	@Autowired
	private GenericTemplateVariableValueRepository variableValueRepository;
	@Autowired
	private GenericActionRepository actionRepository;
	@Autowired
	private ApplicationRepository applicationRepository;

	@Override
	public GenericTemplateDto create(GenericTemplateDto dto) {
		GenericActionTemplatePO actionTemplate = (GenericActionTemplatePO) this.templateRepository
				.save(this.build(dto));
		if (!CollectionUtils.isEmpty(dto.getVarList())) {
			dto.getVarList().forEach(variableValue -> {
				GenericTemplateVariableValuePO cfr_ignored_0 = (GenericTemplateVariableValuePO) this.variableValueRepository
						.save(this.build((GenericTemplateRequestModel.VariableValue) variableValue,
								actionTemplate.getId()));
			});
		}
		return this.convertAndFill(actionTemplate);
	}

	@Override
	public GenericTemplateDto query(Long id) {
		if (id == null) {
			return null;
		}
		GenericActionTemplatePO templatePO = this.templateRepository.findById(id).orElse(null);
		if (templatePO == null) {
			return null;
		}
		GenericTemplateDto templateDto = this.convertAndFill(templatePO);
		this.fillTemplateVariables(templateDto);
		return templateDto;
	}

	@Override
	public Page<GenericTemplateDto> page(GenericTemplateRequest request) {
		Page page = new Page();
		BooleanExpression expression = this.bindCondition(request);
		page.setPage((long) request.getPageIndex());
		page.setTotal(((JPAQuery) ((JPAQuery) this.jpaQueryFactory
				.select((Expression) QGenericActionTemplatePO.genericActionTemplatePO.id.count())
				.from((EntityPath) QGenericActionTemplatePO.genericActionTemplatePO)).where((Predicate) expression))
						.fetchCount());
		List<GenericTemplateDto> result = this.getGenericTemplateDtos(request, expression);
		page.setData(result);
		return page;
	}

	@Override
	public List<GenericTemplateDto> batch(GenericTemplateRequest request) {
		BooleanExpression expression = this.bindCondition(request);
		return this.getGenericTemplateDtos(request, expression);
	}

	@Override
	public GenericTemplateDto update(GenericTemplateDto dto) {
		GenericActionTemplatePO templatePO = this.templateRepository.findById(dto.getId()).orElse(null);
		if (templatePO == null) {
			return null;
		}
		templatePO.setActionId(dto.getActionId());
		templatePO.setName(dto.getName());
		this.templateRepository.save(templatePO);
		for (GenericTemplateRequestModel.VariableValue value : dto.getVarList()) {
			GenericTemplateVariableValuePO variableValuePO = (GenericTemplateVariableValuePO) ((JPAQuery) ((JPAQuery) this.jpaQueryFactory
					.selectFrom((EntityPath) QGenericTemplateVariableValuePO.genericTemplateVariableValuePO)
					.where((Predicate) QGenericTemplateVariableValuePO.genericTemplateVariableValuePO.templateId
							.eq(templatePO.getId()))).where(
									(Predicate) QGenericTemplateVariableValuePO.genericTemplateVariableValuePO.variableId
											.eq(value.getVariableId()))).fetchFirst();
			if (variableValuePO != null) {
				variableValuePO.setVariableValue(value.getValue());
				variableValuePO.setActive(Boolean.TRUE);
				this.variableValueRepository.save(variableValuePO);
				continue;
			}
			this.variableValueRepository.save(this.build(value, templatePO.getId()));
		}
		return dto;
	}

	private GenericActionTemplatePO build(GenericTemplateDto dto) {
		GenericActionTemplatePO templatePO = new GenericActionTemplatePO();
		templatePO.setActive(true);
		templatePO.setActionId(dto.getActionId());
		templatePO.setName(dto.getName());
		return templatePO;
	}

	private GenericTemplateVariableValuePO build(GenericTemplateRequestModel.VariableValue value, Long templateId) {
		GenericTemplateVariableValuePO po = new GenericTemplateVariableValuePO();
		po.setTemplateId(templateId);
		po.setVariableId(value.getVariableId());
		po.setVariableValue(value.getValue());
		po.setActive(true);
		return po;
	}

	private GenericTemplateRequestModel.VariableValue build(Long variableId, String value) {
		GenericTemplateRequestModel.VariableValue variableValue = new GenericTemplateRequestModel.VariableValue();
		variableValue.setVariableId(variableId);
		variableValue.setValue(value);
		return variableValue;
	}

	private BooleanExpression bindCondition(GenericTemplateRequest request) {
		BooleanExpression expression = QGenericActionTemplatePO.genericActionTemplatePO.active
				.eq(Boolean.valueOf(true));
		if (request.getGenericActionId() != null) {
			expression = expression.and((Predicate) QGenericActionTemplatePO.genericActionTemplatePO.actionId
					.eq(request.getGenericActionId()));
		}
		if (!Strings.isNullOrEmpty((String) request.getName())) {
			expression = expression.and((Predicate) QGenericActionTemplatePO.genericActionTemplatePO.name
					.like("%" + request.getName() + "%"));
		}
		return expression;
	}

	private GenericTemplateDto convertAndFill(GenericActionTemplatePO actionTemplate) {
		GenericTemplateDto templateDto = ObjectUtils.convertNotNull(actionTemplate, GenericTemplateDto.class);
		this.fillActionTemplate(templateDto);
		return templateDto;
	}

	private List<GenericTemplateDto> getGenericTemplateDtos(GenericTemplateRequest request, BooleanExpression expression) {
		//jpaQueryFactory.selectFrom()
        List<GenericTemplateDto> result = null;
        result.forEach(dto -> {
            this.fillActionTemplate((GenericTemplateDto)dto);
            this.fillTemplateVariables((GenericTemplateDto)dto);
        });
        return result;
    }

	private void fillActionTemplate(GenericTemplateDto templateDto) {
		this.actionRepository.findById(templateDto.getActionId()).ifPresent(action -> {
			templateDto.setGenericActionName(action.getName());
			this.applicationRepository.findById(action.getAppId())
					.ifPresent(app -> templateDto.setAppName(app.getName()));
		});
	}

	private void fillTemplateVariables(GenericTemplateDto templateDto) {
		List<GenericTemplateVariableValuePO> variableValuePOS = ((JPAQuery) ((JPAQuery) this.jpaQueryFactory
				.selectFrom((EntityPath) QGenericTemplateVariableValuePO.genericTemplateVariableValuePO)
				.where((Predicate) QGenericTemplateVariableValuePO.genericTemplateVariableValuePO.templateId
						.eq(templateDto.getId())))
								.where((Predicate) QGenericTemplateVariableValuePO.genericTemplateVariableValuePO.active
										.isTrue())).fetch();
		if (!CollectionUtils.isEmpty( variableValuePOS)) {
			ArrayList<GenericTemplateRequestModel.VariableValue> vvs = new ArrayList<GenericTemplateRequestModel.VariableValue>(
					variableValuePOS.size());
			variableValuePOS.forEach(vvp -> vvs.add(this.build(vvp.getVariableId(), vvp.getVariableValue())));
			templateDto.setVarList(vvs);
		}
	}
}
