/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.expression.Operator
 */
package com.wacai.trike.mirana.service;

import com.wacai.loan.trike.expression.Operator;
import com.wacai.trike.mirana.domain.manage.po.FlowVariablePO;
import com.wacai.trike.mirana.service.BaseDTO;
import com.wacai.trike.mirana.util.ObjectMappers;
import com.wacai.trike.mirana.util.ObjectUtils;
import com.wacai.trike.mirana.web.model.FlowVariableModel;
import java.util.List;

public class FlowVariableDTO
extends BaseDTO {
    private Long appId;
    private String code;
    private String name;
    private String type;
    private String sourceType;
    private List<SupportedOperator> supportedOperators;
    private List<Candidate> candidates;
    private String candidatesUrl;

    public FlowVariablePO toPo() {
        FlowVariablePO variablePO = ObjectUtils.convertNotNull(this, FlowVariablePO.class);
        variablePO.setSupportedOperators(ObjectMappers.mustWriteValue(this.getSupportedOperators()));
        variablePO.setCandidates(ObjectMappers.mustWriteValue(this.getCandidates()));
        return variablePO;
    }

    public FlowVariableModel toModel() {
        return ObjectUtils.convertNotNull(this, FlowVariableModel.class);
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowVariableDTO)) {
            return false;
        }
        FlowVariableDTO other = (FlowVariableDTO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$type = this.getType();
        String other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$sourceType = this.getSourceType();
        String other$sourceType = other.getSourceType();
        if (this$sourceType == null ? other$sourceType != null : !this$sourceType.equals(other$sourceType)) {
            return false;
        }
        List<SupportedOperator> this$supportedOperators = this.getSupportedOperators();
        List<SupportedOperator> other$supportedOperators = other.getSupportedOperators();
        if (this$supportedOperators == null ? other$supportedOperators != null : !(this$supportedOperators).equals(other$supportedOperators)) {
            return false;
        }
        List<Candidate> this$candidates = this.getCandidates();
        List<Candidate> other$candidates = other.getCandidates();
        if (this$candidates == null ? other$candidates != null : !(this$candidates).equals(other$candidates)) {
            return false;
        }
        String this$candidatesUrl = this.getCandidatesUrl();
        String other$candidatesUrl = other.getCandidatesUrl();
        return !(this$candidatesUrl == null ? other$candidatesUrl != null : !this$candidatesUrl.equals(other$candidatesUrl));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowVariableDTO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $sourceType = this.getSourceType();
        result = result * 59 + ($sourceType == null ? 43 : $sourceType.hashCode());
        List<SupportedOperator> $supportedOperators = this.getSupportedOperators();
        result = result * 59 + ($supportedOperators == null ? 43 : ($supportedOperators).hashCode());
        List<Candidate> $candidates = this.getCandidates();
        result = result * 59 + ($candidates == null ? 43 : ($candidates).hashCode());
        String $candidatesUrl = this.getCandidatesUrl();
        result = result * 59 + ($candidatesUrl == null ? 43 : $candidatesUrl.hashCode());
        return result;
    }

    public Long getAppId() {
        return this.appId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getType() {
        return this.type;
    }

    public String getSourceType() {
        return this.sourceType;
    }

    public List<SupportedOperator> getSupportedOperators() {
        return this.supportedOperators;
    }

    public List<Candidate> getCandidates() {
        return this.candidates;
    }

    public String getCandidatesUrl() {
        return this.candidatesUrl;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setSourceType(String sourceType) {
        this.sourceType = sourceType;
    }

    public void setSupportedOperators(List<SupportedOperator> supportedOperators) {
        this.supportedOperators = supportedOperators;
    }

    public void setCandidates(List<Candidate> candidates) {
        this.candidates = candidates;
    }

    public void setCandidatesUrl(String candidatesUrl) {
        this.candidatesUrl = candidatesUrl;
    }

    @Override
    public String toString() {
        return "FlowVariableDTO(appId=" + this.getAppId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", type=" + this.getType() + ", sourceType=" + this.getSourceType() + ", supportedOperators=" + this.getSupportedOperators() + ", candidates=" + this.getCandidates() + ", candidatesUrl=" + this.getCandidatesUrl() + ")";
    }

    public static class Candidate {
        private String code;
        private String name;

        public String getCode() {
            return this.code;
        }

        public String getName() {
            return this.name;
        }

        public void setCode(String code) {
            this.code = code;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof Candidate)) {
                return false;
            }
            Candidate other = (Candidate)o;
            if (!other.canEqual(this)) {
                return false;
            }
            String this$code = this.getCode();
            String other$code = other.getCode();
            if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
                return false;
            }
            String this$name = this.getName();
            String other$name = other.getName();
            return !(this$name == null ? other$name != null : !this$name.equals(other$name));
        }

        protected boolean canEqual(Object other) {
            return other instanceof Candidate;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            String $code = this.getCode();
            result = result * 59 + ($code == null ? 43 : $code.hashCode());
            String $name = this.getName();
            result = result * 59 + ($name == null ? 43 : $name.hashCode());
            return result;
        }

        public String toString() {
            return "FlowVariableDTO.Candidate(code=" + this.getCode() + ", name=" + this.getName() + ")";
        }

        public Candidate(String code, String name) {
            this.code = code;
            this.name = name;
        }

        public Candidate() {
        }
    }

    public static class SupportedOperator {
        private Operator code;
        private String name;

        public Operator getCode() {
            return this.code;
        }

        public String getName() {
            return this.name;
        }

        public void setCode(Operator code) {
            this.code = code;
        }

        public void setName(String name) {
            this.name = name;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof SupportedOperator)) {
                return false;
            }
            SupportedOperator other = (SupportedOperator)o;
            if (!other.canEqual(this)) {
                return false;
            }
            Operator this$code = this.getCode();
            Operator other$code = other.getCode();
            if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
                return false;
            }
            String this$name = this.getName();
            String other$name = other.getName();
            return !(this$name == null ? other$name != null : !this$name.equals(other$name));
        }

        protected boolean canEqual(Object other) {
            return other instanceof SupportedOperator;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            Operator $code = this.getCode();
            result = result * 59 + ($code == null ? 43 : $code.hashCode());
            String $name = this.getName();
            result = result * 59 + ($name == null ? 43 : $name.hashCode());
            return result;
        }

        public String toString() {
            return "FlowVariableDTO.SupportedOperator(code=" + this.getCode() + ", name=" + this.getName() + ")";
        }

        public SupportedOperator(Operator code, String name) {
            this.code = code;
            this.name = name;
        }

        public SupportedOperator() {
        }
    }
}

