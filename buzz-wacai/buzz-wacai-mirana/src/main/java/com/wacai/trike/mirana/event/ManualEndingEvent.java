/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.event.CancelInstanceEvent;

public class ManualEndingEvent
extends CancelInstanceEvent {
    @Override
    public String toString() {
        return "ManualEndingEvent()";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ManualEndingEvent)) {
            return false;
        }
        ManualEndingEvent other = (ManualEndingEvent)o;
        if (!other.canEqual(this)) {
            return false;
        }
        return super.equals(o);
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof ManualEndingEvent;
    }

    @Override
    public int hashCode() {
        int result = super.hashCode();
        return result;
    }
}

