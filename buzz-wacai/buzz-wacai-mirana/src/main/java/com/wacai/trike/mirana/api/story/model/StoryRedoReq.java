/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.story.model;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import javax.validation.constraints.NotBlank;

public class StoryRedoReq
implements Serializable {
    @NotBlank
    private List<String> instanceIds;
    @NotBlank
    private String bu;
    @NotBlank
    private String app;
    @NotBlank
    private String flow;
    @NotBlank
    private String operator;
    private boolean async = false;
    private Map<String, String> param = new HashMap<String, String>();

    public StoryRedoReq addParam(String key, String value) {
        this.param.put(Objects.requireNonNull(key), value);
        return this;
    }

    public List<String> getInstanceIds() {
        return this.instanceIds;
    }

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getFlow() {
        return this.flow;
    }

    public String getOperator() {
        return this.operator;
    }

    public boolean isAsync() {
        return this.async;
    }

    public Map<String, String> getParam() {
        return this.param;
    }

    public StoryRedoReq setInstanceIds(List<String> instanceIds) {
        this.instanceIds = instanceIds;
        return this;
    }

    public StoryRedoReq setBu(String bu) {
        this.bu = bu;
        return this;
    }

    public StoryRedoReq setApp(String app) {
        this.app = app;
        return this;
    }

    public StoryRedoReq setFlow(String flow) {
        this.flow = flow;
        return this;
    }

    public StoryRedoReq setOperator(String operator) {
        this.operator = operator;
        return this;
    }

    public StoryRedoReq setAsync(boolean async) {
        this.async = async;
        return this;
    }

    public StoryRedoReq setParam(Map<String, String> param) {
        this.param = param;
        return this;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof StoryRedoReq)) {
            return false;
        }
        StoryRedoReq other = (StoryRedoReq)o;
        if (!other.canEqual(this)) {
            return false;
        }
        List<String> this$instanceIds = this.getInstanceIds();
        List<String> other$instanceIds = other.getInstanceIds();
        if (this$instanceIds == null ? other$instanceIds != null : !(this$instanceIds).equals(other$instanceIds)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        if (this$flow == null ? other$flow != null : !this$flow.equals(other$flow)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        if (this.isAsync() != other.isAsync()) {
            return false;
        }
        Map<String, String> this$param = this.getParam();
        Map<String, String> other$param = other.getParam();
        return !(this$param == null ? other$param != null : !(this$param).equals(other$param));
    }

    protected boolean canEqual(Object other) {
        return other instanceof StoryRedoReq;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        List<String> $instanceIds = this.getInstanceIds();
        result = result * 59 + ($instanceIds == null ? 43 : ($instanceIds).hashCode());
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        result = result * 59 + (this.isAsync() ? 79 : 97);
        Map<String, String> $param = this.getParam();
        result = result * 59 + ($param == null ? 43 : ($param).hashCode());
        return result;
    }

    public String toString() {
        return "StoryRedoReq(instanceIds=" + this.getInstanceIds() + ", bu=" + this.getBu() + ", app=" + this.getApp() + ", flow=" + this.getFlow() + ", operator=" + this.getOperator() + ", async=" + this.isAsync() + ", param=" + this.getParam() + ")";
    }
}

