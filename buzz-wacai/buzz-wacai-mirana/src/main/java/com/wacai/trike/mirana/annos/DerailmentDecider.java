/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Lists
 *  com.wacai.trike.mirana.api.constant.FlowStatus
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.annos;

import com.google.common.collect.Lists;
import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.domain.edge.po.EdgePO;
import com.wacai.trike.mirana.domain.edge.repository.EdgeRepository;
import com.wacai.trike.mirana.domain.flow.po.FlowPO;
import com.wacai.trike.mirana.domain.flow.po.FlowUnionNodePO;
import com.wacai.trike.mirana.domain.flow.repository.FlowRepository;
import com.wacai.trike.mirana.domain.flow.repository.FlowUnionNodeRepository;
import java.util.LinkedList;
import java.util.List;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

@Component
public class DerailmentDecider {
    private static final Logger log = LoggerFactory.getLogger(DerailmentDecider.class);
    @Autowired
    private FlowRepository flowRepository;
    @Autowired
    private EdgeRepository edgeRepository;
    @Autowired
    private FlowUnionNodeRepository unionNodeRepository;

    public void handle(InstanceContext context) {
    	
        FlowPO predecessor = this.flowRepository.findById(context.getFlowId()).orElse(null);
        if (predecessor == null) {
            log.error("Should never occurred: not found {}", context.getFlowId());
            return;
        }
        if (predecessor.getStatus() == FlowStatus.ENABLED) {
            log.info("Do nothing, current flow {} / {} / {} is ENABLED", new Object[]{predecessor.getId(), predecessor.getCode(), predecessor.getVersion()});
            return;
        }
        FlowPO incumbent = this.flowRepository.findByCodeAndStatus(context.getFlowCode(), FlowStatus.ENABLED);
        if (incumbent == null) {
            log.error("Should never occurred: flow {} not have incumbent", context.getFlowCode());
            return;
        }
        boolean legitimate = this.legitimate(this.collect(incumbent.getId(), context.getCurrentNodeId()), this.collect(predecessor.getId(), context.getCurrentNodeId()));
        if (!legitimate) {
            log.warn("node {} to flow {} incumbent {} / predecessor {} is not legitimate", new Object[]{context.getCurrentNodeId(), context.getFlowCode(), incumbent.getId(), predecessor.getId()});
            return;
        }
        context.setDerailed(true);
        context.setDirty(true);
        context.setFlowId(incumbent.getId());
        context.setVersion(incumbent.getVersion());
    }

    private boolean legitimate(LinkedList<Long> incumbent, LinkedList<Long> predecessor) {
        if (CollectionUtils.isEmpty(incumbent) || CollectionUtils.isEmpty(predecessor)) {
            return false;
        }
        if (incumbent.size() != predecessor.size()) {
            return false;
        }
        for (int i = 0; i < incumbent.size(); ++i) {
            if (incumbent.get(i).longValue() == predecessor.get(i).longValue()) continue;
            return false;
        }
        return true;
    }

    private LinkedList<Long> collect(long flowId, long specificNodeId) {
        List<FlowUnionNodePO> nodes = this.unionNodeRepository.findByFlowId(flowId);
        if (CollectionUtils.isEmpty(nodes)) {
            return null;
        }
        List<EdgePO> edges = this.edgeRepository.findByFlowId(flowId);
        if (CollectionUtils.isEmpty(edges)) {
            return null;
        }
        LinkedList result = Lists.newLinkedList();
        result.add(specificNodeId);
        while (true) {
            long curr = specificNodeId;
            EdgePO found = edges.stream().filter(e -> e.getToId() == curr).findFirst().orElse(null);
            if (found == null) break;
            specificNodeId = found.getFromId();
            result.addFirst(specificNodeId);
        }
        return result;
    }
}

