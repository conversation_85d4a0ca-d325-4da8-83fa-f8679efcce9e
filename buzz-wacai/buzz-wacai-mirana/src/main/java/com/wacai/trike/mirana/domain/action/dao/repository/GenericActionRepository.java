/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.springframework.data.jpa.repository.JpaRepository
 */
package com.wacai.trike.mirana.domain.action.dao.repository;

import com.wacai.trike.mirana.domain.action.dao.po.GenericActionPO;
import java.util.List;
import org.springframework.data.jpa.repository.JpaRepository;

public interface GenericActionRepository
extends JpaRepository<GenericActionPO, Long> {
    public List<GenericActionPO> findByActive(boolean var1);
}

