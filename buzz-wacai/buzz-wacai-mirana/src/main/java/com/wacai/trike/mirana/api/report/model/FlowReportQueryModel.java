/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.report.model;

import java.io.Serializable;
import java.time.LocalDate;
import javax.validation.constraints.NotBlank;

public class FlowReportQueryModel
implements Serializable {
    @NotBlank
    private String bu;
    @NotBlank
    private String app;
    private String flow;
    private Long createNodeId;
    private LocalDate date;

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getFlow() {
        return this.flow;
    }

    public Long getCreateNodeId() {
        return this.createNodeId;
    }

    public LocalDate getDate() {
        return this.date;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public void setFlow(String flow) {
        this.flow = flow;
    }

    public void setCreateNodeId(Long createNodeId) {
        this.createNodeId = createNodeId;
    }

    public void setDate(LocalDate date) {
        this.date = date;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowReportQueryModel)) {
            return false;
        }
        FlowReportQueryModel other = (FlowReportQueryModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$flow = this.getFlow();
        String other$flow = other.getFlow();
        if (this$flow == null ? other$flow != null : !this$flow.equals(other$flow)) {
            return false;
        }
        Long this$createNodeId = this.getCreateNodeId();
        Long other$createNodeId = other.getCreateNodeId();
        if (this$createNodeId == null ? other$createNodeId != null : !(this$createNodeId).equals(other$createNodeId)) {
            return false;
        }
        LocalDate this$date = this.getDate();
        LocalDate other$date = other.getDate();
        return !(this$date == null ? other$date != null : !(this$date).equals(other$date));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowReportQueryModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $flow = this.getFlow();
        result = result * 59 + ($flow == null ? 43 : $flow.hashCode());
        Long $createNodeId = this.getCreateNodeId();
        result = result * 59 + ($createNodeId == null ? 43 : ($createNodeId).hashCode());
        LocalDate $date = this.getDate();
        result = result * 59 + ($date == null ? 43 : ($date).hashCode());
        return result;
    }

    public String toString() {
        return "FlowReportQueryModel(bu=" + this.getBu() + ", app=" + this.getApp() + ", flow=" + this.getFlow() + ", createNodeId=" + this.getCreateNodeId() + ", date=" + this.getDate() + ")";
    }
}

