/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.task;

public class TaskExecuteException
extends RuntimeException {
    public TaskExecuteException() {
    }

    public TaskExecuteException(String message) {
        super(message);
    }

    public TaskExecuteException(String message, Throwable cause) {
        super(message, cause);
    }

    public TaskExecuteException(Throwable cause) {
        super(cause);
    }
}

