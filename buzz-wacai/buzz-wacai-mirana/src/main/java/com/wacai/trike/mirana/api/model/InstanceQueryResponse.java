/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.model.FlowResponse;
import java.util.HashMap;
import java.util.Map;

public class InstanceQueryResponse
extends FlowResponse {
    private String currentNodeName;
    private String currentNodeCode;
    private String currentTaskInstanceId;
    private Long currentNodeId;
    private Map<String, String> variables = new HashMap<String, String>();

    public String getCurrentNodeName() {
        return this.currentNodeName;
    }

    public String getCurrentNodeCode() {
        return this.currentNodeCode;
    }

    public String getCurrentTaskInstanceId() {
        return this.currentTaskInstanceId;
    }

    public Long getCurrentNodeId() {
        return this.currentNodeId;
    }

    public Map<String, String> getVariables() {
        return this.variables;
    }

    public InstanceQueryResponse setCurrentNodeName(String currentNodeName) {
        this.currentNodeName = currentNodeName;
        return this;
    }

    public InstanceQueryResponse setCurrentNodeCode(String currentNodeCode) {
        this.currentNodeCode = currentNodeCode;
        return this;
    }

    public InstanceQueryResponse setCurrentTaskInstanceId(String currentTaskInstanceId) {
        this.currentTaskInstanceId = currentTaskInstanceId;
        return this;
    }

    public InstanceQueryResponse setCurrentNodeId(Long currentNodeId) {
        this.currentNodeId = currentNodeId;
        return this;
    }

    public InstanceQueryResponse setVariables(Map<String, String> variables) {
        this.variables = variables;
        return this;
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstanceQueryResponse)) {
            return false;
        }
        InstanceQueryResponse other = (InstanceQueryResponse)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$currentNodeName = this.getCurrentNodeName();
        String other$currentNodeName = other.getCurrentNodeName();
        if (this$currentNodeName == null ? other$currentNodeName != null : !this$currentNodeName.equals(other$currentNodeName)) {
            return false;
        }
        String this$currentNodeCode = this.getCurrentNodeCode();
        String other$currentNodeCode = other.getCurrentNodeCode();
        if (this$currentNodeCode == null ? other$currentNodeCode != null : !this$currentNodeCode.equals(other$currentNodeCode)) {
            return false;
        }
        String this$currentTaskInstanceId = this.getCurrentTaskInstanceId();
        String other$currentTaskInstanceId = other.getCurrentTaskInstanceId();
        if (this$currentTaskInstanceId == null ? other$currentTaskInstanceId != null : !this$currentTaskInstanceId.equals(other$currentTaskInstanceId)) {
            return false;
        }
        Long this$currentNodeId = this.getCurrentNodeId();
        Long other$currentNodeId = other.getCurrentNodeId();
        if (this$currentNodeId == null ? other$currentNodeId != null : !(this$currentNodeId).equals(other$currentNodeId)) {
            return false;
        }
        Map<String, String> this$variables = this.getVariables();
        Map<String, String> other$variables = other.getVariables();
        return !(this$variables == null ? other$variables != null : !(this$variables).equals(other$variables));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof InstanceQueryResponse;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $currentNodeName = this.getCurrentNodeName();
        result = result * 59 + ($currentNodeName == null ? 43 : $currentNodeName.hashCode());
        String $currentNodeCode = this.getCurrentNodeCode();
        result = result * 59 + ($currentNodeCode == null ? 43 : $currentNodeCode.hashCode());
        String $currentTaskInstanceId = this.getCurrentTaskInstanceId();
        result = result * 59 + ($currentTaskInstanceId == null ? 43 : $currentTaskInstanceId.hashCode());
        Long $currentNodeId = this.getCurrentNodeId();
        result = result * 59 + ($currentNodeId == null ? 43 : ($currentNodeId).hashCode());
        Map<String, String> $variables = this.getVariables();
        result = result * 59 + ($variables == null ? 43 : ($variables).hashCode());
        return result;
    }

    @Override
    public String toString() {
        return "InstanceQueryResponse(super=" + super.toString() + ", currentNodeName=" + this.getCurrentNodeName() + ", currentNodeCode=" + this.getCurrentNodeCode() + ", currentTaskInstanceId=" + this.getCurrentTaskInstanceId() + ", currentNodeId=" + this.getCurrentNodeId() + ", variables=" + this.getVariables() + ")";
    }
}

