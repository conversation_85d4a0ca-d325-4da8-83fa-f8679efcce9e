/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 */
package com.wacai.trike.mirana.web.model;

import com.fasterxml.jackson.annotation.JsonInclude;
import java.util.List;

@JsonInclude(value=JsonInclude.Include.NON_NULL)
public class GenericTemplateRequestModel {
    private Long id;
    private String name;
    private Long genericActionId;
    private Long actionId;
    private List<VariableValue> varList;

    public Long getId() {
        return this.id;
    }

    public String getName() {
        return this.name;
    }

    public Long getGenericActionId() {
        return this.genericActionId;
    }

    public Long getActionId() {
        return this.actionId;
    }

    public List<VariableValue> getVarList() {
        return this.varList;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setGenericActionId(Long genericActionId) {
        this.genericActionId = genericActionId;
    }

    public void setActionId(Long actionId) {
        this.actionId = actionId;
    }

    public void setVarList(List<VariableValue> varList) {
        this.varList = varList;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof GenericTemplateRequestModel)) {
            return false;
        }
        GenericTemplateRequestModel other = (GenericTemplateRequestModel)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        Long this$genericActionId = this.getGenericActionId();
        Long other$genericActionId = other.getGenericActionId();
        if (this$genericActionId == null ? other$genericActionId != null : !(this$genericActionId).equals(other$genericActionId)) {
            return false;
        }
        Long this$actionId = this.getActionId();
        Long other$actionId = other.getActionId();
        if (this$actionId == null ? other$actionId != null : !(this$actionId).equals(other$actionId)) {
            return false;
        }
        List<VariableValue> this$varList = this.getVarList();
        List<VariableValue> other$varList = other.getVarList();
        return !(this$varList == null ? other$varList != null : !(this$varList).equals(other$varList));
    }

    protected boolean canEqual(Object other) {
        return other instanceof GenericTemplateRequestModel;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        Long $genericActionId = this.getGenericActionId();
        result = result * 59 + ($genericActionId == null ? 43 : ($genericActionId).hashCode());
        Long $actionId = this.getActionId();
        result = result * 59 + ($actionId == null ? 43 : ($actionId).hashCode());
        List<VariableValue> $varList = this.getVarList();
        result = result * 59 + ($varList == null ? 43 : ($varList).hashCode());
        return result;
    }

    public String toString() {
        return "GenericTemplateRequestModel(id=" + this.getId() + ", name=" + this.getName() + ", genericActionId=" + this.getGenericActionId() + ", actionId=" + this.getActionId() + ", varList=" + this.getVarList() + ")";
    }

    public static class VariableValue {
        private Long variableId;
        private String value;

        public Long getVariableId() {
            return this.variableId;
        }

        public String getValue() {
            return this.value;
        }

        public void setVariableId(Long variableId) {
            this.variableId = variableId;
        }

        public void setValue(String value) {
            this.value = value;
        }

        public boolean equals(Object o) {
            if (o == this) {
                return true;
            }
            if (!(o instanceof VariableValue)) {
                return false;
            }
            VariableValue other = (VariableValue)o;
            if (!other.canEqual(this)) {
                return false;
            }
            Long this$variableId = this.getVariableId();
            Long other$variableId = other.getVariableId();
            if (this$variableId == null ? other$variableId != null : !(this$variableId).equals(other$variableId)) {
                return false;
            }
            String this$value = this.getValue();
            String other$value = other.getValue();
            return !(this$value == null ? other$value != null : !this$value.equals(other$value));
        }

        protected boolean canEqual(Object other) {
            return other instanceof VariableValue;
        }

        public int hashCode() {
            int PRIME = 59;
            int result = 1;
            Long $variableId = this.getVariableId();
            result = result * 59 + ($variableId == null ? 43 : ($variableId).hashCode());
            String $value = this.getValue();
            result = result * 59 + ($value == null ? 43 : $value.hashCode());
            return result;
        }

        public String toString() {
            return "GenericTemplateRequestModel.VariableValue(variableId=" + this.getVariableId() + ", value=" + this.getValue() + ")";
        }
    }
}

