/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.context.po;

import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.task.TaskStatus;
import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="task_instance")
public class TaskInstancePO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private String instanceUuid;
    private String uuid;
    private Long nodeId;
    @Enumerated(value=EnumType.STRING)
    private TaskStatus status;
    private String remark;
    private LocalDateTime startTime;
    private LocalDateTime endTime;
    private Boolean esSync;

    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    public String getUuid() {
        return this.uuid;
    }

    public Long getNodeId() {
        return this.nodeId;
    }

    public TaskStatus getStatus() {
        return this.status;
    }

    public String getRemark() {
        return this.remark;
    }

    public LocalDateTime getStartTime() {
        return this.startTime;
    }

    public LocalDateTime getEndTime() {
        return this.endTime;
    }

    public Boolean getEsSync() {
        return this.esSync;
    }

    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setNodeId(Long nodeId) {
        this.nodeId = nodeId;
    }

    public void setStatus(TaskStatus status) {
        this.status = status;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public void setStartTime(LocalDateTime startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(LocalDateTime endTime) {
        this.endTime = endTime;
    }

    public void setEsSync(Boolean esSync) {
        this.esSync = esSync;
    }

    @Override
    public String toString() {
        return "TaskInstancePO(instanceUuid=" + this.getInstanceUuid() + ", uuid=" + this.getUuid() + ", nodeId=" + this.getNodeId() + ", status=" + (this.getStatus()) + ", remark=" + this.getRemark() + ", startTime=" + this.getStartTime() + ", endTime=" + this.getEndTime() + ", esSync=" + this.getEsSync() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof TaskInstancePO)) {
            return false;
        }
        TaskInstancePO other = (TaskInstancePO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$instanceUuid = this.getInstanceUuid();
        String other$instanceUuid = other.getInstanceUuid();
        if (this$instanceUuid == null ? other$instanceUuid != null : !this$instanceUuid.equals(other$instanceUuid)) {
            return false;
        }
        String this$uuid = this.getUuid();
        String other$uuid = other.getUuid();
        if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
            return false;
        }
        Long this$nodeId = this.getNodeId();
        Long other$nodeId = other.getNodeId();
        if (this$nodeId == null ? other$nodeId != null : !(this$nodeId).equals(other$nodeId)) {
            return false;
        }
        TaskStatus this$status = this.getStatus();
        TaskStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !((this$status)).equals(other$status)) {
            return false;
        }
        String this$remark = this.getRemark();
        String other$remark = other.getRemark();
        if (this$remark == null ? other$remark != null : !this$remark.equals(other$remark)) {
            return false;
        }
        LocalDateTime this$startTime = this.getStartTime();
        LocalDateTime other$startTime = other.getStartTime();
        if (this$startTime == null ? other$startTime != null : !(this$startTime).equals(other$startTime)) {
            return false;
        }
        LocalDateTime this$endTime = this.getEndTime();
        LocalDateTime other$endTime = other.getEndTime();
        if (this$endTime == null ? other$endTime != null : !(this$endTime).equals(other$endTime)) {
            return false;
        }
        Boolean this$esSync = this.getEsSync();
        Boolean other$esSync = other.getEsSync();
        return !(this$esSync == null ? other$esSync != null : !(this$esSync).equals(other$esSync));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof TaskInstancePO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $instanceUuid = this.getInstanceUuid();
        result = result * 59 + ($instanceUuid == null ? 43 : $instanceUuid.hashCode());
        String $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        Long $nodeId = this.getNodeId();
        result = result * 59 + ($nodeId == null ? 43 : ($nodeId).hashCode());
        TaskStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
        String $remark = this.getRemark();
        result = result * 59 + ($remark == null ? 43 : $remark.hashCode());
        LocalDateTime $startTime = this.getStartTime();
        result = result * 59 + ($startTime == null ? 43 : ($startTime).hashCode());
        LocalDateTime $endTime = this.getEndTime();
        result = result * 59 + ($endTime == null ? 43 : ($endTime).hashCode());
        Boolean $esSync = this.getEsSync();
        result = result * 59 + ($esSync == null ? 43 : ($esSync).hashCode());
        return result;
    }
}

