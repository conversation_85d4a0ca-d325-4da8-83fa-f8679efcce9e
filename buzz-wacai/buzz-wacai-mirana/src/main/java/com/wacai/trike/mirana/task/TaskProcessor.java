/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.TaskType
 */
package com.wacai.trike.mirana.task;

import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.task.TaskExecuteException;
import java.util.Map;

public interface TaskProcessor {
    public boolean accept(TaskType var1);

    public Map<String, String> execute(Node var1, InstanceContext var2) throws TaskExecuteException;
}

