/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Service
 */
package com.wacai.trike.mirana.domain.bu;

import com.wacai.trike.mirana.domain.bu.ApplicationService;
import com.wacai.trike.mirana.domain.bu.dto.ApplicationDto;
import com.wacai.trike.mirana.domain.bu.po.ApplicationPO;
import com.wacai.trike.mirana.domain.bu.repository.ApplicationRepository;
import com.wacai.trike.mirana.util.ObjectUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

@Service
public class ApplicationServiceImplement
implements ApplicationService {
    private static final Logger log = LoggerFactory.getLogger(ApplicationServiceImplement.class);
    @Autowired
    private ApplicationRepository applicationRepository;

    @Override
    public List<ApplicationDto> batch() {
        return this.applicationRepository.findAll().stream().map(po -> ObjectUtils.convert(po, ApplicationDto.class)).filter(Objects::nonNull).collect(Collectors.toList());
    }

    @Override
    public ApplicationDto query(Long id) {
        ApplicationPO applicationPO = this.applicationRepository.findById(id).orElse(null);
        if (applicationPO == null) {
            return null;
        }
        return ObjectUtils.convert(applicationPO, ApplicationDto.class);
    }
}

