/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  org.springframework.web.bind.annotation.GetMapping
 *  org.springframework.web.bind.annotation.RequestMapping
 *  org.springframework.web.bind.annotation.RequestParam
 */
package com.wacai.trike.mirana.api.manage;

import com.wacai.loan.trike.common.model.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;

@RequestMapping(value={"/application"})
public interface ApplicationService {
	
	
    @GetMapping(value={"/appId"})
    public ApiResponse<Long> queryAppId(@RequestParam(value="app") String var1, @RequestParam(value="app") String var2);
}

