/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.google.common.collect.Lists
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 *  org.springframework.util.CollectionUtils
 */
package com.wacai.trike.mirana.annos;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import com.wacai.trike.mirana.Seraph.SeraphSender;
import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.context.InstanceContextService;
import com.wacai.trike.mirana.domain.event.po.DelayedEventPO;
import com.wacai.trike.mirana.domain.event.repository.DelayedEventRepository;
import com.wacai.trike.mirana.event.CancelInstanceEvent;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.ManualEndingEvent;
import com.wacai.trike.mirana.graph.FlowGraph;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.metrics.FlowMetrics;
import com.wacai.trike.mirana.metrics.MetricsProvider;
import com.wacai.trike.mirana.notify.NotifyService;
import com.wacai.trike.mirana.task.TaskStatus;
import com.wacai.trike.mirana.util.ObjectMappers;

@Component
public class InstanceCancelHandler
		implements MethodInvocationHandler {
	
	private static final Logger log = LoggerFactory.getLogger(InstanceCancelHandler.class);
	@Autowired
	private InstanceContextService contextService;
	@Autowired
	private FlowGraphService graphService;
	@Autowired
	private NotifyService notifyService;
	@Autowired
	private MetricsProvider metricsProvider;
	@Autowired
	private SeraphSender seraphSender;
	@Autowired
	private DelayedEventRepository eventRepository;

	@Override
	public int order() {
		return 4;
	}

	@Override
	public void postHandle(MethodInvocation invocation) {
		if (!this.sanity(invocation)) {
			return;
		}
		EventExchange exchange = (EventExchange) invocation.getArgs()[0];
		CancelInstanceEvent event = (CancelInstanceEvent) exchange.getCurrent();
		InstanceContext context = exchange.getContext();
		this.cancelDelayEvents(event.getInstanceUuid());
		this.contextService.cancelContextFuture(event.getInstanceUuid());
		this.contextService.removeCache(context.getUuid());
		context.setModifier(event.getOperator());
		context.addVariable(event.getParam(), VariableType.MANUAL_INPUT);
		context.setDirty(true);
		if (Objects.nonNull(context.getCurrentTaskInstance())) {
			context.getCurrentTaskInstance().setStatus(TaskStatus.STOPPED);
			context.getCurrentTaskInstance().setEndTime(LocalDateTime.now());
			context.getCurrentTaskInstance().setOperator(event.getOperator());
			context.getCurrentTaskInstance().setDirty(true);
		}
		FlowGraph graph = this.graphService.getFlowGraph(context.getFlowId());
		this.notifyService.notifyFlowEnd(context.copy());
		this.metricsProvider.metricsFlow(FlowMetrics.build(graph, context, context.getStatus().name().toLowerCase()));
	}

	private void cancelDelayEvents(String instanceUuid) {
		List<DelayedEventPO> events = this.eventRepository.findByInstanceUuidAndStatusIn(instanceUuid,
				Arrays.asList(DelayedEventStatus.WAITING, DelayedEventStatus.LOADED));

		if (CollectionUtils.isEmpty(events)) {
			return;
		}
		events.forEach(e -> e.setStatus(DelayedEventStatus.CANCELED));
		this.eventRepository.saveAll(events);
	}

	private boolean sanity(MethodInvocation invocation) {
		Object[] args = invocation.getArgs();
		if (args == null || args.length != 1 || !(args[0] instanceof EventExchange)) {
			return false;
		}
		EventExchange exchange = (EventExchange) args[0];
		if (exchange.getCurrent() == null || exchange.getContext() == null) {
			return false;
		}
		if (!(exchange.getCurrent() instanceof ManualEndingEvent)
				&& !(exchange.getCurrent() instanceof CancelInstanceEvent)) {
			log.info("Non-Expected ManualEndingEvent/CancelInstanceEvent {}",
					 ObjectMappers.mustWriteValue(args[0]));
			return false;
		}
		CancelInstanceEvent current = (CancelInstanceEvent) exchange.getCurrent();
		if (current.getExpectedTime() != null && current.getExpectedTime().isBefore(LocalDateTime.now())) {
			log.error("should never occurred: escaped one event {}",  ObjectMappers.mustWriteValue(current));
			return false;
		}
		return true;
	}
}
