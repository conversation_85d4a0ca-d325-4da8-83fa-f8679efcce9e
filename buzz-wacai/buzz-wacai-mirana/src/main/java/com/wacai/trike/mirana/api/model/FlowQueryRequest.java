/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.validation.constraints.NotBlank
 */
package com.wacai.trike.mirana.api.model;

import java.io.Serializable;
import javax.validation.constraints.NotBlank;

public class FlowQueryRequest
implements Serializable {
    @NotBlank
    private String bu;
    @NotBlank
    private String app;
    private String type;
    private String code;

    public String getBu() {
        return this.bu;
    }

    public String getApp() {
        return this.app;
    }

    public String getType() {
        return this.type;
    }

    public String getCode() {
        return this.code;
    }

    public void setBu(String bu) {
        this.bu = bu;
    }

    public void setApp(String app) {
        this.app = app;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowQueryRequest)) {
            return false;
        }
        FlowQueryRequest other = (FlowQueryRequest)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$bu = this.getBu();
        String other$bu = other.getBu();
        if (this$bu == null ? other$bu != null : !this$bu.equals(other$bu)) {
            return false;
        }
        String this$app = this.getApp();
        String other$app = other.getApp();
        if (this$app == null ? other$app != null : !this$app.equals(other$app)) {
            return false;
        }
        String this$type = this.getType();
        String other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        return !(this$code == null ? other$code != null : !this$code.equals(other$code));
    }

    protected boolean canEqual(Object other) {
        return other instanceof FlowQueryRequest;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $bu = this.getBu();
        result = result * 59 + ($bu == null ? 43 : $bu.hashCode());
        String $app = this.getApp();
        result = result * 59 + ($app == null ? 43 : $app.hashCode());
        String $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        return result;
    }

    public String toString() {
        return "FlowQueryRequest(bu=" + this.getBu() + ", app=" + this.getApp() + ", type=" + this.getType() + ", code=" + this.getCode() + ")";
    }
}

