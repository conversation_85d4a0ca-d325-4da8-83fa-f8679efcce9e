/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.elasticsearch.service.dto;

import com.wacai.trike.mirana.elasticsearch.service.dto.EsFlowTaskInstanceEventDTO;
import java.util.List;

public class EsFlowTaskInstanceUpdateDTO {
    private String flowInstanceId;
    private Long flowId;
    private String flowVersion;
    private List<Long> nodeIdList;
    private String storyCode;
    private List<EsFlowTaskInstanceEventDTO> eventList;

    public String getFlowInstanceId() {
        return this.flowInstanceId;
    }

    public Long getFlowId() {
        return this.flowId;
    }

    public String getFlowVersion() {
        return this.flowVersion;
    }

    public List<Long> getNodeIdList() {
        return this.nodeIdList;
    }

    public String getStoryCode() {
        return this.storyCode;
    }

    public List<EsFlowTaskInstanceEventDTO> getEventList() {
        return this.eventList;
    }

    public void setFlowInstanceId(String flowInstanceId) {
        this.flowInstanceId = flowInstanceId;
    }

    public void setFlowId(Long flowId) {
        this.flowId = flowId;
    }

    public void setFlowVersion(String flowVersion) {
        this.flowVersion = flowVersion;
    }

    public void setNodeIdList(List<Long> nodeIdList) {
        this.nodeIdList = nodeIdList;
    }

    public void setStoryCode(String storyCode) {
        this.storyCode = storyCode;
    }

    public void setEventList(List<EsFlowTaskInstanceEventDTO> eventList) {
        this.eventList = eventList;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof EsFlowTaskInstanceUpdateDTO)) {
            return false;
        }
        EsFlowTaskInstanceUpdateDTO other = (EsFlowTaskInstanceUpdateDTO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        String this$flowInstanceId = this.getFlowInstanceId();
        String other$flowInstanceId = other.getFlowInstanceId();
        if (this$flowInstanceId == null ? other$flowInstanceId != null : !this$flowInstanceId.equals(other$flowInstanceId)) {
            return false;
        }
        Long this$flowId = this.getFlowId();
        Long other$flowId = other.getFlowId();
        if (this$flowId == null ? other$flowId != null : !(this$flowId).equals(other$flowId)) {
            return false;
        }
        String this$flowVersion = this.getFlowVersion();
        String other$flowVersion = other.getFlowVersion();
        if (this$flowVersion == null ? other$flowVersion != null : !this$flowVersion.equals(other$flowVersion)) {
            return false;
        }
        List<Long> this$nodeIdList = this.getNodeIdList();
        List<Long> other$nodeIdList = other.getNodeIdList();
        if (this$nodeIdList == null ? other$nodeIdList != null : !(this$nodeIdList).equals(other$nodeIdList)) {
            return false;
        }
        String this$storyCode = this.getStoryCode();
        String other$storyCode = other.getStoryCode();
        if (this$storyCode == null ? other$storyCode != null : !this$storyCode.equals(other$storyCode)) {
            return false;
        }
        List<EsFlowTaskInstanceEventDTO> this$eventList = this.getEventList();
        List<EsFlowTaskInstanceEventDTO> other$eventList = other.getEventList();
        return !(this$eventList == null ? other$eventList != null : !(this$eventList).equals(other$eventList));
    }

    protected boolean canEqual(Object other) {
        return other instanceof EsFlowTaskInstanceUpdateDTO;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        String $flowInstanceId = this.getFlowInstanceId();
        result = result * 59 + ($flowInstanceId == null ? 43 : $flowInstanceId.hashCode());
        Long $flowId = this.getFlowId();
        result = result * 59 + ($flowId == null ? 43 : ($flowId).hashCode());
        String $flowVersion = this.getFlowVersion();
        result = result * 59 + ($flowVersion == null ? 43 : $flowVersion.hashCode());
        List<Long> $nodeIdList = this.getNodeIdList();
        result = result * 59 + ($nodeIdList == null ? 43 : ($nodeIdList).hashCode());
        String $storyCode = this.getStoryCode();
        result = result * 59 + ($storyCode == null ? 43 : $storyCode.hashCode());
        List<EsFlowTaskInstanceEventDTO> $eventList = this.getEventList();
        result = result * 59 + ($eventList == null ? 43 : ($eventList).hashCode());
        return result;
    }

    public String toString() {
        return "EsFlowTaskInstanceUpdateDTO(flowInstanceId=" + this.getFlowInstanceId() + ", flowId=" + this.getFlowId() + ", flowVersion=" + this.getFlowVersion() + ", nodeIdList=" + this.getNodeIdList() + ", storyCode=" + this.getStoryCode() + ", eventList=" + this.getEventList() + ")";
    }
}

