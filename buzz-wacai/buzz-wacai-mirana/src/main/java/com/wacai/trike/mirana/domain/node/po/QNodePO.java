/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 *  com.wacai.trike.mirana.api.constant.NodeType
 *  com.wacai.trike.mirana.api.constant.TaskExecuteType
 *  com.wacai.trike.mirana.api.constant.TaskType
 */
package com.wacai.trike.mirana.domain.node.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.api.constant.NodeType;
import com.wacai.trike.mirana.api.constant.TaskExecuteType;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.delay.ScheduleType;
import com.wacai.trike.mirana.domain.node.po.NodePO;
import java.time.LocalDateTime;

public class QNodePO
extends EntityPathBase<NodePO> {
    private static final long serialVersionUID = 1208017121L;
    public static final QNodePO nodePO = new QNodePO("nodePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final NumberPath<Long> actionTemplateId = this.createNumber("actionTemplateId", Long.class);
    public final BooleanPath active;
    public final NumberPath<Long> appId;
    public final StringPath code;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final NumberPath<Long> flowId;
    public final NumberPath<Long> id;
    public final StringPath name;
    public final StringPath scheduleContent;
    public final EnumPath<ScheduleType> scheduleType;
    public final StringPath taskContent;
    public final EnumPath<TaskExecuteType> taskExecuteType;
    public final NumberPath<Integer> taskTimeout;
    public final EnumPath<TaskType> taskType;
    public final EnumPath<NodeType> type;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final NumberPath<Double> x;
    public final NumberPath<Double> y;

    public QNodePO(String variable) {
        super(NodePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.scheduleContent = this.createString("scheduleContent");
        this.scheduleType = this.createEnum("scheduleType", ScheduleType.class);
        this.taskContent = this.createString("taskContent");
        this.taskExecuteType = this.createEnum("taskExecuteType", TaskExecuteType.class);
        this.taskTimeout = this.createNumber("taskTimeout", Integer.class);
        this.taskType = this.createEnum("taskType", TaskType.class);
        this.type = this.createEnum("type", NodeType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.x = this.createNumber("x", Double.class);
        this.y = this.createNumber("y", Double.class);
    }

    public QNodePO(Path<? extends NodePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.scheduleContent = this.createString("scheduleContent");
        this.scheduleType = this.createEnum("scheduleType", ScheduleType.class);
        this.taskContent = this.createString("taskContent");
        this.taskExecuteType = this.createEnum("taskExecuteType", TaskExecuteType.class);
        this.taskTimeout = this.createNumber("taskTimeout", Integer.class);
        this.taskType = this.createEnum("taskType", TaskType.class);
        this.type = this.createEnum("type", NodeType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.x = this.createNumber("x", Double.class);
        this.y = this.createNumber("y", Double.class);
    }

    public QNodePO(PathMetadata metadata) {
        super(NodePO.class, metadata);
        this.active = this._super.active;
        this.appId = this.createNumber("appId", Long.class);
        this.code = this.createString("code");
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.flowId = this.createNumber("flowId", Long.class);
        this.id = this._super.id;
        this.name = this.createString("name");
        this.scheduleContent = this.createString("scheduleContent");
        this.scheduleType = this.createEnum("scheduleType", ScheduleType.class);
        this.taskContent = this.createString("taskContent");
        this.taskExecuteType = this.createEnum("taskExecuteType", TaskExecuteType.class);
        this.taskTimeout = this.createNumber("taskTimeout", Integer.class);
        this.taskType = this.createEnum("taskType", TaskType.class);
        this.type = this.createEnum("type", NodeType.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.x = this.createNumber("x", Double.class);
        this.y = this.createNumber("y", Double.class);
    }
}

