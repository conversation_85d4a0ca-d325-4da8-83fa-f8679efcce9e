/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.fasterxml.jackson.annotation.JsonInclude$Include
 *  com.fasterxml.jackson.core.type.TypeReference
 *  com.fasterxml.jackson.databind.DeserializationFeature
 *  com.fasterxml.jackson.databind.JsonNode
 *  com.fasterxml.jackson.databind.ObjectMapper
 *  javax.annotation.Nullable
 */
package com.wacai.trike.mirana.util;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import javax.annotation.Nullable;

public class ObjectMappers {
    private static final ObjectMapper DEFAULT_INSTANCE = new ObjectMapper();

    public static ObjectMapper get() {
        return DEFAULT_INSTANCE;
    }

    public static <T> T mustReadValue(@Nullable String json, Class<T> clazz) {
        if (json == null) {
            return null;
        }
        try {
            return (T)DEFAULT_INSTANCE.readValue(json, clazz);
        }
        catch (IOException e) {
            throw new DataFormatException(e);
        }
    }

    public static <T> T mustReadValue(@Nullable String json, TypeReference<T> typeRef) {
        if (json == null) {
            return null;
        }
        try {
            return (T)DEFAULT_INSTANCE.readValue(json, typeRef);
        }
        catch (IOException e) {
            throw new DataFormatException(e);
        }
    }

    public static String mustWriteValue(@Nullable Object o) {
        if (o == null) {
            return null;
        }
        try {
            return DEFAULT_INSTANCE.writeValueAsString(o);
        }
        catch (IOException e) {
            throw new DataFormatException(e);
        }
    }

    public static String mustWriteValuePretty(@Nullable Object o) {
        if (o == null) {
            return null;
        }
        try {
            return DEFAULT_INSTANCE.writerWithDefaultPrettyPrinter().writeValueAsString(o);
        }
        catch (IOException e) {
            throw new DataFormatException(e);
        }
    }

    public static JsonNode mustReadTree(@Nullable String json) {
        if (json == null) {
            return null;
        }
        try {
            return DEFAULT_INSTANCE.readTree(json);
        }
        catch (IOException e) {
            throw new DataFormatException(e);
        }
    }

    static {
        DEFAULT_INSTANCE.setSerializationInclusion(JsonInclude.Include.NON_NULL);
        DEFAULT_INSTANCE.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        DEFAULT_INSTANCE.configure(DeserializationFeature.READ_UNKNOWN_ENUM_VALUES_AS_NULL, true);
    }

    public static class DataFormatException
    extends RuntimeException {
        public DataFormatException(Throwable t) {
            super(t);
        }
    }
}

