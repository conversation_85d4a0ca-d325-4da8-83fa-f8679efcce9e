/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.dubbo.config.annotation.Service
 *  com.querydsl.core.types.EntityPath
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.jpa.impl.JPAQuery
 *  com.querydsl.jpa.impl.JPAQueryFactory
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  com.wacai.trike.mirana.api.manage.ApplicationService
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.validation.annotation.Validated
 *  org.springframework.web.bind.annotation.RequestParam
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.api.impl.manage;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.dubbo.config.annotation.Service;
import com.querydsl.core.types.EntityPath;
import com.querydsl.core.types.Predicate;
import com.querydsl.jpa.impl.JPAQuery;
import com.querydsl.jpa.impl.JPAQueryFactory;
import com.wacai.loan.trike.common.model.ApiResponse;
import com.wacai.trike.mirana.api.manage.ApplicationService;
import com.wacai.trike.mirana.domain.bu.po.QApplicationPO;
import com.wacai.trike.mirana.domain.bu.po.QBusinessPO;

@RestController
@Service(interfaceClass = ApplicationService.class)
@Validated
public class ApplicationServiceImpl implements ApplicationService {

	@Autowired
	private JPAQueryFactory queryFactory;

	@SuppressWarnings({ "rawtypes", "unchecked" })
	public ApiResponse<Long> queryAppId(@RequestParam(value = "app") String app,
			@RequestParam(value = "app") String bu) {
		Long appId = (Long) ((JPAQuery) this.queryFactory
				.select(QApplicationPO.applicationPO.id).from((EntityPath) QApplicationPO.applicationPO)
				.join((EntityPath) QBusinessPO.businessPO)
				.on((Predicate) QApplicationPO.applicationPO.buId.eq(QBusinessPO.businessPO.id))
						.where((Predicate) QApplicationPO.applicationPO.code.eq(app)
								.and((Predicate) QBusinessPO.businessPO.code.eq(bu))))
										.fetchOne();
		return ApiResponse.success(appId);
	}
}
