/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import java.time.LocalDateTime;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.task.TaskStatus;
import com.wacai.trike.mirana.util.DistributeSequenceIdGenerator;

@Component
public class NodeProcessedEventListener
implements EventListener<NodeProcessedEvent, NodeFinishedEvent> {
    
    @Autowired
    private DistributeSequenceIdGenerator idGenerator;

    @Override
    public Class<NodeProcessedEvent> accept() {
        return NodeProcessedEvent.class;
    }

    @Override
    public void doProcess(EventExchange<NodeProcessedEvent, NodeFinishedEvent> exchange) {
        InstanceContext context = exchange.getContext();
        NodeProcessedEvent event = exchange.getCurrent();
        context.getCurrentTaskInstance().setStatus(TaskStatus.COMPLETED);
        context.getCurrentTaskInstance().setEndTime(LocalDateTime.now());
        context.getCurrentTaskInstance().setDirty(true);
        exchange.setNext(new NodeFinishedEvent()
        		.setUuid(Long.toString(this.idGenerator.nextId()))
        		.setInstanceUuid(event.getInstanceUuid())
        		.setTaskId(context.getCurrentTaskInstance().getUuid())
        		.setOperator(event.getOperator()));
    }

    public int getOrder() {
        return 4;
    }
}

