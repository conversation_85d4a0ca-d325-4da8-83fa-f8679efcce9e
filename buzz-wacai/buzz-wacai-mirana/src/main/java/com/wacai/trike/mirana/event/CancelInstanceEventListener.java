/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  org.apache.logging.log4j.LogManager
 *  org.apache.logging.log4j.Logger
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.Seraph.SeraphSender;
import com.wacai.trike.mirana.annos.Barrier;
import com.wacai.trike.mirana.annos.InstanceCancelHandler;
import com.wacai.trike.mirana.annos.InstanceContextSyncHandler;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.CancelInstanceEvent;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.EventListener;
import java.time.LocalDateTime;
import org.apache.logging.log4j.LogManager;
import org.apache.logging.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class CancelInstanceEventListener
implements EventListener<CancelInstanceEvent, AbstractFlowEvent> {
    private static final Logger log = LogManager.getLogger(CancelInstanceEventListener.class);
    @Autowired
    private SeraphSender seraphSender;

    @Override
    public Class<CancelInstanceEvent> accept() {
        return CancelInstanceEvent.class;
    }

    @Override
    @Barrier(handlers={InstanceCancelHandler.class, InstanceContextSyncHandler.class})
    public void doProcess(EventExchange<CancelInstanceEvent, AbstractFlowEvent> exchange) {
        CancelInstanceEvent event = exchange.getCurrent();
        if (event.getExpectedTime() != null && event.getExpectedTime().isAfter(LocalDateTime.now())) {
            this.seraphSender.publishDelayEvent(new SeraphSender.Metadata(event.getExpectedTime(), event));
            log.info("will cancel instance {} operator {} at {} support by delay event", event.getInstanceUuid(), event.getOperator(), event.getExpectedTime().toString());
            return;
        }
        exchange.getContext().setStatus(InstanceStatus.CANCELED);
    }

    public int getOrder() {
        return 10;
    }
}

