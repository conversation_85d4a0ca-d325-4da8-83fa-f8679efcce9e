/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.NodeType
 *  com.wacai.trike.mirana.api.constant.TaskExecuteType
 *  com.wacai.trike.mirana.api.constant.TaskType
 */
package com.wacai.trike.mirana.graph;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import com.wacai.trike.mirana.api.constant.NodeType;
import com.wacai.trike.mirana.api.constant.TaskExecuteType;
import com.wacai.trike.mirana.api.constant.TaskType;
import com.wacai.trike.mirana.delay.DelayCalculator;
import com.wacai.trike.mirana.delay.ScheduleType;

public class Node implements Serializable {
    private Long id;
    private String code;
    private String name;
    private NodeType type;
    private TaskType taskType = TaskType.USER;
    private TaskExecuteType taskExecuteType;
    private String taskContent;
    private Integer taskTimeout;
    private ScheduleType scheduleType;
    private String scheduleContent;
    private List<Edge> edges = new ArrayList<Edge>();
    private Map<String, String> inputForms = new HashMap<String, String>();
    private Map<String, String> outputForms = new HashMap<String, String>();
    private Action action;
    private Long templateId;
    private String subflowCode;
    private boolean init;

    public boolean delayNotConfig() {
        return Objects.isNull(this.getScheduleContent()) || Objects.isNull(this.getScheduleType());
    }

    public DelayCalculator getDelayCalculator() {
        if (this.delayNotConfig()) {
            return null;
        }
        return (DelayCalculator)this.getScheduleType().parse(this.getScheduleContent());
    }

    public Long getId() {
        return this.id;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public NodeType getType() {
        return this.type;
    }

    public TaskType getTaskType() {
        return this.taskType;
    }

    public TaskExecuteType getTaskExecuteType() {
        return this.taskExecuteType;
    }

    public String getTaskContent() {
        return this.taskContent;
    }

    public Integer getTaskTimeout() {
        return this.taskTimeout;
    }

    public ScheduleType getScheduleType() {
        return this.scheduleType;
    }

    public String getScheduleContent() {
        return this.scheduleContent;
    }

    public List<Edge> getEdges() {
        return this.edges;
    }

    public Map<String, String> getInputForms() {
        return this.inputForms;
    }

    public Map<String, String> getOutputForms() {
        return this.outputForms;
    }

    public Action getAction() {
        return this.action;
    }

    public Long getTemplateId() {
        return this.templateId;
    }

    public String getSubflowCode() {
        return this.subflowCode;
    }

    public boolean isInit() {
        return this.init;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setType(NodeType type) {
        this.type = type;
    }

    public void setTaskType(TaskType taskType) {
        this.taskType = taskType;
    }

    public void setTaskExecuteType(TaskExecuteType taskExecuteType) {
        this.taskExecuteType = taskExecuteType;
    }

    public void setTaskContent(String taskContent) {
        this.taskContent = taskContent;
    }

    public void setTaskTimeout(Integer taskTimeout) {
        this.taskTimeout = taskTimeout;
    }

    public void setScheduleType(ScheduleType scheduleType) {
        this.scheduleType = scheduleType;
    }

    public void setScheduleContent(String scheduleContent) {
        this.scheduleContent = scheduleContent;
    }

    public void setEdges(List<Edge> edges) {
        this.edges = edges;
    }

    public void setInputForms(Map<String, String> inputForms) {
        this.inputForms = inputForms;
    }

    public void setOutputForms(Map<String, String> outputForms) {
        this.outputForms = outputForms;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public void setTemplateId(Long templateId) {
        this.templateId = templateId;
    }

    public void setSubflowCode(String subflowCode) {
        this.subflowCode = subflowCode;
    }

    public void setInit(boolean init) {
        this.init = init;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof Node)) {
            return false;
        }
        Node other = (Node)o;
        if (!other.canEqual(this)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        NodeType this$type = this.getType();
        NodeType other$type = other.getType();
        if (this$type == null ? other$type != null : !this$type.equals(other$type)) {
            return false;
        }
        TaskType this$taskType = this.getTaskType();
        TaskType other$taskType = other.getTaskType();
        if (this$taskType == null ? other$taskType != null : !this$taskType.equals(other$taskType)) {
            return false;
        }
        TaskExecuteType this$taskExecuteType = this.getTaskExecuteType();
        TaskExecuteType other$taskExecuteType = other.getTaskExecuteType();
        if (this$taskExecuteType == null ? other$taskExecuteType != null : !this$taskExecuteType.equals(other$taskExecuteType)) {
            return false;
        }
        String this$taskContent = this.getTaskContent();
        String other$taskContent = other.getTaskContent();
        if (this$taskContent == null ? other$taskContent != null : !this$taskContent.equals(other$taskContent)) {
            return false;
        }
        Integer this$taskTimeout = this.getTaskTimeout();
        Integer other$taskTimeout = other.getTaskTimeout();
        if (this$taskTimeout == null ? other$taskTimeout != null : !(this$taskTimeout).equals(other$taskTimeout)) {
            return false;
        }
        ScheduleType this$scheduleType = this.getScheduleType();
        ScheduleType other$scheduleType = other.getScheduleType();
        if (this$scheduleType == null ? other$scheduleType != null : !((this$scheduleType)).equals(other$scheduleType)) {
            return false;
        }
        String this$scheduleContent = this.getScheduleContent();
        String other$scheduleContent = other.getScheduleContent();
        if (this$scheduleContent == null ? other$scheduleContent != null : !this$scheduleContent.equals(other$scheduleContent)) {
            return false;
        }
        List<Edge> this$edges = this.getEdges();
        List<Edge> other$edges = other.getEdges();
        if (this$edges == null ? other$edges != null : !(this$edges).equals(other$edges)) {
            return false;
        }
        Map<String, String> this$inputForms = this.getInputForms();
        Map<String, String> other$inputForms = other.getInputForms();
        if (this$inputForms == null ? other$inputForms != null : !(this$inputForms).equals(other$inputForms)) {
            return false;
        }
        Map<String, String> this$outputForms = this.getOutputForms();
        Map<String, String> other$outputForms = other.getOutputForms();
        if (this$outputForms == null ? other$outputForms != null : !(this$outputForms).equals(other$outputForms)) {
            return false;
        }
        Action this$action = this.getAction();
        Action other$action = other.getAction();
        if (this$action == null ? other$action != null : !(this$action).equals(other$action)) {
            return false;
        }
        Long this$templateId = this.getTemplateId();
        Long other$templateId = other.getTemplateId();
        if (this$templateId == null ? other$templateId != null : !(this$templateId).equals(other$templateId)) {
            return false;
        }
        String this$subflowCode = this.getSubflowCode();
        String other$subflowCode = other.getSubflowCode();
        if (this$subflowCode == null ? other$subflowCode != null : !this$subflowCode.equals(other$subflowCode)) {
            return false;
        }
        return this.isInit() == other.isInit();
    }

    protected boolean canEqual(Object other) {
        return other instanceof Node;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        NodeType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        TaskType $taskType = this.getTaskType();
        result = result * 59 + ($taskType == null ? 43 : $taskType.hashCode());
        TaskExecuteType $taskExecuteType = this.getTaskExecuteType();
        result = result * 59 + ($taskExecuteType == null ? 43 : $taskExecuteType.hashCode());
        String $taskContent = this.getTaskContent();
        result = result * 59 + ($taskContent == null ? 43 : $taskContent.hashCode());
        Integer $taskTimeout = this.getTaskTimeout();
        result = result * 59 + ($taskTimeout == null ? 43 : ($taskTimeout).hashCode());
        ScheduleType $scheduleType = this.getScheduleType();
        result = result * 59 + ($scheduleType == null ? 43 : (($scheduleType)).hashCode());
        String $scheduleContent = this.getScheduleContent();
        result = result * 59 + ($scheduleContent == null ? 43 : $scheduleContent.hashCode());
        List<Edge> $edges = this.getEdges();
        result = result * 59 + ($edges == null ? 43 : ($edges).hashCode());
        Map<String, String> $inputForms = this.getInputForms();
        result = result * 59 + ($inputForms == null ? 43 : ($inputForms).hashCode());
        Map<String, String> $outputForms = this.getOutputForms();
        result = result * 59 + ($outputForms == null ? 43 : ($outputForms).hashCode());
        Action $action = this.getAction();
        result = result * 59 + ($action == null ? 43 : ($action).hashCode());
        Long $templateId = this.getTemplateId();
        result = result * 59 + ($templateId == null ? 43 : ($templateId).hashCode());
        String $subflowCode = this.getSubflowCode();
        result = result * 59 + ($subflowCode == null ? 43 : $subflowCode.hashCode());
        result = result * 59 + (this.isInit() ? 79 : 97);
        return result;
    }

    public String toString() {
        return "Node(id=" + this.getId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", type=" + this.getType() + ", taskType=" + this.getTaskType() + ", taskExecuteType=" + this.getTaskExecuteType() + ", taskContent=" + this.getTaskContent() + ", taskTimeout=" + this.getTaskTimeout() + ", scheduleType=" + (this.getScheduleType()) + ", scheduleContent=" + this.getScheduleContent() + ", edges=" + this.getEdges() + ", inputForms=" + this.getInputForms() + ", outputForms=" + this.getOutputForms() + ", action=" + this.getAction() + ", templateId=" + this.getTemplateId() + ", subflowCode=" + this.getSubflowCode() + ", init=" + this.isInit() + ")";
    }
}

