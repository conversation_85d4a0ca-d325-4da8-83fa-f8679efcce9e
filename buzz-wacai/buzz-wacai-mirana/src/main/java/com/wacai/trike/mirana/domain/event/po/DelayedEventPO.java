/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.event.po;

import com.wacai.trike.mirana.common.enums.DelayedEventStatus;
import com.wacai.trike.mirana.common.po.BasePO;
import java.time.LocalDateTime;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="delayed_event")
public class DelayedEventPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private String uuid;
    private String instanceUuid;
    private String taskId;
    private String operator;
    private String param;
    private LocalDateTime expectFireTime;
    @Enumerated(value=EnumType.STRING)
    private DelayedEventStatus status;
    private String type;

    public String getUuid() {
        return this.uuid;
    }

    public String getInstanceUuid() {
        return this.instanceUuid;
    }

    public String getTaskId() {
        return this.taskId;
    }

    public String getOperator() {
        return this.operator;
    }

    public String getParam() {
        return this.param;
    }

    public LocalDateTime getExpectFireTime() {
        return this.expectFireTime;
    }

    public DelayedEventStatus getStatus() {
        return this.status;
    }

    public String getType() {
        return this.type;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public void setInstanceUuid(String instanceUuid) {
        this.instanceUuid = instanceUuid;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }

    public void setParam(String param) {
        this.param = param;
    }

    public void setExpectFireTime(LocalDateTime expectFireTime) {
        this.expectFireTime = expectFireTime;
    }

    public void setStatus(DelayedEventStatus status) {
        this.status = status;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "DelayedEventPO(uuid=" + this.getUuid() + ", instanceUuid=" + this.getInstanceUuid() + ", taskId=" + this.getTaskId() + ", operator=" + this.getOperator() + ", param=" + this.getParam() + ", expectFireTime=" + this.getExpectFireTime() + ", status=" + (this.getStatus()) + ", type=" + this.getType() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof DelayedEventPO)) {
            return false;
        }
        DelayedEventPO other = (DelayedEventPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        String this$uuid = this.getUuid();
        String other$uuid = other.getUuid();
        if (this$uuid == null ? other$uuid != null : !this$uuid.equals(other$uuid)) {
            return false;
        }
        String this$instanceUuid = this.getInstanceUuid();
        String other$instanceUuid = other.getInstanceUuid();
        if (this$instanceUuid == null ? other$instanceUuid != null : !this$instanceUuid.equals(other$instanceUuid)) {
            return false;
        }
        String this$taskId = this.getTaskId();
        String other$taskId = other.getTaskId();
        if (this$taskId == null ? other$taskId != null : !this$taskId.equals(other$taskId)) {
            return false;
        }
        String this$operator = this.getOperator();
        String other$operator = other.getOperator();
        if (this$operator == null ? other$operator != null : !this$operator.equals(other$operator)) {
            return false;
        }
        String this$param = this.getParam();
        String other$param = other.getParam();
        if (this$param == null ? other$param != null : !this$param.equals(other$param)) {
            return false;
        }
        LocalDateTime this$expectFireTime = this.getExpectFireTime();
        LocalDateTime other$expectFireTime = other.getExpectFireTime();
        if (this$expectFireTime == null ? other$expectFireTime != null : !(this$expectFireTime).equals(other$expectFireTime)) {
            return false;
        }
        DelayedEventStatus this$status = this.getStatus();
        DelayedEventStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !((this$status)).equals(other$status)) {
            return false;
        }
        String this$type = this.getType();
        String other$type = other.getType();
        return !(this$type == null ? other$type != null : !this$type.equals(other$type));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof DelayedEventPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        String $uuid = this.getUuid();
        result = result * 59 + ($uuid == null ? 43 : $uuid.hashCode());
        String $instanceUuid = this.getInstanceUuid();
        result = result * 59 + ($instanceUuid == null ? 43 : $instanceUuid.hashCode());
        String $taskId = this.getTaskId();
        result = result * 59 + ($taskId == null ? 43 : $taskId.hashCode());
        String $operator = this.getOperator();
        result = result * 59 + ($operator == null ? 43 : $operator.hashCode());
        String $param = this.getParam();
        result = result * 59 + ($param == null ? 43 : $param.hashCode());
        LocalDateTime $expectFireTime = this.getExpectFireTime();
        result = result * 59 + ($expectFireTime == null ? 43 : ($expectFireTime).hashCode());
        DelayedEventStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : (($status)).hashCode());
        String $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        return result;
    }
}

