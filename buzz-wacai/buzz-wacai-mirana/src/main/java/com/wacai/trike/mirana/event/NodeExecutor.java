/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.InstanceStatus
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.beans.factory.annotation.Autowired
 *  org.springframework.stereotype.Component
 */
package com.wacai.trike.mirana.event;

import com.wacai.trike.mirana.Seraph.SeraphSender;
import com.wacai.trike.mirana.annos.DerailmentDecider;
import com.wacai.trike.mirana.api.constant.InstanceStatus;
import com.wacai.trike.mirana.common.enums.VariableType;
import com.wacai.trike.mirana.context.InstanceContext;
import com.wacai.trike.mirana.delay.DelayCalculator;
import com.wacai.trike.mirana.delay.ScheduleType;
import com.wacai.trike.mirana.event.AbstractFlowEvent;
import com.wacai.trike.mirana.event.ESProcessingFactory;
import com.wacai.trike.mirana.event.EventExchange;
import com.wacai.trike.mirana.event.InstanceEndedEvent;
import com.wacai.trike.mirana.event.ManualProcessingEvent;
import com.wacai.trike.mirana.event.NodeDelayProcessingEvent;
import com.wacai.trike.mirana.event.NodeInitEvent;
import com.wacai.trike.mirana.event.NodeProcessedEvent;
import com.wacai.trike.mirana.event.NodeSkipEvent;
import com.wacai.trike.mirana.event.NodeValidState;
import com.wacai.trike.mirana.graph.FlowGraphService;
import com.wacai.trike.mirana.graph.Node;
import com.wacai.trike.mirana.metrics.MetricsProvider;
import com.wacai.trike.mirana.metrics.NodeMetrics;
import com.wacai.trike.mirana.task.TaskExecuteException;
import com.wacai.trike.mirana.task.TaskProcessor;
import com.wacai.trike.mirana.task.TaskStatus;
import com.wacai.trike.mirana.util.DistributeSequenceIdGenerator;
import com.wacai.trike.mirana.util.ObjectMappers;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
public class NodeExecutor {
    private static final Logger log = LoggerFactory.getLogger(NodeExecutor.class);
    @Autowired
    private FlowGraphService graphService;
    @Autowired
    private List<TaskProcessor> taskProcessors;
    @Autowired
    private DistributeSequenceIdGenerator idGenerator;
    @Autowired
    private MetricsProvider metricsProvider;
    @Autowired
    private ESProcessingFactory esProcessingFactory;
    @Autowired
    private DerailmentDecider derailmentDecider;
    @Autowired
    private SeraphSender seraphSender;

    public void execute(EventExchange exchange) {
        if (!(exchange.getCurrent() instanceof NodeInitEvent) && !(exchange.getCurrent() instanceof ManualProcessingEvent)) {
            log.info("Non-Expected NodeInitEvent/ManualProcessingEvent {}", ObjectMappers.mustWriteValue(exchange));
            return;
        }
        Object event = exchange.getCurrent();
        InstanceContext context = exchange.getContext();
        Node node = this.graphService.getFlowGraph(context.getFlowId()).getNode(context.getCurrentNodeId());
        
        //获取节点状态
        switch (this.figureOutState(node, context)) {
        	//大部分都是pass
            case PASS: {
                exchange.setNext(this.delayOrProcessEvent((AbstractFlowEvent)event, context, node));
                return;
            }
            case FAIL: {
                exchange.setNext(this.handleException((AbstractFlowEvent)event, context, null));
                return;
            }
            case SKIP: {
                context.getCurrentTaskInstance().setStatus(TaskStatus.SKIP);
                context.getCurrentTaskInstance().setEndTime(LocalDateTime.now());
                context.getCurrentTaskInstance().setOperator(((AbstractFlowEvent)event).getOperator());
                context.getCurrentTaskInstance().setDirty(true);
                exchange.setNext(new NodeSkipEvent().setUuid(Long.toString(this.idGenerator.nextId())).setInstanceUuid(((AbstractFlowEvent)event).getInstanceUuid()).setTaskId(context.getCurrentTaskInstance().getUuid()).setOperator(((AbstractFlowEvent)event).getOperator()));
                return;
            }
        }
        log.error("Not-support state");
    }

    private AbstractFlowEvent delayOrProcessEvent(AbstractFlowEvent event, InstanceContext context, Node node) {
        if (this.delayBefore(event, context, node)) {
            return null;
        }
        return this.doProcess(event, context, node);
    }

    private boolean delayBefore(AbstractFlowEvent event, InstanceContext context, Node node) {
    	//如果没有配置延迟，返回false
        if (node.delayNotConfig()) {
            return false;
        }
        if (!ScheduleType.couldDelayBefore(node.getScheduleType())) {
            return false;
        }
        LocalDateTime referDate = this.getReferDate(context);
        DelayCalculator calculator = node.getDelayCalculator();
        LocalDateTime expectStart = calculator.expectStart(referDate);
        if (Objects.isNull(expectStart) || expectStart.isBefore(LocalDateTime.now())) {
            return false;
        }
        //提交延时任务
        NodeProcessedEvent delayEvent = ((NodeProcessedEvent)new NodeDelayProcessingEvent().setUuid(Long.toString(this.idGenerator.nextId())).setInstanceUuid(event.getInstanceUuid()).setTaskId(event.getTaskId()).setParam((Map)event.getParam())).setOperator(event.getOperator());
        this.seraphSender.publishDelayEvent(expectStart, delayEvent);
        this.metricsProvider.metricsNode(NodeMetrics.build(context, node));
        return true;
    }

    /**
     * 实际执行任务
     * @param event
     * @param context
     * @param node
     * @return
     */
    public AbstractFlowEvent doProcess(AbstractFlowEvent event, InstanceContext context, Node node) {
        try {
            context.addVariable(node.getInputForms(), VariableType.FORM_INPUT);
            //执行任务
            context.addVariable(this.executeTask(context, node), VariableType.TASK_OUTPUT);
            context.addVariable(node.getOutputForms(), VariableType.FORM_OUTPUT);
            context.getCurrentTaskInstance().setOperator(event.getOperator());
            context.getCurrentTaskInstance().setDirty(true);
            NodeProcessedEvent next = new NodeProcessedEvent()
            		.setUuid(Long.toString(this.idGenerator.nextId()))
            		.setInstanceUuid(event.getInstanceUuid())
            		.setTaskId(context.getCurrentTaskInstance().getUuid())
            		.setOperator(event.getOperator());
            
            if (this.delayAfter(next, context, node)) {
                AbstractFlowEvent abstractFlowEvent = null;
                return abstractFlowEvent;
            }
            
            this.metricsProvider.metricsNode(NodeMetrics.build(context, node));
            NodeProcessedEvent nodeProcessedEvent = next;
            return nodeProcessedEvent;
        }
        catch (TaskExecuteException e) {
            AbstractFlowEvent abstractFlowEvent = this.handleException(event, context, e);
            return abstractFlowEvent;
        }
        finally {
            this.esProcessingFactory.process(new ESProcessingFactory.Salad(ESProcessingFactory.Type.UPDATE_NODE, context, node));
        }
    }

    private Map<String, String> executeTask(InstanceContext context, Node node) throws TaskExecuteException {
        Optional<TaskProcessor> found = Optional.empty();
        for (TaskProcessor p : this.taskProcessors) {
            if (!p.accept(node.getTaskType())) continue;
            found = Optional.of(p);
            break;
        }
        TaskProcessor processor = found.orElseThrow(() -> new TaskExecuteException("task executor not found for " + node.getTaskType()));
        return processor.execute(node, context);
    }

    private boolean delayAfter(AbstractFlowEvent event, InstanceContext context, Node node) {
        if (node.delayNotConfig()) {
            return false;
        }
        if (!ScheduleType.couldDelayAfter(node.getScheduleType())) {
            return false;
        }
        LocalDateTime referDate = this.getReferDate(context);
        DelayCalculator calculator = node.getDelayCalculator();
        LocalDateTime expectEnd = calculator.expectEnd(referDate);
        if (Objects.isNull(expectEnd) || expectEnd.isBefore(LocalDateTime.now())) {
            return false;
        }
        this.seraphSender.publishDelayEvent(expectEnd, event);
        this.metricsProvider.metricsNode(NodeMetrics.build(context, node));
        return true;
    }

    public AbstractFlowEvent handleException(AbstractFlowEvent event, InstanceContext context, Throwable e) {
        Node node = this.graphService.getFlowGraph(context.getFlowId()).getNode(context.getCurrentNodeId());
        log.error("execute task failed of instance [{}], node [{}-{}]", new Object[]{context.getUuid(), node.getName(), node.getId(), e});
        context.setStatus(InstanceStatus.ENDED);
        context.setDirty(true);
        context.getCurrentTaskInstance().setStatus(TaskStatus.FAILED);
        context.getCurrentTaskInstance().setRemark(Optional.ofNullable(e).map(Throwable::getMessage).orElse(""));
        context.getCurrentTaskInstance().setEndTime(LocalDateTime.now());
        context.getCurrentTaskInstance().setDirty(true);
        this.metricsProvider.metricsNode(NodeMetrics.build(context, node));
        return new InstanceEndedEvent().setUuid(Long.toString(this.idGenerator.nextId())).setInstanceUuid(context.getUuid()).setTaskId(context.getUuid()).setOperator(event.getOperator());
    }

    private LocalDateTime getReferDate(InstanceContext context) {
        return Optional.ofNullable(context.getDueDate()).orElse(LocalDateTime.now());
    }

    private NodeValidState figureOutState(Node node, InstanceContext context) {
    	//如果没有配置延迟执行，直接返回PASS
        if (node.delayNotConfig()) {
            return NodeValidState.PASS;
        }
        if (node.getScheduleType() != ScheduleType.DELAY_DAYS && node.getScheduleType() != ScheduleType.DELAY_PHASE) {
            return NodeValidState.PASS;
        }
        LocalDateTime dueDate = context.getDueDate();
        if (Objects.isNull(dueDate)) {
            return NodeValidState.FAIL;
        }
        DelayCalculator calculator = node.getDelayCalculator();
        if (LocalDateTime.now().isAfter(calculator.expectEnd(dueDate))) {
            return NodeValidState.SKIP;
        }
        return NodeValidState.PASS;
    }
}

