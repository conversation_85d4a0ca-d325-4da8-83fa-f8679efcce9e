/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.api.model;

import com.wacai.trike.mirana.api.model.FlowResponse;
import com.wacai.trike.mirana.api.model.TaskInstanceDetail;
import java.util.List;

public class InstanceDetail
extends FlowResponse {
    private List<TaskInstanceDetail> taskInstanceDetails;

    public List<TaskInstanceDetail> getTaskInstanceDetails() {
        return this.taskInstanceDetails;
    }

    public InstanceDetail setTaskInstanceDetails(List<TaskInstanceDetail> taskInstanceDetails) {
        this.taskInstanceDetails = taskInstanceDetails;
        return this;
    }

    @Override
    public String toString() {
        return "InstanceDetail(taskInstanceDetails=" + this.getTaskInstanceDetails() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof InstanceDetail)) {
            return false;
        }
        InstanceDetail other = (InstanceDetail)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        List<TaskInstanceDetail> this$taskInstanceDetails = this.getTaskInstanceDetails();
        List<TaskInstanceDetail> other$taskInstanceDetails = other.getTaskInstanceDetails();
        return !(this$taskInstanceDetails == null ? other$taskInstanceDetails != null : !(this$taskInstanceDetails).equals(other$taskInstanceDetails));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof InstanceDetail;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        List<TaskInstanceDetail> $taskInstanceDetails = this.getTaskInstanceDetails();
        result = result * 59 + ($taskInstanceDetails == null ? 43 : ($taskInstanceDetails).hashCode());
        return result;
    }
}

