/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.alibaba.fastjson.JSON
 *  com.wacai.loan.trike.common.model.ApiResponse
 *  javax.servlet.http.HttpServletRequest
 *  org.slf4j.Logger
 *  org.slf4j.LoggerFactory
 *  org.springframework.http.HttpStatus
 *  org.springframework.http.ResponseEntity
 *  org.springframework.validation.BindException
 *  org.springframework.validation.FieldError
 *  org.springframework.validation.ObjectError
 *  org.springframework.web.bind.annotation.ControllerAdvice
 *  org.springframework.web.bind.annotation.ExceptionHandler
 *  org.springframework.web.bind.annotation.ResponseStatus
 *  org.springframework.web.bind.annotation.RestController
 */
package com.wacai.trike.mirana.config;

import com.alibaba.fastjson.JSON;
import com.wacai.loan.trike.common.model.ApiResponse;
import javax.servlet.http.HttpServletRequest;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.BindException;
import org.springframework.validation.FieldError;
import org.springframework.validation.ObjectError;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestController;

@ControllerAdvice(annotations={RestController.class})
public class WebApiExceptionHandler {
    private static final Logger log = LoggerFactory.getLogger(WebApiExceptionHandler.class);
    private static final String DEFAULT_ERROR_MSG = "服务异常, 请稍后重试";

    @ResponseStatus(value=HttpStatus.BAD_REQUEST)
    @ExceptionHandler(value={Throwable.class})
    public ResponseEntity<ApiResponse<String>> globalHandler(HttpServletRequest request, Throwable e) {
        StringBuilder sb = new StringBuilder();
        if (e instanceof BindException) {
            BindException be = (BindException)e;
            for (ObjectError error : be.getBindingResult().getAllErrors()) {
                if (!(error instanceof FieldError)) continue;
                FieldError fieldError = (FieldError)error;
                sb.append(fieldError.getField()).append(" ").append(fieldError.getDefaultMessage()).append("\n");
            }
        }
        String msg = DEFAULT_ERROR_MSG;
        if (sb.length() >= 0) {
            msg = sb.toString().trim();
        }
        log.error("request [{}], param [{}] occurs error", new Object[]{request.getRequestURI(), JSON.toJSONString(request.getParameterMap()), e});
        return ResponseEntity.badRequest().body(ApiResponse.error(msg));
    }
}

