/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.service;

import com.wacai.trike.mirana.resource.ApplicationModel;
import com.wacai.trike.mirana.service.BaseDTO;
import com.wacai.trike.mirana.util.ObjectUtils;

public class ApplicationDTO
extends BaseDTO {
    private Long id;
    private Long buId;
    private String code;
    private String name;

    public ApplicationModel toModel() {
        return ObjectUtils.convert(this, ApplicationModel.class);
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof ApplicationDTO)) {
            return false;
        }
        ApplicationDTO other = (ApplicationDTO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$id = this.getId();
        Long other$id = other.getId();
        if (this$id == null ? other$id != null : !(this$id).equals(other$id)) {
            return false;
        }
        Long this$buId = this.getBuId();
        Long other$buId = other.getBuId();
        if (this$buId == null ? other$buId != null : !(this$buId).equals(other$buId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        return !(this$name == null ? other$name != null : !this$name.equals(other$name));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof ApplicationDTO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $id = this.getId();
        result = result * 59 + ($id == null ? 43 : ($id).hashCode());
        Long $buId = this.getBuId();
        result = result * 59 + ($buId == null ? 43 : ($buId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        return result;
    }

    @Override
    public Long getId() {
        return this.id;
    }

    public Long getBuId() {
        return this.buId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    @Override
    public void setId(Long id) {
        this.id = id;
    }

    public void setBuId(Long buId) {
        this.buId = buId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String toString() {
        return "ApplicationDTO(id=" + this.getId() + ", buId=" + this.getBuId() + ", code=" + this.getCode() + ", name=" + this.getName() + ")";
    }
}

