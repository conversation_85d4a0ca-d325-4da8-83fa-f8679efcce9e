/*
 * Decompiled with CFR 0.151.
 */
package com.wacai.trike.mirana.event;

import java.io.Serializable;
import java.util.Map;

public abstract class AbstractFlowEvent
		implements Serializable {
	
	public abstract String getUuid();

	public abstract String getInstanceUuid();

	public abstract String getTaskId();

	public abstract String getOperator();

	public abstract Map<String, String> getParam();

	public abstract AbstractFlowEvent setParam(Map<String, String> var1);
}
