/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.wacai.trike.mirana.api.constant.FlowStatus
 *  com.wacai.trike.mirana.api.constant.FlowType
 *  javax.persistence.Entity
 *  javax.persistence.EnumType
 *  javax.persistence.Enumerated
 *  javax.persistence.Table
 */
package com.wacai.trike.mirana.domain.flow.po;

import com.wacai.trike.mirana.api.constant.FlowStatus;
import com.wacai.trike.mirana.api.constant.FlowType;
import com.wacai.trike.mirana.common.po.BasePO;
import javax.persistence.Entity;
import javax.persistence.EnumType;
import javax.persistence.Enumerated;
import javax.persistence.Table;

@Entity
@Table(name="flow")
public class FlowPO
extends BasePO {
    private static final long serialVersionUID = 1L;
    private Long appId;
    private String code;
    private String name;
    private String version;
    @Enumerated(value=EnumType.STRING)
    private FlowStatus status;
    private String timeout;
    @Enumerated(value=EnumType.STRING)
    private FlowType type;

    public Long getAppId() {
        return this.appId;
    }

    public String getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public String getVersion() {
        return this.version;
    }

    public FlowStatus getStatus() {
        return this.status;
    }

    public String getTimeout() {
        return this.timeout;
    }

    public FlowType getType() {
        return this.type;
    }

    public void setAppId(Long appId) {
        this.appId = appId;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public void setName(String name) {
        this.name = name;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    public void setStatus(FlowStatus status) {
        this.status = status;
    }

    public void setTimeout(String timeout) {
        this.timeout = timeout;
    }

    public void setType(FlowType type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "FlowPO(appId=" + this.getAppId() + ", code=" + this.getCode() + ", name=" + this.getName() + ", version=" + this.getVersion() + ", status=" + this.getStatus() + ", timeout=" + this.getTimeout() + ", type=" + this.getType() + ")";
    }

    @Override
    public boolean equals(Object o) {
        if (o == this) {
            return true;
        }
        if (!(o instanceof FlowPO)) {
            return false;
        }
        FlowPO other = (FlowPO)o;
        if (!other.canEqual(this)) {
            return false;
        }
        if (!super.equals(o)) {
            return false;
        }
        Long this$appId = this.getAppId();
        Long other$appId = other.getAppId();
        if (this$appId == null ? other$appId != null : !(this$appId).equals(other$appId)) {
            return false;
        }
        String this$code = this.getCode();
        String other$code = other.getCode();
        if (this$code == null ? other$code != null : !this$code.equals(other$code)) {
            return false;
        }
        String this$name = this.getName();
        String other$name = other.getName();
        if (this$name == null ? other$name != null : !this$name.equals(other$name)) {
            return false;
        }
        String this$version = this.getVersion();
        String other$version = other.getVersion();
        if (this$version == null ? other$version != null : !this$version.equals(other$version)) {
            return false;
        }
        FlowStatus this$status = this.getStatus();
        FlowStatus other$status = other.getStatus();
        if (this$status == null ? other$status != null : !this$status.equals(other$status)) {
            return false;
        }
        String this$timeout = this.getTimeout();
        String other$timeout = other.getTimeout();
        if (this$timeout == null ? other$timeout != null : !this$timeout.equals(other$timeout)) {
            return false;
        }
        FlowType this$type = this.getType();
        FlowType other$type = other.getType();
        return !(this$type == null ? other$type != null : !this$type.equals(other$type));
    }

    @Override
    protected boolean canEqual(Object other) {
        return other instanceof FlowPO;
    }

    @Override
    public int hashCode() {
        int PRIME = 59;
        int result = super.hashCode();
        Long $appId = this.getAppId();
        result = result * 59 + ($appId == null ? 43 : ($appId).hashCode());
        String $code = this.getCode();
        result = result * 59 + ($code == null ? 43 : $code.hashCode());
        String $name = this.getName();
        result = result * 59 + ($name == null ? 43 : $name.hashCode());
        String $version = this.getVersion();
        result = result * 59 + ($version == null ? 43 : $version.hashCode());
        FlowStatus $status = this.getStatus();
        result = result * 59 + ($status == null ? 43 : $status.hashCode());
        String $timeout = this.getTimeout();
        result = result * 59 + ($timeout == null ? 43 : $timeout.hashCode());
        FlowType $type = this.getType();
        result = result * 59 + ($type == null ? 43 : $type.hashCode());
        return result;
    }
}

