/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Path
 *  com.querydsl.core.types.PathMetadata
 *  com.querydsl.core.types.PathMetadataFactory
 *  com.querydsl.core.types.dsl.BooleanPath
 *  com.querydsl.core.types.dsl.DateTimePath
 *  com.querydsl.core.types.dsl.EntityPathBase
 *  com.querydsl.core.types.dsl.EnumPath
 *  com.querydsl.core.types.dsl.NumberPath
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.domain.context.po;

import com.querydsl.core.types.Path;
import com.querydsl.core.types.PathMetadata;
import com.querydsl.core.types.PathMetadataFactory;
import com.querydsl.core.types.dsl.BooleanPath;
import com.querydsl.core.types.dsl.DateTimePath;
import com.querydsl.core.types.dsl.EntityPathBase;
import com.querydsl.core.types.dsl.EnumPath;
import com.querydsl.core.types.dsl.NumberPath;
import com.querydsl.core.types.dsl.StringPath;
import com.wacai.trike.mirana.common.po.BasePO;
import com.wacai.trike.mirana.common.po.QBasePO;
import com.wacai.trike.mirana.domain.context.po.TaskInstancePO;
import com.wacai.trike.mirana.task.TaskStatus;
import java.time.LocalDateTime;

public class QTaskInstancePO
extends EntityPathBase<TaskInstancePO> {
    private static final long serialVersionUID = -1842401620L;
    public static final QTaskInstancePO taskInstancePO = new QTaskInstancePO("taskInstancePO");
    public final QBasePO _super = new QBasePO((Path<? extends BasePO>)this);
    public final BooleanPath active;
    public final StringPath createdBy;
    public final DateTimePath<LocalDateTime> createdTime;
    public final DateTimePath<LocalDateTime> endTime;
    public final BooleanPath esSync;
    public final NumberPath<Long> id;
    public final StringPath instanceUuid;
    public final NumberPath<Long> nodeId;
    public final StringPath remark;
    public final DateTimePath<LocalDateTime> startTime;
    public final EnumPath<TaskStatus> status;
    public final StringPath updatedBy;
    public final DateTimePath<LocalDateTime> updatedTime;
    public final StringPath uuid;

    public QTaskInstancePO(String variable) {
        super(TaskInstancePO.class, PathMetadataFactory.forVariable((String)variable));
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.endTime = this.createDateTime("endTime", LocalDateTime.class);
        this.esSync = this.createBoolean("esSync");
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.remark = this.createString("remark");
        this.startTime = this.createDateTime("startTime", LocalDateTime.class);
        this.status = this.createEnum("status", TaskStatus.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }

    public QTaskInstancePO(Path<? extends TaskInstancePO> path) {
        super(path.getType(), path.getMetadata());
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.endTime = this.createDateTime("endTime", LocalDateTime.class);
        this.esSync = this.createBoolean("esSync");
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.remark = this.createString("remark");
        this.startTime = this.createDateTime("startTime", LocalDateTime.class);
        this.status = this.createEnum("status", TaskStatus.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }

    public QTaskInstancePO(PathMetadata metadata) {
        super(TaskInstancePO.class, metadata);
        this.active = this._super.active;
        this.createdBy = this._super.createdBy;
        this.createdTime = this._super.createdTime;
        this.endTime = this.createDateTime("endTime", LocalDateTime.class);
        this.esSync = this.createBoolean("esSync");
        this.id = this._super.id;
        this.instanceUuid = this.createString("instanceUuid");
        this.nodeId = this.createNumber("nodeId", Long.class);
        this.remark = this.createString("remark");
        this.startTime = this.createDateTime("startTime", LocalDateTime.class);
        this.status = this.createEnum("status", TaskStatus.class);
        this.updatedBy = this._super.updatedBy;
        this.updatedTime = this._super.updatedTime;
        this.uuid = this.createString("uuid");
    }
}

