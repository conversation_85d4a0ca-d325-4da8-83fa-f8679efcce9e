/*
 * Decompiled with CFR 0.151.
 * 
 * Could not load the following classes:
 *  com.querydsl.core.types.Predicate
 *  com.querydsl.core.types.dsl.BooleanExpression
 *  com.querydsl.core.types.dsl.StringPath
 */
package com.wacai.trike.mirana.service;

import com.querydsl.core.types.Predicate;
import com.querydsl.core.types.dsl.BooleanExpression;
import com.querydsl.core.types.dsl.StringPath;
import java.util.ArrayList;
import java.util.List;

public interface JPAQueryBinder {
    public static final String PERCENT_SYMBOL = "%";

    default public BooleanExpression bind(BooleanExpression expression, List<String> elements, StringPath path, boolean fuzzy) {
        if (!fuzzy) {
            return expression.and((Predicate)path.in(elements));
        }
        ArrayList predicates = new ArrayList(elements.size());
        elements.forEach(ele -> predicates.add(path.like(this.fuzzy((String)ele))));
        return expression.andAnyOf(predicates.toArray(new Predicate[0]));
    }

    default public String fuzzy(String keyword) {
        return PERCENT_SYMBOL + keyword + PERCENT_SYMBOL;
    }
}

