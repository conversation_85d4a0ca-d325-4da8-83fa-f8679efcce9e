<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<artifactId>buzz-wacai-mirana</artifactId>

	<parent>
		<groupId>com.wacai</groupId>
		<artifactId>wacai-boot-starter-parent</artifactId>
		<version>2.1.3</version>
		<relativePath></relativePath>
	</parent>


	<properties>
		<ninja.client.version>1.2.0</ninja.client.version>
		<middleware.toolkit.version>1.2.3</middleware.toolkit.version>
		<querydsl.version>4.2.1</querydsl.version>
		<okhttp.version>3.13.1</okhttp.version>
	</properties>

	<dependencies>

		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>kafka-http-client</artifactId>
			<version>2.1.2</version>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>${okhttp.version}</version>
		</dependency>

		<dependency>
			<groupId>joda-time</groupId>
			<artifactId>joda-time</artifactId>
		</dependency>

		<dependency>
			<groupId>net.sf.jopt-simple</groupId>
			<artifactId>jopt-simple</artifactId>
			<version>5.0.2</version>
		</dependency>

		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>wacai-boot-starter-web</artifactId>
		</dependency>

		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>wacai-boot-starter-idcregistery</artifactId>
		</dependency>


		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-aop</artifactId>
		</dependency>


		<dependency>
			<groupId>com.wacai.loan</groupId>
			<artifactId>mirana-api</artifactId>
			<version>0.1.5</version>
		</dependency>

		<dependency>
			<groupId>com.wacai.loan</groupId>
			<artifactId>medivh-engine</artifactId>
			<version>0.0.5</version>
			<exclusions>
				<exclusion>
					<groupId>com.wacai</groupId>
					<artifactId>redis-client-all-in</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Querydsl相关包 -->
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-jpa</artifactId>
			<version>${querydsl.version}</version>
			<exclusions>
				<exclusion>
					<artifactId>jsr305</artifactId>
					<groupId>com.google.code.findbugs</groupId>
				</exclusion>
			</exclusions>
		</dependency>


	</dependencies>


</project>