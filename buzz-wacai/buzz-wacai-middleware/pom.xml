<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.buzz</groupId>
		<artifactId>buzz-wacai</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>buzz-wacai-middleware</artifactId>


	<dependencies>

		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>wacai-boot-starter-cache</artifactId>
			<version>1.2.17</version>
		</dependency>

		<dependency>
			<groupId>com.wacai.middleware</groupId>
			<artifactId>neptune-sdk</artifactId>
			<version>1.0.3</version>
		</dependency>

		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>redis-client-all-in</artifactId>
			<version>3.16.1</version>
		</dependency>


		<!-- 分布式锁 -->
		<dependency>
			<groupId>com.wacai.middleware</groupId>
			<artifactId>zookeeper-kits</artifactId>
			<version>1.0.5</version>
		</dependency>

		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>jasmine-client</artifactId>
			<version>1.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>middleware-toolkit</artifactId>
			<version>1.2.5</version>
		</dependency>

	   <!-- 北斗 -->
		<dependency>
          <groupId>com.wacai.metric</groupId>
          <artifactId>bds-metric-sdk</artifactId>
          <version>1.2.0</version>
        </dependency>

		<!-- dubbo -->
		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>wacai-boot-starter-dubbo</artifactId>
			<version>1.3.6</version>
		</dependency>

		<!-- 网关 -->
		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>wacai-openAllInOne-sdk</artifactId>
			<version>1.5.1</version>
		</dependency>

		<!-- 加解密服务 -->
		<dependency>
			<groupId>com.wacai.ocean</groupId>
			<artifactId>key-escrow-based-http-client</artifactId>
			<version>1.0.3</version>
		</dependency>
		<dependency>
			<groupId>com.wacai.ocean</groupId>
			<artifactId>key-escrow-client</artifactId>
			<version>1.0.2</version>
		</dependency>
		<!-- 加解密服务 -->

		<!-- 分布式ID -->
		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>horae-client</artifactId>
			<version>0.0.5</version>
		</dependency>

		<!-- 假日服务 -->
		<dependency>
			<groupId>com.wacai</groupId>
			<artifactId>service-holidays-api</artifactId>
			<version>1.0.2</version>
		</dependency>


		<!--hermes begin	-->
<!--		<dependency>-->
<!--			<groupId>com.wacai</groupId>-->
<!--			<artifactId>kafka-http-client</artifactId>-->
<!--			<version>2.1.6</version>-->
<!--		</dependency>-->

		<dependency>
			<groupId>com.wacai.hermes</groupId>
			<artifactId>hermes-agent</artifactId>
			<version>2.1.3</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
			<version>3.10</version>
		</dependency>

		<!--hermes end	-->

		<!--sailfish-->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter</artifactId>
            <version>RELEASE</version>
            <scope>compile</scope>
        </dependency>

		<dependency>
			<groupId>com.wacai.pt</groupId>
			<artifactId>red-alert-api</artifactId>
			<version>2.1.8</version>
		</dependency>

        <!--老版本挖财redis-->
<!--		<dependency>-->
<!--			<groupId>com.wacai</groupId>-->
<!--			<artifactId>redis-client</artifactId>-->
<!--			<version>1.0.0</version>-->
<!--		</dependency>-->
		<!--老版本挖财redis-->



	</dependencies>

</project>