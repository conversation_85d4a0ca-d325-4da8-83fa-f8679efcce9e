package com.buzz.wacai.lock;

import com.wacai.zookeeper.ZookeeperCuatorClientHolder;
import com.wacai.zookeeper.lock.BusinessException;
import com.wacai.zookeeper.lock.DistributeLockTemplate;
import com.wacai.zookeeper.lock.LockCallback;
import com.wacai.zookeeper.lock.LockException;
import com.wacai.zookeeper.lock.metric.MetricRegistry;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * 测试分布式锁的path
 * 
 * <pre>
 * 在3.4.8版本下不会自动删除
 * 在3.5之后会自动清理
 * </pre>
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class DsLockTemplateTest {

	private static DistributeLockTemplate dsLockTemplate = new DistributeLockTemplate();

	private static Random random = new Random();

	private static String zk_host = "zklockserver1.test.wacai.info:2181";
	static {
		init();
	}

	@Test
	public void testLock() throws InterruptedException {
		dsLockTemplate.execute("/wacai/abc", 1000L, new LockCallback() {

			@Override
			public Object onLocked() throws BusinessException {
				return null;
			}

			@Override
			public Object onLockException(LockException le) {
				return null;
			}

			@Override
			public Object onLockFailed() throws BusinessException {
				return null;
			}
		});

	}
	@Test
	public void testLockInMultiThread() throws InterruptedException {
		for (int i = 0; i < 20; i++) {
			new Thread(() -> {
				dsLockTemplate.execute("/wacai/abc", 1000L, new LockCallback() {
					@Override
					public Object onLocked() throws BusinessException {
						System.out.println("onLocked "+System.currentTimeMillis());
						return null;
					}

					@Override
					public Object onLockException(LockException le) {
						System.out.println("onLockException "+System.currentTimeMillis());
						return null;
					}

					@Override
					public Object onLockFailed() throws BusinessException {
						System.out.println("onLockFailed "+System.currentTimeMillis());
						return null;
					}
				});
			}).start();
		}

		TimeUnit.SECONDS.sleep(300);
	}

	public static void init() {
		ZookeeperCuatorClientHolder zookeeperCuatorClientHolder = new ZookeeperCuatorClientHolder(zk_host, 3000, 3);
		dsLockTemplate.setZookeeperCuatorClientHolder(zookeeperCuatorClientHolder);
		MetricRegistry.getRecord();

	}

	public static void exeucte(int id) {
		String path = "/test/delete/" + id + "/0";
		dsLockTemplate.execute(path, 3000, new LockCallback() {

			@Override
			public Object onLocked() throws BusinessException {
				int millis = random.nextInt(100);
				log.info(path + "-onLocked " + millis);
				try {
					Thread.sleep(millis);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
				return null;
			}

			@Override
			public Object onLockException(LockException le) {
				return null;
			}

			@Override
			public Object onLockFailed() throws BusinessException {
				log.error(path + "-onLockFailed");
				return null;
			}
		});

	}


}