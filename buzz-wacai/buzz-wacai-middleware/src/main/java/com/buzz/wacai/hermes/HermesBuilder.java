package com.buzz.wacai.hermes;

import com.wacai.hermes.agent.config.HermesConsumerConfig;
import com.wacai.hermes.agent.consumer.HermesConsumer;
import com.wacai.hermes.agent.consumer.protocol.HermesHttpConsumer;
import com.wacai.hermes.bridge.schema.resp.Message;
import lombok.extern.slf4j.Slf4j;

import java.nio.charset.StandardCharsets;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class HermesBuilder {

    public static HermesBuilder builder() {
        return new HermesBuilder();
    }

    private String topic = "bairen.test";

    private String group = "bairen-group";

    private String consumerId;

    private int partition;

    private boolean injectError;

    private Consumer<Message> messageProcessor = message -> {
        String value = new String(message.getValue(), StandardCharsets.UTF_8);
        long latency = System.currentTimeMillis() - message.getTimestamp();

        log.info("receiveMessage topic={},  partition={}, offset={}, latency={} ",
                topic,
                message.getPartition(),
                message.getOffset(),
                latency);
        if (injectError) {
            throw new RuntimeException("inject error");
        }
    };

    public HermesBuilder topic(String topic) {
        this.topic = topic;
        return this;
    }

    public HermesBuilder group(String group) {
        this.group = group;
        return this;
    }

    public HermesBuilder partition(int partition) {
        this.partition = partition;
        return this;
    }

    public HermesBuilder injectError(boolean injectError) {
        this.injectError = injectError;
        return this;
    }

    public HermesBuilder consumerId(String consumerId) {
        this.consumerId = consumerId;
        return this;
    }

    public HermesBuilder consumer(Consumer<Message> consumer) {
        this.messageProcessor = consumer;
        return this;
    }


    public HermesConsumer buildHermesConsumer() {
        HermesConsumerConfig config = new HermesConsumerConfig();
        config.setGroupId(group);
        config.setTopic(topic);
        config.setConsumerId(consumerId);

        HermesHttpConsumer hermesConsumer = new HermesHttpConsumer(config) {
            public void onMessageReceived(Message message) {
                messageProcessor.accept(message);
            }
        };

//        HermesCompositeConsumer hermesConsumer = new HermesCompositeConsumer(config){
//            public void onMessageReceived(Message message) {
//                messageProcessor.accept(message);
//            }
//        };
        return hermesConsumer;
    }
}
