package com.buzz.wacai.hermes;

import com.wacai.hermes.agent.config.HermesConsumerConfig;
import com.wacai.hermes.agent.consumer.protocol.HermesHttpConsumer;
import com.wacai.hermes.bridge.schema.resp.BatchMessage;
import com.wacai.hermes.bridge.schema.resp.Message;

public class MyMessageConsumer extends HermesHttpConsumer {

	public MyMessageConsumer(HermesConsumerConfig config) {
		super(config);
	}

	@Override
	public void onMessageReceived(Message message) {
	}

	public static void main(String[] args) {

		String topic = "payment.capital.transfer.msg";
		HermesConsumerConfig config = new HermesConsumerConfig();
		config.setTopic(topic);
		config.setGroupId("buzz-wacai");
		config.setConsumerId("buzz-wacai-1jkl8");
		config.setPollMs(10);

		MyMessageConsumer consumer = new MyMessageConsumer(config);
		for (int i = 0; i < Integer.MAX_VALUE; ++i) {
			long begin = System.currentTimeMillis();
			BatchMessage ret = consumer.fetch();
			long end = System.currentTimeMillis();
			int total = ret.getTotal();
			
			System.out.println("total:" + total + "\t cost:" + (end - begin) + "\t ackOffset:" + ret.getAckOffset()
					+ "\t startOffset:" + ret.getStartOffset());
			
			//如果存在数据需要ack
			if (total > 0) {
				consumer.ack(topic, 0, ret.getStartOffset());
			}
		}

	}
}
