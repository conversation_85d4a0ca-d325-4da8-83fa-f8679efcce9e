package com.buzz.wacai.keyescrow;

import java.util.Map;

import org.junit.Test;

import com.wacai.ocean.key.escrow.based.http.client.cache.ConfigEscrowCache;

public class KeyescrowTest {

	@Test
	public void test() {

		ConfigEscrowCache configEscrowCache = new ConfigEscrowCache();
		configEscrowCache.init();

		Map<String, String> map = configEscrowCache.getApplicationConfig();
		System.out.println(map);
	}
}
