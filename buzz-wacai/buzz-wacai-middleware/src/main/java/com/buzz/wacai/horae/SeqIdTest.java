package com.buzz.wacai.horae;

import com.buzz.wacai.dubbo.util.DubboConsumerBuilder;
import com.wacai.horae.model.IDRange;
import com.wacai.horae.service.UniversalIdService;
import org.junit.Test;

/**
 * 分布式ID测试
 *
 * <AUTHOR>
 */
public class SeqIdTest {
    @Test
    public void testGetNextRange() throws InterruptedException {

        UniversalIdService service = DubboConsumerBuilder.builder().build(UniversalIdService.class);

        IDRange data = service.getNextRange("user", "account_app_id", 500);
        System.out.println("============"+data);
    }

}
