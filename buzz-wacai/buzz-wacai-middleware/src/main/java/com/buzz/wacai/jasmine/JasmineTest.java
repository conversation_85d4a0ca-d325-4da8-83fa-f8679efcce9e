package com.buzz.wacai.jasmine;

import com.wacai.jasmine.client.ConfigSet;
import com.wacai.jasmine.client.Jasmine;
import org.junit.Test;

import java.io.IOException;

public class JasmineTest {

	@Test
	public void test(){
		String value = Jasmine.getConfig("quantum","agent-config", "agent.config.biz-app");
		System.out.println(value);
		ConfigSet configSet = Jasmine.getConfigSet("quantum", "agent-config", "agent.config.biz-app");
		System.out.println(configSet.toProperties());
	}


	@Test
	public void test2() throws IOException {

		//System.out.println(Jasmine.cfg().getEndpoints());
		try {
			System.out.println(Jasmine.getConfig("jasmine_demo", "quick_start","inner_test"));
			System.out.println(Jasmine.getAllBySubModule("jasmine_demo", "quick_start"));
		}catch (Exception e){
			e.printStackTrace();
		}
		System.in.read();

//		System.setProperty("JASMINE_NAMESPACE", "test2");
//		String centerAddress = Jasmine.getConfig("middleware", "hermes-center","address");
//		System.out.println("centerAddress="+centerAddress);
//		String centerAddress1 = Jasmine.getConfig("middleware", "hermes-center","address1");
//		System.out.println(centerAddress1 );


//		Jasmine.cfg().setEndpoints(Endpoint.valueOf("jasmine-server-test2.middleware.k2.test.wacai.info:80"));

//		System.out.println(Jasmine.getConfig("jasmine_demo", "quick_start","inner_test"));
//		System.out.println(Jasmine.getAllBySubModule("jasmine_demo", "quick_start"));
		//Jasmine.cfg().setEndpoints(Endpoint.valueOf("************:8080"));
		//Jasmine.cfg().setEnableDebug(true);
		//Jasmine.cfg().setEndpoints(Endpoint.valueOf("*************:8080"));while(true) {
		//
		//			System.out.println(Jasmine.getConfigFromServer("jasmine_demo", "quick_start","inner_test"));
		//			Thread.sleep(1000);
		//		}
//		System.out.println(Jasmine.getConfigFromServer("jasmine_demo", "quick_start", "pbkd-test"));
//		System.out.println(Jasmine.getAllBySubModule("jasmine_demo", "quick_start"));
//		System.out.println(Jasmine.getAllBySubModule("jasmine_demo", "quick_start"));
//		Jasmine.updateConfig("jasmine_demo", "quick_start", "inner_test", "1001");


//		

	}
	// http://***********37:8080/jasmine/getAllBySubModule?module=jasmine_demo&subModule=quick_start
}
