package com.buzz.wacai.bds;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SimplePropertyPreFilter;
import com.wac.metric.bds.sdk.common.BdsConfig;
import com.wac.metric.bds.sdk.ratel.RatelContext;
import com.wac.metric.bds.sdk.ratel.RatelTrace;
import com.wac.metric.bds.sdk.ratel.launcher.RatelLauncher;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;

/**
 * 参考：RatelTraceTest
 *
 */
@Slf4j
public class BdsTest {

	public static class LogRatelLauncher implements RatelLauncher<RatelContext> {

		private SimplePropertyPreFilter filter = new SimplePropertyPreFilter();

		@Override
		public void launch(RatelContext t) {
			log.info(JSON.toJSONString(t, filter));
		}
	}

	static {
		BdsConfig.getConfig().setTurnOn(true);
		//BdsConfig.getConfig().setLaunchConsole(true);
		BdsConfig.getConfig().setKafkaProducerHttpUrl("http://172.16.52.73:8080/kafka/publish");
		BdsConfig.getConfig().setKafkaProducerHttpUrl(
				"http://hermes-proxy-log.middleware.wse.test.wacai.info/hermes-proxy/sendSingle");
	}

	@Test
	public void testConfig() {
		BdsConfig.getConfig().loadConfigByUrl("http://bds-web.metric-test.k2.test.wacai.info/metric/config");
		System.out.println(BdsConfig.getConfig().getKafkaProducerHttpUrl());
		System.out.println(BdsConfig.getConfig().getHermesProducerHttpUrl());
	}

	@Test
	public void testServiceTrace() {
		String traceId = null;
		String rpcId = null;
		String appName = "bds-app";
		String unitName = "UserAccountQueryService";
		String unitInfo = "queryUserAccountExtByUid";
		String type = "dubbo";
		boolean sample = false;

		//server开始
		RatelTrace.startServerTrace(traceId, rpcId, appName, unitName, unitInfo, type, sample);

		//记录更多信息
		RatelContext ctx = RatelContext.getCurrentContext();
		ctx.inSize = 1024;
		ctx.outSize = 10240;
		ctx.sql = "select * from app";
		//业务逻辑

		//server结束
		//RatelTrace.endServerTrace(ServiceStatusEnum.TIMEOUT.value(), "dubbo");
	}

	private void mockKafka(String traceId, String rpcId) {
		//server 开始
		RatelTrace.startServerTrace(traceId, rpcId, "kafka-proxy", "test-topic", "", "kafka", false);
		//server 结束
		RatelTrace.endServerTrace("success", "mq");
	}

	@Test
	public void testFullTrace() throws InterruptedException, IOException {
		String traceId = null;
		String rpcId = null;
		String appName = "bds-app";
		String unitName = "UserAccountQueryService";
		String unitInfo = "queryUserAccountExtByUid";
		String type = "dubbo";
		boolean sample = false;
		//server开始
		RatelTrace.startServerTrace(traceId, rpcId, appName, unitName, unitInfo, type, sample);

		//client开始,注意开始和结束type需要配对
		RatelTrace.startClientTrace(appName, "/pull", "GET", "http");
		//业务逻辑
		RatelContext ctx = RatelContext.getCurrentContext();
		new Thread(() -> {
			mockKafka(ctx.getTraceId(), ctx.getRpcId());
		}).start();
		Thread.sleep(100);
		//client 结束
		RatelTrace.endClientTrace("error", "http");
		//server 结束
		RatelTrace.endServerTrace("timeout" ,"dubbo");
		//System.in.read();
	}
}
