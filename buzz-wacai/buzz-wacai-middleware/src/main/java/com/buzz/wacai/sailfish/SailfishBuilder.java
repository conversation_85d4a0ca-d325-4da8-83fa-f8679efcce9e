package com.buzz.wacai.sailfish;

import com.wacai.common.redis.*;
import com.wacai.common.redis.provider.DefaultProvider;
import com.wacai.sailfish.Sailfish;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 **/
public class SailfishBuilder {

    public static SailfishBuilder builder() {
        return new SailfishBuilder();
    }

    private Env env = Env.test;

    private String schema;

    public SailfishBuilder env(Env env) {
        this.env = env;
        return this;
    }

    public SailfishBuilder schema(String schema) {
        this.schema = schema;
        return this;
    }

    public RedisCluster buildRedisCluster() throws IOException {
        ClusterConfig clusterConfig = new ClusterConfig();
        clusterConfig.setMaxIdle(20);
        clusterConfig.setMaxActive(20);
        //clusterConfig.setTimeBetweenEvictionRunsMillisAutoSet(false);

        if (env.seeds != null) {
            clusterConfig.setNodes(env.seeds);
            return new RedisCluster(clusterConfig, schema);
        } else {
            long appId = env.appId;
            String appKey = env.appKey;
            return Sailfish.newCluster(appId, appKey, schema, clusterConfig);
        }
    }

    public RedisClient buildRedisClient() {
        long appId = env.appId;
        String appKey = env.appKey;
        try {
            AbstractRedisClient redisClient = (AbstractRedisClient) Sailfish.newStandalone(appId,
                    appKey,
                    schema,
                    new DefaultProvider());
            redisClient.init();
            return redisClient;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public enum Env {
        my(10069, "e425d99cf7e6e036562e54b987cb4b68"),
        test(10003, "d7317ef13ea37c03f87fcd214755b341"),
        password(10061, "67c5b41d94c5fb8c59c6c538ca096d3b"),
        kvrocks_standlone(10081, "5167d17fa1b1ebe01aec9e0b17c3f94c"),
        local("127.0.0.1:6379,127.0.0.1:6380,127.0.0.1:6381"),
        kvrocks(10090,"a9dd8371d424f2d879d9cfe86f08392a");

        String seeds;
        long appId;
        String appKey;

        Env(long appId, String appKey) {
            this.appId = appId;
            this.appKey = appKey;
        }

        Env(String seeds) {
            this.seeds = seeds;
        }
    }

}
