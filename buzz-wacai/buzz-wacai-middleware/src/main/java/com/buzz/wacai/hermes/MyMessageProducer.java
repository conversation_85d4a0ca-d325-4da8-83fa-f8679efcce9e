package com.buzz.wacai.hermes;

import com.wacai.hermes.agent.producer.HermesProducer;
import com.wacai.hermes.agent.producer.impl.HermesHttpProducer;
import com.wacai.hermes.common.message.HermesMessage;
import com.wacai.hermes.common.message.MessageMeta;

public class MyMessageProducer {

	public static void main(String[] args) throws InterruptedException {
		
		HermesProducer producer = HermesHttpProducer.get();
        HermesMessage message = HermesMessage.builder().setTopic("payment.capital.transfer.msg").setData("helloworld".getBytes()).build();
        
        for(int i=0;i<Integer.MAX_VALUE;++i) {
        	MessageMeta ret = producer.produce(message);
        	System.out.println(ret);
        	Thread.sleep(100);
        }
		
		
	}
}
