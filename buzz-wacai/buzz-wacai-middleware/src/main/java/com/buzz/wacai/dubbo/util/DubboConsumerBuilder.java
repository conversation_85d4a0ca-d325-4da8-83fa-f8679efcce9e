package com.buzz.wacai.dubbo.util;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ReferenceConfig;
import com.alibaba.dubbo.rpc.service.GenericService;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022-06-23 17:11
 **/
public class DubboConsumerBuilder {
    private static ApplicationConfig application = new ApplicationConfig("demo-web");
    private DubboTestConfig.Env env = DubboTestConfig.Env.test;
    private boolean generic;
    private String interfaceName;
    private String version;
    private Class<?> interfaceType;
    private String address;

    public static Map<Class<?>,ReferenceConfig<?>> referenceConfigMap = new HashMap<>();

    public static DubboConsumerBuilder builder() {
        return new DubboConsumerBuilder();
    }

    public DubboConsumerBuilder generic(boolean generic) {
        this.generic = generic;
        return this;
    }

    public DubboConsumerBuilder interfaceName(String interfaceName) {
        this.interfaceName = interfaceName;
        return this;
    }

    public DubboConsumerBuilder version(String version) {
        this.version = version;
        return this;
    }

    public DubboConsumerBuilder version(Class<?> interfaceType) {
        this.interfaceType = interfaceType;
        return this;
    }

    public DubboConsumerBuilder address(String address) {
        this.address = address;
        return this;
    }

    /**
     * 普通 RPC
     * @param type
     * @param <T>
     * @return
     */
    public <T> T build(Class<T> type) {
        ReferenceConfig<T> reference = new ReferenceConfig<T>();
        reference.setApplication(application);
        buildAddress(reference);
        reference.setInterface(type);
        reference.setVersion(version);
        reference.setTimeout(5000);
        referenceConfigMap.put(type,reference);
        return reference.get();
    }

    private void buildAddress(ReferenceConfig reference){
        if (address != null) {
            reference.setUrl(String.format("dubbo://%s", address));
        } else {
            reference.setRegistry(DubboTestConfig.getRegistryConfig(env));
        }
    }
    /**
     * 泛化调用
     * @param interfaceName
     * @return
     */
     public GenericService build(String interfaceName) {
         return buildReferenceConfig(interfaceName).get();

     }

    public ReferenceConfig<GenericService>  buildReferenceConfig(String interfaceName) {
        ReferenceConfig<GenericService> reference = new ReferenceConfig<GenericService>();
        reference.setCheck(true);
        reference.setApplication(application);
        buildAddress(reference);
        // 弱类型接口名
        reference.setInterface(interfaceName);
        // 声明为泛化接口
        reference.setGeneric(true);
        reference.setVersion(version);
        reference.setTimeout(5000);
        return reference;

    }

}
