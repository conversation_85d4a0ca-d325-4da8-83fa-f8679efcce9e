package com.buzz.wacai.sailfish;

import com.buzz.wacai.sailfish.SailfishBuilder.Env;
import com.wacai.CacheType;
import com.wacai.common.redis.DefaultRedisClient;
import com.wacai.common.redis.Node;
import com.wacai.common.redis.RedisCluster;
import com.wacai.common.redis.RedisException;
import com.wacai.common.redis.commands.ScanParams;
import com.wacai.common.redis.commands.ScanResult;
import com.wacai.common.redis.provider.DefaultProvider;
import com.wacai.common.redis.provider.RedisProviders;
import com.wacai.common.redis.util.RedisClusterCRC16;
import lombok.extern.slf4j.Slf4j;
import org.junit.Assert;
import org.junit.Test;

import java.io.IOException;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class SailfishTest {

    private String errorType(int errorCode) {
        if (errorCode == 1) {
            return "write";
        } else if (errorCode == 2) {
            return "read";
        } else if (errorCode == 3) {
            return "all";
        }
        return "except";
    }

    @Test
    public void testZrangeByScore() throws IOException, RedisException {
        RedisCluster cluster = SailfishBuilder.builder().env(Env.test).buildRedisCluster();
        cluster.zadd("myzset", 1, "a");
        cluster.zadd("myzset", 2, "b");
        cluster.zadd("myzset", 3, "c");
        cluster.zadd("myzset", 4, "d");
        cluster.zadd("myzset", 5, "e");
        cluster.zadd("myzset", 6, "f");

        Set<String> set = cluster.zrangeByScore("myzset", "3", "5");
        System.out.println("set=" + set);

        set = cluster.zrangeByScore("myzset", "(3", "(5");
        System.out.println("set=" + set);
    }

    @Test
    public void test101() throws IOException, RedisException {
        RedisCluster cluster = SailfishBuilder.builder().env(Env.test).buildRedisCluster();
        cluster.set("test", "hello");
        cluster.set("test","hello","nx",1000);
        System.out.println(cluster.get("test"));
    }

    @Test
    public void testClusterFault() throws InterruptedException, IOException {
        RedisCluster cluster = SailfishBuilder.builder()
                .env(Env.my)
                .buildRedisCluster();

        ExecutorService executorService = Executors.newFixedThreadPool(40);
        final CountDownLatch latch = new CountDownLatch(40);

        for (int i = 0; i < 40; ++i) {
            executorService.submit(new Runnable() {
                @Override
                public void run() {

                    for (int i = 0; i < 50000; i++) {
                        String key = "test" + i;
                        int errorCode = 0;
                        StringBuffer errorMsg = new StringBuffer();
                        try {
                            cluster.setex(key, 3600, "value-" + String.valueOf(i));
                        } catch (Exception e) {
                            log.error("set error", e);
                            errorCode += 1;
                            errorMsg.append(e.getMessage() + "\r\n");
                        }
                        try {
                            String value = cluster.get(key);
                            if (i % 1000 == 0) {
                                System.out.println(key + "\t" + value + "\t" + RedisClusterCRC16.getSlot(key));
                            }
                        } catch (Exception e) {
                            log.error("get error", e);
                            errorCode += 2;
                            errorMsg.append(e.getMessage() + "\r\n");
                        }
                        if (errorCode > 0) {
                            log.info("ops " + errorType(errorCode) + " error slot:" + RedisClusterCRC16.getSlot(key)
                                    + ",key:" + key + ", error:" + errorMsg.toString());
//                            throw new RuntimeException("ops " + errorType(errorCode) + " error slot:" + RedisClusterCRC16.getSlot(key)
//                                    + ",key:" + key + ", error:" + errorMsg.toString());
                            System.out.println("========error==========");
                            latch.countDown();
                            return;
                        }
                    }
                    //Thread.sleep(2000);
                    System.out.println("========finish==========");
                    latch.countDown();
                }
            });
        }

        latch.await();
    }


    @Test
    public void testLocal() throws Exception {

        RedisCluster cluster = SailfishBuilder.builder()
                .env(Env.my)
                .buildRedisCluster();
        while (true) {
            System.out.println(cluster.get("test1")); //13026
            Thread.sleep(1000);
        }
    }

    @Test
    public void testKvrocks() throws Exception {
        RedisCluster cluster = SailfishBuilder.builder()
                .schema("ucenter:basic:ucenter_account_auth_token")
                .env(Env.kvrocks)
                .buildRedisCluster();

        System.out.println(cluster.get("test"));
        cluster.set("test", "hello world");
        System.out.println(cluster.get("test"));
        cluster.del("test");
        cluster.close();
    }

    @Test
    public void test_ForeverRunning() throws IOException {
        RedisCluster cluster = SailfishBuilder.builder()
                .env(SailfishBuilder.Env.my)
                .buildRedisCluster();

        for (int i = 0; i < Integer.MAX_VALUE; ++i) {
            String key = "test" + i;
            try {
                cluster.set(key, key);
                Assert.assertEquals(key, cluster.get(key));
                Thread.sleep(500);
                if (i % 10 == 0) {
                    System.out.println(key);
                }
            } catch (Exception e) {
                System.err.println("[+key+] " + e.getMessage());
            }
        }
    }

    @Test
    public void testFull() throws Exception {
//        ClusterConfig clusterConfig = new ClusterConfig();
//
//        clusterConfig.setMaxActive(8);
//        clusterConfig.setMaxIdle(8);
//        clusterConfig.setMinIdle(2);
//        clusterConfig.setTimeBetweenEvictionRunsMillisAutoSet(true);
//        clusterConfig.setPreparePool(true);
//
//        long appId = 10003;
//        String appKey = "d7317ef13ea37c03f87fcd214755b341";
//        String schema = "middleware:cache:cluster:test:";

        RedisCluster cluster = SailfishBuilder
                .builder()
                .schema("middleware:cache:cluster:test:")
                .buildRedisCluster();

        for (int i = 0; i < 100000; ++i) {
            cluster.set("name", "test2");
            if (i % 10 == 0) {
                System.out.println("name=" + cluster.get("name"));
            }
            cluster.del("name");
            Thread.sleep(500);
        }

        // 在程序关闭的时候释放资源
        cluster.close();
    }


    /**
     * 测试cluster多key
     */
    @Test
    public void testMultiKey() throws IOException, RedisException {
        RedisCluster cluster = SailfishBuilder.builder()
                .env(Env.test)
                .buildRedisCluster();

        System.out.println(cluster.mget("test1", "test2"));
    }

    @Test
    public void testScan() throws RedisException {
        //测试发现不可用
        String STARTER_CLUSTER = "middleware.cache.redis.starter";
        DefaultProvider provider = new DefaultProvider();
        provider.setCacheType(CacheType.redis);
        provider.setServers("172.16.49.82:6382");
        RedisProviders.addProfile(STARTER_CLUSTER, provider);

        DefaultRedisClient redisClient = new DefaultRedisClient();
        //redisClient.setSchema("test");
        redisClient.setClusterName(STARTER_CLUSTER);
        redisClient.init();

        ScanParams params = new ScanParams();
        params.match("test*").count(10);

        long cursor = 0;

        while (true) {
            ScanResult<String> scanResult = redisClient.scan(new Node("172.16.49.82",6382), cursor, params);

            cursor = scanResult.getCursor();
            List<String> list = scanResult.getResult();

            if ("0".equals(cursor)) {
                break; // 迭代结束
            }

            // 处理返回的key列表
            for (String mapentry : list) {
                System.out.println(mapentry);
                // 可以执行其他操作，例如删除key
                // jedis.del(mapentry);
            }

        }
    }
}
