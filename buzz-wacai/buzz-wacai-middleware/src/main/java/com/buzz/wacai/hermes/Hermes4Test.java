package com.buzz.wacai.hermes;

import com.wacai.hermes.agent.consumer.HermesConsumer;
import com.wacai.hermes.agent.producer.HermesProducer;
import com.wacai.hermes.agent.producer.impl.HermesHttpProducer;
import com.wacai.hermes.agent.util.HermesUtil;
import com.wacai.hermes.common.message.HermesMessage;
import com.wacai.hermes.common.message.MessageMeta;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class Hermes4Test {

    static {
        System.setProperty("middleware.management.port", "8080");
    }


    @Test
    public void testCreateTopic(){
        String result = HermesUtil.createTopic("test", "bairen.test.2000");
        System.out.println(result);
    }

    @Test
    public void testBatchCreateTopic() {
        for (int i = 200; i < 500; ++i) {
            try {
                String result = HermesUtil.createTopic("log", "bairen.test." + i);
                System.out.println(result);
            } catch (Exception e) {
                System.out.println("error " + e.getMessage());
            }

        }
    }

    @Test
    public void testBatchSendMessage() {

        HermesProducer producer = HermesHttpProducer.get();
        for (int i = 200; i < 300; ++i) {
            String topic = "bairen.test." + i;
            HermesMessage message = HermesMessage.builder()
                    .setTopic(topic)
                    .setData("test".getBytes())
                    .build();
            MessageMeta result = producer.produce(message);
            System.out.println("send message topic:" + topic + ",result:" + result);
        }

    }

    @Test
    public void testBatchConsumeMessage() throws IOException {
        int size = 500;
        ExecutorService es = Executors.newFixedThreadPool(size);
        for (int i = 0; i < size; i++) {
            final String topic = "bairen.test." + i;
            es.submit(new Runnable() {
                @Override
                public void run() {
                    log.info("start " + topic + " consumer");
                    HermesConsumer consumer = HermesBuilder.builder()
                            .topic(topic)
                            .consumerId(Thread.currentThread().getName())
                            .buildHermesConsumer();
                    consumer.start();
                }
            });
        }
        System.in.read();
    }
}
