package com.buzz.wacai.gateway;

import com.wacai.neptune.core.NeptuneConfig;
import com.wacai.neptune.core.NeptuneException;
import com.wacai.neptune.core.contract.Encryptors;
import com.wacai.neptune.core.contract.Signatures;
import com.wacai.neptune.sdk.ApiRequest;
import com.wacai.neptune.sdk.ApiResponse;
import com.wacai.neptune.sdk.NeptuneClient;
import com.wacai.neptune.sdk.client.Neptune;
import org.junit.Test;

public class NeptuneTest {

    @Test
    public void test() {
        NeptuneConfig config = Neptune.config()
                .host("http://neptune.test.wacai.info/api-entry")
                .appKey("vdcxe3qwdd5u")
                .appSecret("fh4ap4wy7nkxywu6")
                .timeoutMs(4000)
                .encryptor(Encryptors.RSA.getName())
                .verifier(Signatures.MAC.getName())
                .publicKey("MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCZmLXbvE/CNenyUzeoBgp1UamLwhT1Vfgz1Uh4KMxWKEaw8VB5VHdSAG9ZpoxyuZOHunnvPiCjSfzNTCahovc8Dcqg99HZ1JR4X+YZt4kh6t11MZI2AFAqg6ArTg7OSjVaYzZImWu5TIf74njxAK3pR/dGH/StXO+r3yVdJXVrxQIDAQAB")
                .build();
        NeptuneClient client = Neptune.client(config);
        ApiRequest apiRequest = new ApiRequest("flow.test", "1.0");
        String param = "{\"val\":\"501\"}";
        apiRequest.setData(param.getBytes());
        try {
            ApiResponse apiResponse = client.invoke(apiRequest);
            System.out.println(new String(apiResponse.getData()));
        } catch (NeptuneException e) {
            e.printStackTrace();
        }

    }
}
