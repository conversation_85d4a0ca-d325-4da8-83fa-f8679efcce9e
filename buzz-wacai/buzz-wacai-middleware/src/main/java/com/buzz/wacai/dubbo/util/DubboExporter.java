package com.buzz.wacai.dubbo.util;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ServiceConfig;

public class DubboExporter {

	private ServiceConfig<Object> serviceConfig;
	private ApplicationConfig application;

	public DubboExporter() {
		application = new ApplicationConfig();
		application.setName("service-provider");
	}

	public void export(Object service) {
		serviceConfig = new ServiceConfig<>();
		serviceConfig.setApplication(application);
		// 多个注册中心可以用setRegistries()
		serviceConfig.setRegistry(DubboTestConfig.registry_zk);
		serviceConfig.setInterface(service.getClass().getInterfaces()[0]);
		serviceConfig.setRef(service);
		serviceConfig.setVersion("1.0.0");
		serviceConfig.export();
		
		System.out.println("export success! waiting...."+serviceConfig);
		try {
			System.in.read();
			//Thread.sleep(10*1000);
		} catch (Exception e) {
		}
	}
}
