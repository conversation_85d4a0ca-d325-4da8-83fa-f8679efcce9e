package com.buzz.wacai.harmony;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.wacai.harmony.client.Harmony;
import com.wacai.harmony.common.constants.Constants;
import com.wacai.harmony.common.constants.RealNodeBootStatus;
import com.wacai.harmony.common.dto.ApplicationDTO;
import com.wacai.harmony.common.dto.DomainDTO;
import com.wacai.harmony.common.dto.RealNodeDTO;
import com.wacai.harmony.common.dto.ReporterRequest;

public class HarmonyTest {

	public static void main(String[] args) throws InterruptedException {

		ReporterRequest request = new ReporterRequest();

		ApplicationDTO applicationDTO = new ApplicationDTO();
		applicationDTO.setAppName("service-app");
	
		List<RealNodeDTO> nodeList = new ArrayList<>();

		RealNodeDTO accountNode = new RealNodeDTO();
		accountNode.setDomainKey("service-app");
		accountNode.setCellId("hzifc");
		accountNode.setHost("**************");
		accountNode.setPort("8080");
		accountNode.setWeight(10);
		accountNode.setHealthCheckPort(8080);
		accountNode.setBootStatus(RealNodeBootStatus.STARTUP.getStatus());
		
		Map<String, String> tags = new HashMap<>();
		tags.put(Constants.DomainTags.PROTOCOL_TYPE_KEY, "http");
		tags.put(Constants.RealNodeTags.SECOND_GROUP_TAG_KEY, "hzifc");
		accountNode.setExtTags(tags);
		
		
		nodeList.add(accountNode);

		Map<String, DomainDTO> domainMap  =new HashMap<String, DomainDTO>();
		
		DomainDTO domainDTO = new DomainDTO();
		domainDTO.setDomain("service-app");
		//domainDTO.setGroupName("hzifc");
		
		Map<String,String> extTags = new HashMap<String,String>();
		extTags.put(Constants.DomainTags.APPNAME_TAG_KEY, "service-app");
		extTags.put(Constants.DomainTags.PROTOCOL_TYPE_KEY, "http");
		domainDTO.setExtTags(extTags);
		
		domainMap.put("biz-app", domainDTO);
		
		
		request.setApplicationDTO(applicationDTO);
		request.setDomainMap(domainMap);
		request.setRealNodeList(nodeList);

		int status = Harmony.Reporter.report(request);
		System.out.println(status);

		//Thread.sleep(30 * 1000);

//		ReporterDTO reportDTO = new ReporterDTO();
//		reportDTO.setHost("**************");
//		reportDTO.setPort(8080);
//		reportDTO.setEnv("test");
//		reportDTO.setCellId("hzifc");
//		int status = Harmony.Reporter.reportDomainHostStatus("service-app", reportDTO, ReportTypeEnum.STARTUP);
//		System.out.println(status);
	}
}
