package com.buzz.wacai.jasmine;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;

import lombok.extern.slf4j.Slf4j;


@Slf4j
public class LogTest {

	public static class ThrowableProxy {
		private Throwable throwable;
		private String className;
		private String message;

		public ThrowableProxy(Throwable throwable) {
			super();
			this.throwable = throwable;
			this.className = throwable.getClass().getName();
			this.message = throwable.getMessage();
		}

		public Throwable getThrowable() {
			return throwable;
		}

		public String getClassName() {
			return className;
		}

		public String getMessage() {
			return message;
		}

	}

	public void test() {
		try {
			new Demo().throwExcepiton();
		} catch (Exception e) {
			 throw new RuntimeException("error", e);
			
		}
	}

	public void run() {
		try {
			test();
		} catch (Throwable e) {
			StringBuilder sb = new StringBuilder(2048);
			recursiveAppend(sb, null, 1, new ThrowableProxy(e));
			ByteArrayOutputStream out = new ByteArrayOutputStream(2048);
			e.printStackTrace(new PrintStream(out));
			System.out.println(new String(out.toByteArray()));
			log.error("i know",e);
		}

	}

	public static void main(String[] args) {
		new LogTest().run();
	}

	private static void indent(StringBuilder buf, int indent) {
		for (int j = 0; j < indent; j++) {
			buf.append("\t");
		}
	}

	private void subjoinFirstLine(StringBuilder buf, String prefix, int indent, ThrowableProxy tp) {
		LogTest.indent(buf, indent - 1);
		if (prefix != null) {
			buf.append(prefix);
		}
		buf.append(tp.getClassName()).append(": ").append(tp.getMessage());
	}

	private void recursiveAppend(StringBuilder sb, String prefix, int indent, ThrowableProxy tp) {
		if (tp == null)
			return;
		subjoinFirstLine(sb, prefix, indent, tp);
		sb.append(System.getProperty("line.separator"));
		subjoinSTEPArray(sb, indent, tp);
//		IThrowableProxy[] suppressed = tp.getSuppressed();
//		if (suppressed != null) {
//			for (IThrowableProxy current : suppressed) {
//				recursiveAppend(sb, CoreConstants.SUPPRESSED, indent + ThrowableProxyUtil.SUPPRESSED_EXCEPTION_INDENT,
//						current);
//			}
//		}
//		recursiveAppend(sb, CoreConstants.CAUSED_BY, indent, tp.getCause());
	}

	protected void subjoinSTEPArray(StringBuilder buf, int indent, ThrowableProxy tp) {
//		 	StackTraceElement[] stepArray = tp.getThrowable().getStackTrace();
//	        int commonFrames = tp.getCommonFrames();
//
//	        boolean unrestrictedPrinting = lengthOption > stepArray.length;
//
//	        int maxIndex = (unrestrictedPrinting) ? stepArray.length : lengthOption;
//	        if (commonFrames > 0 && unrestrictedPrinting) {
//	            maxIndex -= commonFrames;
//	        }
//
//	        int ignoredCount = 0;
//	        for (int i = 0; i < maxIndex; i++) {
//	            StackTraceElementProxy element = stepArray[i];
//	            if (!isIgnoredStackTraceLine(element.toString())) {
//	                ThrowableProxyUtil.indent(buf, indent);
//	                printStackLine(buf, ignoredCount, element);
//	                ignoredCount = 0;
//	                buf.append(CoreConstants.LINE_SEPARATOR);
//	            } else {
//	                ++ignoredCount;
//	                if (maxIndex < stepArray.length) {
//	                    ++maxIndex;
//	                }
//	            }
//	        }
//	        if (ignoredCount > 0) {
//	            printIgnoredCount(buf, ignoredCount);
//	            buf.append(CoreConstants.LINE_SEPARATOR);
//	        }
//
//	        if (commonFrames > 0 && unrestrictedPrinting) {
//	            ThrowableProxyUtil.indent(buf, indent);
//	            buf.append("... ").append(tp.getCommonFrames()).append(" common frames omitted").append(CoreConstants.LINE_SEPARATOR);
//	        }
	}

	static class Demo {
		public void throwExcepiton() {
			throw new RuntimeException("hahha");
		}
	}
}
