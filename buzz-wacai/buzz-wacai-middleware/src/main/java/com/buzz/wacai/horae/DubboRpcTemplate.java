//package com.buzz.wacai.horae;
//
//import com.alibaba.dubbo.config.ApplicationConfig;
//import com.alibaba.dubbo.config.ReferenceConfig;
//import com.alibaba.dubbo.config.RegistryConfig;
//
//public class DubboRpcTemplate {
//
//	private static DubboRpcTemplate instance = new DubboRpcTemplate();
//
//	private ApplicationConfig appConfig;
//
//	private RegistryConfig registry;
//
//	public DubboRpcTemplate() {
//		init(EvnType.hchf);
//	}
//
//	public void init(EvnType type) {
//		// 当前应用配置
//		appConfig = new ApplicationConfig();
//		appConfig.setName("consumer");
//
//		// 连接注册中心配置
//		registry = new RegistryConfig();
//		registry.setProtocol("zookeeper");
//		registry.setAddress(type.zk);
//		registry.setGroup(type.group);
//	}
//
//	public <T> T getProxy(Class<T> interfaceClass) {
//
//		ReferenceConfig<T> reference = new ReferenceConfig<T>(); // 此实例很重，封装了与注册中心的连接以及与提供者的连接，请自行缓存，否则可能造成内存和连接泄漏
//		reference.setApplication(appConfig);
//		reference.setRegistry(registry);
//		reference.setInterface(interfaceClass);
////		reference.setVersion("1.0.0");
//		reference.setTimeout(5000);
//
//		return reference.get();
//	}
//
//	public static DubboRpcTemplate get() {
//		return instance;
//	}
//
//
//	static enum EvnType{
//		test("zktestserver1.wacai.info:22181","dubbo_test"),
//		hchf("zktestserver1.wacai.info:22181","dubbo_test_hchf");
//		private String zk;
//		private String group;
//
//		 EvnType(String zk,String group){
//			this.zk =zk;
//			this.group=group;
//		}
//
//	}
//}
