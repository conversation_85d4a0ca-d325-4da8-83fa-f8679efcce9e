package com.buzz.wacai.redalert;

import com.wacai.pt.redalert.api.common.BusinessUnit;
import com.wacai.pt.redalert.api.utils.RedAlertUtils;

/**
 * <AUTHOR>
 * @description
 **/
public class RedAlertTest {

    public void test(){
        RedAlertUtils redAlertUtils = RedAlertUtils.build((BusinessUnit)BusinessUnit.other, (String)"支付清算").addDatabase("payment_monitor");

        String bizName = "";
        redAlertUtils.addMeasurement("bizName")
                .addTag("monitorTag", "monitorName")
                .addTag("monitorTag2", "customizedMsg")
                .addField("className", "className")
                .addField("methodName","methodName")
                .addField("customizedMsg", "customizedMsg")
                .addField("exceptionMsg", "throwableMsg")
                .addField("monitorName","monitorName")
                .produce();


    }
}
