package com.buzz.wacai.jasmine;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class DigestTest {

	public static void main(String[] args) throws NoSuchAlgorithmException {
		MessageDigest digest = MessageDigest.getInstance("MD5");
		digest.update("test".getBytes());
		System.out.println(toHexString(digest.digest()));
	}

	public static String toHexString(byte[] bytes) {
		if (bytes == null) {
			return null;
		}

		StringBuilder buffer = new StringBuilder(bytes.length * 2);

		for (byte aByte : bytes) {
			int b = aByte & 0xFF;
			if (b < 0x10) {
				buffer.append('0');
			}
			buffer.append(Integer.toHexString(b));
		}

		return buffer.toString();
	}
}
