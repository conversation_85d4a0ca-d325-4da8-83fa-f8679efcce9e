package com.buzz.wacai.jasmine;

import java.io.IOException;

import com.wacai.harmony.client.Harmony;
import com.wacai.harmony.common.dto.HarmonyQueryOptions;
import com.wacai.jasmine.common.Constants;

public class HarmonyTest {

	public static void main(String[] args) throws IOException {
		
		System.setProperty("HARMONY_SERVER_LIST_GET_URL", "http://localhost:8080/harmony/getServerList");
		System.out.println(Harmony.selectAll(new HarmonyQueryOptions(Constants.JASMINE_SERVER_DOMAIN_KEY, "default", "default", null)));
		System.in.read();
	}
}
