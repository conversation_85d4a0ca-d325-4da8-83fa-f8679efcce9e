package com.buzz.wacai.dubbo.util;

import com.alibaba.dubbo.config.ProtocolConfig;
import com.alibaba.dubbo.config.RegistryConfig;

public class DubboTestConfig {

    public static RegistryConfig registry_zk = getRegistryConfig(Env.test);

    public ProtocolConfig protocolConfig = getProtocolConfig();

    public static RegistryConfig getRegistryConfig(Env env) {

        RegistryConfig registry = new RegistryConfig();
        registry.setProtocol("zookeeper");
        registry.setAddress(env.host);
        registry.setGroup(env.group);

        return registry;
    }


    private static ProtocolConfig getProtocolConfig() {
        ProtocolConfig protocol = new ProtocolConfig();
        protocol.setName("dubbo");
        protocol.setPort(20880);
        protocol.setThreads(1);

        return protocol;
    }

    public static enum Env {
        dev("localhost:2181", "dubbo_test"),
        test("zktestserver1.wacai.info:22181", "dubbo_test"),
        hchf("zktestserver1.wacai.info:22181", "dubbo_test_hchf");

        private String host;
        private String group;

        Env(String host, String group) {
            this.host = host;
            this.group = group;
        }

    }
}
