package com.buzz.wacai.dubbo;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.URL;
import com.alibaba.dubbo.config.ReferenceConfig;
import com.alibaba.dubbo.registry.RegistryFactory;
import com.alibaba.dubbo.registry.integration.RegistryProtocol;
import com.alibaba.dubbo.registry.zookeeper.ZookeeperRegistry;
import com.alibaba.dubbo.rpc.service.GenericService;
import com.buzz.wacai.dubbo.mock.BizService;
import com.buzz.wacai.dubbo.util.DubboConsumerBuilder;
import com.wacai.horae.model.IDRange;
import com.wacai.horae.service.UniversalIdService;
import com.wacai.service.holidays.service.HolidaysService;
import lombok.extern.slf4j.Slf4j;
import org.I0Itec.zkclient.IZkChildListener;
import org.I0Itec.zkclient.ZkClient;
import org.junit.Test;
import org.springframework.util.ReflectionUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Slf4j
public class DubboConsumerTest {


    @Test
    public void testInvokeHoliday() {
        HolidaysService holidaysService = DubboConsumerBuilder.builder().build(HolidaysService.class);
        Date date = new Date();
        int ret = holidaysService.isHoliday(date);
        System.out.println(date + "=" + ret);
        date = new Date(date.getTime() + ********);

    }

    @Test
    public void testInvokeBiz() {
        BizService bizService = DubboConsumerBuilder.builder().build(BizService.class);
        bizService.sayHelloToWafe(1l);
    }

    @Test
    public void testUserAccountService() {
        //泛化调用
        GenericService service = DubboConsumerBuilder.builder().build("com.wacai.finance.share.user.account.UserAccountService");
        Object obj = service.$invoke("getUserAccountByUid", new String[]{"int"}, new Object[]{********});
        System.out.println("result.type=" + obj.getClass() + ", content=" + obj);
    }



    @Test
    public void test() throws ClassNotFoundException {

        Class klass = Thread.currentThread().getContextClassLoader().loadClass("com.alibaba.dubbo.remoting.zookeeper.zkclient.ZkclientZookeeperClient");
        ReflectionUtils.doWithFields(klass,field->{
            System.out.println(field);
        });

    }
    //测试调用不存在的Dubbo服务
    @Test
    public void testNotExistService() {
        //下面的测试代码存在内存泄露
        ReferenceConfig<GenericService> referenceConfig=null;
        for (int i = 0; i < 5; ++i) {
            referenceConfig = DubboConsumerBuilder
                    .builder()
                    .buildReferenceConfig("com.wacai.finance.share.user.account.UserAccountService2");
            try {
                //泛化调用
                GenericService service = referenceConfig.get();
                Object obj = service.$invoke("getUserAccountByUid", new String[]{"int"}, new Object[]{********});
                System.out.println("result.type=" + obj.getClass() + ", content=" + obj);
            } catch (Exception e) {
               log.error("dubbo invoke error",e.getMessage());
            } finally {
                referenceConfig.destroy();
            }
        }



        URL url0 = referenceConfig.toUrl();
        log.info("url0:{}", url0);
        URL url = url0.setProtocol(url0.getParameter(Constants.REGISTRY_KEY, Constants.DEFAULT_REGISTRY))
                .removeParameter(Constants.REGISTRY_KEY);
        log.info("url:{}", url);
        RegistryProtocol registryProtocol = RegistryProtocol.getRegistryProtocol();
        ReflectionUtils.doWithFields(RegistryProtocol.class, field0->{
            ReflectionUtils.makeAccessible(field0);
            if(field0.getName().equals("registryFactory")){ //com.alibaba.dubbo.registry.RegistryFactory
                RegistryFactory registryFactory = (RegistryFactory) field0.get(registryProtocol);
                ZookeeperRegistry zookeeperRegistry = (ZookeeperRegistry) registryFactory.getRegistry(url);
                log.info("zookeeperRegistry:{}",zookeeperRegistry);
                log.info("=================================================");
                ReflectionUtils.doWithFields(ZookeeperRegistry.class, field->{
                    if(field.getName().equals("zkClient")){
                        ReflectionUtils.makeAccessible(field);
                        Object zookeeperClient = field.get(zookeeperRegistry);//com.alibaba.dubbo.remoting.zookeeper.zkclient.ZkclientZookeeperClient
                        ReflectionUtils.doWithFields(zookeeperClient.getClass(), field1 ->{
                            if(field1.getName().equals("client")){
                                ReflectionUtils.makeAccessible(field1);
                                Object ZkClientWrapper = field1.get(zookeeperClient); //com.alibaba.dubbo.remoting.zookeeper.zkclient.ZkClientWrapper
                                ReflectionUtils.doWithFields(ZkClientWrapper.getClass(), field2 ->{
                                    if(field2.getName().equals("client")){
                                        ReflectionUtils.makeAccessible(field2);
                                        ZkClient zkClient = (ZkClient) field2.get(ZkClientWrapper); //org.I0Itec.zkclient.ZkClient
                                        ReflectionUtils.doWithFields(zkClient.getClass(), field3 ->{
                                            if(field3.getName().equals("_childListener")){
                                                ReflectionUtils.makeAccessible(field3);
                                                Map<String, Set<IZkChildListener>> _childListener = (Map<String, Set<IZkChildListener>>) field3.get(zkClient);
                                                log.info("=================================================");
                                                for(Map.Entry<String, Set<IZkChildListener>> entry: _childListener.entrySet()){
                                                    log.info(entry.getKey()+"\t"+entry.getValue().size());
                                                }
                                                log.info("=================================================");
                                            }
                                        });
                                    }
                                });
                            }
                        } );
                    }
                });

            }
        });
    }


    @Test
    public void testDubboTestService() {
        //泛化调用
        GenericService service = DubboConsumerBuilder.builder().address("localhost:20880").version("1.1").build("com.wacai.quntum.example.common.DubboTestService");
        Object obj = service.$invoke("echo", new String[]{"java.lang.String"}, new Object[]{"hello"});
        System.out.println("result.type=" + obj.getClass() + ", content=" + obj);
    }

    @Test
    public void testSeqService() {

        UniversalIdService service = DubboConsumerBuilder.builder().build(UniversalIdService.class);
        IDRange data = service.getNextRange("user", "account_app_id", 10);
        System.out.println(data);
        DubboConsumerBuilder.referenceConfigMap.get(UniversalIdService.class).destroy();
    }

    @Test
    public void testBizService() {
        //泛化调用
        try {
            GenericService service = DubboConsumerBuilder.builder().build("com.wacai.app.service.BizService");
//			for(int i=0;i<1;++i)
//			service.$invoke("sayHelloToWafe", new String[] { "long" }, new Object[] { ********l });
            Map<String, Object> user = new HashMap<>();
            user.put("name", "test-" + System.currentTimeMillis());
            user.put("age", 12);
            user.put("score", 0.1f);
            service.$invoke("createOrder", new String[]{"java.lang.Long", "com.wacai.app.service.User"}, new Object[]{********l, user});

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

}
