package com.buzz.wacai.sailfish;

import com.alibaba.dubbo.common.utils.NamedThreadFactory;
import com.buzz.wacai.sailfish.SailfishBuilder.Env;
import com.wacai.common.redis.ClusterConfig;
import com.wacai.common.redis.RedisClient;
import com.wacai.common.redis.RedisCluster;
import com.wacai.common.redis.RedisException;
import org.junit.BeforeClass;
import org.junit.Test;

import java.io.IOException;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.function.Consumer;

public class KvRocksTest {

    private static RedisCluster cluster;

    @BeforeClass
    public static void init() throws IOException {
        //ClusterConfig clusterConfig = new ClusterConfig();
        //clusterConfig.setNodes("172.16.49.83:7381,172.16.49.96:7381,172.16.49.95:7381");
        //cluster = new RedisCluster(clusterConfig, "ucenter:basic");
        cluster = SailfishBuilder.builder().env(Env.kvrocks).buildRedisCluster();
    }

    @Test
    public void testGetAndSet() throws RedisException {
        cluster.set("test_get", "hello kvrocks");
        System.out.println(cluster.get("test_get"));
    }

    @Test
    public void testWhile() throws RedisException, InterruptedException {
        while (true) {
            cluster.get("test_get");
            Thread.sleep(100);
        }
    }

    @Test
    public void testExpire() throws RedisException, InterruptedException {
        cluster.setex("test_expire", 1, "hello kvrocks");
        System.out.println(cluster.get("test_expire"));
        Thread.sleep(1000);
        System.out.println(cluster.get("test_expire"));
    }


    private String buildValue() {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < 100; ++i) {
            sb.append(UUID.randomUUID().toString());
        }
        return sb.toString();
    }

    private void batchExecute(Consumer<Integer> consumer) throws InterruptedException {
        Executor executor = Executors.newFixedThreadPool(10, new NamedThreadFactory("kvrocks-test"));
        CountDownLatch latch = new CountDownLatch(10);
        for (int n = 0; n < 10; ++n) {
            final int index = n;
            executor.execute(() -> {
                int start = 50000 * index;
                for (int i = 0; i < 50000; ++i) {

                    consumer.accept(start);

                    ++start;

                    if (i % 1000 == 0) {
                        System.out.println("set start=" + start);
                    }
                }

                latch.countDown();
            });
        }
        latch.await();
    }

    @Test
    public void testSetStandalone() throws IOException, RedisException, InterruptedException {
        RedisClient redisClient = SailfishBuilder.builder().env(Env.kvrocks_standlone).buildRedisClient();
        final String value = buildValue();

        batchExecute((start)->{
            try {
                redisClient.set("kktest-" + start, value + start);
            } catch (RedisException e) {
                e.printStackTrace();
            }
        });

        System.out.println("execute end");
    }

    @Test
    public void testSet() throws IOException, RedisException, InterruptedException {
        RedisCluster redisCluster = SailfishBuilder.builder().env(Env.kvrocks).buildRedisCluster();
        final String value = buildValue();
        System.out.println("test:" + value);

        batchExecute((start)->{
            try {
                redisCluster.set("kktest-" + start, value + start);
            } catch (RedisException e) {
                e.printStackTrace();
            }
        });

        System.out.println("execute end");

    }

}
