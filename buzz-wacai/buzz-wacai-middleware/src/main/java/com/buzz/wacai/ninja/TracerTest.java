package com.buzz.wacai.ninja;

import java.util.concurrent.ConcurrentMap;

import org.junit.Test;
import org.slf4j.MDC;

import com.wacai.brave.Span;
import com.wacai.brave.Span.Kind;
import com.wacai.brave.propagation.TraceContext;
import com.wacai.common.ninja.client.NinjaClientUtil;
import com.wacai.common.ninja.client.SpanState;
import com.wacai.common.ninja.client.TracerAdaptor;
import com.wacai.common.ninja.client.metric.enums.EndpointEnum;
import com.wacai.common.ninja.client.metric.enums.ServiceTypeEnum;
import com.wacai.brave.Tracer;
import com.wacai.zipkin.reporter.Reporter;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * 
 * 
<pre>
Using a tracer, you can create a root span capturing the critical path of a request. Child spans can be created to allocate latency relating to outgoing requests. Here's a contrived example:

Span twoPhase = tracer.newTrace().name("twoPhase").start();
 try {
   Span prepare = tracer.newChild(twoPhase.context()).name("prepare").start();
   try {
     prepare();
   } finally {
     prepare.finish();
   }
   Span commit = tracer.newChild(twoPhase.context()).name("commit").start();
   try {
     commit();
   } finally {
     commit.finish();
   }
 } finally {
   twoPhase.finish();
 }
 }
 
 </pre>
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class TracerTest {
	static String applicationName = "trace-test";

	private void prepare() {

	}

	private void commit() {

	}

	/**
	 * 一个调用链包含三段请求。来源 Tracer注释
	 */
	@Test
	public void testTracer() {

		Tracer tracer = Tracer.newBuilder().localServiceName(applicationName).reporter(new MyReporter()).build();

		Span root = tracer.newTrace().name("root").start();
		Span prepare = tracer.newChild(root.context()).name("prepare").start();
		try {
			prepare();
		} finally {
			prepare.finish();
		}
		Span commit = tracer.newChild(root.context()).name("commit").start();
		try {
			commit();
		} finally {
			commit.finish();
		}
		root.finish();
	}

	private void addTag(String key, String value) {
		Span span = SpanState.getCurrentSpan();
		if (span == null)
			span = SpanState.getCurrentServerSpan();
		span.addBusinessContext(key, value);
	}

	/**
	 * 
	 * 创建并开启一个Span
	 * 
	 * clone from NinjaTracerFacade
	 */
	private static void doStartServerTrace(String spanName, TraceContext parentContext,
			ConcurrentMap<String, String> lastBusinessContext) {
		//记录时间戳
		long timestamp = System.currentTimeMillis() * 1000L;
		Tracer tracer = TracerAdaptor.getTracer();
		Span currentSpan = null;
		if (parentContext == null) {
			currentSpan = tracer.newTrace().name(spanName).kind(Kind.SERVER);
		} else {
			currentSpan = tracer.joinSpan(parentContext).name(spanName).kind(Kind.SERVER);
		}
		//加入用户自定义上下文
		currentSpan.transBusinessContext(lastBusinessContext);
		SpanState.setCurrentServerSpan(currentSpan);
		currentSpan.start(timestamp);
		MDC.put(NinjaClientUtil.TID, currentSpan.context().traceIdString());
	}

	/**
	 * clone from NinjaTracerFacade
	 */
	private static void doFinishServerTrace() {
		try {
			Span currentSpan = SpanState.getCurrentServerSpan();
			if (currentSpan == null)
				return;
			currentSpan.finish();
			SpanState.removeCurrentServerSpan();
		} catch (Throwable t) {
			log.warn("NinjaTracerFacade.doFinishServerTrace occur error ...", t);
		} finally {
			MDC.remove(NinjaClientUtil.TID);
		}
	}

	private static void doStartClientTrace(String spanName) {
		Span parent = SpanState.getCurrentSpan();
		long timestamp = System.currentTimeMillis() * 1000L;
		Tracer tracer = TracerAdaptor.tracer;

		Span currentSpan = null;
		if (parent == null) {
			currentSpan = tracer.newTrace().name(spanName).kind(Kind.CLIENT);
		} else {
			currentSpan = tracer.newChild(parent.context()).name(spanName).kind(Kind.CLIENT);
			currentSpan.setParent(parent);
		}
		SpanState.setCurrentClientSpan(currentSpan);

		// 数据透传
		ConcurrentMap<String, String> currentContext = null;
		if (parent != null) {
			currentContext = parent.getBusinessContext();
		}
		currentSpan.transBusinessContext(currentContext);
		//开启本次Span
		currentSpan.start(timestamp);
		//添加tag
		NinjaClientUtil.addBusinessContext(NinjaClientUtil.APP_GROUP, NinjaClientUtil.getAppGroup());
		NinjaClientUtil.addBusinessContext(NinjaClientUtil.CELL_ID, NinjaClientUtil.getCellId());
	}

	private static void doFinishClientTrace() {
		Span currentSpan = SpanState.getCurrentClientSpan();
		if (currentSpan == null)
			return;
		currentSpan.finish();
		SpanState.removeCurrentClientSpan();

	}

	@Test
	public void testTracerByNinja() throws InterruptedException {
		TracerAdaptor.getTracer();
		//doStartServerTrace
		doStartServerTrace("/index", null, null);
		//add tag
		addTag("app_group", "test");
		addTag("app_idc", "hzifc");

		//http 客户端埋点
		doStartClientTrace("/kafka/publish");
		addTag(NinjaClientUtil.ZIPKIN_COMPONENT_KEY, "http-client");
		addTag(NinjaClientUtil.HTTP_URL, "http://localhost:8080/kafka/publish");
		//发送透传数据 HTTP_TID,HTTP_CLIENT_IP,HTTP_SID,HTTP_SAMPLE
		doFinishClientTrace();

		Thread.sleep(1000);
		////返回结果
		addTag(NinjaClientUtil.HTTP_STATUS_CODE, "200");
		doFinishServerTrace();
	}

	private static class MyReporter implements Reporter<com.wacai.zipkin.Span> {

		@Override
		public void report(com.wacai.zipkin.Span span) {
			log.info("report span=" + span.toString());
		}
	}

	private static class TracerAdaptor {
		static volatile Tracer tracer = null;
		public static Tracer getTracer() {
			if (tracer == null) {
				tracer = Tracer.newBuilder().localServiceName(applicationName).reporter(new MyReporter()).build();
			}
			return tracer;
		}
	}
}
