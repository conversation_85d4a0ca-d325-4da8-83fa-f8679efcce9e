package com.buzz.wacai.hermes;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.buzz.wacai.util.IOUtils;
import com.google.common.collect.Lists;
import com.wacai.hermes.ApiUtil;
import com.wacai.hermes.agent.config.HermesAppConfig;
import com.wacai.hermes.agent.consumer.HermesConsumer;
import com.wacai.hermes.agent.producer.HermesProducer;
import com.wacai.hermes.agent.producer.impl.HermesHttpProducer;
import com.wacai.hermes.agent.util.HermesUtil;
import com.wacai.hermes.agent.util.TopicVo;
import com.wacai.hermes.common.http.HttpBuilder;
import com.wacai.hermes.common.message.HermesMessage;
import com.wacai.hermes.common.message.MessageMeta;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class Hermes2Test {

    static {
        System.setProperty("middleware.management.port", "8864");
    }

    @Test
    public void testCreateTopic(){
        TopicVo dlqTopicVo = HermesUtil.getTopic("linkup.record.event.prediction.DLQ");
        if (null == dlqTopicVo) {
            HermesUtil.createTopic("test","linkup.r·ecord.event.prediction.DLQ");
        }
    }

    @Test
    public void test() {
        HermesMessage message = HermesMessage.builder()
                .setTopic("bairen.test.1")
                .setPartition(0)
                .setData("hello".getBytes())
                .build();

        String jsonStr = new String(JSON.toJSONBytes(message));
        System.out.println(jsonStr);
    }


    @Test
    public void testSendMsg_Loop() throws IOException, InterruptedException {
        while (true) {
            testSendMsg();
            Thread.sleep(1000);
        }
    }

    @Test
    public void testSendMsg() throws IOException, InterruptedException {
        HermesMessage message = HermesMessage.builder()
                .setTopic("bairen.test.101")
                .setPartition(0)
                .setData("hello".getBytes())
                .build();
        HermesProducer producer = HermesHttpProducer.get();
        MessageMeta result = producer.produce(message);
        System.out.println("result=" + JSON.toJSONString(result));
        Thread.sleep(1000);
    }

    /**
     * 参考 AuthAPI.auth()
     */
    @Test
    public void testSendWithAuth(){
        HermesAppConfig appConfig = HermesAppConfig.get();
        appConfig.setAppName("biz-app"); // uav上的应用
        appConfig.setUserId("biz-app");
        appConfig.setSecretKey("xhmE/VQe40P1rz9EUzaBkqTpbH+JiOycLkOIm56emSDJ1X0zb0vexOhB8y6bfnAwtveovJyFBcX935OmgDjm9g==");

        HermesMessage message = HermesMessage.builder()
                .setTopic("hchf.pass.user.login")
                .setData("hello".getBytes())
                .build();
        HermesProducer producer = HermesHttpProducer.get();
        MessageMeta result = producer.produce(message);
        System.out.println("result=" + JSON.toJSONString(result));
    }

    //开启认证之后，需要手动申请消费者配置
    @Test
    public void testConsumerWithAuth() throws IOException {
        HermesAppConfig appConfig = HermesAppConfig.get();
        appConfig.setAppName("biz-app"); // uav上的应用
        appConfig.setUserId("biz-app");
        appConfig.setSecretKey("xhmE/VQe40P1rz9EUzaBkqTpbH+JiOycLkOIm56emSDJ1X0zb0vexOhB8y6bfnAwtveovJyFBcX935OmgDjm9g==");


        HermesConsumer consumer = HermesBuilder.builder()
                .group("biz-app")
                .topic("hchf.pass.user.login")
                .buildHermesConsumer();
        consumer.start();

        System.in.read();
    }

    @Test
    public void testSendMsgBatch() throws IOException, InterruptedException {
        HermesProducer producer = HermesHttpProducer.get();
        String topic = "bairen.test.101";
        for (int i = 0; i < 100; ++i) {
            MessageMeta result = producer.produce(topic, null, String.valueOf(i).getBytes());
            System.out.println("result=" + JSON.toJSONString(result));
        }
    }

    /**
     * 测试发送消息，topic、partition 不存在
     *
     * @throws IOException
     */
    @Test
    public void testSendMsg_PartitionInvalid() throws IOException {
        HermesMessage message = HermesMessage.builder()
                .setTopic("fund.report.sub.file")
                .setPartition(1)
                .setData("test".getBytes())
                .build();
        HermesProducer producer = HermesHttpProducer.get();
        MessageMeta result = producer.produce(message);
        System.out.println("result=" + result);
    }


    @Test
    public void testConsumeLongtime() throws IOException {
        HermesConsumer consumer = HermesBuilder.builder()
                .topic("manyou.test")
                .group("buzz.app")
                .buildHermesConsumer();
        consumer.start();
        System.out.println("======Consumer started!======");
        System.in.read();
    }

    /**
     * 测试指定的partition
     * @throws IOException
     */
    @Test
    public void testConsumeSpecialPartition() throws IOException {
        HermesConsumer consumer = HermesBuilder.builder()
                .topic("manyou.test")
                .group("bairen-test-2")
                .partition(2)
                .buildHermesConsumer();
        consumer.start();
        System.out.println("======Consumer started!======");
        System.in.read();
    }


    /**
     * 测试超过消费超过30秒
     *
     * @throws IOException
     */
    @Test
    public void testConsumeTimeout() throws IOException {
        String topic = "bairen.test.101";
        String group = "bairen-test-2";
        HermesConsumer consumer = HermesBuilder.builder()
                .topic(topic)
                .group(group)
                .consumer(message -> {
                    long latency = System.currentTimeMillis() - message.getTimestamp();
                    log.info("receiveMessage topic={},\r\n offset={},partition={},latency={}",
                            topic,
                            message.getOffset(),
                            message.getPartition(),
                            latency);
                    log.info("hold");
                    try {
                        Thread.sleep(1 * 1000);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                    log.info("finish");

                })
                .buildHermesConsumer();
        consumer.start();
        System.out.println("======Consumer started!======");
        System.in.read();

    }

    @Test
    public void testConsumeError() throws IOException {
        String topic = "bairen.test.101";
        String group = "bairen-test-2";
        HermesConsumer consumer = HermesBuilder.builder()
                .topic(topic)
                .group(group)
                .injectError(true)
                .buildHermesConsumer();
        consumer.start();
        System.out.println("======Consumer started!======");
        System.in.read();
    }

    /**
     * 测试同一个group多个consumer是否重复消费
     * 1、正常场景不会重复消费，原因是consumer-A拉取会加锁，consumer-B在fetch之前获取锁会失败
     * 2、失败场景中，假设consumer-A处理消息抛出异常，会调用 consumer.pause()缩短lock的时间到5秒，5秒内锁还在，5秒后会再次重试也不会重复消费
     *
     * @throws IOException
     */
    @Test
    public void testConsumeConcurrent() throws IOException {
        String topic = "bairen.test.101";
        String group = "bairen-test-2";
        AtomicInteger counter = new AtomicInteger();
        for (int i = 0; i < 2; ++i) {
            final int currentId = i;
            HermesConsumer consumer = HermesBuilder.builder()
                    .topic(topic)
                    .group(group)
                    .injectError(true)
                    .consumer(message -> {
                        try {
                            Thread.sleep(1000);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }

                        if (counter.getAndIncrement() % 2 == 0) {
                            log.error("error " + currentId + " receive \t" + message.getTopic() + "\t" + message.getOffset() + " " + counter.get());
                            throw new RuntimeException("number error");
                        }
                        log.info(currentId + " receive \t" + message.getTopic() + "\t" + message.getOffset() + " " + counter.get());
                    })
                    .buildHermesConsumer();
            consumer.start();
            System.out.println("======Consumer started!======");
        }
        System.in.read();
    }
//    @Test
//    public void test_MultiConsume() throws IOException {
//        ExecutorService es = Executors.newFixedThreadPool(10);
//        CyclicBarrier barrier = new CyclicBarrier(10);
//        for (int i = 0; i < 10; i++) {
//            es.submit(new Runnable() {
//                @Override
//                public void run() {
//                    try {
//                        barrier.await();
//                    } catch (Exception e) {
//                        e.printStackTrace();
//                    }
//                    log.info("start consumer");
//                    HermesConsumer consumer = HermesBuilder.builder()
//                            .consumerId(Thread.currentThread().getName())
//                            .buildHermesConsumer();
//                    consumer.start();
//                }
//            });
//        }
//        System.in.read();
//    }

    @Test
    public void testConsumeDrc() throws IOException {

//		HermesAppConfig appConfig = HermesAppConfig.get();
//		appConfig.setAppName("bairen-test-app");

        HermesConsumer consumer = HermesBuilder.builder()
                .group("drc.sinker")
                .topic("db.user_sharding.ucenter_account")
                .buildHermesConsumer();
        consumer.start();
        System.in.read();

    }

//    static void sendMsg() {
//
//        JSONObject jsonObject = new JSONObject();
//        jsonObject.put("type", "update");
//        jsonObject.put("nickname", "test");
//
//        HermesMessage message = HermesMessage.builder().setTopic("bairen.test")
//                .setData(jsonObject.toJSONString().getBytes()).build();
//
//        // 上层API
//        HermesProducer producer = HermesHttpProducer.get();
//        MessageMeta result = producer.produce(message);
//
//    }

    @Test
    public void testSendByHttp2() {
        String topic = "bairen.test";
        String appName = "wups";
        String proxyUrl = "http://hermes-proxy-log.middleware.wse.test.wacai.info";
        List<String> params = Lists.newArrayList("topic=" + ApiUtil.encode(topic),
                "appName=" + ApiUtil.encode(appName));

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("type", "update");
        jsonObject.put("nickname", "test");
        HermesMessage message = HermesMessage.builder()
                .setData(jsonObject.toJSONString().getBytes())
                .build();
        HttpBuilder httpBuilder = HttpBuilder.builder()
                .desc("produce")
                .url(proxyUrl + "/hermes-proxy/sendSingle")
                .build();
        MessageMeta resp = httpBuilder.execute(message, MessageMeta.class, params.toArray());
        System.out.println(resp);
    }

    @Test
    public void testSendHttp() throws Exception {
        URL url = new URL(
                "http://hermes-proxy-log.middleware.wse.test.wacai.info/hermes-proxy/sendSingle?topic=bairen.test&appName=middleware-test-app");

        HttpURLConnection conn = (HttpURLConnection) url.openConnection();
        conn.setDoOutput(true);
        conn.setRequestMethod("POST");
        conn.setReadTimeout(3000);

        conn.setRequestProperty("Client-Id", "metric_bds_sender_1");
        conn.setRequestProperty("Content-Type", "application/json;charset=UTF-8");

        conn.getOutputStream().write("test".getBytes());

        System.out.println(conn.getResponseCode());
        System.out.println(IOUtils.readLines((InputStream) conn.getContent()));

        if (conn.getResponseCode() != 200) {
            throw new IOException(conn.getResponseMessage());
        }
    }

}
