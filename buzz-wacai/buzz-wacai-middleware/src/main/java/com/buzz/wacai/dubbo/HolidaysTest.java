package com.buzz.wacai.dubbo;


import java.util.Date;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ReferenceConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import com.wacai.service.holidays.service.HolidaysService;

public class HolidaysTest {

	public static void main(String[] args) {

		// 当前应用配置
		ApplicationConfig application = new ApplicationConfig();
		application.setName("demo-web");

		// 连接注册中心配置
		RegistryConfig registry = new RegistryConfig();
		registry.setAddress("zktestserver1.wacai.info:22181");
		registry.setProtocol("zookeeper");
		registry.setGroup("dubbo_test");

		// 引用远程服务
		ReferenceConfig<HolidaysService> reference = new ReferenceConfig<HolidaysService>(); // 此实例很重，封装了与注册中心的连接以及与提供者的连接，请自行缓存，否则可能造成内存和连接泄漏
		reference.setApplication(application);
		reference.setRegistry(registry); // 多个注册中心可以用setRegistries()
		reference.setInterface(HolidaysService.class);
		//reference.setVersion("1.0.0");
		reference.setTimeout(5000);
		// 和本地bean一样使用xxxService
		HolidaysService holidaysService = reference.get(); // 注意：此代理对象内部封装了所有通讯细节，对象较重，请缓存复用

		Date date = new Date();
		for (int i = 0; i < 30; ++i) {
			int ret = holidaysService.isHoliday(date);
			System.out.println(date + "=" + ret);
			date = new Date(date.getTime() + 86400000);
		}
		
		reference.destroy();
		registry.destroyAll();
	}
}
