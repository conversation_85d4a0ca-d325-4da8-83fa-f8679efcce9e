package com.buzz.wacai.jasmine;

import java.io.File;
import java.io.IOException;

public class FileTest {

	static final int DEFAULT_BUFFER_SIZE = 1024 * 4;

	public static void main(String[] args) throws IOException {

		File file = new File("/work/dist/branch/wacai/apps/self-test/my-boot/nest-test/target/nest-test-0.0.1-SNAPSHOT.jar");
		System.out.println(file.exists());
		
//		InputStream input = new FileInputStream(new File("/work/dist/branch/wacai/apps/self-test/my-boot/nest-test/target/nest-test-0.0.1-SNAPSHOT.jar"));
//		OutputStream output = new FileOutputStream(new File("/tmp/settings2.jar"));
//
//		byte[] buffer = new byte[DEFAULT_BUFFER_SIZE];
//		long maxSize = Long.MAX_VALUE;
//
//		long remaining = maxSize;
//		while (remaining > 0) {
//			// let's safely cast to int because the min value will be lower than the buffer size
//			int len = (int) Math.min(buffer.length, remaining);
//			int n = input.read(buffer, 0, len);
//
//			System.out.println(n);
//			if (n == -1) {
//				break;
//			}
//
//			output.write(buffer, 0, n);
//
//			remaining -= n;
//			
//		}
	
	}
}
