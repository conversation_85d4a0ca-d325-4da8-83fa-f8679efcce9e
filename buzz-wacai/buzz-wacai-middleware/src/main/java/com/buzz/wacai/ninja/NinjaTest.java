package com.buzz.wacai.ninja;

import com.wacai.brave.Tracer;
import com.wacai.brave.propagation.TraceContext;
import com.wacai.common.ninja.client.NinjaClientUtil;
import com.wacai.common.ninja.client.NinjaTracerFacade;
import com.wacai.common.ninja.client.SpanState;
import com.wacai.common.ninja.client.TracerAdaptor;
import com.wacai.github.kristofa.brave.IdConversion;
import org.junit.Test;
import org.slf4j.MDC;

import java.io.IOException;
import java.util.concurrent.ConcurrentMap;

public class NinjaTest {

	private TraceContext getTraceContext(String traceId, String spanId, String parentId) {

		TraceContext traceContext = null;
		if (spanId != null && traceId != null) {
			if (parentId != null) {
				traceContext = TraceContext.newBuilder()
						.traceId(IdConversion.convertToLong(traceId))
						.sampled(false)
						.spanId(IdConversion.convertToLong(spanId))
						.parentId(IdConversion.convertToLong(parentId))
						.build();
			} else {
				traceContext = TraceContext.newBuilder()
						.traceId(IdConversion.convertToLong(traceId))
						.spanId(IdConversion.convertToLong(spanId))
						.sampled(false)
						.build();
			}
		}

		return traceContext;
	}

	static {
		//http://*************:8080/kafka/publish
		//System.setProperty(NinjaClientUtil.KAFKA_URL_KEY, "http://localhost:8080");
	}
	
	@Test
	public void testServerTrace() throws IOException {
		//初始化
		Tracer tracer = TracerAdaptor.getInstance().getTracer("ninja-app");
		
		//开始
		String spanName = "/hello/{id}";
		String url ="/hello/1";
		TraceContext traceContext = getTraceContext(null, null, null);
		ConcurrentMap<String, String> lastBusinessContext = null;
		NinjaTracerFacade.doStartServerTrace(spanName, traceContext, lastBusinessContext);
		NinjaTracerFacade.addTraceTag("http.url", url);
		
		NinjaClientUtil.addBusinessContext(NinjaClientUtil.CELL_ID, "hzifc");
		NinjaClientUtil.addBusinessContext(NinjaClientUtil.HTTP_METHOD, "POST");
		MDC.put(NinjaClientUtil.TID, SpanState.traceIdString());
		
		//after
		NinjaTracerFacade.addTraceTag(NinjaClientUtil.HTTP_STATUS_CODE, "200");
		
		//finish
		NinjaTracerFacade.doFinishServerTrace();
		
		System.in.read();
	}
}
