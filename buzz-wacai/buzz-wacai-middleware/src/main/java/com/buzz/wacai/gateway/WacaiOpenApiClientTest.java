package com.buzz.wacai.gateway;

import com.wacai.open.sdk.WacaiOpenApiClient;
import com.wacai.open.sdk.request.WacaiOpenApiRequest;
import org.junit.Before;
import org.junit.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

//https://open.wacai.com/gw/api_entry
public class WacaiOpenApiClientTest {

	WacaiOpenApiClient client;

	@Before
	public void setup() {
		client = new WacaiOpenApiClient("wbu7r8m8pp53", "ca61f211831141dcba7ae72e3bc77304");

//		client.setGatewayEntryUrl("http:// 你guard-boot.loan.k2.test.wacai.info/gw/api_entry");
		client.setGatewayEntryUrl("http://guard.ngrok.wacaiyun.com/gw/api_entry");
		client.init();

	}

	@Test
	public void testLocalTest() {

		WacaiOpenApiRequest request = new WacaiOpenApiRequest("local.test", "1.0");
		Map<String, Object> bizParam = new HashMap<String, Object>();
		bizParam.put("id", "1001");
		request.setBizParam(bizParam);
		String ret = client.invoke(request, String.class);
		System.out.println("ret=" + ret);
	}

	@Test
	public void testlossPush() {

		WacaiOpenApiRequest request = new WacaiOpenApiRequest("lossPush", "1.0");
		Map<String, Object> item = new HashMap<>();
		item.put("bizLine", "AMC-test");
		item.put("campCode", "d6846522086a4ec89e336a31062da79a");
		item.put("idno", "123456");
		List<Map> items = new ArrayList();
		items.add(item);
		
		Map<String, Object> map = new HashMap<>();
		map.put("data", items);
		request.setBizParam(map);
		
		
		String ret = client.invoke(request, String.class);
		System.out.println("ret=" + ret);
	}

	public static void main(String[] args) {
		// WacaiOpenApiClient 是一个单例,不要在每次调用时候创建!!!
				WacaiOpenApiClient wacaiOpenApiClient = new WacaiOpenApiClient("7h394cma45pn", "156f105fd7fa4b889cc8916e30250546");

		// 如果是测试联调环境,需要添加如下一行代码,线上环境则不需要
		        wacaiOpenApiClient.setGatewayEntryUrl("http://guard.ngrok.wacaiyun.com/gw/api_entry");

				wacaiOpenApiClient.init();

				WacaiOpenApiRequest wacaiOpenApiRequest = new WacaiOpenApiRequest("海川汇富入口", "1.0");
				wacaiOpenApiRequest.putBizParam("card_id", "34121141242144");
				wacaiOpenApiRequest.putBizParam("apply_money", 10);

	}
}
