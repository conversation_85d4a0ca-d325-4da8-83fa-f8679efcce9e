package com.wacai.hermes.agent.consumer.store;


import com.wacai.hermes.agent.manage.HttpEndpointController;
import com.wacai.hermes.config.GlobalHermesProps;
import org.slf4j.Logger;

import java.io.Closeable;
import java.io.IOException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 消息消费重试队列
 *
 * <AUTHOR>
 * @version 2.1.0
 * @date 2018/9/20 下午3:42
 */
public class MessageRetryQueue implements Closeable {

    private final Logger HLOG = HttpEndpointController.HLOG;
    private AtomicLong size;
    private long capacity = GlobalHermesProps.CONSUMER_ROCKSDB_CAPACITY;

    public MessageRetryQueue() {

    }

    public synchronized void addOrModify(MessageEntry messageEntry) {

    }

    public synchronized void deleteKey(String key) {

    }

    public long getSize() {
        return size.get();
    }

    @Override
    public void close() throws IOException {

    }

    public String snapshotKeys() {
        return "";
    }

    public long getCapacity() {
        return capacity;
    }



}