//package com.wacai.hermes.agent.consumer.template;
//
//
//import com.wacai.hermes.agent.config.HermesConsumerConfig;
//import com.wacai.hermes.agent.consumer.HermesConsumer;
//import com.wacai.hermes.agent.core.EndPointContext;
//import com.wacai.hermes.agent.exception.PauseException;
//import com.wacai.hermes.agent.util.EndPointUtil;
//import com.wacai.hermes.api.ConsumerAPI;
//import com.wacai.hermes.bridge.schema.resp.AckResult;
//import com.wacai.hermes.bridge.schema.resp.BatchMessage;
//import com.wacai.hermes.center.schema.resp.EndPoint;
//import com.wacai.hermes.common.constants.ClientType;
//import lombok.extern.slf4j.Slf4j;
//
//
//@Slf4j
//public class ConsumerApiTemplate {
//
//    private HermesConsumerConfig config;
//
//    public ConsumerApiTemplate(HermesConsumer consumer) {
//        this.config = consumer.getConsumerConfig();
//    }
//
//    public BatchMessage fetch() {
//        config.getHermesListeners().stream().forEach(l -> l.preFetch());
//        EndPoint endPoint = EndPointContext.get().selectEndPoint(config.getClusterId());
//
//        String bridgeUrl = EndPointUtil.convert2Host(endPoint) + "/hermes-bridge/fetch";
//        //String bridgeUrl = "http://localhost:8080/hermes-bridge/fetch";
//        BatchMessage batchMessage = ConsumerAPI.fetch(config.getTopic(), config, bridgeUrl);
//        config.getHermesListeners().stream().forEach(l -> l.postFetch());
//        if (batchMessage != null && batchMessage.getTotal() > 0) {
////            log.info("[fetch]fetched messages,total:" + batchMessage.getTotal() + ",startOffset:" + batchMessage
////                    .getStartOffset() + ",endOffset:" + batchMessage.getEndOffset());
//        }
//        return batchMessage;
//    }
//
//    @Deprecated
//    public AckResult ack(int partition, long offset) {
//        return ack(config.getTopic(), partition, offset);
//    }
//
//    /**
//     * 获取config的当前EndPoint为空，因为这里的config是HermesConsumer全局的，而不是拆分后的topic
//     *
//     * @param topic     消息主题
//     * @param partition 分区
//     * @param offset    消息位点
//     * @return ack结果
//     */
//    public AckResult ack(String topic, int partition, long offset) {
//        try {
//            config.getHermesListeners().stream().forEach(e -> e.preAck());
//        } catch (Throwable t1) {
//            log.warn(t1.getMessage(), t1);
//        }
//        EndPoint endPoint = EndPointContext.get().selectEndPoint(config.getClusterId());
//        String bridgeUrl = EndPointUtil.convert2Host(endPoint) + "/hermes-bridge/ack";
//        AckResult ackResult = ConsumerAPI.ack(topic, config, bridgeUrl, partition, offset);
//        log.info("[commit]groupId:" + config.getGroupId() + ",topic:" + topic + ",partition:" + partition
//                + ",submit offset:" + (offset + 1));
//        try {
//            config.getHermesListeners().stream().forEach(e -> e.postAck());
//        } catch (Throwable t1) {
//            log.warn(t1.getMessage(), t1);
//        }
//        return ackResult;
//    }
//
//    public AckResult ack(String topic, HermesConsumerConfig config, int partition, long offset) {
//        if (config == null) {
//            config = this.config;
//        }
//        config.getHermesListeners().stream().forEach(e -> e.preAck());
//        EndPoint endPoint = EndPointContext.get().selectEndPoint(config.getClusterId());
//        String bridgeUrl = EndPointUtil.convert2Host(endPoint) + "/hermes-bridge/ack";
//        AckResult ackResult = ConsumerAPI.ack(topic, config, bridgeUrl, partition, offset);
//        config.getHermesListeners().stream().forEach(e -> e.postAck());
//        return ackResult;
//    }
//
//    @Deprecated
//    public boolean pause(int partition, int delay) {
//        return pause(config.getTopic(), partition, delay);
//    }
//
//    public boolean pause(String topic, int partition, long delay) {
//        try {
//            config.getHermesListeners().stream().forEach(e -> e.preLock());
//            EndPoint endPoint = EndPointContext.get().selectEndPoint(config.getClusterId());
//            String bridgeUrl = EndPointUtil.convert2Host(endPoint) + "/hermes-bridge/lock";
//            boolean lock = ConsumerAPI.pause(topic, config, bridgeUrl, partition, delay);
//            config.getHermesListeners().stream().forEach(e -> e.postLock());
//            return lock;
//        } catch (Throwable e) {
//            throw new PauseException("http pause error:" + e.getMessage());
//        }
//    }
//}
