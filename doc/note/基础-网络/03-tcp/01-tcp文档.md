

## 概述

从一个有点意思的问题说出。 有一天有个同事让我看一个问题，我们有一个文件下载的http代理服务，本质是从ceph读取文件然后下载。

同事发现下载文件代码调用这行代码需要耗时几十毫秒 

```
S3Object object = amazonS3.getObject();
IOUtils.toByteArray(object.getObjectContent());
```
这不是纯内存操作吗？为什么会耗时这么多？

我看了下，微微一笑，这是因为 object.getObjectContent() 返回的是Inputstream。Inputstream读取一段发现没有数据了就会从socket中读取。

1. 为什么获取http code很快，后面很慢？
2. 数据传输是通过调用socket.read()触发的吗？如果不调用该方法server方法还会发送数据吗？系统内核还会接受数据吗？
3. 如果A向B传输1g的文件，B的应用并没有调用socket.read()，那么会把B系统的缓冲区占满吗？占满之后会怎么样？ 这个缓冲区是多大？

一时半会还把我问懵了。重新梳理了一些TCP相关知识

## 问题分析

这个问题的本质是调用 socket.read() 时网络数据是如何传输的？

带着这个问题，我写了一个 http 测试代码

```java
String url = "http://*************:8888/hermes-proxy-4.0-SNAPSHOT.jar";
int respCode;
HttpURLConnection conn = (HttpURLConnection) new URL(url).openConnection();
conn.setRequestMethod("GET");
conn.setConnectTimeout(3000);
conn.setReadTimeout(3000);
conn.setRequestProperty("Connection", "Keep-Alive");
conn.addRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=UTF-8");
conn.connect();
log.info("connected! ConnectTimeout={}", conn.getConnectTimeout());
long begin = System.currentTimeMillis();
respCode = conn.getResponseCode();
log.info("respCode={} cost={} ms", respCode, System.currentTimeMillis() - begin);

begin = System.currentTimeMillis();
log.info("read from InputStream now");
String resp = toString(conn.getInputStream(), "utf-8");
log.info("cost={} ms, \t {} \t {}", System.currentTimeMillis() - begin, conn.getHeaderFields(), resp);

log.info("InputStream ={}", conn.getInputStream().getClass());
conn.getInputStream().close();
```

输出:

```
2024-07-16 15:28:03,585 INFO  [main] c.b.http.HttpURLConnectionTest - respCode=200 cost=23 ms
2024-07-16 15:28:03,585 INFO  [main] c.b.http.HttpURLConnectionTest - read from InputStream now
2024-07-16 15:28:05,825 INFO  [main] c.b.http.HttpURLConnectionTest - cost=2240 ms
```

这是一段最简单的http代码， conn.connect() 方法会建立tcp连接，当调用 conn.getResponseCode(); 是会发送http请求，并等待http响应，这个方法和后续conn.getInputStream().read()其实没有本质区别，都是从输入流中读数据，具体查看SocketInputStream.read()方法。
首先要理解**TCP是面向连接的、可靠的、基于字节流的传输层通信协议**：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240716155604090.png" alt="image-20240716155604090" style="zoom:50%;" />

client端发送完http请求之后，server端就会往tcp写入数据，数据会打包为一个个segment发送，每个segment包含的字节数通过TCP MSS（**Maximum Segment Size**）配置，它决定了每个TCP段可以携带的最大数据量，不包括TCP头部。默认情况下，MSS 为1460个字节。

同时TCP还会在**一个窗口内批量发送多个Segment**，这个窗口叫滑动窗口，TCP的**初始滑动窗口大小**（Initial Congestion Window，IW）设置为1个最大报文段（MSS）的大小，这是为了在TCP连接建立后立即开始传输数据。随着TCP连接的进行，滑动窗口大小会根据网络的拥塞情况动态调整.

当client调用 conn.getResponseCode() 方法时，会**从缓存区中读取第一个Segment中的数据，这步操作比较快**，而后续的文件内容解析需要读取完整个网络中的所有数据当然要耗费更多的时间，当调用socket.read()从缓冲区中读取数据，如果缓冲区中没有可读的数据，那么就会等待，耗时慢就是在等待网络传输。

**如果Clinet没有及时调用 socket.read() 会发生什么**？ 如果TCP不做处理，Server继续发送数据可能导致网络拥堵，所有TCP有一套机制来避免这个问题的发生：

操作系统为每个TCP连接维护一个接收缓冲区，用于临时存储从网络接口接收到的数据。如果应用程序不及时读取这些数据，缓冲区会逐渐填满。上述例子中，当Client的接收缓冲区快要满时，Server会通过调整TCP窗口大小（窗口缩减）来减少或暂停发送数据。如果缓冲区完全满了，窗口大小会变成零，Server将停止发送数据，直到Client的应用程序读取了一些数据并释放了缓冲区空间。

可以通过`/proc/sys/net/ipv4/tcp_rmem`文件查看和设置接收缓冲区大小范围。这个文件包含三个值：最小值、默认值和最大值:

```
cat /proc/sys/net/ipv4/tcp_rmem
4096 87380 6291456
```

其中，`4096`表示最小缓冲区大小（4KB），`87380`是默认缓冲区大小（约85KB），`6291456`是最大缓冲区大小（约6MB）。

java 可以查看默认缓冲区大小：

```java
Socket socket = new Socket();
int defaultReceiveBufferSize = socket.getReceiveBufferSize();
System.out.println("Default receive buffer size: " + defaultReceiveBufferSize);//mac本地测试是398kb
```

netty设置接收缓冲区的大小：

```
config.setOption(ChannelOption.SO_RCVBUF, 1024 * 1024); // 设置为1MB
```

## 抓包分析

下面我使用socket发送http下载文件，然后抓包，代码在HttpURLConnectionTest。

**1、建立连接，三次握手**

```
No   Time       Source          Destionation.   Pro Length  Info
4   8.987076    **************  *************   TCP 78  63827 → 8888 [SYN] Seq=0 Win=65535 Len=0 MSS=1460 WS=64 TSval=1033881228 TSecr=0 SACK_PERM
5   8.999410    *************   **************  TCP 66  8888 → 63827 [SYN, ACK] Seq=0 Ack=1 Win=14600 Len=0 MSS=1360 SACK_PERM WS=256
6   8.999481    **************  *************   TCP 54  63827 → 8888 [ACK] Seq=1 Ack=1 Win=262144 Len=0
```

 解释：

- 包4： ************** 发送SYN
- 包5：************* 返回SYN+ACK
- 包6： ************** ACK



**2、第一次发起请求并响应**

```
No   Time       Source          Destionation.   Pro Length  Info
7   9.001118    **************  *************   TCP 107 63827 → 8888 [PSH, ACK] Seq=1 Ack=1 Win=3145728 Len=53
8   9.005682    *************   **************  TCP 60  8888 → 63827 [ACK] Seq=1 Ack=54 Win=14848 Len=0
9   9.007957    *************   **************  TCP 71  8888 → 63827 [PSH, ACK] Seq=1 Ack=54 Win=14848 Len=17 [TCP segment of a reassembled PDU]
```
解释：

- 包7： client这里发送的Seq=1，len=53，
- 包8： server端如果全部收到消息了，会直接ack 1+53=54 ，同时会设置Win大小=14848
- 包9： server端会继续发送响应，ack同样是=54

**3、继续发送消息**

因为server还有数据需要推送，所以会继续发送：

```
No   Time       Source          Destionation.   Pro Length  Info
10  9.007957    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=18 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
11  9.007958    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=1378 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
12  9.007959    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=2738 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
13  9.007959    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=4098 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
14  9.007960    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=5458 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
15  9.007961    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=6818 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
16  9.007962    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=8178 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
17  9.007962    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=9538 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
18  9.007963    *************   **************  TCP 1414    8888 → 63827 [ACK] Seq=10898 Ack=54 Win=14848 Len=1360 [TCP segment of a reassembled PDU]
20  9.008025    **************  *************   TCP 54      63827 → 8888 [ACK] Seq=54 Ack=12258 Win=3133440 Len=0
```

从这些数据可以看出，序列号是依次递增的，每个数据段的序列号是前一个数据段的序列号加上其数据长度：

```
包10: Seq=18, Len=1360
包11: Seq=1378, Len=1360 (18 + 1360 = 1378)
包12: Seq=2738, Len=1360 (1378 + 1360 = 2738)
包20: 批量ACK
```

另外可以看到多个数据包连续发送，而接收方（**************）在接收了多个数据包之后，只返回了一个 ACK，延迟确认是一种优化机制，TCP 接收端在接收到数据段后不会立即发送 ACK，而是等待一段时间（通常是200毫秒）以期望在这段时间内能接收到更多的数据段，然后一次性确认这些数据段。这有助于减少网络上的 ACK 数据包数量，提升整体网络性能。

4、缓冲区满

```
2564    10.385026   **************  *************   TCP 54  63827 → 8888 [ACK] Seq=54 Ack=3188477 Win=8960 Len=0
2565    10.385099   **************  *************   TCP 54  63827 → 8888 [ACK] Seq=54 Ack=3196637 Win=768 Len=0
2566    10.608053   *************   **************  TCP 822 [TCP Window Full] 8888 → 63827 [PSH, ACK] Seq=3196637 Ack=54 Win=14848 Len=768 [TCP segment of a reassembled PDU]
2567    10.608184   **************  *************   TCP 54  [TCP ZeroWindow] 63827 → 8888 [ACK] Seq=54 Ack=3197405 Win=0 Len=0
2568    10.823859   *************   **************  TCP 60  [TCP Keep-Alive] 8888 → 63827 [ACK] Seq=3197404 Ack=54 Win=14848 Len=0
2569    10.824021   **************  *************   TCP 54  [TCP ZeroWindow] 63827 → 8888 [ACK] Seq=54 Ack=3197405 Win=0 Len=0
2570    11.248962   *************   **************  TCP 60  [TCP Keep-Alive] 8888 → 63827 [ACK] Seq=3197404 Ack=54 Win=14848 Len=0
2571    11.249086   **************  *************   TCP 54  [TCP ZeroWindow] 63827 → 8888 [ACK] Seq=54 Ack=3197405 Win=0 Len=0
2572    12.203027   *************   **************  TCP 60  [TCP Keep-Alive] 8888 → 63827 [ACK] Seq=3197404 Ack=54 Win=14848 Len=0
2573    12.203394   **************  *************   TCP 54  [TCP ZeroWindow] 63827 → 8888 [ACK] Seq=54 Ack=3197405 Win=0 Len=0
2574    13.888727   *************   **************  TCP 60  [TCP Keep-Alive] 8888 → 63827 [ACK] Seq=3197404 Ack=54 Win=14848 Len=0
2575    13.888835   **************  *************   TCP 54  [TCP ZeroWindow] 63827 → 8888 [ACK] Seq=54 Ack=3197405 Win=0 Len=0
```
我代码中设置的缓冲区是3mb，我故意第一次读取数据之后不再读取数据，可以看到系统会继续传输数据，直到缓冲区被填满，当满之后，系统会减少窗口大小，************* 此时会发送TCP Window Full，验证了我们前面的想法。

## 核心原理

[图解你管这破玩意儿叫TCP](https://mp.weixin.qq.com/s/Uf42QEL6WUSHOwJ403FwOA)这篇文章是我看过把TCP协议解释的最清楚的文章， 再次理解TCP是 **面向连接、可靠、基于字节流** 的传输层通信协议这句话

### 数据传输

经过《如果让你来设计网络》这篇文章中的一番折腾，只要你知道另一位伙伴 B 的 IP 地址，且你们之间的网络是通的，无论多远，你都可以将一个数据包发送给你的伙伴 B。

这就是物理层、数据链路层、网络层这三层所做的事情。

前三层协议只能把数据包从一个主机搬到另外一台主机，但是，到了目的地以后，数据包具体交给哪个程序（进程）呢？

所以，你需要把通信的进程区分开来，于是就给每个进程分配一个数字编号，你给它起了一个响亮的名字：端口号。

然后你在要发送的数据包上，增加了传输层的头部，源端口号与目标端口号。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717143908413.png" alt="image-20240717143908413" style="zoom:50%;" />



OK，这样你将原本主机到主机的通信，升级为了**进程和进程之间的通信**。

你没有意识到，你不知不觉实现了 **UDP 协议**！

（当然 UDP 协议中不光有源端口和目标端口，还有数据包长度和校验值，我们暂且略过）

就这样，你用 UDP 协议无忧无虑地同 B 进行着通信，一直没发生什么问题。但很快，你发现事情变得非常复杂......



### 丢包问题

由于网络的不可靠，数据包可能在半路丢失，而 A 和 B 却无法察觉。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717144118367.png" alt="image-20240717144118367" style="zoom:50%;" />

于是你设计了如下方案，A 每发一个包，都必须收到来自 B 的**确认**（ACK），再发下一个，否则在一定时间内没有收到确认，就**重传**这个包。

你管它叫**停止等待协议**。只要按照这个协议来，虽然 A 无法保证 B 一定能收到包，但 A 能够确认 B 是否收到了包，收不到就重试，尽最大努力让这个通信过程变得可靠，于是你们现在的通信过程又有了一个新的特征，**可靠交付**。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717144209530.png" alt="image-20240717144209530" style="zoom:50%;" />

停止等待虽然能解决问题，但是效率太低了，A 原本可以在发完第一个数据包之后立刻开始发第二个数据包，但由于停止等待协议，A 必须等数据包到达了 B ，且 B 的 ACK 包又回到了 A，才可以继续发第二个数据包，这效率慢得可不是一点两点。

于是你对这个过程进行了改进，采用**流水线**的方式，不再傻傻地等。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717144227158.png" alt="image-20240717144227158" style="zoom:50%;" />

但是网路是复杂的、不可靠的。

有的时候 A 发出去的数据包，分别走了不同的路由到达 B，可能无法保证和发送数据包时一样的顺序。

在流水线中有多个数据包和ACK包在**乱序流动**，他们之间对应关系就乱掉了。

难道还回到停止等待协议？A 每收到一个包的确认（ACK）再发下一个包，那就根本不存在顺序问题。应该有更好的办法！

A 在发送的数据包中增加一个**序号**（seq），同时 B 要在 ACK 包上增加一个**确认号**（ack），这样不但解决了停止等待协议的效率问题，也通过这样标序号的方式解决了顺序问题。

而 B 这个确认号意味深长：比如 B 发了一个确认号为 ack = 3，它不仅仅表示 A 发送的序号为 2 的包收到了，还表示 2 之前的数据包都收到了。这种方式叫**累计确认**或**累计应答**。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717144331919.png" alt="image-20240717144331919" style="zoom:50%;" />

>  注意，实际上 ack 的号是收到的最后一个数据包的序号 seq + 1，也就是告诉对方下一个应该发的序号是多少。但图中为了便于理解，ack 就表示收到的那个序号，不必纠结。

### 流量问题

有的时候，A 发送数据包的速度太快，而 B 的接收能力不够，但 B 却没有告知 A 这个情况。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717144430313.png" alt="image-20240717144430313" style="zoom:50%;" />

怎么解决呢？

很简单，B 告诉 A 自己的接收能力，A 根据 B 的接收能力，相应控制自己的**发送速率**，就好了。

B 怎么告诉 A 呢？B 跟 A 说"我很强"这三个字么？那肯定不行，得有一个严谨的规范。

于是 B 决定，每次发送数据包给 A 时，顺带传过来一个值，叫**窗口大小**（win)，这个值就表示 B 的**接收能力**。同理，每次 A 给 B 发包时也带上自己的窗口大小，表示 A 的接收能力(**即窗口大小都是对方指定的**，因为接收能力觉得了发送速率)

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717144509121.png" alt="image-20240717144509121" style="zoom:50%;" />

下面是A的发送窗口示意图，窗口大小为5

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717150855341.png" alt="image-20240717150855341" style="zoom:50%;" />

 工作过程：

- t1时刻发送了5个数据包，等待对方发送ack。
- t2时刻收到3个数据包的ack，窗口会向前滑动3位，发送6,7,8
- t3时刻收到2个数据包的ack，窗口会向前滑动3位，发送9,10

注意实际工作中，窗口大小可能会动态调整(对方ACK时携带)。

OK，终于将流量控制问题解决得差不多了，给这个窗口起了一个更生动的名字，**滑动窗口**。



### 拥塞问题

但有的时候，不是 B 的接受能力不够，而是网络不太好，造成了**网络拥塞**。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717151437471.png" alt="image-20240717151437471" style="zoom:50%;" />

拥塞控制与流量控制有些像，但流量控制是受 B 的接收能力影响，而拥塞控制是受**网络环境**的影响。

拥塞控制的解决办法依然是通过设置一定的窗口大小，只不过，流量控制的窗口大小是 B 直接告诉 A 的，而拥塞控制的窗口大小按理说就应该是网络环境主动告诉 A。

但网络环境怎么可能主动告诉 A 呢？只能 A 单方面通过**试探**，不断感知网络环境的好坏，进而确定自己的拥塞窗口的大小。

拥塞窗口大小的计算有很多复杂的算法，就不在本文中展开了，假如**拥塞窗口的大小为  cwnd**，上一部分流量控制的**滑动窗口的大小为 rwnd**，那么窗口的右边界受这两个值共同的影响，需要取它俩的最小值。

**窗口大小 = min(cwnd, rwnd)**

含义很容易理解，当 B 的接受能力比较差时，即使网络非常通畅，A 也需要根据 B 的接收能力限制自己的发送窗口。当网络环境比较差时，即使 B 有很强的接收能力，A 也要根据网络的拥塞情况来限制自己的发送窗口。正所谓受其**短板**的影响嘛~

### 连接问题

有的时候，B 主机的相应进程还没有准备好或是挂掉了，A 就开始发送数据包，导致了浪费。

这个问题在于，A 在跟 B 通信之前，没有事先确认 B 是否已经准备好，就开始发了一连串的信息。就好比你和另一个人打电话，你还没有"喂"一下确认对方有没有在听，你就巴拉巴拉说了一堆。

这个问题该怎么解决呢？

地球人都知道，**三次握手**嘛！

A：我准备好了(SYN)

B：我知道了(ACK)，我也准备好了(SYN)

A：我知道了(ACK)

A 与 B 各自在内存中维护着自己的状态变量，三次握手之后，双方的状态都变成了**连接已建立**（ESTABLISHED）。

虽然就只是发了三次数据包，并且在各自的内存中维护了状态变量，但这么说总觉得太 low，你看这个过程相当于双方建立连接的过程，于是你灵机一动，就叫它**面向连接**吧。

>  注意：这个连接是虚拟的，是由 A 和 B 这两个终端共同维护的，在网络中的设备根本就不知道连接这回事儿！

但凡事有始就有终，有了建立连接的过程，就要考虑释放连接的过程，又是地球人都知道，**四次挥手**嘛！

A：再见，我要关闭了(FIN)

B：我知道了(ACK)

   给 B 一段时间把自己的事情处理完...

B：再见，我要关闭了(FIN)

A：我知道了(ACK)

### 总结

以上讲述的，就是 TCP 协议的核心思想，上面过程中需要传输的信息，就体现在 TCP 协议的头部，这里放上最常见的 TCP 协议头解读的图。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717154500216.png" alt="image-20240717154500216" style="zoom:50%;" />

>  图中可以看到 TCP头和TCP数据被封装在IP层的数据部分，然后发送出去

不知道你现在再看下面这句话，是否能理解：

**TCP 是 面向连接的、可靠的、基于字节流的传输层通信协议**

面向连接、可靠，这两个词通过上面的讲述很容易理解，那什么叫做基于字节流呢？

很简单，TCP 在建立连接时，需要告诉对方 MSS（最大报文段大小）。

也就是说，如果要发送的数据很大，在 TCP 层是需要按照 MSS 来切割成一个个的 **TCP 报文段** 的。

切割的时候我才不管你原来的数据表示什么意思，需要在哪里断句啥的，我就把它当成一串毫无意义的字节，在我想要切割的地方咔嚓就来一刀，标上序号，只要接收方再根据这个序号拼成最终想要的完整数据就行了。

在我 TCP 传输这里，我就把它当做一个个的**字节**，也就是基于字节流的含义了。

## 日常总结

### TIME_WAIT是如何产生的

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240717172402497.png" alt="image-20240717172402497" style="zoom:50%;" />

可以把关闭连接想象为男女朋友分手，假设A为主动发起分手方：

```properties
A: 我想分手，发送FIN，状态 ESTABLISHED 变为 FIN-WAIT-1
B: 好的，发送ACK，但是我们现在还没正式分手，你要等我收拾下东西，状态 STABLISHED 变为 CLOSE-WAIT
A: 收到ACK，好的，等你，FIN-WAIT-1 变为 FIN-WAIT-2

// a few later

B: 收拾完毕，我们正式分手吧，发送FIN， 状态CLOSE-WAIT 变为LAST-ACK 
A: 收到fin，那我们分手吧，发送ack，FIN-WAIT-2 变为 TIME-WAIT
B: 收到ack, 那我们分手咯，LAST-ACK 变为 CLOSED

// a few later
A: 等待2 * MSL之后，终于分手成功了，状态 TIME-WAIT 变为 CLOSED 
```

如果不考虑状态变化，只考虑动作：

```properties
A：FIN
B: ACK

B: FIN
A: ACK
```

可以发现后面两步就是前两步反过来。

所以如果出现大量 CLOSE-WAIT，应该是程序bug，忘记调用close()方法，而出现大量TIME_WAIT一般是客户端大量发起短连接，大量TIME_WAIT可能会导致端口被占用问题，如何避免呢？最佳实践是通过设置 SO_LINGER(0) 快速关闭，参考[左耳朵-从一次经历谈 TIME_WAIT 的那些事](https://coolshell.cn/articles/22263.html)

## 参考

推荐：

- [图解 | 你管这破玩意儿叫TCP？](https://mp.weixin.qq.com/s/Uf42QEL6WUSHOwJ403FwOA)
- [TCP 拥塞控制对数据延迟的影响](https://www.kawabangga.com/posts/5181)
- [TCP 长连接 CWND reset 的问题分析](https://www.kawabangga.com/posts/5217)

- [强烈推荐->有关 MTU 和 MSS 的一切](https://www.kawabangga.com/posts/4983)

历史：

- [What is TCP/IP And How Does it Work](https://www.techtarget.com/searchnetworking/definition/TCP-IP)
- [iteye-再谈应用环境下的TIME_WAIT和CLOSE_WAIT ](https://san-yun.iteye.com/admin/blogs/1696494)
- [iteye- TCP 状态转换](https://san-yun.iteye.com/admin/blogs/1696494)
- [阿里P10李运华-TIME_WAIT问题解决方法大全](https://blog.csdn.net/yunhua_lee/article/details/8146837)
- [浅谈TCP优化](https://kb.cnblogs.com/page/197406/)
- [TCP连接的建立和关闭详解](https://web.archive.org/web/20180331204558/http://anonymalias.github.io/2017/04/07/tcp-create-close-note/)

-   [腾讯技术工程万字详文：TCP 拥塞控制详解](https://zhuanlan.zhihu.com/p/144273871)