## [Flags](https://amits-notes.readthedocs.io/en/latest/networking/tcpdump.html#id2)

*tcpdump* Flags:

> | TCP Flag | tcpdump Flag | Meaning                                       |
> | -------- | ------------ | --------------------------------------------- |
> | SYN      | S            | Syn packet, a session establishment request.  |
> | ACK      | A            | Ack packet, acknowledge sender’s data.        |
> | FIN      | F            | Finish flag, indication of termination.       |
> | RESET    | R            | Reset, indication of immediate abort of conn. |
> | PUSH     | P            | Push, immediate push of data from sender.     |
> | URGENT   | U            | Urgent, takes precedence over other data.     |
> | NONE     | A dot *.*    | Placeholder, usually used for ACK.            |

## 1、概述

在本文中，我们将研究 Linux 中的工具 tcpdump，学习如何在 Linux 环境中使用 tcpdump 捕获网络数据包。

PS，英文文档中出现的词汇解释:

- interface。在网络中，Interface表示网卡的意思，参考 [Network_interface](https://en.wikipedia.org/wiki/Network_interface)
- epoch time。epoch time指的是UNIX时间，从UTC1970年1月1日0时0分0秒起至现在的总秒数，参考[Unix time](https://en.wikipedia.org/wiki/Unix_time)
- Log rotation。日志轮换是系统管理中使用的自动化过程，其中日志文件在过旧或过大时被压缩、移动、重命名或删除，参考

## 2、基本语法

通常，tcpdump 的语法可以表示为命令后跟 options 列表和表达式

> tcpdump [OPTIONS] [expression]

让我们使用默认配置运行 tcpdump，不带任何标志或表达式：

```
$ tcpdump
08:41:13.729687 IP *************.22 > ************.41916: Flags [P.], seq 196:568, ack 1, win 309, options [nop,nop,TS val 117964079 ecr 816509256], length 372
```

当我们不指定网卡时，tcpdump 将侦听系统接口列表中编号最低的网卡。在这种情况下，它选择了 eth0 作为监听的接口，然后，它捕获通过网卡的任何数据包并打印到控台。

从输出中，我们可以看到每一行网络数据包的详细信息。让我们更深入地研究一个示例数据包转储：

```
10:33:18.583077 IP 927e94ccde39.54226 > ************.53: 39122+ AAAA? www.baeldung.com. (34)
08:41:13.729687 IP *************.22 > ************.41916: Flags [P.], seq 196:568, ack 1, win 309, options [nop,nop,TS val 117964079 ecr 816509256], length 372
```

从输出中，我们可以从左到右依次看到的分别是:

- 时间;

- IP 代表网络层协议——在本例中为 IPv4。对于 IPv6 数据包，该值为 IP6;




下面列举一些常用选项：

- -A 只使用 ASCII 打印报文的全部数据，不要和 `-X` 一起使用
- -b 在数据链路层上选择协议，包括 ip, arp, rarp, ipx 等
- -c 指定要抓取包的数量
- -D 列出操作系统所有可以用于抓包的接口
- -e 输出链路层报头
- -i 指定监听的网卡，`-i any` 显示所有网卡
- -n 表示不解析主机名，直接用 IP 显示，默认是用 hostname 显示
- -nn 表示不解析主机名和端口，直接用端口号显示，默认显示是端口号对应的服务名
- -p 关闭接口的混杂模式
- -P 指定抓取的包是流入的包还是流出的，可以指定参数 in, out, inout 等，默认是 inout
- -q 快速打印输出，即只输出少量的协议相关信息
- -s len 设置要抓取数据包长度为 len，默认只会截取前 96bytes 的内容，`-s 0` 的话，会截取全部内容。
- -S 将 TCP 的序列号以绝对值形式输出，而不是相对值
- -t 不要打印时间戳
- -vv 输出详细信息（比如 tos、ttl、checksum等）
- -X 同时用 hex 和 ascii 显示报文内容
- -XX 同 -X，但同时显示以太网头部

Redis 抓包:

```
tcpdump -n -s 0 tcp port 6385 -w redis.pcap -i bond0 
```

ES抓包：

```
tcpdump -n -s 0 tcp port 9301 -w es.pcap -i any
```



## 3、网卡选项

### 查询网卡

使用 -D 标志来查询可用的网卡

```
tcpdump -D
1.en0 [Up, Running]
2.p2p0 [Up, Running]
3.awdl0 [Up, Running]
4.bridge0 [Up, Running]
5.utun0 [Up, Running]
6.en1 [Up, Running]
7.utun1 [Up, Running]
8.en2 [Up, Running]
9.lo0 [Up, Running, Loopback]
10.gif0
11.stf0
12.XHC0
13.XHC20
```

我们可以看到，tcpdump 显示了每个接口的状态，除了实际的网络接口之外，tcpdump 还创建了一个名为“any”的伪设备，通过侦听这个伪设备 any，tcpdump 将捕获通过所有接口的数据包。

### 指定网卡

我们可以使用 -i 标志选择要侦听的接口：

```
tcpdump -i wlan0
```

上面的命令在网卡 wlan0 上启动 tcpdump，此外，我们可以使用伪设备 any 捕获所有接口的数据包：

```
tcpdump -i any
```

## 4、格式化时间

在每个数据包转储输出上，tcpdump 在第一列附加时间戳以指示捕获数据包的时间。 tcpdump 支持使用 -t 标志格式化此时间戳输出。

### 禁用时间戳信息

可以指定 -t 标志来禁用打印时间戳

```
tcpdump -i lo0 -t
tcpdump: verbose output suppressed, use -v or -vv for full protocol decode
listening on lo0, link-type NULL (BSD loopback), capture size 262144 bytes
IP localhost.64416 > localhost.54530: Flags [S], seq 496069784, win 65535, options [mss 16344,nop,wscale 5,nop,nop,TS val 461027395 ecr 0,sackOK,eol], length 0
IP localhost.54530 > localhost.64416: Flags [S.], seq 3358093257, ack 496069785, win 65535, options [mss 16344,nop,wscale 5,nop,nop,TS val 461027395 ecr 461027395,sackOK,eol], length 0
```

### 打印UTC时间戳

还可以使用 -tt 标志以秒为单位打印时间戳

```
tcpdump -i lo0 -tt
tcpdump: verbose output suppressed, use -v or -vv for full protocol decode
listening on lo0, link-type NULL (BSD loopback), capture size 262144 bytes
1629019287.555867 IP localhost.7890 > localhost.64707: Flags [.], ack 1326835569, win 12496, length 0
1629019287.555883 IP localhost.7890 > localhost.64701: Flags [.], ack 2174047828, win 12694, length 0
```

### 在时间增量中打印时间戳

可以使用 -ttt 标志根据增量输出时间戳。

具体来说，tcpdump 将在每一行打印当前转储和前一个转储之间的时间差：

```
tcpdump -i lo0 -ttt
tcpdump: verbose output suppressed, use -v or -vv for full protocol decode
00:00:00.000000 IP localhost.websm > localhost.61320: Flags [P.], seq 873073832:873073852, ack 2292989049, win 12750, options [nop,nop,TS val 461415676 ecr 461414686], length 20
00:00:00.000014 IP localhost.65472 > localhost.54530: Flags [.], ack 1, win 12759, options [nop,nop,TS val 461415880 ecr 461415880], length 0
00:00:00.000019 IP localhost.54530 > localhost.65472: Flags [.], ack 1, win 12759, options [nop,nop,TS val 461415880 ecr 461415880], length 0
```

### 打印带日期的时间戳

最后，我们可以使用 -tttt 标志打印时间戳和日期：

```
tcpdump -i lo0 -tttt
tcpdump: verbose output suppressed, use -v or -vv for full protocol decode
listening on lo0, link-type NULL (BSD loopback), capture size 262144 bytes
2021-08-15 17:24:54.812131 IP localhost.7890 > localhost.65086: Flags [P.], seq 2989204259:2989204332, ack 3682704335, win 12710, options [nop,nop,TS val 461506935 ecr 461493742], length 73
```

## 5、将数据包转储保存到文件

tcpdump 还提供了几个标志，允许我们将数据包转储写入文件。

### 写入文件

要将数据包转储写入文件，我们可以使用标志 -w 后跟文件名：

```
tcpdump -w packet-captured.pcap
```

当我们将转储写入文件时，tcpdump 将不再将转储打印到标准输出。

### 限制每个文件的大小

我们可以使用 -C 标志限制每个文件的大小。该标志接受一个整数值，表示每个文件的大小限制(MB)

```
tcpdump -w out.pcap -C 2
```

在上面的例子中，tcpdump 会将捕获的数据包写入文件 out.pcap 中。一旦文件达到 2MB 的大小，

### 间隔旋转保存文件

使用 -G 标志，tcpdump 以指定的时间间隔将转储保存到新文件中。例如，我们可以每五秒轮换一次保存文件：

```
tcpdump -w out-%Y%m%d-%H%M%S.pcap -G 5
```

在上面的例子中，我们根据 strftime 格式动态命名我们的文件。如果指定的文件名在每个指定的时间间隔都是恒定的，则先前的保存文件将被覆盖。

## 6、其他有用的选项

### 禁止DNS解析

默认情况下，tcpdump 会自动解析主机地址的名称，它会导致 DNS 查找的网络开销。我们可以使用 -n 标志禁用查找：

```
tcpdump -n
```

### 数据包方向

默认情况下，tcpdump 捕获接口的 incoming 和outgoing流量，我们可以使用 -Q 标志后跟方向值 in、out 或 inout 来捕获特定方向的数据包。

例如，我们可以只捕获接口 eth0 上的传出数据包：

```
 tcpdump -i eth0 -Q out
```

### 打印包号

我们可以使 tcpdump 使用 -# 标志在每个输出行中显示数据包编号：

```
$ tcpdump -#
tcpdump: verbose output suppressed, use -v or -vv for full protocol decode
listening on eth0, link-type EN10MB (Ethernet), capture size 262144 bytes
    1  10:33:50.254707 IP 927e94ccde39.55667 > ************.53: 36710+ AAAA? www.baeldung.com. (34)
    2  10:33:50.254897 IP 927e94ccde39.49841 > ************.53: 19808+ A? www.baeldung.com. (34)
    3  10:33:50.255044 IP 927e94ccde39.58436 > ************.53: 37800+ PTR? ************.in-addr.arpa. (43)
```

### 显示详细信息

通过 -v 显示多一些信息 例如，IP 中的生存时间、标识、总长度和选项

```
tcpdump -i any -v host www.baidu.com
```

如果不带-v，HTTP请求不会记录请求内容

```
sudo tcpdump -i any -t host www.baidu.com
IP *************.50979 > *************.http: Flags [P.], seq 1:78, ack 1, win 8192, length 77: HTTP: GET / HTTP/1.1
IP *************.http > *************.50979: Flags [.], ack 78, win 908, length 0
IP *************.http > *************.50979: Flags [P.], seq 1:1441, ack 78, win 908, length 1440: HTTP: HTTP/1.1 200 OK
```

-vv

```
更详细的输出。 比如从NFS回复包打印附加字段，SMB包完全解码
```



## 7、表达式过滤

除了能使用的无数选项之外，tcpdump 还提供了一组表达式来过滤数据包。

### 表达式结构

表达式由限定符和原语(primitives) 组成：

> [qualifier...] qualifier primitive [and|or|not] [primitive...]

原语通常是数字或字符串。这些原语本身没有意义。为了赋予这些原语以意义，我们将在它们前面加上一个或多个限定符。

有三种不同的限定符—— type, dir, proto

- type 限定符指定原语的类型，类型限定符的可能值是host, net, port, and portrange。
-  dir 限定符指定数据包的方向，此限定符的一些可能值是 src、dst、src 或 dst，以及 src 和 dst。
-  proto 限定符代表要要匹配的协议，此限定符的可能值包括 ether、ip、ip6、arp、tcp 和 udp。

让我们看有些列子

### 按host过滤数据包

过滤数据包的最简单方法之一是按其主机进行过滤。具体来说，我们可以使用 host 语法捕获与特定主机关联的数据包：

```
tcpdump -t  host www.baidu.com
tcpdump: data link type PKTAP
tcpdump: verbose output suppressed, use -v or -vv for full protocol decode
listening on pktap, link-type PKTAP (Apple DLT_PKTAP), capture size 262144 bytes
IP *************.54796 > *************.https: Flags [P.], seq 1137109612:1137110716, ack 410197337, win 8192, length 1104
IP *************.https > *************.54796: Flags [.], ack 1104, win 1372, length 0
```

上面的命令只会转储来自或前往 www.baidu.com 的数据包。

### 按port过滤数据包

我们还可以使用限定符端口按端口号过滤数据包：

```
tcpdump port 80
```

上面的命令只捕获来自或去往端口 80 的任何数据包。

此外，我们还可以使用 src 或 dst 来确定方向。例如，如果我们只想捕获源字段为端口 80 的数据包，我们可以在表达式前加上 src：

```
tcpdump src port 80
```

除此之外，限定符 portrange 允许我们指定我们希望从中捕获数据包的端口号范围：

```
tcpdump portrange 80-90
```

使用上面的表达式，tcpdump 会捕获所有落在 80 到 90 端口范围内的数据包。

### 按Network过滤数据包

使用 net 限定符，我们可以根据数据包所属的网络过滤数据包。例如，我们可以捕获来自 ***********/16 网络的数据包

```
tcpdump net 192.168
```

当我们省略最后两个字节时，tcpdump 会自动将网络掩码 /16 应用于网络。 也可以使用掩码限定符显式指定网络掩码：

```
tcpdump net *********** mask 255.255.0.0
```

这两个不同的表达式最终构造了相同的过滤器。

### 按Protocol过滤数据包

要按协议过滤数据包，我们可以使用限定符 proto 后跟协议名称。例如，我们可以只转储 UDP 数据包：

```
tcpdump proto udp
```

该工具为更常见的协议（例如 tcp、udp 和 icmp）提供了一些快捷方式。例如，上面的命令可以只用 tcp 表达式重写：

```
tcpdump proto tcp
```

### 逻辑条件使用and、or

表达式支持 or， and 逻辑表达式

例如，我们可以构建一个过滤器，只捕获来自主机 www.baeldung.com **或** www.google.com 的流量：

```
 tcpdump 'src host www.baeldung.com or www.google.com'
```

### 范围条件使用 less

使用less 或greater 语法，我们可以按数据包长度过滤数据包。例如，仅捕获长度小于 40 的数据包：

```
tcpdump 'less 40'
```

除了使用前面提到的-Q 标志，我们可以使用入站和出站限定符按方向过滤数据包：

```
tcpdump 'inbound'
tcpdump 'outbound'
```



## 8、案例参考

推荐的参数公共参数

```
-i any -vv -A -n 
```

以上参数表示：

- 包含所以网卡。
- 输入详细信息
- 以 ASCII 格式打印每个数据包， 方便捕捉网页数据
- 禁用DNS解析

匹配端口号为8080的数据包

```
tcpdump  -i any -vv -A -n port 8080 -w /tmp/local.pcap
```

匹配指定主机，保存到指定文件

```
tcpdump -i any -vv -A -n  host ************ -w /tmp/local.pcap
```

Redis 抓包:

```
tcpdump -n -s 0 tcp port 6385 -w redis.pcap -i bond0 
```



## Wireshark过滤器



```
http.request.uri contains "hermes"
ip.src == *************
ip.dst == *************
```





## 参考

建议都参考英文文档

- [opensource.com:An introduction to using tcpdump at the Linux command line](https://opensource.com/article/18/10/introduction-tcpdump)
- [baeldung:sniffing-packet-tcpdump](https://www.baeldung.com/linux/sniffing-packet-tcpdump)
- [tcpdump](https://segmentfault.com/a/1190000018944427#item-5)
- [Wireshark过滤器写法总结 ](https://www.cnblogs.com/willingtolove/p/12519490.html)