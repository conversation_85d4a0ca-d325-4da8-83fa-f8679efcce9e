## 概述

TCP/IP 协议理论文章看了很多，那么计算机网络中实际传输的数据包是什么样的？是不是真的如理论说的一样3次握手，4 次挥手，这里我们通过 wireshark 抓包工具来验证下

## 环境准备

准备docker环境，启动两个容器

```bash
docker network create -d bridge my-net
docker run -it --rm --name test1 --network my-net centos-jdk-jemalloc:1.8 bash
docker run -it --rm --name test2 --network my-net centos-jdk-jemalloc:1.8 bash
```

在test2容器启动http服务，在test1容器通过telnet或者curl即可抓包：

```bash
python -mSimpleHTTPServer 8000
```

## tcpdump

抓包命令

```bash
tcpdump -i any -vv -A -n -S port 8000
```

>  这里的参数需要提一下的是 `-S`，如果不加 `-S` 参数看到的第三次握手的`ack=1`，与书上的理论不太一样，其实这里只是 **tcpdump** 简化了展示，想看实际值需要加 `-S`



### 三次握手

首先可以看到建立连接的 3 次握手过程：

![image-20220829234210852](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220829234210852.png)

每行都会先显示源 IP 和目标IP，这里是**********发起请求到**********， Flags表示如下: 

- [S] 代表 SYN，是Synchronize的缩写
- [.] 代表 ACK，[S.] 就是 SYN + ACK
- [P.]代表PSH，表示发送数据用。
- [R.] 代表 RST，表示直接关闭TCP连接，无需确认

对应的流程如下：

```
A-->B  SYN		
B-->A  SYN+ACK
A->B   ACK
```

### 发送数据

![wecom-temp-2b812caec720211d106312be32ce8fd6](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/wecom-temp-2b812caec720211d106312be32ce8fd6.png)

对应的流程：

```
A->B PSH+ACK //发送HTTP请求
B->A ACK
B->A PSH+ACK //返回HTTP响应
A->B ACK
```

### 4次挥手

我们抓到如下挥手数据：

![image-20220829234649082](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220829234649082.png)

- [F] 代表 FIN

这里可以看到是server端(**********)主动发起关闭的，不过有点奇怪的是，四次挥手居然变成了三次，这其实是 TCP 协议的实现问题，如果第2次与第3次挥手之间没有数据发送，那么被动断开连接的一方就可能会把第二次的 ACK 与 第三次的 FIN 合并为一次挥手。

## wireshark

可以通过如下命令把数据dump成文件然后通过wireshark分析

```
tcpdump -i any -vv -A -n port 8000  -w /tmp/local.pcap
```

wireshark封包详细信息是用来查看协议中的每一个字段。各行信息分别对应TCP/IP协议的不同层级。以下图为例，分别表示：传输层、网络层、数据链路层、物理层，一共四层。如果有应用层数据会显示第五层，即一共会出现五层。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830000339811.png" alt="image-20220830000339811" style="zoom:50%;" />

### 三次握手

根据上述报文格式我们可以将wireshark捕获到的TCP包中的每个字段与之对应起来，更直观地感受一下TCP通信过程。先看三次握手，下图中的3条数据包就是一次TCP建立连接的过程。

![image-20220830000155114](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830000155114.png)



第一次握手，客户端发送一个TCP，标志位为SYN=1，序号seq为Sequence number=0， 37442 -> 8000，代表客户端请求建立连接；

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830000634705.png" alt="image-20220830000634705" style="zoom:60%;" />



第二次握手，服务器向客户端返回一个数据包，SYN=1，ACK=1，8000-> 37442，将确认序号(Acknowledgement Number)设置为客户的序号seq(Sequence number)加1，即0+1=1；

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830001023064.png" alt="image-20220830001023064" style="zoom:60%;" />



第三次握手，客户端收到服务器发来的包后检查确认序号(Acknowledgement Number)是否正确，即第一次发送的序号seq加1（X+1= 0+1=1）。以及标志位ACK是否为1。若正确，客户端会再向服务器端发送一个数据包，SYN=0，ACK=1，确认序号(Acknowledgement Number)=Y+1=0+1=1，并且把服务器发来ACK的序号seq(Sequence number)加1发送给对方，发送序号seq为X+1= 0+1=1。客户端收到后确认序号值与ACK=1，37442 -> 8000，至此，一次TCP连接就此建立，可以传送数据了。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830001226944.png" alt="image-20220830001226944" style="zoom:60%;" />

还可以通过直接看标志位查看三次握手的数据包，如下图所示，第一个数据包标志位【SYN】，这是第一次握手；第二个数据包标志位【SYN,ACK】，这是第二次握手；第三个数据包标志位【ACK】，这是第三次握手。

![image-20220830001407669](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830001407669.png)



在三次握手的三个数据包之后，第四个包才是HTTP的， 这说明HTTP的确是使用TCP建立连接的。

### 发送数据

再往下看其他数据包，会发现存在大量的TCP segment of a reassembled PDU，字面意思是要重组的协议数据单元（PDU：Protocol Data Unit）的TCP段，这是TCP层收到上层大块报文后分解成段后发出去。

   每个数据包的Protocol Length都是1502 Byte，这是因为以太网帧的封包格式为：Frame = Ethernet Header + IP Header + TCP Header + TCP Segment Data。即：

1. Ethernet Header = 14 Byte = Dst Physical Address（6 Byte）+ Src Physical Address（6 Byte）+ Type（2 Byte），以太网帧头以下称之为数据帧。
2. IP Header = 20 Byte（without options field），数据在IP层称为Datagram，分片称为Fragment。
3. TCP Header = 20 Byte（without options field），数据在TCP层称为Stream，分段称为Segment（UDP中称为Message）。
4. TCP Segment Data = 1448 Byte（从下图可见）。

所以，每个数据包的Protocol Length = 14 Byte + 20 Byte + 20 Byte + 1448 Byte = 1502 Byte。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830001745200.png" alt="image-20220830001745200" style="zoom:50%;" />



### 四次挥手 

我们再来看四次挥手。TCP断开连接时，会有四次挥手过程，标志位是FIN，我们在封包列表中找到对应位置，理论上应该找到4个数据包，但我试了好几次，实际只抓到3个数据包。因为服务器端在给客户端传回的过程中，将两个连续发送的包进行了合并。

![image-20220830001901457](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830001901457.png)



 第一次挥手：客户端给服务器发送TCP包，用来关闭客户端到服务器的数据传送。将标志位FIN和ACK置为1

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830002132521.png" alt="image-20220830002132521" style="zoom:60%;" />





第二次挥手：服务器收到FIN后，服务器关闭与客户端的连接，发回一个FIN和ACK(标志位FIN=1,ACK=1)，37442 -> 8000；

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830002307699.png" alt="image-20220830002307699" style="zoom:60%;" />





第三次挥手：客户端收到服务器发送的FIN之后，发回ACK确认(标志位ACK=1),确认序号为收到的序号加1

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220830002457641.png" alt="image-20220830002457641" style="zoom:60%;" />



 至此，整个TCP通信过程已经介绍完毕。

## 抓包mysql

我在本地通过 [mysql-docker-compose](https://github.com/jiangyunpeng/dockers/tree/master/docker-compose/mysql)启动了一个mysql，尝试抓包mysql 结果踩了个坑，wireshark无法识别Mysql协议，只会显示TCP，如下图：

![image-20220831101930030](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220831101930030.png)

上网搜索了很久，wireshark设置了protocol的端口号还是无法解决，结果换到centos就可以了，怀疑还是mac docker的问题：

![image-20220831102258694](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220831102258694.png)

该pcap文件已上传到[github](https://github.com/jiangyunpeng/dockers/tree/master/docker-compose/mysql/datadir0)

## 抓包redis

因为redis是文本协议，所以redis非常容易抓包。[redis-traffic-stats](https://redis.io/docs/reference/protocol-spec/)是一个通过抓包分析redis流量的perl脚本。我通过java也实现了一个RedisTrafficParser。

### 一些有意思的发现

1、这里我发现第77和84 是一个 TCP 被拆分为两个包，组开始我以为每个packet都是完整数据，结果在解析redis协议的时候发现并不是这样

![image-20220928205723067](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220928205723067.png)



2、 ACK也会携带数据

最开始我在代码中过滤了非PSH标识的情况，感觉统计结果不准，猛然发现ACK也有带数据的情况：

![image-20220928213122256](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220928213122256.png)

stackoverflow寻找[答案](https://stackoverflow.com/questions/7935958/data-payload-in-a-tcp-ack)：

> 此行为取决于操作系统和应用程序。在linux中，内核不直接发送ACK，而是等待固定的毫秒数（大约200），希望有一些数据要发回，并且可以让ACK捎带数据。
>
> 如果计时器关闭，则立即发送 ACK。
>
> 例子 1
>
> ```
> Client sends the GET request.
> 
> Server tries to create a http response, but before it does that 200ms are gone
> and it must send the ACK before the http response.
> ```
>
> 例子 2
>
> ```
> Client sends the GET request.
> 
> Server creates a http response within the timer limit, and the ACK can piggyback
> the data.
> ```


## 参考

- [4个实验，彻底搞懂TCP连接的断开](https://mp.weixin.qq.com/s/7SvkHe3FiljxBWFkm8oAeA)
- [wireshark抓包分析——TCP/IP协议](https://www.cnblogs.com/163yun/p/9552368.html)

