## 追踪网络流量

某些场景下通过监控发现了流量比较大，不太合理，需要知道这些流量都是哪些进程访问哪些服务触发的

方法：

1. 定位流量是由哪个进程触发的；
2. 定位流量主要是访问哪些ip导致的：
3. 定位具体的端口有较大的流量：

工具

nethogs/iftop/tcptrack

### 定位进程

```
sudo nethogs 
```

[![image.png](https://plantegg.github.io/images/oss/e89eaf27aa98a6192d109ee22f9c0da8.png)](https://plantegg.github.io/images/oss/e89eaf27aa98a6192d109ee22f9c0da8.png)

从上图可以看到总的流量，以及每个进程的流量大小。这里可以确认流量主要是3820的java进程消耗的

### 定位ip

```
sudo iftop -p -n -B
```

[![image.png](https://plantegg.github.io/images/oss/1f03abbebbfc173b5af3163d017fd901.png)](https://plantegg.github.io/images/oss/1f03abbebbfc173b5af3163d017fd901.png)

通过上图可以看到流量主要是消耗在 *********的ip上

### 定位端口

********* 有可能是一个mapping ip，需要进一步查看具体

```
sudo tcptrack -r 5 -i eth0  //然后输入小写s，按流量排序
sudo tcptrack -r 5 -i eth0 host ********* //filter 语法和tcpdump一样
```

[![image.png](https://plantegg.github.io/images/oss/07f0fceb6af4c4387832561b630c00b3.png)](https://plantegg.github.io/images/oss/07f0fceb6af4c4387832561b630c00b3.png)

可以看到4355/4356端口上流量相对较大

