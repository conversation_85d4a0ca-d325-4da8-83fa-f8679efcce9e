# TCP疑难问题案例汇总

> 本文摘录自 [plantegg](https://plantegg.github.io/2021/02/14/TCP%E7%96%91%E9%9A%BE%E9%97%AE%E9%A2%98%E6%A1%88%E4%BE%8B%E6%B1%87%E6%80%BB/) 博客

碰到各种奇葩的TCP相关问题，所以汇总记录一下。分析清楚这些问题的所有来龙去脉，就能帮你在TCP知识体系里建立几个坚固的抓手，让TCP知识慢慢在抓手之间生长和互通

## 服务不响应的现象或者奇怪异常的原因分析

- [一个黑盒程序奇怪行为的分析](https://plantegg.github.io/2021/02/10/一个黑盒程序奇怪的行为分析/) listen端口上很快就全连接队列溢出了，导致整个程序不响应了
- [举三反一–从理论知识到实际问题的推导](https://plantegg.github.io/2020/11/02/举三反一--从理论知识到实际问题的推导/) 服务端出现大量CLOSE_WAIT 个数正好 等于somaxconn（调整somaxconn大小后 CLOSE_WAIT 也会跟着变成一样的值）
- [活久见，TCP连接互串了](https://plantegg.github.io/2020/11/18/TCP连接为啥互串了/) 应用每过一段时间总是会抛出几个连接异常的错误，需要查明原因。排查后发现是TCP连接互串了，这个案例实在是很珍惜，所以记录一下。
- [如何创建一个自己连自己的TCP连接](https://plantegg.github.io/2020/07/01/如何创建一个自己连自己的TCP连接/)

## 传输速度分析

- 案例：[TCP传输速度案例分析](https://plantegg.github.io/2021/01/15/TCP传输速度案例分析/)（长肥网络、rt升高、delay ack的影响等）
- 原理：[就是要你懂TCP–性能和发送接收Buffer的关系：发送窗口大小(Buffer)、接收窗口大小(Buffer)对TCP传输速度的影响，以及怎么观察窗口对传输速度的影响。BDP、RT、带宽对传输速度又是怎么影响的](https://plantegg.github.io/2019/09/28/就是要你懂TCP--性能和发送接收Buffer的关系/)
- [最经典的TCP性能问题 Nagle和Delay ack](https://plantegg.github.io/2018/06/14/就是要你懂TCP--最经典的TCP性能问题/)
- [性能优化大全](https://plantegg.github.io/2019/06/21/就是要你懂TCP--性能优化大全/)

- [10+倍性能提升全过程](https://plantegg.github.io/2018/01/23/10+%E5%80%8D%E6%80%A7%E8%83%BD%E6%8F%90%E5%8D%87%E5%85%A8%E8%BF%87%E7%A8%8B/)

## TCP队列问题以及连接数

- [到底一台服务器上最多能创建多少个TCP连接](https://plantegg.github.io/2020/11/30/一台机器上最多能创建多少个TCP连接/)
- [就是要你懂TCP队列–通过实战案例来展示问题](https://plantegg.github.io/2019/08/31/就是要你懂TCP队列--通过实战案例来展示问题/)
- [就是要你懂TCP–半连接队列和全连接队列](https://plantegg.github.io/2017/06/07/就是要你懂TCP--半连接队列和全连接队列/)
- [就是要你懂TCP–握手和挥手](https://plantegg.github.io/2017/06/02/就是要你懂TCP--连接和握手/)

## 防火墙和reset定位分析

对ttl、identification等的运用

- [关于TCP连接的Keepalive和reset](https://plantegg.github.io/2018/08/26/关于TCP连接的KeepAlive和reset/)
- [谁动了我的TCP连接](https://plantegg.github.io/2019/11/06/谁动了我的TCP连接/)

## TCP相关参数

- [TCP相关参数解释](https://plantegg.github.io/2020/01/26/TCP相关参数解释/)
- [网络通不通是个大问题–半夜鸡叫](https://plantegg.github.io/2019/05/16/网络通不通是个大问题--半夜鸡叫/)
- [网络丢包](https://plantegg.github.io/2018/12/26/网络丢包/)

## 工具技巧篇

- [netstat定位性能案例](https://plantegg.github.io/2019/04/21/netstat定位性能案例/)
- [netstat timer keepalive explain](https://plantegg.github.io/2017/08/28/netstat --timer/)
- [ss用法大全](https://plantegg.github.io/2016/10/12/ss用法大全/)
- [WireShark之命令行版tshark](https://plantegg.github.io/2019/06/21/就是要你懂抓包--WireShark之命令行版tshark/)
- [通过tcpdump对Unix Domain Socket 进行抓包解析](https://plantegg.github.io/2018/01/01/通过tcpdump对Unix Socket 进行抓包解析/)
- [如何追踪网络流量](https://plantegg.github.io/2017/12/07/如何追踪网络流量/)

## 更多参考

- [如何在工作中学习V1.1](https://plantegg.github.io/2018/05/24/%E5%A6%82%E4%BD%95%E5%9C%A8%E5%B7%A5%E4%BD%9C%E4%B8%AD%E5%AD%A6%E4%B9%A0V1.1/)