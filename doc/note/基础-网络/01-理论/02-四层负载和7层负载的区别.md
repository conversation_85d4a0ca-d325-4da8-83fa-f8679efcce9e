##  1、概述

笔者在学习kubernetes的kube-proxy的时候，kube-proxy具有三种proxy mode:

- userspace
- iptables
- ipvs

在Kubernetes v1.8.0-beta.0后引入的ipvs ， 官方文档宣称其有更好的性能和更多的选项。

那什么是IPVS呢， IPVS（IP Virtual Server）是构建于Netfilter之上，作为Linux内核的一部分提供传输层[负载均衡](https://cloud.tencent.com/product/clb?from=10680)的模块。

## 2、LVS是什么

提IPVS就不得不提LVS。

LVS是国内章文嵩博士开发并贡献给社区的，主要由IPVS和ipvsadm组成，IPVS是工作在内核态的4层负载均衡，和iptables一样都是基于内核底层netfilter实现，netfilter主要通过各个链的钩子实现包处理和转发。ipvsadm和IPVS的关系，就好比netfilter和iptables的关系，它运行在用户态，提供简单的CLI接口进行IPVS配置。

## 3 、什么是四层负载均衡

这里的“四层”指的是OSI七层模型中的第四层——传输层。 先看一下图吧：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/01.png" alt="01" style="zoom:50%;" />

如上图，一个数据从A节点传送到B节点，在A节点上的数据经过从第七层到第一层的层层封装，通过物理链路到达B节点后，再经过从第一层到第七层的层层解封后，最终为进程所用。

那么到底什么是四层负载均衡呢，上图只能解释OSI七层的工作模式，甚至连其中的网络层做什么都没有展现出来，再来看一下下图：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/02.png" alt="02" style="zoom:50%;" />

OSI模型是一个高阶模型，它指导着计算机节点间通讯所需遵循的基本原则，而在其具体实现——TCP/IP模型上，TCP/IP四层模型与之相对应。

**可以看到，工作在第四层的协议是TCP/UDP协议，也就是说四层负载均衡，是主要作用于TCP协议报文之上，基于IP+端口来判断需要重定向的报文，并修改报文中目标IP地址以进行重定向的负载均衡方式。**

**从第一张图可知，第四层的报文是看不到真正的数据内容的，所以此种负载不会考虑消息内容，例如无法根据不同的URL地址重定向到特定主机，相对的，其效率较高。**

## 4、 小结

由于IPVS工作在内核态，直接基于内核处理包转发，所以最大的特点就是性能非常好。又由于它工作在4层，因此不会处理应用层数据，经常有人问IPVS能不能做SSL证书卸载、或者修改HTTP头部数据，显然这些都不可能做的。

我们知道应用层负载均衡大多数都是基于反向代理实现负载的，工作在应用层，当用户的包到达负载均衡监听器listening后，基于一定的算法从后端服务列表中选择其中一个后端服务进行转发。当然中间可能还会有一些额外操作，最常见的如SSL证书卸载。而IPVS工作在内核态，只处理四层协议。

因此只能基于路由或者NAT进行数据转发，可以把IPVS当作一个特殊的路由器网关，这个网关可以根据一定的算法自动选择下一跳，或者把IPVS当作一个多重DNAT，按照一定的算法把ip包的目标地址DNAT到其中真实服务的目标IP。针对如上两种情况分别对应IPVS的两种模式--网关模式和NAT模式

## 5、IPVS用法

ipvsadm命令行用法和iptables命令行用法非常相似，毕竟是兄弟，比如 -L列举， -A添加， -D删除。

```
ipvsadm -A -t ***************:32016 -s rr
```


但是其实ipvsadm相对iptables命令简直太简单了，因为没有像iptables那样存在各种table，table嵌套各种链，链里串着一堆规则，ipvsadm就只有两个核心实体，分别为Service和Server，Service就是一个负载均衡实例，而Server就是后端member，IPVS术语中叫做real server，简称RS。

如下命令创建一个service实例 **********:32016， -t指定监听的为 TCP端口， -s指定算法为轮询算法rr（Round Robin），IPVS支持简单轮询（rr）、加权轮询（wrr）、最少连接（lc）、源地址或者目标地址散列（sh、dh）等10种调度算法。

```
ipvsadm -A -t **********:32016 -s rr
```


然后把**********:8080、**********:8080、**********:8080添加到Service后端member中。

```
ipvsadm -a -t **********:32016 -r **********:8080 -m -w 1
ipvsadm -a -t **********:32016 -r **********:8080 -m -w 1
ipvsadm -a -t **********:32016 -r **********:8080 -m -w 1
```


其中-t指定Service实例， -r指定Server地址， -w指定权值， -m即前面说的转发模式，其中 -m表示为masquerading，即NAT模式， -g为gatewaying，即直连路由模式， -i为 ipip,ji即IPIP隧道模式。

与iptables-save、iptables-restore对应的工具IPVS也有ipvsadm-save、ipvsadm-restore。

## 6、IPVS三种模式

### 1、NAT模式（VS-NAT）

原理：就是把客户端发来的数据包的IP头的目的地址，在负载均衡器上换成其中一台RS的IP地址，并发至此RS来处理,RS处理完成后把数据交给经过负载均衡器,负载均衡器再把数据包的原IP地址改为自己的IP，将目的地址改为客户端IP地址即可期间,无论是进来的流量,还是出去的流量,都必须经过负载均衡器。（即修改来去的目标IP和MAC地址）

优点：集群中的物理服务器可以使用任何支持TCP/IP操作系统，只有负载均衡器需要一个合法的IP地址。

缺点：扩展性有限。当服务器节点（普通PC服务器）数据增长到20个或更多时,负载均衡器将成为整个系统的瓶颈，因为所有的请求包和应答包都需要经过负载均衡器再生。假使TCP包的平均长度是536字节的话，平均包再生延迟时间大约为60us（在Pentium处理器上计算的，采用更快的处理器将使得这个延迟时间变短），负载均衡器的最大容许能力为8.93M/s，假定每台物理服务器的平台容许能力为400K/s来计算，负责均衡器能为22台物理服务器计算。

### 2、直接路由模式（VS-DR）

原理：负载均衡器和RS都使用同一个IP对外服务但只有DR对ARP请求进行响应,所有RS对本身这个IP的ARP请求保持静默也就是说,网关会把对这个服务IP的请求全部定向给DR,而DR收到数据包后根据调度算法,找出对应的RS,把目的MAC地址改为RS的MAC（因为IP一致）并将请求分发给这台RS这时RS收到这个数据包,处理完成之后，由于IP一致，可以直接将数据返给客户，则等于直接从客户端收到这个数据包无异,处理后直接返回给客户端由于负载均衡器要对二层包头进行改换,所以负载均衡器和RS之间必须在一个广播域,也可以简单的理解为在同一台交换机上

优点：和TUN（隧道模式）一样，负载均衡器也只是分发请求，应答包通过单独的路由方法返回给客户端。与VS-TUN相比，VS-DR这种实现方式不需要隧道结构，因此可以使用大多数操作系统做为物理服务器。

缺点：（不能说缺点，只能说是不足）要求负载均衡器的网卡必须与物理网卡在一个物理段上。

### 3、IP隧道模式（VS-TUN）

原理：首先要知道，互联网上的大多Internet服务的请求包很短小，而应答包通常很大。那么隧道模式就是，把客户端发来的数据包，封装一个新的IP头标记(仅目的IP)发给RS,RS收到后,先把数据包的头解开,还原数据包,处理后,直接返回给客户端,不需要再经过负载均衡器注意,由于RS需要对负载均衡器发过来的数据包进行还原,所以说必须支持IPTUNNEL协议所以,在RS的内核中,必须编译支持IPTUNNEL这个选项

优点：负载均衡器只负责将请求包分发给后端节点服务器，而RS将应答包直接发给用户。所以，减少了负载均衡器的大量数据流动，负载均衡器不再是系统的瓶颈，就能处理很巨大的请求量，这种方式，一台负载均衡器能够为很多RS进行分发。而且跑在公网上就能进行不同地域的分发。

缺点：隧道模式的RS节点需要合法IP，这种方式需要所有的服务器支持”IP Tunneling”(IP Encapsulation)协议，服务器可能只局限在部分Linux系统上。

IPVS是一个内核态的四层负载均衡，支持NAT、Gateway以及IPIP隧道模式，Gateway模式性能最好，但LB和RS不能跨子网，IPIP性能次之，通过ipip隧道解决跨网段传输问题，因此能够支持跨子网。而NAT模式没有限制，这也是唯一一种支持端口映射的模式。

我们不难猜想，由于Kubernetes Service需要使用端口映射功能，因此kube-proxy必然只能使用IPVS的NAT模式。

## 参考

- [浅析kube-proxy中的IPVS模式](https://cloud.tencent.com/developer/article/1477638)
- [IPVS从入门到精通kube-proxy实现原理](http://dockone.io/article/9441)
- [关于负载均衡的三种传输模式（反向代理，透传，三角）](https://blog.csdn.net/qq32933432/article/details/72547357)