## 概述

对于搞IT的同行而言，大部分人都不会直接和网络打交道，因此除非从事网络开发，否则对网络内部机制也不会太关心，但是在k8s时代，明白网络数据是怎么走的，这对每个IT工程师应该是很重要的基础知识。

## ISO

说起网络，大家不约而同会想起大学课本上那个臭名昭著的**ISO7层网络网络模型**，但是ISO模型只是提供了一个参考，并不是具体实现，目前我们使用最多的实现其实是TCP/IP协议族。但是对于TCP/IP，除了表示层和会话层没有体现，其它几层和ISO基本是对应的，从这个角度讲ISO模型还是有参考意义的。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/network/1.jpg" alt="1" style="zoom:67%;" />

- L1。对于物理层而言打交道的基本都是电信号和光信号，例如网卡、光纤、双绞线等都被归到物理层考虑；
- L2。对于链路层，数据在离散电/光信号的基础之上，被逻辑划分成一帧一帧（Frame）来管理，这一层是数据交换的主要层面，交换的依据主要是网卡MAC地址，以太网（定义了一种帧格式）、交换机、集线器都划归这一层；
- L3。网络层是比链路层更高一级的逻辑层，在这一层主要工作的是路由器，**路由器基于IP地址进行跨网**链路的计算；
- L4。传输层顾名思义是用来控制网络层传输的，因为网络层只是一个“尽力而为”的层，其传输不是完全可靠的，如果将超时重传等可靠性保障机制都交给程序员来做，估计大部分程序员都要疯了，幸好有了传输层提供了TCP和UDP两种机制给我们，才让我们可以高枕无忧的传输数据，而我们在代码里要做的只是打开一个传输层的套接字（即Socket）就可以了；
- L5和L6。至于表示层和会话层我们就不多做理解了，这两层基本只是摆设；
- L7。应用层是最高层的协议，Web HTTP协议、远程登录SSH协议等都被划归这一层，确切来说这一层已经不属于基础网络了，基本都是软件自定义协议。

理解了网络各层的概念后，我们再回过头来看，在这七层中网络层、链路层、物理层属于低三层，其余属于高四层，从字面上可以看出传输层以下才是真正通过网络传输数据的层面。对于物理层，主要定义的是各种传输介质的信号传输方式，比如时钟频率、电平高低、信道编码等，这一层只是机械的传输，不涉及数据包选路逻辑，链路层（L2层）和网络层（L3层）才是我们要理解的地方。

首先看L2链路层，这一层以帧（Frame）为单位组织物理信号，每个帧都需要有一个源地址和目的地址，绝大多数情况下使用的都是网卡MAC地址。这一层主要的数据转发设备是交换机。对于L2层，交换机只能转发一个子网内的数据帧（子网是通过IP地址划分的），如果要将一个数据帧跨网转发，则需要借助于L3层的路径规划功能。



上图是一张网络图谱图，简单解释下当A发送数据给C时的流程：

1、首先Ａ在发送之前，发现C和A在同一个子网内，于是A试图先在物理子网内找一下C，在同一物理子网内是通过硬件MAC地址来寻址的，而A此时并不知道C的MAC地址，于是A通过ARP广播来试图获取。

2、 交换机1收到ARP广播后会在本地缓存中找C，没有找到会转发给交换机2，交换机2收到后也会继续广播出去。当主机B和D收到这个广播包之后，发现和自己无关，于是便直接丢弃这个包，不做任何处理；C收到这个广播包后，发现原来是找自己的，于是它发出回应。

3、这个过程对于所有参与的交换机也是个学习的过程，因此交换机1和交换机2也学习到了Ａ和C的位置。至此AC相互找到对方后，便可以在同一物理子网之间直接通过指定MAC地址通信了。

下面再来看跨物理网络通信的情况（A向E发送数据）

1、第一步和前面发送给C的流程一致，不同的是A所在的交换机不会返回数据给A。A在等待超时后，发现当前物理子网内找不到E，但是A已经知道了网关路由器的MAC地址，于是便会将发给E的数据包扔给网关（也就是路由器1的1口）

2、路由器1收到这个包后，发现E的IP在自己内部也没有缓存，于是路由器1也开始了寻找E的过程。路由器的选路范围更大也更复杂，很多情况下是整个Internet，并且要夸多个运营商，所以在L3层面路由器的路径计算协议较多，包括：RIP、OSPF、IS-IS、BGP、IGRP等协议。这里假设路由器1直接找到了路由器2。

3、当路由器2接到寻找主机E的广播包后，发现E位于自己的网络中，便向前一跳路由器（即路由器1）反馈自己离主机E最近，最终经过这样一个“A→网关路由器→路由器间选路→找到主机E所在子网”的过程A终于可以与E进行通信了，由于A和E之间经历了多个物理子网，因此需要多次的L2转发才能实现数据包的到达，这个过程中L3层IP包外包帧的MAC地址会不断变换

## 二三层交换机

二三层网络交换机的区别用一句话解释：

- 二层交换机 基于MAC地址
- 三层交换机 具有VLAN功能 有交换和路由 ///基于IP，就是网络

　二层交换技术从网桥发展到VLAN（虚拟局域网），在局域网建设和改造中得到了广泛的应用。第二层交换技术是工作在OSI七层网络模型中的第二层，即数据链路层。它按照所接收到数据包的目的MAC地址来进行转发，对于网络层或者高层协议来说是透明的。它不处理网络层的IP地址，不处理高层协议的诸如TCP、UDP的端口地址，它只需要数据包的物理地址即MAC地址，数据交换是靠硬件来实现的，其速度相当快，这是二层交换的一个显著的优点。但是，它不能处理不同IP子网之间的数据交换。传统的路由器可以处理大量的跨越IP子网的数据包，但是它的转发效率比二层低，因此要想利用二层转发效率高这一优点，又要处理三层IP数据包，三层交换技术就诞生了。

第三层交换工作在OSI七层网络模型中的第三层即网络层，是利用第三层协议中的IP包的包头信息来对后续数据业务流进行标记，具有同一标记的业务流的后续报文被交换到第二层数据链路层，从而打通源IP地址和目的IP地址之间的一条通路。这条通路经过第二层链路层。有了这条通路，三层交换机就没有必要每次将接收到的数据包进行拆包来判断路由，而是直接将数据包进行转发，将数据流进行交换。

## 路由技术

　　路由器工作在OSI模型的第三层—网络层操作，其工作模式与二层交换相似，但路由器工作在第三层，这个区别决定了路由和交换在传递包时使用不同的控制信息，实现功能的方式就不同。工作原理是在路由器的内部也有一个表，这个表所标示的是如果要去某一个地方，下一步应该向那里走，如果能从路由表中找到数据包下一步往那里走，把链路层信息加上转发出去；如果不能知道下一步走向那里，则将此包丢弃，然后返回一个信息交给源地址。
　　路由技术实质上来说不过两种功能：决定最优路由和转发数据包。路由表中写入各种信息，由路由算法计算出到达目的地址的最佳路径，然后由相对简单直接的转发机制发送数据包。接受数据的下一台路由器依照相同的工作方式继续转发，依次类推，直到数据包到达目的路由器。
　　而路由表的维护，也有两种不同的方式。一种是路由信息的更新，将部分或者全部的路由信息公布出去，路由器通过互相学习路由信息，就掌握了全网的拓扑结构，这一类的路由协议称为距离矢量路由协议；另一种是路由器将自己的链路状态信息进行广播，通过互相学习掌握全网的路由信息，进而计算出最佳的转发路径，这类路由协议称为链路状态路由协议。
　　由于路由器需要做大量的路径计算工作，一般处理器的工作能力直接决定其性能的优劣。当然这一判断还是对中低端路由器而言，因为高端路由器往往采用分布式处理系统体系设计。

## BGP协议

BGP全称Border Gateway Protocol，对应中文是边界网关协议。BGP是互联网上一个去中心化自治路由协议。



## 交换机和路由器的区别

以下是交换机和路由器的主要区别：

1. **工作层次不同**：
   - 交换机位于网络的数据链路层（第二层），负责在局域网内根据MAC地址转发数据帧。
   - 路由器位于网络的网络层（第三层），负责在不同的子网之间进行数据包转发和路由选择。
2. **转发决策依据**：
   - 交换机根据MAC地址来转发数据，因此它主要在局域网内部进行数据包交换。
   - 路由器根据IP地址来转发数据，因此它主要用于连接不同子网或广域网之间的数据传输。
3. **范围和规模**：
   - 交换机通常用于连接局域网内的设备，可以处理大量的局域网内部通信。
   - 路由器通常用于连接不同的局域网或广域网，用于跨越较大范围的网络传输。
4. **数据包处理**：
   - 交换机在内部维护MAC地址表，根据学习的地址表将数据帧直接转发给目标设备，从而提高了内部通信的效率。
   - 路由器根据路由表来决定如何将数据包从源地址转发到目标地址，进行网络间的数据传递。
5. **广播和碰撞域**：
   - 交换机划分了每个端口的碰撞域，因此它可以避免碰撞，并且不会在整个局域网内广播数据包。
   - 路由器在不同的子网之间创建隔离的广播域，不会传播广播包到其他子网。
6. **网络地址转换（NAT）**：
   - 路由器通常支持网络地址转换（NAT），允许多个设备共享一个公共IP地址，从而实现多台设备通过单个IP地址上网。
   - 交换机一般不直接支持NAT功能，因为它主要处理数据链路层的转发。

交换机、路由器使用在不同的网络环境：

1. 如果只有一台台式电脑，网线直接插入光猫即可，不需要路由器、交换机
2. 如果多台电脑和手机需要接入，光猫肯定是不够用的(没有wifi、插口不够)，这时候就需要路由器
3. 对于网吧有 100 台机器，路由器不可能连 100 台机器，这时候就需要交换机，交换机再连接路由器



## 参考

- [1. 交换机二三层转发原理简单总结](https://blog.csdn.net/Apollon_krj/article/details/82086174)
- [2. 两层网络、三层网络的理解](https://blog.csdn.net/cj2580/article/details/80107037)
- [3. 图文详解：当我用最浅显的方式打开网络传输！](https://zhuanlan.zhihu.com/p/417217501)