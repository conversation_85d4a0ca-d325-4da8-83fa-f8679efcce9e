## 概述

**超文本传输协议**（英语：**H**yper**T**ext **T**ransfer **P**rotocol，缩写：**HTTP**）是一种用于分布式、协作式和超媒体信息系统的应用层协议。HTTP是万维网的数据通信的基础。

HTTP是一个客户端（用户）和服务端（网站）之间请求和应答的标准，通常使用TCP协议。通过使用网页浏览器、网络爬虫或者其它的工具，客户端发起一个HTTP请求到服务器上指定端口（默认端口为80）。我们称这个客户端为用户代理程序（user agent）。应答的服务器上存储着一些资源，比如HTML文件和图像。我们称这个应答服务器为源服务器（origin server）。在用户代理和源服务器中间可能存在多个“中间层”，比如代理服务器、网关或者隧道（tunnel）。

尽管TCP/IP协议是互联网上最流行的应用，但是在HTTP协议中并没有规定它必须使用或它支持的层。**事实上HTTP可以在任何互联网协议或其他网络上实现。HTTP假定其下层协议提供可靠的传输。因此，任何能够提供这种保证的协议都可以被其使用，所以其在TCP/IP协议族使用TCP作为其传输层**。

通常，由HTTP客户端发起一个请求，创建一个到服务器指定端口（默认是80端口）的TCP连接。HTTP服务器则在那个端口监听客户端的请求。一旦收到请求，服务器会向客户端返回一个状态，比如"HTTP/1.1 200 OK"，以及返回的内容，如请求的文件、错误消息、或者其它信息。

>设计HTTP最初的目的是为了提供一种发布和接收HTML页面的方法。通过HTTP或者HTTPS协议请求的资源由统一资源标识符（Uniform Resource Identifiers，URI）来标识。
>
>HTTP的标准制定由 W3C 和互联网工程任务组（Internet Engineering Task Force，IETF）进行协调，最终发布了一系列的RFC，其中最著名的是1999年6月公布的 RFC 2616，定义了HTTP协议中现今广泛使用的一个版本——HTTP 1.1。
>
>2014年12月，互联网工程任务组（IETF）的Hypertext Transfer Protocol Bis（httpbis）工作小组将HTTP/2标准提议递交至IESG进行讨论[2]，于2015年2月17日被批准。 HTTP/2标准于2015年5月以RFC 7540正式发表，取代HTTP 1.1成为HTTP的实现标准。

## 历史版本

超文本传输协议已经演化出了很多版本，它们中的大部分都是向下兼容的。在[RFC 2145](https://tools.ietf.org/html/rfc2145)中描述了HTTP版本号的用法。客户端在请求的开始告诉服务器它采用的协议版本号，而后者则在响应中采用相同或者更早的协议版本。

- HTTP/1.0
    这是第一个在通讯中指定版本号的HTTP协议版本。
- HTTP/1.1
    默认采用持续连接（Connection: keep-alive），能很好地配合代理服务器工作。还支持以管道方式在同时发送多个请求，以便降低线路负载，提高传输速度。
    HTTP/1.1相较于HTTP/1.0协议的区别主要体现在：
    - 缓存处理
    - 带宽优化及网络连接的使用
    - 错误通知的管理
    - 消息在网络中的发送
    - 互联网地址的维护
    - 安全性及完整性
- HTTP/2
    当前版本，于2015年5月作为互联网标准正式发布。

## 报文

### 请求报文

HTTP 协议是以 ASCII 码传输，建立在 TCP/IP 协议之上的应用层规范。规范把 HTTP 请求分为三个部分：状态行、请求头、消息主体。类似于下面这样：

```xml
<method> <request-URL> <version>
<headers>
\r\n 
\r\n
<entity-body>
```

GET 请求报文示例：

```java
GET /books/?sex=man&name=Professional HTTP/1.1
Host: www.example.com
User-Agent: Mozilla/5.0 (Windows; U; Windows NT 5.1; en-US; rv:1.7.6)
Gecko/20050225 Firefox/1.0.1
Connection: Keep-Alive
\r\n 
\r\n    
```

Post 请求报文示例：

```java
 POST /books/ HTTP/1.1
 Host: localhost:8864    
 User-Agent: Java/1.8.0_121
 Content-Type: application/x-www-form-urlencoded;charset=utf-8
 Content-Length: 15
 Connection: Keep-Alive
 \r\n
 \r\n
 aaa=111&bbb=222  
```

- headers 末尾会带上两个\r\n 表示header结束
- 参考上面的报文示例，可以发现 GET 和 POST 数据内容是一模一样的，只是位置不同，一个在 URL 里，一个在 HTTP 包的包体里
- GET 可提交的数据量受到URL长度的限制(2KB)，HTTP 协议规范没有对 URL 长度进行限制。这个限制是特定的浏览器及服务器对它的限制。
- 理论上讲，POST 是没有大小限制的，HTTP 协议规范也没有进行大小限制，出于安全考虑，服务器软件在实现时会做一定限制。

###  响应报文

HTTP 响应与 HTTP 请求相似，HTTP响应也由3个部分构成：状态行，响应头(Response Header)，响应正文。

响应报文格式如下:

```xml
<协议版本> 状态码 状态描述
<响应头>
    
<响应正文>    
```

下面是一个HTTP响应的例子：

```java
HTTP/1.1 200 OK
Server: openresty
Date: Fri, 10 Jan 2020 09:42:54 GMT
Content-Type: text/html;charset=utf-8
Transfer-Encoding: chunked
Connection: keep-alive

<html>...
```

如果服务器端响应的是 Transfer-Encoding: chunked ，那么可以不指定Content-Length，后面会详细解释。



### 请求方法

HTTP/1.1协议中共定义了八种方法来以不同方式操作指定的资源：

- GET

    向指定的资源发出“显示”请求。使用GET方法应该只用在读取资料，而不应当被用于产生“[副作用](https://zh.wikipedia.org/wiki/超文本传输协议#副作用)”的操作中。浏览器直接发出的GET只能由一个url触发。GET上要在url之外带一些参数就只能依靠url上附带querystring。

- HEAD

    与GET方法一样，都是向服务器发出指定资源的请求。只不过服务器将不传回资源的本文部分。它的好处在于，使用这个方法可以在不必传输全部内容的情况下，就可以获取其中“关于该资源的信息”（元信息或称元数据）。

- POST

    向指定资源提交数据，请求服务器进行处理（例如提交表单或者上传文件）。数据被包含在请求本文中。这个请求可能会创建新的资源或修改现有资源，或二者皆有。每次提交，表单的数据被浏览器用编码到HTTP请求的body里。浏览器发出的POST请求的body主要有两种格式，一种是application/x-www-form-urlencoded用来传输简单的数据，大概就是"key1=value1&key2=value2"这样的格式。另外一种是传文件，会采用multipart/form-data格式。**采用后者是因为application/x-www-form-urlencoded的编码方式对于文件这种二进制的数据非常低效**。

- PUT

    向指定资源位置上传其最新内容。

- DELETE

    请求服务器删除Request-URI所标识的资源。

- TRACE

    回显服务器收到的请求，主要用于测试或诊断。

- OPTIONS

    这个方法可使服务器传回该资源所支持的所有HTTP请求方法。用'*'来代替资源名称，向Web服务器发送OPTIONS请求，可以测试服务器功能是否正常运作。

- CONNECT

    HTTP/1.1协议中预留给能够将连接改为隧道方式的代理服务器。通常用于SSL加密服务器的链接（经由非加密的HTTP代理服务器）。

**方法名称是区分大小写的**。当某个请求所针对的资源不支持对应的请求方法的时候，服务器应当返回[状态码405](https://zh.wikipedia.org/wiki/HTTP状态码#405)（Method Not Allowed），当服务器不认识或者不支持对应的请求方法的时候，应当返回[状态码501](https://zh.wikipedia.org/wiki/HTTP状态码#501)（Not Implemented）。

**HTTP服务器至少应该实现GET和HEAD方法**，其他方法都是可选的。当然，所有的方法支持的实现都应当符合下述的方法各自的语义定义。此外，除了上述方法，特定的HTTP服务器还能够扩展自定义的方法。例如：

- PATCH（由 [RFC 5789](https://tools.ietf.org/html/rfc5789) 指定的方法）

### 状态码

所有HTTP响应的第一行都是**状态行**，依次是当前HTTP版本号，3位数字组成的[状态代码](https://zh.wikipedia.org/wiki/HTTP状态码)，以及描述状态的短语，彼此由空格分隔。

状态代码的第一个数字代表当前响应的类型：

- [1xx消息](https://zh.wikipedia.org/wiki/HTTP状态码#1xx消息)——请求已被服务器接收，继续处理
- [2xx成功](https://zh.wikipedia.org/wiki/HTTP状态码#2xx成功)——请求已成功被服务器接收、理解、并接受
- [3xx重定向](https://zh.wikipedia.org/wiki/HTTP状态码#3xx重定向)——需要后续操作才能完成这一请求
- [4xx请求错误](https://zh.wikipedia.org/wiki/HTTP状态码#4xx请求错误)——请求含有词法错误或者无法被执行。 403没有权限
- [5xx服务器错误](https://zh.wikipedia.org/wiki/HTTP状态码#5xx服务器错误)——服务器在处理某个正确请求时发生错误

虽然 [RFC 2616](https://tools.ietf.org/html/rfc2616) 中已经推荐了描述状态的短语，例如"200 OK"，"[404 Not Found](https://zh.wikipedia.org/wiki/HTTP_404)"，但是WEB开发者仍然能够自行决定采用何种短语，用以显示本地化的状态描述或者自定义信息。

*Table 27.* *Five Types of HTTP Result Codes.

| Code | Type          | Example Reasons                                        |
| ---- | ------------- | ------------------------------------------------------ |
| 1xx  | Informational | request received, continuing process                   |
| 2xx  | Success       | action successfully received, understood, and accepted |
| 3xx  | Redirection   | further action must be taken to complete the request   |
| 4xx  | Client Error  | request contains bad syntax or cannot be fulfilled     |
| 5xx  | Server Error  | server failed to fulfill an apparently valid request   |



## Content-Type

Content-Type 基本要点：

- Content-Type说明了http body的MIME类型的 header字段。
- MIME类型由一个主媒体类型(比如text,image,audio等)后面跟一条斜线以及一个子类型组成，子类型用于进一步描述媒体类型。

对于post请求，默认情况下， http 会对表单数据进行编码提交。笔者实现分片文件上传时，上传分片二进制数据，若是不指定`Content-Type: application/octet-stream` 则http对二进制进行了一定的变化，导致服务端和客户端对不上。

## Content-Encoding

http协议中有 Content-Encoding（内容编码）。Content-Encoding 通常用于对实体内容进行压缩编码，目的是优化传输，例如用 gzip 压缩文本文件，能大幅减小体积。内容编码通常是选择性的，例如 jpg / png 这类文件一般不开启，因为图片格式已经是高度压缩过的。

内容编码针对的只是传输正文。在 HTTP/1 中，头部始终是以 ASCII 文本传输，没有经过任何压缩，这个问题在 HTTP/2 中得以解决。

## Content-length

Content-length有几个问题：

- 发送数据时，对某些场景，计算数据长度本身是一个比较耗时的事情，同时会占用一定的memory。
- 接收数据时，从协议头拿到数据长度，接收不够这个长度的数据，就不能解码后交给上层处理。

## Transfer-Encoding

Transfer-Encoding 是一种**响应报文**， 用来改变报文格式。这涉及到一个通信协议的重要问题：如何定义协议数据的边界？通常有以下三种方式：

1. 发送完就断连接（非持久连接）
2. 协议头部设定content-length
3. 以特殊字符结尾

Transfer-Encoding 是一个用来标示 HTTP 报文传输格式的Header值。表示消息体由数量未定的块(chunk)组成，每个chunked包含十六进制的长度值和数据，并以最后一个大小为0的块为结束。

一个示例如下：

```java
HTTP/1.1 200 OK
Content-Type: text/plain
Transfer-Encoding: chunked

25
This is the data in the first chunk

1A
and this is the second one
0
```

注意：

- chunked 传输不能事先知道内容的长度，只能靠最后的空 chunk 块来判断，因此对于下载请求来说，是没有办法实现进度的。在浏览器和下载工具中，偶尔我们也会看到有些文件是看不到下载进度的，即采用 chunked 方式进行下载。
- chunked 的优势在于，服务器端可以边生成内容边发送，无需事先生成全部的内容。HTTP/2 不支持 Transfer-Encoding: chunked，因为 HTTP/2 有自己的 streaming 传输方式

参考：

- [MDN - Transfer-Encoding](https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Transfer-Encoding)

## KeepAlive

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_3c148a80-6730-4cd0-bbd9-102570a67dff.png" alt="企业微信截图_3c148a80-6730-4cd0-bbd9-102570a67dff" style="zoom:50%;" />

在HTTP 0.9和1.0中，[TCP连接](https://zh.wikipedia.org/wiki/传输控制协议)在每一次请求/回应对之后关闭。在HTTP 1.1中，引入了keepAlive机制，一个连接可以重复在多个请求/回应使用。keepAlive的方式可以大大减少[等待时间](https://zh.wikipedia.org/wiki/延迟_(工程学))，因为在发出第一个请求后，双方不需要重新运行[TCP握手程序](https://zh.wikipedia.org/wiki/握手_(技术))。

在 HTTP 1.1 版本中，默认情况下所有连接都被保持，如果加入 "Connection: close" 才关闭。目前大部分浏览器都使用 HTTP 1.1 协议，也就是说默认都会发起 Keep-Alive 的连接请求了，所以是否能完成一个完整的 Keep-Alive 连接就看服务器设置情况。

关于 Keep-Alive 的一些小结

- HTTP Keep-Alive 简单说就是保持当前的TCP连接，避免了重新建立连接。
- HTTP 长连接不可能一直保持，例如 `Keep-Alive: timeout=5, max=100`，表示这个TCP通道可以保持5秒，max=100，表示这个长连接最多接收100次请求就断开。
- 使用长连接之后，客户端、服务端怎么知道本次传输结束呢？两部分：1. 判断传输数据是否达到了Content-Length 指示的大小；2. 动态生成的文件没有 Content-Length ，它是分块传输（chunked），这时候就要根据 chunked 编码来判断，chunked 编码的数据在最后有一个空 chunked 块，表明本次传输数据结束。

## If-Modified-Since

If-Modified-Since 是 HTTP 协议为了减少不必要的带宽浪费，提出的一种方案。详见 [RFC2616](http://www.w3.org/Protocols/rfc2616/rfc2616-sec9.html) 。

客户端向服务器发送一个包询问是否在上一次访问网站的时间后更改了页面，如果服务器没有更新，显然不需要把整个网页传给客户端，客户端只要使用本地缓存即可，否则发送这个更新了的网页给用户。

下面是一个具体的发送接受报文示例：

```java
 GET / HTTP/1.1  
 Host: www.sina.com.cn:80  
 If-Modified-Since:Thu, 4 Feb 2010 20:39:13 GMT  
 Connection: Close  
```

第一次请求时，服务器端返回请求数据，之后的请求，服务器根据请求中的 `If-Modified-Since` 字段判断响应文件没有更新，如果没有更新，服务器返回一个 `304 Not Modified`响应，告诉浏览器请求的资源在浏览器上没有更新，可以使用已缓存的上次获取的文件。

## 100-continue

http 100-continue用于客户端在发送POST数据给服务器前，征询服务器情况，看服务器是否处理POST的数据，如果不处理，客户端则不上传POST数据，如果处理，则POST上传数据。
在现实应用中，通常在POST大数据时，才会使用100-continue协议。

## Pipelining

注意：HTTP Pipelining 默认并未支持。

默认情况下 HTTP 协议中每个传输层连接只能承载一个 HTTP 请求和响应，浏览器会在收到上一个请求的响应之后，再发送下一个请求。在使用持久连接的情况下，某个连接上消息的传递类似于`请求1 -> 响应1 -> 请求2 -> 响应2 -> 请求3 -> 响应3`。

HTTP Pipelining（管线化）是将多个 HTTP 请求整批提交的技术，在传送过程中不需等待服务端的回应。使用 HTTP Pipelining 技术之后，某个连接上的消息变成了类似这样`请求1 -> 请求2 -> 请求3 -> 响应1 -> 响应2 -> 响应3`。

注意下面几点：

- 管线化机制通过持久连接（persistent connection）完成，仅 HTTP/1.1 支持此技术（HTTP/1.0不支持）
- 只有 GET 和 HEAD 请求可以进行管线化，而 POST 则有所限制
- 初次创建连接时不应启动管线机制，因为对方（服务器）不一定支持 HTTP/1.1 版本的协议
- 管线化不会影响响应到来的顺序，如上面的例子所示，响应返回的顺序并未改变
- HTTP /1.1 要求服务器端支持管线化，但并不要求服务器端也对响应进行管线化处理，只是要求对于管线化的请求不失败即可
- 由于上面提到的服务器端问题，开启管线化很可能并不会带来大幅度的性能提升，而且很多服务器端和代理程序对管线化的支持并不好，因此现代浏览器如 Chrome 和 Firefox 默认并未开启管线化支持

## Connect

Connect 是一个HTTP方法，用于请求代理服务器建立连接。

## Trailer

**Trailer** 是一个响应 Header，允许发送方在分块发送的消息后面添加额外的元信息，这些元信息可能是随着消息主体的发送动态生成的，比如消息的完整性校验，消息的数字签名，或者消息经过处理之后的最终状态等。例如：

```
HTTP/1.0 200 OK 
Content-Type: text/plain 
Transfer-Encoding: chunked
Trailer: Expires

0\r\n
Mozilla\r\n 
7\r\n
Developer\r\n
9\r\n
Network\r\n
0\r\n 
\r\n
```



## 工作中的实践

### 1、错误状态码注意事项

5XX 和 4XX 都是错误状态码，区别是4XX代表用户参数的错误而5XX代表服务器内部错误，在API网关中验签失败最开始返回的是500错误码，导致外网用户看到的是统一错误页面，后来改为401。



### 2、使用KeepAlive注意事项

- HTTP1.1自带KeepAlive。
- 当Server端返回HTTP头Connection: Close，Client端会关闭连接。 
- Tomcat 对于每个连接达到一次请求次数时会返回Connection: Close关闭连接，这个配置是maxKeepAliveRequests，默认100。详见[Tomcat对于http-keepalive的控制.md]
- 如果server既没有返回content length 也没有返回 Transfer-Encoding，可能会导致http client关闭，详见OkHTTP文档。
- KeepAlive不是灵丹妙药，在内网中不一定是瓶颈点。



### 3、GET 请求中能携带body体吗？

答案是可以的。

不带 body 的普通GET报文：

```properties
GET /titan/auth/dubbo/resource?artifactid=test HTTP/1.1\r\n
User-Agent: PostmanRuntime/7.26.8\r\n
Accept: */*\r\n
Postman-Token: cdfb1fdf-c558-4fd0-af1d-6a13bd1fe223\r\n
Host: *************:8080\r\n
Accept-Encoding: gzip, deflate, br\r\n
Connection: keep-alive\r\n
\r\n
```

带body 的GET报文，在SpringMVC中可以拿到body中的数据：

```properties
GET /titan/auth/dubbo/resource?artifactid=test HTTP/1.1\r\n
User-Agent: PostmanRuntime/7.26.8\r\n
Accept: */*\r\n
Postman-Token: b4dcc12f-635c-4c28-8829-9a99f6733951\r\n
Host: *************:8080\r\n
Accept-Encoding: gzip, deflate, br\r\n
Connection: keep-alive\r\n
Content-Type: application/json\r\n
Content-Length: 112\r\n
\r\n
{"providerName":"buzz-wacai","resourceName":"com.wacai.ucenter.service.PaymentRealNameService2","tolerant":true}
```

 [RFC 7231](http://tools.ietf.org/html/rfc7231#page-24) 并没有说明GET请求中包含body体会发生什么，所以有些server允许，有些不行(尤其是caching proxies)不推荐这样做。不过在特定的场景存在GET请求携带body，比如 Elasticsearch中的查询，body中会包含一些参数：

```properties
GET /_search
{
  "from": 30,
  "size": 10
}
```

相关讨论:

- [elasticsearch search ](https://www.elastic.co/guide/en/elasticsearch/guide/current/_empty_search.html)
- [http-get-with-request-body](https://stackoverflow.com/questions/978061/http-get-with-request-body)
- [mozilla-GET](https://developer.mozilla.org/en-US/docs/Web/HTTP/Methods/GET)

### 4、HTTP如何传输二进制？

关注此问题是API网关对加密是否需要Base64的问题:

>我们知道在计算机中任何数据都是按ascii码存储的，而ascii码的128～255之间的值是不可见字符。而在网络上交换数据时，比如说从A地传到B地，往往要经过多个路由设备，由于不同的设备对字符的处理方式有一些不同，这样那些不可见字符就有可能被处理错误，这是不利于传输的。所以就先把数据先做一个Base64编码，统统变成可见字符，这样出错的可能性就大降低了。

我的疑问是HTTP是针对二进制数据做特殊的规定（mime），所以用http直接传输二进制数据是可行的，为啥还需要Base64?

真正的原因是二进制不兼容。某些二进制值，在一些硬件上，比如在不同的路由器，老电脑上，表示的意义不一样，做的处理也不一样。同样，一些老的软件，网络协议也有类似的问题。但是万幸，Base64使用的64个字符，经ASCII/UTF-8编码后在大多数机器，软件上的行为是一样的。

相关讨论:

- [为什么要使用base64编码，有哪些情景需求](https://www.zhihu.com/question/36306744)

### 5、 HTTP Header的实践

HTTP Header 存在以下限制：

- 长度限制。
- 不能包含中文字符，需要URLEncode。
- value不能null。
- name不能是下划线。

所以在 header 中保存 error 信息是一种不太好的设计。

## 参考

- [维基百科-HTTP协议](https://zh.wikipedia.org/wiki/%E8%B6%85%E6%96%87%E6%9C%AC%E4%BC%A0%E8%BE%93%E5%8D%8F%E8%AE%AE)
- [ HTTP1.1规范 RFC2616](https://datatracker.ietf.org/doc/html/rfc2616)
- [HTTP 协议中的 Transfer-Encoding](https://imququ.com/post/transfer-encoding-header-in-http.html)

