## HTTP/2 

HTTP2.0 可以说是SPDY的升级版（其实原本也是基于SPDY设计的），但是，HTTP2.0 跟 SPDY 仍有不同的地方，主要是以下两点：

- HTTP2.0 支持明文 HTTP 传输，而 SPDY 强制使用 HTTPS （注：不过实际上HTTP2.0）
- HTTP2.0 消息头的压缩算法采用 HPACK，而非 SPDY 采用的 DEFLATE

HTTP2.0的新特性:

- 新的二进制格式（Binary Format），HTTP1.x的解析是基于文本。基于文本协议的格式解析存在天然缺陷，文本的表现形式有多样性，要做到健壮性考虑的场景必然很多，二进制则不同，只认0和1的组合。基于这种考虑HTTP2.0的协议解析决定采用二进制格式，实现方便且健壮。
- 多路复用（MultiPlexing），即连接共享，即每一个request都是是用作连接共享机制的。一个request对应一个id，这样一个连接上可以有多个request，每个连接的request可以随机的混杂在一起，接收方可以根据request的 id将request再归属到各自不同的服务端请求里面。
- header压缩，如上文中所言，对前面提到过HTTP1.x的header带有大量信息，而且每次都要重复发送，HTTP2.0使用encoder来减少需要传输的header大小，通讯双方各自cache一份header fields表，既避免了重复header的传输，又减小了需要传输的大小。
- 服务端推送（server push），同SPDY一样，HTTP2.0也具有server push功能。目前，有大多数网站已经启用HTTP2.0，例如YouTuBe，淘宝网等网站，利用chrome控制台可以查看是否启用H2：

更多关于HTTP2的问题可以参考：HTTP2奇妙日常（http://www.alloyteam.com/2015/03/http2-0-di-qi-miao-ri-chang/），以及HTTP2.0的官方网站。



### 1、单一长连接

在HTTP/2中，客户端向某个域名的服务器请求页面的过程中，只会创建一条TCP连接，即使这页面可能包含上百个资源。而之前的`HTTP/1.x`一般会创建6-8条TCP连接来请求这100多个资源。单一的连接应该是HTTP2的主要优势，单一的连接能减少TCP握手带来的时延（如果是建立在SSL/TLS上面，HTTP2能减少很多不必要的SSL握手，大家都知道SSL握手很慢吧）。

另外我们知道，TCP协议有个滑动窗口，有慢启动这回事，就是说每次建立新连接后，数据先是慢慢地传，然后滑动窗口慢慢变大，才能较高速度地传，这下倒好，这条连接的滑动窗口刚刚变大，`http1.x`就创个新连接传数据（这就好比人家HTTP2一直在高速上一直开着，你`HTTP1.x`是一辆公交车走走停停）。由于这种原因，让原本就具有突发性和短时性的 HTTP 连接变的十分低效。

所以咯，HTTP2中用一条单一的长连接，避免了创建多个TCP连接带来的网络开销，提高了吞吐量。

### 2、多路复用

明明是一条连接，怎么又是多路复用呢？后台开发的童鞋应该对多路复用很熟悉，例如用select,poll,epoll来管理一堆的fd，在这里，HTTP2虽然只有一条TCP连接，但是在逻辑上分成了很多stream。这个特性又很关键呐。先看下面这个网上借用的图 。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_3ad7240e-680d-4ef7-84c6-50e3228274ef.png" alt="企业微信截图_3ad7240e-680d-4ef7-84c6-50e3228274ef" style="zoom:40%;" />

以前的 HTTP/1怎么干的？在一条TCP连接上，多个请求只能串行执行！你说咱们现在的网络带宽这么大，这不浪费吗?HTTP/2就不一样了，不管多少请求，只要有，就往连接里面扔好了，这能明显降低一个页面加载的时间。

HTTP2多路复用怎么做到的？HTTP2把要传输的信息分割成一个个二进制帧，首部信息会被封装到Header Frame，相应的request body就放到Data Frame，这种做法有点像是“Chunked”分块编码的方式， HTTP/2 数据分帧后“Header+Body”的报文结构就完全消失了，协议看到的只是一个个的“碎片”。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_a4eca2a2-36e6-4482-8bdb-b2528253dc92.png" alt="企业微信截图_a4eca2a2-36e6-4482-8bdb-b2528253dc92" style="zoom:60%;" />



Http2Frame 类型

|               | type值 |                            |                                                              |
| :------------ | :----- | :------------------------- | :----------------------------------------------------------- |
| data          | 0x0    |                            |                                                              |
| header        | 0x1    |                            |                                                              |
| PRIORITY      | 0x2    |                            |                                                              |
| RST_STREAM    | 0x3    | 流结束帧，用于终止异常流   |                                                              |
| SETTINGS      | 0x4    | 连接配置参数帧             | 设置帧由两个终端在连接开始时发送，连接生存期的任意时间发送;设置帧的参数将替换参数中现有值;client和server都可以发送;设置帧总是应用于连接，而不是一个单独的流; |
| PUSH_PROMISE  | 0x5    | 推送承诺帧                 |                                                              |
| PRIORITY      | 0x6    | 检测连接是否可用           |                                                              |
| GOAWAY        | 0x7    | 通知对端不要在连接上建新流 |                                                              |
| WINDOW_UPDATE | 0x8    | 实现流量控制               |                                                              |
| CONTINUATION  | 0x9    |                            |                                                              |

我们可以将frame笼统的分为data frame和 header frame，每一种类型的payload都是有自己的结构。可以参考下 go http2 实现 [HTTP/2 in GO(三)](https://www.infoq.cn/article/iQMYEEVzIWZGqd2pG2UN)

消息的“碎片”到达目的地后应该怎么组装起来呢？HTTP/2 为此定义了一个“流”（Stream）的概念，它是二进制帧的双向传输序列，同一个消息往返的帧会分配一个唯一的流 ID。

因为**流是虚拟的，实际上并不存在（除了Frame 结构里有一个StreamId）**，所以 HTTP/2 就可以在一个 TCP 连接上用“流”同时发送多个“碎片化”的消息，这就是常说的“多路复用”（ Multiplexing）——多个往返通信都复用一个连接来处理。

下面是浏览器渲染一个页面需要一个css文件，一个js文件，n个图片文件

![企业微信截图_9e7567b7-dacb-4e63-b2e0-16241a742172](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_9e7567b7-dacb-4e63-b2e0-16241a742172.png)



这个思路其实和Dubbo是类似的。

更进一步，http2还能对这些流指定优先级，优先级能动态的被改变，例如把CSS和JavaScript文件设置得比图片的优先级要高，这样代码文件能更快的下载下来并得到执行。

### 3、头部压缩和二进制格式

HTTP/1一直都是plain text，对此我只能想到一个优点，便于阅读和debug。但是，现在很多都走HTTPS，SSL也把plain text变成了二进制，那这个优点也没了。

### 4、服务端推送Server Push

这个功能通常被称作“缓存推送”。主要的思想是：当一个客户端请求资源X，而服务器知道它很可能也需要资源Z的情况下，服务器可以在客户端发送请求前，主动将资源Z推送给客户端。

这个功能帮助客户端将Z放进缓存以备将来之需。服务器推送需要客户端显式的允许服务器提供该功能。但即使如此，客户端依然能自主选择是否需要中断该推送的流。如果不需要的话，客户端可以通过发送一个`RST_STREAM`帧来中止。



# 历史

###  概述

在讨论HTTP2.X 之前，先要搞清楚HTTP2.0的目的
```
HTTP/2 协议的主要目的是提高网页性能。
```
以及 HTTP1.0, HTTP1.1 ,SPDY，HTTPS之间的关系 参考：https://cloud.tencent.com/developer/article/1082516

### HTTP1.0

超文本传输协议 (HyperText Transfer Protocol) 伴随着计算机网络和浏览器的诞生，HTTP1.0 也随之而来，处于计算机网络中的应用层，HTTP 是建立在 TCP 协议之上，所以 HTTP 协议的瓶颈及其优化技巧都是基于 TCP 协议本身的特性，例如 tcp 建立连接的 3 次握手和断开连接的 4 次挥手以及每次建立连接带来的 RTT 延迟时间。


早在 HTTP 建立之初，主要就是为了将超文本标记语言 (HTML) 文档从 Web 服务器传送到客户端的浏览器。也是说对于前端来说，我们所写的 HTML 页面将要放在我们的 web 服务器上，用户端通过浏览器访问 url 地址来获取网页的显示内容，但是到了 WEB2.0 以来，我们的页面变得复杂，不仅仅单纯的是一些简单的文字和图片，同时我们的 HTML 页面有了 CSS，Javascript，来丰富我们的页面展示，当 ajax 的出现，我们又多了一种向服务器端获取数据的方法，这些其实都是基于 HTTP 协议的。同样到了移动互联网时代，我们页面可以跑在手机端浏览器里面，但是和 PC 相比，手机端的网络情况更加复杂，这使得我们开始了不得不对 HTTP 进行深入理解并不断优化过程中。


### HTTP 的基本优化

影响一个 HTTP 网络请求的因素主要有两个：**带宽**和**延迟**。

带宽：如果说我们还停留在拨号上网的阶段，带宽可能会成为一个比较严重影响请求的问题，但是现在网络基础建设已经使得带宽得到极大的提升，我们不再会担心由带宽而影响网速，那么就只剩下延迟了。

延迟：
- 浏览器阻塞（HOL blocking）：浏览器会因为一些原因阻塞请求。浏览器对于同一个域名，同时只能有 4 个连接（这个根据浏览器内核不同可能会有所差异），超过浏览器最大连接数限制，后续请求就会被阻塞。
- DNS 查询（DNS Lookup）：浏览器需要知道目标服务器的 IP 才能建立连接。将域名解析为 IP 的这个系统就是 DNS。这个通常可以利用 DNS 缓存结果来达到减少这个时间的目的。
- 建立连接（Initial connection）：HTTP 是基于 TCP 协议的，浏览器最快也要在第三次握手时才能捎带 HTTP 请求报文，达到真正的建立连接，但是这些连接无法复用会导致每次请求都经历三次握手和慢启动。三次握手在高延迟的场景下影响较明显，慢启动则对文件类大请求影响较大。

### HTTP1.1 

HTTP1.0 最早在网页中使用是在 1996 年，那个时候只是使用一些较为简单的网页上和网络请求上，而 HTTP1.1 则在 1999 年才开始广泛应用于现在的各大浏览器网络请求中，同时 HTTP1.1 也是当前使用最为广泛的 HTTP 协议。 主要区别主要体现在：

- 1.缓存处理，在 HTTP1.0 中主要使用 header 里的 If-Modified-Since,Expires 来做为缓存判断的标准，HTTP1.1 则引入了更多的缓存控制策略例如 Entity tag，If-Unmodified-Since, If-Match, If-None-Match 等更多可供选择的缓存头来控制缓存策略。
- 2.断点续传，HTTP1.0 中，存在一些浪费带宽的现象，例如客户端只是需要某个对象的一部分，而服务器却将整个对象送过来了，并且不支持断点续传功能，HTTP1.1 则在请求头引入了 range 头域，它允许只请求资源的某个部分，即返回码是 206（Partial Content），这样就方便了开发者自由的选择以便于充分利用带宽和连接。
- 3.长连接，HTTP 1.1 支持长连接（PersistentConnection）和请求的流水线（Pipelining）处理，在一个 TCP 连接上可以传送多个 HTTP 请求和响应，减少了建立和关闭连接的消耗和延迟，在 HTTP1.1 中默认开启 Connection： keep-alive，一定程度上弥补了 HTTP1.0 每次请求都要创建连接的缺点。

问题：
- 1.HTTP1.1在传输数据时，所有传输的内容都是明文，客户端和服务器端都无法验证对方的身份，这在一定程度上无法保证数据的安全性。
- 2.虽然HTTP1.1支持了keep-alive，来弥补多次创建连接产生的延迟，但是keep-alive使用多了同样会给服务端带来大量的性能压力，并且对于单个文件被不断请求的服务(例如图片存放网站)，keep-alive可能会极大的影响性能，因为它在文件被请求之后还保持了不必要的连接很长时间。

### HTTPS
1、HTTPS协议需要到CA申请证书，一般免费证书很少，需要交费。
2、HTTP协议运行在TCP之上，所有传输的内容都是明文，HTTPS运行在SSL/TLS之上，SSL/TLS运行在TCP之上，所有传输的内容都经过加密的。
3、HTTP和HTTPS使用的是完全不同的连接方式，用的端口也不一样，前者是80，后者是443。
4、HTTPS可以有效的防止运营商劫持，解决了防劫持的一个大问题。

### SPDY

2012年google如一声惊雷提出了SPDY的方案，大家才开始从正面看待和解决老版本HTTP协议本身的问题，SPDY可以说是综合了HTTPS和HTTP两者有点于一体的传输协议，主要解决：

1、降低延迟，针对HTTP高延迟的问题，SPDY优雅的采取了多路复用（multiplexing）。多路复用通过多个请求stream共享一个tcp连接的方式，解决了HOL blocking的问题，降低了延迟同时提高了带宽的利用率。

2、请求优先级（request prioritization）。多路复用带来一个新的问题是，在连接共享的基础之上有可能会导致关键请求被阻塞。SPDY允许给每个request设置优先级，这样重要的请求就会优先得到响应。比如浏览器加载首页，首页的html内容应该优先展示，之后才是各种静态资源文件，脚本文件等加载，这样可以保证用户能第一时间看到网页内容。

3、header压缩。前面提到HTTP1.x的header很多时候都是重复多余的。选择合适的压缩算法可以减小包的大小和数量。

4、基于HTTPS的加密协议传输，大大提高了传输数据的可靠性。

5、服务端推送（server push），采用了SPDY的网页，例如我的网页有一个sytle.css的请求，在客户端收到sytle.css数据的同时，服务端会将sytle.js的文件推送给客户端，当客户端再次尝试获取sytle.js时就可以直接从缓存中获取到，不用再发请求了。

SPDY位于HTTP之下，TCP和SSL之上，这样可以轻松兼容老版本的HTTP协议(将HTTP1.x的内容封装成一种新的frame格式)，同时可以使用已有的SSL功能。

### HTTP2.0

HTTP2.0 可以说是SPDY的升级版（其实原本也是基于SPDY设计的），但是，HTTP2.0 跟 SPDY 仍有不同的地方，主要是以下两点：
- HTTP2.0 支持明文 HTTP 传输，而 SPDY 强制使用 HTTPS （注：不过实际上HTTP2.0）
- HTTP2.0 消息头的压缩算法采用 HPACK，而非 SPDY 采用的 DEFLATE

### HTTP2.0的新特性
- 新的二进制格式（Binary Format），HTTP1.x的解析是基于文本。基于文本协议的格式解析存在天然缺陷，文本的表现形式有多样性，要做到健壮性考虑的场景必然很多，二进制则不同，只认0和1的组合。基于这种考虑HTTP2.0的协议解析决定采用二进制格式，实现方便且健壮。
- 多路复用（MultiPlexing），即连接共享，即每一个request都是是用作连接共享机制的。一个request对应一个id，这样一个连接上可以有多个request，每个连接的request可以随机的混杂在一起，接收方可以根据request的 id将request再归属到各自不同的服务端请求里面。
- header压缩，如上文中所言，对前面提到过HTTP1.x的header带有大量信息，而且每次都要重复发送，HTTP2.0使用encoder来减少需要传输的header大小，通讯双方各自cache一份header fields表，既避免了重复header的传输，又减小了需要传输的大小。
- 服务端推送（server push），同SPDY一样，HTTP2.0也具有server push功能。目前，有大多数网站已经启用HTTP2.0，例如YouTuBe，淘宝网等网站，利用chrome控制台可以查看是否启用H2：

更多关于HTTP2的问题可以参考：HTTP2奇妙日常（http://www.alloyteam.com/2015/03/http2-0-di-qi-miao-ri-chang/），以及HTTP2.0的官方网站。

### 总结
从上可以看到HTTP2.0主要用于外网优化网络的，那么HTTP2.0是否适合用于内网呢？网上也有人做对了对比测试

|测试组合	HTTP |without Keep-Alive|HTTP with Keep-Alive|HTTP+SSL|HTTP2|
| :------| :------| :------| :------| :------| 
|T100| O10|	1210|8850|3310|6320|
|T100| 0100|1115|7525|2225|3410|
|T100| 01000|950|2710|1150|1080|
|T200| 010|1050|8650+error|4200+error|6120|
|T200| 0100|1035|7250|330+error|3250|
|T200| 01000|870|2650|495|930|


结论是HTTP2+SSL 相比 HTTP1.1+SSL 有性能优势，但是针对 Spring cloud 内部调用场景我们并不会开启 HTTPS，因此是个废的，在此场景之下最佳选择是 HTTP1.1 + Keep-Alive。

