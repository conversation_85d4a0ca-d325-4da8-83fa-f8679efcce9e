## 背景&概念

HTTPS：在http(超文本传输协议)基础上提出的一种安全的http协议，因此可以称为安全的超文本传输协议。 http协议直接放置在TCP协议之上，而https提出在http和TCP中间加上一层加密层。从发送端看，这一层负责把http的内容加密后送到下层 的TCP，从接收方看，这一层负责将TCP送来的数据解密还原成http的内容。

SSL(Secure Socket Layer)：是Netscape公司设计的主要用于WEB的安全传输协议。从名字就可以看出它在https协议栈中负责实现上面提到的加密层。因此，一个https协议栈大致是这样的：

![01](/work/dist/branch/wacai/middleware/my-boot/doc/images/java/01.png)

### 数字证书

一种文件的名称，好比一个机构或人的签名，能够证明这个机构或人的真实性。其中包含的信息，用于实现上述功能。

### 加密和认证

加密是指通信双方为了防止铭感信息在信道上被第三方窃听而泄漏，将明文通过加密变成密文，如果第三方无法解密的话，就算他获得密文也无能为力；认证是指通信双方为了确认对方是值得信任的消息发送或接受方，而不是使用假身份的非法者，采取的确认身份的方式。只有同时进行了加密和认证才能保证通信的安全，因此在SSL通信协议中这两者都被应。早期一般是用对称加密算法，现在一般都是不对称加密，最常见的算法就是RSA。

### 消息摘要

这个技术主要是为了避免消息被篡改。消息摘要是把一段信息，通过某种算法，得出一串字符串。这个字符串就是消息的摘要。如果消息被篡改（发生了变化），那么摘要也一定会发生变化（如果2个不同的消息生成的摘要是一样的，那么这就叫发生了碰撞）。

消息摘要的算法主要有MD5和SHA，在证书领域，一般都是用SHA（安全哈希算法）。

数字证书、加密和认证、消息摘要三个技术结合起来，就是在HTTPS中广泛应用的证书（certificate），证书本身携带了加密/解密的信息，并且可以标识自己的身份，也自带消息摘要。

## HTTPS认证过程

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/java/02.png" alt="02" style="zoom:50%;" />

① 客户端(浏览器)发起https请求；

② 服务器将自己的证书，以及同证书相关的信息发送给客户浏览器。

③ 客户浏览器检查服务器送过来的证书是否是由自己信赖的 CA 中心所签发的。如果是，就继续执行协议；如果不是，客户浏览器就给客户一个警告消息：警告客户这个证书不是可以信赖的，询问客户是否需要继续。

④ 接着客户浏览器比较证书里的消息，例如域名和公钥，与服务器刚刚发送的相关消息是否一致，如果是一致的，客户浏览器认可这个服务器的合法身份。

⑤ 服务器要求客户发送客户自己的证书。收到后，服务器验证客户的证书，如果没有通过验证，拒绝连接；如果通过验证，服务器获得用户的公钥。

⑥ 客户浏览器告诉服务器自己所能够支持的通讯对称密码方案。

⑦ 服务器从客户发送过来的密码方案中，选择一种加密程度最高的密码方案，用客户的公钥加过密后通知浏览器。

⑧ 浏览器针对这个密码方案，选择一个通话密钥，接着用服务器的公钥加过密后发送给服务器。

⑨ 服务器接收到浏览器送过来的消息，用自己的私钥解密，获得通话密钥。

⑩ 服务器、浏览器接下来的通讯都是用对称密码方案，对称密钥是加过密的。

### 单向认证

Https在建立Socket连接之前，需要进行握手，具体过程如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/java/03.png" alt="03" style="zoom:40%;" />

①客户端向服务端发送SSL协议版本号、加密算法种类、随机数等信息。

②服务端给客户端返回SSL协议版本号、加密算法种类、随机数等信息，同时也返回服务器端的证书，即公钥证书

③客户端使用服务端返回的信息验证服务器的合法性，包括： 证书是否过期

```
    - 发型服务器证书的CA是否可靠
    - 返回的公钥是否能正确解开返回证书中的数字签名
    - 服务器证书上的域名是否和服务器的实际域名相匹配
    - 验证通过后，将继续进行通信，否则，终止通信
```

④客户端向服务端发送自己所能支持的对称加密方案，供服务器端进行选择

⑤服务器端在客户端提供的加密方案中选择加密程度最高的加密方式。

⑥服务器将选择好的加密方案通过明文方式返回给客户端

⑦客户端接收到服务端返回的加密方式后，使用该加密方式生成产生随机码，用作通信过程中对称加密的密钥，使用服务端返回的公钥进行加密，将加密后的随机码发送至服务器

⑧服务器收到客户端返回的加密信息后，使用自己的私钥进行解密，获取对称加密密钥。 在接下来的会话中，服务器和客户端将会使用该密码进行对称加密，保证通信过程中信息的安全。

### 双向认证

双向认证和单向认证原理基本差不多，只是除了客户端需要认证服务端以外，增加了服务端对客户端的认证，具体过程如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/java/04.png" alt="04" style="zoom:50%;" />

① 客户端向服务端发送SSL协议版本号、加密算法种类、随机数等信息。

② 服务端给客户端返回SSL协议版本号、加密算法种类、随机数等信息，同时也返回服务器端的证书，即公钥证书

③ 客户端使用服务端返回的信息验证服务器的合法性，包括：

```
- 证书是否过期
- 发型服务器证书的CA是否可靠
- 返回的公钥是否能正确解开返回证书中的数字签名
- 服务器证书上的域名是否和服务器的实际域名相匹配
验证通过后，将继续进行通信，否则，终止通信
```

④ 服务端要求客户端发送客户端的证书，客户端会将自己的证书发送至服务端

⑤ 验证客户端的证书，通过验证后，会获得客户端的公钥

⑥ 客户端向服务端发送自己所能支持的对称加密方案，供服务器端进行选择

⑦ 服务器端在客户端提供的加密方案中选择加密程度最高的加密方式

⑧ 将加密方案通过使用之前获取到的公钥进行加密，返回给客户端

⑨ 客户端收到服务端返回的加密方案密文后，使用自己的私钥进行解密，获取具体加密方式，而后，产生该加密方式的随机码，用作加密过程中的密钥，使用之前从服务端证书中获取到的公钥进行加密后，发送给服务端

⑩ 服务端收到客户端发送的消息后，使用自己的私钥进行解密，获取对称加密的密钥，在接下来的会话中，服务器和客户端将会使用该密码进行对称加密，保证通信过程中信息的安全。