## 概述

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211216191148854.png" alt="image-20211216191148854" style="zoom:50%;" />

我是一个电商应用，采用微服务架构，业务拆分为order, rating, payment三个应用，另外还有一个boot应用作为入口流量，我在boot应用中如何使用feign调用order应用中的OrderService。

```java
@FeignClient(name = "order-service")
public interface OrderServiceClient {
    @GetMapping("/orders/{orderId}")
    OrderDTO getOrder(@PathVariable("orderId") Long orderId);
}

```

如果使用 Nacos 注册中心，配置如下：

```yaml
spring.cloud.nacos.discovery.server-addr: 10.10.20.22:8848
```

如果不使用Nacos，依赖k8s：

```yaml
# 使用 Feign 客户端调用 order-service，通过 K8s DNS 地址
feign.client.config.order-service.url=http://order-service.default.svc.cluster.local:8080
```

使用 Nacos vs 不使用 Nacos（或其他注册中心）的区别总结：

| 特性                             | 使用 Nacos 注册中心                          | 不使用注册中心（Kubernetes DNS 直连）                       |
| -------------------------------- | -------------------------------------------- | ----------------------------------------------------------- |
| Feign 调用是否需要显式配置 `url` | ❌ 不需要，Feign 会自动从注册中心获取服务地址 | ✅ 需要手动配置 `url`（例如通过 Kubernetes DNS）             |
| 服务发现方式                     | 服务自动注册到 Nacos，由 Feign/Ribbon 发现   | 依赖 K8s DNS，通过 `ServiceName.namespace.svc`              |
| 服务实例负载均衡                 | ✅ 支持（Ribbon、LoadBalancer）               | ✅ 也支持，只要 Service 是 ClusterIP 或负载均衡              |
| 动态扩缩容感知                   | ✅ 感知服务实例变更                           | ✅ K8s Service 本身会做负载均衡                              |
| 跨命名空间调用是否更复杂         | ❌ 简单，只需服务名                           | ⚠️ 需要明确写出 DNS，如 `svc.namespace.svc`                  |
| 配置管理、服务治理能力           | ✅ 可配合 Nacos Config 做统一配置、限流等     | ❌ 没有这些能力，除非额外使用 Istio、Spring Cloud Gateway 等 |



Nacos 2.X 在 1.X 的架构基础上 新增了对长连接模型的支持，同时保留对旧客户端和 openAPI 的核心功能支持。

## 安装

Docker 安装

```shell
docker pull zhusaidong/nacos-server-m1:2.0.3
docker run --name nacos-standalone -e MODE=standalone -e JVM_XMS=512m -e JVM_XMX=512m -e JVM_XMN=256m -p 8848:8848 -d zhusaidong/nacos-server-m1:2.0.3
```

### IDE中启动

EnvUtil 中通过 nacos.home 获取nacos的目录，默认是~/nacos，在nocos/config目录下新建cluster.conf

```
192.168.16.101:8847
172.16.49.70:8847
```



## naming 服务端

naming 服务端接收来自 sdk 的请求，作为源码阅读下手的入口。

### 类概述

- ServiceController   服务API。
- InstanceController  实例的API

- ServiceOperator  Service的CRUD
- InstanceOperator Instance的CRUD

### ServiceOperator 
有两个实现类对应了v1和v2的实现，方法参数包括：
- namespaceId 客户端可以传，不传默认为public
- serviceName 服务名
- ServiceMetadata 一些meta信息，主要是map

ServiceOperatorV2Impl 实现依赖 NamingMetadataOperateService, NamingMetadataOperateService 使用 CPProtocol 持久化服务数据，方式是异步写入 operationLog。


### InstanceOperator
InstanceOperatorClientImpl 对了V2的实现。

服务注册的实现如下：

```java
public void registerInstance(String namespaceId, String serviceName, Instance instance) {
    boolean ephemeral = instance.isEphemeral();
    String clientId = IpPortBasedClient.getClientId(instance.toInetAddr(), ephemeral);//获取clientId
    createIpPortClientIfAbsent(clientId);//clientManager 保存 client
    Service service = getService(namespaceId, serviceName, ephemeral);//创建Service
    clientOperationService.registerInstance(service, instance, clientId);//注册instance
}

```
### ClientOperationService

保存 Client 的服务类，提供了注册下线实例的操作。实现方式只是简单的发送消息：

```java
 
 @Override
 public void registerInstance(Service service, Instance instance, String clientId) {
	 NotifyCenter.publishEvent(new ClientOperationEvent.ClientRegisterServiceEvent(singleton, clientId));
     NotifyCenter.publishEvent(new MetadataEvent.InstanceMetadataEvent(singleton, instanceInfo.getMetadataId(), false));
}
```

DistroClientDataProcessor 会监听到这个event。

这里不展开，详见Distro分析

## member 管理

nacos 本身作为一个集群必然要对集群成员进行管理。这部分逻辑在 nacos-core 模块下的cluster包。

ServerMemberManager 是这部分的入口，它会使用 LookupFactory 进行寻址，有三种寻址方式

- 单机模式：StandaloneMemberLookup。
- 文件模式：FileConfigMemberLookup -- 利用监控cluster.conf文件的变动实现节点的管理。
- 服务器模式：AddressServerMemberLookup – 使用额外的地址服务器存储节点信息，服务端节点定时拉取信息进行管理

流程如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211216195057129.png" alt="image-20211216195057129" style="zoom:50%;" />



这里说下 AddressServerMemberLookup。该寻址模式是基于一个额外的web服务器来管理cluster.conf，每个节点定期向该web服务器请求cluster.conf的文件内容，然后实现集群节点间的寻址，以及扩缩容。
当需要进行集群扩缩容时，只需要修改cluster.conf文件即可，然后每个节点向地址服务器请求时会自动的得到最新的cluster.conf文件内容。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211216195326156.png" alt="image-20211216195326156" style="zoom:50%;" />



[Nacos 1.3.0 特性以及功能使用文档](https://nacos.io/zh-cn/blog/nacos-1.3.0-design.html)对此有介绍。



## distro 协议
Distro 协议保证写必须永远是成功的，即使可能会发生网络分区。当网络恢复时，把各数据分片的数据进行合并。

Distro 协议具有以下特点：

- 专门为了注册中心而创造出的协议；
- 客户端与服务端有两个重要的交互，服务注册与心跳发送；
- 客户端以服务为维度向服务端注册，注册后每隔一段时间向服务端发送一次心跳，**心跳包需要带上注册服务的全部信息**，在客户端看来，服务端节点对等，所以请求的节点是随机的；
- 客户端请求失败则换一个节点重新发送请求；
- 服务端节点都存储所有数据，但每个节点只负责其中一部分服务，在接收到客户端的“写”（注册、心跳、下线等）请求后，服务端节点判断请求的服务是否为自己负责，如果是，则处理，否则交由负责的节点处理；
- 每个服务端节点主动发送健康检查到其他节点，响应的节点被该节点视为健康节点；
- 服务端在接收到客户端的服务心跳后，如果该服务不存在，则将该心跳请求当做注册请求来处理；
- 服务端如果长时间未收到客户端心跳，则下线该服务；
- 负责的节点在接收到服务注册、服务心跳等写请求后将数据写入后即返回，后台异步地将数据同步给其他节点；
- 节点在收到读请求后直接从本机获取后返回，无论数据是否为最新。

### 类概述

distro 在 nacos-core 模块的distro包下 

- DistroClientComponentRegistry 初始化类，负责初始化 DistroClientDataProcessor，DistroClientTransportAgent
- DistroProtocol 协议
- DistroClientDataProcessor 集群节点数据同步的处理类

### DistroProtocol

DistroProtocol 被初始化时会启动 DistroLoadDataTask 用于从member 其他节点同步全量数据，在Nacos中，整体流程如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211216211539690.png" alt="image-20211216211539690" style="zoom:50%;" />

- 调用loadAllDataSnapshotFromRemote()方法从远程机器同步所有的数据快照
- 处理数据processData
- 监听器listener执行成功后，就更新data store

从远程机器获取数据快照主要依赖 DistroTransportAgent，DistroTransportAgent 内部使用 grpc 获取集群内所有其他成员的数据，处理数据依赖 DistroClientDataProcessor，处理过程就是把数据回放一遍，并不会校验数据是否一致。

### DistroClientComponentRegistry

主要关注 doRegister()方法，负责注册 distro 协议的相关组件

```java
    @PostConstruct
    public void doRegister() {
    	//数据处理功能
        DistroClientDataProcessor dataProcessor = new DistroClientDataProcessor(clientManager, distroProtocol,
                upgradeJudgement);
        DistroTransportAgent transportAgent = new DistroClientTransportAgent(clusterRpcClientProxy,
                serverMemberManager);
        DistroClientTaskFailedHandler taskFailedHandler = new DistroClientTaskFailedHandler(taskEngineHolder);
        componentHolder.registerDataStorage(DistroClientDataProcessor.TYPE, dataProcessor);
        componentHolder.registerDataProcessor(dataProcessor);
        componentHolder.registerTransportAgent(DistroClientDataProcessor.TYPE, transportAgent);
        componentHolder.registerFailedTaskHandler(DistroClientDataProcessor.TYPE, taskFailedHandler);
    }
```



## cp 协议

### 类概述

- ConsistencyProtocol 协议抽象接口，定义了一系列抽象方法。
- CPProtocol CP协议，默认实现类是 JRaftProtocol
- PersistentServiceProcessor 持久化处理器，复制初始化JRaftProtocol，等待选主完成

## 踩坑

- mac启动报错：dyld: lazy symbol binding failed: Symbol not found: ____chkstk_darwin 。原因是nacos依赖的jraft会引入rocksdb，mac存在兼容性问题。

## 参考

- [支持 gRPC 长链接，深度解读 Nacos 2.0 架构设计及新模型](https://developer.aliyun.com/article/780680)

- [一致性算法 - Distro协议在Nacos的实践](https://cloud.tencent.com/developer/article/1697338)

- [2019-Nacos 注册中心的设计原理详解](https://www.infoq.cn/article/b*6vymikao9vakisjype)

    

