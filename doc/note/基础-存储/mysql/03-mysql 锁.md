##  概述

锁的分类可以按照以下维度

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211226205534837.png" alt="image-20211226205534837" style="zoom:50%;" />

InnoDB行锁类型

- 共享锁(S-Lock)。允许多个事务对于同一数据可以共享一把锁，都能访问到数据，阻止其它事务对于同一数据获取`排它锁`。

- 排它锁(X-Lock)。允许事务删除或者更新一行数据，阻止其它事务对于同一数据获取其它锁，包括`共享锁`和`排它锁`。
- 需要注意select 语句默认不获取任何锁，所以是可以读被其它事务持有排它锁的数据的！

事务隔离级别

锁机制又和事务隔离级别有关，主要有两种

- Read Committed (RC) 读提交，也称为不可重复读。RC存在幻读的现象。
- Repeatable Read (RR) 可重复读。RR隔离级别保证对读取到的记录加锁，同时保证对读取的范围加锁，新满足查询条件的记录不能够被插入(间隙锁)，不存在幻读现象。

由于RC级别加锁比较少，在**挖财生产环境中设置的是RC级别**，Mysql默认为RR。

## 一致性非锁定读

RC和RR会主要影响mysql 的非锁定一致性读(consistent nonlocking read)

一致性非锁定读是什么？这里我先给出一个最最最简单的解释：**一致性非锁定读就是读快照**！

**快照即当前行数据之前的历史版本，每行记录可能存在多个历史版本**，或者说每行记录可能有不止一个快照数据，一般我们将这种技术称为 **行多版本技术**。而由于一个行记录可能对应着多个快照（历史版本），为此不可避免地会带来一系列的并发问题，如何解决这些并发问题，就是所谓的 **多版本并发控制（MVCC）**，当然，这不是本文的重点。

RC和RR它俩对于快照数据的定义也各不相同：

- 在 RC 事务隔离级别下，总是读取行的最新版本；**如果行被锁定了，非一致性读不会因此去等待行上锁的释放，而是去读取该行版本的最新一个快照**
- 在RR 事务隔离级别下，对于快照数据，非一致性读总是读取事务开始时的行数据版本

下面通过一个例子说明下RC和RR的区别：

在会话 A 中执行如下 SQL 语句：

```sql
BEGIN
SELECT * FROM PARENT WHERE id=1
+----+
| id |
+----+
|  1 |
+----+
```

会话 A 中通过显式地执行命令 BEGIN 开启一个事务，并读取表parent 中id为1的数据，但事务并没有结束。与此同时，用户再开启另外一个会话 B ，操作如下：

```sql
BEGIN
UPDATE PARENT SET id=3 WHERE id=1;
```

在会话B中将事务表parent中ID为1修改为id=3，同样也没有提交事务，这样id为=1的行其实加了一个行锁。*为啥需要加行锁呢？因为需要保证在会话B中查询是修改后的记录，会话A查询还是之前的记录，这也是为什么需要多版本和MVCC了，因为存在多条事务同时修改同一条记录，每条事务都需要保存自己的修改记录。*

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211226215329238.png" alt="image-20211226215329238" style="zoom:50%;" />

当我们在会话B中提交事务：

```mysql
commit
```

这时在会话A中再运行SELECT * FROM PARENT WHERE id=1的SQL语句，在RC和RR事务隔离级别下得到结果就不一样了。

对于RC，它总是读取行的最新版本，如果行被锁定了，则读取该行版本的最新一个快照。因为会话B已经提交了事务，所以RC隔离级别下会话 A 得到如下结果:

```sql
mysql> SELECT * FROM PARENT WHERE id=1
Empty set (0.00 sec)
```

对于RR，总是读取事务开始时的行数据，因此会话 A 得到的结果如下：

```
BEGIN
SELECT * FROM PARENT WHERE id=1
+----+
| id |
+----+
|  1 |
+----+
```

 从数据库理论的角度来看，RC事务隔离级别违反了事务ACID中的I，即隔离性。

## 一致性锁定读

其实从名字上也能看出来，非一致性锁定读适用于对数据一致性要求不是很高的情况，比如在 RC 隔离级别下，即使行被锁定了，非一致性读也可以读到该行版本的最新一个快照。非锁定读机制极大地提高了数据库的并发性。

而一致性锁定读适用于对数据一致性要求比较高的情况，这个时候我们需要对读操作进行加锁以保证数据逻辑的一致性。

nnoDB 存储引擎对读操作支持两种一致性锁定读方式，或者说对读操作支持两种加锁方式：

- `SELECT ... FOR UPDATE`，对于读取的行记录加一个 X 排它锁，其他事务不能对锁定的行加任何锁；
- `SELECT ... LOCK IN SHARE MODE`，对于读取的行记录添加一个 S 共享锁。其它事务可以向被锁定的行加 S 锁，但是不允许添加 X 锁，否则会被阻塞住；

两者的区别就是 SELECT ... FOR UPDATE 不允许同时执行，SELECT ... LOCK IN SHARE MODE允许。



### SELECT FOR UPDATE

SELECT FOR UPDATE 在日常中会比较常见一点。

在会话A中执行如下sql

```sql
BEGIN
select app, ukey, value, creator, createTime from universal_id where app='test_app' and ukey='test for update

```

在会话B中执行 update universal_id 或select for update 会等待会话A提交之后再执行，如果会话A一直不提交，会话B会等待一段时间之后超时失败。

验证

| 会话A                                       | 会话B                                              |
| :------------------------------------------ | :------------------------------------------------- |
| begin;                                      |                                                    |
| SELECT * FROM user WHERE id = 1 FOR UPDATE; |                                                    |
|                                             | UPDATE user SET name = ‘test’ WHERE id = 2; – 成功 |
|                                             | UPDATE user SET name = ‘test’ WHERE id = 1; – 等待 |
| commit;                                     |                                                    |
|                                             | 执行等待的任务，成功                               |



需要注意**加锁必须在事务中执行**，否则 auto commit SQL执行完就自动释放锁就没有意义了，比如分布式自增ID正确完整的用法是：

```sql
BEGIN
select app, ukey, value, creator, createTime from universal_id where app='test_app' and ukey='test for update 
UPDATE  universal_id set value=value+1000 where where app='test_app' and ukey='test
COMMIT
```



### SELECT LOCK IN SHARE MODE

语法:

```
 SELECT * FROM hero WHERE number = 8 LOCK IN SHARE MODE;
```

select lock in share mode 的作用就是将查找到的数据加上一个 share 锁，这个就是表示其他的事务只能对这些数据进行简单的select 操作，并不能够进行 DML 操作。

## RR和RC的区别

mysql加锁策略在不同的事务隔离级别下是不一样的，mysql现在默认的事务隔离级别是 RR，但是在实践中都会改为 RC，两者加锁区别是什么？为什么会改为 RC ?

## 参考

- [一致性非锁定读与一致性锁定读 ](https://www.cnblogs.com/cswiki/p/15307265.html)
- [MySQL for update 详解](https://segon.cn/mysql-for-update.html)
- [RR和 RC的区别](https://developer.aliyun.com/article/801013)

