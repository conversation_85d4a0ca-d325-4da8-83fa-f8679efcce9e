## 概述

Mysql 表中的数据是如何存储的，即数据在表中是如何组织和存放的？本章将探讨这一话题。

### 表

在InnoDB存储引擎中，表都是根据主键顺序组织存放的，这种存储方式的表称为索引组织表(index organized table IOT)。在InnoDB存储引擎中，每张表都有个主键(Primary key)，如果创建表时没有显示定义主键，则 InnoDB 存储引擎会选择表中符合条件的列去创建主键：

- 首先判断表中是否有非空的唯一索引（Unique NOT NULL），如果有，则该列即为主键。
- 如果不符合上述条件，InnoDB存储引擎自动创建一个6字节大小的指针。
- 当表中存在多个非空的唯一索引的时候，InnoDB存储引擎会根据建表时所创建的第一个非空唯一索引作为主键。

通过以上操作 Mysql 保证了每个表都有一个主键，因此对于Mysql中的表数据来说**数据即索引，索引即数据**。

我们又把这个主键成为聚集索引，叶子节点存的是整行数据，直接通过这个聚集索引的键值找到某行。同时数据的物理存放顺序与索引顺序是一致的，即：只要索引是相邻的，那么对应的数据一定也是相邻地存放在磁盘上的。

### 逻辑存储结构

InnoDB存储引擎的逻辑存储结构所有的数据都被逻辑存放在一个空间中，称为表空间(tablespace)。表空间又由段(segment)，区(extent)，页(page)组成。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_e1946889-9389-46b0-becc-604fc98e5b10.png" alt="企业微信截图_e1946889-9389-46b0-becc-604fc98e5b10" style="zoom:50%;" />

### 表空间

表空间可以看做是InnoDB存储引擎逻辑结构的**最高层** ，所有的数据都是存放在表空间中。



## mysql如何保证数据不丢失

在开始之前，你需要对InnoDB的事务系统有个基本的认识。如果您不了解，可以参考我之前的几篇关于InnoDB的文章，包括InnoDB的[事务子系统](http://mysql.taobao.org/monthly/2015/12/01/)，[事务锁](http://mysql.taobao.org/monthly/2016/01/01/)，[redo log](http://mysql.taobao.org/monthly/2015/05/01/)，[undo log](http://mysql.taobao.org/monthly/2015/04/01/)，以及[崩溃恢复逻辑](http://mysql.taobao.org/monthly/2015/06/01/)。在这里我们简单的概述一下几个基本的概念：

**事务ID**：一个自增的序列号，每次开启一个读写事务（或者事务从只读转换成读写模式）时分配并递增，每更新256次后持久化到Ibdata的事务系统页中。每个读写事务都必须保证拥有的ID是唯一的。

**Read View**: 用于一致性读的snapshot，InnoDB里称为视图；在需要一致性读时开启一个视图，记录当时的事务状态快照，包括当时活跃的事务ID以及事务ID的上下水位值，以此用于判断数据的可见性。

**Redo Log**：用于记录对物理文件的修改，所有对InnoDB物理文件的修改都需要通过Redo保护起来，这样才能从崩溃中恢复。

**Mini Transaction(mtr)**：是InnoDB中修改物理块的最小原子操作单位，同时也负责生产本地的redo日志，并在提交mtr时将redo日志拷贝到全局log buffer中。

**LSN**: 一个一直在递增的日志序列号，它是一个不断递增的 unsigned long long 类型整数，代表了从日志记录创建开始到特定的日志记录已经写入的字节数。可以通过LSN计算出其在日志文件中的位置。每个block在写盘时，其最近一次修改的LSN也会记入其中，这样在崩溃恢复时，无需Apply该LSN之前的日志。

**Undo Log**: 用于存储记录被修改之前的旧版本，如果被多次修改，则会产生一个版本链。保留旧版本的目的是用于可重复读。通过结合Undo和视图控制来实现InnoDB的MVCC。

**Binary Log**: 构建在存储引擎之上的统一的日志格式；有两种存储方式，一种是记录执行的SQL，另外一种是记录修改的行记录。Binlog本质上是一种逻辑日志，因此能够适用所有的存储引擎，并进行数据复制。

## 概述

InnoDB 有两块非常重要的日志，一个是undo log，另外一个是redo log，前者用来保证事务的原子性以及InnoDB的MVCC，后者用来保证事务的持久性。

和大多数关系型数据库一样，InnoDB记录了对数据文件的物理更改，并保证总是日志先行，也就是所谓的WAL，即在持久化数据文件前，保证之前的redo日志已经写到磁盘。

为了管理脏页，在 Buffer Pool 的每个instance上都维持了一个flush list，flush list 上的 page 按照修改这些 page 的LSN号进行排序。因此定期做redo checkpoint点时，选择的 LSN 总是所有 bp instance 的 flush list 上最老的那个page（拥有最小的LSN）。由于采用WAL的策略，每次事务提交时需要持久化 redo log 才能保证事务不丢。而延迟刷脏页则起到了合并多次修改的效果，避免频繁写数据文件造成的性能问题。

## Redo 写盘操作

有几种场景可能会触发redo log写文件：

1. Redo log buffer空间不足时
2. 事务提交
3. 后台线程
4. 做checkpoint
5. 实例shutdown时
6. binlog切换时

我们所熟悉的参数`innodb_flush_log_at_trx_commit` 作用于事务提交时，这也是最常见的场景：

- 当设置为0时，事务提交不会触发redo写操作，而是留给后台线程每秒一次的刷盘操作，因此实例crash将最多丢失1秒钟内的事务。
- 当设置该值为1时，每次事务提交都要做一次fsync，这是最安全的配置，即使宕机也不会丢失事务；
- 当设置为2时，则在事务提交时只做write操作，只保证写到系统的page cache，因此实例crash不会丢失事务，但宕机则可能丢失事务；

显然对性能的影响是随着持久化程度的增加而增加的。通常我们建议在日常场景将该值设置为1，但在系统高峰期临时修改成2以应对大负载。

由于各个事务可以交叉的将事务日志拷贝到log buffer中，因而一次事务提交触发的写redo到文件，可能隐式的帮别的线程“顺便”也写了redo log，从而达到group commit的效果。

## BinLog 写入

和redoLog 类似，写binlog日志时，也有个参数 sync_binlog 来控制何时将binlog fsync到磁盘。

参数为0时，并不是立即fsync文件到磁盘，而是依赖于操作系统的fsync机制；
参数为1时，立即fsync文件到磁盘；
参数大于1时，则达到指定提交次数后，统一fsync到磁盘。 因此只有当sync_binlog参数为1时，才是最安全的，当其不为1时，都存在binlog未fsync到磁盘的风险，若此时发生断电等故障，就有可能出现此事务并未刷出到磁盘，从而故障恢复时将此事务回滚的情况。

## MySQL的事务处理—两阶段事务提交2PC

MySQL采用了两阶段事务提交(Two-Phase Commit Protocol)协议，当操作完成后，首先Prepare事务，在binlog中实际上只是fake一下，不作任何事情，而是innodb层需要将prepare写入redolog中；然后执行commit事务，首先在binlog文件中写入这些操作的binlog日志，完成后再在Innodb的redolog中写入commit日志。

![pic](http://mysql.taobao.org/monthly/pic/201812/201812-01.png)

## Redo checkpoint

InnoDB的redo log采用覆盖循环写的方式，而不是拥有无限的redo空间；即使拥有理论上极大的redo log空间，为了从崩溃中快速恢复，及时做checkpoint也是非常有必要的。

## InnoDB shutdown

实例关闭分为两种，一种是正常shutdown（非fast shutdown），实例重启时无需apply日志，另外一种是异常shutdown，包括实例crash以及fast shutdown。

当正常shutdown实例时，会将所有的脏页都刷到磁盘，并做一次完全同步的checkpoint；同时将最后的lsn写到系统表ibdata的第一个page中（函数`fil_write_flushed_lsn`）。在重启时，可以根据该lsn来判断这是不是一次正常的shutdown，如果不是就需要去做崩溃恢复逻辑。

参阅函数`logs_empty_and_mark_files_at_shutdown`。

关于异常重启的逻辑，由于崩溃恢复涉及到的模块众多，逻辑复杂，我们将在下期月报单独进行描述。

## redolog 文件结构

innodb存储引擎中，redo log以块为单位进行存储的，每个块占512字节，这称为redo log block。所以不管是log buffer中还是os buffer中以及redo log file on disk中，都是这样以512字节的块存储的。

为什么是512字节呢？一种说法说是因为磁盘扇区大小也是512字节，写入可以保证原子性，不需要doublewrite技术。另外一种说法是由于是使用512字节block对齐写入文件，可以很方便的根据全局维护的LSN号计算出要写入到哪一个文件以及对应的偏移量。

每个redo log block由3部分组成：**日志块头、日志块尾和日志主体**。其中日志块头占用12字节，日志块尾占用8字节，所以每个redo log block的日志主体部分只有512-12-8=492字节。

日志块头包含4部分：

-  log_block_hdr_no：(4字节)该日志块在redo log buffer中的位置ID。
-  log_block_hdr_data_len：(2字节)该log block中已记录的log大小。写满该log block时为0x200，表示512字节。
-  log_block_first_rec_group：(2字节)该log block中第一个log的开始偏移位置。
-  lock_block_checkpoint_no：(4字节)写入检查点信息的位置。

关于log block块头的第三部分 log_block_first_rec_group ，因为有时候一个数据页产生的日志量超出了一个日志块，这是需要用多个日志块来记录该页的相关日志。例如，某一数据页产生了552字节的日志量，那么需要占用两个日志块，第一个日志块占用492字节，第二个日志块需要占用60个字节，那么对于第二个日志块来说，它的第一个log的开始位置就是73字节(60+12)。如果该部分的值和 log_block_hdr_data_len 相等，则说明该log block中没有新开始的日志块，即表示该日志块用来延续前一个日志块。

日志尾只有一个部分： log_block_trl_no ，该值和块头的 log_block_hdr_no 相等。

上面所说的是一个日志块的内容，在redo log buffer或者redo log file on disk中，由很多log block组成。如下图：

![img](https://images2018.cnblogs.com/blog/733013/201805/733013-20180508182756285-1761418702.png)



## 参考

- https://juejin.im/entry/5dd5fee1f265da478f20696d
- https://juejin.im/entry/5ba0a254e51d450e735e4a1f
- http://mysql.taobao.org/monthly/2018/12/04/
- https://www.dazhuanlan.com/2019/08/28/5d65fdb59acf0/
- https://www.cnblogs.com/f-ck-need-u/p/9010872.html#auto_id_16 
- https://www.jianshu.com/p/6e9042de03c3
- [MySQL技术内幕 InnoDB存储引擎第四章](https://book.douban.com/subject/24708143/)



