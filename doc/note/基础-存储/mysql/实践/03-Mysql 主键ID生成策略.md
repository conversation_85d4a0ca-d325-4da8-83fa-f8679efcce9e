## 前言

在mysql中设计表的时候，不同的主键生成策略效率之间差异有多少呢？我们来对比下。

## UUID

 MySQL官方有明确的建议主键要尽量越短越好[4]，36个字符长度的UUID不符合要求。

UUID的无序性可能会引起数据位置频繁变动，严重影响性能。

UUID问题分析

1、使用自增id的内部结构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/存储/04.png" alt="04" style="zoom:50%;" />

自增的主键的值是顺序的，所以Innodb把每一条记录都存储在一条记录的后面。当达到页面的最大填充因子时候：

- 下一条记录就会写入新的页中，一旦数据按照这种顺序的方式加载，主键页就会近乎于顺序的记录填满，提升了页面的最大填充率，不会有页的浪费。
- 新插入的行一定会在原有的最大数据行下一行,mysql定位和寻址很快，不会为计算新行的位置而做出额外的消耗。
- 减少了页分裂和碎片的产生。

2、使用uuid的索引内部结构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/存储/05.png" alt="05" style="zoom:50%;" />

因为uuid相对顺序的自增id来说是毫无规律可言的,新行的值不一定要比之前的主键的值要大,所以innodb无法做到总是把新行插入到索引的最后,而是需要为新行寻找新的合适的位置从而来分配新的空间。

这个过程需要做很多额外的操作，数据的毫无顺序会导致数据分布散乱，将会导致以下的问题：

- 写入的目标页很可能已经刷新到磁盘上并且从缓存上移除，或者还没有被加载到缓存中，innodb在插入之前不得不先找到并从磁盘读取目标页到内存中，这将导致大量的随机IO
- 因为写入是乱序的,innodb不得不频繁的做页分裂操作,以便为新的行分配空间,页分裂导致移动大量的数据，一次插入最少需要修改三个页以上
- 由于频繁的页分裂，页会变得稀疏并被不规则的填充，最终会导致数据会有碎片

在把随机值载入到聚簇索引(innodb默认的索引类型)以后,有时候会需要做一次OPTIMEIZE TABLE来重建表并优化页的填充，这将又需要一定的时间消耗。

结论：使用innodb应该尽可能的按主键的自增顺序插入，并且尽可能使用单调的增加的聚簇键的值来插入新行

## snowflake方案

雪花算法，它是Twitter开源的由64位整数组成分布式ID，性能较高，并且在单机上递增。这种方案大致来说是一种以划分命名空间来生成ID的一种算法，这种方案把64-bit分别划分成多段，分开来标示机器、时间等，比如在snowflake中的64-bit分别表示如下图（图片来自网络）所示：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/存储/06.png" alt="06" style="zoom:50%;" />

41-bit的时间可以表示（1L<<41）/(1000L*3600*24*365)=69年的时间，10-bit机器可以分别表示1024台机器。如果我们对IDC划分有需求，还可以将10-bit分5-bit给IDC，分5-bit给工作机器。这样就可以表示32个IDC，每个IDC下可以有32台机器，可以根据自身需求定义。12个自增序列号可以表示2^12个ID，理论上snowflake方案的QPS约为409.6w/s，这种分配方式可以保证在任何一个IDC的任何一台机器在任意毫秒内生成的ID都是不同的。

这种方式的优缺点是：

优点：

- 毫秒数在高位，自增序列在低位，整个ID都是趋势递增的。
- 不依赖数据库等第三方系统，以服务的方式部署，稳定性更高，生成ID的性能也是非常高的。
- 可以根据自身业务特性分配bit位，非常灵活。

缺点：

- 强依赖机器时钟，如果机器上时钟回拨，会导致发号重复或者服务会处于不可用状态。

Mongdb objectID可以算作是和snowflake类似方法，通过“时间+机器码+pid+inc”共12个字节，通过4+3+2+3的方式最终标识成一个24长度的十六进制字符。

### UidGenerator

UidGenerator是百度开源的分布式ID生成器，其基于雪花算法实现。 

### 天猫 fun idGenerator

fun idGenerator能满足上面提到的所有需求：

- 全局唯一性
- 64位Long类型数字存储
- 按时间趋势递增，能精确到ms
- 无中心生成，不存在单点故障
- 满足高并发高性能
- 高性能指的是id生成效率高，鹰眼上rt <= 0.02ms，批量一次性生成1000个id的rt大概为1~2ms
- 实际通过PAP平台压测表明单机至少能达到5w qps，由于PAP本身压测极限是16w，分布式压测在PAP下只能压到16w，此时系统毫无压力，为：cpu 7%，load 0.4，从单机至少5w qps，线上一共63台机器进行预估来看，实际qps极限预计能达到100w
- 使用简单，不依赖于任何其它三方或二方库或平台，迁移方便

除了上面这些基本的特性外，我们的id生成器还提供一些额外的能力：

- id可带业务属性，通过一个id你可以知道这个id的生成时间、这个id是哪个模块或实体的id（比如是点赞id还是评论id）
- 可按需动态定制业务能力，在系统允许的情况下可以通过配置定制id在1ms内可以达到的最高并发能力

## 数据库生成方案

以MySQL举例，利用给字段设置`auto_increment_increment`和`auto_increment_offset`来保证ID自增，每次业务使用下列SQL读写MySQL得到ID号。

```sql
begin;
REPLACE INTO Tickets64 (stub) VALUES ('a');
SELECT LAST_INSERT_ID();
commit;
```

这种方案的优缺点如下：

优点：

- 非常简单，利用现有数据库系统的功能实现，成本小，有DBA专业维护。
- ID号单调自增，可以实现一些对ID有特殊要求的业务。

缺点：

- 强依赖DB，当DB异常时整个系统不可用，属于致命问题。配置主从复制可以尽可能的增加可用性，但是数据一致性在特殊情况下难以保证。主从切换时的不一致可能会导致重复发号。
- ID发号性能瓶颈限制在单台MySQL的读写性能。

对于MySQL性能问题，可用如下方案解决：在分布式系统中我们可以多部署几台机器，每台机器设置不同的初始值，且步长和机器数相等。比如有两台机器。设置步长step为2，TicketServer1的初始值为1，生产的ID为（1，3，5，7，9，11…）、TicketServer2的初始值为2，生产的ID为（2，4，6，8，10…）。

这是Flickr团队在2010年撰文介绍的一种主键生成策略（[Ticket Servers: Distributed Unique Primary Keys on the Cheap ](http://code.flickr.net/2010/02/08/ticket-servers-distributed-unique-primary-keys-on-the-cheap/)）。如下所示，为了实现上述方案分别设置两台机器对应的参数，TicketServer1从1开始发号，TicketServer2从2开始发号，两台机器每次发号之后都递增2。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/存储/07.png" alt="07" style="zoom:50%;" />

这种架构貌似能够满足性能的需求，但有以下几个缺点：

- 系统水平扩展比较困难，比如定义好了步长和机器台数之后，如果要添加机器该怎么做？假设现在只有一台机器发号是1,2,3,4,5（步长是1），这个时候需要扩容机器一台。可以这样做：把第二台机器的初始值设置得比第一台超过很多，比如14（假设在扩容时间之内第一台不可能发到14），同时设置步长为2，那么这台机器下发的号码都是14以后的偶数。然后摘掉第一台，把ID值保留为奇数，比如7，然后修改第一台的步长为2。让它符合我们定义的号段标准，对于这个例子来说就是让第一台以后只能产生奇数。扩容方案看起来复杂吗？貌似还好，现在想象一下如果我们线上有100台机器，这个时候要扩容该怎么做？简直是噩梦。所以系统水平扩展方案复杂难以实现。
- ID没有了单调递增的特性，只能趋势递增，这个缺点对于一般业务需求不是很重要，可以容忍。
- 数据库压力还是很大，每次获取ID都得读写一次数据库，只能靠堆机器来提高性能。

## 基于数据库的号段方案

本方案是对数据库方案的一种性能优化，每次从数据库取回的不是一个id，而是一个号段，在单独进程内通过锁保证每次发放一个唯一的id，甚至可以在系统快要发放完号码时异步地去获取下一个号段，该方案性能明显高于数据库方案，但也失去了id单调递增的特性，如果开启全同步复制，则可认为是一个`高可用`的方案，通过调大号段的长度，可以达到`高性能`的要求。

预先分配一段id到本地缓存，更新数据库value时会加锁。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/middleware/11.png" alt="11" style="zoom:50%;" />

表结构设计如下

| id   | app                         | ukey                     | value  | creator | createTime          | isDelete |
| :--- | :-------------------------- | :----------------------- | :----- | :------ | :------------------ | :------- |
| 1224 | finance                     | luna_transfer_id         | 40000  | creator | 2020-02-26 11:59:34 | 0        |
| 1223 | fund_pacific_tadataexchange | ack_order_no             | 120000 | creator | 2019-12-19 14:34:01 | 0        |
| 1222 | finance                     | morpheus_trans_id        | 40000  | creator | 2019-12-17 18:12:55 | 0        |
| 1221 | finance                     | morpheus_repay_notice_id | 40000  | creator | 2019-12-17 18:12:55 | 0        |

更新id时候，通过 select  for update 加锁（需要开启事务）

```sql
select app, ukey, value, creator, createTime from universal_id where app=#{app} and ukey=#{key} for update
```

然后再更新

```sql
  update universal_id set value=#{value} where app=#{app} and ukey=#{key}
```

整个更新代码如下:

```java
    @Transactional(value="crtTransactionManager", propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public IDRange get4Update(String app, String key, Long size) {
        Map<String, String> params = new HashMap<String, String>();
        params.put("app", app);
        params.put("key", key);
        SqlSession sqlSession = this.commonSqlSessionTemplate.getSqlSessionFactory()
                .openSession();
        //select for update 会对记录加上行锁保证原子性
        UniversalId universalId = sqlSession.selectOne("horae.select4Update", params);
        if (universalId == null) {
            return null;
        }

        IDRange range = new IDRange();
        range.setApp(app);
        range.setKey(key);
        range.setStart(universalId.getValue() + 1);
        range.setEnd(universalId.getValue() + size);

        universalId.setValue(universalId.getValue() + size);
        sqlSession.update("horae.updateValue", universalId);
        return range;
    }
```

事务管理配置:

```xml
<bean id="crtTransactionManager"
      class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
    <property name="dataSource" ref="horaeDataSource"/>
</bean>
 <tx:annotation-driven transaction-manager="crtTransactionManager"/>
```



## Leaf

Leaf是美团开源的分布式ID生成器，能保证全局唯一，趋势递增，但需要依赖关系数据库、Zookeeper等中间件。

Leaf主要解决了前面数据库方案存在的问题。在使用数据库的方案上，做了如下改变：  原方案每次获取ID都得读写一次数据库，造成数据库压力大。改为利用proxy server批量获取，每次获取一个segment(step决定大小)号段的值。用完之后再去数据库获取新的号段，可以大大的减轻数据库的压力。 - 各个业务不同的发号需求用biz_tag字段来区分，每个biz-tag的ID获取相互隔离，互不影响。如果以后有性能需求需要对数据库扩容，不需要上述描述的复杂的扩容操作，只需要对biz_tag分库分表就行。

数据库表设计如下：

```
+-------------+--------------+------+-----+-------------------+-----------------------------+
| Field       | Type         | Null | Key | Default           | Extra                       |
+-------------+--------------+------+-----+-------------------+-----------------------------+
| biz_tag     | varchar(128) | NO   | PRI |                   |                             |
| max_id      | bigint(20)   | NO   |     | 1                 |                             |
| step        | int(11)      | NO   |     | NULL              |                             |
| desc        | varchar(256) | YES  |     | NULL              |                             |
| update_time | timestamp    | NO   |     | CURRENT_TIMESTAMP | on update CURRENT_TIMESTAMP |
+-------------+--------------+------+-----+-------------------+-----------------------------+
```

重要字段说明：biz_tag用来区分业务，max_id表示该biz_tag目前所被分配的ID号段的最大值，step表示每次分配的号段长度。原来获取ID每次都需要写数据库，现在只需要把step设置得足够大，比如1000。那么只有当1000个号被消耗完了之后才会去重新读写一次数据库。读写数据库的频率从1减小到了1/step，大致架构如下图所示：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/存储/08.png" alt="08" style="zoom:50%;" />

test_tag在第一台Leaf机器上是1~1000的号段，当这个号段用完时，会去加载另一个长度为step=1000的号段，假设另外两台号段都没有更新，这个时候第一台机器新加载的号段就应该是3001~4000。同时数据库对应的biz_tag这条数据的max_id会从3000被更新成4000，更新号段的SQL语句如下：

```sql
Begin
UPDATE table SET max_id=max_id+step WHERE biz_tag=xxx
SELECT tag, max_id, step FROM table WHERE biz_tag=xxx
Commit
```

## Tinyid

滴滴tinyid参考https://github.com/didi/tinyid

## 参考

- [Leaf——美团点评分布式ID生成系统](https://tech.meituan.com/2017/04/21/mt-leaf.html)
- [tinyid](https://github.com/didi/tinyid)