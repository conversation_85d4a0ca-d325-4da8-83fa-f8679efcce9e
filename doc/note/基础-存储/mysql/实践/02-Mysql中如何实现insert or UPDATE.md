## 一、前言

在日常业务开发中经常有这样一个场景，首先创建一条记录，然后插入到数据库；如果数据库已经存在同一主键的记录，则执行update操作，如果不存在，则执行insert操作；

这个操作可以在业务层做，也可以在数据库层面做；

业务层一般做法是先查询，如果不存在在插入，如果存在则更新，但是查询和插入不是原子性操作，在并发量比较高的时候，可能两个线程都查询某个记录不存在，所以会执行两次插入，然后其中一条必然会因为主键（这里说的主键不是递增主键）冲突而失败。

数据库层mysql中INSERT ... ON DUPLICATE KEY UPDATE就可以做这个事情，并且是原子性操作，本文就来讲解的使用。

## 二、INSERT ... ON DUPLICATE KEY UPDATE命令

### 2.1单条记录下使用



```java
INSERT INTO t1 (a,b,c) VALUES (1,2,3)
  ON DUPLICATE KEY UPDATE c=c+1;
```

如上sql假如t1表的主键或者UNIQUE 索引是a，那么当执行上面sql时候，如果数据库里面已经存在a=1的记录则更新这条记录的c字段的值为原来值+1，然后返回值为2。如果不存在则插入a=1,b=2,c=3到数据库，然后返回值为1。

### 2.2多记录下使用



```java
INSERT INTO t1 (a,b,c) VALUES (1,2,3),(4,5,6)
  ON DUPLICATE KEY UPDATE c=VALUES(c);
```

## 三、MyBatis下使用

Mybatis作为经典的数据库持久层框架，自然要介绍下它下的使用

- 在mapper.xml里面配置如下：

```java
	<insert id="mergeHourHitrateStats">
        insert into hitrate_hour_statistics(app_id, cache_schema, collect_time, hits, misses)
        values (#{appId},#{schema},substring(#{collectTime}, 1, 10),#{hits},#{misses})
        on duplicate key update
          hits = hits + #{hits},
          misses = misses + #{misses}
    </insert>
    
    <insert id="mergeMinuteHitrateStats">
        insert into hitrate_minute_statistics(app_id, cache_schema, collect_time, hits, misses)
        values (#{appId},#{schema},#{collectTime},#{hits},#{misses})
        on duplicate key update
          hits = hits + #{hits},
          misses = misses + #{misses}
    </insert>
        
	<select id="getTop5HitRate" resultType="TopHitRate">
	    select cache_schema,
	    avg(hits/(hits+misses)) as avgHitRate,
	    max(hits/(hits+misses)) as maxHitRate,
	    min(hits/(hits+misses)) as minHitRate 
        from hitrate_hour_statistics 
        where app_id = #{appId} and (hits != 0 or misses != 0) and collect_time between #{begin} and #{end}
        group by cache_schema
        order by avgHitRate desc
        limit 5
    </select>        
```

- 对应的mapper接口可以定义为：

```java
public interface HitrateStatisticsDao {
    public void mergeHourHitrateStats(@Param("appId") long appId, @Param("schema") String schema,
            @Param("collectTime") long collectTime, @Param("hits") long hits, @Param("misses") long misses);

    public void mergeMinuteHitrateStats(@Param("appId") long appId, @Param("schema") String schema,
            @Param("collectTime") long collectTime, @Param("hits") long hits, @Param("misses") long misses);
    
    public List<HitRate> getStatsList(@Param("appId") long appId, @Param("schema") String schema,
            @Param("td") TimeDimensionality td);
    
    public TopHitRate getHitRate(@Param("appId") long appId,@Param("schema") String schema, @Param("begin") long begin, @Param("end") long end);
    
    public List<TopHitRate> getTop5HitRate(@Param("appId") long appId, @Param("begin") long begin, @Param("end") long end);
    
    public List<TopHitRate> getBottom5HitRate(@Param("appId") long appId, @Param("begin") long begin, @Param("end") long end);
}

```

另外一种用法：

```xml
<insert id="saveOrUpdateConfig">
		insert into app_client_config(<include refid="columns" />)
		values(#{appId},#{templateName},#{configKey},#{configValue},#{configDesc},#{status})
	    on duplicate key update
	    config_value = #{configValue}, status = #{status} 
</insert>
```

```java
int saveOrUpdateConfig(AppClientConfig clientConfig);
```



## 四、参考

[https://dev.mysql.com/doc/refman/5.7/en/insert-on-duplicate.html](https://link.jianshu.com?t=https%3A%2F%2Fdev.mysql.com%2Fdoc%2Frefman%2F5.7%2Fen%2Finsert-on-duplicate.html)

