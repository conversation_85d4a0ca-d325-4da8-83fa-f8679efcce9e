## 概述

[tstorage](https://github.com/nakabonne/tstorage) 是一个轻量的time-series database

## 原理

### 模型

- storage：表示一个存储对象，对外暴露的CRUD,内部封装了partitionList，wal等对象

- partitionList：表示一个partition的链表，负责新建partitionIterator，同时包含insert，remove，swap，getHead操作。
- partitionIterator： 内部封装了partitionNode，对外提供一个了next方法，主要为了方便对链表结点进行迭代。
- partitionNode：包含了partition和一个next引用。
- partition：分区表示具有时间戳范围的时间序列数据块，包含一个max和min。

### 结构

partition 结构如下：

```
  │                 │
Read              Write
  │                 │
  │                 V
  │      ┌───────────────────┐ max: 1600010800
  ├─────>   Memory Partition
  │      └───────────────────┘ min: 1600007201
  │
  │      ┌───────────────────┐ max: 1600007200
  ├─────>   Memory Partition
  │      └───────────────────┘ min: 1600003601
  │
  │      ┌───────────────────┐ max: 1600003600
  └─────>   Disk Partition
         └───────────────────┘ min: 1600000000
```

在内存中老的partition 会定期持久化到磁盘，磁盘的目录格式如下：

```
$ tree ./data
./data
├── p-1600000001-1600003600
│   ├── data
│   └── meta.json
├── p-1600003601-1600007200
│   ├── data
│   └── meta.json
└── p-1600007201-1600010800
    ├── data
    └── meta.json
```

每个目录下都存在两个文件：meta.json 和 data。data是被压缩过的数据文件并通过mmap方式使用，而 meta.json会常驻内存，其结构如下:

```json
$ cat ./data/p-1600000001-1600003600/meta.json
{
  "minTimestamp": 1600000001,
  "maxTimestamp": 1600003600,
  "numDataPoints": 7200,
  "metrics": {
    "metric-1": {
      "name": "metric-1",
      "offset": 0,
      "minTimestamp": 1600000001,
      "maxTimestamp": 1600003600,
      "numDataPoints": 3600
    },
    "metric-2": {
      "name": "metric-2",
      "offset": 36014,
      "minTimestamp": 1600000001,
      "maxTimestamp": 1600003600,
      "numDataPoints": 3600
    }
  }
}
```

可以发现本质是指定每个metric在文件中的offset，这点和 bitcast 是一样的。



## 初始化

通过 func NewStorage(opts ...Option)进行初始化 storage。

### 1、初始化 storage 对象

```go
	s := &storage{
		partitionList:      newPartitionList(), 
		workersLimitCh:     make(chan struct{}, defaultWorkersLimit),
		partitionDuration:  defaultPartitionDuration,
		retention:          defaultRetention,
		timestampPrecision: defaultTimestampPrecision,
		writeTimeout:       defaultWriteTimeout,
		walBufferedSize:    defaultWALBufferedSize,
		wal:                &nopWAL{},
		logger:             &nopLogger{},
		doneCh:             make(chan struct{}, 0),
	}
```

这里会初始化 partitionList。

### 2、从本地目录加载 partition

```go
	// Read existent partitions from the disk.
	dirs, err := os.ReadDir(s.dataPath)
	if err != nil {
		return nil, fmt.Errorf("failed to open data directory: %w", err)
	}
	if len(dirs) == 0 {
		s.newPartition(nil, false)
		return s, nil
	}
	//创建一个 partition 数组
	partitions := make([]partition, 0, len(dirs))
	for _, e := range dirs {
		if !isPartitionDir(e) {
			continue
		}
		path := filepath.Join(s.dataPath, e.Name())
		//执行加载Partition
		part, err := openDiskPartition(path, s.retention)
		if errors.Is(err, ErrNoDataPoints) {
			continue
		}
		if errors.Is(err, errInvalidPartition) {
			// It should be recovered by WAL
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("failed to open disk partition for %s: %w", path, err)
		}
		//创建好的partition对象加入到partitions
		partitions = append(partitions, part)
	}

	//把 partition 加入到 partitionList 中
	for _, p := range partitions {
		s.newPartition(p, false)
	}
```

### 3、初始化 WAL

```go
	// Start WAL recovery if there is.
	if err := s.recoverWAL(walDir); err != nil {
		return nil, fmt.Errorf("failed to recover WAL: %w", err)
	}
	s.newPartition(nil, false)
	
```

注意这里s.newPartition(nil, false)会创建 memoryPartition。

### 5、定时检查并删除过期的partitions

```go
	// periodically check and permanently remove expired partitions.
	go func() {
		ticker := time.NewTicker(checkExpiredInterval)
		defer ticker.Stop()
		for {
			select {
			case <-s.doneCh:
				return
			case <-ticker.C:
				err := s.removeExpiredPartitions()
				if err != nil {
					s.logger.Printf("%v\n", err)
				}
			}
		}
	}()
```

## 插入数据

初始化好storage 之后就可以通过 InsertRows() 插入数据，方法主体结构如下：

```go
func (s *storage) InsertRows(rows []Row) error {
  // ①创建一个insert函数
  insert := func() error {
    //省略
  }
  // 二通过goroutines执行
  select {
    case s.workersLimitCh <- struct{}{}:
      return insert()
    default:
	}
}
```

### 1、inner insert()

```go
insert := func() error {
		defer func() { <-s.workersLimitCh }()
		if err := s.ensureActiveHead(); err != nil {
			return err
		}
		//①初始化 partitionIterator
		iterator := s.partitionList.newIterator()
		n := s.partitionList.size()
		rowsToInsert := rows
		// Starting at the head partition, try to insert rows, and loop to insert outdated rows
		// into older partitions. Any rows more than `writablePartitionsNum` partitions out
		// of date are dropped.
  	//②默认前2 个partion才允许写
		for i := 0; i < n && i < writablePartitionsNum; i++ {
			//插入的数据，如果为空直接break
      if len(rowsToInsert) == 0 {
				break
			}
      //如果到末尾了
			if !iterator.next() {
				break
			}
      //③调用partition的插入方法，如果插入的timestamp比partion的min还旧(小)会返回数据
			outdatedRows, err := iterator.value().insertRows(rowsToInsert)
			if err != nil {
				return fmt.Errorf("failed to insert rows: %w", err)
			}
			rowsToInsert = outdatedRows
		}
		return nil
	}
```

主要流程：

- 初始化storage.partitionList.partitionIterator()
- 迭代partitionList，调用每个partition的insertRows()，如果执行成功推出

### 2、内存partion的insert实现

Partition 按照从新到旧的顺序添加到partitionList，所以storage 默认只会执行 MemoryPartition 的 insertRows方法，代码实现如下:

```go
// insertRows inserts the given rows to partition.
func (m *memoryPartition) insertRows(rows []Row) ([]Row, error) {
  for i := range rows {
    //标准化 metric name
		name := marshalMetricName(row.Metric, row.Labels)
    //从map中通过name拿到memoryMetric
		mt := m.getMetric(name)
		//memoryMetric插入DataPoint
		mt.insertPoint(&row.DataPoint)
  }
}
```

这里省略边界判断的代码，只保留了核心逻辑，主要就三步：

- 标准化 metric name；
- 调用memoryPartition.getMetric()获取到memoryPartition， 内部实现是通过维护一个map；
- 插入DataPoint到MemoryMetric，内部通过一个数组保存DataPoint，MemoryMetric同样维持了minTimestamp和maxTimestamp；

对于 diskPartition 不允许插入， insertRows 方法直接抛出异常：

```go
func (d *diskPartition) insertRows(_ []Row) ([]Row, error) {
	return nil, fmt.Errorf("can't insert rows into disk partition")
}
```

## 查询

查询对应了 Select 方法，方法结构如下:

```go
func (s *storage) Select(metric string, labels []Label, start, end int64) ([]*DataPoint, error) {
  // Iterate over all partitions from the newest one.
	iterator := s.partitionList.newIterator()
	for iterator.next() {
		part := iterator.value()
		if part == nil {
			return nil, fmt.Errorf("unexpected empty partition found")
		}
		if part.minTimestamp() == 0 {
			// Skip the partition that has no points.
			continue
		}
		if part.maxTimestamp() < start {
			// No need to keep going anymore
			break
		}
		if part.minTimestamp() > end {
			continue
		}
		ps, err := part.selectDataPoints(metric, labels, start, end)
		if errors.Is(err, ErrNoDataPoints) {
			continue
		}
		if err != nil {
			return nil, fmt.Errorf("failed to select data points: %w", err)
		}
		// in order to keep the order in ascending.
		points = append(ps, points...)
	}
  return points, nil
}
```

核心逻辑就是遍历partitionList，然后调用每个partition的selectDataPoints方法()

### 1、内存partition的select

memoryPartition 的查询实现代码如下：

```go
func (m *memoryPartition) selectDataPoints(metric string, labels []Label, start, end int64) ([]*DataPoint, error) {
	name := marshalMetricName(metric, labels)
	mt := m.getMetric(name)
	return mt.selectPoints(start, end), nil
}
```

代码很简单，就是通过 metric 和 label 获取到对应的memoryMetric，然后调用它的selectPoints()方法，实现方式就是通过timestamp区间确定数据的开始和结束索引，然后返回子数组。

### 2、diskPartition 的select

在看 diskPartition 之前需要先看下diskPartition的初始化过程，在openDiskPartition()方法中:

```go
func openDiskPartition(dirPath string, retention time.Duration) (partition, error) {
  	//① Map data to the memory
  	dataPath := filepath.Join(dirPath, "data")
    f, err := os.Open(dataPath)
    mapped, err := syscall.Mmap(int(f.Fd()), int(info.Size()))  
    //② 读取 metadata
  	m := meta{}
  	mf, err := os.Open(metaFilePath)
  	decoder := json.NewDecoder(mf)
  	decoder.Decode(&m);
  	//③ 返回diskPartition对象
    return &diskPartition{
        dirPath:    dirPath,
        meta:       m,
        f:          f,
        mappedFile: mapped,
        retention:  retention,
      }, nil  	
}
```

一共三步：

- 读取data并执行mmap。
- 读取meta.json并反序列得到meta对象
- 拿到上述对象实例化 diskPartition。

通过初始化过程可以知道 diskPartition 内部持有meta信息和data文件的mmp，所以其查询也主要通过这两点实现。

```go 
func (d *diskPartition) selectDataPoints(metric string, labels []Label, start, end int64) ([]*DataPoint, error) {
  //① 标准化 metric name
  name := marshalMetricName(metric, labels)
  //② 通过name 拿到 diskMetric，diskMetric持有offset和DataPoint的数量
  mt, ok := d.meta.Metrics[name]
 	//③ 通过offset读取数据
  r.Seek(mt.Offset, io.SeekStart)
}
```



## 小结

tstorage代码非常短小精炼，不过并没有实现聚合功能。