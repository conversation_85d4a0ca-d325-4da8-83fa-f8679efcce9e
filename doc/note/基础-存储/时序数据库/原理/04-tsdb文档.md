## 概述

本文是对 网易范欣欣 时序数据库技术体系的摘录和提炼。

## 时序数据模型

到了2024年时序数据发展已经相对比较成熟，主流就是两类数据模型： InfluxDB 和 Prometheus。 包括国内知名的 greptimeDB 也是类似InfluxDB的做法。

**InfluxDB 的数据模型**

- **Measurement（度量）**：类似于指标的概念。
- **Tag（标签）**：用于描述附加信息，类似于 Prometheus 的标签。
- **Field（字段）**：一个 `measurement` 可以有多个字段（field），即一个时间点可以存储多个值。每个 `field` 的值是必须存储的，不能被索引。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241015112519213.png" alt="image-20241015112519213" style="zoom:50%;" />

InfluxDB这种方式适合需要同时查询多个度量值的情况，比如：监控 CPU 的不同指标。

而Prometheus采用了一种稍微不同的数据模型：

- **Metric（指标）**：名称。
-  **Label（标签）** ：标识指标的不同维度，比如应用名称、实例、区域等。
- **Value** ：对应的值。<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241015155313219.png" alt="image-20241015155313219" style="zoom:67%;" />

可以看到 Prometheus 采用单值模型，即一个Metric只能有一个Value，如果要查询机器性能设计到cpu、load、网络需要查询多个指标。

对于时序数据可以简单的概括为：一个时序数据点point由 **(tags)+metric+timestamp** 这三部分唯一确定。然而，这只是逻辑上的概念理解，那具体的时序数据库到底是如何将这样一系列时序数据点进行存储的呢。

## OpenTSDB

OpenTSDB基于HBase存储时序数据，在HBase层面设计RowKey规则为：metric+timestamp+tags。HBase是一个KV数据库，一个时序数据(point)如果以KV的形式表示，那么其中的V必然是point的具体数值，而Key就自然而然是唯一确定point数值的tags+metric+timestamp。

既然HBase中K是由tags、metric以及timestamp三者构成，现在我们可以简单认为rowkey就为这三者的组合，那问题来了：这三者的组合顺序是怎么样的呢？

OpenTSDB中rowkey的设计为：metric+timestamp+datasource，这样可以通过rowkey scan过滤出的数据。好了，那HBase就可以只设置一个columnfamily和一个column。那问题来了，OpenTSDB的这种设计有什么问题？在了解设计问题之前需要简单看看HBase在文件中存储KV的方式，即一系列时序数据在文件、内存中的存储方式，如下图所示：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241015160102311.png" alt="image-20241015160102311" style="zoom:50%;" />

上图是HBase中一个存储KeyValue(KV)数据的数据块结构，一个数据块由多个KeyValue数据组成，在我们的事例中KeyValue就是一个时序数据点（point）。其中Value结构很简单，就是一个数值。而Key就比较复杂了，由rowkey+columnfamily+column+timestamp+keytype组成，其中rowkey等于metric+timestamp+tags。

- 问题一：存在很多无用的字段。一个KeyValue中只有rowkey是有用的，其他字段诸如columnfamily、column、timestamp以及keytype从理论上来讲都没有任何实际意义，但在HBase的存储体系里都必须存在，因而耗费了很大的存储成本。
- 问题二：tags和采集指标冗余。KeyValue中rowkey等于metric+timestamp+tags，试想同一个tag的同一个采集指标，随着时间的流逝不断吐出采集数据，这些数据理论上共用同一个tags和采集指标(metric)，但在HBase的这套存储体系下，共用是无法体现的，因此存在大量的数据冗余，主要是tag冗余以及采集指标冗余。
- 问题三：不能完全保证多维查询能力。HBase本身没有schema，目前没有实现倒排索引机制，所有查询必须指定metric、timestamp以及完整的tags或者前缀tags进行查询，对于后缀维度查询也勉为其难。

虽说有这样那样的问题，但是OpenTSDB还是针对存储模型做了两个方面的优化：

- 优化一：timestamp并不是想象中细粒度到秒级或毫秒级，而是精确到小时级别，然后将小时中每一秒设置到列上。这样一行就会有3600列，每一列表示一小时的一秒。这样设置据说可以有效的取出一小时整的数据。
- 优化二：所有metrics以及所有标签信息（tags）都使用了全局编码将标签值编码成更短的bit，减少rowkey的存储数据量。上文分析HBase这种存储方式的弊端是说道会存在大量的数据源(tags)冗余以及指标(metric)冗余，有冗余是吧，那我就搞个编码，将string编码成bit，尽最大努力减少冗余。虽说这样的全局编码可以有效降低数据的存储量，但是因为全局编码字典需要存储在内存中，因此在很多时候（海量标签值），字典所需内存都会非常之大。

上述两个优化可以参考OpenTSDB这张经典的示意图：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241015160402930.png" alt="image-20241015160402930" style="zoom:50%;" />

## InfluxDB

InfluxDB采用LSM结构，数据先写入内存，当内存容量达到一定阈值之后flush到文件。

### 内存结构

InfluxDB在时序数据模型设计方面提出了一个非常重要的概念：seriesKey，也可称之为数据源/时间线，在InfluxDB series由**measurement name + tag_key + tag_value +field_key** 组成，时序数据写入内存之后按照seriesKey进行组织：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241015160754024.png" alt="image-20241015160754024" style="zoom:60%;" />

比如我们记录的es metric数据：

| @timestamp       | index_name | shard_id | count | error_count |
| ---------------- | ---------- | -------- | ----- | ----------- |
| 2024-10-15-13:01 | hermes     | 0        | 100   | 0           |
| 2024-10-15-13:01 | hermes     | 1        | 50    | 0           |
| 2024-10-15-13:01 | jasmine    | 0        | 1     | 0           |
| 2024-10-15-13:01 | jasmine    | 1        | 5     | 0           |

对于 InfluxDB 会按照 **seriesKey+fieldKey** 作为key，point_values 记录的时间点对应的value

| seriesKey+fieldKey | point_values               |
| ------------------ | -------------------------- |
| hermes-0-count     | 13:01=>50,13:02=>100...... |
| hermes-1-count     | 13:01=>50,13:02=>90......  |
| jasmine-0-count    | 13:01=>1,13:02=>5......    |
| jasmine-1-count    | 13:01=>2,13:02=>5......    |

内存中实际上就是一个Map，这个Map可以表示为<Key, List<Timestamp|Value>>。其中Key表示为**seriesKey+fieldKey**，Map中一个Key对应一个List，List中存储时间线数据。

数据进来之后根据 tags+metric拼成SeriesKey，再将Timestamp|Value组合值写入时间线数据List中。内存中的数据flush的文件后，同样会将同一个SeriesKey中的时间线数据写入同一个Block块内，即一个Block块内的数据都属于同一个tag下的一个metric。

这种设计我们认为是将时间序列数据按照时间线挑了出来。先来看看这样设计的好处：

- 好处一：同一数据源的tags不再冗余存储。一个Block内的数据都共用一个SeriesKey，只需要将这个SeriesKey写入这个Block的Trailer部分就可以。大大降低了时序数据的存储量。
- 好处二：时间序列和value可以在同一个Block内分开独立存储，独立存储就可以对时间列以及数值列分别进行压缩。InfluxDB对时间列的存储借鉴了Beringei的压缩方式，使用delta-delta压缩方式极大的提高了压缩效率。而对Value的压缩可以针对不同的数据类型采用相同的压缩效率。
- 好处三：对于给定tag以及时间范围的数据查找，可以非常高效的进行查找。这一点和OpenTSDB一样。

细心的同学可能会问了，将datasource(tags)和metric拼成SeriesKey，不是也不能实现多维查找。确实是这样，不过InfluxDB内部实现了倒排索引机制，即实现了tag到SeriesKey的映射关系，如果用户想根据某个tag查找的话，首先根据tag在倒排索引中找到对应的SeriesKey，再根据SeriesKey定位具体的时间线数据。

> 注意【多维查找】就是指支持任意维度条件过滤，比如上述例子中查询shard_id=0的所有数据，像hbase rowkey因为按照tag拼接，所以不太好支持后缀维度查询

InfluxDB的这种存储引擎称为TSM，全称为Timestamp-Structure Merge Tree，基本原理类似于LSM。

接下来深入分析 TSM工作原理。

### 时序数据写入

InfluxDB在内存中使用一个Map来存储时间线数据，这个Map可以表示为<Key, List<Timestamp|Value>>。其中Key表示为**seriesKey+fieldKey**，Map中一个Key对应一个List，List中存储时间线数据。其实这是个非常自然的想法，并没有什么高深的难点。基于Map这样的数据结构，时序数据写入内存流程可以表示为如下三步：

1. 时间序列数据进入系统之后首先根据measurement + datasource(tags)拼成seriesKey
2. 根据这个seriesKey以及待查fieldKey拼成Key，再在Map中根据Key找到对应的时间序列集合，如果没有的话就新建一个新的List
3. 找到之后将Timestamp|Value组合值追加写入时间线数据链表中

### TSM文件结构

每隔一段时间，内存中的时序数据就会执行flush操作将数据写入到文件（称为TSM文件），整个文件的组织和HBase中HFile基本相同，对HFile文件比较了解的童鞋会很容易理解。相同点主要在于两个方面：

1. 数据都是以Block为最小读取单元存储在文件中
2. 文件数据块都有相应的类B+树索引，而且数据块(**Series Data Block**)和索引结构(**Series Index Block**)进行存储在同一个文件中(**文件产生之后不再修改，个人理解**)

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241015164448276.png" alt="image-20241015164448276" style="zoom:50%;" />

**Series Data Block**保存的是某个key对应的时间序列和对应的value，由四部分构成：Type、Length、Timestamps以及Values，分别表示意义如下：

1. Type：表示该seriesKey对应的时间序列的数据类型，数值数据类型通常为int、long、float以及double等。不同的数据类型对应不同的编码方式。
2. Length：len(Timestamps)，用于读取Timestamps区域数据，解析Block。
    时序数据的时间值以及指标值在一个Block内部是按照列式存储的：所有的时间值存储在一起，所有的指标值存储在一起。使用列式存储可以极大提高系统的压缩效率。
3. Timestamps：时间值存储在一起形成的数据集，通常来说，时间序列中时间值的间隔都是比较固定的，比如每隔一秒钟采集一次的时间值间隔都是1s，这种具有固定间隔值的时间序列压缩非常高效，TSM采用了Facebook开源的Geringei系统中对时序时间的压缩算法：delta-delta编码。
4. Values：指标值存储在一起形成的数据集，同一种Key对应的指标值数据类型都是相同的，由Type字段表征，相同类型的数据值可以很好的压缩，而且时序数据的特点决定了这些相邻时间序列的数据值基本都相差不大，因此也可以非常高效的压缩。需要注意的是，不同数据类型对应不同的编码算法。



当用户查询某段时间（比如最近一小时）的时序数据，如果没有索引，就会需要将整个TSM文件加载到内存中才能一个Data Block一个Data Block查找，这样一方面非常占用内存，另一方面查询效率非常之低。为了提高查询效率，TSM文件引入了索引，索引数据由一系列索引Block组成，每个索引Block又由Index Block Meta以及一系列Index Entry构成：

- Index Block Meta最核心的字段是Key，表示这个索引Block内所有IndexEntry所索引的时序数据块都是该Key对应的时序数据。
- Index Entry表示一个索引字段，指向对应的Series Data Block。指向的Data Block由Offset唯一确定，Offset表示该Data Block在文件中的偏移量，Size表示指向的Data Block大小。Min Time和Max Time表示指向的Data Block中时序数据集合的最小时间以及最大时间，**用户在根据时间范围查找时可以根据这两个字段进行过滤**。

通过索引我们可以建立一个 seriesKey+fieldKey 到 DataPoint的映射， 不过这里我不太理解为什么把Data和Index放在一个文件，感觉完全可以放在两个文件。 

### Flush和Compaction

我们知道 influxdb 内存中是一个 Map <Key, List<Timestamp|Value>> 保存数据，然后会定期把这个Map中的数据刷新到tsm文件，那么influxdb多久会把map中的数据刷新到tsm文件？

默认当缓存达到1GB或者在 10 分钟内没有数据更新时会自动刷新，提供了两个相关配置：

- `cache-max-memory-size`: 默认约 1GB 或者根据系统内存的比例自动调整。
- `cache-snapshot-write-cold-duration`: 默认值是 **10 分钟**，表示如果数据在 10 分钟内没有更新就会被写入磁盘。

定期刷新可能导致产生大量小的tsm文件，InfluxDB 使用 **Compaction（压缩）** 机制来合并小的 TSM 文件成更大的 TSM 文件，以优化存储和查询性能。

InfluxDB 将 TSM 文件划分为不同的级别（Level 0, Level 1, Level 2 等），每个级别的文件大小和合并频率都不同：

- **Level 0**：由最新生成的小型 TSM 文件组成。这些文件较小且生成频繁。
- **Level 1**：当 Level 0 文件达到一定数量时，InfluxDB 会将它们合并成较大的 Level 1 文件。
- **Level 2 及更高级别**：更高的级别会将较小的 TSM 文件进一步合并成更大的文件，以减少文件碎片和提高查询效率。



### 时序数据读取

基于对TSM文件的了解，在一个文件内部根据Key查找一个某个时间范围的时序数据就会变得很简单，整个过程如下图所示：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241015171531562.png" alt="image-20241015171531562" style="zoom:50%;" />

上图中中间部分为索引层，TSM在启动之后就会将TSM文件的索引部分加载到内存，数据部分因为太大并不会直接加载到内存。用户查询可以分为三步：

1. 首先根据Key找到对应的SeriesIndex Block，因为Key是有序的，所以可以使用二分查找来具体实现
2. 找到SeriesIndex Block之后再根据查找的时间范围，使用[MinTime, MaxTime]索引定位到可能的Series Data Block列表
3. 将满足条件的Series Data Block加载到内存中解压进一步使用二分查找算法查找即可找到

### 倒排索引

上一节如果要支持多维查询挑战比较大，需要扫描所有的seriesKey，过滤出符合条件的Key，然后读取对应的Data Block，这种方式比较低效，所以InfluxDB引入了倒排索引，首先引入了一个**series ID** 的概念，**series ID** 是用来唯一标识一个时间序列的标识符，当 InfluxDB 存储一个数据点时，它会根据 `measurement`、`tag` 组合生成一个唯一的标识符，即 series ID。

比如一张数据表，其中 `topic` 和 `partition` 是 `tag`，并且不同的 `seriesKey` 会生成不同的 `seriesId`

| seriesId | seriesKey                 | point_values               |
| -------- | ------------------------- | -------------------------- |
| 1        | topic=hermes,partition=0  | 13:01=>50,13:02=>100...... |
| 2        | topic=hermes,partition=1  | 13:01=>50,13:02=>90......  |
| 3        | topic=jasmine,partition=0 | 13:01=>1,13:02=>5......    |
| 4        | topic=jasmine,partition=1 | 13:01=>2,13:02=>5......    |

InfluxDB 分别对 topic 和 partition 两个字段建立倒排索引：

- topic索引：`hermes` -> [1, 2]， `jasmine` -> [3, 4]
-  partition索引： `0` -> [1, 3]， `1` -> [2, 4]

当你执行查询语句，如 `WHERE partition=0`，InfluxDB 会按照以下步骤操作：

1. **加载倒排索引**：Ingest 倒排索引中的 `partition=0` 条目。
2. **获取 seriesId**：从倒排索引中得到 `partition=0` 对应的 seriesId 集合 `[1, 3]`。
3. **定位数据块**：根据这些 seriesId `[1, 3]`，InfluxDB 可以快速跳转到对应的 TSM 数据块。
4. **读取数据**：从 TSM 文件中读取与 seriesId `[1, 3]` 相关的数据块，并进行进一步处理或聚合。

### 数据聚合

如果想统计某个时间段的所有topic的写入量，这个时候无法利用倒排索引，需要读取所有索引再过滤时间段，是不是有点低效？我把这个问题丢给chatGPT，它的回答：

查询只根据时间范围进行过滤时，确实会遇到一些性能上的挑战。

在 InfluxDB 中，如果查询条件只涉及时间范围，例如统计某个时间段的所有 topic 的写入量，这种查询无法直接利用倒排索引中的 `tag` 来快速过滤数据。这种情况下，查询流程会有所不同：

1. **根据时间范围定位数据块**：尽管无法利用倒排索引过滤 `tags`，但 InfluxDB 会根据时间范围来尽量缩小读取的 TSM 数据块的范围。**TSM 文件是按时间顺序存储的**，这意味着数据块内的数据点都是按时间顺序排列的。因此，InfluxDB 可以在一定程度上通过时间戳快速定位到相关的 TSM 数据块，而不需要读取所有数据块。
2. **按需加载数据块**：InfluxDB 只会读取那些包含了查询时间范围内数据的数据块，并不会直接加载所有数据块。虽然这不能完全避免不必要的数据块读取，但相较于全表扫描仍然更为高效。
3. **过滤与聚合**：在加载了符合时间条件的数据块后，InfluxDB 会在内存中对数据进行进一步的过滤和聚合，计算出所有 topic 的写入量。

优化方式：

- **使用 downsampling（降采样）策略**：将历史数据降采样并存储在新的 measurement 中，减少大范围查询的数据量，比如提前按天聚合。

### shard

>  问题：我有100个tsm文件，如果我查询条件只包含了时间范围，我如何判断应该读哪些tsm文件，总不能把这100个tsm文件都读一遍吧？

最开始我以为 influxdb 会按照 $metric-$timestamp.tsm方式保存数据。但实际上不是这样的。在influxdb中一个tsm文件实际会包含多个metric的数据，它是按照shard组织的，一个shard就是一个目录，**shard目录存储了一个固定时间范围的数据，比如一天，甚至一周**。

假设你有一个数据库 `example_db` 和一个保留策略 `autogen`，其目录结构可能类似于：

```
/var/lib/influxdb/data/
└── example_db/
    └── autogen/
        ├── 1234/         # Shard 1234 的目录
        │   ├── 1234-00001.tsm
        │   ├── 1234-00002.tsm
        │   └── index.tsi
        ├── 1235/         # Shard 1235 的目录
        │   ├── 1235-00001.tsm
        │   └── index.tsi
        └── 1236/         # Shard 1236 的目录
            ├── 1236-00001.tsm
            ├── 1236-00002.tsm
            └── index.tsi

```

当你执行一个带有时间范围的查询时，InfluxDB 首先会查找其内部的元数据（如 `meta.db` 文件），这个元数据记录了每个 shard 的时间范围。

根据查询的时间条件，InfluxDB 可以快速筛选出哪些 shard 包含了需要的数据。

通过shard管理数据的优势：

- **按时间范围隔离数据**：shard 是基于时间范围组织的，所有属于同一时间范围的数据都存储在同一个 shard 目录下。这样可以在进行时间范围查询时迅速定位到相关的数据目录，而无需遍历所有的 TSM 文件
- **便于管理**：将不同时间段的数据分散到多个 shard 目录中，便于管理数据的存储和清理。比如当数据超过保留期限时，可以直接删除整个 shard 目录来释放空间
- **并行化操作**：由于不同的 shard 目录是独立的，因此 InfluxDB 可以对不同 shard 进行并行化操作（如查询、写入和合并），提高性能和效率。



> 问题：先写入了一段12点到13点的时序数据，突然又写入了一条昨天的历史数据？ influxdb如何处理这种特殊情况？

**之前我以为整个 InfluxDB 只有一个map，其实不是这样的**，每个shard都有一个map，一个shard可以理解是一个独立的读写单元。如果突然写入一条昨天的历史数据会进去昨天的那个shard，这种处理方式太优雅了！

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241016152726330.png" alt="image-20241016152726330" style="zoom:50%;" />



## 参考

网易范欣欣时序数据库技术体系
1. [时序数据库技术体系 -时序数据存储模型设计](https://sq.sf.163.com/blog/article/169864634071179264)
2. [时序数据库技术体系 -初识InfluxDB](https://sq.sf.163.com/blog/article/169866295296581632)
3. [时序数据库技术体系 – InfluxDB TSM存储引擎之TSMFile](http://web.archive.org/web/20190118051908/http://hbasefly.com/2018/01/13/timeseries-database-4/)
4. [时序数据库技术体系 – InfluxDB 多维查询之倒排索引](http://web.archive.org/web/20190118102621/http://hbasefly.com/2018/02/09/timeseries-database-5/)
5. [时序数据库技术体系 – InfluxDB TSM存储引擎之数据写入](http://web.archive.org/web/20190118102631/http://hbasefly.com/2018/03/27/timeseries-database-6/)
6. [时序数据库技术体系 – InfluxDB TSM存储引擎之数据读取](http://web.archive.org/web/20190118102223/http://hbasefly.com/2018/05/02/timeseries-database-7/)

其他：

- [OpenTSDB 底层 HBase 的 Rowkey 是如何设计的](https://cloud.tencent.com/developer/article/1544737)
- [InfluxDB数据压缩算法](https://zhuanlan.zhihu.com/p/108968057)
- [360分布式时序数据库 QTSDB 的设计与实现](https://www.infoq.cn/article/6yle5pk3hweqbzkahxau)
- [TDengine技术内幕](https://docs.taosdata.com/tdinternal/storage/)
- [BladeX](https://iot.bladex.cn/)

