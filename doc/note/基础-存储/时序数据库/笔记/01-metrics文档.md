## 概述

Metrics就是度量的意思。当我们需要为某个系统某个服务做监控、做统计，就需要用到Metrics。

一般的metrics统计包含以下几个方面：

1. 每秒钟的请求数是多少（TPS）？
2. 平均每个请求处理的时间？
3. 请求处理的最长耗时？
4. 等待处理的请求队列长度？

基本上每一个服务、应用都需要做一个监控系统，这需要尽量以少量的代码，实现统计某类数据的功能。



以 Java 为例，目前最为流行的 metrics 库是来自 Coda Hale 的 [dropwizard/metrics](https://github.com/dropwizard/metrics)，该库被广泛地应用于各个知名的开源项目中。例如 Hadoop，Kafka，Spark，JStorm 中。

## 用法

我们需要在`pom.xml`中依赖 `metrics-core` 包：

```java
<dependencies>
    <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-core</artifactId>
        <version>3.1.0</version>
    </dependency>
</dependencies>
```



### Metric Registries

`MetricRegistry`类是Metrics的核心，它是存放应用中所有metrics的容器。也是我们使用 Metrics 库的起点。

```java
MetricRegistry registry = new MetricRegistry();
```

每一个 metric 都有它独一无二的名字，Metrics 中使用句点名字，如 com.example.Queue.size。当你在 com.example.Queue 下有两个 metric 实例，可以指定地更具体：com.example.Queue.requests.size 和 com.example.Queue.response.size 。使用`MetricRegistry`类，可以非常方便地生成名字。

```java
MetricRegistry.name(Queue.class, "requests", "size")
```



### Metrics 类型

下面是对 legion topic的监控指标

![image-20240105151002420](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240105151002420.png)

- count 记录请求量和错误数
- meter计算请求量的速率
- histogram计算耗时的直方图





#### 1、Gauge

最简单的度量指标，只有一个简单的返回值，gauge可以增加也可以减小。例如，我们想衡量一个待处理队列中任务的个数，代码如下：

```java
	@Test
	public void testGauge() throws InterruptedException {
		String name = MetricRegistry.name("MetricTest", "queue", "size");
		Queue<String> q = new LinkedList<String>();
		Gauge<Integer> gauge = metricRegistry.gauge(name,()->new Gauge<Integer>() {
			@Override
			public Integer getValue() {
				return q.size();
			}
		});
		q.add("test1");
		q.add("test2");
		assertEquals(Integer.valueOf(2), gauge.getValue());
	}
```



#### 2、Counter

Counter 就是计数器，Counter一般是单调自增的，Counter 只是用 Gauge 封装了 `AtomicLong` 。

```java
	@Test
	public void testCounter() throws InterruptedException {
		String name = MetricRegistry.name("MetricTest", "queue", "size");
		Counter counter = metricRegistry.counter(name);
		counter.inc();
		counter.inc(2);
		assertEquals(3, counter.getCount());
	}
```



#### 3、Meter

Meter度量一系列事件发生的速率(rate)，一般用于请求tps、错误tps的统计。

Meters会统计最近1分钟，5分钟，15分钟，还有全部时间的速率。下面统计某个api的qps代码：

```java
	@Test
	public void testMeter() throws InterruptedException {
		Meter meter = metricRegistry.meter("api.request");
		//每秒执行5次
    for(int k=0;k<5;++k){
      for (int i = 0; i < 5; ++i) {
        meter.mark();
      }
      Thread.sleep(1000);
    }
		//平均qps
		double meanRate = meter.getMeanRate();
		double oneMinRate = meter.getOneMinuteRate();
		double fiveMinRate = meter.getFiveMinuteRate();
		double fifteenMinRate = meter.getFifteenMinuteRate();
	}
```

需要注意的是Meter返回的是移1 分钟，5分钟，15 分钟是**移动平均速率**。比如按照下面的速率请求：

```
第1秒 10
第2秒 10
第3秒 0 
第4秒 0
第5秒 0
第6秒 0
第7秒 0
第8秒 3
```

得到的结果如下：

```
* 2022-10-02 20:12:49.913 - 2022-10-02 20:12:57.963 (8050ms)
* Total:23
* 平均 qps:2.8566256961108554
* 1分钟 qps:4.0
* 5分钟 qps:4.0
* 15分钟 qps:4.0
```

>rate自己的一些总结（于2022年 9 月）：
>
>关于rate即qps， 如果要求一个指标的qps，最近精确的方式当然是每秒一个点记录count，但是这种方式对内存的消耗也是最大的，一天就是86400一个点，要获取一天的峰值qps则取这些点的最大值。精度稍微低一点的方式是一分钟记录一个count，除以 60得到一分钟内的平均qps，这样一天是1440一个点，精度最低的是一天一个点，因为一天内流量波动太大，这样得到平均qps已经没有太大的参考价值。
>
>所以qps按照分类可以有秒级qps，1分钟级qps，5 分钟级qps，小时级qps，精度越高消耗的内存也越大。



#### 4、Histogram

Histogram统计数据的分布情况，比如统计班级分数，90分以上多少人，60分以下多少人，还有最小值，最大值，中间值，还有中位数，75百分位, 90百分位, 95百分位, 98百分位, 99百分位, 和 99.9百分位的值(percentiles)。

Histogram一般用于统计耗时分布、流量大小分布情况：

```
	@Test
	public void testHistogram() {
		
		Histogram histogram = metricRegistry.histogram("redis-traffic-outgoing");
		for (int i = 1; i < 11; ++i) {
			histogram.update(i * 10);
		}
		
		//返回中位数
		System.out.println(histogram.getSnapshot().getMedian());
		//返回max
		System.out.println(histogram.getSnapshot().getMax());
	}

```



#### 5、Timer

Timer其实是 Histogram 和 Meter 的结合， histogram 某部分代码/调用的耗时， meter统计TPS。

```java
	@Test
	public void testTimer() throws InterruptedException {
		Timer timer = metricRegistry.timer("api.get.latency");
		Timer.Context ctx = timer.time();
		TimeUnit.SECONDS.sleep(5);
		long elapsed1 = ctx.stop();
		assertEquals(5000000000L, elapsed1, 1000000);
		
		Snapshot snapshot = timer.getSnapshot();
		//返回中位数
		System.out.println(snapshot.getMedian());
		//返回max
		System.out.println(snapshot.getMax());

	}
```

当我们需要统计某个函数被调用的频率（TPS）会使用Meters，当我们需要统计某个函数的执行耗时分布，会使用Histograms，当我们既要统计TPS又要统计耗时时，我们会使用Timers。



## 实践

### 1、8864本地监控

在日常使用中，一个应用的监控可以参考 Google 的 4 个黄金指标：

- Traffic (流量)
- Latency (Latency)
- Errors (Errors)
- Saturation (Saturation)

bds监控最佳实践：

```
/config
| key | value

/stats
|consumer | count | rate1 | rate5| rate15| error| 
 
/traffic
|consumer | bytes | mean | max | media| 75% | 95% |99.9% |

/latency
|consumer | mean | max | media| 75% | 95% |99.9% |
```

对于中间件的本地监控，因为我们要在本地查询，所以更合适的做法是通过一个类来封装这几个指标，比如：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221107110830718.png" alt="image-20221107110830718" style="zoom:50%;" />

代码如下：

```java
public class MetricsRegistry {
    private static final MetricRegistry INNER_REGISTRY = new MetricRegistry();
    private static final Map<String, ConsumerMetric> CONSUMER_METRIC_MAP = new ConcurrentHashMap<>();

    public static ConsumerMetric registerConsumerMetric(String name) {
        return CONSUMER_METRIC_MAP.computeIfAbsent(name, (k) -> {
            return new ConsumerMetric(name);
        });
    }

    public static Map<String, ConsumerMetric> getConsumerMetricMap(){
        return CONSUMER_METRIC_MAP;
    }
    @Data
    public static class ConsumerMetric {
        private Meter request;
        private Counter requestError;
        private Histogram traffic;
        private Counter trafficSum;
        private Histogram latency;

        ConsumerMetric(String name) {
            request = INNER_REGISTRY.meter(name + ".request");
            requestError = INNER_REGISTRY.counter(name + ".request_error");
            traffic = INNER_REGISTRY.histogram(name + ".traffic");
            trafficSum = INNER_REGISTRY.counter(name + ".traffic_sum");
            latency = INNER_REGISTRY.histogram(name + ".latency");
        }
    }
}
```

用法：

```java
    private void afterMessageProcessSuccess(ConsumerRecord<String, String> record) {
        String formattedName = getFormattedName();
        MetricsRegistry.ConsumerMetric consumerMetric = MetricsRegistry.registerConsumerMetric(formattedName);

        //meter
        consumerMetric.getRequest().mark();
        //traffic
        consumerMetric.getTraffic().update(record.value().getBytes().length);
        consumerMetric.getTrafficSum().inc(record.value().getBytes().length);
        //latency
        consumerMetric.getLatency().update(System.currentTimeMillis() - currentTimestamp);
    }
```

在legion场景中，因为我需要按照不同的维度统计，比如app维度，index维度，所以采用另外一种设计思路:

```
+----------------+     +-------------+     +-------------+
| MetricRegistry | --- | MetricGroup | --- | MetricEntry |
+----------------+     +-------------+     +-------------+
```



MetricRegistry 通过map保存MetricGroup， MetricGroup通过map保存MetricEntry  

MetricGroup 代码如下:

 ```java
 @Data
 public static class MetricGroup {
   private Map<String, MetricEntry> entryStatsMap = new ConcurrentHashMap<>();
 
   private String group;
 
   //限制MetricEntry的大小
   private int maxSize = -1;
 
   public MetricGroup(String group) {
     this.group = group;
   }
    public MetricEntry entry(String entry) {
    		return entryStatsMap.computeIfAbsent(entry, (k) -> new MetricEntry(entry));
    }
 }
 ```

MetricEntry 代码：

```java
 /**
     * 表示监控项
     */
    @Data
    public static class MetricEntry {
        private String name;
        private Map<String, Object> fieldMap = new ConcurrentHashMap<>();
        private Map<String, Counter> counterMap = new ConcurrentHashMap<>();
        private Map<String, Meter> meterMap = new ConcurrentHashMap<>();
        private Map<String, Histogram> histogramMap = new ConcurrentHashMap<>();

        public MetricEntry(String name) {
            this.name = name;
        }

        public Counter counter(String counter) {
            return counterMap.computeIfAbsent(counter, (k) -> new Counter());
        }

        public Meter meter(String meter) {
            return meterMap.computeIfAbsent(meter, (k) -> new Meter());
        }

        public Histogram histogram(String histogram) {
            return histogramMap.computeIfAbsent(histogram, (k) -> new Histogram(new ExponentiallyDecayingReservoir()));
        }
      
        public MetricEntry field(String key, Object value) {
            fieldMap.put(key, value);
            return this;
        }
    }
```

用法：

```java
MetricRegistry.group("consumer").entry("k2.tomcat.log").counter("msgCount").inc();
MetricRegistry.group("consumer").entry("k2.tomcat.log").counter("msgTraffic").inc();
MetricRegistry.group("consumer").entry("k2.tomcat.log").meter("msgTraffic").mark();
```





对于 Prometheus 因为查询不在本地，所以可以不用封装类，所以直接把各种 metric 暴露出来即可，伪代码比如：

```java
public class MetricsRegistry{
   private counter_map: Map<String,Counter>
	 private his_map: Map<String,Histogram>
     
  public Counter registerCounter(name, tags);
  
  public Histogram registerHistogram(name,tags);   
  
}
```

我们可以为每个指标打上标签，比如ip，应用名，消费者，因为 Prometheus 的 QPS直接在服务端查询，所以不存在 Meter 这个类。

### 2、Springboot 暴露Prometheus

通过 Springboot Actuator + micrometer 轻松搞定Prometheus的监控，网上有很多资料，参考阿里云文档：[Spring Boot应用如何快速接入Prometheus监控](https://help.aliyun.com/document_detail/423850.html)

## 参考

- [Intro to Dropwizard Metrics](https://www.baeldung.com/dropwizard-metrics)

- [一文搞懂 Prometheus 的直方图](https://juejin.cn/post/6844903907265642509)

    

