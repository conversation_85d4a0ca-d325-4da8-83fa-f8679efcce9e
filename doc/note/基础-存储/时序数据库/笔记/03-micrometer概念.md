## 概述

本文是对 micrometer 官方文档[concepts](https://micrometer.io/docs/concepts) 一章的翻译

## 1. 动机

Micrometer 是一个用于基于 JVM 应用程序的度量工具库。它为最流行的监控系统的 instrumentation 客户端提供了一个简单的facade，允许您检测基于 JVM 的应用程序代码，而无需锁定供应商。它旨在为您的指标收集活动增加很少甚至没有开销，同时最大限度地提高您的指标工作的可移植性。

Micrometer 不是分布式跟踪系统或事件记录器。Adrian Cole 关于  [Observability 3 Ways](https://www.dotconferences.com/2017/04/adrian-cole-observability-3-ways-logging-metrics-tracing) 的演讲很好地强调了这些不同类型系统之间的差异。

## 2. 支持的监控系统

Micrometer 包含一个带有 instrumentation SPI 的核心模块、一组包含各种监控系统实现的模块（每个都称为 registry ）和一个测试套件。关于监控系统有三个重要特征：

Dimensionality。 系统是否支持使用标签键/值对来丰富指标名称，如果系统没有维度，则它是分层的，这意味着它仅支持平面度量名称。将指标发布到分层系统时，Micrometer 会将标签键/值对集展平并将它们添加到名称中。

| Dimensional                                                  | Hierarchical                        |
| :----------------------------------------------------------- | :---------------------------------- |
| AppOptics, Atlas, Azure Monitor, Cloudwatch, Datadog, Datadog StatsD, Dynatrace, Elastic, Humio, Influx, KairosDB, New Relic, Prometheus, SignalFx, Sysdig StatsD, Telegraf StatsD, Wavefront | Graphite, Ganglia, JMX, Etsy StatsD |

Rate aggregation。在这种情况下，我们指的是在规定的时间间隔内聚合一组样本。一些监控系统期望应用程序在发布之前将某些类型的离散样本（例如计数）转换为速率，有些希望始终发送累积值，还有一些对此没有意见。

| Client-side                                                  | Server-side                                                  |
| :----------------------------------------------------------- | :----------------------------------------------------------- |
| AppOptics, Atlas, Azure Monitor, Datadog, Elastic, Graphite, Ganglia, Humio, Influx, JMX, Kairos, New Relic, all StatsD flavors, SignalFx | Prometheus, Wavefront [[1](https://micrometer.io/docs/concepts#_footnotedef_1)] |

**Publishing**。一些系统希望主动轮询应用程序以获取指标，另一些系统则希望程序定期将指标推送给它们。

| Client pushes                                                | Server polls                   |
| :----------------------------------------------------------- | :----------------------------- |
| AppOptics, Atlas, Azure Monitor, Datadog, Elastic, Graphite, Ganglia, Humio, Influx, JMX, Kairos, New Relic, SignalFx, Wavefront | Prometheus, all StatsD flavors |

从一个监控系统到另一个监控系统的期望值还有其他更小的变化，例如他们对基本测量单位（尤其是时间）的概念和度量的规范命名约定。 Micrometer 自定义您的指标以满足这些需求以及每个注册表的基础。

## 3. Registry

Meter 是用于收集有关您的应用程序的一组度量（我们单独称为度量）的接口。Micrometer 中的 Meters 是从 MeterRegistry 创建并保存在 MeterRegistry 中的，每个受支持的监控系统都有一个 MeterRegistry 的实现，registry 的创建方式因每个实现而异。

Micrometer 带有一个 SimpleMeterRegistry，它在内存中保存每个仪表的最新值，并且不会将数据导出到任何地方。如果您还没有首选的监控系统，您可以使用简单的注册表开始使用指标：

```
MeterRegistry registry = new SimpleMeterRegistry();
```

### 3.1 Composite registries

Micrometer 提供了一个 CompositeMeterRegistry，可以向其中添加多个注册表，允许您同时将指标发布到多个监控系统

```java
CompositeMeterRegistry composite = new CompositeMeterRegistry();

Counter compositeCounter = composite.counter("counter");
compositeCounter.increment(); //①

SimpleMeterRegistry simple = new SimpleMeterRegistry();
composite.add(simple); //②

compositeCounter.increment(); //③
```

### 3.2 Global registry

Micrometer 提供了一个静态全局注册表 Metrics.globalRegistry 和一组用于基于此注册表生成仪表的静态构建器。 globalRegistry 是一个复合注册表。

```java
class MyComponent {
    Counter featureCounter = Metrics.counter("feature", "region", "test"); (1)

    void feature() {
        featureCounter.increment();
    }

    void feature2(String type) {
        Metrics.counter("feature.2", "type", type).increment(); (2)
    }
}

class MyApplication {
    void start() {
        // wire your monitoring system to global static state
        Metrics.addRegistry(new SimpleMeterRegistry()); (3)
    }
}
```

## 4. Meters

Micrometer 提供了一组 Meter：Timer`, `Counter`, `Gauge`, `DistributionSummary`, `LongTaskTimer`, `FunctionCounter`, `FunctionTimer`, and `TimeGauge。 不同的meter类型代表不同数量的时间序列指标，例如，单个指标代表 Gauge， Timer 表示计时事件的计数和所有计时事件的总时间。

一个 meter 通过其名称和维度代表唯一标识，我们交替使用术语 dimensions 和 tag，而 Micrometer 接口是标签只是因为它更短，作为一般规则，应该可以使用名称作为中心(pivot)，维度允许对特定的命名指标进行切片以深入了解数据并进行推理。这意味着如果只选择名称，用户可以使用其他维度和显示值的原因进行深入研究。

## 5. Naming meters

### 5.1. Tag naming

### 5.2. Common tags

### 5.3. Tag values

tag值必须为非空。

> 注意来自用户提供的来源的标签值可能会破坏指标的基数。

## 6. Meter filters

每个 registry 都可以配置 meter filters，这使您可以更好地控制 meters 的注册方式和时间以及它们发出的统计数据类型。Meter filters具有三个基本功能：

- 拒绝（或接受）仪表注册。
- 转换仪表 ID（例如更改名称、添加或删除标签、更改描述或基本单位）
- 为某些仪表类型配置分布统计。



## 7. Rate 聚合



## 8. Counters

## 9. Gauges

## 10. Timers

## 11. Distribution summaries

## 12. Long task timers

## 13. Histograms and percentiles

## 术语表

- metrics 指标
- infrastructure 基础设施
- instrumentation 仪表
- Dimensionality 维度
- hierarchical 分层的
- Meter filters 仪表过滤器