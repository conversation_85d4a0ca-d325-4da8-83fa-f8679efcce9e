## 概述

InfluxDB是一个由InfluxData开发的开源时序型数据库。它由Go写成，着力于高性能地查询与存储时序型数据。InfluxDB被广泛应用于存储系统的监控数据。

在 [db-engines](https://db-engines.com/en/ranking/time+series+dbms) 时间时序数据库排名上，influxdb长期霸榜：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221118105434065.png" alt="image-20221118105434065" style="zoom:50%;" />

目前分为单机版和集群版。单机版开源，目前在 [github](https://github.com/influxdata/influxdb) 上有 21k+ star。集群版闭源，走商业路线，由于InfluxDB不支持分布式，现在 timeseriesdb 也有其他选择国内包括： TDengine、 ceresdb、greptime 。

## 版本历史

- InfluxDB1.x 采用了一种类似 SQL 的 InfluxQL 语言来操作数据。

- InfluxDB2.0 alpha 在2019 年 1 月推出，推出了全新的查询语言 Flux，JavaScript 语法。Flux 不是绑定在 InfluxDB 上的查询脚本语言，它是一个独立的项目，图灵完备，便于处理数据，也可以用作 InfluxDB 以外。
- TICK 是 InfluxData 平台的组件集的缩写，分别代表四大组件：Telegraf（数据收集器）、InfluxDB（时序数据库）、Chronograf（可视化 UI）和 Kapacitor（监控服务）。



## 基本概念

InfluxDB 的数据模型概念和 RDBMS 稍有不同，下面是和 MySQL 的概念对照表。

| **InfluxDB** | **MySQL** | **解释**                            |
| ------------ | --------- | ----------------------------------- |
| Buckets      | Database  | 数据桶-数据库，即存储数据的命名空间 |
| Measurement  | Table     | 度量-表                             |
| Point        | Record    | 数据点-记录                         |
| Field        | Field     | 未设置索引的字段                    |
| Tag          | Index     | 设置了索引的字段                    |

## 安装

目前公司主要还是在 1.x。进入[下载页面](https://portal.influxdata.com/downloads/)，在页面末尾选择 1.8.10版本。

InfluxDB 默认开启两个端口:

- 8086 用于和客户端通信
- 8088 用于



## 案例

下面是 hermes 配置在 grafana 的一些样例:

```
SELECT mean("p99") FROM "hermes_client_consume_delay_time" WHERE ("topic" =~ /^$topic$/ AND "group" =~ /^$groupId$/) AND $timeFilter GROUP BY time(1m) fill(0)

SELECT mean("m1_rate") FROM "hermes_client_consume_execute_time" WHERE ("topic" =~ /^$topic$/ AND "group" =~ /^$groupId$/) AND $timeFilter GROUP BY time(1m) fill(null)
```



## 参考

- [InfluxDB 通识篇](https://juejin.cn/post/6947575345570643981)
- [时序型数据库InfluxDB前世今生](https://juejin.cn/post/6844903879302184974)
- [360 influxDB 集群模式实践](https://www.infoq.cn/article/icvfemgm8wzs8po72zwr)