## 概述

Zk 接入 prometheus 监控有两种方案：

- 方案1：使用 prometheus 提供的 jmx_exporter 监控，里面包含对zk的监控，详见:[jmx_exporter](https://github.com/prometheus/jmx_exporter)
- 方案2： 从zk3.6开始，官方提供了直接对 prometheus 的支持，基于 PrometheusMetricsProvider，详见：[https://github.com/apache/zookeeper/pull/977/files](https://github.com/apache/zookeeper/pull/977/files)

开启 prometheus 需要增加配置

```properties
metricsProvider.className=org.apache.zookeeper.metrics.prometheus.PrometheusMetricsProvider
metricsProvider.httpPort=7003
metricsProvider.exportJvmInfo=true
```

通过 http://localhost:7003/metrics 即可访问

## 实现原理

zk 增加 prometheus 代码非常简单，主要用到如下几个类。

### 1、MetricsProvider

org.apache.zookeeper.metrics.MetricsProvider 。 指标提供者的抽象接口，避免直接依赖 prometheus。任何第三方服务都可以通过实现此接口来提供zk的监控能力，主要方法是getMetricsContext。代码如下：

```java
public interface MetricsProvider {
    void configure(Properties configuration) throws MetricsProviderLifeCycleException;
    void start() throws MetricsProviderLifeCycleException;
    MetricsContext getRootContext();
}
```

### 2、MetricsContext

org.apache.zookeeper.metrics.MetricsContext。指标上下文。用于获取几个记录指标的对象 Counter，Summary。代码如下:

```java
public interface MetricsContext {
    Counter getCounter(String name);
    Summary getSummary(String name, DetailLevel detailLevel);
    SummarySet getSummarySet(String name, DetailLevel detailLevel);
    void registerGauge(String name, Gauge gauge);
}
```

- Counter 用于表示数字，只能自增。比如znode数字。
- Summary 也是表示数字，但是Summary内部会自动增加 minumum, maximum, average，分位数等信息。
- Gauge 表示一个静态数字，比如集群成员数量。

上面几种类型都是接口，由具体的 MetricsProvider 来实现。

#### Counter

```java
public interface Counter {
    default void inc() {
        add(1);
    }
    void add(long delta);
    long get();
}
```

#### Gauge

```java
public interface Gauge {
    Number get();
}
```

由于 Prometheus 的Gauges 是不会自动触发更新，所以zk对gauge做了包装。

```java
private class Context implements MetricsContext {
    private final ConcurrentMap<String, PrometheusGaugeWrapper> gauges = new ConcurrentHashMap<>();

    @Override
    public void registerGauge(String name, Gauge gauge) {
        Objects.requireNonNull(name);
        gauges.compute(name, (id, prev) ->new PrometheusGaugeWrapper(id, gauge, prev != null ? prev.inner : null));
    }
}
```

PrometheusGaugeWrapper 定义了一个sample()方法

```java
private class PrometheusGaugeWrapper {
    private final io.prometheus.client.Gauge inner;
    private final Gauge gauge;
    private final String name;
    
    public PrometheusGaugeWrapper(String name, Gauge gauge, io.prometheus.client.Gauge prev) {
            this.name = name;
            this.gauge = gauge;
            this.inner = prev != null ? prev
                    : io.prometheus.client.Gauge
                    .build(name, name)
                    .register(collectorRegistry);
    }
    private void sample() {
        Number value = gauge.get();
        this.inner.set(value != null ? value.doubleValue() : 0);
    }

}
```

在Prometheus exporter收到server请求时会调用sample()更新代码。

#### Summary

```java
public interface Summary {
    void add(long value);	
}

```

其实就是不带lable的Summary。

#### SummarySet

```java
public interface SummarySet {
    void add(String key, long value);
}

```

其实就是带lable的Summary。

### 3、ServerMetrics

org.apache.zookeeper.server.ServerMetrics 包含了zookeeper 使用到的指标集合，在zk 启动时会调用metricsProviderInitialized()初始化，代码如下：

```java
public final class ServerMetrics {
    
    //这里定义了非常多指标
    public final Counter DIFF_COUNT;
    public final Summary FSYNC_TIME;
    public final Summary SNAPSHOT_TIME;
    public final Summary DB_INIT_TIME;
    public final SummarySet LEARNER_HANDLER_QP_SIZE;
    
    private final MetricsProvider metricsProvider;
    private static volatile ServerMetrics CURRENT;
    
    private ServerMetrics(MetricsProvider metricsProvider) {
        this.metricsProvider = metricsProvider;
        MetricsContext metricsContext = this.metricsProvider.getRootContext();

        FSYNC_TIME = metricsContext.getSummary("fsynctime", DetailLevel.BASIC);
        SNAPSHOT_TIME = metricsContext.getSummary("snapshottime", DetailLevel.BASIC);
        DB_INIT_TIME = metricsContext.getSummary("dbinittime", DetailLevel.BASIC);
	
        //省略后续代码
    }
    
    //对外提供的函数
    public static void metricsProviderInitialized(MetricsProvider metricsProvider) {
        LOG.info("ServerMetrics initialized with provider {}", metricsProvider);
        CURRENT = new ServerMetrics(metricsProvider);
    }
    
    public void resetAll() {
        metricsProvider.resetAllValues();
    }
}
```



注册Gauge:

```java
public class ZooKeeperServer{  
    protected void registerMetrics() {
        MetricsContext rootContext = ServerMetrics.getMetrics().getMetricsProvider().getRootContext();
        ServerStats stats = this.serverStats();
        rootContext.registerGauge("avg_latency", stats::getAvgLatency);
        rootContext.registerGauge("znode_count", zkdb::getNodeCount);
    }
}    
```



### 4、 zk记录指标

记录耗时:

```java
  ServerMetrics.getMetrics().SERVER_WRITE_COMMITTED_TIME
                    .add(Time.currentElapsedTime() - request.commitRecvTime);
```

记录LOOKING数量:

```java
 ServerMetrics.getMetrics().LOOKING_COUNT.add(1);
```



