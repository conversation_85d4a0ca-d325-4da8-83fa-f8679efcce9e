## 概述

Prometheus是一个开源的监控系统和时序数据库，使用Go语言开发，它是由 google 前员工在 2012 年创建，作为社区开源项目进行开发，并于 2015 年正式发布。2016 年，Prometheus 正式加入 Cloud Native Computing Foundation。

Prometheus的优势

- Prometheus核心部分只有一个单独的二进制文件，不存在任何的第三方依赖(数据库，缓存等等)。唯一需要的就是本地磁盘，因此不会有潜在级联故障的风险。
- Prometheus有丰富的Client库，便于用户监控服务的内部状态。
-  强大的数据模型。
-  强大的查询语言PromQL。
- 高性能。单一Prometheus Server实例而言它可以处理数以百万的监控指标。每秒处理数十万的数据点。
- 可扩展。通过使用功能分区(sharding)+联邦集群(federation)可以对其进行扩展。
- 可视化。



## 快速使用

### 概念

在 Prometheus 中，**Metric**（度量）数据是核心概念，用于收集和存储各种监控数据。每个 Metric 数据点由以下几部分组成：

- **Metric 名称**：描述了监控数据的名称，例如 `http_requests_total`。
- **标签（Labels）**：用来标识数据源的一组键值对，可以用来区分相同类型的不同实例。例如，标签可能包含 `instance="localhost:8080"` 或 `job="api_server"`。
- **值（Value）**：具体的数值数据，通常是一个浮点数。
- **时间戳（Timestamp）**：标识数据点的时间，但在大多数情况下，Prometheus 会自动分配时间戳。



### 查询

Prometheus 采用pull模式，指标通过target主动暴露，比如这是kafka export 暴露的metrics ：

```
kafka_cluster_partition_replicascount{topic="bkk.blackhole.activation",partition="0",} 3.0
kafka_cluster_partition_replicascount{topic="statics.add.qy.wechat",partition="0",} 3.0
```

可以在 Prometheus console查询指标，比如查询 kafka_server_brokertopicmetrics_totalfetchrequestspersec_count 会返回这个指标的值：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241014192730161.png" alt="image-20241014192730161" style="zoom:50%;" />

可以通过在花括号 {} 中附加逗号分隔的标签匹配器列表来进一步过滤这些时间序列。例如只返回其中一个topic：

![image-20241014192621354](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241014192621354.png)

prometheus 如何对某个tag聚合，比如下面这个 metric 有4个tag，我们希望 group by topic 统计每个topic的消息写入量：

```
kafka_log_log_logstartoffset{instance="172.16.48.179:7071", job="kafka-test", partition="0", topic="manyou.test"}
kafka_log_log_logstartoffset{instance="172.16.48.179:7071", job="kafka-test", partition="1", topic="manyou.test"}
kafka_log_log_logstartoffset{instance="172.16.48.179:7071", job="kafka-test", partition="2", topic="manyou.test"}
```

Prometheus 中，你可以使用聚合函数结合标签（tags）来进行分组。例如，对于你提供的指标 `kafka_log_log_logstartoffset`，如果你希望按 `topic` 进行聚合，可以使用以下 PromQL 查询：

```
sum(kafka_log_log_logstartoffset) by (topic)
```

不过这种方式得到的是瞬时向量，多数情况下我们希望是计算指标的变化速率并按 `topic` 聚合，需要利用rate：

```
sum(rate(kafka_log_log_logstartoffset[5m])) by (topic)
```





### 度量指标

Prometheus 里的度量指标有以下几种类型：

#### Counter

**计数器**（Counter）：一种累计型的度量指标，它是一个只能递增的数值。计数器主要用于统计类似于服务器请求数、任务完成数和错误出现次数这样的数据。

注：在Prometheus 中没有Meter这种数据类型，速率也是用Counter，通过服务端的rate函数进行计算。

#### Gauge

**计量器**（Gauge）：表示一个可以增加，可以减少，可以重置的度量指标。计量器主要用于测量类似于队列大小、内存使用量这样的瞬时数据。

#### Histogram

在大多数情况下人们都倾向于使用某些量化指标的平均值，例如 CPU 的平均使用率、页面的平均响应时间。这种方式的问题很明显，以系统 API 调用的平均响应时间为例：如果大多数 API 请求都维持在 100ms 的响应时间范围内，而个别请求的响应时间需要 5s，那么就会导致某些 WEB 页面的响应时间落到中位数的情况，而这种现象被称为**长尾问题**。

为了区分是平均的慢还是长尾的慢，最简单的方式就是按照请求延迟的范围进行分组。例如，统计延迟在 0~10ms 之间的请求数有多少， 10~20ms 之间的请求数又有多少。通过这种方式可以快速分析系统慢的原因。

Histogram 在一段时间范围内对数据进行采样，并将其计入可配置的存储桶（bucket）中，后续可通过指定区间筛选样本，也可以统计样本总数，最后一般将数据展示为直方图。

**上面这句话不太好理解，下面通过具体的示例来说明。**

假设我们想监控某个应用在一段时间内的响应时间，最后监控到的样本的响应时间范围为 0s~10s。现在我们将样本的值域划分为不同的区间，即不同的 `bucket`，每个 bucket 的宽度是 0.2s。那么第一个 bucket 表示响应时间小于等于 0.2s 的请求数量，第二个 bucket 表示响应时间大于 0.2s 小于等于 0.4s 的请求数量，以此类推。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210914203120203.png" alt="image-20210914203120203" style="zoom:50%;" />

Prometheus 的 histogram 是一种累积直方图，与上面的区间划分方式是有差别的，它的划分方式如下：还假设每个 bucket 的宽度是 0.2s，那么第一个 bucket 表示响应时间小于等于 0.2s 的请求数量，第二个 bucket 表示响应时间小于等于 0.4s 的请求数量，以此类推。也就是说，**每一个 bucket 的样本包含了之前所有 bucket 的样本**，所以叫累积直方图。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210914203821794.png" alt="image-20210914203821794" style="zoom:50%;" />

为什么是累积直方图？

通过对比可以发现，非累积的直方图更容易理解。Prometheus 为什么要这么做呢？

想象一下，如果 histogram 类型的指标中加入了额外的标签，或者划分了更多的 bucket，那么样本数据的分析就会变得越来越复杂。如果 histogram 是累积的，在抓取指标时就可以根据需要丢弃某些 bucket，这样可以在降低 Prometheus 维护成本的同时，还可以粗略计算样本值的分位数。通过这种方法，用户不需要修改应用代码，便可以动态减少抓取到的样本数量。

假设某个 histogram 类型指标的样本数据如下：
<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210914204040647.png" alt="image-20210914204040647" style="zoom:50%;" />

现在我们希望 Prometheus 在抓取指标时丢弃响应时间在 `100ms` 以下的 bucket，就可以通过下面的 relabel 配置来实现：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210914204104922.png" alt="image-20210914204104922" style="zoom:50%;" />

其中，`example_latency_seconds_bucket` 用来匹配标签 `__name__` 的值，'0.0.*' 用来匹配标签 `le` 的值，即 `le` 的值为 `0.0x`。然后将匹配到的样本丢弃。

通过这种方法，你可以丢弃任意的 bucket，但不能丢弃 `le="+Inf"` 的 bucket，因为 `histogram_quantile` 函数需要使用这个标签。

另外 histogram 还提供了 `_sum` 指标和 `_count` 指标，即使你丢弃了所有的 bucket，仍然可以通过这两个指标值来计算请求的平均响应时间。

通过累积直方图的方式，还可以很轻松地计算某个 bucket 的样本数占所有样本数的比例。例如，想知道响应时间小于等于 1s 的请求占所有请求的比例，可以通过以下公式来计算：

```
example_latency_seconds_bucket{le="1.0"} / ignoring (le) example_latency_seconds_bucket{le="+Inf"}
```

**分位数计算**

Prometheus 通过 histogram_quantile 函数来计算分位数（quantile），而且是一个预估值，并不完全准确，因为这个函数是假定每个区间内的样本分布是线性分布来计算结果值的。预估的准确度取决于 bucket 区间划分的粒度，粒度越大，准确度越低。以下图为例：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210914204541442.png" alt="image-20210914204541442" style="zoom:50%;" />

假设有 10000 个样本，第 9501 个样本落入了第 8 个 bucket。第 8 个 bucket 总共有 368 个样本，其中第 9501 个样本在该 bucket 中属于第 93 个样本。
根据 Prometheus 源代码文件 promql/quantile.go 第 108 行的公式：

```java
return bucketStart + (bucketEnd-bucketStart)*float64(rank/count)
```

们可以计算（quantile=0.95）的样本值为：1.45

这个值已经很接近精确的分位数值了。关于 histogram_quantile 函数的详细使用方式，请参考：[PromQL 内置函数](https://yunlzheng.gitbook.io/prometheus-book/parti-prometheus-ji-chu/promql/prometheus-promql-functions)。



#### Summary

我们先来说说 histogram 存在的问题。

histogram并不会保存数据采样点值，每个bucket只有个记录样本数的counter，即histogram存储的是区间的样本数统计值。

在prometheus服务端基于这么有限的数据做百分位估算，所以的确不是很准确，summary就是解决百分位准确的问题而来的。summary直接存储了 quantile 数据，而不是根据统计区间计算出来的。



### PromQL

#### 基本语法

PromQL是Prometheus内置的数据查询语言，当我们直接使用监控指标名称查询时，可以查询该指标下的所有时间序列。如：

```
http_requests_total
```

等同于：

```
http_requests_total{}
```

该表达式会返回指标名称为http_requests_total的所有时间序列：

```
http_requests_total{code="200",handler="alerts",instance="localhost:9090",job="prometheus",method="get"}=(20889@1518096812.326)
http_requests_total{code="200",handler="graph",instance="localhost:9090",job="prometheus",method="get"}=(21287@1518096812.326)
```

可以通过在花括号 {} 中附加逗号分隔的标签匹配器列表来进一步过滤这些时间序列。

例如，如果我们只需要查询所有http_requests_total时间序列中满足标签instance为localhost:9090的时间序列，则可以使用如下表达式：

```
http_requests_total{instance="localhost:9090"}
```

总共有以下几种标签匹配运算符：

- `=`: 选择与字符串完全相同的标签(labels)。
- `!=`: 选择不等于字符串的labels。
- `=~`:选择与字符串正则表达式匹配的labels。
- `!~`: 选择与字符串正则表达式匹配的labels。

#### instant vector 和 range vector

直接通过类似于PromQL表达式查询时间序列时，返回值中只会包含该时间序列中的最新的一个样本值，这样的返回结果我们称之为**瞬时向量(Instant vector )**

例如，我们直接查询  jvm_gc_collection_seconds_sum 对于同一个指标只会有一条数据：

```
jvm_gc_collection_seconds_sum{gc="ConcurrentMarkSweep",instance="***********:7003",job="zk"}	
141.34
```



如果我们想过去一段时间范围内的样本数据时，我们则需要使用**区间向量表达式(Range vector)**，时间范围通过时间范围选择器`[]`进行定义。

例如，通过 jvm_gc_collection_seconds_sum[1m] 可以查询出最近1分钟内的所有样本数据：

```
jvm_gc_collection_seconds_sum{gc="ConcurrentMarkSweep",instance="***********:7003",job="zk"}	
141.34 @1631106494.675
141.34 @1631106499.675
141.34 @1631106504.675
141.34 @1631106509.675
141.34 @1631106514.675
141.34 @1631106519.675
141.34 @1631106524.675
141.34 @1631106529.675
141.34 @1631106534.675
141.34 @1631106539.675
141.34 @1631106544.675
141.34 @1631106549.675
```

这里返回的结果是每5秒一条数据，每条数据后面会带上timestamp，返回的区间向量和部分内置函数可以配合使用。

PS:同样1分钟返回的数据条数不固定，只保证头和尾相差一分钟。

#### 内置函数

increase 和 rate 这两个函数是PromQL最常用的内置函数，它们都接收一个区间向量参数，区别是increase计算的是指定时间区间内的增量，rate计算的是单位时间每秒的增长速率。

**比如假定以每秒5次的速率请求neptune_inbound_count指标**。

通过increase计算一分钟的增量，理论是5*60=300

```
increase(neptune_inbound_count[1m])
```

输出：

```
{app="neptune-server",instance="172.30.130.245:8090"}	293.3333333333333
```

使用rate函数，理论是5秒

```
rate(neptune_inbound_count[1m])
```

输出：

```
{app="neptune-server",instance="172.30.130.245:8090"}	4.977445948047909
```

因为rate是每秒的增长量，所以参数调大对于结果没有变化：

```
rate(neptune_inbound_count[3m])
```

输出：

```
{app="neptune-server",instance="172.30.130.245:8090"}	4.939393939393939
```

反应每秒变化趋势时使用 rate，有些情况使用increase也比较合理，比如每30秒GC耗时情况:

```
increase(jvm_gc_collection_seconds_sum{instance="10.1.160.23:7003"}[30s])
```

详细说明参考[这篇文档](https://yunlzheng.gitbook.io/prometheus-book/parti-prometheus-ji-chu/promql/prometheus-promql-functions)

#### 正则表达式

用于查询多个条件，比如：

```
pod=~"(hermes-proxy-fc6d7f496-xchk6|hermes-proxy-fc6d7f496-lqfsk)"
```

参考 [An Intro to PromQL: Basic Concepts & Examples](https://logz.io/blog/promql-examples-introduction/)

#### 聚合

Prometheus 聚合操作符参考[文档](https://yunlzheng.gitbook.io/prometheus-book/parti-prometheus-ji-chu/promql/prometheus-aggr-ops)



聚合的基本语法：

 ```
<aggr-op>([parameter,] <vector expression>) [without|by (<label list>)]
 ```

举个例子：

按照集群统计每个集群的流量：

```
sum (rate(hermes_proxy_produce_msg_count_total[1m])) by (kubernetes_name)
```

按照 topic 计算每个topic的每秒字节数的总量：

```java
sum (rate(hermes_proxy_consume_msg_bytes_total{kubernetes_name="hermes-proxy"}[1m])) by(topic)
```

如果topic 比较多会导致数据非常多，可以通过topk限制数据量，查询流量前5的topic:

```
topk(5,sum by(topic)(rate(hermes_proxy_consume_msg_bytes_total{kubernetes_name="hermes-proxy"}[1m])))
```

输出:

```properties
{topic="growth.statis.ingest.sending.wc"}	263637.1333333333
{topic="wdp.hdfsaudit2kafka"}	228867.46666666662
{topic="growth.statis.ingest.sending"}	218538.00000000003
{topic="stanlee.iceye.source.mapped"}	124158.1111111111
{topic="ds.living.room.user.status.data"}	96596.66666666667
```

我为了验证上述查询是否正确， 查询 stanlee.iceye.source.mapped 的sum

```
sum(rate(hermes_proxy_consume_msg_bytes_total{kubernetes_name="hermes-proxy",topic="stanlee.iceye.source.mapped"}[1m])) by (topic)
```

输出：

```properties
{topic="stanlee.iceye.source.mapped"}	124158.1111111111
```

结果能对上。

常见聚合函数包括：

- sum (求和)
- min (最小值)
- max (最大值)
- avg (平均值)
- stddev (标准差)
- stdvar (标准方差)
- count (计数)
- count_values (对value进行计数)
- bottomk (后n条时序)
- topk (前n条时序)
- quantile (分位数)



#### 时间位移

在瞬时向量表达式或者区间向量表达式中，都是以prometheus当前系统时间为基准进行查询：

```
http_request_total{} # 瞬时向量表达式，选择当前最新的数据
http_request_total{}[5m] # 区间向量表达式，选择以当前时间为基准过去5分钟内的所有数据
```


而如果我们想查询5分钟之前的最新数据，或者想查询昨天的所有数据呢?  这个时候我们就可以使用位移操作，位移操作的关键字为offset，例如：

```
 http_request_total{} offset 5m
 http_request_total{}[1d] offset 1d
```

### Grafana 

这里以 zookeeper 为例来介绍如何把 Prometheus 和 grafana 整合到一起。首先假设 zookeeper已经暴露好了 exportor，具体细节详见zk相关文档。

#### 1、Prometheus 添加 ZkExporter

![image-20210426175258296](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210426175258296.png)

如图所示，这里添加了3个zk节点的exporter

#### 2、grafana 导入zk模板

[这里有一个](https://grafana.com/grafana/dashboards/10465) grafana 模板可以下载之后导入到本地 grafana，效果如下:

![image-20210426175701586](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210426175701586.png)



#### 3、调整指标

有可能默认的指标是不对的，这里以 jvm_gc_collection_seconds_sum 为例，30s一个点的指标表达式为：

```
increase(jvm_gc_collection_seconds_sum[30s])
```

这里表示30秒内jvm_gc_collection_seconds_sum的变化情况。具体可以看后面PromQL介绍。



## 安装

Prometheus基于Golang编写，编译后的软件包，不依赖于任何的第三方依赖。用户只需要下载对应平台的二进制包，解压并且添加基本的配置即可正常启动Prometheus Server。

### 1、安装Prometheus Server

可以选择通过docker或者从https://prometheus.io/download/找到最新版本的Prometheus Sevrer软件包。

### 2、配置

从官方包中获取默认的 prometheus.yml 

基本配置

```yaml
global:
  scrape_interval:     10s # Scrape interval to every 10 seconds. Default value is every 1 minute.
```

添加job

```yaml
  - job_name: "middleware-benchmark"

    # metrics_path defaults to '/metrics'
    # scheme defaults to 'http'.
    metrics_path: "/actuator/prometheus"
    static_configs:
      - targets: ["*************:8080"]
```



### 3、启动

把 prometheus.yml 复制到/tmp 目录 (注：复制到/tmp的原因是docker默认会共享此目录) ，然后执行如下命令

```
docker run \
    -p 9090:9090 \
    -v /tmp/prometheus.yml:/etc/prometheus/prometheus.yml \
    prom/prometheus
```

请求 http://localhost:9090/config 能看到我们的配置.

## 客户端接入

### pom依赖

```java
<prometheus.version>0.10.0</prometheus.version>        
<dependency>
    <groupId>io.prometheus</groupId>
    <artifactId>simpleclient</artifactId>
     <version>${prometheus.version}</version>
</dependency>
<dependency>
    <groupId>io.prometheus</groupId>
    <artifactId>simpleclient_hotspot</artifactId>
     <version>${prometheus.version}</version>
</dependency>
<dependency>
    <groupId>io.prometheus</groupId>
    <artifactId>simpleclient_httpserver</artifactId>
     <version>${prometheus.version}</version>
</dependency>
```

### Counter 用法

```java
 class YourClass {
     static final Counter requests = Counter.build()
         .name("requests_total").help("Total requests.").register();
     static final Counter failedRequests = Counter.build()
         .name("requests_failed_total").help("Total failed requests.").register();

     void processRequest() {
        requests.inc();
        try {
          // Your code here.
        } catch (Exception e) {
          failedRequests.inc();
          throw e;
        }
     }
   }
 }
```

### Gauge 用法

```java
 class YourClass {
     static final Gauge inprogressRequests = Gauge.build()
         .name("inprogress_requests").help("Inprogress requests.").register();

     void processRequest() {
        inprogressRequests.inc();
        // Your code here.
        inprogressRequests.dec();
         //设置
        inprogressRequests.set(10)
     }
   }
 }
```

Gauge 和 Counter的区别是可增，可减，可设置。

### Histogram 用法

```java
 class YourClass {
     static final Histogram requestLatency = Histogram.build()
         .name("requests_latency_seconds").help("Request latency in seconds.").register();
     void processRequest(Request req) {
        Histogram.Timer requestTimer = requestLatency.startTimer();
        try {
          // Your code here.
        } finally {
          requestTimer.observeDuration();
        }
        //也可以自己计算duration, duration可以是耗时, 也可以是byteSize 
        histogram.observe(duration);
     }
    
   }
 }
 
```

可以手动设置bucket

```java
private static double[] timeBucket = new double[]{10, 20, 30, 40, 50, 60, 70, 80, 90, 100};  
private static Histogram requestLatency = = Histogram.build().buckets(buckets).name(name).help().register()
```

### HTTPServer 用法

```java
public final class PrometheusBootService implements MetricsBootService {
    private HTTPServer server;
    private volatile AtomicBoolean registered = new AtomicBoolean(false);

    @Override
    public void start(final MetricsConfig metricsConfig, final MetricsRegister metricsRegister) {
        startServer(metricsConfig);
        MetricsReporter.register(metricsRegister);
    }

    @Override
    public void stop() {
        if (server != null) {
            server.stop();
            registered.set(false);
            CollectorRegistry.defaultRegistry.clear();
        }
    }

    private void startServer(final MetricsConfig metricsConfig) {
        if (!registered.compareAndSet(false, true)) {
            return;
        }
        int port = metricsConfig.getPort();
        try {
            server = new HTTPServer(port, true);
            LOG.info("Monitor server started at port {}", port);
        } catch (final IOException ex) {
            LOG.error("Prometheus metrics HTTP server start fail", ex);
        }
        try {
            // JVM exports
            DefaultExports.initialize();
            // also can init define erporter
        } catch (Throwable t) {
            LOG.warn("Unable to initialize JVM exports.", t);
        }
    }
}
```

小结

- Gauge、Counter、Histogram 均继承于 Collector，这三种类型在调用register()方法创建过程中都会自动注入到 CollectorRegistry.defaultRegistry。
- HTTPServer 启动时会自动暴露 CollectorRegistry.defaultRegistry 中注册的Collector。
- 在实践中一般不会直接把三种指标定义在类中，而是定义一个MetricsRegister用于注册和使用指标。

### 整合Dropwizard

Prometheus Client 提供了把 Dropwizard 整合到 Prometheus，参考[exposing-dropwizard-metrics-to-prometheus](https://www.robustperception.io/exposing-dropwizard-metrics-to-prometheus)



## 实现原理

### 架构

我们先解释一些名词术语再来看整体架构

#### 基本概念

在 Prometheus 术语中，你可以抓取的endpoint被称为instance，通常对应的是一个进程，多个相同目的的instance组合成一个job，例如一个有4个实例的API server job:

```
job: api-server
instance 1: *******:5670
instance 2: *******:5671
instance 3: *******:5670
instance 4: *******:5671
```

在prometheus.yml配置文件中，我们已经用到了job和instance概念。

```yaml
scrape_configs:
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
  - job_name: 'node'
    static_configs:
      - targets: ['localhost:9100']
```

下图展示Prometheus的基本架构：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/microservice/5.png" alt="5" style="zoom:50%;" />

#### Prometheus Server

Prometheus Server是Prometheus组件中的核心部分，负责实现对监控数据的抓取(Retrieval)，存储(TSDB)以及查询(HTTP Server)。 Prometheus Server可以通过静态配置管理监控目标实例，也可以配合使用Service Discovery的方式动态管理监控目标。

关于prometheus server 内部架构的说明可以[官方文档](https://github.com/prometheus/prometheus/blob/main/documentation/internal_architecture.md)

![image-20210908213936744](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210908213936744.png)

#### Exporters

广义上讲所有可以向Prometheus提供监控样本数据的程序都可以被称为一个Exporter。而Exporter的一个实例称为target，如下所示，Prometheus通过轮询的方式定期从这些target中获取样本数据:

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/microservice/6.png" alt="6" style="zoom:50%;" />

Prometheus社区提供了丰富的Exporter实现，涵盖了从基础设施，中间件以及网络等各个方面的监控功能。这些Exporter可以实现大部分通用的监控需求。下表列举一些社区中常用的Exporter：

| 范围     | 常用Exporter                                                 |
| -------- | ------------------------------------------------------------ |
| 数据库   | MySQL Exporter, Redis Exporter, MongoDB Exporter, MSSQL Exporter等 |
| 硬件     | Apcupsd Exporter，IoT Edison Exporter， IPMI Exporter, Node Exporter等 |
| 消息队列 | Beanstalkd Exporter, Kafka Exporter, NSQ Exporter, RabbitMQ Exporter等 |
| 存储     | Ceph Exporter, Gluster Exporter, HDFS Exporter, ScaleIO Exporter等 |
| HTTP服务 | Apache Exporter, HAProxy Exporter, Nginx Exporter等          |
| API服务  | AWS ECS Exporter， Docker Cloud Exporter, Docker Hub Exporter, GitHub Exporter等 |
| 日志     | Fluentd Exporter, Grok Exporter等                            |
| 监控系统 | Collectd Exporter, Graphite Exporter, InfluxDB Exporter, Nagios Exporter, SNMP Exporter等 |
| 其它     | Blockbox Exporter, JIRA Exporter, Jenkins Exporter， Confluence Exporter等 |

Exporter 既可以作为独立程序运行，也可以集成到应用程序中。

#### AlertManager

在Prometheus Server中支持基于PromQL创建告警规则，如果满足PromQL定义的规则，则会产生一条告警，而告警的后续处理流程则由AlertManager进行管理。在AlertManager中我们可以与邮件，Slack等等内置的通知方式进行集成，也可以通过Webhook自定义告警处理方式。AlertManager即Prometheus体系中的告警处理中心。

#### PushGateway

由于Prometheus数据采集基于Pull模型进行设计，因此在网络环境的配置上必须要让Prometheus Server能够直接与Exporter进行通信。 当这种网络需求无法直接满足时，就可以利用PushGateway来进行中转。可以通过PushGateway将内部网络的监控数据主动Push到Gateway当中。而Prometheus Server则可以采用同样Pull的方式从PushGateway中获取到监控数据。

### 服务发现

回忆最开始安装启动时候，我们是通过配置文件方式管理job，在应用大规模部署下，采用这种方式监控肯定不现实，prometheus实际提供了[服务发现机制](https://yunlzheng.gitbook.io/prometheus-book/part-ii-prometheus-jin-jie/sd)来解决这类问题，同时在k8s下有专门的机制:[监控Kubernetes集群](https://yunlzheng.gitbook.io/prometheus-book/part-iii-prometheus-shi-zhan/readmd/use-prometheus-monitor-kubernetes)，具体到挖财的环境是通过对service增加一个annotation属性来实现服务发现，网上也有很多资料，可以goolge: **prometheus k8s service monitor**，比如[kubernetes-monitoring-prometheus-operator-part3/](https://sysdig.com/blog/kubernetes-monitoring-prometheus-operator-part3/)

### 存储

- [蚂蚁金服-Prometheus 存储引擎分析](https://mp.weixin.qq.com/s/wwKMdVrHd5_s3eNE_Hg4Vg)
- [伴鱼技术-Prometheus 存储层的演进](https://tech.ipalfish.com/blog/2020/03/31/the-evolution-of-prometheus-storage-layer/)
- [youtub视频-Prometheus如何存储]

## 总结

### 方案对比

Telegraf。 Telegraf 是InfluxDB 推出了类似Prometheus 的指标收集系统，通过灵活可配置的插件形式，采集和上报被监控IT资源的指标数据，它的角色类似Prometheus的Exporter，只是独立部署的进程。

### Prometheus存在的不足

随着数据的增长， Prometheus单机在数据时效性、持久化和存储可扩展性也会面临挑战，所以不少互联网企业的时序系统都会针对Prometheus做二次开发，或者另起炉灶，可以参考这篇： [Prometheus 构建一体化监控平台的最佳实践](https://mp.weixin.qq.com/s/12pL7F178ePLWPwWM-yVww) ，通过算子下推、压缩算法解决查询瓶颈，**高基数问题**，相关优化也可以参看prometheus   CeresDB。

## 参考

- [Prometheus book](https://yunlzheng.gitbook.io/prometheus-book)
- [官方文档](https://prometheus.io/docs/introduction/overview/)
- [分位数算法总结](https://caorong.github.io/2020/08/03/quartile- algorithm/)
- [推荐How Cloudflare runs Prometheus at scale](https://blog.cloudflare.com/author/lukasz/)