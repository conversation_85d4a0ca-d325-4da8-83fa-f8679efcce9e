## 概述

在metric中经常会接触到 quantile(分位数) 和 histogram(直方图)，这两个概念总是一起出现，他们又有什么区别和联系呢？本文对这两个概念进行总结

## 什么是 histogram

在理解 quantile 之前我们先搞清楚什么是 histogram？查阅资料的一些定义：

- A **histogram** is a graphical representation of data points organized into user-specified ranges.Similar in appearance to a bar graph  -[investopedia](https://www.investopedia.com/terms/h/histogram.asp)
- A **histogram** is an approximate representation of the [distribution](https://www.wikiwand.com/en/Frequency_distribution) of numerical data. -[wikiwand](https://www.wikiwand.com/en/Histogram)
- A **histogram** is a type of chart that shows the frequency distribution of [data points](https://www.techtarget.com/whatis/definition/data-point) across a continuous range of numerical values - [techtarget](https://www.techtarget.com/searchsoftwarequality/definition/histogram)

这里总结了一些关键词：

- 使用柱状图表示(a bar graph)
- 反应了数字频率的分布（frequency distribution）这个其实就是指一个数字出现的次数在总数量的占比，比如班级考试里60分出现了10 次。
- 用户定义范围( user-specified ranges)

总结下来就是直方图是组织成用户指定范围的数据点的图形表示，外观类似于条形图。

例如，针对城镇人口统计的人口普查可能会使用直方图来显示年龄在 0 - 10、11 - 20、21 - 30、31 - 40、41 - 50、51 -60、 61 - 70 和 71 - 80

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230610000002955.png" alt="image-20230610000002955" style="zoom:50%;" />

使用histogram可以缩小统计数量使得观察更容易，比如图中我们能一眼看出41-50人口最多。



## 什么是 Percentile

首先我们看 percentile 的定义:

- A percentile (or a centile) is a measure used in statistics indicating the value **below** which a given percentage of observations in a group of observations fall. percentile 百分位数是统计中使用的一种度量，表示一组观察值中给定百分比的观察值低于该值。
- Percentiles give you a deeper insight into how your system is behaving. -[cloudprober](https://cloudprober.org/docs/how-to/percentiles/) 

翻译成人话就是一堆有序数字中第百分比位置的数字是什么，比如p50取第50%的那个值作为TP50的值。求 percentile 要求对观察的数字排序，如果数据量很多，比如网站一天 10 亿请求，如果要对10 亿请求的延迟求分位数就需要保存 10亿个数字，内存消耗太大了。所以计算分位数有一些算法，其中一种算法是利用 histogram 的特性求percentile，因为histogram是按bucket划分的，比如我们可以预先按照 0.1秒，0.5秒，1秒，5 秒，10 秒，60 秒定义6 个bucket，这样计算分位数的时候就不需要对原始数据排序之后，而是对bucket进行计算，具体算法我后面会讲。所以 histogram 和 percentile 的关系是求percentile可以通过histogram实现。

除了通过 histogram 实现percentile，另外还有一些算法，比如：

- 初始方案最大的问题是内存消耗，所以也可以对**数据抽样**，比如插入100次随机抽样 1 次，这样数据规模减少 100 倍，这种算法思想叫随机算法。dropwizard的UniformReservoir就是这种
- 另外比较常见的是CKMS算法也是prometheus 的summary 使用的算法

## 如何实现 histogram percentile

### 1、定义bucket

bucket 表示用户定义的数值范围，比如 0.1秒，0.5秒，1秒，5 秒，10 秒，60 秒，我们需要对每个bucket计数

```java
public class Histogram {
	   private Bucket[] buckets;
	   
		 public Histogram(long[] bucketLimits) {
        this.buckets = createBucket(bucketLimits);
    }

    private Bucket[] createBucket(long[] bucketLimits) {
        Bucket[] buckets = new Bucket[bucketLimits.length];
        long last = -1;
        for (int i = 0; i < bucketLimits.length; ++i) {
            long value = bucketLimits[i];
            buckets[i] = new Bucket(value);
            if (value < 0) {
                throw new IllegalPathStateException("value " + value + " must not be negative");
            }
            if (value < last) {
                throw new IllegalPathStateException("value " + value + " must greater than last " + last);
            }
            last = value;
        }
        return buckets;
    }
  
  	private static class Bucket {
        private long value;
        private AtomicLong counter;
		}
}

```



### 2、添加值

添加一个值需要找到对应的bucket，比如 2 秒，对应的bucket应该是buckets[3]，这里通过遍历buckets找到大于value的bucket

```java
    public void add(long value) {
        getBucket(value).counter.incrementAndGet();
        valueCounter.incrementAndGet();//记录value的条数
    }

    private Bucket getBucket(long value) {
        for (Bucket bucket : buckets) {
            if (bucket.value >= value) {
                return bucket;
            }
        }
        return buckets[buckets.length - 1];
    }
```



### 3、计算分位数

本质还是找到一批数中的第N个, 不过这里通过bucket计数的方式实现，逻辑如下

1. 先计算percent对应的条数，比如当前插入了200条数据，p75就是200*0.75=150；
2. 然后迭代bucket，累加bucket保存的value，比如假设第一个bucket 0.1秒已经保存超过 150 条，那么p75肯定就是0.1秒，如果小于 150 秒，继续迭代累加直到超过为止。

```java
		public long getPercentile(int percent) {
        long percentSize = (long) Math.ceil(valueCounter.get() * percent / 100.0);
        log.info("percent {} valueSize {}", percent, percentSize);
        long valueSize = 0;
        for (Bucket bucket : buckets) {
            valueSize += bucket.counter.get();
            if (valueSize >= percentSize) {
                return bucket.value;
            }
        }
        return -1;
    }
```

## 开源实现

这里我简单记录了一些部分开源Histogram：

- com.netflix.stats.distribution.Histogram
- io.prometheus.client.Histogram
- org.HdrHistogram.HdrHistogram
- com.taobao.tlog.disruptor.collections.Histogram
- com.alibaba.druid.util.Histogram
- org.apache.kafka.common.metrics.stats.Histogram

### 1、基于bucket版本

com.netflix.stats.distribution.Histogram ，disruptor.collections.Histogram 都是基于bucket实现Histogram。

### 2、dropwizard/metrics实现

dropwizard/metrics的Histogram不是基于预分桶实现，而是基于数据抽样，Histogram的构造函数中需要传递一个 Reservoir。

所谓[Reservoir Sampling](https://www.geeksforgeeks.org/reservoir-sampling/)，字面意义就是水库抽样的意思。直方图用来衡量一个数据集的统计学分布。在大流量场合中，不可能在内存中保留数 据全集，所以需要抽样。

内置的数据抽样有以下几种实现：

- ExponentiallyDecayingReservoir：基于指数级别的抽样算法，根据更新时间与开始时间的差值转化为权重值，权重越大数据被保留的几率越大。
- UniformReservoir：随机抽样，随着更新次数的增加，数据被抽样的概率会减少。
- SlidingWindowReservoir：滑动窗口抽样，总是保留最新的统计数据。
- SlidingTimeWindowReservoir：滑动时间窗口抽样，总是保留最近时间段的统计数据。

若使用ExponentiallyDecayingReservoir和SlidingTimeWindowReservoir，需要注意容量，底层并不会限制容量大小。若服务流量大，可能会占用很多内存。

默认是ExponentiallyDecayingReservoir

### 3、Prometheus histogram

Prometheus 的 histogram也是基于bucket实现，不过Prometheus的的 percentile 计算是在服务端计算的。可以通过如下代码验证：

```java
 		@Test
    public void testHistogram() {
        Histogram histogram = Histogram.build()
                .buckets(10, 20, 30, 40, 50, 60, 70, 80, 90, 100)//定义bucket
                .name("latency")
                .help("latency")
                .register();

        for (int i = 0; i < 100; ++i) {
            histogram.observe(i);
        }
        for (int i = 0; i < 100; ++i) {
            histogram.observe(5);
        }
      	//输出内容
        for(Collector.MetricFamilySamples samples :histogram.collect()){
            System.out.println(JSON.toJSONString(samples));
        }

    }
```

这段代码保存 200 个值，数据分布为：

```
{
10:111,
20:10,
30:10,
40:10,
50:10,
60:10,
70:10,
80:10,
90:10,
100:9
}
```

Prometheus 代码输出内容为：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230610005712221.png" alt="image-20230610005712221" style="zoom:60%;" />

可以看到Prometheus只会输出分桶信息，并且内容和实际不太一样，Prometheus的 histogram 是一种累积直方图，它的**每一个 bucket 的样本包含了之前所有 bucket 的样本**，所以叫累积直方图

### 4、Prometheus summary

前面我们说过求percentile有多种算法，基于bucket histogram的算法只是一种，我问了下chartGPT，它告诉我：

>
>计算分位数的常见算法有以下几种：
>
>1. 排序法（Sorting method）：将数据集进行排序，然后根据分位数的位置直接取对应的值。这种方法简单直接，但对于大型数据集可能会有较高的计算开销。
>2. 近似法（Approximation method）：使用一些近似算法来估计分位数，例如CKMS算法、Greenwald-Khanna算法等。这些算法通过维护有限的样本集合来估计分位数，具有较低的计算复杂度和内存需求。
>3. 插值法（Interpolation method）：在已排序的数据集上，通过插值方法估计分位数的值。常见的插值方法包括线性插值、分段线性插值、三次样条插值等。
>4. 分组法（Grouping method）：将数据集分成多个组，然后根据组的统计信息来估计分位数。例如，分位数可通过组中位数的加权平均值来估计。
>5. 基于分布函数的法（Distribution-based method）：假设数据集服从某种分布，根据分布函数来计算分位数。例如，对于正态分布，可以使用正态分布的累积分布函数（CDF）来计算分位数。



Prometheus summary 是基于CKMS算法，其基本思想是 #待梳理todo (2023-06-11)

参考资料：

- [How summary metrics work in Prometheus](https://grafana.com/blog/2022/03/01/how-summary-metrics-work-in-prometheus/)



## 参考

- [维基百科-Histogram](https://www.wikiwand.com/en/Histogram)
- [维基百科-Percentile](https://www.wikiwand.com/en/Percentile)
- [cloudprober](https://cloudprober.org/docs/how-to/percentiles/)
- [Bucketing technique for calculating median and k-th percentile of a large dataset](https://medium.com/@mnylen/bucketing-technique-for-calculating-median-and-k-th-percentile-of-a-large-dataset-33199af2538c)
- [statistical data distributions](https://machinelearningmastery.com/statistical-data-distributions/)

