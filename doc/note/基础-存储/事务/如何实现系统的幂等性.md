## 概述

众所周知，构建分布式应用所面临的复杂度远远超出集中式的单一应用，导致复杂性的因素有很多，在此只提其中一点：网络的不可靠性，在单一进程内部，对一个函数的调用，结果只有两种——成功和失败，失败的情况下，调用者可以决定做一些事情弥补。但是在跨进程的调用中，对一个远程（也可以在同一个节点上）进程上运行的函数调用除了会得到成功和失败，还会有第三种的情况——超时，这个现象被称为分布式的三态。这也是困扰分布式应用构建的最核心因素之一，很多分布式应用的复杂度之所以上升这么多也是因为三态之中的超时引起的。

简单看看超时给我们带来的困扰，进程A调用进程B上的函数f，对于成功和失败的结果，相信和单机下一样，进程A都可以进行很好地的处理，因为结果是很明确的。如果进程A调用f之后，在允许的等待最大时间内没有返回结果，就是调用超时了，此时进程A能做什么？其实进程A什么都做不了，因为超时是一个不明确的结果——成功和失败都有可能。详细解释下可能的情况：

- 成功的情况：进程A把数据通过网络传输到进程B上，f执行成功，通过网络返回执行结果给进程A，可是网络不太好，传输失败了，进程A并未在指定时间内收到结果，认为超时了。 
- 失败的情况：情况和成功的情况差不多，只是f执行失败了，但是结果依然传输失败，进程A也认为执行超时了。
- 未执行的情况：进程A的数据发送到进程B所在的节点过程中网络失败了，或者发送到了进程B所在的机器上，但是进程B没有消费掉在TCP 网络层的数据等等 ；

由此可见，进程A对于超时确实无能为力，有太多的可能存在的情况了。但是分布式协作过程中又必须解决这个问题，不然分布式应用是没意义的，这种情况下，一般会采用让进程A尝试重试——即重复发起之前的调用。但是这样也可能会带来问题，因为超时的那次调用可能已经成功了，再次以同样的参数调用f会不会带来额外的问题？这就引出本文的主角——幂等性。

## 幂等

幂等（idempotent、idempotence）是一个数学与计算机学概念，常见于抽象代数中。

在编程中，一个幂等操作的特点是其任意多次执行所产生的影响均与一次执行的影响相同。所谓“影响相同”，不是要求返回值完全相同，而且是指后续多余的调用对系统的数据一致性不造成破坏。

- 对于写入类操作，如果第一次写入是成功的，后续的写入应该抛出异常或者空操作，或者执行了写入但是未对数据造成变化。
- 对于读取类操作，需要保证其实现上是真正的读取，不能在读操作中夹带写操作。

幂等函数，或幂等方法，是指可以使用相同参数重复执行，并能获得相同结果的函数。这些函数不会影响系统状态，也不用担心重复执行会对系统造成改变。例如，“getUsername()和setTrue()”函数就是一个幂等函数。

用通俗的话讲：就是针对一个操作，不管做多少次，产生效果或返回的结果都是一样的。接口的幂等性实际上就是接口可重复调用，在调用方多次调用的情况下，接口最终得到的结果是一致的。有些接口可以天然的实现幂等性，比如查询接口，对于查询来说，你查询一次和两次，对于系统来说，没有任何影响，查出的结果也是一样。

举几个例子：

1. 前端重复提交选中的数据，应该后台只产生对应这个数据的一个反应结果。
2. 我们发起一笔付款请求，应该只扣用户账户一次钱，当遇到网络重发或系统bug重发，也应该只扣一次钱；
3. 创建业务订单，一次业务请求只能创建一个，创建多个就会出大问题。

## 幂等场景

需要实现幂等性的典型场景有以下两种：

- 客户端发起的请求可能需要重试，请求的后端处理需要保证幂等；
- 后端系统使用同步RPC调用或异步消息实现分布式事务，消息的消费者需要保证幂等；

可能会发生重复请求或消费的场景，在微服务架构中是随处可见的，以下是几个常见场景：

- 网络波动：因网络波动，可能会引起重复请求；
- 分布式消息消费：任务发布后，使用分布式消息服务来进行消费；
- 用户重复操作：用户在使用产品时，可能会无意的触发多笔交易，甚至没有响应而有意触发多笔交易；
- 未关闭的重试机制：因开发人员、测试人员或运维人员没有检查出来，而开启的重试机制（如Nginx重试、RPC通信重试或业务层重试等）；

广义上的RPC，包括客户端对服务端的api调用、后端系统的内网调用、跨机房调用等。一次RPC大体上包括三个步骤：发送请求、执行过程、接收响应。由于网络传输存在不确定性，导致RPC调用存在一个陷阱，即有可能出现第一、第二步都成功、第三步失败的情况，此时RPC的调用方由于接收不到结果，无法判断被调用方是否已经完成过程调用，只能按失败处理。

通常RPC调用方会针对网络失败进行重试。在上述情况下，如果远端代码不具备幂等性，却进行了重试，将导致系统的数据一致性遭到破坏，本该只执行一次的事务被执行了两次。

对于异步消息的消费者来讲，也有同样的问题。在手动ACK的情况下，消息的处理需要接收消息、处理消息、ACK三步，ACK的失败也会导致相同的问题。

在交易类的系统（比如电商、证券等）中，对非幂等的远程过程进行重试，可能会导致超买超卖，对客户造成经济损失。

互联网应用一般都是提供7*24服务的，而互联网应用本身又是快速迭代，后端系统是随时有可能需要进行发布的。发布等同于一次宕机（进程被kill），这意味着对于互联网应用的后端系统，宕机是常态而非特例。这也是幂等性和重试的必要性来源之一。

## 幂等性适用领域

试想这样的一种场景：在电商平台上支付后，因为网络原因导致系统提示你支付失败，于是你又重新付款了一次，等完成后检查网银发现被系统扣了两次款，这是一种什么样的体验？

造成上述问题的原因可能有很多，比如第一次付款时实际支付成功，但是信息返回时网络中断导致系统误判；又比如第一次付款的确失败了，但第二次付款时发生意外，导致支付请求被重复发送等等。在一次支付的过程中，每个环节都有可能会发生问题，我们要如何规避这类问题引发的分险？
幂等性是解决这类问题的方案之一，所以在电商，银行，互联网金融等对数据准确性要求很高的领域中，这一特性具有十分重要的地位。

假设有一个从账户取钱的远程API：

```java

boolean withdraw(long account_id, double amount);

```

withdraw的语义是从account_id对应的账户中扣除amount数额的钱；如果扣除成功则返回true，账户余额减少amount；如果扣除失败则返回false，账户余额不变。

需要注意的是：和本地环境相比，我们不能轻易假设分布式环境的可靠性。

一种典型的情况是withdraw请求已经被服务器端正确处理，但服务器端的返回结果由于网络等原因被掉丢了，导致客户端无法得知处理结果。如果是在网页上，一些不恰当的设计可能会使用户认为上一次操作失败了，然后刷新页面，这就导致了withdraw被调用两次，账户也被多扣了一次钱，如图所示：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_e117cb18-7e1f-4bd5-9b06-1ed6cb6ecc4c.png" alt="企业微信截图_e117cb18-7e1f-4bd5-9b06-1ed6cb6ecc4c" style="zoom:50%;" />

这个问题的解决方案之一是采用分布式事务，通过引入支持分布式事务的中间件来保证withdraw功能的事务性。分布式事务的优点是对于调用者很简单，复杂性都交给了中间件来管理。缺点则是一方面架构太重量级，容易被绑在特定的中间件上，不利于异构系统的集成；另一方面分布式事务虽然能保证事务的ACID性质，而但却无法提供性能和可用性的保证。

另一种更轻量级的解决方案是幂等设计，我们可以通过一些技巧把withdraw变成幂等的，比如：

```java
int create_ticket()
bool idempotent_withdraw(ticket_id, account_id, amount)
```

create_ticket的语义是获取一个服务器端生成的唯一的处理号ticket_id，它将用于标识后续的操作。idempotent_withdraw和withdraw的区别在于关联了一个ticket_id，一个ticket_id表示的操作至多只会被处理一次，每次调用都将返回第一次调用时的处理结果。这样，idempotent_withdraw就符合幂等性了，客户端就可以放心地多次调用。

基于幂等性的解决方案中一个完整的取钱流程被分解成了两个步骤：

```java
1.调用create_ticket()获取ticket_id；
2.调用idempotent_withdraw(ticket_id, account_id, amount)。
```

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_ab693c06-f1f1-4916-9e77-4e8e711d5e80.png" alt="企业微信截图_ab693c06-f1f1-4916-9e77-4e8e711d5e80" style="zoom:50%;" />

虽然create_ticket不是幂等的，但在这种设计下，它对系统状态的影响可以忽略，加上idempotent_withdraw是幂等的，所以任何一步由于网络等原因失败或超时，客户端都可以重试，直到获得结果。如图所示：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_42c3124f-02e9-49e9-b9ac-f205e0d9c983.png" alt="企业微信截图_42c3124f-02e9-49e9-b9ac-f205e0d9c983" style="zoom:50%;" />

对于传统页面防重复提交和上述方案有点类似，进入页面会生成一个token，提交表单会提交这个token，如果页面重复提交会传递相同的token到后端，后端发现 token 存在即提示重复操作。流程如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210331234513368.png" alt="image-20210331234513368" style="zoom:50%;" />



## 如何防止订单中的重复支付

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210401113956826.png" alt="image-20210401113956826" style="zoom:50%;" />

如图是一个简化的下单流程，首先是提交订单，然后是支付。

支付的话，一般是走支付网关（支付中心），然后支付中心与第三方支付渠道（微信、支付宝、银联）交互，支付成功以后，异步通知支付中心，支付中心更新自身支付订单状态，再通知业务应用，各业务再更新各自订单状态。

这个过程中经常可能遇到的问题是掉单，无论是超时未收到回调通知也好，还是程序自身报错也好，总之由于各种各样的原因，没有如期收到通知并正确的处理后续逻辑等等，都会造成用户支付成功了，但是服务端这边订单状态没更新，这个时候有可能产生投诉，或者用户重复支付。

由于③⑤造成的掉单称之为外部掉单，由④⑥造成的掉单我们称之为内部掉单。

为了防止掉单，这里可以这样处理：

1. 支付订单增加一个中间状态“支付中”，当同一个订单去支付的时候，先检查有没有状态为“支付中”的支付流水，当然支付（prepay）的时候要加个锁。支付完成以后更新支付流水状态的时候再讲其改成“支付成功”状态。
2. 支付中心这边要自己定义一个超时时间（比如：30秒），在此时间范围内如果没有收到支付成功回调，则应调用接口主动查询支付结果，比如10s、20s、30s查一次，如果在最大查询次数内没有查到结果，应做异常处理。
3. 支付中心收到支付结果以后，将结果同步给业务系统，可以发MQ，也可以直接调用，直接调用的话要加重试（比如：SpringBoot Retry）。
4. 无论是支付中心，还是业务应用，在接收支付结果通知时都要考虑接口幂等性，消息只处理一次，其余的忽略。
5. 业务应用也应做超时主动查询支付结果。

对于上面说的超时主动查询可以在发起支付的时候将这些支付订单放到一张表中，用定时任务去扫。

为了防止订单重复提交，可以这样处理：

创建订单的时候，用订单信息计算一个哈希值，判断redis中是否有key，有则不允许重复提交，没有则生成一个新key，放到redis中设置个过期时间，然后创建订单。其实就是在一段时间内不可重复相同的操作。

附上微信支付最佳实践：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210401114409917.png" alt="image-20210401114409917" style="zoom:50%;" />

## 参考

- [阿里巴巴刘晓-幂等性](http://byteliu.com/2019/01/05/%E5%B9%82%E7%AD%89%E6%80%A7/)
- [SpringBoot接口幂等性实现的4种方案，真的够用了](https://zhuanlan.zhihu.com/p/349665232)
- [美团-分布式系统互斥与幂等性问题的探索与实践](https://www.huaweicloud.com/articles/5b8234116bc1cc7ac669123c3b9b4375.html)