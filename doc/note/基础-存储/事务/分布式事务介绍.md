## 概述

分布式事务其实有几种方式解决。其中的一种就是在分布式数据库 DDB 中提交分布事务 XA。如果跨多个 DDB 中间件保证事务性，就需要使用 TCC 模型。我们的中间件 DTS 可以做这件事。同时，我们有个 TMC 中间件，含有异步消息模型（可以用事务消息的方式来完成）。这里我举两个场景来说明问题。



## 场景一：下单即减优惠券、减库存

下单是事务的发起方，优惠券和库存是事务的参与方，分支事务需要用事务 ID 做幂等，几方调用都需要同时并行。一旦程序出现错误，就会有一些定时任务去重试和回滚。如果分支事务不在，这时候会有一个超时机制，在某一个时间之内，无论是判断成功和判断失败都不做响应。但是在一个设定的时间之外，如果它还没给出应答，最后就会判断失败。这就是一个 TCC 的场景，TCC 适用于立即应答的场景，就像下单是没有“下单中”这个概念的，下单只有“成功”与“失败”，并且立刻反馈给商家。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/transaction/1.png" alt="1" style="zoom:50%;" />

## 场景二：交易消息异步

比如，在转账的时候，会有一个状态叫“转帐中”，一旦有中间状态，就不是一个完全同步的事务，可能需要 5 分钟的延迟。这种消息需要通过一定的机制，保证它能够到达对方，如果不能到达就需要重试。这种方式被称为事务消息队列

## 

