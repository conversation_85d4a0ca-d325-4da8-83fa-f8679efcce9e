## 概述

Youtube 上有个老外讲btree的视频相当不错(B站也有)，网上有人翻译版本叫《从磁盘结构到B+树》，本文是对这篇文章的梳理和补充，因为会涉及一些概念，所以单独开一节概念便于查阅。

## 引子

我们先思考一个简单的问题

> 如何修改一个一万行文本文件中第500行的一条数据？

把这个问题抛给chatGPT，它会告诉你 **直接修改文本文件中的某一行（如第 500 行）并不简单，因为文本文件是顺序读取和写入的**，并不支持随机访问行。你无法直接跳转到第 500 行进行修改，而需要**逐行读取和写入**。

传统的做法是先顺序读，找到修改的数据，修改完成，整个数据写入临时文件，再替换文件。

当然实际上java也是支持随机读，通过 MappedByteBuffer 或者 RandomAccessFile 可以定位到某个byte位置，前提你要先通过索引知道修改的数据对应的位置，然后调用它的seek()方法。

只修改一行代码却需要更新整个文件，看上去非常低效，但实际上对于小文件，这种方式比随机写效率更高，原因是对于小文件，读取 + 修改 + 写回整个文件，在现代 SSD 上是非常快的。

实际工程建议：

| 文件大小             | 建议修改方式                              |
| -------------------- | ----------------------------------------- |
| 小文件（<10MB）      | **读取整文件、修改后重写**（推荐）        |
| 中等文件（10~100MB） | 仍建议使用“读取 + 写入临时文件”方式       |
| 大文件（>100MB）     | 如果行长固定，可以考虑 `MappedByteBuffer` |

但是对于msyql这种大型数据库，肯定还是得采用修改原有数据文件，那么mysql是如何做到的呢？

首先mysql会把数据的最小单位分为page固定位16KB，这样设计很好理解，每个数据单元大小固定，修改数据不会引起数据位置的移动：

```
page1
page2
page3   <-要修改的数据
page4
....

```

比如要修改某个条数据，mysql通过索引定位到page3的postion，然后替换对应内容。这里还存在一个问题，万一用户从一个较小的数据更新到较大的数据甚至超过了16k，不是更新失败了吗？

mysql 有两种方案：

- Row Relocation(行迁移)，简单来说InnoDB 会将**原来的行标记为删除**，然后将新的大行**插入到另一个有足够空间的数据页中**，所以看起来是 UPDATE，其实底层是 DELETE + INSERT。
-  溢出页（Overflow Page）机制，如果更新的字段超过了page，特别是TEXT，BLOB等，行记录本身只存储一个 20 字节的“溢出指针”；真正的数据被放入一个单独的、页链组成的结构中；这些溢出页由 InnoDB 自动管理。



## disk 结构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130110218044.png" alt="image-20230130110218044" style="zoom:50%;" />

简单来说：按照时钟方向分，disk由很多个sector组成，编号为0-N。按照从外到内分，disk又由多个track组成，编号为0-N。sector和track交叉的地方，叫做block，每个block有自己的address，可以用(track_no,sector_no)表示。每个block的大小是一样的，具体的大小要看实际情况，在这里，我们假设一个block的大小是512bytes。

需要十分明确的一点是：**无论是读操作，还是写操作，都是以block为单位进行的。**

在block内部，可以看作是一个数组结构，坐标从0到511。每个byte都有一个address，这个称为offset。所以我们可以把disk上的每一个byte用(track_no,sector_no,offset)的形式表示。

![image-20230130110304651](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130110304651.png)

需要明确的：**只有disk上的数据被加载到RAM（random access memory），才能被程序使用。**或者说，才是真正对程序有用的。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130110339301.png" alt="image-20230130110339301" style="zoom:50%;" />

如何优化RAM中数据的效率，这门学问叫做数据结构；如何优化disk中数据的效率，这门学问叫做DBMS，也就是大部分数据库所要研究的内容

## disk是如何存储数据的

现在有一个employee table，其中有这些字段，每个字段的大小如图所示，一个record的大小总计为128 bytes。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130110437298.png" alt="image-20230130110437298" style="zoom:80%;" />

总共有100行数据：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130110456649.png" alt="image-20230130110456649" style="zoom:48%;" />

每个block可以存4行数据。存这100条数据，需要25个block。如果现在我们需要查询其中的一条数据，最多就需要查询25个block。

## 什么是索引

我们建一个简单的索引，有两个字段，一个eid，表示employee的id，还有一个字段pointer，指向数据存储在disk上的位置。empolyee中的每一行，在index上都有一条记录。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130110539012.png" alt="image-20230130110539012" style="zoom:50%;" />

那么我们又是怎么存储这个索引的呢？这里我们假设还是全部存在disk上（当然你完全可以选择直接存在内存里）。那么这个索引需要占据多少个block呢？eid大小为10bytes，pointer大小为6bytes，所以一行索引就有16个bytes大小。100条索引就需要占据100 * 16 / 512 -> 4个block。

那么现在要查询employee表中的某一条数据，最多需要查询多少个block呢？答案是4+1=5个。效率比之前要高了很多。

在这中索引方式中，我们为数据表中的每一条记录都建立了一条索引，这种索引方式我们叫做**稠密索引**。

## 稀疏索引

现在假设有1000条数据，这1000条数据将占据250个block，用上一小节讲的索引将占据40个block。现在用索引查询一次，最多需要41次block access。现在这个索引已经不能满足我们对性能的追求了，那么能不能对索引建一个索引呢？也就是稀疏索引？

>  稀疏索引就是指每隔一定数量的数据才会创建索引，这样既兼顾了查找效率，同时也避免了索引过大。

**对于二级索引，不需要记录每条employee在disk的位置，只需要记录一级索引所有block的位置就行了。** 所以，二级索引需要40条记录，也就是需要占据2个block的空间。这种二级索引可以叫做稀疏索引，他不会包含所有数据行所在的位置。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130113456356.png" alt="image-20230130113456356" style="zoom:50%;" />



现在借助稀疏索引，查询效率为：2 + 1 + 1 = 4次block access。

随着数据量不断增加，还可以对二级索引建立三级索引，对三级索引建立四级索引……

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130113600194.png" alt="image-20230130113600194" style="zoom:50%;" />

如果我们把上面的图逆时针转90度，在稍微修改一下，那么他就会变成下面这样，那么其实[B树](https://so.csdn.net/so/search?q=B树&spm=1001.2101.3001.7020)的雏形已经有了

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130113647808.png" alt="image-20230130113647808" style="zoom:50%;" />

## m-路搜索树

m-路搜索树也称为m-way搜索树，在搞清楚这个概念之前我们先来看二叉搜索树，他是一种特殊的二叉树。特殊在：每一个节点，左边的孩子节点总是小于该节点，右边的孩子节点总是大于该节点，然后通过这种特性我们可以实现log级别的二分搜索的查询时间复杂度。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130113840868.png" alt="image-20230130113840868" style="zoom:50%;" />

由二叉搜索树扩展，让每个节点最多可以存m-1个索引值，每个节点可以有m个子节点，就是m路搜索树。

![image-20230130114031925](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130114031925.png)

上图所示的就是3-路搜索树。假设有其中一个节点有K1，K2两个关键字(图中的20,50)，K1左边的孩子节点总是小于K1，K1和K2中间的孩子节点总是介于K1和K2之间，K2右边的孩子节点总是大于K2。那么这棵树我们就可以叫做**M路搜索树**。

下图就是一颗4路搜索树。他的每一个节点最多可以有三个关键字，四棵子树。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130114321423.png" alt="image-20230130114321423" style="zoom:50%;" />

我们可以用这个m-way搜索树作为数据库的索引，但是，m-way搜索树存在一些问题：

如果是按照递增或者递减的顺序去插入新节点来实现，那么这课二叉搜索树就会退化成链表，比如现在有三个数据：10，20，30，要用一个10-way搜索树来构建。很有可能，最终会构建出一个这样的树：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130114408305.png" alt="image-20230130114408305" style="zoom:50%;" />

解决方案就得靠B树了

## B树

B树的B是平衡（balanceed）这个单词的缩写，他主要通过附加以下三点约束来防止了M路搜索树退化成链表，以及使它达能到一个平衡的状态：

1. 根节点至少有两个子女
2. 根节点以外的所有节点至少有**m/2向上取整**个子女
3. 所有的叶子节点都位于同一层

### B树中的插入操作

值：10，20，40，50。要构建一个4-way搜索树。4-way搜索树，意味着一个节点最多可以有3个值。



## 一些概念

### 什么是B树的阶？

这个最好看下英文的定义，其实存在两个术语 order 和 degree 表示树的阶，它们都表示一个树中的节点允许子节点的数量，区别在于order表示最大值，degree表示最小值，参考[stackoverflow上的讨论](https://stackoverflow.com/questions/28846377/what-is-the-difference-btw-order-and-degree-in-terms-of-tree-data-structure)

### 什么是2-3树？

每个节点中对多可以有3个子节点的树，阶数(Order)是指节点的子节点数量的最大值，比如2-3树的阶就是3，2-3-4树的阶是4。

### 树都有哪些术语？
1、Height：节点的高度是该节点和叶子之间最长路径上的边数

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230129201404404.png" alt="image-20230129201404404" style="zoom:50%;" />

比如图中A的高度为3，因为最长的叶子节点是E

2、Depth：节点的深度是从节点到树的根节点的边数。root的深度为 0

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230129201539495.png" alt="image-20230129201539495" style="zoom:50%;" />

比如图中D的深度为 2

3、Level：层就是深度+1

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230129201905926.png" alt="image-20230129201905926" style="zoom:50%;" />

比如图中D的层为 3

## bplustree源码

[bplustree](https://github.com/davidmoten/bplustree) 是一个开源的纯java实现btree持久化库，代码量不多，几个核心的类如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230130155906932.png" alt="image-20230130155906932" style="zoom:50%;" />

- Node分为叶子节点和和非叶子节点，Leaf节点保存value，NonLeaf节点保存key
- BPlusTree 提供对外 insert() 和find()  API
- FactoryFile 提供节点持久化能力

### index文件保存格式

```
POSITION(LONG) + 节点类型(BYTE) + key数量(BYTE) + key数量*(keyByteSize+KEY_POSITION) + key_data + VALUE_POSITION
```

- 节点类型：Leaf：0，NoneLeaf：1

## 参考

```
googel: java b+tree stored on disk site:medium.com
google: b+tree stored on disk
```

- [[译]从磁盘结构到B+树](https://juejin.cn/post/6844903774402641927)
- [从磁盘结构和索引来看B/B+树](https://blog.csdn.net/weixin_40149557/article/details/122023572)
- https://medium.com/@pthtantai97/implement-key-value-store-by-btree-5a100a03da3a
- https://www.geeksforgeeks.org/introduction-of-b-tree-2/
- https://medium.com/@andrewjmarkham1/disk-storage-data-indexing-and-a-use-case-for-b-trees-608ff56bffdd
- https://github.com/myui/btree4j
- https://github.com/phamtai97/key-value-store
- https://github.com/amaru0601/b-tree-on-disk