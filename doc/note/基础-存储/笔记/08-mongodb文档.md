## mongodb集群环境

美利车mongodb集群有两种版本，3.4 和 4.2

| 版本 | 美丽车mongo集群          | 区域 | 账号信息             | 公网地址                           | 挖财master地址     | 挖财集群账号   |
| ---- | ------------------------ | ---- | -------------------- | ---------------------------------- | ------------------ | -------------- |
| 3.4  | 线上-车金融产品-mongodb  | 北京 | root/nnz3YgC@QXYBBnm | mlc-mysql.ngrok.wacaiyun.com:13311 | 10.1.128.120:27017 | meiliche/bx7de |
| 3.4  | 线上-负债监控-mongodb    | 北京 | root/nnz3YgC@QXYBBnm | mlc-mysql.ngrok.wacaiyun.com:13312 | 10.1.128.120:27018 | meiliche/bx7de |
| 3.4  | 线上-融资-贷后mongodb    | 北京 | root/nnz3YgC@QXYBBnm | mlc-mysql.ngrok.wacaiyun.com:13313 | 10.1.128.120:27019 | meiliche/bx7de |
| 4.2  | dds-<br>uf6fb12bafdfdc34 | 上海 | root/nnz3YgC@QXYBBnm | mlc-mysql.ngrok.wacaiyun.com:13314 | ************:27017 | meiliche/bx7de |

集群内部部署，采用三台虚拟机，端口递增方式：

| 版本 | master             | slave1             | slave2             |
| ---- | ------------------ | ------------------ | ------------------ |
| 3.4  | 10.1.128.120:27017 | ************:27017 | 10.1.128.122:27017 |
| 3.4  | 10.1.128.120:27018 | ************:27018 | 10.1.128.122:27018 |
| 3.4  | 10.1.128.120:27019 | ************:27019 | 10.1.128.122:27019 |
| 4.2  | ************:27017 | ************:27017 | ************:27017 |



## 环境搭建步骤

### 1、centos系统设置

1、执行

```
echo never > /sys/kernel/mm/transparent_hugepage/enabled
echo never > /sys/kernel/mm/transparent_hugepage/defrag
```

2、修改 /etc/security/limits.conf ,新增

```
echo '* soft nproc 327675' >> /etc/security/limits.conf
echo '* hard nproc 327675' >> /etc/security/limits.conf
```

3、设置 path

```
echo 'export PATH=/data/program/mongodb/mongodb-linux-x86_64-rhel62-4.2.24/bin/:$PATH' >> ~/.bashrc
```



### 2、启动节点

配置文件

3.4

```
systemLog:
  destination: file
  path: "/data/program/mongodb/mongo-27017/logs/mongod.log"
  logAppend: true
storage:
  dbPath: "/data/program/mongodb/mongo-27017/data"
  journal:
    enabled: true
processManagement:
  fork: true
replication:
  oplogSizeMB: 50
  replSetName: "rs1"
  secondaryIndexPrefetch: "all"
net:
  #bindIp: ************
  port: 27017
setParameter:
  enableLocalhostAuthBypass: true
```

4.2

```
systemLog:
  destination: file
  path: "/data/program/mongodb/mongo-27017/logs/mongod.log"
  logAppend: true
storage:
  dbPath: "/data/program/mongodb/mongo-27017/data"
  journal:
    enabled: true
processManagement:
  fork: true
replication:
  oplogSizeMB: 50
  replSetName: "rs1"
net:
  port: 27017
  bindIpAll: true
setParameter:
  enableLocalhostAuthBypass: true
```

启动

```
mongod --config /data/program/mongodb/mongo-27017/config/mongo-27017.config
```



### 3、集群初始化

查看帮助 rs.help()

```
> rs.help()
	rs.status()                                { replSetGetStatus : 1 } checks repl set status
	rs.initiate()                              { replSetInitiate : null } initiates set with default settings
	rs.initiate(cfg)                           { replSetInitiate : cfg } initiates set with configuration cfg
	rs.conf()                                  get the current configuration object from local.system.replset
	rs.reconfig(cfg)                           updates the configuration of a running replica set with cfg (disconnects)
	rs.add(hostportstr)                        add a new member to the set with default attributes (disconnects)
	rs.add(membercfgobj)                       add a new member to the set with extra attributes (disconnects)
	rs.addArb(hostportstr)                     add a new member which is arbiterOnly:true (disconnects)
	rs.stepDown([stepdownSecs, catchUpSecs])   step down as primary (disconnects)
	rs.syncFrom(hostportstr)                   make a secondary sync from the given member
	rs.freeze(secs)                            make a node ineligible to become primary for the time specified
	rs.remove(hostportstr)                     remove a host from the replica set (disconnects)
	rs.slaveOk()                               allow queries on secondary nodes

	rs.printReplicationInfo()                  check oplog size and time range
	rs.printSlaveReplicationInfo()             check replica set members and replication lag
	db.isMaster()                              check who is primary
```

配置

```
cfg = {
	"_id" : "rs1",
	"version" : 1,
	"protocolVersion" : NumberLong(1),
	"members" : [
		{"_id" : 0,"host" : "************:27017"},
		{"_id" : 1,"host" : "************:27017"},
		{"_id" : 2,"host" : "************:27017"}
	],
	"settings" : {
		"chainingAllowed" : true,
		"heartbeatIntervalMillis" : 2000,
		"heartbeatTimeoutSecs" : 10,
		"electionTimeoutMillis" : 10000,
		"catchUpTimeoutMillis" : 60000
	}
}
```

执行

```
rs.initiate(cfg)
```

### 4、权限控制

先创建用户再增加security配置

mongo 基于角色管理权限，内置了一些角色，参考[built-in-roles](https://www.mongodb.com/docs/v4.2/reference/built-in-roles/index.html)

创建超级管理员

```
use admin
db.createUser({user:"root",pwd:"bx7de",roles:[{role:"root",db:"admin"}]})
db.createUser({ user: "meiliche" , pwd: "bx7de", roles: ["root"]})
```

创建库管理员

```
db.auth("root", "bx7de")
use admin
db.createUser({ user: "admin" , pwd: "bx7de", roles: ["readWriteAnyDatabase", "userAdminAnyDatabase"]})
```

创建keyfile文件

```
openssl rand -base64 126 -out > /data/program/mongodb/mongo-27017config/key.txt
chmod 600 /data/program/mongodb/mongo-27017config/key.txt
```

配置文件新增

```
security:
  keyFile: "/data/program/mongodb/mongo-27017/config/key.txt"
```

登录有两种方式：

```
方式1： mongo admin -u meiliche -p 'bx7de' --host localhost --port 27017
方式2： use admin 
			 db.auth("meiliche", "bx7de")
```

删除用户

```
db.dropUser('meiliche')
```



### 5、监控

```
db.isMaster() 查看master状态
rs.status()   查看副本状态     
```



