原文链接：https://tech.meituan.com/about_desk_io.html

## 一、背景

计算机硬件性能在过去十年间的发展普遍遵循摩尔定律，通用计算机的CPU主频早已超过3GHz，内存也进入了普及DDR4的时代。然而传统硬盘虽然在存储容量上增长迅速，但是在读写性能上并无明显提升，同时SSD硬盘价格高昂，不能在短时间内完全替代传统硬盘。传统磁盘的I/O读写速度成为了计算机系统性能提高的瓶颈，制约了计算机整体性能的发展。

硬盘性能的制约因素是什么？如何根据磁盘I/O特性来进行系统设计？针对这些问题，本文将介绍硬盘的物理结构和性能指标，以及操作系统针对磁盘性能所做的优化，最后讨论下基于磁盘I/O特性设计的技巧。

## 二、硬盘的物理结构

硬盘内部主要部件为磁盘盘片、传动手臂、读写磁头和主轴马达。实际数据都是写在盘片上，读写主要是通过传动手臂上的读写磁头来完成。实际运行时，主轴让磁盘盘片转动，然后传动手臂可伸展让读取头在盘片上进行读写操作。磁盘物理结构如下图所示：

![img](https://awps-assets.meituan.net/mit-x/blog-images-bundle-2017/343bddc0.jpg)

由于单一盘片容量有限，一般硬盘都有两张以上的盘片，每个盘片有两面，都可记录信息，所以一张盘片对应着两个磁头。盘片被分为许多扇形的区域，每个区域叫一个扇区，硬盘中每个扇区的大小固定为512字节。盘片表面上以盘片中心为圆心，不同半径的同心圆称为磁道，不同盘片相同半径的磁道所组成的圆柱称为柱面。磁道与柱面都是表示不同半径的圆，在许多场合，磁道和柱面可以互换使用。磁盘盘片垂直视角如下图所示：

![img](https://awps-assets.meituan.net/mit-x/blog-images-bundle-2017/6af12fac.png)

早期的硬盘每磁道扇区数相同，此时由磁盘基本参数可以计算出硬盘的容量：存储容量=磁头数*磁道（柱面）数*每道扇区数*每扇区字节数。由于每磁道扇区数相同，外圈磁道半径大，里圈磁道半径小，外圈和里圈扇区面积自然会不一样。同时，为了更好的读取数据，即使外圈扇区面积再大也只能和内圈扇区一样存放相同的字节数（512字节）。这样一来，外圈的记录密度就要比内圈小，会浪费大量的存储空间。

如今的硬盘都使用ZBR（Zoned Bit Recording，区位记录）技术，盘片表面由里向外划分为数个区域，不同区域的磁道扇区数目不同，同一区域内各磁道扇区数相同，盘片外圈区域磁道长扇区数目较多，内圈区域磁道短扇区数目较少，大体实现了等密度，从而获得了更多的存储空间。此时，由于每磁道扇区数各不相同，所以传统的容量计算公式就不再适用。实际上如今的硬盘大多使用LBA（Logical Block Addressing）逻辑块寻址模式，知道LBA后即可计算出硬盘容量。

### 影响硬盘性能的因素

影响磁盘的关键因素是磁盘服务时间，即磁盘完成一个I/O请求所花费的时间，它由寻道时间、旋转延迟和数据传输时间三部分构成。

#### 1. 寻道时间

Tseek是指将读写磁头移动至正确的磁道上所需要的时间。寻道时间越短，I/O操作越快，目前磁盘的平均寻道时间一般在3-15ms。

#### 2. 旋转延迟

Trotation是指盘片旋转将请求数据所在的扇区移动到读写磁盘下方所需要的时间。旋转延迟取决于磁盘转速，通常用磁盘旋转一周所需时间的1/2表示。比如：7200rpm的磁盘平均旋转延迟大约为60*1000/7200/2 = 4.17ms，而转速为15000rpm的磁盘其平均旋转延迟为2ms。

#### 3. 数据传输时间

Ttransfer是指完成传输所请求的数据所需要的时间，它取决于数据传输率，其值等于数据大小除以数据传输率。目前IDE/ATA能达到133MB/s，SATA II可达到300MB/s的接口数据传输率，数据传输时间通常远小于前两部分消耗时间。简单计算时可忽略。

### 衡量性能的指标

机械硬盘的连续读写性能很好，但随机读写性能很差，这主要是因为磁头移动到正确的磁道上需要时间，随机读写时，磁头需要不停的移动，时间都浪费在了磁头寻址上，所以性能不高。衡量磁盘的重要主要指标是IOPS和吞吐量。

#### 1. IOPS

IOPS（Input/Output Per Second）即每秒的输入输出量（或读写次数），即指每秒内系统能处理的I/O请求数量。随机读写频繁的应用，如小文件存储等，关注随机读写性能，IOPS是关键衡量指标。可以推算出磁盘的IOPS = 1000ms / (Tseek + Trotation + Transfer)，如果忽略数据传输时间，理论上可以计算出随机读写最大的IOPS。常见磁盘的随机读写最大IOPS为：

- 7200rpm的磁盘 IOPS = 76 IOPS
- 10000rpm的磁盘IOPS = 111 IOPS
- 15000rpm的磁盘IOPS = 166 IOPS

#### 2. 吞吐量

吞吐量（Throughput），指单位时间内可以成功传输的数据数量。顺序读写频繁的应用，如视频点播，关注连续读写性能、数据吞吐量是关键衡量指标。它主要取决于磁盘阵列的架构，通道的大小以及磁盘的个数。不同的磁盘阵列存在不同的架构，但他们都有自己的内部带宽，一般情况下，内部带宽都设计足够充足，不会存在瓶颈。磁盘阵列与服务器之间的数据通道对吞吐量影响很大，比如一个2Gbps的光纤通道，其所能支撑的最大流量仅为250MB/s。最后，当前面的瓶颈都不再存在时，硬盘越多的情况下吞吐量越大。

## 三、操作系统层的优化

虽然15000rpm的磁盘计算出的理论最大IOPS仅为166，但在实际运行环境中，实际磁盘的IOPS往往能够突破200甚至更高。这其实就是在系统调用过程中，操作系统进行了一系列的优化。

那么操作系统是如何操作硬盘的呢？类似于网络的分层结构，下图显示了Linux系统中对于磁盘的一次读请求在核心空间中所要经历的层次模型。从图中看出：对于磁盘的一次读请求，首先经过虚拟文件系统层（VFS Layer），其次是具体的文件系统层（例如Ext2），接下来是Cache层（Page Cache Layer）、通用块层（Generic Block Layer）、I/O调度层（I/O Scheduler Layer）、块设备驱动层（Block Device Driver Layer），最后是物理块设备层（Block Device Layer）。

<img src="https://awps-assets.meituan.net/mit-x/blog-images-bundle-2017/6e034503.png" alt="img" style="zoom:50%;" />

### 虚拟文件系统层（VFS Layer）

VFS（Virtual File System）虚拟文件系统是一种软件机制，更确切的说扮演着文件系统管理者的角色，与它相关的数据结构只存在于物理内存当中。它的作用是：屏蔽下层具体文件系统操作的差异，为上层的操作提供一个统一的接口。正是因为有了这个层次，Linux中允许众多不同的文件系统共存并且对文件的操作可以跨文件系统而执行。

VFS中包含着向物理文件系统转换的一系列数据结构，如VFS超级块、VFS的Inode、各种操作函数的转换入口等。Linux中VFS依靠四个主要的数据结构来描述其结构信息，分别为超级块、索引结点、目录项和文件对象。

1. 超级块（Super Block）：超级块对象表示一个文件系统。它存储一个已安装的文件系统的控制信息，包括文件系统名称（比如Ext2）、文件系统的大小和状态、块设备的引用和元数据信息（比如空闲列表等等）。VFS超级块存在于内存中，它在文件系统安装时建立，并且在文件系统卸载时自动删除。同时需要注意的是对于每个具体的文件系统来说，也有各自的超级块，它们存放于磁盘。
2. 索引结点（Inode）：索引结点对象存储了文件的相关元数据信息，例如：文件大小、设备标识符、用户标识符、用户组标识符等等。Inode分为两种：一种是VFS的Inode，一种是具体文件系统的Inode。前者在内存中，后者在磁盘中。所以每次其实是将磁盘中的Inode调进填充内存中的Inode，这样才是算使用了磁盘文件Inode。当创建一个文件的时候，就给文件分配了一个Inode。一个Inode只对应一个实际文件，一个文件也会只有一个Inode。
3. 目录项（Dentry）：引入目录项对象的概念主要是出于方便查找文件的目的。不同于前面的两个对象，目录项对象没有对应的磁盘数据结构，只存在于内存中。一个路径的各个组成部分，不管是目录还是普通的文件，都是一个目录项对象。如，在路径/home/<USER>/test.java中，目录 /, home, source和文件 test.java都对应一个目录项对象。VFS在查找的时候，根据一层一层的目录项找到对应的每个目录项的Inode，那么沿着目录项进行操作就可以找到最终的文件。
4. 文件对象（File）：文件对象描述的是进程已经打开的文件。因为一个文件可以被多个进程打开，所以一个文件可以存在多个文件对象。一个文件对应的文件对象可能不是惟一的，但是其对应的索引节点和目录项对象肯定是惟一的。

### Ext2文件系统

VFS的下一层即是具体的文件系统，本节简要介绍下Linux的Ext2文件系统。

一个文件系统一般使用块设备上一个独立的逻辑分区。对于Ext2文件系统来说，硬盘分区首先被划分为一个个的Block，一个Ext2文件系统上的每个Block都是一样大小的。但是不同Ext2文件系统，Block大小可能不同，这是在创建Ext2系统决定的，一般为1k或者4k。由于Block数量很多，为了方便管理，Ext2将这些Block聚集在一起分为几个大的块组（Block Group），每个块组包含的等量的物理块，在块组的数据块中存储文件或目录。Ext2文件系统存储结构如下图所示：

![img](https://awps-assets.meituan.net/mit-x/blog-images-bundle-2017/88a82956.png)

Ext2中的Super Block和Inode Table分别对应VFS中的超级块和索引结点，存放在磁盘。每个块组都有一个块组描述符GDT（Group Descriptor Table），存储一个块组的描述信息，例如在这个块组中从哪里开始是Inode表，从哪里开始是数据块等等。Block Bitmap和Inode Bitmap分别表示Block和Inode是否空闲可用。Data Block数据块是用来真正存储文件内容数据的地方，下面我们看一下具体的存储规则。

在Ext2文件系统中所支持的Block大小有1K、2K、4K三种。在格式化时Block的大小就固定了，且每个Block都有编号，方便Inode的记录。每个Block内最多只能够放置一个文件的数据，如果文件大于Block的大小，则一个文件会占用多个Block；如果文件小于Block，则该Block的剩余容量就不能够再被使用了，即磁盘空间会浪费。下面看看Inode和Block的对应关系。

Inode要记录的数据非常多，但大小仅为固定的128字节，同时记录一个Block号码就需要4字节，假设一个文件有400MB且每个Block为4K时，那么至少也要十万笔Block号码的记录。Inode不可能有这么多的记录信息，因此Ext2将Inode记录Block号码的区域定义为12个直接、一个间接、一个双间接与一个三间接记录区。Inode存储结构如下图所示：

![img](https://awps-assets.meituan.net/mit-x/blog-images-bundle-2017/4a560e8c.png)

最左边为Inode本身（128 bytes），里面有12个直接指向Block号码的对照，这12笔记录能够直接取得Block号码。至于所谓的间接就是再拿一个Block来当作记录Block号码的记录区，如果文件太大时，就会使用间接的Block来记录编号。如上图当中间接只是拿一个Block来记录额外的号码而已。 同理，如果文件持续长大，那么就会利用所谓的双间接，第一个Block仅再指出下一个记录编号的Block在哪里，实际记录的在第二个Block当中。依此类推，三间接就是利用第三层Block来记录编号。

### Page Cache层

引入Cache层的目的是为了提高Linux操作系统对磁盘访问的性能。Cache层在内存中缓存了磁盘上的部分数据。当数据的请求到达时，如果在Cache中存在该数据且是最新的，则直接将数据传递给用户程序，免除了对底层磁盘的操作，提高了性能。Cache层也正是磁盘IOPS为什么能突破200的主要原因之一。

在Linux的实现中，文件Cache分为两个层面，一是Page Cache，另一个Buffer Cache，每一个Page Cache包含若干Buffer Cache。Page Cache主要用来作为文件系统上的文件数据的缓存来用，尤其是针对当进程对文件有read/write操作的时候。Buffer Cache则主要是设计用来在系统对块设备进行读写的时候，对块进行数据缓存的系统来使用。

磁盘Cache有两大功能：预读和回写。预读其实就是利用了局部性原理，具体过程是：对于每个文件的第一个读请求，系统读入所请求的页面并读入紧随其后的少数几个页面（通常是三个页面），这时的预读称为同步预读。对于第二次读请求，如果所读页面不在Cache中，即不在前次预读的页中，则表明文件访问不是顺序访问，系统继续采用同步预读；如果所读页面在Cache中，则表明前次预读命中，操作系统把预读页的大小扩大一倍，此时预读过程是异步的，应用程序可以不等预读完成即可返回，只要后台慢慢读页面即可，这时的预读称为异步预读。任何接下来的读请求都会处于两种情况之一：第一种情况是所请求的页面处于预读的页面中，这时继续进行异步预读；第二种情况是所请求的页面处于预读页面之外，这时系统就要进行同步预读。

回写是通过暂时将数据存在Cache里，然后统一异步写到磁盘中。通过这种异步的数据I/O模式解决了程序中的计算速度和数据存储速度不匹配的鸿沟，减少了访问底层存储介质的次数，使存储系统的性能大大提高。Linux 2.6.32内核之前，采用pdflush机制来将脏页真正写到磁盘中，什么时候开始回写呢？下面两种情况下，脏页会被写回到磁盘：

1. 在空闲内存低于一个特定的阈值时，内核必须将脏页写回磁盘，以便释放内存。
2. 当脏页在内存中驻留超过一定的阈值时，内核必须将超时的脏页写会磁盘，以确保脏页不会无限期地驻留在内存中。

回写开始后，pdflush会持续写数据，直到满足以下两个条件：

1. 已经有指定的最小数目的页被写回到磁盘。
2. 空闲内存页已经回升，超过了阈值。

Linux 2.6.32内核之后，放弃了原有的pdflush机制，改成了bdi_writeback机制。bdi_writeback机制主要解决了原有fdflush机制存在的一个问题：在多磁盘的系统中，pdflush管理了所有磁盘的Cache，从而导致一定程度的I/O瓶颈。bdi_writeback机制为每个磁盘都创建了一个线程，专门负责这个磁盘的Page Cache的刷新工作，从而实现了每个磁盘的数据刷新在线程级的分离，提高了I/O性能。

回写机制存在的问题是回写不及时引发数据丢失（可由sync|fsync解决），回写期间读I/O性能很差。

### 通用块层

通用块层的主要工作是：接收上层发出的磁盘请求，并最终发出I/O请求。该层隐藏了底层硬件块设备的特性，为块设备提供了一个通用的抽象视图。

对于VFS和具体的文件系统来说，块（Block）是基本的数据传输单元，当内核访问文件的数据时，它首先从磁盘上读取一个块。但是对于磁盘来说，扇区是最小的可寻址单元，块设备无法对比它还小的单元进行寻址和操作。由于扇区是磁盘的最小可寻址单元，所以块不能比扇区还小，只能整数倍于扇区大小，即一个块对应磁盘上的一个或多个扇区。一般来说，块大小是2的整数倍，而且由于Page Cache层的最小单元是页（Page），所以块大小不能超过一页的长度。

大多情况下，数据的传输通过DMA方式。旧的磁盘控制器，仅仅支持简单的DMA操作：每次数据传输，只能传输磁盘上相邻的扇区，即数据在内存中也是连续的。这是因为如果传输非连续的扇区，会导致磁盘花费更多的时间在寻址操作上。而现在的磁盘控制器支持“分散/聚合”DMA操作，这种模式下，数据传输可以在多个非连续的内存区域中进行。为了利用“分散/聚合”DMA操作，块设备驱动必须能处理被称为段（segments）的数据单元。一个段就是一个内存页面或一个页面的部分，它包含磁盘上相邻扇区的数据。

通用块层是粘合所有上层和底层的部分，一个页的磁盘数据布局如下图所示：

![img](https://awps-assets.meituan.net/mit-x/blog-images-bundle-2017/293fcf09.png)

### I/O调度层

I/O调度层的功能是管理块设备的请求队列。即接收通用块层发出的I/O请求，缓存请求并试图合并相邻的请求。并根据设置好的调度算法，回调驱动层提供的请求处理函数，以处理具体的I/O请求。

如果简单地以内核产生请求的次序直接将请求发给块设备的话，那么块设备性能肯定让人难以接受，因为磁盘寻址是整个计算机中最慢的操作之一。为了优化寻址操作，内核不会一旦接收到I/O请求后，就按照请求的次序发起块I/O请求。为此Linux实现了几种I/O调度算法，算法基本思想就是通过合并和排序I/O请求队列中的请求，以此大大降低所需的磁盘寻道时间，从而提高整体I/O性能。

常见的I/O调度算法包括Noop调度算法（No Operation）、CFQ（完全公正排队I/O调度算法）、DeadLine（截止时间调度算法）、AS预测调度算法等。

- Noop算法：最简单的I/O调度算法。该算法仅适当合并用户请求，并不排序请求。新的请求通常被插在调度队列的开头或末尾，下一个要处理的请求总是队列中的第一个请求。这种算法是为不需要寻道的块设备设计的，如SSD。因为其他三个算法的优化是基于缩短寻道时间的，而SSD硬盘没有所谓的寻道时间且I/O响应时间非常短。
- CFQ算法：算法的主要目标是在触发I/O请求的所有进程中确保磁盘I/O带宽的公平分配。算法使用许多个排序队列，存放了不同进程发出的请求。通过散列将同一个进程发出的请求插入同一个队列中。采用轮询方式扫描队列，从第一个非空队列开始，依次调度不同队列中特定个数（公平）的请求，然后将这些请求移动到调度队列的末尾。
- Deadline算法：算法引入了两个排队队列分别包含读请求和写请求，两个最后期限队列包含相同的读和写请求。本质就是一个超时定时器，当请求被传给电梯算法时开始计时。一旦最后期限队列中的超时时间已到，就想请求移至调度队列末尾。Deadline算法避免了电梯调度策略（为了减少寻道时间，会优先处理与上一个请求相近的请求）带来的对某个请求忽略很长一段时间的可能。
- AS算法：AS算法本质上依据局部性原理，预测进程发出的读请求与刚被调度的请求在磁盘上可能是“近邻”。算法统计每个进程I/O操作信息，当刚刚调度了由某个进程的一个读请求之后，算法马上检查排序队列中的下一个请求是否来自同一个进程。如果是，立即调度下一个请求。否则，查看关于该进程的统计信息，如果确定进程p可能很快发出另一个读请求，那么就延迟一小段时间。

前文中计算出的IOPS是理论上的随机读写的最大IOPS，在随机读写中，每次I/O操作的寻址和旋转延时都不能忽略不计，有了这两个时间的存在也就限制了IOPS的大小。现在如果我们考虑在读取一个很大的存储连续分布在磁盘的文件，因为文件的存储的分布是连续的，磁头在完成一个读I/O操作之后，不需要重新寻址，也不需要旋转延时，在这种情况下我们能到一个很大的IOPS值。这时由于不再考虑寻址和旋转延时，则性能瓶颈仅是数据传输时延，假设数据传输时延为0.4ms，那么IOPS=1000 / 0.4 = 2500 IOPS。

在许多的开源框架如Kafka、HBase中，都通过追加写的方式来尽可能的将随机I/O转换为顺序I/O，以此来降低寻址时间和旋转延时，从而最大限度的提高IOPS。

### 块设备驱动层

驱动层中的驱动程序对应具体的物理块设备。它从上层中取出I/O请求，并根据该I/O请求中指定的信息，通过向具体块设备的设备控制器发送命令的方式，来操纵设备传输数据。这里不再赘述。

## 四、基于磁盘I/O特性设计的技巧

在上一节中我们了解了Linux系统中请求到达磁盘的一次完整过程，期间Linux通过Cache以及排序合并I/O请求来提高系统的性能。其本质就是由于磁盘随机读写慢、顺序读写快。本节针对常见开源系统阐述一些基于磁盘I/O特性的设计技巧。

### 采用追加写

在进行系统设计时，良好的读性能和写性能往往不可兼得。在许多常见的开源系统中都是优先在保证写性能的前提下来优化读性能。那么如何设计能让一个系统拥有良好的写性能呢？一个好的办法就是采用追加写，每次将数据添加到文件。由于完全是顺序的，所以可以具有非常好的写操作性能。但是这种方式也存在一些缺点：从文件中读一些数据时将会需要更多的时间：需要倒序扫描，直到找到所需要的内容。当然在一些简单的场景下也能够保证读操作的性能：

数据是被整体访问，比如HDFS

HDFS建立在**一次写多次读的模型**之上。在HDFS中就是采用了追加写并且设计为高数据吞吐量；高吞吐量必然以高延迟为代价，所以HDFS并不适用于对数据访问要求低延迟的场景；由于采用是的追加写，也并不适用于任意修改文件的场景。HDFS设计为流式访问大文件，使用大数据块并且采用流式数据访问来保证数据被整体访问，同时最小化硬盘的寻址开销，只需要一次寻址即可，这时寻址时间相比于传输时延可忽略，从而也拥有良好的读性能。HDFS不适合存储小文件，原因之一是由于NameNode内存不足问题，还有就是因为访问大量小文件需要执行大量的寻址操作，并且需要不断的从一个datanode跳到另一个datanode，这样会大大降低数据访问性能。

知道文件明确的偏移量，比如Kafka

在Kafka中，采用消息追加的方式来写入每个消息，每个消息读写时都会利用Page Cache的预读和后写特性，同时 partition 中都使用顺序读写，以此来提高I/O性能。虽然Kafka能够根据偏移量查找到具体的某个消息，但是查找过程是顺序查找，因此如果数据很大的话，查找效率就很低。所以Kafka中采用了分段和索引的方式来解决查找效率问题。Kafka把一个patition大文件又分成了多个小文件段，每个小文件段以偏移量命名，通过多个小文件段，不仅可以使用二分搜索法很快定位消息，同时也容易定期清除或删除已经消费完的文件，减少磁盘占用。为了进一步提高查找效率，Kafka为每个分段后的数据建立了索引文件，并通过索引文件稀疏存储来降低元数据占用大小。一个段中数据对应结构如下图所示：

![img](https://awps-assets.meituan.net/mit-x/blog-images-bundle-2017/abc6c736.png)



在面对更复杂的读场景（比如按key）时，如何来保证读操作的性能呢？简单的方式是像Kafka那样，将文件数据有序保存，使用二分查找来优化效率；或者通过建索引的方式来进行优化；也可以采用hash的方式将数据分割为不同的桶。以上的方法都能增加读操作的性能，但是由于在数据上强加了数据结构，又会降低写操作的性能。比如如果采用索引的方式来优化读操作，那么在更新索引时就需要更新B-tree中的特定部分，这时候的写操作就是随机写。那么有没有一种办法在保证写性能不损失的同时也提供较好的读性能呢？一个好的选择就是使用LSM-tree。LSM-tree与B-tree相比，LSM-tree牺牲了部分读操作，以此大幅提高写性能。

日志结构的合并树LSM（The Log-Structured Merge-Tree）是HBase，LevelDB等NoSQL数据库的存储引擎。Log-Structured的思想是将整个磁盘看做一个日志，在日志中存放永久性数据及其索引，每次都添加到日志的末尾。**并且通过将很多小文件的存取转换为连续的大批量传输**，使得对于文件系统的大多数存取都是顺序的，从而提高磁盘I/O。LSM-tree就是这样一种采用追加写、数据有序以及将随机I/O转换为顺序I/O的延迟更新，批量写入硬盘的数据结构。

LSM-tree将数据的修改增量先保存在内存中，达到指定的大小限制后再将这些修改操作批量写入磁盘。因此比较旧的文件不会被更新，重复的纪录只会通过创建新的纪录来覆盖，这也就产生了一些冗余的数据。所以系统会周期性的合并一些数据，移除重复的更新或者删除纪录，同时也会删除上述的冗余。在进行读操作时，如果内存中没有找到相应的key，那么就是倒序从一个个磁盘文件中查找。如果文件越来越多那么读性能就会越来越低，目前的解决方案是采用页缓存来减少查询次数，周期合并文件也有助于提高读性能。在文件越来越多时，可通过布隆过滤器来避免大量的读文件操作。LSM-tree牺牲了部分读性能，以此来换取写入的最大化性能，特别适用于读需求低，会产生大量插入操作的应用环境。

### 文件合并和元数据优化

目前的大多数文件系统，如XFS/Ext4、GFS、HDFS，在元数据管理、缓存管理等实现策略上都侧重大文件。上述基于磁盘I/O特性设计的系统都有一个共性特点就是都运行在这些文件系统之上。这些文件系统在面临海量时在性能和存储效率方面都大幅降低，本节来探讨下海量小文件下的系统设计。

常见文件系统在海量小文件应用下性能表现不佳的根本原因是磁盘最适合顺序的大文件I/O读写模式，而非常不适合随机的小文件I/O读写模式。主要原因体现在元数据管理低效和数据布局低效：

元数据管理低效：由于小文件数据内容较少，因此元数据的访问性能对小文件访问性能影响巨大。Ext2文件系统中Inode和Data Block分别保存在不同的物理位置上，一次读操作需要至少经过两次的独立访问。在海量小文件应用下，Inode的频繁访问，使得原本的并发访问转变为了海量的随机访问，大大降低了性能。另外，大量的小文件会快速耗尽Inode资源，导致磁盘尽管有大量Data Block剩余也无法存储文件，会浪费磁盘空间。

数据布局低效：Ext2在Inode中使用多级指针来索引数据块。对于大文件，数据块的分配会尽量连续，这样会具有比较好的空间局部性。但是对于小文件，数据块可能零散分布在磁盘上的不同位置，并且会造成大量的磁盘碎片，不仅造成访问性能下降，还大量浪费了磁盘空间。数据块一般为1KB、2KB或4KB，对于小于4KB的小文件，Inode与数据的分开存储破坏了空间局部性，同时也造成了大量的随机I/O。

对于海量小文件应用，常见的I/O流程复杂也是造成磁盘性能不佳的原因。对于小文件，磁盘的读写所占用的时间较少，而用于文件的open()操作占用了绝大部分系统时间，导致磁盘有效服务时间非常低，磁盘性能低下。针对于问题的根源，优化的思路大体上分为：

针对数据布局低效，采用小文件合并策略，将小文件合并为大文件。
针对元数据管理低效，优化元数据的存储和管理。针对这两种优化方式，业内也出现了许多优秀的开源软件。

### 小文件合并

小文件合并为大文件后，首先减少了大量元数据，提高了元数据的检索和查询效率，降低了文件读写的I/O操作延时。其次将可能连续访问的小文件一同合并存储，增加了文件之间的局部性，将原本小文件间的随机访问变为了顺序访问，大大提高了性能。同时，合并存储能够有效的减少小文件存储时所产生的磁盘碎片问题，提高了磁盘的利用率。最后，合并之后小文件的访问流程也有了很大的变化，由原来许多的open操作转变为了seek操作，定位到大文件具体的位置即可。如何寻址这个大文件中的小文件呢？其实就是利用一个旁路数据库来记录每个小文件在这个大文件中的偏移量和长度等信息。其实小文件合并的策略本质上就是通过分层的思想来存储元数据。中控节点存储一级元数据，也就是大文件与底层块的对应关系；数据节点存放二级元数据，也就是最终的用户文件在这些一级大块中的存储位置对应关系，经过两级寻址来读写数据。

淘宝的TFS就采用了小文件合并存储的策略。TFS中默认Block大小为64M，每个块中会存储许多不同的小文件，但是这个块只占用一个Inode。假设一个Block为64M，数量级为1PB。那么NameServer上会有 1 1024 1024 * 1024 / 64 = 16.7M个Block。假设每个Block的元数据大小为0.1K，则占用内存不到2G。在TFS中，文件名中包含了Block ID和File ID，通过Block ID定位到具体的DataServer上，然后DataServer会根据本地记录的信息来得到File ID所在Block的偏移量，从而读取到正确的文件内容。TFS一次读过程如下图所示：

![img](https://awps-assets.meituan.net/mit-x/blog-images-bundle-2017/1c284a10.png)

### 元数据管理优化

一般来说元数据信息包括名称、文件大小、设备标识符、用户标识符、用户组标识符等等，在小文件系统中可以对元数据信息进行精简，仅保存足够的信息即可。元数据精简可以减少元数据通信延时，同时相同容量的Cache能存储更多的元数据，从而提高元数据使用效率。另外可以在文件名中就包含元数据信息，从而减少一个元数据的查询操作。最后针对特别小的一些文件，可以采取元数据和数据并存的策略，将数据直接存储在元数据之中，通过减少一次寻址操作从而大大提高性能。

TFS中文件命名就隐含了位置信息等部分元数据，从而减少了一个元数据的查询操作。在Rerserfs中，对于小于1KB的小文件，Rerserfs可以将数据直接存储在Inode中。

## 总结

本文从磁盘性能指标出发，探究了操作系统与磁盘的交互以及对磁盘读写的优化，最后列举了一些常用开源系统中基于磁盘I/O特性的设计特点。期望通过展现磁盘I/O的特性，为存储系统设计和解决一些系统性能问题提供一种新思路。



## **参考文献**

1. IBM developerWorks，[AIX 下磁盘 I/O 性能分析](https://www.ibm.com/developerworks/cn/aix/library/1203_weixy_aixio/)，2012。
2. CSDN博客频道，[磁盘性能评价指标—IOPS和吞吐量](http://blog.csdn.net/hanchengxi/article/details/19089589)，2014。
3. IBM developerWorks，[read 系统调用剖析](https://www.ibm.com/developerworks/cn/linux/l-cn-read/)，2008。
4. IBM developerWorks，[从文件 I/O 看 Linux 的虚拟文件系统](https://www.ibm.com/developerworks/cn/linux/l-cn-vfs/)，2007。
5. CSDN博客频道，[Linux文件系统预读](http://blog.csdn.net/kai_ding/article/details/17322787)，2013。
6. Linux Kernel Exploration，[Linux通用块设备层](http://www.ilinuxkernel.com/files/Linux.Generic.Block.Layer.pdf)。
7. CSDN博客频道，[Linux块设备的IO调度算法和回写机制](http://blog.csdn.net/hustyangju/article/details/40507647)，2014。
8. Apache，[Kafka](http://kafka.apache.org/)。
9. Taobao，[Taobao File System](http://tfs.taobao.org/)。