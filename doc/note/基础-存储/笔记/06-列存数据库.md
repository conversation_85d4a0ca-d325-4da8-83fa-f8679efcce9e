## 概述

关于列存数据库(columnar DBMS)网上有很多资料，大部分都会出现这样图

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230711203650122.png" alt="image-20230711203650122" style="zoom:50%;" />



我对这里有两个疑问：

1. 列存中同一列的数据存在一起，插入操作不会涉及数据的移动吗？这种每次插入都要挪动数据位置的效率很低吧
2. HBase算列存吗？

找了一些资料

问题①，不同的列存数据库(druid、clickhouse、starRocks)实现方式不同，比如druid就是把不同列存在不同文件上，这样插入就不会涉及到数据的移动。

问题②，HBase算是基于列簇(Family)的数据库，列簇之间是独立存储的，同一个列簇下的不同字段还是保存在一起的。具体来说，比如这样一张表：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230711202129657.png" alt="image-20230711202129657" style="zoom:50%;" />

HBase 底层的 KV 存储大概如下所示的：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230711202715917.png" alt="image-20230711202715917" style="zoom:60%;" />

图中每一行数据：

- CF 是 Column Family
- CQ是 Column Qualifier
- 每条数据都会带上 timestamp

从上图可以看出：

- 不同的列族存在不同的文件中（上面两个表格代表不同的 HFile）；
- 整个数据是按照 Rowkey 进行字典排序的；
- 每一列数据在底层 HFile 中是以 KV 形式存储的，key是rowkey+cf+cq+ts，value是value
- 相同的一行数据中，如果列族也一样，那么这些数据是顺序放在一起的。

到这里大家应该可以看到，**HBase 其实不是列式数据库**，因为同一行数据，如果列族也一样，这些数据是存储在相邻位置的；这和上面的列式存储不太一样。所以说，HBase 既不像行式存储，又不像列式存储。它其实更像是面向列族的存储数据库，因为不同行相同的列族数据是相邻存储的；而同一行不同列族的数据是存储在不同位置的。

## 参考

- [The design and implementation of modern column-oriented database systems](https://blog.acolyer.org/2018/09/26/the-design-and-implementation-of-modern-column-oriented-database-systems/)
- [build-a-nosql-database-from-the-scratch-in-1000-lines-of-code](https://betterprogramming.pub/build-a-nosql-database-from-the-scratch-in-1000-lines-of-code-8ed1c15ed924)
- [简单说说列式存储数据库](https://juejin.cn/post/7056820168239874062#heading-5)
- [HBase 是列式存储数据库吗](https://blog.csdn.net/JacksonKing/article/details/107617608)
- [深入分析 Druid 存储结构](https://www.infoq.cn/article/7tuvqugp142tszpdr3eb)



