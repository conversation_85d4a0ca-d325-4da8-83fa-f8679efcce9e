## 概述

本文基于chatGPT整理，问题来源是es无法很好管理大量分片，那么Hbase是否可以管理大量分片呢？问了这些问题

1. hbase 有表的概念吗?不同业务之间的数据如何隔离
2. HBase 写入数据是否需要先创建表，创建表需要哪些设置
3. 如何使用 使用 HBase Shell 写入数据？
4. hbase 内部实现中表是如何存储的？HFiles会不会过多？ HFiles合并操作是只会同一个表内的HFiles合并还是不同表的HFiles也会合并？
5. 搭建一个hbase集群需要部署哪些东西?
6. 当 Client 申请创建一张表，HBase会发生什么?
7. es可以通过调大索引的Shard数量来提升性能，Hbase中一个表是否也有类似的升缩机制?
8. 当 Client 向Hbase写入数据，Hbase会发生什么？
9. 当 Client 向Hbase执行scan，Hbase会发生什么？



## 表的概念

HBase 是一个基于列存储的分布式数据库，在 HBase 中，**数据以表的形式组织**，每个表由行键（Row Key）、列族（Column Family）和列限定符（Column Qualifier）组成。

- 表（Table）: 类似于传统关系型数据库中的表格，表是数据的逻辑组织形式。每张表有一个唯一的名称，并且可以包含多个列族。
- 行键（Row Key）: 每一行都有一个唯一的行键，这些行键在表内是唯一的。
- 列族（Column Family）: 列族是表的一个重要组成部分，一个表可以包含多个列族。列族在表的创建时定义，并且列族内的列可以在运行时动态增加。
- 列限定符（Column Qualifier）: 每个列族下可以有多个列，列由列族名称和列限定符组成。

**一般来说，HBase 可以支持成百上千张表，但如果表的数量非常多（例如上万张）**，可能需要特别设计和优化集群资源，以确保性能和稳定性

### 创建表

在 HBase 中，创建表通常使用 **HBase Shell** 或通过 Java API 来完成。以下是 HBase Shell 中创建表的示例：

```
create '表名', '列族1', '列族2'
```

### 设置参数

在创建表时，可以指定以下设置：

- **表名**: 你需要指定一个唯一的表名，用于标识表。

- **列族（Column Family）**: 至少需要定义一个列族，每个列族在表中代表一组相关的列。列族名称通常是简短的字符串，列族需要在创建表时定义。

- **版本数（VERSIONS）**: 可以设置一个列族最多存储多少个版本的值。默认情况下，HBase 允许存储 1 个版本的值。

  ```shell
  create '表名', {NAME => '列族1', VERSIONS => 3}
  ```

- **压缩（COMPRESSION）**: 可以为列族指定压缩算法，比如 `NONE`（无压缩）、`SNAPPY`、`GZIP` 等。

  ```shell
  create '表名', {NAME => '列族1', COMPRESSION => 'SNAPPY'}
  ```

- **TTL（Time to Live）**: 可以为列族中的数据设置生存时间，超过生存时间的数据将被自动删除。TTL 的单位是秒。

  ```shell
  create '表名', {NAME => '列族1', TTL => 86400}  # 1 天
  ```

- **Block Size**: 设置列族的块大小，通常用于优化性能。

  ```shell
  create '表名', {NAME => '列族1', BLOCKSIZE => 65536}
  ```

### 写入数据

HBase Shell 提供了简单的命令来插入数据，使用 `put` 命令可以将数据插入到指定的表中。

```
put '表名', '行键', '列族:列限定符', '值'
```



## HBase 集群构成

搭建 HBase 集群所需的主要组件骤：

### 1. **HDFS（Hadoop Distributed File System）**

- **作用**: HBase 依赖于 HDFS 来存储数据。HDFS 是 HBase 的底层存储系统，提供分布式文件存储。
- **部署**: 需要部署和配置 HDFS 集群，包括 NameNode、DataNode、Secondary NameNode（可选）等组件。

### 2. **Zookeeper**

- **作用**: Zookeeper 在 HBase 中用于集群的协调和管理，包括追踪 RegionServer 的状态、存储 HBase 的元数据（例如表的配置信息）等。
- **部署**: 需要部署一个 Zookeeper 集群，通常至少部署 3 个节点以保证高可用性。

### 3. **HBase Master**

- **作用**: HBase Master 负责管理集群的元数据和协调 Region 的分配和移动等操作。HBase 集群可以有一个主 HBase Master 和若干备份 HBase Master。
- **部署**: 部署至少一个 HBase Master 节点。为了高可用性，可以部署多个 HBase Master，但只有一个处于活跃状态。

### 4. **RegionServer**

- **作用**: RegionServer 是 HBase 集群的核心组件，负责管理和处理具体的 Regions，响应客户端的读写请求，并将数据写入 HDFS 的 HFiles。
- **部署**: 根据集群的大小和数据负载，部署多个 RegionServer 节点。



## HBase 内部原理

### 数据的存储

表中的数据是通过Region组成的，每个 Region 会有自己的一组 HFiles,如图：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240815112201668.png" alt="image-20240815112201668" style="zoom:50%;" />

说明：

- 随着表的数据增长，一个 Region 可能会分裂成多个 Region。
- 每个 Region 会有自己的一组 HFiles，HBase 会定期执行的 Compaction 操作会将多个较小的 HFiles 合并为一个较大的文件，从而减少文件数量。不过**合并只会在同一个表内的 HFiles 进行合并，不会跨表进行合并**，每个表的 Compaction 操作都是独立进行的，不会影响其他表。
- 每个 Region 的数据存储在 HDFS 上的 HFiles 中。这些 HFiles 只包含该 Region 所属表的数据，确保了数据的隔离性和一致性。
- 每个Region被对应的RegionServer管理



### 创建表的过程

当一个客户端（Client）申请在 HBase 中创建一张表时，**HBase Master主要负责创建Region元数据，并分配对应的RegionServer**

以下是大致流程：

1、客户端请求（Client Request）

- 客户端使用 HBase API 或 HBase Shell 发出创建表的请求，包含表名、列族定义和其他配置参数。

2.、HBase Master 接收请求

- 创建表的请求首先被发送到 HBase Master。
- HBase Master 负责管理表的元数据，因此它会验证请求的有效性，比如检查表名是否已存在等。

3、更新元数据

- 如果验证通过，HBase Master 会将表的元数据信息（如表名、列族定义等）记录到 HBase 的 hbase:meta 表中。hbase:meta 是一个特殊的表，存储了 HBase 集群中所有表的元数据。
- 这个过程涉及到对 hbase:meta 表的写操作，保证新表的元数据被正确记录。

4、Region 初始化

- HBase Master 负责初始化这个新表的 Region。通常，表在创建时会有一个初始的 Region，包含表的全范围行键（Row Key）。
- 该 Region 的信息也会被写入 hbase:meta 表，并指定由哪个 RegionServer 管理。

5、通知 RegionServer

- HBase Master 通知相关的 RegionServer，告知它们有新的 Region 被分配给它们进行管理。
- RegionServer 准备管理新表的 Region，并为它们分配资源。

6、完成表创建

- 当所有步骤完成后，HBase Master 会向客户端返回确认，表创建成功。
- 客户端此时可以开始向新表中插入数据。



### 写入数据的过程

当客户端（Client）向 HBase 写入数据时，HBase会路由到对应的RegionServer，RegionServer先写入WAL在写入到HFile。

以下是详细的步骤：

1、 客户端请求
- 客户端通过 HBase API 向 HBase 发送写入请求（例如 put 操作），指定表名、行键、列族、列限定符和数据值。

2、请求路由
- HBase Master 或 Zookeeper（主要用于位置查找）根据表的元数据确定数据所在的 Region 和对应的 RegionServer。
- 客户端会被路由到负责该 Region 的 RegionServer。

3、写入到 MemStore
- RegionServer 接收到写入请求后，会将数据写入到 MemStore。MemStore 是一个内存中的数据结构，用于暂存即将写入 HFiles 的数据。
- 数据在 MemStore 中是按行键排序的。MemStore 存储的数据会根据时间间隔和数据量进行定期刷写（flush）。

4、WAL（Write-Ahead Log）写入
- WAL 是 HBase 的日志机制，用于记录所有写入操作。每个 RegionServer 会将写入的数据同时写入到 WAL 中。WAL 确保了数据在系统故障情况下的持久性。
- 如果发生系统崩溃或 RegionServer 重启，可以通过 WAL 恢复未持久化的数据。

5、 数据刷写（Flush）
- 当 MemStore 达到预定的大小（或超时）时，HBase 会触发刷写操作，将 MemStore 中的数据持久化到 HDFS 中的 HFiles。这个过程称为 Flush。
- MemStore 被清空后，可以再次接收新的写入数据。

6、Compaction（合并）
- HFiles 会随着时间的推移积累多个文件，这些文件会通过定期的 Compaction 操作进行合并。Compaction 将较小的 HFiles 合并为较大的 HFiles，以提高读取性能和优化存储。
- Compaction 会将旧的 HFiles 中的数据合并并去除过期的数据，保持 HFiles 的有效性和高效性。

7、更新元数据
- 写入操作涉及到表的元数据更新，如写入行键到 hbase:meta 表（对于新表或表的结构变化时）。这些更新保证了集群中各个 RegionServer 对表的正确管理。

### 执行 Scan 的过程

这里只是简要概述一下，和put请求类似，也是先根据zk上表的获取到对应的RegionServer，客户端请求被路由到相应的 RegionServer，这些 RegionServer 负责处理扫描操作。

RegionServer 处理请求: 接收到扫描请求后，确定要扫描的 Region 和数据范围。
- MemStore: 首先从 MemStore 中读取数据。MemStore 是内存中的缓存，包含最新的写入数据。
- HFiles: 接下来，RegionServer 从 HDFS 上的 HFiles 中读取数据。HFiles 存储了已经持久化的数据，按行键排序。
- 行键范围: RegionServer 根据扫描请求中指定的行键范围来过滤数据，只读取符合条件的数据。



## 参考

- [网易-HBase最佳实践 – 集群规划](https://sq.sf.163.com/blog/article/155473761979842560)
- [网易范欣欣-HBase最佳实践 – Scan用法大观园](http://www.uml.org.cn/bigdata/201801162.asp)