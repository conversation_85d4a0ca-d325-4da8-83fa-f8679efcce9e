流式计算系统方兴未艾。为了反映现实世界事件驱动的特性，为了对接消息队列事件驱动的设计，为了获得更好的时延，越来越多的业务采用流式计算系统来处理它们的数据。在批流统一的理论指导下，可想而知，未来的计算是属于流式计算的天下。

从 2018 年年中参与 Flink 社区的开发，到在阿里巴巴 BLINK 团队和鹅厂数据中心的 FLINK 团队基于 Flink 支持了诸多流式计算作业的运行，这段时间的经历使我深深地体会到了自己作为本领域新人的局限性。虽然在我优秀的导师施晓罡博士的指导和帮助下，偶然的成为 Flink Committer，但是为了填平技术上的泡沫，还是必须多做思考和学习。

近日拿到了若干本流式计算系统相关的经典书籍，结合 Apache Flink 及相关项目的源码开始锻炼自己在本领域更深层次的理解和运用。在此过程中偶有所得，一并记录为《流式计算系统系列》系列文章。小作分享，以飨读者。

### 文章链接

- [流式计算系统系列（1）：恰好一次处理](https://zhuanlan.zhihu.com/p/102607983)
- [流式计算系统系列（2）：时间](https://zhuanlan.zhihu.com/p/103472646)
- [流式计算系统系列（3）：窗口](https://zhuanlan.zhihu.com/p/103890281)
- [流式计算系统系列（4）：状态](https://zhuanlan.zhihu.com/p/119305376)

