## 架构

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250729172756973.png" alt="image-20250729172756973" style="zoom:50%;" />

分层架构：

| Tier             | Component                       | Purpose                                               | File Types                |
| ---------------- | ------------------------------- | ----------------------------------------------------- | ------------------------- |
| **Coordination** | `HStore`                        | Route requests to buckets, manage global operations   | N/A                       |
| **Partitioning** | `Bucket`                        | Logical data partition, coordinate storage components | N/A                       |
| **Storage**      | `DataStore`, `HintMgr`, `HTree` | Physical data, indexing, hash trees                   | `.data`, `.s/.m`, `.hash` |

Bucket Distribution Strategy

| Configuration        | Bucket Count | Use Case                           |
| -------------------- | ------------ | ---------------------------------- |
| **Small Deployment** | 16           | Single-node testing, development   |
| **Production**       | 256          | Multi-node production clusters     |
| **Custom**           | 16 × N       | Configurable based on cluster size |

The bucket distribution is managed through the `RouteTable` configuration, which maps buckets to physical servers in the cluster. This static routing approach provides predictable performance characteristics and simplifies cluster management.

### Core Structure

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-*****************.png" alt="image-*****************" style="zoom:50%;" />

`HStore` serves as the top-level storage coordinator, managing bucket lifecycle, routing operations to appropriate buckets, and coordinating system-wide operations like garbage collection.

`Bucket` represents a logical data partition that coordinates read/write operations across its storage components. Each bucket manages its own `dataStore`, `hintMgr`, and `HTree` instances.

### 物理存储结构

据bucket数量的不同，系统采用不同的目录结构： config.go:98-107

- **16个bucket**: 使用单层目录 `{bucketID}` (如 `0/`, `1/`, `f/`)
- **256个bucket**: 使用两层目录 `{bucketID/16}/{bucketID%16}` (如 `0/0/`, `0/1/`, `f/f/`)

每个bucket目录下包含该bucket的所有数据文件和索引文件： data.go:53-54

```
base-dir/  
├── 0/  
│   ├── 0/          # bucket 0  
│   │   ├── 000.data  
│   │   ├── 000.000.idx.s  
│   │   ├── 001.data  
│   │   └── 001.000.idx.s  
│   └── 1/          # bucket 1  
│       ├── 000.data  
│       └── 000.000.idx.s  
```

索引hint文件和data文件放在同一个bucket目录下

每个bucket包含三种类型的文件： bucket.go:23-27

- **数据文件**: `NNN.data` - 存储实际的key-value记录
- **索引文件**: `NNN.SSS.idx.s` - 存储key到位置的映射
- **合并索引**: `merged.m` - 合并后的索引文件
- **哈希树文件**: `NNN.SSS.idx.hash` - 序列化的哈希树快照



Bucket 分配策略

| Configuration        | Bucket Count | Use Case                           |
| -------------------- | ------------ | ---------------------------------- |
| **Small Deployment** | 16           | Single-node testing, development   |
| **Production**       | 256          | Multi-node production clusters     |
| **Custom**           | 16 × N       | Configurable based on cluster size |



### Data Storage and Records

The `Record` structure represents a key-value pair with metadata. The `Payload` contains both the metadata (`Meta`) and the actual value data stored in a `cmem.CArray` for efficient memory management. The `WriteRecord` wraps a `Record` for serialization with additional fields like CRC checksum and size information.

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250729174835376.png" alt="image-20250729174835376" style="zoom:50%;" />

The header encoding uses little-endian format:

- **CRC32**: Checksum of header (excluding CRC field) + key + value
- **Sizes**: Used for record parsing and validation
- **Padding**: Ensures all records align to 256-byte boundaries (`PADDING = 256`)



Data is organized into numbered chunks, each stored as a separate `.data` file. The `dataStore` manages these chunks and handles record appending with automatic chunk rotation.

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250729174632451.png" alt="image-20250729174632451" style="zoom:50%;" />

The system maintains up to 998 data chunks (`MAX_NUM_CHUNK`), with each chunk limited by `Conf.DataFileMax` size. When a chunk reaches capacity, the system rotates to the next chunk and triggers background flushing of the previous chunk.

Data files follow a consistent naming pattern:

- Format: `{home}/{chunkID:03d}.data`
- Examples: `000.data`, `001.data`, `002.data`
- Generated by: `genDataPath(home, chunkID)` function

### Indexing and Hint 

The indexing and hint system provides efficient key-to-position mapping for data stored in gobeansdb, enabling fast record lookups without scanning through data files. This system maintains in-memory and on-disk indexes that map key hashes to their physical storage locations, handles hash collisions, and performs background maintenance operations like merging and garbage collection coordination.

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250730103303656.png" alt="image-20250730103303656" style="zoom:50%;" />

The hint system uses a hierarchy of data structures to efficiently manage key-to-position mappings with collision resolution and memory management.

| Structure    | Purpose                    | Key Fields                                   | Location                                                     |
| ------------ | -------------------------- | -------------------------------------------- | ------------------------------------------------------------ |
| `HintItem`   | Individual hint record     | `Keyhash`, `Key`, `Position`, `Ver`, `Vhash` | [store/item.go53-60](https://github.com/douban/gobeansdb/blob/16a6a291/store/item.go#L53-L60) |
| `HintBuffer` | In-memory hint collection  | `index`, `collisions`, `items[]`             | [store/hint.go93-99](https://github.com/douban/gobeansdb/blob/16a6a291/store/hint.go#L93-L99) |
| `hintSplit`  | Buffer + file coordination | `buf`, `file`                                | [store/hint.go101-104](https://github.com/douban/gobeansdb/blob/16a6a291/store/hint.go#L101-L104) |
| `hintChunk`  | Per-chunk hint management  | `splits[]`, `lastTS`                         | [store/hint.go214-222](https://github.com/douban/gobeansdb/blob/16a6a291/store/hint.go#L214-L222) |
| `hintMgr`    | System coordinator         | `chunks[]`, `collisions`, `merged`           | [store/hint.go303-321](https://github.com/douban/gobeansdb/blob/16a6a291/store/hint.go#L303-L321) |



### HTree

HTree是gobeansdb的核心内存索引结构，它是一个16叉的Merkle树，每个bucket都会维护自己的HTree，HTree记录了data的索引(Postion)

设计优势

- **内存效率**: 16叉树结构在内存使用和查找性能之间取得平衡
- **同步友好**: Merkle树特性使得数据同步变得高效
- **扩展性**: 支持不同的树高度配置以适应不同规模的数据

主要有以下几个关键作用：

**1、内存索引和快速查找**

HTree为**每个bucket提供全内存的key索引，支持快速的key查找操作**。 htree.go:25-59

它通过16叉树结构将key的hash值映射到具体的存储位置，实现O(log16 N)的查找复杂度。

**2、数据同步和一致性检查**

HTree最重要的作用是支持分布式数据同步。每个bucket维护一个16叉的内存Merkle树，通过比较不同副本的htree来检测数据差异，实现最终一致性。

**3、存储操作的核心接口**

HTree提供了key-value操作的核心接口：

- **写操作**: `set(ki, meta, pos)` - 更新key的元数据和位置信息 htree.go:287-294
- **读操作**: `get(ki)` - 根据key查找元数据和位置 htree.go:313-320
- **删除操作**: `remove(ki, oldPos)` - 移除指定key的记录 htree.go:305-311

**4、与Bucket的集成**

在Bucket层面，HTree与其他存储组件协同工作： bucket.go:395-403

```go
func (bkt *Bucket) set(ki *KeyInfo, v *Payload) error {  
    pos, err := bkt.datas.AppendRecord(&Record{ki.Key, v})  
    if err != nil {  
        return err  
    }  
    bkt.htree.set(ki, &v.Meta, pos)  // 更新HTree索引  
    bkt.hints.set(ki, &v.Meta, pos, v.RecSize, "set")  
    return nil  
}
```

5、持久化和恢复

HTree支持将内存结构序列化到磁盘的`.hash`文件中，并在系统重启时恢复： htree.go:146-203

6、全局树结构

在HStore层面，还有一个全局的HTree用于管理所有bucket的汇总信息： hstore.go:188-209





## 系统加载

系统初始化时，会调用 `bucket.open()` 方法加载bucket，主要逻辑是恢复htree。

这里我有个疑问：

> htree包含了所有索引信息，既然htree都持久化了，那hint文件存在的意义是什么

这里需要明确 hint 文件和 htree 文件在 gobeansdb 中的区别：**hint 文件用于增量索引构建和冲突解决，而 htree 文件是内存索引的快照备份**。

在系统启动时，`bucket.open()` 方法首先加载最新的 htree 快照，然后使用 hint 文件来增量更新索引。这个过程通过 `updateHtreeFromHint()` 方法实现，它读取 hint 文件中的条目并更新内存中的 htree。

HTree 文件只是内存索引在特定时间点的快照。 它通过 `dumpHtree()` 方法定期生成，但不包含最新的数据变更。HTree 主要基于 hash 值进行索引，无法直接处理 hash 冲突情况，这需要依赖 hint 系统的冲突解决机制。

在数据恢复过程中，系统首先加载 htree 快照作为基础索引，然后通过 `checkHintWithData()` 方法处理 hint 文件，确保索引的完整性和一致性。 bucket.go:153-164 如果 hint 数据大小小于实际数据文件大小，系统会调用 `buildHintFromData()` 重建缺失的 hint 信息。



## GetKey过程

### 入口

入口在bucket的get()方法，该方法协调多个存储组件来查找和返回数据。

### 1. 冲突检查

```go
	hintit, _ := bkt.hints.collisions.get(ki.KeyHash, ki.StringKey)
```

bkt.hints.collisions 保存了出现hash冲突的 key，如果在冲突表中找到了对应的 hint 项，直接使用该项的位置信息和元数据

### 2、HTree 索引查找

如果冲突表中没有找到，则通过 htree 进行索引查找

```go
if hintit == nil {
  meta, pos, found = bkt.htree.get(ki)
  if !found {
    return
  }
```

HTree 的 `get()` 方法会根据 key 的 hash 值定位到对应的叶子节点并查找记录

### 3、数据文件读取

获得位置信息后，从数据文件中读取实际的记录:

```go
	rec, inbuffer, err := bkt.datas.GetRecordByPos(pos)
```

### 4、 数据验证

读取记录后进行多层验证：

```go
	keyhash := getKeyHash(rec.Key)
	if keyhash != ki.KeyHash {
		if inbuffer && pos.ChunkID < bkt.datas.newHead-1 {
			logger.Warnf("get out-of-date pos during gc, should be rarely seen, omit it, pos %v", pos)
			payload = nil
			return
		}

		// not remove for now: it may cause many sync
		// bkt.htree.remove(ki, pos)
		err = fmt.Errorf("bad htree item want (%s, %016x) got (%s, %016x), pos %x, inbuffer %v",
			ki.Key, ki.KeyHash, rec.Key, keyhash, pos, inbuffer)
		logger.Errorf("%s", err.Error())
		return
	}

```

### 5、冲突处理

如果发现 hash 相同但 key 不同（hash 冲突），系统会进行特殊的冲突处理：

```go
	// here: same key hash, diff key
	hintit, chunkID, err := bkt.hints.getItem(ki.KeyHash, ki.StringKey, false)
	if err != nil || hintit == nil {
		return
	}

	vhash := uint16(0)
	if rec.Payload.Ver > 0 {
		vhash = Getvhash(rec.Payload.Body)
	}
	hintit2 := newHintItem(ki.KeyHash, rec.Payload.Ver, vhash, pos, string(rec.Key))
	bkt.hints.collisions.compareAndSet(hintit2, "get1") // the one in htree

	pos = Position{chunkID, hintit.Pos.Offset}
	hintit.Pos = pos
	bkt.hints.collisions.compareAndSet(hintit, "get2") // the one not in htree
```

这个过程包括：

- 从 hint 系统获取正确的记录位置
- 更新冲突表中的记录
- 重新读取正确的数据





## HTree 原理

beansdb和bitcast最大的区别是每个bucket包含了一个HTree， HTree方便用于同步数据，同时HTree不会记录key原始字符串，而是index，可以节约内存

HTree的构造函数需要传递两个参数depth和height:

```go
func newHTree(depth, bucketID, height int) *HTree {
	if depth+height > MAX_DEPTH {
		panic("HTree too high")
}
```

要理解这两个参数的含义需要理解htree结构。

在beansdb中， 每个bucket代表一个htree，所有的bucket(htree)又组成了一个全局的htree，这个全局的htree定义在HStore中：

```go
type HStore struct {
	buckets   []*Bucket
	gcMgr     *GCMgr
	htree     *HTree
	htreeLock sync.Mutex
}
```



<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250730174033075.png" alt="image-20250730174033075" style="zoom:50%;" />

depth 表示 hstore 整颗树的深度级别，很明显和bucket数量相关，16的depth为1，16\*16(256)的depth为2，16\*16*16的depth为3，对应的代码如下：

```go
func (c *HStoreConfig) InitTree() error {
    // TreeDepth
    n := c.NumBucket
    c.TreeDepth = 0
    for n > 1 {
        c.TreeDepth += 1
        n /= 16
    }
}
```

height 表示单个 HTree 的高度，通过配置文件设置，控制单个树的高度，影响内存使用和查找性能，默认是3。


### 初始化

```go
func newHTree(depth, bucketID, height int) *HTree {
	if depth+height > MAX_DEPTH {
		panic("HTree too high")
	}
	tree := new(HTree)
	tree.depth = depth
	tree.bucketID = bucketID
	tree.levels = make([][]Node, height)//通过高度初始化二维数组levels
	size := 1
	for i := 0; i < height; i++ {
		tree.levels[i] = make([]Node, size)
		size *= BUCKET_SIZE
	}
	size /= BUCKET_SIZE
	leafnodes := tree.levels[height-1] //初始化叶子节点，leafnodes大小为256
	for i := 0; i < size; i++ {
		leafnodes[i].isHashUpdated = true
	}
	tree.leafs = make([]SliceHeader, size)
	return tree
}
```

levels 是二维数组，记录了树的状态，比如 height 为 3的情况

```go
levels[0]= node[1] //root只有一个节点
levels[1]= node[16] //16个节点
levels[2]= node[256] //256个节点
```

### 查找过程

1、入口


```go

func (tree *HTree) getReq(req *HTreeReq) (found bool) {
    tree.Lock()
    defer tree.Unlock()
    ni := &tree.ni //复用NodeInfo

    //①找到叶子节点对应的offset
    tree.getLeaf(req.ki, ni)

    //②叶子节点找到对应的value
    found = tree.leafs[ni.offset].Get(req)
    return
}

```

2、叶子节点的索引位置计算

HTree 手的路径计算是通过 KeyInfo 结构中的 KeyPath 字段来实现的。

KeyPath 通过 ParsePathUint64()，函数将 64 位 hash 值解析成路径数组，每个元素代表一个 4 位的十六进制数字，一共16个：

```
public static int[] parsePathUint64(long khash, int[] buf) {
    for (int i = 0; i < 16; i++) {
        int shift = 4 * (15 - i);
        int idx = (int)((khash >> shift) & 0xf);
        buf[i] = idx;
    }
    return buf;
}
```

例如，hash 值 `0x89abcdef01234567` 被解析成路径数组 `[8, 9, a, b, c, d, e, f, 0, 1, 2, 3, 4, 5, 6, 7]`，每个元素代表一个 4 位的十六进制数字。

然后计算offset，这个过程通过以下步骤实现：

- 从 `ki.KeyPath[tree.depth:]` 开始，跳过全局树的深度部分
- 逐层计算偏移量：`ni.offset = ni.offset*16 + path[level-1]`
- 最终得到叶子节点的索引位置

举个例子，假设htree的高度为 3，深度为2，设有一个 hash 值 对应的路径数组是 `[1,9,0,2, 8, 9, a, b...]`，首先前两位确定bucketId为25

> 提示：如果bucket数量只有16，即深度为1，则bucketId只有0-15，所以bucket数量关系是很大的

然后开始计算offset，步骤如下：

- 初始：`ni.offset = 0`
- Level 1：`ni.offset = 0*16 + path[0] = 0*16 + 8 = 8`
- Level 2：`ni.offset = 8*16 + path[1] = 8*16 + 9 = 137`
- 最终叶子节点索引：137

为什么要跳过全局树的depth?

因为depth对应了bucket数量，用于将 key 路由到不同的 bucket，实际上bucketId就是前depth数量计算的：

```java
//key.go 
public void prepare() throws Exception {
    keyPath = parsePathUint64(keyHash, Arrays.copyOf(keyPathBuf, 16));
    bucketID = 0;
   //通过keyPath前TreeDepth计算bucketID
    for (int i = 0; i < Conf.TreeDepth; i++) {
        bucketID <<= 4; 
        bucketID += (keyPath[i] & 0xFF);  
    }
}
```

总结，假设系统配置为 256 个 bucket（`NumBucket = 256`）：

1. **全局树深度计算**：256 = 16²，所以 `TreeDepth = 2`
2. 路径分配
   - 前 2 层路径用于全局路由（确定 bucket）
   - 后续路径用于 bucket 内部索引

以 hash 值 `0x89abcdef01234567` 为例，路径数组为 `[8, 9, 0xa, 0xb, ...]`：

- `path[0:2] = [8, 9]` 用于确定 bucket ID
- `path[2:] = [0xa, 0xb, ...]` 用于 bucket 内部的 HTree 索引



3、查找数据

当定位到叶子节点之后，会通过二分查找或线性查找定位具体的 key：

```go
func findInBytes(leaf []byte, keyhash uint64) int {
    lenKHash := Conf.TreeKeyHashLen
    lenItem := lenKHash + TREE_ITEM_HEAD_SIZE
    size := len(leaf)
    var khashBytes [8]byte
    khashToBytes(khashBytes[0:], keyhash)
    kb := khashBytes[:lenKHash]
    n := len(leaf) / lenItem
    if n < LEN_USE_C_FIND {
    for i := 0; i < size; i += lenItem {
    if bytes.Compare(leaf[i:i+lenKHash], kb) == 0 {
      return i
    }
    }
    } else {
    ss := (*reflect.SliceHeader)((unsafe.Pointer(&leaf))).Data
    s := (*reflect.SliceHeader)((unsafe.Pointer(&kb))).Data
    i := int(C.find((unsafe.Pointer(ss)), unsafe.Pointer(s), C.int(lenItem), C.int(lenKHash), C.int(n)))
    return i * lenItem
    }
    return -1
}
```

### 总结

这个设计思路在分布式系统和数据库领域中相当常见，它本质上是一种**分层哈希**（Hierarchical Hashing）或者**哈希树**（Hash Tree）的应用。这种方法结合了哈希的快速查找和树状结构的有序性，能够高效地管理和路由大量数据。

`gobeansdb` 通过 `hash(key)` 将键映射为一个64位的长整型，然后将这个长整型切分为多个部分进行逐级路由。这种分层路由的设计有几个主要优点：

- **减少热点**：通过将哈希值切分成多个部分，可以避免所有数据都集中在少数几个节点上。前两位的 `keyPath` 确定 `bucketId`，这相当于将所有数据均匀地分散到16x16=256个桶中（如果每个 `keyPath` 位是4位，那么前两位就是8位，可以表示 `2^8=256` 个桶）。这种分桶机制能有效地分散负载，减少单个节点的压力。
- **高效的查找**：在每个桶内部，使用16叉哈希树进行路由。由于哈希树的查找时间复杂度是 `O(logN)`，其中 `N` 是哈希树的深度，而这个深度是由 `keyPath` 的长度决定的，所以查找效率非常高。你举例中的 `ni.offset = 8*16 + 9 = 137`，其实是把 `[8, 9]` 这两个路径值组合成一个偏移量，这正是多级索引（或多叉树）的查找方式。
- **可扩展性**：这种分层结构天然支持扩展。当数据量增加时，可以增加哈希树的深度，或者增加桶的数量，从而保持系统的性能。例如，如果 `keyPath` 的长度增加，哈希树的叶子节点数量会呈指数级增长，能够容纳更多的数据。



# 历史记录

## 默认配置

默认配置在 config.go中

```go
func (c *DBConfig) InitDefault() {
	//默认ServerConfig
	c.ServerConfig = config.DefaultServerConfig
	c.MCConfig = config.DefaultMCConfig
	//默认的HStoreConfig
	c.HStoreConfig.InitDefault()
	utils.InitSizesPointer(c)
}
```



## gobeansdb 初始化

1、hstore.NewHStore() 初始化 HStore。
2、初始化 memcached Server。
3、接收网络请求 server.Serve()。

## 处理get请求

1、server.go 中的ServeOnce()响应请求。
2、调用 Request.Read()进行预处理，包括分配token，判断是什么类型等。 #protocol.go
3、调用 Request.Process()方法。 
4、如果是get请求，调用store.Get(key)。 store.go中的StorageClient实现了该方法。
5、StorageClient交给HStore.GetRecordByKeyHash()执行。
6、HStore首先通过KeyInfo中的BucketID得到对应的Bucket，然后调用Bucket.GetRecordByKeyHash()。这里的Bucket代表一个目录。
7、Bucket内部再调用 htree.get(ki)得到数据的pos。 Htree保存这该Bucket下的所有索引数据。
8、如果返回的pos不为空，调用Bucket中dataStore.GetRecordByPos(pos)获取数据。
9、数据是按文件存储的，在代码中用Chunk表示。数据查找先通过pos.ChunkID(文件ID)找到对应的dataChunk，再调用dataChunk.GetRecordByOffset()方法获取数据。



## htree设计思路

### 几个变量

后面几列是通过NumBucket计算的

| TreeHeight | NumBucket | TreeDepth | TreeKeyHashLen |
| ---------- | --------- | --------- | -------------- |
| 3          | 16        | 1         | 7              |
| 3          | 32        | 2         | 6              |
| 3          | 64        | 3         |                |
| 3          | 128       | 4         |                |
| 3          | 256       | 5         |                |

一些约定：

depth+heigh不能超过8，即NumBucket不能超过256，但实际上可以配置NumBucket只能是1，16，256三种，在创建目录是会检查：

```go
func GetBucketDir(numBucket, bucketID int) string {
	if numBucket == 1 {
		return ""
	} else if numBucket == 16 {
		return fmt.Sprintf("%x", bucketID)
	} else if numBucket == 256 {
		return fmt.Sprintf("%x/%x", bucketID/16, bucketID%16)
	}
	panic(fmt.Sprintf("wrong numBucket: %d", numBucket))
}
```



### key 

key 首先会通过hash产生成 8 bytes的long，hash算法如下：

```go
func getKeyHashDefalut(key []byte) uint64 {
	return (uint64(fnv1a(key)) << 32) | uint64(murmur(key))
}
```

然后会被转换为长度为16的数组，每个原始中保存4bit即半个字节，这样每个元素刚好可以被一个16进制表示，代码如下：

```go
func ParsePathUint64(khash uint64, buf []int) []int {
	for i := 0; i < 16; i++ {
        //shift从60开始，每次减小4
		shift := uint32(4 * (15 - i))
		idx := int((khash >> shift)) & 0xf
		buf[i] = int(idx)
	}
	return buf
}
```



 8 bytes的long，也被分为长度为16 高位到低位分三部分：

- 第一部分 定位在那个 bucket。
- 第二部分 定位在 bucket.htree 的哪个叶子
- 第三部分 在叶子对应节点中定位 key 对应的 item





## 更新流程

1、protocol.go中Request.Process()负责处理网络请求，当发现是set类型会调用 StorageClient.Set()方法。
2、StorageClient.Set() 负责初始化 KeyInfo和Payload，然后调用HStore.Set()。
3、HStore.Set()方法中，首先通过key得到hash,调用ki.Prepare()为ki分配所属的BucketID。通过BucketID定位所属的bucket，然后调用bucket.set()。
4、Bucket.set()方法中,首先调用dataStore.AppendRecord()追加数据。调用htree.set()




## StorageClient

定义在store.go中， 实现了StorageClient接口

### Set()

```
func (s *StorageClient) Set(key string, item *mc.Item, noreply bool) (bool, error) {

	//通过prepare()初始化KeyInfo
	ki := s.prepare(key, false);


	//调用hstore.Set
	err := s.hstore.Set(ki, payload)
	if err != nil {
		logger.Errorf("err to get %s: %s", key, err.Error())
		return false, err
	}
	return true, nil
}

```

### prepare()

```
func (s *StorageClient) prepare(key string, isPath bool) *store.KeyInfo {
	ki := &store.KeyInfo{}
	ki.StringKey = key
	ki.Key = []byte(key)
	ki.KeyIsPath = isPath
}
```



## HStore

定义在hstore.go中

### Set()

```go
func (store *HStore) Set(ki *KeyInfo, p *Payload) error {
	//根据key得到keyHash
	ki.KeyHash = getKeyHash(ki.Key)

	//该方法内部会为ki分配bucketID
	ki.Prepare()
	//根据BucketID得到bucket
	bkt := store.buckets[ki.BucketID]
	atomic.AddInt64(&bkt.NumSet, 1)
	if bkt.State != BUCKET_STAT_READY {
		cmem.DBRL.SetData.SubSizeAndCount(p.CArray.Cap)
		p.CArray.Free()
		return nil
	}

	return bkt.checkAndSet(ki, p)
}

```


## Htree

### set()

更新htree。把ki简单封装，调用setReq()

```go
func (tree *HTree) set(ki *KeyInfo, meta *Meta, pos Position) {
	var req HTreeReq
	req.ki = ki
	req.Meta = *meta
	req.Position = pos
	req.item = HTreeItem{ki.KeyHash, pos, meta.Ver, meta.ValueHash}
	tree.setReq(&req)
}

```

### setReq()

获取叶子节点同时更新isHashUpdated为false，更新叶子节点。

```go
func (tree *HTree) setReq(req *HTreeReq) {
	tree.Lock()
	defer tree.Unlock()

	tree.getLeafAndInvalidNodes(req.ki, &tree.ni)
	tree.setToLeaf(&tree.ni, req)
}
```

### getLeafAndInvalidNodes（）

```go
func (tree *HTree) getLeafAndInvalidNodes(ki *KeyInfo, ni *NodeInfo) {
	//计算叶子节点的level，等于树的总层数-1
	ni.level = len(tree.levels) - 1
	ni.offset = 0
	path := ki.KeyPath[tree.depth:]
	tree.levels[0][0].isHashUpdated = false
	for level := 1; level < len(tree.levels)-1; level += 1 {
		ni.offset = ni.offset*16 + path[level-1]
		tree.levels[level][ni.offset].isHashUpdated = false
	}
	//计算叶子节点的offset
	ni.offset = ni.offset*16 + path[ni.level-1]
	//通过层数+offset定位到node
	ni.node = &tree.levels[ni.level][ni.offset]
	ni.path = ki.KeyPath[:tree.depth+ni.level]

	return
}
```

### setToLeaf()

更新叶子节点

```go
func (tree *HTree) setToLeaf(ni *NodeInfo, req *HTreeReq) {
	node := ni.node
	//基于 ni.offset 在叶子中定位到数据，执行Set方法
	oldm, exist := tree.leafs[ni.offset].Set(req)
}

```




1、TreeDepth的作用？
TreeDepth在 config.go 中被InitTree()初始化

```
func (c *HStoreConfig) InitTree() error {
	// TreeDepth
	n := c.NumBucket
	c.TreeDepth = 0
	for n > 1 {
		c.TreeDepth += 1
		n /= 16
	}
	// TreeKeyHashLen & TreeKeyHashMask
	c.TreeKeyHashLen = KHASH_LENS[c.TreeDepth+c.TreeHeight-1]
	shift := 64 - uint32(c.TreeKeyHashLen)*8
	c.TreeKeyHashMask = (uint64(0xffffffffffffffff) << shift) >> shift

	return nil
}
```

NumBucket必须是16的倍数，同时它还决定了TreeDepth。TreeDepth和TreeHeight功能决定了HashMask的长度


2、KeyInfo中KeyPath的作用？ 
KeyPath看上去像是KeyHash之后的变种，通过如下代码初始化

```
ki.KeyPath = ParsePathUint64(ki.KeyHash, ki.KeyPathBuf[:16])

```


3、HTree.ni是如何初始化的？
ni 是一个临时变量，当HTree被调用过getLeaf()方法时会初始化。





## 参考

- [优雅的Bitcask/BeansDB](https://zhuanlan.zhihu.com/p/53682577)
- [htree：结构，冲突处理，内存和速度优化](https://github.com/douban/gobeansdb/wiki/htree%EF%BC%9A%E7%BB%93%E6%9E%84%EF%BC%8C%E5%86%B2%E7%AA%81%E5%A4%84%E7%90%86%EF%BC%8C%E5%86%85%E5%AD%98%E5%92%8C%E9%80%9F%E5%BA%A6%E4%BC%98%E5%8C%96)