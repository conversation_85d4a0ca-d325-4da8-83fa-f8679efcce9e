## 概述

BeansDB 是豆瓣内部实现的分布式存储系统，最开始的时候是 [Davies](https://github.com/davies) 用 C 来实现的。2015 年的时候，内部决定开始用 Go 来重新写 BeansDB，然后 xiufeng 与 zzl 就实现了现在的 GoBeansDB。

为什么豆瓣还需要自己重新实现一套 k/v 存储？ 

1. 首先是因为数据大，而且数据是非结构化数据，像豆瓣的日记，就是一批很长的字符串。
2. 其次是非常多的随机读。
3. 有的时候会有大量的写操作。
4. 不需要外键什么的。

## GoBeansDB 的架构设计

GoBeansDB 是基于 [Dynamo](http://www.allthingsdistributed.com/files/amazon-dynamo-sosp2007.pdf) 与 [Bitcask](http://basho.com/wp-content/uploads/2015/05/bitcask-intro.pdf) 两篇论文来做的实现，这里优先讨论基于 Bitcask 实现的存储引擎。Bitcask 有一个缺点在于所有的 key 值都必须放在内存里面，GoBeansDB 这这个基础之上做了一些优化，绕过了 Bitcask 这个痛点。

GobeansDB 的存储有有两个比较重要的组成部分，一个是索引(htree)，一个是数据文件(data)。索引与数据文件组成 Bucket。Bucket 的概念类似与关系行数据库里面的 table，在 GoBeansDB 的实现中就是给一个 Bucket 分配一个文件夹，这个文件夹下面放着相关的数据。每个 Bucket 下面一次只允许打开一个文件。打开的这个文件会一直保持打开的状态，一直等到追加到活跃文件超出阈值。文件超出阈值之后就关闭，然后新建一个新的继续添加。data 文件一旦关闭之后，文件就转换成为不活跃的数据文件。无法再往这个 data 文件上面追加数据。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/mysql/0.png" alt="0" style="zoom:50%;" />

状态为 active 的数据文件只做追加操作，这样连续的写入操作也不会明显增加磁盘的 IO 使用量。这种设计也极大的简化了数据的写入操作。

上面的图简单描述了 Bucket 内部文件的架构，每条数据里面包含TS（TimeStamp）,Flag，Ver（Version），ValueHash，RecSize（单条记录的主要内容的大小）,Value，crc（key，value，header 的 crc），ksz（Key Size）,vsz（Value Size）,pos（Position，这条记录在文件中的位置）,Header。

当插入新数据的时候，直接在文件尾部添加这种结构的数据。删除操作是对原有的数据做更新操作，并将 Ver 绝对值+1，转变为负数。

在文件写入完成之后，需要更新内存里面的数据结构，也就是前面提到的 HTree，HTree 是一个 Hash Tree，结构如下

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/mysql/1.png" alt="1" style="zoom:50%;" />

`levels` 表示真实的树状结构， `leafs` 是树的叶子，保存着真实的数据。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/mysql/2.png" alt="2" style="zoom:50%;" />

这种数据结构下读取一个值也非常简单，大多数情况下最多只需要一次 seek 。我们首先在 Htree 中通过 `levels` 找到 `key` 对应的 `leafs` ， 然后通过 `leafs` 里面的保存的 `Pos` 来拿到文件编号（chunkID）以及 offset，这样就可以通过文件编号（chunkID）和 offset 来拿到保存的数据。在很多情况下文件系统的缓存会让这个读操作比预期的要快。

到这里关于 GoBeansDB `wirte/delete/read` 相关的操作都已经基本完成。但是仅仅这样还不能完备。

## GC 操作

GoBeansDB 的模型非常简单，`write/delete` 操作都是在文件尾部追加新的数据，这样存在一个问题就是占用的磁盘空间比真实的数据要多。所以我们引入了 GC 机制来回收垃圾，释放内存与磁盘空间。

在 GoBeansDB 中，通过增量 GC 来减小 GC 的开销。xiufeng 通过分析 BeansDB 的日志，统计出一条新写入的数据，修改操作基本在写入之后的 7 天之内，所以我们保留 7 天之内的新数据不做 GC。然后在每天晚上，访问量较低的时候，分批做增量 GC。

GC 的过程是将 datafile 里面已经过时的数据清除掉，比如旧版本的value，已经标记为删除掉的key。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/mysql/3.png" alt="3" style="zoom:50%;" />

如 上图所示，GC 会读取状态为不活跃的数据文件，用其中存活的数据或者最新版本的数据生成一份新的数据文件，同时为这个新的数据文件创建一个 hint file。

## 参考

- http://sunisdown.me/gobeansdb-jia-gou-she-ji.html