## 概述

[Bitcask](https://github.com/basho/bitcask) 是一个日志型的键值对存储引擎。最初 Bitcask 是作为 Riak 这样一个分布式存储系统的核心存储引擎被人知晓，之后豆瓣的 beansdb 也使用 Bitask 的存储模型。



## 原理

### 日志型存储

与常见基于 B+ 树的存储引擎不同，日志型的存储引擎不支持随机写，而只支持 Append-Only 写入。由于将硬盘的随机写转化为顺序写，因此日志型的存储引擎能够提高写入的吞吐量，因为硬盘没有seek的过程，直接追加即可。

在 Bitcask 中，数据被以追加的方式写入硬盘的数据文件中，数据文件只增不减。硬盘不可能有无限容量，数据文件也就不可能无限制写入。当文件增加到一定大小时，就会产生一个新的文件，老的文件只读不写。因此，在任意一个时刻，只会有一个文件允许写入数据。在 Bitbask 中，称为活动文件（active file）。

![img](http://www.shangyan.site/img/2020-04-03/20200403195136.png)

文件中数据的格式也十分简单，依次由crc校验码、时间戳、key的大小、value的大小、key、value组成：

![img](http://www.shangyan.site/img/2020-04-03/20200403195417.png)

 其中时间戳用于判断数据是否过期。

数据删除也是通过另一次写入实现，删除时写入的value是一个特殊值，标记为删除，这个特殊值Bitcask称为“墓碑”。

数据文件的内容如下图所示。

![img](http://www.shangyan.site/img/2020-04-03/20200403195853.png)

数据可能被多次写入和删除，因此数据文件中会存在大量无用的数据记录。Bitcask有一个merge的过程，会定期将历史上的数据文件全部扫描一遍，生成一个新的数据文件。注意，merge的过程不包括active文件，因为它还在不断地写入。merge的过程，实际上就是对同一个key的多个value合并的过程，merge后只保留这个key最新的value；如果这个key被标记为删除，新的数据文件中也不再包含这个key。每次merge后，新的数据文件中就不再有冗余数据了。

### 索引

连续写入数据的方式提高了写操作的吞吐量，但对读操作并不友好，如果要查询某个key现在只能从所有文件中查找。因此，Bitcask设计了一个内存中的hash索引结构，用于加速读操作。

索引的结构如下：

![img](http://www.shangyan.site/img/2020-04-03/20200403200637.png)

索引中每个key有三个元素可用来定位value所在，分别是file_id、value_pos、value_sz，于是可以通过file_id找到value所在的文件、通过value_pos找到value在这个文件中的偏移量、从偏移量开始读value_sz大小的数据，就得到了value。

过程如下：

![img](http://www.shangyan.site/img/2020-04-03/20200403200938.png)

### Hint文件

索引是内存中的数据结构，Bitcask重启时需要在内存重建索引。如果数据量特别大，重建索引的过程将非常耗时。Bitcask使用了Hint文件（hint file）用来加速这一过程。Hint文件在merge完成后被创建，Hint文件的数据结构与数据文件类似，不同的是数据文件记录的是数据，而Hint文件记录的是数据的位置。

![img](http://www.shangyan.site/img/2020-04-03/20200407130243.png)

启动的时候就可以不读数据文件，而是读线索文件，按行重建即可，大大加快了索引的重建速度。

### 小结

Bitcask是一个设计十分精简的日志型键值对存储引擎。

- 添加：新添加的数据直接追加到活动文件中。随着活动文件越来越大，当达到一定大小时Bitcask会停止写当前的活动文件，新建一个新的文件作为活动文件，之前的文件只能读不能再写入。
- 删除：Bitcask不会直接删除记录，而是新增一条新的记录，将key标记为删除。然后更改索引中的哈希表。
- 修改：Bitcask不支持随机写入，因此修改时不是在原记录上直接修改value，而是新增一条记录，value为新值。然后同步修改索引中的哈希表。
- 查询：读取时，首先从索引的哈希表中定位到记录的位置，然后通过磁盘IO读取对应的记录。
- 合并：Bitcask只增不减的特性，必然会导致数据不断膨胀。Bitcask提供了merge的操作，剔除数据中无用的部分，包括已删除的数据、修改过留下的历史数据。merge只针对非活动文件，因为活动文件目前还在不断的被写入。merge后还会生成一个线索文件，用于重启时加速内存中索引的重建。

## 实现

### 关键类

- BitCask；对外API
- BitCaskOptions； 配置项：单个数据文件大小，key的失效时间
- BitCaskFile；读写文件；内部维护 FileChannel对象 
- BitCaskEntry；保存数据；file_id，ts，offset，total_sz
- BitCaskLock 文件锁
- IOUtils 文件读写工具类

### 入口

通过BitCask.open( )初始化，需要处理的逻辑：

- 创建BitCask数据目录
- 如果目录中存在已有数据需要恢复

```java
 public static BitCask open(File dirname, BitCaskOptions opts) throws Exception {
 }
```



### BitCask 写数据

```java
public class BitCask {
    private BitCaskFile writeFile = BitCaskFile.FRESH_FILE; //默认值

    public void put(ByteString key, ByteString value) throws IOException {

        switch (writeFile.check_write(key, value, max_file_size)) {
            case WRAP: {
                // 如果需要滚动文件，需要关闭已有文件
                writeFile.closeForWriting();
                BitCaskFile last_write_file = writeFile;
                BitCaskFile nwf = BitCaskFile.create(dirname);
                write_lock.write_activefile(nwf);

                writeFile = nwf;
                read_files.put(last_write_file.filename, last_write_file);
                break;
            }
            case FRESH:{
                //新的文件
                BitCaskLock wl = BitCaskLock.acquire(Type.WRITE, dirname);
                BitCaskFile nwf = BitCaskFile.create(dirname); 
                wl.write_activefile(nwf);

                this.write_lock = wl;
                this.writeFile = nwf;

                break;
            }
        }

        BitCaskEntry entry = writeFile.write(key, value);//写入数据
        keydir.put(key, entry);   
    }
}    
```

BitCask默认只会写入一个文件，写入超过一定大小需要进行文件滚动，所以需要先判断状态并初始化BitCaskFile。

然后委托给 BitCaskFile写入，最后放入BitCaskKeyDir中

#### BitCaskFile.create()初始化

```java
private static int tstamp() {
    return (int)(System.currentTimeMillis() / 1000L); //转换为秒为单位
}

public static BitCaskFile create(File dirname) throws IOException {
    return create(dirname, tstamp());
}

public static BitCaskFile create(File dirname, int tstamp) throws IOException {
        ensuredir(dirname);

        boolean created = false;

        File filename = null;
        while (!created) {
            filename = makeFileName(dirname, tstamp);
            created = filename.createNewFile();
            if (!created) {
                tstamp += 1;
            }
        }

        FileChannel wch = new FileOutputStream(filename, true).getChannel();//数据 Write Channle
        FileChannel wch_hint = new FileOutputStream(hintFileName(filename), true).getChannel();//Hint文件 Write Channle

        FileChannel rch = new RandomAccessFile(filename, "r").getChannel();//随机读Channle

        return new BitCaskFile(tstamp, filename, wch, wch_hint, rch);
    }
```

BitCaskFile创建了三个FileChannel

#### BitCaskFile构造函数

```java
private BitCaskFile(int file_id, File filename, FileChannel wch, FileChannel wch_hint, FileChannel rch)
        throws IOException {
        this.file_id = file_id;
        this.filename = filename;
        this.wch = wch;
        this.rch = rch;
        this.wch_hint = wch_hint;
        this.write_offset = new AtomicLong(rch.size());//文件已写的offset
    }
```

- file_id为时间戳
- filename为 /tmp/bitcask/1591705468.bitcask.data

#### BitCaskFile实际写入数据

```java
public class BitCaskFile {   
	public BitCaskEntry write(ByteString key, ByteString value) throws IOException {

        int tstamp = tstamp();// 时间戳
        int key_size = key.size();// key大小
        int value_size = value.size();// value大小

        ByteBuffer[] vec = fileEntry(key, value, tstamp, key_size, value_size);

        int entry_size = HEADER_SIZE + key_size + value_size;
        long entry_pos = write_offset.getAndAdd(entry_size);
        IOUtils.writeFully(wch, vec);//写入数据

        ByteBuffer[] hfe = hint_file_entry(key, tstamp, entry_pos, entry_size);
        IOUtils.writeFully(wch_hint, hfe);//写入hint

        return new BitCaskEntry(file_id, tstamp, entry_pos, entry_size);
    }
}    
```

BitCaskFile 写入过程就是依次写入数据和hint文件，然后把索引封装为BitCaskEntry返回

#### BitCaskKeyDir

落盘之后会把BitCaskEntry放入BitCaskKeyDir中，BitCaskKeyDir内部就是一个map，其中key为数据对应的key

```java
public class BitCaskKeyDir {
    Map<ByteString, BitCaskEntry> map = new HashMap<ByteString, BitCaskEntry>();

    public boolean put(ByteString key, BitCaskEntry ent) {
            Lock writeLock = rwl.writeLock();
            writeLock.lock();
            try {
                BitCaskEntry old = map.get(key);
                if (old == null) {
                    map.put(key, ent);
                    return true;
                } else if (ent.is_newer_than(old)) {
                    map.put(key, ent);
                    return true;
                } else {
                    return false;
                }
            } finally {
                writeLock.unlock();
            }

        }
}    
```

### BitCask 读数据

```java
 private ByteString get(ByteString key) throws IOException {
        BitCaskEntry entry = keydir.get(key);
        if (entry == null) {
            return null;
        }

        if (entry.tstamp < opts.expiry_time()) {
            return null;
        }

        BitCaskFile rf = getBitCaskFile(entry.file_id);
        if (rf == null) {
            return null;
        }

        ByteString[] kv = rf.read(entry.offset, entry.total_sz);

        if (kv[1].equals(TOMBSTONE)) {
            return null;
        } else {
            return kv[1];
        }
    }
```

读取的过程如下:

1. 通过key从keydir中获取BitCaskEntry；如果BitCaskEntry不存在直接结束。
2. 在BitCaskEntry得到file_id(实际是时间戳)，得到对应的数据文件BitCaskFile。
3. BitCaskFile读取数据

#### BitCaskFile读数据

```java
public class BitCaskFile {
    public ByteString[] read(long offset, int length) throws IOException {
		//先读取hreader
        byte[] header = new byte[HEADER_SIZE];
        ByteBuffer h = ByteBuffer.wrap(header);
        long read = IOUtils.read(rch, h, offset);
        if (read != HEADER_SIZE) {
            throw new IOException("cannot read header @ 0x" + Long.toHexString(offset));
        }
        int crc32 = h.getInt(0);
        /* int tstamp = h.getInt(); */
        int key_len = h.getChar(8);
        int val_len = h.getInt(10);

        int key_val_size = key_len + val_len;

        if (length != (HEADER_SIZE + key_val_size)) {
            throw new IOException("bad entry size");
        }

        byte[] kv = new byte[key_val_size];
        ByteBuffer key_val = ByteBuffer.wrap(kv);
		
        //读取数据
        long kv_pos = offset + HEADER_SIZE;
        read = IOUtils.read(rch, key_val, kv_pos);
        if (read != key_val_size) {
            throw new IOException("cannot read key+value @ 0x" + Long.toHexString(offset));
        }
        
        //校验
        CRC32 crc = new CRC32();
        crc.reset();
        crc.update(header, 4, HEADER_SIZE - 4);
        crc.update(kv);

        if (crc.getValue() != crc32) {
            throw new IOException("Mismatching CRC code");
        }

        ByteString[] result =
            new ByteString[] {ByteString.copyFrom(kv, 0, key_len), ByteString.copyFrom(kv, key_len, val_len)};

        return result;
    }
}    
```



## 参考

- [豆瓣 GoBeansDB 架构设计](http://sunisdown.me/gobeansdb-jia-gou-she-ji.html)
- Googole: **Riak's Bitcask - A Log-Structured Hash Table for Fast Key/Value Data**
- [TalkGO-116 理论结合实践详解 lsm 树存储引擎](https://www.youtube.com/watch?v=adamqSuHHck&t=35s)
- [go-caskdb](https://github.com/avinassh/go-caskdb)
- [从零开始写KV数据库：基于哈希索引](https://blog.xiaohansong.com/kvstore_hash_index.html)

