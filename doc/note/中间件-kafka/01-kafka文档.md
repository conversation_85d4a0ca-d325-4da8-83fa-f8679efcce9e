## 概述

Apache Kafka由著名职业社交公司LinkedIn开发，最初是被设计用来解决LinkedIn公司内部海量日志传输等问题。Kafka使用Scala语言编写，于2011年开源并进入Apache孵化器，2012年10月正式毕业，现在为Apache顶级项目。本文旨在使读者对Kafka有一个较为基本和全面的认识。

## 版本演进

[RELEASE_NOTES](https://archive.apache.org/dist/kafka/2.4.0/RELEASE_NOTES.html)

| 版本   | 发布时间   | 特性                                                         |
| ------ | ---------- | ------------------------------------------------------------ |
| 0.7.0  | 2012-01-04 | 基本的消息队列功能，连消息副本机制都没有，不建议使用         |
| 0.8.0  | 2013-12-03 | 引入了副本机制，引入了新版本Producer API                     |
| 0.9.0  | 2015-11-23 | 增加安全认证，新版本Consumer API，引入了新的组件Kafka Connect |
| 0.10.0 | 2016-05-22 | 引入了 Kafka Streams,Consumer API 保持稳定，Producer API 的性能提升 |
| 0.11.0 | 2017-06-28 | 支持Exactly-Once，实现了Producer端的消息幂等性，以及事务特性 |
| 1.0.0  | 2017-11-01 | 磁盘的故障转移，Kafka Streams方面的改进，以及Kafka Connect改进与功能完善 |
| 2.0.0  | 2018-07-30 | Kafka Streams、Connect方面的性能提升与功能完善，以及安全方面的增强等，2.1.0开始支持ZStandard的压缩方式 |
| 2.2    | 2019-03-22 | 必须显式设置消费者组id；kafka-topics.sh可以直接连接--bootstrap-server； |
| 2.3    | 2019-06-25 | kafka connec增量协作式重新平衡；                             |
| 2.4    | 2019-12-16 | Kafka Streams 的弹性扩展功能                                 |
| 2.5    | 4/15/2020  | Kafka Connect 的增量发布和 Kafka Streams 的性能优化          |
| 2.6    | 8/3/2020   | 交叉数据中心复制（CDCR）                                     |
| 2.7    | 12/21/2020 | Kafka 消息压缩的改进和优化                                   |
| 2.8    | 4/19/2021  | Kafka 控制器的改进                                           |
| 3.0    | 9/21/2021  |                                                              |

## 源码搭建

下载kakfa源码之后，查看README.md，里面有说明，操作如下：

```
<NAME_EMAIL>:apache/kafka.git
ce kafka
git co -t remotes/origin/2.2
./gradlew idea
```

输出：

```bash
BUILD SUCCESSFUL in 7m 15s
30 actionable tasks: 30 executed
```



1、修改build.gradle
找到 compile(libs.zookeeper) ，注释掉下面两行代码：

```
    compile(libs.zookeeper) {
      //exclude module: 'slf4j-log4j12'
      //exclude module: 'log4j'
      exclude module: 'netty'
    }
```

2、修改 log4j.properties配置文件
文件路径在/data/program/kafka/kafka_2.13-2.8.0/config/log4j.properties

```
log4j.rootLogger=INFO, stdout

log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=[%d] %p %m (%c)%n


# Change the line below to adjust ZK client logging
log4j.logger.org.apache.zookeeper=INFO

# Change the two lines below to adjust the general broker logging level (output to server.log and stdout)
log4j.logger.kafka=INFO
log4j.logger.org.apache.kafka=INFO

```

3、修改server.properties

```
```



4、启动参数

```
VM options设置： -Dlog4j.configuration=file://data/program/kafka/kafka_2.13-2.8.0/config/log4j.properties
Program arguments设置：/data/program/kafka/kafka_2.13-2.8.0/config/server.properties
```





## 架构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220507215415516.png" alt="image-20220507215415516" style="zoom:50%;" />

这张图是kafka典型的架构图，在这张图表示 TopicA 有三个分区(parttion0,parttion2,parttion2)，两个副本，保存在3 个Borker上，有4 个生产者，2个消费组在消费。

相关名词解释：

| 名词           | 解释                                                         |
| -------------- | ------------------------------------------------------------ |
| Producer       | 生产者，也就是发送消息的一方                                 |
| Consumer       | 消费者，也就是接受消息的一方                                 |
| Consumer Group | 同一消费组中的消费者不会重复消费消息，不同消费组中的消费者消息消息时互不影响，通过此实现 P2P 模式和广播模式 |
| Broker         | 处理所有来自客户端的请求(包括生产、消费、metadata)，并且集群内的数据复制，broker的集群管理依赖zookeepr来实现 |
| Topic          | Topic 代表了一类消息，不管是生产还是消费都是面向Topic进行的  |
| Partition      | opic 可以细分为多个partition，每个partition内部是有序的      |
| offset         | 消息存储时被分配一个唯一的offset，offset是一个逻辑意义上的偏移，用于区分每一条消息。 |

### Topic和Partition



<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220507222641121.png" alt="image-20220507222641121" style="zoom:50%;" />

topic 和 partition 是kafka中最重要的概念，一个topic可以认为一个一类消息，每个topic将被分成多个partition，每个partition在存储层面是append log文件。任何发布到此partition的消息都会被追加到log文件的尾部，每条消息在文件中的位置称为offset(偏移量)，offset为一个long型的数字，它唯一标记一条消息。每条消息都被append到partition中，是顺序写磁盘，因此效率非常高。 

每一条消息被发送到broker中，会根据partition规则选择被存储到哪一个partition。如果partition规则设置的合理，所有消息可以均匀分布到不同的partition里，这样就实现了水平扩展。



- 从面试角度一文学完 Kafka:  https://mp.weixin.qq.com/s/Ndwrlst-aVdUpcNz-inM3Q
- 震惊了！原来这才是 Kafka！: https://mp.weixin.qq.com/s/DKy5oc2H8k83sFlhUNjVUw

### ACK/ISR/HW

[Kafka数据可靠性保证三板斧-ACK/ISR/HW](https://mp.weixin.qq.com/s/x3i5YJ8_vg6Z7qOZWOlx8g)

### 存储机制

和Lucene类似，Kafka 每个分区也使用基于 segment 的方式来存储数据，基于segment可以让kafak更方便的清理过期数据，比如某个分区文件如下:

```
-rw-rw-r-- 1 <USER> <GROUP>        8 Oct 31 09:22 00000000000000000023.index
-rw-rw-r-- 1 <USER> <GROUP>     5005 Oct 30 15:57 00000000000000000023.log
-rw-rw-r-- 1 <USER> <GROUP>       10 Oct 23 17:20 00000000000000000023.snapshot
-rw-rw-r-- 1 <USER> <GROUP>       12 Oct 31 09:22 00000000000000000023.timeindex
-rw-rw-r-- 1 <USER> <GROUP> 10485760 Oct 31 09:22 00000000000000000040.index
-rw-rw-r-- 1 <USER> <GROUP>     2357 Oct 31 16:42 00000000000000000040.log
-rw-rw-r-- 1 <USER> <GROUP>       10 Oct 31 09:22 00000000000000000040.snapshot
-rw-rw-r-- 1 <USER> <GROUP> 10485756 Oct 31 09:22 00000000000000000040.timeindex
```

这表示这个分区有两个段，每个段的文件名表示offset的起始位置，23.log表示23到39， 40.log表示40到最新的数据。

这里有一个细节，对于kafka broker **是不会跨段读取**(Cross-Segemt Reading)的，对于消费者如果从23开始消费，最多只会返回到39的数据。



## 使用

- [[Kafka Consumer多线程消费](https://www.cnblogs.com/huxi2b/p/13668061.html)](https://www.cnblogs.com/huxi2b/p/13668061.html)

pom.xml 依赖:

```xml
<dependency>
  <groupId>org.apache.kafka</groupId>
  <artifactId>kafka_2.13</artifactId>
  <version>2.8.0</version>
</dependency>
```



### 创建topic

管理 topic 主要通过 AdminClient 这个类操作，参考[adminapi](https://kafka.apache.org/documentation/#adminapi)

```java
	void createTopic(String topic) throws Exception {
        AdminClient = AdminClient.create(KafkaTest.getProperties());
		CreateTopicsResult topicsResult = client.createTopics(Arrays.asList(new NewTopic(topic, 1, (short) 1)));
		topicsResult.all().get();
	}
```

### 发送消息

发送消息通过 KafkaProducer 对象，参考[producerapi](https://kafka.apache.org/documentation/#producerapi)，下面是基本用法：

```java
@Test
public void testSend() throws Throwable {
    
  send("bairen.test", null, UUID.randomUUID().toString(), "{name:test,op:1}");
    
}

public void send(String topic, Integer partition, String key, String data)throws InterruptedException, ExecutionException {
  	//①
  	KafkaProducer<String, byte[]> producer = new KafkaProducer<String, byte[]>(KafkaTestConfig.getProduceProperties());  
		//②	
  	ProducerRecord<String, byte[]> record = new ProducerRecord<String, byte[]>(topic, partition, key,data.getBytes());
		//③
  	producer.send(record);	
  
}
```

1. 首先是初始化KafkaProducer，构建KafkaProducer需要一个Properties对象，后面会说明一些必要的参数。
2. 接着创建要发送的消息ProducerRecord，包括topic，partition，key，data，partition可以设置为null，如果partition为null会根据key进行路由。
3. 调用 producer.send() 执行发送，该方法返回 Future 对象。

#### 发送方式

kafka 实际上有三种发送消息的方式：

- fire-and-forget(发射后不管)。也就是我们刚才这种只调用send()方法的方式，这种存在消息丢失的风险应该尽量避免。

- 同步发送。只需要在send()之后再调用get()即可。

- 异步发送。在send()方法中指定一个callback：

  ```java
  topicProduce.send(record, (metadata, exception) -> {
    if (exception == null) {
      log.info("send ok,offset:" + metadata.offset() + ",partition:" + metadata.partition());
    } else {
      log.error("send faild,topic:" + topic, exception);
    }
  });
  ```

#### 参数

公共参数，不管是生产还是消费都需要的参数：

| 参数名            | 含义       |
| -------------- | ----------- |
| bootstrap.servers | broker地址 |
| client.id         | 客户端id   |
|                   |            |

KafkaProducer特有的参数：

| 参数名           | 配置                      | 含义                                                         |
| ---------------- | ------------------------- | ------------------------------------------------------------ |
| key.serializer   | StringSerializer.class    | key序列化方式                                                |
| value.serializer | ByteArraySerializer.class | value序列化方式                                              |
| max.block.ms     | 10000                     |                                                              |
| acks             | 1                         | ack策略，1表示等待broker集群全部同步完才返回，后面再可靠性保证中会再讨论 |



### 消费消息

消费消息通过KafkaConsumer完成，但是注意一次消费不一定能消费到消息，日常一般通过 while(true)方式不断轮训消费。

样例代码：

```java
	public void fetch(String topic) throws InterruptedException {
		consumer.subscribe(Collections.singletonList(KafkaTest.topic));

		while (true) {
			Thread.sleep(1000);
			ConsumerRecords<String, byte[]> resords = consumer.poll(Duration.ofMillis(1));
			log.info("poll resords size:" + resords.count());
			Iterator<ConsumerRecord<String, byte[]>> iterator = resords.records(topic).iterator();
			while (iterator.hasNext()) {
				//maxPollRecords
				ConsumerRecord<String, byte[]> record = iterator.next();
				System.out.println(record.topic() + "\t content:" + new String(record.value()) + "\t partition:" + record.partition()+"\t offset:"+record.offset());
			}
			
		}
	}
```

消费有三种方式：subscribe，assign，seek。 



## 可靠性保证

- [Kafka数据可靠性保证三板斧-ACK/ISR/HW](https://mp.weixin.qq.com/s/x3i5YJ8_vg6Z7qOZWOlx8g)

- [如何理解Kafka的消息可靠性策略？](https://mp.weixin.qq.com/s/EY6-rA5DJr28-dyTh5BP8w)

- [干货 | kafka数据可靠性深度解读](https://mp.weixin.qq.com/s/vWoxovJOkA9PSMtXwidUXw)

- [无消息丢失配置怎么实现](https://learn.lianglianglee.com/%E4%B8%93%E6%A0%8F/Kafka%E6%A0%B8%E5%BF%83%E6%8A%80%E6%9C%AF%E4%B8%8E%E5%AE%9E%E6%88%98/11%20%20%E6%97%A0%E6%B6%88%E6%81%AF%E4%B8%A2%E5%A4%B1%E9%85%8D%E7%BD%AE%E6%80%8E%E4%B9%88%E5%AE%9E%E7%8E%B0%EF%BC%9F.md)

  



## 高性能

### **1. 顺序写磁盘**

Kafka 采用了顺序写磁盘，而由于顺序写磁盘相对随机写，减少了寻地址的耗费时间。（在 Kafka 的每一个分区里面消息是有序的）

### **2. Page Cache**

Kafka 在 OS 系统方面使用了 Page Cache 而不是我们平常所用的 Buffer。Page Cache 其实不陌生，也不是什么新鲜事物。



![图片](https://mmbiz.qpic.cn/mmbiz_png/VY8SELNGe94vk8quibEpnsrTgdQsziaw5uic1hUOfoG01oiaoXPTDCibjZd9K7p0ZTcIvOYoZPEPwyy1Cct3IglMI7A/640?wx_fmt=png&wxfrom=5&wx_lazy=1&wx_co=1)



我们在 linux 上查看内存的时候，经常可以看到 buff/cache，两者都是用来加速 IO 读写用的，而 cache 是作用于读，也就是说，磁盘的内容可以读到 cache 里面，这样应用程序读磁盘就非常快；而 buff 是作用于写，我们开发写磁盘都是，一般如果写入一个 buff 里面再 flush 就非常快。而 kafka 正是把这两者发挥到了极致：Kafka 虽然是 scala 写的，但是依旧在 Java 的虚拟机上运行，尽管如此，kafka 它还是尽量避开了 JVM 的限制，它利用了 Page cache 来存储，这样躲开了数据在 JVM 因为 GC 而发生的 STW。另一方面也是 Page Cache 使得它实现了零拷贝，具体下面会讲。



### **3. 零拷贝**

无论是优秀的 Netty 还是其他优秀的 Java 框架，基本都在零拷贝减少了 CPU 的上下文切换和磁盘的 IO。当然 Kafka 也不例外。零拷贝的概念具体这里不作太详细的复述。



### **4. 分区分段**

我们上面也介绍过，kafka 采取了分区的模式，而每一个分区又对应到一个物理分段，查找的时候可以根据二分查找快速定位。这样不仅提供了数据读的查询效率，也提供了并行操作的方式。



### **5. 数据压缩**

Kafka 对数据提供了：Gzip 和 Snappy 压缩协议等压缩协议，对消息结构体进行了压缩，一方面减少了带宽，也减少了数据传输的消耗。





## 面试相关

### kafka高吞吐量的原因

一、顺序读写磁盘，充分利用了操作系统的预读机制。
二、linux中使用sendfile命令，减少一次数据拷贝，如下。
①把数据从硬盘读取到内核中的页缓存。
②把数据从内核中读取到用户空间。(sendfile命令将跳过此步骤)
③把用户空间中的数据写到socket缓冲区中。
④操作系统将数据从socket缓冲区中复制到网卡缓冲区，以便将数据经网络发出
三、生产者客户端缓存消息批量发送，消费者批量从broker获取消息，减少网络io次数，充分利用磁盘顺序读写的性能。
四、通常情况下kafka的瓶颈不是cpu或者磁盘，而是网络带宽，所以生产者可以对数据进行压缩。

### kafka在高并发的情况下,如何避免消息丢失和消息重复?

消息丢失解决方案:
首先对kafka进行限速， 其次启用重试机制，重试间隔时间设置长一些，最后Kafka设置acks=all，即需要相应的所有处于ISR的分区都确认收到该消息后，才算发送成功

消息重复解决方案:
消息可以使用唯一id标识
生产者（ack=all 代表至少成功发送一次)
消费者 （offset手动提交，业务逻辑成功处理后，提交offset）
落表（主键或者唯一索引的方式，避免重复数据）
业务逻辑处理（选择唯一主键存储到Redis或者mongdb中，先查询是否存在，若存在则不处理；若不存在，先插入Redis或Mongdb,再进行业务逻辑处理）

### kafka怎么保证数据消费一次且仅消费一次

幂等producer：保证发送单个分区的消息只会发送一次，不会出现重复消息
事务(transaction)：保证原子性地写入到多个分区，即写入到多个分区的消息要么全部成功，要么全部回滚流处理EOS：流处理本质上可看成是“读取-处理-写入”的管道。此EOS保证整个过程的操作是原子性。注意，这只适用于Kafka Streams

### kafka保证数据一致性和可靠性

数据一致性保证

一致性定义：若某条消息对client可见，那么即使Leader挂了，在新Leader上数据依然可以被读到
HW-HighWaterMark: client可以从Leader读到的最大msg offset，即对外可见的最大offset， HW=max(replica.offset)
对于Leader新收到的msg，client不能立刻消费，Leader会等待该消息被所有ISR中的replica同步后，更新HW，此时该消息才能被client消费，这样就保证了如果Leader fail，该消息仍然可以从新选举的Leader中获取。
对于来自内部Broker的读取请求，没有HW的限制。同时，Follower也会维护一份自己的HW，Folloer.HW = min(Leader.HW, Follower.offset)

数据可靠性保证

当Producer向Leader发送数据时，可以通过acks参数设置数据可靠性的级别
0: 不论写入是否成功，server不需要给Producer发送Response，如果发生异常，server会终止连接，触发Producer更新meta数据；
1: Leader写入成功后即发送Response，此种情况如果Leader fail，会丢失数据
-1: 等待所有ISR接收到消息后再给Producer发送Response，这是最强保证



## 参考

分享

- [rfyiamcool的分享，还不错](https://github.com/rfyiamcool/share_ppt/blob/master/kafka.pdf) (2025-05-19)

腾讯云

- [总结 Kafka 背后的优秀设计](https://mp.weixin.qq.com/s/dfOP2MeBOqFqg_BdcJCYug)
- [一文入门 Kafka](https://mp.weixin.qq.com/s/vzvmOXGcsX7rwY4J_--onw)
- [如何理解Kafka的消息可靠性策略？](https://mp.weixin.qq.com/s/EY6-rA5DJr28-dyTh5BP8w)

源码分析

- [一文读懂kafka消息拉取机制｜线程拉取模型](https://mp.weixin.qq.com/s/jeDFs_TE8NVccTD_6F8RlQ)
- [图解Kafka线程模型及其设计缺陷](https://mp.weixin.qq.com/s/J6fXFn2DpHj3QfqDwlhPDQ)

原理

- [Kafka中副本机制的设计和原理](https://mp.weixin.qq.com/s/yIPIABpAzaHJvGoJ6pv0kg)
- [10分钟带你逆袭Kafka](https://mp.weixin.qq.com/s/9bYe095WzyRLZ3_B5PveZQ)
- [Kafka日志存储详解](https://my.oschina.net/zhangxufeng/blog/3114166)

业界实践

- [滴滴开源-DDMQ](https://github.com/didi/DDMQ)
- [快手基于 RocketMQ 的在线消息系统建设实践](https://www.infoq.cn/article/4uhJGtqKy5BZJ1EqUPT8)
- [Uber基于kafka proxy 实现 Async Queuing](https://www.uber.com/en-HK/blog/kafka-async-queuing-with-consumer-proxy/)

运维

- [Kafka 设计解析（三）：Kafka High Availability](https://www.infoq.cn/article/kafka-analysis-part-3)

公众号

- [搞透Kafka的存储架构，看这篇就够了](https://mp.weixin.qq.com/s?__biz=Mzg3MTcxMDgxNA==&mid=2247488854&idx=1&sn=32ff746feb1865157192df8d0eee45d0)

### 消费者如何提交offset保证不丢数据也不重复？

#todo202212

- [Understanding Offset Commits](https://www.logicbig.com/tutorials/misc/kafka/committing-offsets.html)
- [ConsumerRebalanceListener Example](https://www.logicbig.com/tutorials/misc/kafka/consumer-rebalance-listener-example.html)
- [Kafka - When to commit?](https://cn.quarkus.io/blog/kafka-commit-strategies/)
- [kafka如何手动异步提交offset](https://cloud.tencent.com/developer/article/1704689)
- kafka权威指南

