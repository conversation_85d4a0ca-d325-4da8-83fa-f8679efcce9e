## 概述

kafka broker存储机制，LogSegment：

-  **数据文件**.log，
- **偏移量索引文件**.index
- **时间戳索引文件.timeindex**



<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230904113337080.png" alt="image-20230904113337080" style="zoom:50%;" />

重要概念：

- **topic**：消息主题。Kafka 按 topic 对消息进行分类，我们在收发消息时只需指定 topic。
- **partition**：分区。为了提升系统的吞吐，一个 topic 下通常有多个 partition，partition 分布在不同的 Broker 上，用于存储 topic 的消息，这使 Kafka 可以在多台机器上处理、存储消息，给 kafka 提供给了并行的消息处理能力和横向扩容能力。另外，为了提升系统的可靠性，partition 通常会分组，且每组有一个主 partition、多个副本 partition，且分布在不同的 broker 上，从而起到**容灾**的作用。
- **segment**：分段。宏观上看，一个 partition 对应一个日志（Log）。由于生产者生产的消息会不断**追加到 log 文件末尾**，为防止 log 文件过大导致数据检索效率低下，**Kafka 采取了分段和索引机制，将每个 partition 分为多个 segment，同时也便于消息的维护和清理**。每个 segment 包含一个.log 日志文件、两个索引(.index、timeindex)文件以及其他可能的文件。每个 Segment 的数据文件以该段中最小的 offset 为文件名，当查找 offset 的 Message 的时候，通过二分查找快找到 Message 所处于的 Segment 中。
- **offset**：消息在日志中的位置，消息在被追加到分区日志文件的时候都会分配一个特定的偏移量。offset 是消息在分区中的唯一标识，是一个单调递增且不变的值。Kafka 通过它来保证消息在分区内的顺序性，不过 offset 并不跨越分区，也就是说，**Kafka 保证的是分区有序而不是主题有序**。



## Partition 物理文件结构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230904113843802.png" alt="image-20230904113843802" style="zoom:50%;" />



### .log(数据文件)

- 数据文件是以 `.log` 为文件后缀名的消息集文件(FileMessageSet)，用于保存消息实际数据
- 命名规则为：由数据文件的第一条消息偏移量，也称之为基准偏移量(`BaseOffset`)，左补0构成20位数字字符组成
- 每个数据文件的基准偏移量就是上一个数据文件的`LEO+1`（第一个数据文件为0）

### .index(偏移量索引文件)

- 文件名与数据文件相同，但是以`.index`为后缀名。它的目的是为了快速根据偏移量定位到消息所在的位置。
- 首先Kafka将每个log segment以`BaseOffset`为key保存到一个 `ConcurrentSkipListMap`跳跃表中，这样在查找指定偏移量的消息时，用二分查找法就能快速定位到消息所在的数据文件和索引文件
- 然后在索引文件中通过二分查找，查找值小于等于指定偏移量的最大偏移量，最后从查找出的最大偏移量处开始顺序扫描数据文件，直到在数据文件中查询到偏移量与指定偏移量相等的消息
- 需要注意的是并不是每条消息都对应有索引，而是采用了稀疏存储的方式，每隔一定字节的数据建立一条索引，我们可以通过`index.interval.bytes`设置索引跨度。

### .timeindex(时间戳索引文件)

- Kafka从********版本开始引入了一个基于时间戳的索引文件，文件名与数据文件相同，但是以`.timeindex`作为后缀。它的作用则是为了解决根据时间戳快速定位消息所在位置。
- Kafka API提供了一个 offsetsForTimes （Map<TopicPartition, Long> timestampsToSearch）
- 方法，该方法会返回时间戳大于等于待查询时间的第一条消息对应的偏移量和时间戳。这个功能其实挺好用的，假设我们希望从某个时间段开始消费，就可以用`offsetsForTimes()`方法定位到离这个时间最近的第一条消息的偏移量，然后调用 `seek(TopicPartition, long offset)`方法将消费者偏移量移动过去，然后调用`poll()`方法长轮询拉取消息。



从上述结构中，我们能看到segment **00000000000000000000.log**  保存了offset从0 到 1006的数据。下一个 segment **00000000000000001007.log** 记录了offset 1007开始的数据，并且它是**active segment**

## 未来

 [KIP-405: Kafka Tiered Storage](https://cwiki.apache.org/confluence/display/KAFKA/KIP-405%3A+Kafka+Tiered+Storage)

## 参考

- [美团-Kafka文件存储机制那些事](https://tech.meituan.com/2015/01/13/kafka-fs-design-theory.html)
- [Kafka 的消息存储结构：索引文件与数据文件](https://shuyi.tech/archives/kafka-message-storage)
- [understanding-kafkas-internal-storage-and-log-retention](https://www.conduktor.io/blog/understanding-kafkas-internal-storage-and-log-retention/)
- [Kafka Tiered Storage](https://cwiki.apache.org/confluence/display/KAFKA/KIP-405%3A+Kafka+Tiered+Storage)

