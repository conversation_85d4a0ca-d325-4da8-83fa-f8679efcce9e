### [MemQ: An efficient, scalable cloud native PubSub system](https://medium.com/pinterest-engineering/memq-an-efficient-scalable-cloud-native-pubsub-system-4402695dd4e7)

- Not every dataset needs [sub-second](亚秒) latency service, latency and [cost](成本) should be inversely proportional



### [git commits are snapshots not diff](https://github.blog/2020-12-17-commits-are-snapshots-not-diffs/)

- A **commit** is a snapshot [in time](即时)