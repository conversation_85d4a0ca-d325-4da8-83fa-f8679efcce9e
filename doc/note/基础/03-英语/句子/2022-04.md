### 1、[Introducing Domain-Oriented Microservice Architecture](https://eng.uber.com/microservice-architecture/)

- The layer that the domain belongs to [establishes](确定) what dependencies the microservices within that domain are allowed to take on. We call this layer design.
- Layer design describes a mechanism for thinking about failure blast radius and product specificity across service dependencies at Uber.
- **It’s worth noting** that functionality often moves “down” this chart from specific to more general.
- One can imagine a simple feature that eventually becomes more and more of a platform as requirements evolve.
- As you can see, each subsequent layer represents an [increasingly specific](more and more specific) grouping of functionality.
- except that we [tend to]() think of gateways exclusively as a **single entry-point** into a collection of underlying services
- The success of a gateway relies on the success of the API design.
- Logic extensions provide a mechanism for extending the [underlying](底层，注意这里修饰的是逻辑而不是服务) logic of a service.
- Data extensions provide a mechanism for attaching [arbitrary](任意的) data to an interface to avoid [bloat](膨胀) in core platform data models
- [Outside of](除了) logic and data extensions, many teams at Uber have introduced their own extension patterns that are appropriate for their domain
- For example, an early platform consumer of our extensions architecture was able to **drop the time** to prioritize and integrate a new feature from three days to three hours by adopting an extension architecture with reduced time for code review, planning, and learning curve for consumers.
- DOMA was [the result of a consensus effort]() across product and platform teams at Uber.
-  Platform support [costs often dropped](成本下降) an order of magnitude.
-  At Uber, we calculated that the half-life of a microservice was 1.5 years, which means that every 1.5 years 50% of our microservices churn.
-  Platforms designed using DOMA have proven to be much more extensible and easier to maintain.
-  The guiding principle here is that in our experience a mature and thoughtful microservice architecture stems from quiet nudges in the right direction at the right time.  [stem from](源于)
-  In small organizations, the operational benefit likely does not [offset the increase]() in architectural complexity

  

### 2、[Microservices Observability Design Patterns](https://learncsdesign.medium.com/microservices-observability-design-patterns-bdfa5807f81e)

- Additionally, an observable system provides [ample](充足) context about its inner workings, [enabling](想想) the discovery of deeper, systemic issues.

### 3、[What Is ClickHouse?](https://clickhouse.com/docs/en/intro)

- There is no system that is [equally](?) well-suited to [significantly](?) different scenarios.



### 4、日常

- [the internal representation](内部表示) of the transformation that a webSocket extension performs on a message.
- ML algorithms [can be only](只能) as good as the data that we provide to it 



## 看过

- https://developers.redhat.com/blog/2017/03/14/java-inside-docker
