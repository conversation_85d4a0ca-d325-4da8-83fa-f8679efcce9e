>  原文：https://www.techtarget.com/searchnetworking/definition/TCP-IP

## What is TCP/IP?

TCP/IP 代表传输控制协议/互联网协议，是一套用于互连互联网上的网络设备的通信协议。TCP/IP 也用作专用计算机网络（内联网）中的通信协议。

整个 IP 套件——一组规则和程序——通常被称为 TCP/IP。TCP 和 IP 是两个主要协议，但套件中还包括其他协议。TCP/IP 协议套件充当 Internet 应用程序与路由和交换结构之间的抽象层。

TCP/IP 通过提供端到端通信来指定数据如何在 Internet 上进行交换，该通信确定应如何将数据分解为数据包、寻址、传输、路由和在目的地接收。TCP/IP 几乎不需要中央管理，旨在使网络可靠，能够从网络上任何设备的故障中自动恢复。

IP 套件中的两个主要协议服务于特定功能。TCP 定义了应用程序如何创建通信channels通过网络。它还管理如何将消息组装成更小的packets，然后再通过 Internet 传输并在目标地址以正确的顺序重新组装。

IP 定义了如何寻址和路由每个数据包，以确保它到达正确的目的地。网络上的每台网关都会检查此 IP 地址以确定将消息转发到何处。

子网掩码告诉计算机或其他网络设备，IP 地址的哪一部分用于表示网络，哪一部分用于表示网络上的主机(hosts)或其他计算机。

Network address translation  (NAT) 是 IP 地址的虚拟化。NAT 有助于提高安全性并减少组织所需的 IP 地址数量。

常见的 TCP/IP 协议包括：

- **Hypertext Transfer Protocol (HTTP)** 处理 Web 服务器和 Web 浏览器之间的通信
- HTTPS 处理 Web 服务器和 Web 浏览器之间的安全通信。
- **File Transfer Protocol(FTP)** 处理计算机之间的文件传输

## How does TCP/IP work?

TCP/IP 使用C-S通信模型，其中用户机器（客户端）由网络中的另一台计算机（服务器）提供服务，例如发送网页。

总的来说，TCP/IP 协议套件被归类为无状态，这意味着每个客户端请求都被认为是新的，因为它与以前的请求无关。

然而，传输层本身是有状态的。它传输单个消息，并且它的连接保持在原位，直到消息中的所有数据包都已被接收并在目的地重新组合。

TCP/IP 模型与在它之后设计的七层 Open Systems Interconnection (OSI) 网络模型略有不同。OSI 参考模型定义了应用程序如何通过网络进行通信。

## Why is TCP/IP important?

TCP/IP 是非专有的，因此不受任何单一公司的控制。它与所有操作系统 (OS) 兼容，因此可以与任何其他系统进行通信。IP 套件还与所有类型的计算机硬件和网络兼容。

TCP/IP 具有高度可扩展性，并且作为一种可路由协议，可以确定通过网络的最有效路径。它广泛用于当前的互联网架构中

## The 4 layers of the TCP/IP model

TCP/IP 功能分为四层，每一层都包括特定的协议：

- 应用层为应用提供标准化的数据交换。其协议包括 HTTP、FTP、邮局协议 3、简单邮件传输协议和简单网络管理协议。在应用层，payload是实际的应用数据。
- 传输层负责维护端到端的跨网络通信。TCP 处理主机之间的通信并提供流量控制、多路复用和可靠性。传输协议包括 TCP 和UDP，UDP用于某些特殊目的替代 TCP。
- 网络层，也称为***internet layer***，处理数据包并连接独立网络以跨网络边界传输数据包。网络层协议是 IP 和 Internet 控制消息协议，用于错误报告。
- 物理层，也称为网络接口层或数据链路层，由仅在链路上运行的协议组成，链路是互连网络中节点或主机的网络组件。这个最低层的协议包括用于局域网的以太网和地址解析协议([Address Resolution Protocol](https://www.techtarget.com/searchnetworking/definition/Address-Resolution-Protocol-ARP))

## Uses of TCP/IP

TCP/IP 可用于通过网络提供远程登录，以进行交互式文件传输、传送电子邮件、通过网络传送网页以及远程访问服务器主机的文件系统。

最广泛地说，它用于表示信息在通过网络从具体物理层传输到抽象应用层时如何变化形式。它详细说明了信息通过时每一层的基本协议或通信方法。

### Pros and cons of TCP/IP

使用 TCP/IP 模型的优点包括：

- 帮助在不同类型的计算机之间建立连接；
- 独立于操作系统工作；
- 支持多种路由协议；
- 使用高度可扩展的客户端-服务器架构；
- 可以独立操作；
- 支持多种路由协议；
- 轻量，不会对网络或计算机造成不必要的压力

TCP/IP 的缺点包括：

- 设置和管理复杂；
- 传输层不保证数据包的传递；
- TCP/IP中的协议不易替换；
- 没有明确区分services, interfaces 和 protocols 的概念，因此不适合描述新网络中的新技术；
- 尤其容易受到[synchronization attack](https://www.techtarget.com/searchsecurity/definition/SYN-flooding)，这是一种拒绝服务攻击，其中不法分子使用 TCP/IP。



## How are TCP/IP and IP different?

TCP/IP 和 IP 之间有许多不同之处。

例如，IP 是一种低级 Internet 协议，可促进 Internet 上的数据通信。其目的是传送由 header 组成的数据包，该header含路由信息，例如数据的源和目的地，以及数据payload。

IP受到它可以发送的数据量的限制。包含header和data的单个 IP 数据包的最大长度在 20 到 24 个字节之间。这意味着较长的数据串必须分解成多个数据包，这些数据包必须独立发送，然后在发送后重新组织成正确的顺序。

由于 IP 严格来说是一种数据发送/接收协议，因此没有内置检查来验证发送的数据包是否被实际接收。

与 IP 相比，TCP/IP 是一种更高级别的智能通信协议，可以做更多的事情。TCP/IP 仍然使用 IP 作为传输数据包的手段，但它也连接计算机、应用程序、网页和 Web 服务器。TCP 全面了解这些资源(assets)运行所需的全部数据流，并确保首次发送所需的全部数据量。TCP 还运行检查以确保数据已交付。

在它的工作中，TCP 还可以控制数据的大小和流量。它确保网络没有任何可能阻止数据接收的拥塞。

一个示例是想要通过 Internet 发送大量数据的应用程序。 如果应用程序仅使用 IP，则必须将数据分成多个 IP 数据包。 这将需要多个请求来发送和接收数据，因为每个数据包都会发出 IP 请求。

而使用 TCP，只需一个请求即可发送整个数据流， TCP 处理其余部分。与 IP 不同，TCP 可以检测 IP 中出现的问题并请求重新传输任何丢失的数据包。TCP 还可以重新组织数据包，使它们以合适的顺序传输——它可以最大限度地减少网络拥塞。TCP/IP 使互联网上的数据传输更容易。



## 生词

- stands for
- fabric
- little 表否定，几乎没有
- collectively
- remains in place
- nonproprietary
- **Most broadly**, it is used to represent how information **changes form** as it travels over a network from the concrete physical layer to the abstract application layer
- several
- numerous
- doesn't place unnecessary strain 
- complicated
- facilitates
- holistically
- asset
- retransmission