> 原文：https://redis.com/blog/redis-architecture-13-years-later/

 

## 概述
Redis 作为一项底层技术，我们偶尔会看到人们考虑其他架构，几年前有KeyDB，最近有一个新项目Dragonfly，声称兼容Redis且是最快的内存数据库。 我们相信这些项目带来了很多有意思的技术和想法值得讨论和争论，在Redis这里，我们喜欢这样的挑战，因为它要求我们重新审视Redis最初的架构原则。

虽然我们一直在寻找机会来创新和提升 Redis 的性能和功能, 我们想分享我们的一些观点和反思，为什么Redis架构在同类内存、实时数据库(cache,database)产品中保持领先

因此，在接下来的部分中，我们将重点介绍我们对速度和架构差异的看法，因为它与正在进行的比较有关。在文章最后，我们也提供了关于基准测试的详情以及和Dragonfly 项目的对比。


## 速度

Dragonfly 基准测试比较了一个单机单进程Redis实例(只能利用单核)和多线程的Dragonfly实例(可以利用多核)。不幸的是，这样的比较不能反映Redis是如何在实际环境中运行的。

 所以我们做了我们认为公平的较量，通过 40 个shard的Redis Cluster和Dragonfly对比，使用了一组最大的性能测试实例AWS c6gn.16xlarge，这也是Dragonfly team 在他们的基准测试中使用的。

在我们的测试中，我们看到Redis在吞吐量上比Dragonfly高18% – 40%，虽然只利用了64个核中的40个。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220816100205094.png" alt="image-20220816100205094" style="zoom:50%;" />

## 不同的架构

### 背景

我们相信，这些多线程项目的创建者做出的许多架构决策都受到了他们在之前工作中所经历的痛点的影响

### 原则

#### 1个VM运行多个Redis实例

1个VM运行多个Redis实例：

1. 使用了完全无共享架构，具有垂直和水平的线性伸缩，和多线程架构只提供垂直伸缩相比更具灵活。
2. 提供了复制的速度，因为复制会在多个进程中并行完成。
3. 从故障的 VM 中快速恢复，新的Redis实例中的数据可以多个外部 Redis 实例同时加载。

#### 每个Redis实例限制合理的大小

我们不允许单个Redis进程超过25GB，这使我们能够：

- 在 fork Redis 进行复制、快照和AOF 重写时，享受写时复制的好处，而无需支付大内存开销的代价。如果您不这样做，您（或您的用户）将付出高昂的代价，如此处所示。
- 为了更容易管理我们的集群，更快速的迁移分片，reshard，scale，rebalance



## 记录

### 生词

-  [looking for opportunities](寻找机会)
-  [reflection](反思)
-  [in class](同类 )
-  [highlight](重点介绍) 
-  [fair](公平)
-  simultaneously

### 难句

```
We believe a lot of the architectural decisions made by the creators of these multithreaded projects were influenced by the pain points they experienced in their previous work.
```

