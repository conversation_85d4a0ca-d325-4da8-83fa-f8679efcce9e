>  原文：https://dzone.com/articles/minor-gc-vs-major-gc-vs-full

在使用 Plumbr 中的 GC 暂停检测功能时，我不得不通过大量关于该主题的文章、书籍和演示文稿来工作。在整个过程中，我反复对 Minor、Major 和 Full GC 事件的使用感到困惑。这导致了这篇博客文章，我希望我能设法消除一些这种困惑。

## Minor GC

从 Young 空间（由 Eden 和 Survivor 空间组成）收集垃圾称为 Minor GC。这个定义既清楚又统一，但是在处理 Minor Garbage Collection 事件时，您仍然应该注意一些有趣的要点：

1. 当 JVM 无法为新对象分配空间时，总是会触发 Minor GC，例如Eden快满了。所以分配率越高，Minor GC 执行的频率越高。
2. 每当池被填满时，它的全部内容都会被复制，并且指针可以再次从零开始跟踪空闲内存。因此，不是经典的 Mark、Sweep 和 Compact，而是使用 Mark 和 Copy 来清理 Eden 和 Survivor 空间。因此，在 Eden 或 Survivor 空间内实际上不会发生碎片。写指针始终驻留在已用池的顶部。

所以对于 Minor GC，情况就很清楚了——每个 Minor GC 都会清理年轻代。

## Major GC vs Full GC

应该注意到，这些术语没有正式的定义，在 JVM 规范和垃圾收集研究论文中都没有，但乍一看，在我们知道的关于 Minor GC 清理 Young 空间的正确知识之上构建这些定义应该很简单：

- **Major GC** is cleaning the Tenured space.
- **Full GC** is cleaning the entire Heap – both Young and Tenured spaces.

不幸的是，实际情况比它更复杂和混乱点。

首先——许多 Major GC 是由 Minor GC 触发的，因此在许多情况下将两者分开是不可能的。另一方面——许多现代垃圾回收会部分地清理 Tenured 空间，所以再一次，使用术语“清理”只是部分正确。这导致我们不必担心 GC 是称为 Major GC 还是 Full GC，而应该专注于找出手头的 GC 是否停止了所有应用程序线程，或者它是否能够与应用程序线程同时进行。

这种混淆甚至内置在 JVM 标准工具中。 我最好通过一个例子来解释。 让我们比较两个不同工具在使用 Concurrent Mark and Sweep 收集器 (-XX:+UseConcMarkSweepGC) 运行的 JVM 上跟踪 GC 的输出。

首先尝试是通过 jstat 输出得到结果:

```bash
#略...
```



基于这些信息，我们可以得出结论，在 12 次 Minor GC 运行后，执行了两次 Full GC 运行，总共跨越 50 毫秒。

在得出这个结论之前，让我们看看从同一个 JVM 启动收集的垃圾收集日志的输出。显然 -XX:+PrintGCDetails 告诉我们一个不同的更详细的故事：

```bash
#略...
```

基于这些信息，我们可以看到确实在 12 次 Minor GC 运行之后“一些不同的事情”开始发生，但是这个“不同的东西”实际上只是在 Tenured 代中运行的单个 GC，而不是两次Full GC，包括不同阶段组成：

- 初始标记阶段，跨越大约 4 毫秒。此阶段是停止所有应用程序线程以进行初始标记的stop-the-world事件。
- 并发执行标记和预清理阶段，这些与应用程序线程同时运行。
- 最终标记阶段，跨越大约 46 毫秒。这一阶段再次成为stop-the-world 事件。
- 并发执行的 Sweep 操作。顾名思义，这个阶段也是在不停止应用程序线程的情况下同时执行的。

所以我们从实际的垃圾收集日志中看到的是——不是两次 Full GC 操作，实际上只执行了一次 Major GC 清理旧空间。

## Conclusion

考虑到这种情况，最好避免考虑 Minor、Major 或 Full GC。相反，监控您的应用程序的延迟或吞吐量并将 GC 事件链接到结果。

除了这些事件，您还需要了解特定 GC 事件是强制所有应用程序线程停止还是同时处理的事件的一部分。

## 记录

- was forced to 
- fragmentation