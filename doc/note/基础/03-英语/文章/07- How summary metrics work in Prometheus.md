> 原文：https://grafana.com/blog/2022/03/01/how-summary-metrics-work-in-prometheus/

汇总summary是 Prometheus 中的一种度量类型，可用于监控延迟（或其他分布，如请求大小）。例如，当您监控 REST 端点时，您可以使用汇总并将其配置为提供 95% 的延迟。
如果该百分位数是 120 毫秒，则意味着 95% 的调用比 120 毫秒快，5% 比 120 毫秒慢。

摘要指标在 Prometheus 客户端库中实现，例如 client_golang 或 client_java。您在 REST 应用程序中调用该库，该库将为您计算百分位数。

理解这些库中使用的底层算法很困难。您将看到引用名为 “Effective computation of biased quantiles over data streams” by Graham Cormode, Flip Korn, S. Muthuk<PERSON>an, and Divesh Srivastava 研究论文的代码注释，通常由作者姓氏的首字母缩写为 CKMS。如果你像我一样，你会发现那篇论文很难阅读。

这篇博文试图用简单的术语来总结 CKMS 算法。

## Do I really need to know this？
不，您可以在不知道它们内部如何工作的情况下使用汇总指标。 在测量延迟时，您还应该知道其他一些事情，例如[ when to use summaries and when to use histograms](https://prometheus.io/docs/practices/histograms/)，但用于计算百分位数的算法都不是。
然而，深入了解底层是非常有趣的，了解 CKMS 算法的工作原理将帮助您在监控延迟或其他分布时避免一些常见的陷阱。

## Terminology

术语百分位数和分位数的含义基本相同：
- 百分位数表示为 0 到 100 之间的百分比。例如，“延迟分布的第 95 个百分位数是 120 毫秒”意味着该分布中 95% 的观察值比 120 毫秒快，5% 比 120 毫秒慢。
- φ 分位数 (0 ≤ φ ≤ 1) 表示为 0 和 1 之间的值。例如，延迟分布的 0.95 φ 分位数与该延迟分布的第 95 个百分位数相同。

在这篇博文中，我们使用 quantile 作为 φ-quantile 的简称，遵循 CKMS 论文中使用的术语.


## Exact quantiles
计算一个精确的分位数很简单，但它会消耗大量内存，因为您需要将所有延迟观察结果存储在一个排序列表中。让我们引入一些变量：
- n：观察总数（即排序列表的大小）。
- rank：观察在排序列表中的位置。

给定一个完整排序的观察列表，很容易找到分位数。 例如，如果您正在寻找 0.95 分位数，您只需获取具有 *rank = 0.95*n * 的元素（即，将位置 0.95*n 处的元素向上舍入以转换为 int）。


## Data model for a compressed list
存储观察到的延迟的完整列表对于现实世界的应用程序显然会占用太多内存。因此，CKMS 算法定义了一个 compress 函数，该函数从列表中丢弃观察值，仅保留少量样本。


缺点是我们无法再知道样本的确切排名：当我们将新的观察结果插入样本列表时，无法准确说出它会在完整列表中的哪个位置结束。

但是，我们可以跟踪每个样本的最小和最大可能排名：


请注意，可能等级的间隔重叠。 但是，每个样本的最小可能排名总是比其前身的最小可能排名至少大 1。

在实践中，存储每个样本的绝对最小和最大可能秩是不方便的，因为当我们插入一个新样本时，右边所有样本的秩都需要递增 1。

因此，CKMS 算法存储增量而不是绝对值：

g：这个样本的最小等级与其前身的最小等级之间的差异。
delta：间隔的大小，即最大可能排名减去最小可能排名。
使用该编码，上面的示例列表如下所示：


使用该表示，我们可以在不更新列表中现有样本的情况下插入新样本。

通过将样本的 g 值与列表中所有前辈的 g 值之和相加，我们可以很容易地计算出样本的最小可能排名。 最大可能等级是最小加增量。