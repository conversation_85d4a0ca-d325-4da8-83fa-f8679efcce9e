> 原文：https://windsock.io/the-docker-proxy/

## 概述

由 Docker 平台创建和管理的容器能够提供在容器内运行的服务，不仅可以提供给其他位于同一位置的容器，还可以提供给远程主机。Docker 通过端口转发实现了这一点。

当一个容器启动时，它的端口转发到它运行的 Docker 主机，除了在容器内运行的新进程之外，还会在主机上启动一个额外进程，称为 docker-proxy：

```
/usr/libexec/docker/docker-proxy-current -proto tcp -host-ip 0.0.0.0 -host-port 3306 -container-ip ********** -container-port 3306
```

此进程的目的是使服务使用者能够与提供服务的容器进行通信，但它只在特定情况下被使用。

docker-proxy 在用户空间中运行，并简单地接收到达主机指定端口的任何数据包，内核尚未“丢弃”或转发，并将它们重定向到容器的端口。docker-proxy 与 Docker 守护程序、 Docker 客户端是相同的二进制文件，Docker 守护程序在需要时会“重新执行”。

为了理解为什么会存在这个过程，我们首先需要了解一点Docker的网络配置。Docker 主机的默认操作方式是创建一个虚拟以太网网桥（称为 docker0），并将每个容器的网卡连接到网桥，并在容器需要使自己对 Docker 主机可见时使用网络地址转换（NAT）：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220902*********.png" alt="image-20220902*********" style="zoom:50%;" />

控制对容器服务的访问由与主机的 netfilter 框架相关联的规则控制，包括 NAT 和filter tables， netfilter 对数据包的一般处理流程如[this diagram](http://inai.de/images/nf-packet-flow.png).所示。

如果要将容器的端口 172.17.0.2:8000 作为端口 8000 转发到主机，则 Docker 将一些规则添加到 netfilter 的 NAT 表中，使容器能够“伪装”为使用 NAT 的主机：

```
Chain PREROUTING (policy ACCEPT 49 packets, 9985 bytes)
num   pkts bytes target     prot opt in     out       source               destination         
1       80  4152 DOCKER     all  --  *      *         0.0.0.0/0            0.0.0.0/0            ADDRTYPE match dst-type LOCAL

Chain OUTPUT (policy ACCEPT 1436 packets, 151K bytes)
num   pkts bytes target     prot opt in     out       source               destination         
1      274 56172 DOCKER     all  --  *      *         0.0.0.0/0           !*********/8          ADDRTYPE match dst-type LOCAL

Chain POSTROUTING (policy ACCEPT 1369 packets, 137K bytes)
num   pkts bytes target     prot opt in     out       source               destination         
1      274 56172 MASQUERADE all  --  *      !docker0  172.17.0.0/16        0.0.0.0/0                   
2        0     0 MASQUERADE tcp  --  *      *         172.17.0.2           172.17.0.2           tcp dpt:8000

Chain DOCKER (2 references)
num   pkts bytes target     prot opt in       out     source               destination         
1        0     0 DNAT       tcp  --  !docker0 *       0.0.0.0/0            0.0.0.0/0            tcp dpt:8000 to:172.17.0.2:8000
```



Netfilter 是有状态的，这意味着它可以跟踪已经建立的连接，在这种情况下它会绕过 NAT 表规则，但是为了首先建立连接，数据包要接受 NAT 和过滤表中规则的审查。

发往主机socket（容器的转发端口）的数据包由 netfilter 处理，并根据 NAT 表的 PREROUTING 链中的规则进行测试，如果数据包的目标地址是 Docker 主机本地的，则 netfilter 跳转到 DOCKER 链做进一步处理，如果数据包的地址是 Docker 主机上的 TCP 端口 8000，那么 DNAT 目标将其目的地更改为 172.17.0.2:8000，即容器套接字。 由于需要将数据包路由到容器，因此评估过滤表的 FORWARD 链中的规则：

```
Chain FORWARD (policy ACCEPT 0 packets, 0 bytes)
num   pkts bytes target     prot opt in       out      source              destination         
1       63 10326 DOCKER     all  --  *        docker0  0.0.0.0/0           0.0.0.0/0           
2       50  9618 ACCEPT     all  --  *        docker0  0.0.0.0/0           0.0.0.0/0            ctstate RELATED,ESTABLISHED
3       61  5675 ACCEPT     all  --  docker0 !docker0  0.0.0.0/0           0.0.0.0/0           
4        0     0 ACCEPT     all  --  docker0  docker0  0.0.0.0/0           0.0.0.0/0           

Chain DOCKER (1 references)
num   pkts bytes target     prot opt in       out      source              destination         
1        0     0 ACCEPT     tcp  --  !docker0 docker0  0.0.0.0/0           172.17.0.2           tcp dpt:8000
```

第一条规则适用，它强制跳转到 DOCKER 链，链中的单个规则匹配数据包的特征，并“接受”数据包以转发到容器的套接字。因此，一个远程服务消费进程认为它正在与 Docker 主机通信，但实际上是由容器提供服务。

> 注：**postrouting一般指的是发往公网的数据包；prerouting一般指来自公网的数据包**

类似地，当容器发起与远程服务提供者的对话时，netfilter 的 NAT POSTROUTING 链将数据包的源 IP 地址从容器的 IP 地址更改为负责将数据包路由到所需目的地的主机网络接口地址(host's network interface ) 。这是通过 netfilter 的 MASQUERADE 目标实现的。

Docker 主机大量使用 netfilter 规则来辅助 NAT，并控制对其托管的容器的访问，并且并不总是需要 docker-proxy 机制。但是在某些情况下，这种控制方法不可用，这就是为什么每当容器的端口被转发到 Docker 主机时，Docker 需要创建一个 docker-proxy 实例。

## 什么时候需要创建 docker-proxy ？

首先，为了让远程主机使用容器的服务，Docker 主机必须充当路由器，将流量转发到与以太网桥相关的网络。Linux 主机通常不配置为路由器，因此需要将内核参数 net.ipv4.ip_forward 设置为 1， 如果 Docker 的守护进程以默认设置启动，则 Docker 会处理此问题。但是，如果守护进程是在 --ip-forward 和/或 --iptables 命令行选项设置为 false 的情况下启动的，那么 Docker 将无法使用 netfilter 规则，而必须倒退依靠 docker-proxy，**这种情况可能很少见，但可以想象一些公司的安全策略可能会施加这种约束**。

其次，即使 Docker 能够使用 netfilter 规则转发数据包，也有一种情况是无法应用 netfilter 规则。除非另有说明，当一个容器的端口被转发到 Docker 主机时，它将被转发到主机的所有接口，包括它的环回接口。 但是 Linux 内核不允许环回流量的路由，因此无法将 netfilter NAT 规则应用于来自 *********/8 的数据包。 相反，netfilter 通过过滤表的 INPUT 链将数据包发送到侦听指定端口的本地进程 - docker-proxy。

因此，docker-proxy 是一种“包罗万象”的方法，允许将容器端口转发到 Docker 主机。但是，一般认为 docker-proxy 是对上面强调的问题的一种不优雅的解决方案，并且当大范围的容器端口暴露时，它会[消耗相当大的内存](https://github.com/moby/moby/issues/11185)。之前曾尝试删除对 docker-proxy 的依赖，但这违反了 RHEL 6.x 和 CentOS 6.x 中老化内核的限制，Docker 项目认为有义务支持这些限制。

因此，在当前版本 1.5 之前的所有 Docker 版本中，docker-proxy 仍然是 Docker 体验的主要组成部分。

在我写这篇文章的时候，1.6 版即将发布，并且已经移除了对 docker-proxy 的自动要求，我将在另一篇文章中介绍。

## 小结

- be to enable... 使得...
- circumstances
-  are subjected 
- scrutiny
- destined for
- applies 适用
- dialogue
- aid
-  make use of
- fall back on
- conceivable
- impose
- circumstance
- it is not possible to apply netfilter rules
- Unless told otherwise
- it's generally considered
- inelegant 不优雅的
- attempt
- imminent