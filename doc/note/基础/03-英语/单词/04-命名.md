## 项目名

- karma

- avitor

- mascot

- fabric

- skuld 希腊神话，代表将来

- axion 轴子

- fortress 堡垒

- Methuselah 圣经中最长寿的人

- supersonic

- bullseye 靶心

- Sonic 声速的

- sonnet 十四行诗

- catalyser 催化剂

- Tilano 提尔纳诺

  

## 类名

- Epilogue  后记、尾声、收场





## 日志

清洗的报错信息便于排查问题，这里记录一些英文报错日志：

```properties
java.lang.IllegalStateException: The platform metadata area could not be written: /Volumes/MemoryAnalyzer/MemoryAnalyzer.app/Contents/MacOS/workspace/.metadata.  By default the platform writes its content
under the current working directory when the platform is launched.  Use the -data parameter to
specify a different content area for the platform.
```



```properties
Caused by: java.io.FileNotFoundException: class path resource [org/springframework/boot/autoconfigure/web/ErrorController.class] cannot be opened because it does not exist
```



```properties
Caused by: java.lang.LinkageError: loader constraint violation: when resolving method "org.springframework.http.client.OkHttp3ClientHttpRequestFactory.<init>(Lokhttp3/OkHttpClient;)V" the class loader (instance of com/alipay/sofa/ark/container/service/classloader/PluginClassLoader) of the current class, com/wacai/middleware/waferpc/extend/proxy/RestTemplateProxyTarget, and the class loader (instance of com/alipay/sofa/ark/container/service/classloader/BizClassLoader) for the method's defining class, org/springframework/http/client/OkHttp3ClientHttpRequestFactory, have different Class objects for the type okhttp3/OkHttpClient used in the signature
	at com.wacai.middleware.waferpc.extend.proxy.RestTemplateProxyTarget.<init>(RestTemplateProxyTarget.java:37) 
```



我们的模仿的：

```java
ResultFuture<Packet.ResponsePacket> result = Results.getResultFuture(packet.getReqId());
if(result==null){
    throw new IllegalArgumentException("the request "+packet.getReqId()+" does not exist");
}
```

