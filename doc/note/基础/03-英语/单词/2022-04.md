### Introducing Domain-Oriented Microservice Architecture

| 单词                    | 含义                                                 | 例句                                                         |
| ----------------------- | ---------------------------------------------------- | ------------------------------------------------------------ |
| worse                   | n. 更坏的事, a. 更坏的, 更恶劣的ad. 更坏地, 更恶劣地 |                                                              |
| worth                   | n. 价值, 财产 a. 值...的, 值得的                     |                                                              |
| this sort of            | 这种                                                 | This sort of life is not [calculated](吸引力) to appeal to a young man of 20 |
| increasingly            | 同 more and more                                     | increasingly specific：越来越具体                            |
| broadly                 | a.  广泛的 ad. 宽阔地                                | [broadly established](https://www.redhat.com/en/topics/api/what-does-an-api-gateway-do) |
| vary                    | vt. 改变, vi. 变化                                   | Our definition does not vary greatly from the established definition |
| exclusively             | ad. 只, 仅仅                                         |                                                              |
| tend to                 | 倾向于                                               |                                                              |
| in terms of             | 在…方面;                                             | gateways provide numerous benefits in terms of future migrations, discoverability |
| comes with              | 附带                                                 |                                                              |
| outside of              | 除了                                                 | Outside of logic and data extensions, many teams at Uber have introduced their own extension patterns that are appropriate for their domain |
| drop the time           | 节约时间                                             |                                                              |
| integrate a new feature | 集成新功能                                           |                                                              |
| Ever changing           | 不断变化                                             |                                                              |



- Keeps track of ；跟踪
- negotiate；协商
- getPrecedence；优先
- expression；表达式
- representation; 表示
- concise; 简洁的
- In contrast; 相比之下
