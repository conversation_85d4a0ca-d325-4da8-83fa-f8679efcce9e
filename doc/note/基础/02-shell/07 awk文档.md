## 基本语法

awk 的基本用法就是下面的形式。

```bash
# 格式
$ awk pattern1{ action; } 
$ awk pattern2{ action; } 

```

本质是一组 patterns 和 actions 的集合，patterns 表示条件，可以是正则也可以是表达式， 如果空白表示匹配所有行。

test.log:

```
a	1
b	2
c	3
```

示例：

```bash
awk '{print $1}' /tmp/test.log 	#输出a,b,c
awk '/b/ {print $1}' /tmp/test.log 	#正则，输出b
awk 'NR % 2 == 1 {print $1}' demo.txt #输出奇数行

```



## 正则表达式

awk 中正则表示需要通过/ /包裹起来，比如：

```shell
awk '/b/ {print $1}' /tmp/test.log # 匹配b
awk '{gsub(/\-2(\w)*/,""); print}'  shards.log
```



## BEGIN 和 END

BEGIN 和 END是一个特殊的pattern，可以简单理解在awk最初和最后执行一次，下面是一个样例：

```bash
awk 'BEGIN{ a=100 } { a++ } END{ print a }' /tmp/test.log
103
```

我们在 BEGIN pattern中声明了一个变量a, 在后续的 pattern 中自增a，最后在 END pattern 中输出 a，因为文件只有三行，所以a 为3。

我们甚至还可以在 BEGIN 里写一些 if、for 循环语句，代码：

```bash
#!/usr/bin/awk -f

BEGIN {

    # BEGIN will run at the beginning of the program. It's where you put all
    # the preliminary set-up code, before you process any text files. If you
    # have no text files, then think of BEGIN as the main entry point.

    # Variables are global. Just set them or use them, no need to declare.
    count = 0;

    # Operators just like in C and friends
    a = count + 1;
    b = count - 1;
    c = count * 1;
    d = count / 1; # integer division
    e = count % 1; # modulus
    f = count ^ 1; # exponentiation

    a += 1;
    b -= 1;
    c *= 1;
    d /= 1;
    e %= 1;
    f ^= 1;

    # Incrementing and decrementing by one
    a++;
    b--;

    # As a prefix operator, it returns the incremented value
    ++a;
    --b;

    # Notice, also, no punctuation such as semicolons to terminate statements

    # Control statements
    if (count == 0)
        print "Starting with count of 0";
    else
        print "Huh?";

    # Or you could use the ternary operator
    print (count == 0) ? "Starting with count of 0" : "Huh?";

    # Blocks consisting of multiple lines use braces
    while (a < 10) {
        print "String concatenation is done" " with a series" " of"
            " space-separated strings";
        print a;

        a++;
    }

    for (i = 0; i < 10; i++)
        print "Good ol' for loop";
}        
```



## 内置变量-$0、$1

awk会根据空格和制表符，将每一行分成若干字段，依次用$1、$2、$3代表第一个字段、第二个字段、第三个字段等等，$0代表整行。

可以通过-F 参数指定分隔符输出特定的列，比如分隔符为逗号，输出第一列：

```bash
awk -F ',' '{print $1}'
```



除了$ + 数字表示某个字段，awk还提供其他一些变量：

- NF(Number of Filed)：字段数量;
- FS：字段分隔符，默认是空格和制表符。
- OFS：输出字段的分隔符，用于打印时分隔字段，默认为空格。
- RS：行分隔符，用于分割每一行，默认是换行符。
- ORS：输出记录的分隔符，用于打印时分隔记录，默认为换行符。 可以通过ORS实现列转行。

例子：

```bash
awk '{print $1,$2}' OFS=','  /tmp/test.log  #输出a,1
awk '{print $1}' ORS=' '  /tmp/test.log  	# 输出a b c
```

## 数组

awk 的数组也比较强大，可以关联下标，也可以关联字符串(有点类似map):

```shell
# Arrays
arr[0] = "foo";
arr[1] = "bar";

# Associative arrays
assoc["a"] = "bar";
assoc["b"] = "bar";
assoc["foo"] = "bar";

# And multi-dimensional arrays
multidim[0,0] = "foo";
multidim[0,1] = "bar";
multidim[1,0] = "baz";
multidim[1,1] = "boo";

# You can test for array membership
if ("foo" in assoc)
	print "Fooey!";

# You can also use the 'in' operator to traverse the keys of an array
for (key in assoc)
	print assoc[key];

```

例子：

```bash
awk 'BEGIN { assoc["foo"] = "bar"} END {print assoc["foo"]}'   /tmp/test.log  #输出 bar
```

可以利用 awk 数组实现 group count/sum：

```bash
awk '{++assoc[$1]} END { for(key in assoc) print key,assoc[key]}' /tmp/test.log # count
awk '{assoc[$1]+=$2} END { for(key in assoc) print key,assoc[key]}' /tmp/test.log #sum
```





## 内置函数

上面例子中的 print awk 内置了很多强大有用的函数，包括：

- split: 字符串切分。

- substr：返回子字符串。

- sub：替换首个字符。

- gsub：全局替换。

  

```bash
localvar = "fooooobar";
sub("fo+", "Meet me at the ", localvar); # fooooobar => "Meet me at the bar"
gsub("e", ".", localvar); # Meet me at the bar => "m..t m. at th. bar"
    
# Split on a delimiter
n = split("foo-bar-baz", arr, "-"); # a[1] = "foo"; a[2] = "bar"; a[3] = "baz"; n = 3

 # Other useful stuff
sprintf("%s %d %d %d", "Testing", 1, 2, 3); # => "Testing 1 2 3"
substr("foobar", 2, 3); # => "oob"
substr("foobar", 4); # => "bar"
length("foo"); # => 3
tolower("FOO"); # => "foo"
toupper("foo"); # => "FOO"

printf
```



例子:

```bash
awk 'BEGIN { n = split("foo-bar-baz", arr, "-"); for(x in arr) print arr[x]}' /tmp/test.log
awk '{n=split($0,arr,",");} END {for(x in arr) print arr[x] }'  /tmp/log
```







## 实践

### 字符串转义

打印字符串常量使用 "字符串 " ,例如：

```
 cat /tmp/log2 | awk '{print "insert " $1}'
```

字符串中打印单引号需要需要转义

```
'\''
```

例如:

```
 cat /tmp/log2 | awk '{print "insert '\'' " $1}'
```

### 字符串替换

下列中2023-*替换为2024-1-*

```properties
DELETE w-fund_fund-fs-provider-2023-*
```

用法：

```shell
 cat /tmp/log  | awk '{ gsub("2023-*","2024-01-*"); print $0}'
```

去掉数字：

```shell
 cat /tmp/1.log  | awk '{print $1}' | sort | awk '{ gsub("-2024-[0-9]{2}-[0-9]{2}","");print }'
```



### sum

```shell
cat /tmp/log10 | grep "AMX" |  awk '{total = total + $1}END{print total}'
```

### group by+gsub

需要汇总下面的数据:

```shell
loan_yo-voice-event_2019-10                1635
loan_yo-voice-event_2019-10                1635
user_material                                80
user_material                                80
wke_event_center_2022-07-25                  10
```

 首先需要去掉日期后缀，有的文件名后缀是到月，有的是到天，还有的文件名不带后缀

最开始的想法是通过 _2 作为分隔符切换

```shell
 awk -F '_2' '{print $1 "\t" $2}' shards.log
```

输出：

```shell
loan_yo-voice-event	019-10                1635
loan_yo-voice-event	019-10                1635
user_material                                80
user_material                                80
wke_event_center	022-07-25                  10
```

然后分开拿到$1 和$3，类似这样：

```shell
awk -F '_2' '{print $1 "\t" $2}' | awk '{print $1 "\t" $3}'
```

输出：

```shell
loan_yo-voice-event	1635
loan_yo-voice-event	1635
user_material
user_material
wke_event_center	10
```

但是可以看到这样会导致 user_material 的值丢失，所以不能采用分割符的方式。

awk 的gsub() 支持字符串替换，而且支持正则，用法如下(注意只能在linux上执行,mac无效):

```shell
echo 'loan_yo-inspection_2021-10-11' | awk '{gsub(/\_2(\w|-)*/,""); print}'

loan_yo-voice-event               1635
loan_yo-voice-event               1635
user_material                     80
user_material                     80
wke_event_center                  10
```

解决了文件名之后就是sum了，[参考这里](https://stackoverflow.com/questions/14916826/awk-unix-group-by)，使用 arr

```shell
 awk '{gsub(/\_2(\w|-)*/,""); print}' |  awk  '{arr[$1]+=$2}END{for (a in arr) print a, arr[a]}'
```

最后再加上排序，完整的命令

```shell
cat shards.log | grep -v "\." | grep -v test | awk '{gsub(/\_2(\w|-)*/,""); print}'| awk '{gsub(/\-2(\w|-)*/,""); print}' | awk  '{arr[$1]+=$2}END{for (a in arr) print a, arr[a]/1024}' | sort -rnk 2
```

### row to column

原文：

```
a,b,c,d
```

需要输出

```
a
b
c
d
```

两种思路：

1、利用内置函数(split)

```bash
 awk '{n=split($0,arr,",");} END {for(x in arr) print arr[x]; print n}'  /tmp/log
```

2、利用内置变量NF

```bash
awk -F ',' '{ for(i=1; i<=NF;++i){print $i}} ' /tmp/log
```

注意行号从 1 开始，如需要统计有几行可以在加个END:

```shell
awk -F ',' '{for (i=1 ; i <= NF ; i++) print $i} END{print i}' /tmp/log
```



 3、没看懂

```bash
awk -F',' 'BEGIN{OFS=ORS} {$1=$1; print}' /tmp/log
```

### 列转行

原文:

```
a
b
c
```

输出:

```
a,b,c
```

思路：

利用 printf 函数

```
awk  '{ printf( "'\''%s'\'',", $1 ); } END { printf( "\n" ); }' /tmp/log
```

例子：

```
cat /tmp/t1 | awk -F '|' '{print $2}' | awk  '{ printf( "'\''%s'\'',", $1 ); } END { printf( "\n" ); }'
```

### 按照字段过滤

给的原始数据如下:

```
饶心怡	1	2
汤  彦	0	0
黄晓婷	1	2
```

需要把第二列为 0 的数据过滤掉。参考[filter column with awk and regexp](https://stackoverflow.com/questions/18962153/filter-column-with-awk-and-regexp) , 表达式如下：

```
 cat /tmp/test2.log | awk  '$2~/^[1-9]/{ print $1 " "$3}'
```




## 参考

- [learn-awk-in-y-minutes](https://learnxinyminutes.com/docs/awk/)
- [阮一峰 awk 入门教程](https://www.ruanyifeng.com/blog/2018/11/awk.html)
