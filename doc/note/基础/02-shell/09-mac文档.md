##   		必备软件

安装清单

- chrome
- 翻墙
- iterm
- oh-my-zsh
- brew
- moom
- sublime
- typora
- 配置ssh

### 1、jdk

下载[jdk11](https://www.oracle.com/java/technologies/javase/jdk11-archive-downloads.html)，编辑 ~/.zshrc 设置多版本：

```shell
export JAVA_8_HOME=/Library/Java/JavaVirtualMachines/jdk1.8.0_321.jdk/Contents/Home
export JAVA_11_HOME=/Library/Java/JavaVirtualMachines/jdk-11.0.15.jdk/Contents/Home
export JAVA_HOME=JAVA_11_HOME

maven_3_6="/work/dist/sys/apache-maven-3.6"
export M2_HOME="$maven_3_6"

export PATH="$PATH:/$M2_HOME/bin"
```



### 2、maven

[官方下载](https://maven.apache.org/download.cgi)3.6版本，注意3.8开始要求私有仓库必须是HTTPS

替换IDEA自带的maven路径：

``` 
/Applications/IntelliJ IDEA CE.app/Contents/plugins/maven/lib/maven3
```



### 2、moom

 官网下  载软件之后，在163邮箱中搜索moom能看到证书。

### 3、iterm

[官网](https://iterm2.com/downloads.html) 下载

### 4、oh-my-zsh

[官网](https://ohmyz.sh/#install)，执行:

```
$ sh -c "$(curl -fsSL https://raw.github.com/ohmyzsh/ohmyzsh/master/tools/install.sh)"
```

~/.zshrc 文件，配置:

```shell
export JAVA_8_HOME="/Library/Java/JavaVirtualMachines/jdk1.8.jdk/Contents/Home"
export JAVA_11_HOME="/Library/Java/JavaVirtualMachines/jdk-11.jdk/Contents/Home"
export JAVA_17_HOME="/Library/Java/JavaVirtualMachines/jdk-17.jdk/Contents/Home"

export JAVA_HOME="$JAVA_8_HOME"
#export JAVA_HOME="$JAVA_11_HOME"

maven_3_6="/work/dist/sys/apache-maven-3.6.3"
export M2_HOME="$maven_3_6"

```

### 5、git config

编辑:~/.gitconfig

```shell
# This is Git's per-user configuration file.
[user]
	name = bairen
	email = <EMAIL>

[alias]
        last = log -1 HEAD
        co = checkout
        br = branch
        ci = commit
        st = status
        
[filesystem "Oracle Corporation|1.8.0_121|/dev/disk1s1"]
	timestampResolution = 1001 milliseconds
	minRacyThreshold = 0 nanoseconds

[pull]
	rebase = false

[oh-my-zsh]
	hide-dirty = 1
	
[core]
	quotepath = false	
	
```

**注意**：部分开源软件会校验提交人，必须和github用户名一致：

```
git config --global user.name "yunpeng.jiangyp"
git config --global user.email "<EMAIL>"
```





### 6、idea

[参考这篇文章](https://www.jianshu.com/p/bbc7cd207a85)， 注意必须使用Mac idea 2020.1 版本

问题排查：when occur error running from tenminal

```
/Applications/IntelliJ\ IDEA.app/Contents/MacOS/idea
```

IDEA use this path for save configuration:

```
~/Library/Application\ Support/JetBrains
```

Required plugin

| 插件             | 地址                                                         | 重要性 |
| ---------------- | ------------------------------------------------------------ | ------ |
| lombok           | https://plugins.jetbrains.com/plugin/6317-lombok             | ⭐️⭐️⭐️    |
| eclipse theme    | https://plugins.jetbrains.com/plugin/17004-eclipse-plus-theme | ⭐️⭐️     |
| maven-helper     | https://plugins.jetbrains.com/plugin/7179-maven-helper       | ⭐️⭐️⭐️    |
| protocol-buffers | https://plugins.jetbrains.com/plugin/14004-protocol-buffers  | ⭐️⭐️⭐️    |

protocol buffer成功之后是这样的：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230704110737258.png" alt="image-20230704110737258" style="zoom:50%;" />



设置字体大小 14

```
Settings > Editor > Font Set Size 14
```

设置导入内部类:

```
Settings > Editor > Code Style > Java > Imports` and mark the `Insert imports for inner classes` checkbox.
```



### 7、ssh 

执行

```
cp -r /work/dist/branch/wacai/middleware/my-boot-raw/configs/ssh ~/.ssh
chmod 400 ~/.ssh/id_rsa
```



### 8、axure 8

[官方下载](https://www.axure.com/release-history/rp8)

填入 key 和 licence

```
www.jb51.net
D0Srb+A6vmCrc+AClDrmRTQFpzTEXNuX7Ep1Prr9GqpKOU9JZHbA8OFwOYcZr7L4
```

编辑连接点的快捷键是Commond+8，也可以右键 **Transform Shape → Edit Connector Points**

好用的库：

- [UML资源](https://github.com/refscn/rplibs)

### 9、 kubectl

安装：

```
brew install kubectl
```

### 10、typora

序列号

```
7BV5YG-XEFG6Y-97TDX6-A6YHHA
```

快捷键

- commond +k: 插入超链接(可以先复制好连接)

### 11、 VIM

设置~/.vimrc

```
set ts=2 sw=2
```

### 12、python 

```
curl https://bootstrap.pypa.io/get-pip.py -o get-pip.py
python get-pip.py
```

### 13、 翻墙工具

目前使用的是 [clashX](https://github.com/yichengchen/clashX)，可以通过如下方式添加白名单解决内网域名被代理的问题

设置-更多设置-忽略这些主机

```
*.wacai.info,*.wacai.com,*.xamc.info,*.wramc.info
```



### 14、ascii diagrams

之前代码或文档中会看到类型这样的图，比如[CeresDB](https://github.com/CeresDB/ceresdb/blob/main/docs/guides/src/architecture.md)的架构图：

```
┌───────┐      ┌───────┐      ┌───────┐
│       │──1──▶│       │──2──▶│       │
│Server │      │  SQL  │      │Catalog│
│       │◀─10──│       │◀─3───│       │
└───────┘      └───────┘      └───────┘
                │    ▲
               4│   9│
                │    │
                ▼    │
┌─────────────────────────────────────┐
│                                     │
│             Interpreter             │
│                                     │
└─────────────────────────────────────┘
                           │    ▲
                          5│   8│
                           │    │
                           ▼    │
                   ┌──────────────────┐
                   │                  │
                   │   Query Engine   │
                   │                  │
                   └──────────────────┘
                           │    ▲
                          6│   7│
                           │    │
                           ▼    │
 ┌─────────────────────────────────────┐
 │                                     │
 │            Table Engine             │
 │                                     │
 └─────────────────────────────────────┘
```

有时候感觉这样梳理代码挺快的，想找下这种图怎么画，最开始google的关键词 text diagram , charset diagram, banner text ,write flow diagram text in code comment 都不对，最后突然想起 ascii text flow 才搜到有效信息，所以要找到正确的内容关键词还是挺重要的。

找到 5 个还不错的 ascii diagrams tools：

- [asciiflow](https://asciiflow.com/#/) 在线画图
- [textik](https://textik.com/) 在线画图
- [graph-easy](http://bloodgate.com/perl/graph/manual/overview.html) 本地工具,Perl实现，重点推荐
- [plantuml](https://plantuml.com/zh/) 一个通过文本产生uml的工具，搜索过程中发现的
- [mermaid](https://github.com/mermaid-js/mermaid) markdown中可用



使用[asciiflow](https://asciiflow.com/#/) 在线画图再本地修改之后的效果：

```
 * 线程安全的FetcherAPI:
 * ┌────────────────┐                                     ┌───────────────┐
 * │  Consumer1     ├──────┐                       ┌──────┤HttpConnection1│
 * └────────────────┘      │                       │      └───────────────┘
 * ┌────────────────┐      │    ┌─────────────┐    │      ┌───────────────┐
 * │  Consumer2     ├──────┼────┤  FetcherAPI ├────┼──────┤HttpConnection2│
 * └────────────────┘      │    └─────────────┘    │      └───────────────┘
 * ┌────────────────┐      │                       │      ┌───────────────┐
 * │   Consumer3    ├──────┘                       └──────┤HttpConnection3│
 * └────────────────┘                                     └───────────────┘
 *
 * Author: bairen
 * Date:2024/4/26 17:13
 */
```



graph-easy

安装：

```
brew install graphviz
cpan
cpan Graph:Easy
```

一些例子：

入门

```
 echo  '[a]->[b]->[c][b]->[d]->[e]' | graph-easy
```

输出：

```
+---+     +---+     +---+     +---+
| a | --> | b | --> | d | --> | e |
+---+     +---+     +---+     +---+
            |
            |
            v
          +---+
          | c |
          +---+
```



使用[mermaid](https://github.com/mermaid-js/mermaid)

```mermaid
graph LR;
    A-->B;
    B-->B1;
    B-->B2;
    B1-->C;
    B2-->C;
    C-->D;
```





```mermaid
graph LR
    A[create_retrieval_chain] --> B1[create_history_aware_retriever]
    A --> B2[create_stuff_documents_chain]
    B1 --> C1[history]
    B1 --> C2[retriever]
    B2 --> C3[qa_prompt]
```



序列图例子：

```mermaid
sequenceDiagram
    participant Alice
    participant Bob
    Alice->>Bob: Hello
    Bob->>Alice: Hi there!
    activate Alice
    Alice->>Bob: How are you?
    deactivate Alice
```





```mermaid
sequenceDiagram
    participant DefaultChannelPipeline
    participant HeadContext
    participant HttpServerCodec
    participant HttpObjectAggregator
    participant HttpRequestHandler

    activate DefaultChannelPipeline
    DefaultChannelPipeline ->>HeadContext:fireChannelRead(ByteBuf)
    activate HeadContext
    HeadContext->>HttpServerCodec:fireChannelRead(ByteBuf) 
    activate HttpServerCodec
    HttpServerCodec->>HttpServerCodec:decode() [DefaultHttpRequest,EmptyLastHttpContent]
    HttpServerCodec->>HttpObjectAggregator:fireChannelRead(DefaultHttpRequest)
    activate HttpObjectAggregator
    HttpObjectAggregator->>HttpObjectAggregator:decode()
    deactivate HttpObjectAggregator
    HttpServerCodec->>HttpObjectAggregator:fireChannelRead(EmptyLastHttpContent)
    activate HttpObjectAggregator
    HttpObjectAggregator->>HttpObjectAggregator:decode()
    HttpObjectAggregator->> HttpRequestHandler:fireChannelRead(FullHttpRequest)       
    deactivate HttpObjectAggregator
    deactivate HttpServerCodec
    deactivate HeadContext
    deactivate DefaultChannelPipeline      
```

出站：

```mermaid
sequenceDiagram
    participant DefaultChannelHandlerContext
    participant HttpServerCodec
    participant HeadContext
    participant NioSocketChannelUnsafe

    activate DefaultChannelHandlerContext
    DefaultChannelHandlerContext ->>HttpServerCodec:write(FullHttpResponse)
    activate HttpServerCodec
    HttpServerCodec ->> HttpServerCodec:decode()
    HttpServerCodec ->>HeadContext:write(ctx,byteBuf,promise)
    deactivate HttpServerCodec
    activate HeadContext
    HeadContext ->> NioSocketChannelUnsafe:write(msg,promise)
    deactivate HeadContext
    deactivate DefaultChannelHandlerContext
```





### 15、cfr 反编译工具

官方[文档](https://github.com/leibnitz27/cfr)，下载 [cfr-0.152.jar](https://www.benf.org/other/cfr/cfr-0.152.jar) 到本地，用法： 

```
java -jar cfr-0.152.jar pinpoint-spring-web-plugin-1.7.0-SNAPSHOT.jar --outputdir pinpoint-spring-web-plugin
```

### 16、brew慢的问题

国内,使用brew极慢. 因为它需要访问国外的一些服务器.

解决方法是使用国内的镜像站.

- 如果是首次安装:

```text
curl https://raw.githubusercontent.com/Homebrew/install/master/install.sh  > install-brew.sh
```

然后,在下载的文件中, 修改BREW_REPO为:

```text
BREW_REPO="https://mirrors.ustc.edu.cn/brew.git"
```

最后, 运行:

```text
HOMEBREW_CORE_GIT_REMOTE=https://mirrors.ustc.edu.cn/homebrew-core.git bash install-brew.sh
```

- 如果是已经安装了brew, 可以这样替换镜像站:

```text
cd "$(brew --repo)"
git remote set-url origin https://mirrors.ustc.edu.cn/brew.git

echo 'export HOMEBREW_BOTTLE_DOMAIN=https://mirrors.ustc.edu.cn/homebrew-bottles' >> ~/.bash_profile
source ~/.bash_profile

cd "$(brew --repo)/Library/Taps/homebrew/homebrew-core"
git remote set-url origin https://mirrors.ustc.edu.cn/homebrew-core.git
```

替换后, 就可以正常使用brew了.

## 系统设置

### mkdir: /work: Read-only file system 问题

进入恢复模式(如何操作可以百度下)，执行：

```
csrutil disable
```

编辑 /etc/synthetic.conf

```
work	/System/Volumes/Data/work
data	/System/Volumes/Data/data
```

重启电脑执行

```
sudo mkdir /System/Volumes/Data/data
sudo mkdir /System/Volumes/Data/work
sudo chown bairen:staff /System/Volumes/Data/data
udo chown bairen:staff /System/Volumes/Data/work
```

### 打印机配置

<img src="http://git.caimi-inc.com/wac-base/jasmine/uploads/f6a06022e6e2a5a8d7f4362c68b15472/image.png" alt="image" style="zoom:40%;" />

### excel中文乱码

首先要确认通过文本打开没有乱码，dms可以可以换个浏览器下载，然后excel选择【数据】-> 【从文本】 utf-8格式

### 空间不足

正常情况空间占用97.6G：

```
 17G	/Applications
8.1G	/Library
9.8G	/System
 20G	/Users
2.8G	/cores
3.6G	/data
7.3G	/private
2.9G	/usr
1.0G	/vm
 24G	/work
```

清理目录可以使用omnidisksweeper工具，常见case:

- 遇到过一次/private/tmp目录下占用83G的情况

-  检查 /Users/<USER>/Library/Containers、/Users/<USER>/Library/Caches 目录，清理微信、企业微信、飞书、chrome缓存：

  ```
   du -sh /Users/<USER>/Library/ | grep G
  ```

  

注意：删除之后需要重启系统才会释放空间

### 从USB安装系统

Reference：

- [Create a bootable installer for macOS](https://support.apple.com/en-hk/HT201372)

 Install step:

1、Download High Sierra。 [Open the Mac App Store link](https://go.redirectingat.com/?id=111346X1569486&url=https://apps.apple.com/us/app/macos-high-sierra/id1246284741?ls=1&mt=12&xcust=1-1-671911-1-0-0&sref=https://www.macworld.com/article/671911/how-to-get-old-macos-download-big-sur-catalina-mojave-and-more.html) using Safari 。

> 注意不要直接通过浏览器下载安装包，无法安装的，只能从App Store下载，成功之后在应用程序会有一个《安装 macOS High Sierra》

2、Create the bootable installer

use this command:

```
sudo /Applications/Install\ macOS\ High\ Sierra.app/Contents/Resources/createinstallmedia --volume /Volumes/MyVolume
```



## M1 的坑

###  1、grcp报错：

报错：

```properties
[ERROR] Failed to execute goal org.xolstice.maven.plugins:protobuf-maven-plugin:0.6.1:compile-custom (grpc-build) on project java-agent-network: protoc did not exit cleanly. Review output for more information. -> [Help 1]
```

首先要知道 protobuf-maven-plugin 这个插件是干嘛的。 它是**负责把 protobuf文件编译为java代码**，需要 protobuf 命令支持。

注意这个只是maven抛出来的错误，实际错误需要往前翻：

```properties
[INFO] Compiling 23 proto file(s) to /System/Volumes/Data/work/dist/branch/opensource/skywalking-java/apm-protocol/apm-network/target/generated-sources/protobuf/grpc-java
[ERROR] PROTOC FAILED: /System/Volumes/Data/work/dist/branch/opensource/skywalking-java/apm-protocol/apm-network/target/protoc-plugins/protoc-gen-grpc-java-1.53.0-osx-aarch_64.exe: program not found or is not executable
Please specify a program using absolute path or make sure the program is available in your PATH system variable
--grpc-java_out: protoc-gen-grpc-java: Plugin failed with status code 1.
```

这个原因本质还是 /protoc-gen-grpc-java-1.53.0-osx-aarch_64.exe 执行失败了，我们可以尝试执行会报错：

```
bad CPU type in executable
```

参考了这两篇文章：

- [grpc-java-7690-protoc-gen-grpc-java not available on apple m1](https://github.com/grpc/grpc-java/issues/7690)
- [protobuf-8062-macOS Apple M1/Arm Support ](https://github.com/protocolbuffers/protobuf/issues/8062)

根本原因还是**protobuf没有原生支持m1芯片，目前只支持 Rosetta2 转译方式**。

安装Rosetta：

```properties
softwareupdate --install-rosetta
```

使用 protobuf 高版本，完整配置如下：

```xml
   <com.google.protobuf.protoc.version>3.17.3</com.google.protobuf.protoc.version>
   <protoc-gen-grpc-java.plugin.version>1.44.0</protoc-gen-grpc-java.plugin.version>

	<plugin>
        <groupId>org.xolstice.maven.plugins</groupId>
        <artifactId>protobuf-maven-plugin</artifactId>
        <version>${protobuf-maven-plugin.version}</version>
        <configuration>
            <protocArtifact>
                com.google.protobuf:protoc:${com.google.protobuf.protoc.version}:exe:${os.detected.classifier}
            </protocArtifact>
            <pluginId>grpc-java</pluginId>
            <pluginArtifact>
                io.grpc:protoc-gen-grpc-java:${protoc-gen-grpc-java.plugin.version}:exe:${os.detected.classifier}
            </pluginArtifact>
        </configuration>
        <executions>
            <execution>
                <id>grpc-build</id>
                <goals>
                    <goal>compile</goal>
                    <goal>compile-custom</goal>
                </goals>
            </execution>
        </executions>
    </plugin>
```

对于 protobuf 低版本需要强制设置为osx-x86_64

```
com.google.protobuf:protoc:${com.google.protobuf.protoc.version}:exe:osx-x86_64
```

[protoc 3.21.1](https://repo1.maven.org/maven2/com/google/protobuf/protoc/3.21.1/)版本后不需要该操作，因为[pull/8557](https://github.com/protocolbuffers/protobuf/pull/8557)发布了一个mac-aarch64的wrapper，实际用的还是osx-x86_64 
