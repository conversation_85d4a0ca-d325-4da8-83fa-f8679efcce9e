## 概述

Git 最初是2005年 Linus Torvalds 的一个为[期一周](https://lkml.org/lkml/2005/4/6/121)的项目，旨在替换他们当时用来管理对 Linux 内核的贡献的旧版本控制系统，接下来 git 改变了整个程序界。

## 使用

### 1、repository

git 可以轻易把一个目录设置为 repository ，执行:

```
mkdir temp
cd temp
git init
```

输出:

```
Initialized empty Git repository in /work/dist/branch/wacai/middleware/temp/.git/
```

### 2、branch

#### 新建本地分支

```
git co -b branche_name
```

#### 删除分支

删除本地分支

```
git br -D feature-modify-login   
```

删除远程分支

```
git push origin --delete feature-modify-login
```

#### 切换远程分支

```
git co -t remotes/origin/feature/branche_name
```
切记带上参数t，否则会报错：You are in 'detached HEAD' state.

#### 提交

```
git ci -a
```
#### 推送

```
git push --set-upstream origin v1.2.9-snapshot
```
#### 合并

```
git merge --no-commit 20181109-fixbug
```

如果不带 -no-commit 会自动提交

#### 回滚

回滚到指定版本号
```
git reset --hard 091823bb3ebbb7deabe04bb946fec37ddb2ef428
```

回滚到上个版本
```
git reset --hard HEAD~1
```
#### push

push的时候必须带上参数 -f

```
git push -f
```
注意:push的可能会失败，gitlab可以对分支开启Protected branches。

##查看记录

```
git log --oneline
```

#### rebase

同一个分支，如果我本地和其他人同时都commit，就算是不同的代码，如果直接pull，git会自动产生一个merge commit。可以使用

```
git pull --rebase
```

来避免产生merge commit



####  squash commit

Squash commit 翻译为压缩提交，如果每次提交都在master分支保留一次记录会导致master的历史很乱，通过rebase 可以合并多次提交。

比如我们要把最近 5 条commit合并为一条：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220506181618246.png" alt="image-20220506181618246" style="zoom:60%;" />

```
git rebase -i  HEAD~5
```

操作之后会出现如下交互界面

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220506181731425.png" alt="image-20220506181731425" style="zoom:60%;" />

 

通过指令要选择需要合并那些提交，注释已经解释了指令的含义。

这里我选择使用最开始一次提交，合并后面的，指令如下：

```
pick 3a4023c add OPT
s 12dec3f otp使用挖财版本
s 5263fb9 otp增加降级
s b8d1c43 otp 降级
s 1a64497 opt 降级
```

保存之后提示输入注释，输出：

```
➜  wsso git:(master) git rebase -i  HEAD~5
[detached HEAD b5abf21] Add opt
 Date: Fri Apr 29 16:49:03 2022 +0800
 10 files changed, 71 insertions(+), 94 deletions(-)
Successfully rebased and updated refs/heads/master.
```



再次查看log会看到被合并为一条commit：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220506182631791.png" alt="image-20220506182631791" style="zoom:60%;" />

最后 push 需要强制

```
git push --force
```

如果填错了会中断，可以通过如下命令恢复:

```
git rebase --edit-todo
```

也可以通过如下命令中断rebase

```
git rebase --abort
```

#### Commit message 规范

参考：[Commit message 和 Change log 编写指南](https://www.ruanyifeng.com/blog/2016/01/commit_message_change_log.html)

#### merge&rebase

log中经常会出现这种commit

```
    Merge remote-tracking branch 'origin/master'
```

如何避免这种情况的发生呢？

参考：

- https://stackoverflow.com/questions/6406762/why-am-i-merging-remote-tracking-branch-origin-develop-into-develop
- https://youtrack.jetbrains.com/issue/IDEA-279984/How-to-avoid-git-message-Merge-remote-tracking-branch-xxx-xxxx-into-xxx-using-IntelliJ-IDEA

产生原因是当使用git pull时，git内部对应了git fetch和git merge两个动作，前者将远程仓库拉取到本地仓库，后者将本地仓库与分支进行merge，git在合并时会记录merge信息，如何避免呢？最简单的方式是使用

```
git pull --rebase
```

merge和rebase的区别

现在我们有这样的两个分支,test和master，提交如下：

```
       D---E test
      /
 A---B---C---F--- master
```

在master执行git merge test,然后会得到如下结果：

```
       D--------E
      /          \
 A---B---C---F----G---   test, master
```

可以看到G会有两个parent分别对应 E和F

在master执行git rebase test，然后得到如下结果：

```
A---B---D---E---C‘---F‘---   test, master
```

merge操作会生成一个新的节点，之前的提交分开显示。
而rebase操作不会生成新的节点，是将两个分支融合成一个线性的提交。

rebase如何处理冲突？

和merge是类似的，解决冲突后，通过git add 标记，执行git rebase –continue继续操作，或者git rebase –skip忽略冲突。

### 3、tag

#### 新增tag

```
git tag                     //查看
git tag -a v1.2.9           //新增
git push origin v1.2.9      //推送
git tag -d v1.2.9    //删除tag
git push origin :refs/tags/v1.2.9  //删除远程tag
```

#### 切换到某个tag

git clone 整个仓库后使用，以下命令就可以取得该 tag 对应的代码了。 

```
git checkout tag_name 
```

但是，这时候 git 可能会提示你当前处于一个“detached HEAD" 状态。

因为 tag 相当于是一个快照，是不能更改它的代码的。

如果要在 tag 代码的基础上做修改，你需要一个分支： 

```
git checkout -b branch_name tag_name
```

这样会从 tag 创建一个分支，然后就和普通的 git 操作一样了。

## 配置

### cofnig

```
git config user.name jiangyunpeng
git config user.email <EMAIL>
```

### gitconfig

可以通过 usr.dir/.gitconfig 文件中配置全局git

```
# This is Git's per-user configuration file.
[user]
# Please adapt and uncomment the following lines:
#	name = bairen
#	email = <EMAIL>
[user]
	name = bairen
	email = <EMAIL>

[alias]
        last = log -1 HEAD
        co = checkout
        br = branch
        ci = commit
        st = status
[filesystem "Oracle Corporation|1.8.0_121|/dev/disk1s1"]
	timestampResolution = 1001 milliseconds
	minRacyThreshold = 0 nanoseconds

[pull]
	rebase = false
	
[oh-my-zsh]
	hide-dirty = 1
	hide-status = 1
	
[core]
	quotepath = false	
	
[branch]
	autosetuprebase = always	
```

配置说明：

- oh-my-zsh 解决 zsh 出现卡顿
-  quotepath 解决文件名中文乱码
-  autosetuprebase pull分支的时候自动rebase，消灭 merge branch

### zsh

[ohmyzsh](https://github.com/ohmyzsh/ohmyzsh) 提供了git plugin，详见[文档](https://github.com/ohmyzsh/ohmyzsh/tree/master/plugins/git)。

常用如下：

| Alias | Command       |
| ----- | ------------- |
| g     | git           |
| ga    | git add       |
| gb    | git branch    |
| gbD   | git branch -D |

## Git message

### 1、 add sth. to sth.

[add sth. to sth](https://www.doubao.com/chat/9476264788592898).（将某物添加到某对象），是固定搭配，也是最常用的句式

- feat(history): add background highlight to selected history item
- refactor(model):  add new 'tool' role and context metadata to ChatMessage
- feat(block/text): add file hyperlink support to text blocks
- add dark mode support to the app

在技术文档中，add X to Y 通常译为：
- 给 Y 增加 X 功能
- 为 Y 添加 X 支持
- 向 Y 中加入 X



### 2、modify sth. to support 动名词.

- Modify CodeFence to support streaming XML parsing.

动名词结构更强调 “能力” 或 “方式”：
streaming parsing 直接描述了一种解析方式（流式解析），而 parse in a stream 更像是一个动作指令（在流中解析）。
例如：
- ✅ This library supports streaming parsing（库的能力）
- ❌ This library supports parse in a stream（语法错误）



### 3、refactor sth to integrate A into B 

还有拆分：separate A from B

- Refactor Dialogue initiate() to integrate user environment into user prompt because we only allow one user prompt in the dialogue messages
- Refactor OpenAILLMProvider to separate system prompt from PromptMessage 



### 4、 restrict sth. to  sth. 

restrict sth. to sth.（将某物限制到某范围），是固定搭配。

- refactor(listener): Restrict context attachment to intentional multi-line selections
- Restrict access to the system



### 5、add sth. to correct sth.

- fix(history): add EVENT Role to correct recovering history dialogue in ChatPanel



### 6、replace A with B

- refactor(CollapsiblePanel.java): replace custorm border color with ColorUtil.getBorderColor()
- refactor(model):Replace ChatMessage with PromptMessage in LLMProvider   



### 7、remove/udpate sth. to avoid sth.

- fix(InputEditor): remove trailing '\n' when pressing Enter to avoid unexpected line breaks



### 8、 add sth. for sth. in sth.

- fix(ResponseView): add null check for responseView in taskFinish()
- feat(block): add streaming xml parsing support for block 
- add syntax highlighting for Python files



### 9、fix sth. when ...

- fix(Task): Fix task canceling when task is already completed



### 10、avoid sth. in sth. during sth.

- fix(history): avoid showing loading indicators in code blocks during history recovery

11、 when + 动名词
有时候会看到when 直接加动名词，没有主语，比如 remove trailing '\n' when pressing Enter
这里 when 后面直接动名词，本质上是状语从句的省略结构，其正确性建立在 “逻辑主语与主句主语一致” 的前提下
比如 Close the file when finishing writing”（完成写入时关闭文件）
完整形式：when you/it are/is finishing writing，此处 “you” 指开发者，“it” 指程序，根据语境可明确逻辑主语，所以省略



## 原理

Git也是一个大部分人都知道如何去使用它，知道有哪些命令，却只有少部分人知道具体原理的东西。

git 是如何管理文件的？当执行 git commit 内部发生了什么？带着这些疑问我 google了:" how git work? how git store file?"

### .git目录

git 的秘密都藏在.git目录中，当我们在git提交了一些文件

```
echo 'AAAAAAAAAAAAAAAAA' > a
git add .
git ci -
```

这次提交产生的信息会被保存在.git目录下。

```properties
 find .git/objects -type f
.git/objects/26/9ea4f54781259bf58480fd49d01e2b138b1df2
.git/objects/91/acbb80201b8ada7dbbd7be00f5fae1a811b3b5
.git/objects/6c/335d52e415b8ca496d63818006e8950464e288
.git/objects/4b/825dc642cb6eb9a060e54bf8d69288fbee4904
```

这里产生了 4 个 object，我们可以通过 `git cat-file` 来查看文件内容，第1个文件保存的是a文件的内容：

```properties
git cat-file -p 269ea4f54781259bf58480fd49d01e2b138b1df2
AAAAAAAAAAAAAAAAA
```

第2个文件保存的是commit提交信息：

```properties
git cat-file -p 91acbb80201b8ada7dbbd7be00f5fae1a811b3b5
tree 6c335d52e415b8ca496d63818006e8950464e288
author bairen <<EMAIL>> 1652272097 +0800
committer bairen <<EMAIL>> 1652272097 +0800

add a
```

注意在提交信息中还有一个tree-6c335，也就是第3个文件，表示我们提交的当前目录信息：

```properties
git cat-file -p 6c335d52e415b8ca496d63818006e8950464e288
100644 blob 269ea4f54781259bf58480fd49d01e2b138b1df2	a
```

第4 个文件 4b825 内容为空，应该是项目初始化时的的hash。

我们再做一次提交，这次我们修改文件a的内容，再增加文件b

```properties
 echo 'AAAAAAAAAAAAAAAAAaaaaaaaaaaaaa' > a
 echo 'BBBBBBBBBBBBB' > b
```

会发现git会创建更多的object对象. 

### object对象

在 .git/objects/目录下保存的object对象包括三种：

- commit: 记录一次提交信息；
- Trees：记录文件目录，包括目录包含的文件名，hash。子目录也通过tree表示。
- Blobs：记录文件内容。注意不包括文件名；

可以通过 git rev-parse 来查看某个文件在某次commit下的 hash

```properties
# 查看root tree的hash
git rev-parse HEAD^{tree}		
841375b8a06ec1c2da8cb40b16ec4e73582f0b0e

# 查看a文件的hash
git rev-parse HEAD:a				
a1542326dccda686d83d14837dfe72cb216d9c36

#通过hash反向查看内容
git cat-file -p 841375b8a06ec1c2da8cb40b16ec4e73582f0b0e 	
100644 blob a1542326dccda686d83d14837dfe72cb216d9c36	a
100644 blob 8275495284ab33f6e6ca4f62d361977bf5136a7b	b
040000 tree 2e3a6260f32088fd75ec78a554f31da4c48a7b1c	src
```

### snapshot commit

git 每次 commit 都会带一个根 tree，比如我们查看wsso某次提交的信息:

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511210857538.png" alt="image-20220511210857538" style="zoom:50%;" />

查看tree的内容会发现git每次commit都会把 **工作区root目录**下所有文件的hash值都记录下来，我们再来看下一次提交的内容：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511211234717.png" alt="image-20220511211234717" style="zoom:50%;" />

对比tree文件内容会发现，第二次提交.gitignore的hash发生了变化，同时有几个文件删除了，而src目录，pom.xml并没有变化。

这里需要注意一点，commit中记录的tree对象一定是工作区root目录，就算我们提交一个子目录也是这样的，可以自行验证。

所以git commit 本质会把整个工作区的快照作为[Merkle tree](https://en.wikipedia.org/wiki/Merkle_tree)全部提交，只是如果文件内容没有变化的话直接提交历史的hash，如果文件发生了修改，会产生一个新副本提交，老文件继续保留。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511213056634.png" alt="image-20220511213056634" style="zoom:50%;" />

> 图示：红色箭头是我加的，每个commit之间有link 的，commit顺序是从上到下。

上图第二次提交中 csv文件没有变化，所以tree文件直接记录历史的hash，report.md文件发生了修改，所以会记录一个新的object以及它的hash。

git这种方式是和svn很大的区别，svn提交的是diff数据，git是全量数据，那么git这种方式有何优势？

答案就是 Git 这种方式大大减轻了ci所需要的时间，因为ci不需要计算diff。并且Git 可以比较任意两个commit，而不仅仅是相邻的commit。

不过 git 这种每次修改文件就新建副本的方式不是会很浪费磁盘空间吗？确实会，所以git提供了git gc 命令，它会把这种稀疏的object合并成pack文件，保存在 .git/objects/pack/ 目录

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511212833792.png" alt="image-20220511212833792" style="zoom:50%;" />

Packfiles 文件的介绍可以参考[Git-Internals-Packfiles](https://git-scm.com/book/en/v2/Git-Internals-Packfiles)

### branch

别忘了git还有分支，branch在 .git/refs/heads 目录中：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511213647386.png" alt="image-20220511213647386" style="zoom:50%;" />

这个目录中记录 master 和 feature-integrate-k2 两个分支指向的commit地址。

.git/HEAD 文件引用当前分支：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220515110902216.png" alt="image-20220515110902216" style="zoom:50%;" />

当我们从 feature-integrate-k2 切换到 master分支，.git/HEAD 也会从 master 修改为 feature-integrate-k2

所以branch 在 git 中只通过一个简单的指针就实现了，非常简单和优雅。

在下图中圆形表示commit，每个commit都指向了它的parent commit和一个对应的root tree，切换分支就是修改HEAD指向commit的位置。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511214136451.png" alt="image-20220511214136451" style="zoom:50%;" />

### merge

git merge也很好理解了，只要一个 commit 有多个parent就说明他是一次merge commit，比如：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511214609086.png" alt="image-20220511214609086" style="zoom:50%;" />

在git中是这样记录的：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511215158106.png" alt="image-20220511215158106" style="zoom:50%;" />

### diff 

由于 Git commit内容是以为Merkle tree形式记录的，那么要比较两个commit之间的差异就很容易了，直接递归比较每个节点的hash就能高效的找到差异。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220511215402045.png" alt="image-20220511215402045" style="zoom:50%;" />

###  status

可以通过 git rev-parse 来查看某个文件在某次commit下的 hash，比如下面我打印了a文件在不同commit下的hash:

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220513194347446.png" alt="image-20220513194347446" style="zoom:70%;" />

当执行 git status 时，git会计算出该文件的hash，当发现和HEAD中hash不一致则会提醒该文件发生了修改, 对于tree目录也是同样的原理。

### 参考

- [stackoverflow How does git store files](https://stackoverflow.com/questions/8198105/how-does-git-store-files)
- [stackoverflow What-is-a-git-snapshot](https://stackoverflow.com/questions/4964099/what-is-a-git-snapshot)
- [How Does Git Actually Work?](https://www.raywenderlich.com/books/advanced-git/v1.0/chapters/1-how-does-git-actually-work)
- [Understanding Git: Data Structure and Code Recovery](https://inpsyde.com/en/understanding-git/)
- [GitHub‘s blog：Commits are snapshots, not diffs](https://github.blog/2020-12-17-commits-are-snapshots-not-diffs/)
- [git is simpler than you think](https://nfarina.com/post/9868516270/git-is-simpler)
- [ Git 到 Fuse Engine 存储引擎](https://bohutang.me/2022/05/06/databend-cloud-warehouse-fuse-engine/#Git-%E5%B7%A5%E4%BD%9C%E6%9C%BA%E5%88%B6)
- [git pull和fetch有什么区别](https://stackoverflow.com/questions/292357/what-is-the-difference-between-git-pull-and-git-fetch)

