## 概述

最近有人反馈测试环境可以通过  zktestserver1.wacai.info:2181 访问，但实际我们zk只启动了22181，通过运维发现是基于iptable端口转发实现的。

本文总结一下iptable

## 工作原理

iptables 是一个用户空间实用程序，用于设置和管理 Linux 内核中的包过滤规则。它是 Linux 防火墙功能的核心，基于 Netfilter 框架，这是一个内核模块，决定允许哪些数据包进入或出去。以下是 iptables 的基本思想和工作原理：



<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-*****************.png" alt="image-*****************" style="zoom:50%;" />



`iptables` 由不同的组件组成，如下所述：

**链 (Chains)**：

`iptables` 中有5个链，如上图所示，每个链负责特定的任务，包括：

- `PREROUTING`：在路由决策前处理入站包（用于 `nat` 表）。该链决定数据包到达网络接口后立即发生的情况。我们有不同的选择，例如更改数据包（可能用于 NAT）、丢弃数据包
- `INPUT`：处理流入本地系统的包。如果您想打开/阻止端口，可以在此处执行此操作。
- `FORWARD`：处理转发到其他主机的包。 限制出站流量的最佳位置。

- `OUTPUT`：处理从本地系统发出的包。
- `POSTROUTING`：在路由决策后处理出站包（用于 `nat` 表）。 

大多数案例都是 INPUT 和 OUTPUT 链

**表 (Tables)**：

不同的表负责不同的任务。该列表包含**filter、nat**、mangle、raw 和 security。前两个是最常用的。filter负责过滤和限制进出我们计算机的数据包。 Nat 负责网络地址转换。

- `filter`：默认表，包含用于包过滤的规则（允许或拒绝包）。
- `nat`：用于网络地址转换（如端口转发）。
- `mangle`：用于包修改（如修改 TOS 字段）。
- `raw`：用于配置包的原始处理。

**目标 (Targets)**：

规则匹配后执行的操作。主要目标包括：

- `ACCEPT`：允许包通过。
- `DROP`：丢弃包，不给任何响应。
- `REJECT`：拒绝包，并发送响应。
- `REDIRECT`：重定向包到本地机器的其他端口（用于 `nat` 表）。
- `MASQUERADE`：伪装包（用于 `nat` 表）。
- `LOG`：记录包信息到系统日志。



##  如何使用

以下是将 8888 端口的流量转发到 8080 端口的规则：

```
iptables -t nat -A PREROUTING -p tcp --dport 8081 -j REDIRECT --to-port 8080
```

详细解释：

```
-t nat: 指定操作的表是 NAT 表。NAT 表用于网络地址转换（如端口转发和伪装）
-A PREROUTING: 在 PREROUTING 链中追加（-A 表示 append）规则。PREROUTING 链在数据包进入防火墙时处理，用于在路由决策之前对入站包进行操作。
-p tcp: 指定匹配的协议是 TCP。仅对 TCP 数据包应用此规则。
--dport 8081: 匹配目标端口为 8081 的数据包。也就是说，这条规则只适用于目标端口是 8081 的 TCP 数据包。
-j REDIRECT: 指定匹配到的数据包的处理动作（目标）。REDIRECT 表示将数据包重定向到本地机器的另一个端口。
--to-port 8080: 将匹配到的数据包重定向到本地机器的 8080 端口。也就是说，所有目标端口为 8081 的数据包会被重定向到 8080 端口。
```



查看 iptables 规则

```
iptables -t nat -L -n -v
```



## 常用功能

### 模拟丢包

```bash
##proxyIptables.sh
#! /bin/sh
 
# 模拟客户端到xgraph-proxy的网络丢包10%

iptables -I INPUT -s ************** -m statistic --mode random --probability 0.1 -j DROP 

# 模拟xgraph-proxy到hermes-proxy丢包10%
# 以下ip是测试环境hermes的proxy部署虚拟机ip

iptables -I OUTPUT -d ************* -m statistic --mode random --probability 0.1 -j DROP
iptables -I OUTPUT -d ************ -m statistic --mode random --probability 0.1 -j DROP
 
# 模拟xgraph-proxy消费hermes丢包10%
# 以下ip是测试环境hermes的bridge部署虚拟机ip

iptables -I INPUT -s *********** -m statistic --mode random --probability 0.1 -j DROP
iptables -I INPUT -s ************* -m statistic --mode random --probability 0.1 -j DROP 
iptables -I INPUT -s ************* -m statistic --mode random --probability 0.1 -j DROP
iptables -I INPUT -s ************ -m statistic --mode random --probability 0.1 -j DROP
iptables -I INPUT -s ************ -m statistic --mode random --probability 0.1 -j DROP
```



## 参考

- [what-is-iptables-and-how-to-use-it](https://medium.com/skilluped/what-is-iptables-and-how-to-use-it-781818422e52)
