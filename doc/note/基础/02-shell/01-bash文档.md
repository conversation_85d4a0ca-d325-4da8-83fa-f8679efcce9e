## sort

You can use the following options in conjunction with the raw command to modify how the values are sorted.

- **-n –** sorts in numerical values.
- **-h –** compares human-readable numbers such as 1k, 1G
- **-R –** sort in random order but group the identical keys.
- **-r –** sort the values in reverse (descending order).
- **-o –** save ouput to a file
- **-c –** check if the input file is sorted; do not sort if true.
- **-u –** show unique values only.
- **-k –** sort the data via a specific key (useful when sorting columnar data).

例子：

```
sort -nrk 2
```

参数表示：按照数字类型排序，倒序，第二列



## 统计topN

```
/duitang/logs/usr/www.duitang.nginx.access.log | awk '{print $1}'|sort |uniq -c|sort -nr | head -n 10
```

## tar

开启压缩:

```
 tar -zcvf heap.tgz heap.bin 
```

## Run-java-sh
通用启动 java 应用程序脚本：[run-java-sh](https://github.com/fabric8io-images/run-java-sh)

### 启动脚本

下面是一个最简单的启动脚本

```bash
#!/bin/sh
check(){

	local proc_id=$(ps aux | grep "runner" | grep -v "grep" | awk '{print $2}')
	echo "$proc_id"
	if [ -n "$proc_id" ]; then
		echo "wse_runner is running! pid=${proc_id} " && exit 1
	fi
}
config(){
	local current_dir=`pwd`
	echo $current_dir
	rm -rf "target"
	tar -xvf "$current_dir/wse-runner.tgz"
	cp "test.yaml" "./target/config/clusters/"
}

start(){
  echo "[INFO] start wse-runner ..."
  local current_dir=`pwd`
  cd "target"
  nohup "./bin/runner" &
}
check
config
start
```



## 上传文件

```shell
#!/bin/bash
function cf_wac_pushf() {
  local server_url="http://uav.wacai.info/api/v2/file/upload"

  if [ $# -ne 1 ]; then
    echo "Just need file path, please." && return 1
  fi

  if [ ! -f "$1" ]; then
    echo "$1 Not a file." && return 1
  fi

  local url_s symlink
  url_s=$(curl -X POST -F file=@"$1" --progress-bar $server_url)
  symlink=$(echo "$url_s" | awk -F/ '{print $NF}')
  echo "该文件会保存7天，可以通过如下两种方式下载："
  echo "服务器的命令行: cf_wac_pullf $symlink"
  echo "浏览器下载: $url_s"
}
cf_wac_pushf $1
```

下载 

```shell
#!/bin/bash

# 2018.06.06

server_url="http://uav.wacai.info"

if [ $# -ne 1 ]
then
    echo "file code, please."
fi

pull(){
    url=$server_url/api/v2/file/s/$1
    download_url=$( curl -s $url )
    filename=$(curl -sI  $download_url | grep -o -E 'filename=.*$' | sed -e 's/filename=//' | tr -d '\r')
    curl -sS -o $filename  $download_url
    echo "save to "$filename
}

 
```



## netstat 

```
netstat -n | awk  '/^tcp/ {++S[$NF]} END {for(a in S) print a, S[a]}'   
```

输出：

```
TIME_WAIT 814
CLOSE_WAIT 1
FIN_WAIT1 1
ESTABLISHED 634
SYN_RECV 2
LAST_ACK 1
```



## 参考

- [shell编程的若干实用技巧](https://zhuanlan.zhihu.com/p/46100771)
- [100-bash](https://github.com/jiangyunpeng/bash)
