## iostat

> 参考：
>
> - [How to Monitor Disk IO in a Linux System](https://www.baeldung.com/linux/monitor-disk-io)
> - [Two traps in iostat: %util and svctm](https://brooker.co.za/blog/2014/07/04/iostat-pct.html)
> - [IOSTAT中关于%util，svctm存在的陷阱及解决办法](https://blog.csdn.net/dbcrocodile/article/details/47318181)

iostat 主要用于监控系统设备的IO负载情况，iostat首次运行时显示自系统启动开始的各项统计信息，之后运行iostat将显示自上次运行该命令以后的统计信息。用户可以通过指定统计的次数和时间来获得所需的统计信息。

使用说明：

```
    -C 显示CPU使用情况
    -d 显示磁盘使用情况
    -k 以 KB 为单位显示
    -m 以 M 为单位显示
    -N 显示磁盘阵列(LVM) 信息
    -n 显示NFS 使用情况
    -p[磁盘] 显示磁盘和分区的情况
    -t 显示终端和CPU的信息
    -x 显示详细信息
    -V 显示版本信息
```

为显示更详细的io设备统计信息，我们可以使用-x选项，在分析io瓶颈时，一般都会开启-x选项，重点关注util%数值：

![wecom-temp-1ea4cd522a5637984129321b7d81b2cd](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/wecom-temp-1ea4cd522a5637984129321b7d81b2cd.jpg)





```
 iostat -mdx 3
 
Device:         rrqm/s   wrqm/s     r/s     w/s    rMB/s    wMB/s avgrq-sz avgqu-sz   await r_await w_await  svctm  %util
fioa              0.00     0.00    2.33  435.67     0.02    65.56   306.62     0.00   11.90    0.29   11.96   0.00   0.00
sda               0.00     1.00    0.00    0.67     0.00     0.01    20.00     0.00    6.50    0.00    6.50   6.50   0.43
sdb               1.00     8.00    9.67  179.67     0.37    16.63   183.87   109.04  657.99   52.31  690.58   5.28 100.00
```

上面是一台ES，使用ssd，轻松支持每秒几十mb的写入

以上各列的含义如下：

- **rrqm/s**: 每秒对该设备的读请求被合并次数，文件系统会对读取同块(block)的请求进行合并
- **wrqm/s**: 每秒对该设备的写请求被合并次数
- **r/s**: 每秒完成的读次数
- **w/s**: 每秒完成的写次数
- **rkB/s**: 每秒读数据量(kB为单位)
- **wkB/s**: 每秒写数据量(kB为单位)
- **avgrq-sz**:平均每次IO操作的数据量(扇区数为单位)
- **avgqu-sz**: 平均等待处理的IO请求队列长度
- **await**: 平均每次IO请求等待时间(包括等待时间和处理时间，毫秒为单位)
- **svctm**: 平均每次IO请求的处理时间(毫秒为单位)
- **%util**: 采用周期内用于IO操作的时间比率，即IO队列非空的时间比率





## strace

发现某个进程会不断请求zk的选举端口，导致zk不停的打印错误日志，由于socket会被zk快速close找不到对应的pid，那么如何找出这个pid呢?

```
$ netstat -antp | grep "WAIT" | grep 127.0
(Not all processes could be identified, non-owned process info
 will not be shown, you would have to be root to see it all.)
tcp        0      0 127.0.0.1:28175             127.0.0.1:8081              TIME_WAIT   -
tcp        0      0 127.0.0.1:38199             127.0.0.1:7003              TIME_WAIT   -
tcp        0      0 127.0.0.1:2181              127.0.0.1:19076             TIME_WAIT   -
```

首先可以查看系统中的可疑对象，通过ps 可以发现下面两个进程最可疑：

![image-20210508193757096](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210508193757096.png)

这里可以使用 strace 这个命令来查看8793进程在干什么？

```
strace -f -p 8793 -e connect
```

等待一段时间后会输出：

![image-20210508193921964](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210508193921964.png)

就是 wsm-agent 在扫描本机监听端口

## perf

在网上看到使用 perf 命令定位dubbo性能问题，所以想尝试用下 linux perf 。

遇到的问题

在 docker 中安装好 perf 准备执行，结果遇到一个问题，直接报错：

```
 ┌─Error:────────────────────────────────────────────────┐
 │You may not have permission to collect stats.          │
 │Consider tweaking /proc/sys/kernel/perf_event_paranoid:│
 │ -1 - Not paranoid at all                              │
 │  0 - Disallow raw tracepoint access for unpriv        │
 │  1 - Disallow cpu events for unpriv                   │
 │  2 - Disallow kernel profiling for unpriv             │
 │                                                       │
 │                                                       │
 │Press any key...                                       │
 └───────────────────────────────────────────────────────┘
```

搜索：'running `perf` in k8s' 找到[一篇文章](https://blog.alicegoldfuss.com/enabling-perf-in-kubernetes/) 对此问题进行了说明，大意是需要开启 linux CAP_SYS_ADMIN FLAG,这个flag是[Linux capabilities](http://man7.org/linux/man-pages/man7/capabilities.7.html)中的一个，如果在docker中需要执行以下命令来开启

```
docker run --cap-add SYS_ADMIN
```

在k8s中可以通过以下配置激活：

```
securityContext:
    capabilities:
        add: ["SYS_ADMIN"]
```

参考

- [通过linux perf定位dubbo问题](https://lishoubo.github.io/2017/08/09/%E9%97%AE%E9%A2%98%E6%8E%92%E6%9F%A5%E4%B8%80%E6%AC%A1%E8%AF%A1%E5%BC%82%E7%9A%84rt%E6%8B%89%E9%AB%98%E7%8E%B0%E8%B1%A1/)
- [记一次perf 定位 docker问题](https://www.cnblogs.com/276815076/p/8961160.html)

## 网卡流量

比较常用的有：iftop、nethogs

iftop 非常直观方便 

![image-20220928102636176](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220928102636176.png)

## lsof

查看文件被哪个进程使用:

```bash
lsof test.txt
```

查看端口被占用:

```
➜  bds-metric-sdk lsof -nP -i4TCP |grep 8080
java      22768 bairen   86u  IPv6 0x35f20ae339719195      0t0  TCP *:8080 (LISTEN)
```



## netstat 

查看本地 redis 7002端口的client

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230315103104765.png" alt="image-20230315103104765" style="zoom:80%;" />

## limits.conf

 limits.conf  配置会影响部分应用性能

通过 ulimit -a 查看:

```
core file size          (blocks, -c) 0
data seg size           (kbytes, -d) unlimited
scheduling priority             (-e) 0
file size               (blocks, -f) unlimited
pending signals                 (-i) 15064
max locked memory       (kbytes, -l) 64
max memory size         (kbytes, -m) unlimited
open files                      (-n) 655350
pipe size            (512 bytes, -p) 8
POSIX message queues     (bytes, -q) 819200
real-time priority              (-r) 0
stack size              (kbytes, -s) 8192
cpu time               (seconds, -t) unlimited
max user processes              (-u) 15064
virtual memory          (kbytes, -v) unlimited
file locks                      (-x) unlimited
```



调整  max user processes，编辑 /etc/security/limits.conf 新增：

```
* soft nproc 327675
* hard nproc 327675
```



