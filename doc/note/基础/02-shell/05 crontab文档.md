

## 概述



```
例子：
    # 每月的最后1天
    0 0 L * * *

    说明：
    Linux
    *    *    *    *    *
    -    -    -    -    -
    |    |    |    |    |
    |    |    |    |    +----- day of week (0 - 7) (Sunday=0 or 7) OR sun,mon,tue,wed,thu,fri,sat
    |    |    |    +---------- month (1 - 12) OR jan,feb,mar,apr ...
    |    |    +--------------- day of month (1 - 31)
    |    +-------------------- hour (0 - 23)
    +------------------------- minute (0 - 59)
```



语法

```bash
*/1 * * * * sh /data/program/auto_start_app.sh
@reboot  sh /rebootAutoStartKafkaCluster.sh	
```



## 自动重启

语法：

```
@reboot /data/program/kafka/rebootAutoStartKafkaCluster.sh &
@reboot /data/program/es/startElasticsearch.sh &
```

注意crontab有一个坑，无法获取 /etc/profile 和 ~/.bashrc 中定义的环境变量，后果是启动失败：

```
2024-04-09 16:58:45 prepare start elasticsearch
2024-04-09 16:58:45 start elasticsearch
warning: Falling back to java on path. This behavior is deprecated. Specify JAVA_HOME
2024-04-09 16:58:55 failed to start elasticsearch 9300: port not available
```

需要在启动脚本中添加环境变量:

```shell
#!/bin/bash

JAVA_HOME=/data/program/java8
export JAVA_HOME

# 运行 Elasticsearch
...

```



## 案例

- 0 7 * * * 每天 7 点执行
- @reboot 系统启动自动执行

 

## 参考

- https://tool.lu/crontab/