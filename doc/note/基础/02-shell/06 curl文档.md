##  状态码

```
curl $url -I
```



## HTTP 头

```shell
curl -H 'X-Login-User: jack' -H 'token: 5d9ef3e1790a94a4edb78b0a4r5dd89564fe4f3c'  http://wse-server.wse.k2.test.wacai.info/wse/api/v1/app/my/group
{
	"code":0,
	"data":[]
}%
```



## POST请求

```shell
curl  -H 'x-token: 8d9681e1790a94abf3b78b0a7ffdd89564f94f3c' -X POST "http://localhost:8080/api/v2/clusters/test/namespaces" -H "accept: application/json" -H "Content-Type: application/json" -d "{\"groupName\":\"group\",\"envType\":\"type\",\"name\":\"name\",\"description\":\"desc\",\"env\":\"env\"}
```



Hermes getAccessModel：

```shell
 curl -X POST  http://center.hermes.middleware.k2.wacai.info/hermes-center/manager/consumerGroup  -H "Content-Type: application/json" -d "{\"appName\":\"edu-gatling-service\",\"groupId\":\"edu-gatling-service\",\"topic\":\"qy.mark.tag\"}"
```





## 使用 nc

```shell
printf 'GET /jasmine/getAllBySubModule?test=bairen&subModule=wacai-boot-starters&module=com.wacai.middleware HTTP/1.1\r\n'\
'Content-Type: application/x-www-form-urlencoded;charset=utf-8\r\n'\
'User-Agent: Java/1.8.0_221\r\n'\
'Host: *************:8080\r\n'\
'Accept: text/html, image/gif, image/jpeg, *; q=.2, */*; q=.2\r\n'\
'\r\n'\
| nc ************* 8080
```



## + GREP

```
curl "" 2>&1 | grep 
```



