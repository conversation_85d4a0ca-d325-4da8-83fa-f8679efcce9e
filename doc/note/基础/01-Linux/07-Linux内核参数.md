## 概述

Linux 部分内核参数配置如果不正确会导致网络问题。基本上都在 sysctl.conf 中，本文对参数进行整理

## tcp_tw_reuse

tcp_tw_reuse 参数如果没有打开，不能快速地复用还处在 time-wait 状态的地址，只能等待 time-wait 的超时关闭，rfc 协议里规定等待 2 分钟左右，开启 tw_reuse可在 1s 后复用该地址。另外 `ip_local_port_range` 端口范围也不大，缩短了可用的连接范围。

```
sysctl  -a|egrep "tw_reuse|timestamp|local_port"

net.ipv4.ip_local_port_range = 35768    60999
net.ipv4.tcp_timestamps = 1
net.ipv4.tcp_tw_reuse = 0

```

由于没有可用地址才爆出了 `connect: cannot assign requested address` 错误





## 参考

- [高并发服务遇redis瓶颈引发time-wait事故](http://xiaorui.cc/archives/7175)

