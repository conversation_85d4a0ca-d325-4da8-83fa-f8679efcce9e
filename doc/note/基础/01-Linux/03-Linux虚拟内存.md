## 概述

日常工作中或多或少会看到 《Linux 虚拟内存》 这个术语，比如MMap就是把硬盘上的文件映射到虚拟内存，那么什么是虚拟内存？ Linux为什么要引入虚拟内存呢？

如果需要我们从头设计一个操作系统，让系统中的进程直接访问主内存中的物理地址应该是非常自然的决定，早期的操作系统确实也都是这么实现的，进程会使用目标内存的物理地址（Physical Address）直接访问内存中的内容：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/18.png" alt="18" style="zoom:50%;" />



然而现代的操作系统都引入了虚拟内存，进程持有的虚拟地址（Virtual Address）会经过内存管理单元（Memory Mangament Unit）的转换变成物理地址。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/19.png" alt="19" style="zoom:50%;" />

引入MMU相当于我们引入了一个抽象的代理层，这样设计会带来很多好处：

- 虚拟内存可以控制进程对物理内存的访问，隔离不同进程的访问权限，提高系统的安全性，假设一个用户进程恶意或无意修改了内核的内存数据会导致系统崩溃。
- 虚拟内存为每个进程提供了一个一致的地址空间，从而降低了程序员对内存管理的复杂性。
- 引入虚拟内存还可以扩大物理内存的边界，比如通过和磁盘数据进程swap，可以提供大于物理内存的容量。

理解不深刻的人会认为虚拟内存只是“使用硬盘空间来扩展内存“的技术，这是不对的。虚拟内存的重要意义是它定义了一个连续的虚拟地址空间，使得程序的编写难度降低。并且，把内存扩展到硬盘空间只是使用虚拟内存的必然结果，虚拟内存空间会存在硬盘中，并且会被内存缓存（按需），有的操作系统还会在内存不够的情况下，将某一进程的内存全部放入硬盘空间中，并在切换到该进程时再从硬盘读取（这也是为什么Windows会经常假死的原因…）。

## 从进程视角看虚拟内存

当一个程序尝试使用虚拟地址访问内存时，操作系统连同硬件会将该分页的虚拟地址映射到某个具体的物理位置，这个位置可以是物理内存、swap文件。尽管每个进程都有其自己的地址空间，但程序通常无法使用所有这些空间，因为地址空间被划分为内核空间和用户空间。大部分操作系统将每个进程地址空间的一部分映射到一个通用的内核内存区域。被映射来供内核使用的地址空间部分被称为内核空间，其余部分被称为用户空间，可供用户应用程序使用。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/23.png" alt="23" style="zoom:50%;" />

## CPU寻址

内存通常被组织为一个由M个连续的字节大小的单元组成的数组，每个字节都有一个唯一的物理地址（Physical Address PA），作为到数组的索引。CPU访问内存最简单直接的方法就是使用物理地址，这种寻址方式被称为物理寻址。

现代处理器使用的是一种称为虚拟寻址（Virtual Addressing）的寻址方式。使用虚拟寻址，CPU需要将虚拟地址翻译成物理地址，这样才能访问到真实的物理内存。

MMU是处理器/核（processer）中的一个硬件单元，通常每个核有一个MMU。MMU由两部分组成：TLB(Translation Lookaside Buffer)和table walk unit。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/20.png" alt="20" style="zoom:50%;" />

MMU另外还需要借助存放在内存中的页表(Page Table)来动态翻译虚拟地址，该页表由操作系统管理，每个进程独有的，是软件实现的。

因为访问内存中的页表相对耗时，尤其是在现在普遍使用多级页表的情况下，需要多次的内存访问，为了加快访问速度，系统设计人员为page table设计了一个硬件缓存 - **TLB**，CPU会首先在TLB中查找，因为在TLB中找起来很快。TLB之所以快，一是因为它含有的entries的数目较少，二是TLB是集成进CPU的，它几乎可以按照CPU的速度运行。

如果在TLB中找不到才会在页表中找。

## 虚拟页

操作系统通过将虚拟内存分割为大小固定的块来作为硬盘和内存之间的传输单位，这个块被称为虚拟页（Virtual Page, VP），每个虚拟页的大小为P=2^12字节=4KB。物理内存也会按照这种方法分割为**物理页**（Physical Page, PP），大小也为P字节。

MMU在翻译的过程中还需要借助页表，所谓页表就是一个存放在物理内存中的数据结构，它记录了虚拟页与物理页的映射关系。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/22.png" alt="22" style="zoom:50%;" />

页表是一个元素为页表条目（Page Table Entry, PTE）的集合，每个虚拟页在页表中一个固定偏移量的位置上都有一个PTE。PET保存了从虚拟内存的page转换到物理内存中page frame的全部信息:

- the physical base address
- page’s access permission
- the cache and write buffer configuration for the page

在进行动态内存分配时，例如`malloc()`函数或者其他高级语言中的`new`关键字，操作系统会在硬盘中创建或申请一段虚拟内存空间，并更新到页表（分配一个PTE，使该PTE指向硬盘上这个新创建的虚拟页）。

由于CPU每次进行地址翻译的时候都需要经过PTE，所以如果想控制内存系统的访问，可以在PTE上添加一些额外的许可位（例如读写权限、内核权限等），这样只要有指令违反了这些许可条件，CPU就会触发一个一般保护故障，将控制权传递给内核中的异常处理程序。一般这种异常被称为“段错误（Segmentation Fault）”。



### 页命中

如上图所示，MMU根据虚拟地址在页表中寻址到了`PTE 4`，该PTE的有效位为1，代表该虚拟页已经被缓存在物理内存中了，最终MMU得到了PTE中的物理内存地址（指向`PP 1`）。

### 缺页

如上图所示，MMU根据虚拟地址在页表中寻址到了`PTE 2`，该PTE的有效位为0，代表该虚拟页并没有被缓存在物理内存中。**虚拟页没有被缓存在物理内存中（缓存未命中）被称为缺页。**

**当CPU遇见缺页时会触发一个缺页异常，缺页异常将控制权转向操作系统内核，然后调用内核中的缺页异常处理程序，该程序会选择一个牺牲页，如果牺牲页已被修改过，内核会先将它复制回硬盘（采用写回机制而不是直写也是为了尽量减少对硬盘的访问次数），然后再把该虚拟页覆盖到牺牲页的位置，并且更新PTE。**

**当缺页异常处理程序返回时，它会重新启动导致缺页的指令，该指令会把导致缺页的虚拟地址重新发送给MMU**。由于现在已经成功处理了缺页异常，所以最终结果是页命中，并得到物理地址。

这种在硬盘和内存之间传送页的行为称为页面调度（paging）：页从硬盘换入内存和从内存换出到硬盘。当缺页异常发生时，才将页面换入到内存的策略称为按需页面调度（demand paging），所有现代操作系统基本都使用的是按需页面调度的策略。

虚拟内存跟CPU高速缓存一样依赖于局部性原则。 虽然处理缺页消耗的性能很多（毕竟还是要从硬盘中读取），而且程序在运行过程中引用的不同虚拟页的总数可能会超出物理内存的大小，但是**局部性原则保证了在任意时刻，程序将趋向于在一个较小的活动页面（active page）集合上工作，这个集合被称为工作集（working set）**。根据空间局部性原则（一个被访问过的内存地址以及其周边的内存地址都会有很大几率被再次访问）与时间局部性原则（一个被访问过的内存地址在之后会有很大几率被再次访问），只要将工作集缓存在物理内存中，接下来的地址翻译请求很大几率都在其中，从而减少了额外的硬盘流量。

如果一个程序没有良好的局部性，将会使工作集的大小不断膨胀，直至超过物理内存的大小，这时程序会产生一种叫做抖动（thrashing）的状态，页面会不断地换入换出，如此多次的读写硬盘开销，性能自然会十分“恐怖”。**所以，想要编写出性能高效的程序，首先要保证程序的时间局部性与空间局部性。**

### 多级页表

我们目前为止讨论的只是单页表，但在实际的环境中虚拟空间地址都是很大的（一个32位系统的地址空间有`2^32 = 4GB`，更别说64位系统了）。在这种情况下，使用一个单页表明显是效率低下的。

这个结构看起来很像是一个`B-Tree`，这种层次结构有效的减缓了内存要求：

- 如果一个一级页表的一个PTE是空的，那么相应的二级页表也不会存在。这代表一种巨大的潜在节约（对于一个普通的程序来说，虚拟地址空间的大部分都会是未分配的）。
- 只有一级页表才总是需要缓存在内存中的，这样虚拟内存系统就可以在需要时创建、页面调入或调出二级页表（只有经常使用的二级页表才会被缓存在内存中），这就减少了内存的压力。

## 内存映射

简单的来说：**普通文件映射就是将一个文件与一块内存建立起映射关系，对该文件进行IO操作可以绕过内核直接在用户态完成（用户态在该虚拟地址区域读写就相当于读写这个文件）。匿名文件映射一般在用户空间需要分配一段内存来存放数据时，由内核创建匿名文件并与内存进行映射，之后用户态就可以通过操作这段虚拟地址来操作内存了。匿名文件映射最熟悉的应用场景就是动态内存分配（malloc()函数）。**

Linux很多地方都采用了“懒加载”机制，自然也包括内存映射。不管是普通文件映射还是匿名映射，Linux只会先划分虚拟内存地址。只有当CPU第一次访问该区域内的虚拟地址时，才会真正的与物理内存建立映射关系。

只要虚拟页被初始化了，它就在一个由内核维护的交换文件（swap file）之间换来换去。交换文件又称为交换空间（swap space）或交换区域（swap area）。swap区域不止用于页交换，在物理内存不够的情况下，还会将部分内存数据交换到swap区域（使用硬盘来扩展内存）。

### 共享对象

虚拟内存系统为每个进程提供了私有的虚拟地址空间，这样可以保证进程之间不会发生错误的读写。但多个进程之间也含有相同的部分，例如每个C程序都使用到了C标准库，如果每个进程都在物理内存中保持这些代码的副本，那会造成很大的内存资源浪费。

同样的，Redis 快照使用子进程 复制就利用了虚拟内存的这个特性。当我们在 Linux 中调用 `fork` 创建子进程时，实际上**只复制了父进程的页表**。如下图所示，父子进程会通过不同的页表指向相同的物理内存：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/21.png" alt="21" style="zoom:50%;" />

## VSS/RSS/PSS/USS

在Linux里面，一个进程占用的内存有不同种说法，可以是VSS/RSS/PSS/USS四种形式，这四种形式首字母分别是Virtual/Resident/Proportional/Unique的意思。


- VSS是单个进程全部可访问的虚拟地址空间，其大小可能包括还尚未在内存中驻留的部分。对于确定单个进程实际内存使用大小，VSS用处不大。
- RSS是单个进程实际占用的内存大小，RSS不太准确的地方在于它包括该进程所使用共享库全部内存大小。对于一个共享库，可能被多个进程使用，实际该共享库只会被装入内存一次。
- 进而引出了PSS，PSS相对于RSS计算共享库内存大小是按比例的。N个进程共享，该库对PSS大小的贡献只有1/N。
- USS是单个进程私有的内存大小，即该进程独占的内存部分。USS揭示了运行一个特定进程在的真实内存增量大小。如果进程终止，USS就是实际被返还给系统的内存大小。

## 匿名映射

进程用户态内存分布中提到过，映射分为文件映射和匿名映射。

文件映射就是磁盘中的数据通过文件系统映射到内存再通过文件映射映射到虚拟空间，这样，用户就可以在用户空间通过 open ,read, write 等函数区操作文件内容。至于实际的代码，open,read,write,close,mmap... 操作的虚拟地址都属于文件映射。

匿名映射就是用户空间需要分配一定的物理内存来存储数据，这部分内存**不属于任何文件**，内核就使用匿名映射将内存中的某段物理地址与用户空间一一映射，这样用户就可用直接操作虚拟地址来范围这段物理内存。比如使用malloc申请内存。

## 参考

- [Java NIO，一本难念的经](https://zhuanlan.zhihu.com/p/29675083)
- [为什么 Linux 需要虚拟内存](https://draveness.me/whys-the-design-os-virtual-memory/)
- [虚拟内存的那点事儿](https://sylvanassun.github.io/2017/10/29/2017-10-29-virtual_memory/)

