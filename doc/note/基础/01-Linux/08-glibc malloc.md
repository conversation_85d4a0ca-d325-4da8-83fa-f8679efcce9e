## 概述

之前遇到过某java应用经常出现RSS 长期上涨超过docker物理内存限制，后来发现是 glibc malloc 导致的问题。这篇文章对相关知识点进行总结。



## glibc malloc 是什么

glibc是GNU发布的libc库，即c运行库。glibc是linux系统中最底层的api，几乎其它任何运行库都会依赖于glibc。glibc除了封装linux操作系统所提供的系统服务外，它本身也提供了许多其它一些必要功能服务的实现。

linux 内核提供两种申请内存的方式 mmap 和 brk( 可通过直接man查看文档)，malloc本质是对他们的封装。[2]()

malloc 是 glibc 提供的一个重要 API 用于申请内存，一个简单 C++代码如下：[1]()

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725003601622.png" alt="image-20230725003601622" style="zoom:50%;" />

有多中内存分配器实现，比如：

- ptmallac2- glibc默认实现
- jemalloc - FreeBSD & Firefox
- tcmalloc - Google

为什么需要封装一套 malloc直接使用内核提供的 API 不行吗？

系统调用本身会产生软中断，导致程序从用户态陷入内核态，比较消耗资源。试想，如果频繁分配回收小块内存区，那么将有很大的性能耗费在系统调用中。因此，为了减少系统调用带来的性能损耗，glibc采用了内存池的设计，增加了一个代理层，每次内存分配，都优先从内存池中寻找，如果内存池中无法提供，再向操作系统申请

## 伙伴分配算法

在内存管理中经常听到伙伴分配算法，那么什么是伙伴分配算法？ [3]()

伙伴分配算法的本质是为了减少内存碎片。

## 应用内存管理

从Linux操作系统层面来看，每个应用进程使用task_struct结构进行描述和管理，在task_struct的中，使用mm_struct对内存进行管理，如下图所示：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230726070801563.png" alt="image-20230726070801563" style="zoom:67%;" />

在mm_struct管理的虚拟内存中，主要包括：Kernel Space、MMAP segment、Stack、Heap、BSS segment、Data segment和Text segment

从图中可以看到**堆就是一个进程的虚拟内存中的一片区域**，可以通过系统调用sbrk函数和mmap函数从操作系统申请heap。**申请一个超级大或者超级小的heap都是不合适的**，glibc会申请固定大小的heap(64MB)进行管理，分配内存时内部再将heap拆分为一个个不同大小的虚拟内存片（chunk)，chunk要么是已分配的，要么是空闲的。 [9]()

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230726073127280.png" alt="image-20230726073127280" style="zoom:50%;" />

注意实际情况是，main arena中通过sbrk扩展heap，在thread arena中才是通过mmap分配新的heap。[7]()

> Main arena没有multiple heaps，因此没有heap_info结构。当main arena空间用尽时，sbrk创建的堆段会被增长（连续空间），直到它撞到内存映射段。
> 和thread arena不同，main arena的arena header不是sbrk创建的堆段的一部分，它是一个全局变量，因此它在libc.so的数据段。



## malloc 基本思想

如果简单描述malloc可以理解为通过伙伴系统和基于链表的实现 [4]()

1. 它有一个将可用的内存块连接为一个长长的列表的所谓空闲链表；
2. 调用malloc函数时，它沿连接表寻找一个大到足以满足用户请求所需要的内存块。然后，将该内存块一分为二（一块的大小与用户请求的大小相等，另一块的大小就是剩下的字节）。接下来，将分配给用户的那块内存传给用户，并将剩下的那块（如果有的话）返回到链表上。
3. 调用free函数时，它将用户释放的内存块连接到空闲链上。到最后，空闲链会被切成很多的小内存片段，如果这时用户申请一个大的内存片段，那么空闲链上可能没有可以满足用户要求的片段了。于是，malloc函数请求延时，并开始在空闲链上翻箱倒柜地检查各内存片段，对它们进行整理，将相邻的小空闲块合并成较大的内存块。 

链表是一种经典的堆内存管理方式，经常被用在教学中，很多C语言教程都会提到“栈内存的分配类似于数据结构中的栈，而堆内存的分配却类似于数据结构中的链表”就是源于此。

当然实际实现过程中 ptmallac 比这个复杂。接下来我们来看 ptmallac2是如何实现的。



## ptmalloc 数据结构

ptmallac 从宏观到围观由以下三层数据结构组成：[5]()

- arena (分配区）对进程内存是通过一个个arena来进行管理，arena之间是通过指针连接形成一个环形链表。
- bin（空闲链表）是arena中用于管理可用内存块的链表，当内存回收之后会被放在这里  
- chunk（内存块）则是用户申请和释放内存的最小单位。bins的头部永远是一个被称为top chunk的空闲块，当没有合适的chunk时，会扩容并返回top chunk来处理请求。

### arena

早期的 glibc 版本中，只有一个内存池，称为 main arena，在多线程场景中，每次分配和释放需要进行加锁。后来为了降低锁的粒度，从glibc 2.10版本开始引入了 thread arena，线程在申请内存的时候，glibc 为他创建一个 thread arena，这个内存池的大小一般是64M，thread arena不被某个线程独占，全部的 thread arena被加入到环形链表，被所有线程共享使用。

**主分配区可以使用sbrk和mmap向os申请内存，而非分配区只能通过mmap向os申请内存**。

当一个线程调用malloc申请内存时，该线程先查看线程私有变量中是否已经存在一个分配区。如果存在，则对该分配区加锁，加锁成功的话就用该分配区进行内存分配；失败的话则搜索环形链表找一个未加锁的分配区。如果所有分配区都已经加锁，那么malloc会开辟一个新的分配区加入环形链表并加锁，用它来分配内存。释放操作同样需要获得锁才能进行。

比如图中 arena1 和 arena2 都已经被加锁，那么会新创建一个arena3， 如果超过最大限制并且没能找到可用的arena，那么就将线程的malloc操作阻塞，直到有可用的arena为止。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725210711593.png" alt="image-20230725210711593" style="zoom:50%;" />



需要注意的是，非主分配区是通过mmap向os申请内存，一次申请64MB，一旦申请了，该分配区就不会被释放，为了避免资源浪费，ptmalloc对分配区是有个数限制的。

对于64位系统，分配区最大个数 = 8 * CPU核数 + 1 ，每个分配区缓存 64MB

### chunk

ptmalloc通过malloc_chunk来管理内存，其定义如下:

```c++
struct malloc_chunk {  
  INTERNAL_SIZE_T      prev_size;    /* Size of previous chunk (if free).  */  
  INTERNAL_SIZE_T      size;         /* Size in bytes, including overhead. */  
  
  struct malloc_chunk* fd;           /* double links -- used only if free. */  
  struct malloc_chunk* bk;  
  
  /* Only used for large blocks: pointer to next larger size.  */  
  struct malloc_chunk* fd_nextsize;      /* double links -- used only if free. */  
  struct malloc_chunk* bk_nextsize; 
};  
```

字段含义：

- prev_size: 如果前一个chunk是空闲的，则该域表示前一个chunk的大小，如果前一个chunk不空闲，该域无意义。**prev_size主要用于相邻空闲chunk的合并**。

- size ：该 chunk 的大小，大小必须是 2 * SIZE_SZ 的整数倍。如果申请的内存大小不是 2 * SIZE_SZ 的整数倍，会被转换满足大小的最小的 2 * SIZE_SZ 的倍数。SIZE_SZ表示字长，32 位系统中是 4，64 位系统中是 8。因此size是8的整数倍，所以后3位是无效的，该字段的低三个比特位是标志位，它们从高到低分别表示：

  - [NON_MAIN_ARENA](https://github.com/sploitfun/lsploits/blob/master/glibc/malloc/malloc.c#L1283) (N) – 记录当前 chunk 是否不属于主线程，1 表示不属于，0 表示属于。
  - [IS_MMAPPED](https://github.com/sploitfun/lsploits/blob/master/glibc/malloc/malloc.c#L1274) (M) – 记录当前 chunk 是否是由 mmap 分配的。
  - [PREV_INUSE](https://github.com/sploitfun/lsploits/blob/master/glibc/malloc/malloc.c#L1267) (P) –记录前一个 chunk 块是否被分配。

- fd，bk ： chunk 处于分配状态时，从 fd 字段开始是用户的数据。chunk 空闲时，会被添加到对应的空闲管理链表bins中，其字段的含义如下

  - fd 指向下一个（非物理相邻）空闲的 chunk
  - bk 指向上一个（非物理相邻）空闲的 chunk
  - 通过 fd 和 bk 可以将空闲的 chunk 块加入到空闲的 chunk 块链表进行统一管理

- **fd_nextsize， bk_nextsize** ：也是只有 chunk 空闲的时候才使用，不过其用于较大的 chunk（large chunk在后面bins中会讲到）

  - fd_nextsize 指向前一个与当前 chunk 大小不同的第一个空闲块，不包含 bin 的头指针。

  - bk_nextsize 指向后一个与当前 chunk 大小不同的第一个空闲块，不包含 bin 的头指针。

  - 一般空闲的 large chunk 在 fd 的遍历顺序中，按照由大到小的顺序排列。**这样做可以避免在寻找合适 chunk 时挨个遍历。**

    

正如上面所描述，在ptmalloc中，为了尽可能的节省内存，已分配的chunk和未使用的chunk在结构上是不一样的。

空闲chunk与非空闲chunk相比，空闲chunk在用户区域多了四个指针

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725213501327.png" alt="image-20230725213501327" style="zoom:45%;" />

已分配的chunk

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725213302797.png" alt="image-20230725213302797" style="zoom:48%;" />

### bins

**用户调用free函数释放内存的时候，ptmalloc并不会立即将其归还操作系统，而是将其放入空闲链表(bins)中，这样下次再调用malloc函数申请内存的时候，就会从bins中取出一块返回，这样就避免了频繁调用系统调用函数，从而降低内存分配的开销。**

 在具体的实现中，ptmalloc 采用分箱式方法对空闲的 chunk 进行管理。首先，它会根据空闲的 chunk 的大小以及使用状态将 chunk 初步分为 4 类：fast bins，small bins，large bins，unsorted bin。其中fast bins是10个，后面三种  small bins，large bins，unsorted bin ，ptmalloc 将它们维护在同一个数组中。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725214440214.png" alt="image-20230725214440214" style="zoom:45%;" />



这些 bin 对应的数据结构在 malloc_state 中，如下

```c++
#define NBINS 128
/* Normal bins packed as described above */
mchunkptr bins[ NBINS * 2 - 2 ];
```

bins这个长128的数组中，每个元素都是一双向个链表。其中

1. bins[0]目前没有使用
2. bins[1] unsorted bin，字如其面，这里面的 chunk 没有进行排序，存储的 chunk 比较杂。
3. bins[2,63)的区间称为 small bin，同一个 small bin 链表中的 chunk 的大小相同。两个相邻索引的 small bin 链表中的 chunk 大小相差的字节数为 **2 个机器字长**，即 32 位相差 8 字节，64 位相差 16 字节。
4. bins[64,127) 的区间称作 large bins。large bins 中的每一个 bin 都包含一定范围内的 chunk，其中的 chunk 按 fd 指针的顺序从大到小排列。相同大小的 chunk 同样按照最近使用顺序排列。

需要注意的是，并不是所有的 chunk 被释放后就立即被放到 bin 中。ptmalloc 为了提高分配的速度，会把一些小的 chunk **先**放到 fast bins 的容器内。

### fast bin

程序在运行时会经常需要申请和释放一些较小的内存空间。当分配器合并了相邻的几个小的 chunk 之后,也许马上就会有另一个小块内存的请求,这样分配器又需要从大的空闲内存中切分出一块,这样无疑是比较低效的,故而,malloc 中在分配过程中引入了 fast bins。

在前面 malloc_state 定义中

```c++
mfastbinptr fastbins[NFASTBINS]; // NFASTBINS  = 10
```

一些说明：

1. fast bin的个数是10个
2. 为了更加高效地利用 fast bin，glibc 采用单向链表对其中的每个 bin 进行组织，并且**每个 bin 采取 LIFO 后进先出策略**，最近释放的 chunk 会更早地被分配，所以会更加适合于局部性。
3. 10个fast bin中所包含的chunk size以8个字节逐渐递增，即第一个fast bin中chunk size均为16个字节，第二个fast bin的chunk size为24字节，以此类推，最后一个fast bin的chunk size为80字节。
4. 不会对free chunk进行合并操作。这是因为fast bin设计的初衷就是小内存的快速分配和释放，因此系统将属于fast bin的chunk的P(未使用标志位)总是设置为1，这样即使当fast bin中有某个chunk同一个free chunk相邻的时候，系统也不会进行自动合并操作，而是保留两者。
5. malloc操作：在malloc的时候，如果申请的内存大小范围在fast bin的范围内，则先在fast bin中查找，如果找到了，则返回。否则则从small bin、unsorted bin以及large bin中查找。
6. free操作：先通过chunksize函数根据传入的地址指针获取该指针对应的chunk的大小；然后根据这个chunk大小获取该chunk所属的fast bin，然后再将此chunk添加到该fast bin的链尾即可



结构如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725215759002.png" alt="image-20230725215759002" style="zoom:60%;" />

### unsorted bin

unsorted bin 处于我们之前所说的 bin 数组下标 1 处。故而 unsorted bin 只有一个链表。unsorted bin 中的空闲 chunk 处于乱序状态，主要有两个来源

- 当一个较大的 chunk 被分割成两半后，如果剩下的部分大于 **最小的 chunk 大小** ( 2 * SIZE_SZ )，就会被放到 unsorted bin 中。
- 释放一个不属于 fast bin 的 chunk，并且该 chunk 不和 top chunk 紧邻时，该 chunk 会被首先放到 unsorted bin 中。关于 top chunk 的解释，请参考下面的介绍。

此外，Unsorted Bin 在使用的过程中，采用的遍历顺序是 FIFO 。

### small bin

bins[2,63)的区间称为small_bins，一共有 62 个, 用于保存小于512字节的chunk。

small bins 中每个 chunk 的大小与其所在的 bin 的 index 的关系为：chunk_size = 2 * SIZE_SZ *index，SIZE_SZ表示表示字长，32位系统中是4，64位系统中是8。具体如下：

| 下标 | SIZE_SZ=8（64 位） |
| :--- | :----------------- |
| 2    | 32                 |
| 3    | 48                 |
| 4    | 64                 |
| 5    | 80                 |
| i    | 2x8xi              |
| 63   | 1008               |

small bins 中一共有 62 个**循环双向链表**，每个链表中存储的 chunk 大小都一致。比如对于 64 位系统来说，下标 2 对应的双向链表中存储的 chunk 大小为均为 32 字节。

small bins 中每个 bin 对应的链表采用 FIFO 的规则，所以同一个链表中先被释放的 chunk 会先被分配出去。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725222629457.png" alt="image-20230725222629457" style="zoom:50%;" /> 

**或许，大家会很疑惑，那 fastbin 与 small bin 中 chunk 的大小会有很大一部分重合啊，那 small bin 中对应大小的 bin 是不是就没有什么作用啊？** 其实不然，fast bin 中的 chunk 是有可能被放到 small bin 中去的，我们在后面分析具体的源代码时会有深刻的体会

### large bin

bins[64,127)称为large_bins，一共包括 63 个 bin，用于维护>512字节的内存块，**每个元素对应的链表中的chunk大小不同，而是处于一定区间范围**内。
此外，这 63 个 bin 被分成了 6 组，分别有不同的步长，具体如下：

| 组   | 数量 | 公差   |
| :--- | :--- | :----- |
| 1    | 32   | 64     |
| 2    | 16   | 512    |
| 3    | 8    | 4096   |
| 4    | 4    | 32768  |
| 5    | 2    | 262144 |
| 6    | 1    | 不限制 |

第一组的32个largebin链依次以64字节步长为间隔，即第一个largebin链中chunk size为 1024-1087 字节，第二个large bin中chunk size为1088~1151字节。
第二组的16个largebin链依次以512字节步长为间隔；
第三组的8个largebin链以步长4096为间隔；
第四组的4个largebin链以32768字节为间隔；
第五组的2个largebin链以262144字节为间隔；
最后一组的largebin链中的chunk大小无限制。

在进行malloc操作的时候，首先确定用户请求的大小属于哪一个large bin，然后判断该large bin中最大的chunk的size是否大于用户请求的size。如果大于，就从尾开始遍历该large bin，找到第一个size相等或接近的chunk，分配给用户。如果该chunk大于用户请求的size的话，就将该chunk拆分为两个chunk：**前者返回给用户，且size等同于用户请求的size；剩余的部分做为一个新的chunk添加到unsorted bin中**。


如果该large bin中最大的chunk的size小于用户请求的size的话，那么就依次查看后续的large bin中是否有满足需求的chunk，不过需要注意的是鉴于bin的个数较多(不同bin中的chunk极有可能在不同的内存页中)，如果按照上一段中介绍的方法进行遍历的话(即遍历每个bin中的chunk)，就可能会发生多次内存页中断操作，进而严重影响检索速度，所以glibc malloc设计了Binmap结构体来帮助提高bin-by-bin检索的速度。Binmap记录了各个bin中是否为空，通过bitmap可以避免检索一些空的bin。如果通过binmap找到了下一个非空的large bin的话，就按照上一段中的方法分配chunk，否则就使用top chunk（在后面有讲）来分配合适的内存。

### top trunk

程序第一次进行 malloc 的时候，heap 会被分为两块，一块给用户，剩下的那块就是 top chunk。其实，所谓的 top chunk 就是处于当前堆的物理地址最高的 chunk。这个 chunk 不属于任何一个 bin，它的作用在于当所有的 bin 都无法满足用户请求的大小时，如果其大小不小于指定的大小，就进行分配，并将剩下的部分作为新的 top chunk。否则，就对 heap 进行扩展后再进行分配。在 main arena 中通过 sbrk 扩展 heap，而在 thread arena 中通过 mmap 分配新的 heap。

需要注意的是，top chunk 的 prev_inuse 比特位始终为 1，否则其前面的 chunk 就会被合并到 top chunk 中。

### malloc_state

该结构用于管理堆，记录每个 arena 当前申请的内存的具体状态，比如说是否有空闲 chunk，有什么大小的空闲 chunk 等等。无论是 thread arena 还是 main arena，它们都只有一个 malloc state 结构。由于 thread 的 arena 可能有多个，malloc state 结构会在最新申请的 arena 中

**main arena 的 malloc_state 并不是 heap segment 的一部分，而是一个全局变量，存储在 libc.so 的数据段。**其结构如下

```c++
struct malloc_state {
    /*并发编程下锁的竞争  */
    __libc_lock_define(, mutex);

    /* Flags (formerly in max_fast).  */
    int flags;

    /* Fastbins */
    mfastbinptr fastbinsY[ NFASTBINS ];

    /* Base of the topmost chunk -- not otherwise kept in a bin */
    mchunkptr top;

    /* The remainder from the most recent split of a small request */
    mchunkptr last_remainder;

    /* Normal bins packed as described above */
    mchunkptr bins[ NBINS * 2 - 2 ];

    /* Bitmap of bins, help to speed up the process of determinating if a given bin is definitely empty.*/
    unsigned int binmap[ BINMAPSIZE ];

    /* Linked list, points to the next arena */
    struct malloc_state *next;

    /* Linked list for free arenas.  Access to this field is serialized
       by free_list_lock in arena.c.  */
    struct malloc_state *next_free;

    /* Number of threads attached to this arena.  0 if the arena is on
       the free list.  Access to this field is serialized by
       free_list_lock in arena.c.  */
    INTERNAL_SIZE_T attached_threads;

    /* Memory allocated from the system in this arena.  */
    INTERNAL_SIZE_T system_mem;
    INTERNAL_SIZE_T max_system_mem;
};
```

- flags - 记录了分配区的一些标志
- fastbinsY[NFASTBINS] - 存放每个 fast chunk 链表头部的指针
- top - 指向分配区的 top chunk 
- bins - 用于存储 unstored bin，small bins 和 large bins 的 chunk 链表。
- binmap ptmalloc 用一个 bit 来标识某一个 bin 中是否包含空闲 chunk 。



## ptmalloc 流程

### 分配

glibc运行时库分配动态内存，底层用的是malloc来实现(new 最终也是调用malloc)，下面是malloc函数调用流程图：

![image-20230725224925805](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725224925805.png)

具体流程如下：

1. 获取分配区的锁。参见arena小节
2. 将用户的请求大小转换为实际需要分配的 chunk 空间大小
3. 判断所需分配chunk 的大小是否满足chunk_size <= max_fast (max_fast 默认为 64B)， 如果是的话，则转下一步，否则跳到第 5 步。
4. 首先尝试在 fast bins 中取一个所需大小的 chunk 分配给用户。如果可以找到，则分配结束。否则转到下一步。
5. 判断所需大小是否处在 small bins 中，即判断 chunk_size < 512B 是否成立。如果chunk 大小处在 small bins 中尝试分配，否则到下一步。
6. 说明需要分配的是一块大的内存，或者 small bins 中找不到合适的chunk。于是，ptmalloc 首先会遍历 fast bins 中的 chunk，将相邻的 chunk 进行合并，并链接到 unsorted bin 中，然后遍历 unsorted bin 中的 chunk，如果 unsorted bin 只有一个 chunk，并且这个 chunk 在上次分配时被使用过，并且所需分配的 chunk 大小属于 small bins，并且 chunk 的大小大于等于需要分配的大小，这种情况下就直接将该 chunk 进行切割，分配结束，否则将根据 chunk 的空间大小将其放入 small bins 或是 large bins 中，遍历完成后，转入下一步。
7. 到了这一步，说明需要分配的是一块大的内存，或者 small bins 和 unsorted bin 中都找不到合适的 chunk，并且 fast bins 和 unsorted bin 中所有的 chunk 都清除干净了。从 large bins 中按照“smallest-first，best-fit”原则，找一个合适的 chunk，从中划分一块所需大小的 chunk，并将剩下的部分链接回到 bins 中。若操作成功，则分配结束，否则转到下一步。
8. 如果搜索 fast bins 和 bins 都没有找到合适的 chunk，那么就需要操作 top chunk 来进行分配了。判断 top chunk 大小是否满足所需 chunk 的大小，如果是，则从 top chunk 中分出一块来。否则转到下一步。
9. 到了这一步，说明 top chunk 也不能满足分配要求，所以，于是就有了两个选择: 如果是主分配区，调用 sbrk()，增加 top chunk 大小；如果是非主分配区，调用 mmap 来分配一个新的 sub-heap，增加 top chunk 大小；或者使用 mmap()来直接分配。在这里，需要依靠 chunk 的大小来决定到底使用哪种方法。判断所需分配的 chunk 大小是否大于等于 mmap 分配阈值，如果是的话，则转下一步，调用 mmap 分配， 否则跳到第 12 步，增加 top chunk 的大小。
10. 使用 mmap 系统调用为程序的内存空间映射一块 chunk_size align 4kB 大小的空间。然后将内存指针返回给用户。
11. 判断是否为第一次调用 malloc，若是主分配区，则需要进行一次初始化工作，分配一块大小为(chunk_size + 128KB) align 4KB 大小的空间作为初始的 heap。若已经初始化过了，主分配区则调用 sbrk()增加 heap 空间，分主分配区则在 top chunk 中切割出一个 chunk，使之满足分配需求，并将内存指针返回给用户。

### 释放

malloc进行内存分配，那么与malloc相对的就是free，进行内存释放，下面是free函数的基本流程图：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230725225538286.png" alt="image-20230725225538286" style="zoom:60%;" />

对上述流程图进行描述，如下：

1. 获取分配区的锁，保证线程安全。
2. 如果free的是空指针，则返回，什么都不做。
3. 判断当前chunk是否是mmap映射区域映射的内存，如果是，则直接munmap()释放这块内存。前面的已使用chunk的数据结构中，我们可以看到有M来标识是否是mmap映射的内存。
4. 判断chunk是否与top chunk相邻，如果相邻，则直接和top chunk合并（和top chunk相邻相当于和分配区中的空闲内存块相邻）。否则，转到步骤8
5. 如果chunk的大小大于max_fast（64b），则放入unsorted bin，并且检查是否有合并，有合并情况并且和top chunk相邻，则转到步骤8；没有合并情况则free。
6. 如果chunk的大小小于 max_fast（64b），则直接放入fast bin，fast bin并没有改变chunk的状态。没有合并情况，则free；有合并情况，转到步骤7
7. 在fast bin，如果当前chunk的下一个chunk也是空闲的，则将这两个chunk合并，放入unsorted bin上面。合并后的大小如果大于64B，会触发进行fast bins的合并操作，fast bins中的chunk将被遍历，并与相邻的空闲chunk进行合并，合并后的chunk会被放到unsorted bin中，fast bin会变为空。合并后的chunk和topchunk相邻，则会合并到topchunk中。转到步骤8
8. 判断top chunk的大小是否大于mmap收缩阈值（默认为128KB），如果是的话，对于主分配区，则会试图归还top chunk中的一部分给操作系统。free结束。



## 问题

回到java应用最初遇到的问题也就是 glibc malloc无法回收内存给 OS，可以参考[10]()



## 参考

- [1.Dynamic Memory Allocation using malloc()](https://www.youtube.com/watch?v=Vch7_YeGKH4&t=409s)
- [2.The Heap: what does malloc() do? - bin 0x14](https://www.youtube.com/watch?v=HPDBOhiKaD8)
- [3.b站讲解最好的伙伴算法](https://www.bilibili.com/video/BV1jL411N7sK/?spm_id_from=333.999.0.0)
- [4.malloc()背后的实现原理——内存池](https://zhuanlan.zhihu.com/p/474207846)
- [5.雨乐glibc内存管理5章开始](https://mp.weixin.qq.com/s/pdv5MMUQ9ACpeCpyGnxb1Q)
- [5.CTF Wiki堆相关数据结构](https://ctf-wiki.org/pwn/linux/user-mode/heap/ptmalloc2/heap-structure/)
- [6.understanding glibc malloc](https://sploitfun.wordpress.com/2015/02/10/understanding-glibc-malloc/)
- [6.understanding glibc malloc中文版](https://wooyun.js.org/drops/Linux%E5%A0%86%E7%AE%A1%E7%90%86%E5%AE%9E%E7%8E%B0%E5%8E%9F%E7%90%86%E5%AD%A6%E4%B9%A0%E7%AC%94%E8%AE%B0%20(%E4%B8%8A%E5%8D%8A%E9%83%A8).html)
- [7.MallocInternals](https://sourceware.org/glibc/wiki/MallocInternals)
- [7.MallocInternals中文版](https://www.yeefea.com/os/glibc_malloc/)
- [8.glibc内存管理那些事儿](https://cloud.tencent.com/developer/article/1141442)
- [9.理解 glibc malloc 的工作原理](https://blog.51cto.com/u_15452732/4780221)
- [10.Linux Glibc 内存站岗问题及解决方法](https://www.51cto.com/article/666913.html)

  