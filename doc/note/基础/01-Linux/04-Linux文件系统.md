## 概述

大部分的Linux文件系统（如ext2、ext3）规定，一个文件由目录项、inode和数据块组成：

- 目录项：包括文件名和inode节点号。
- Inode：又称文件索引节点，包含文件的基础信息以及数据块的指针。
- 数据块：包含文件的具体内容。

详细到具体的狭义的文件和目录则是：文件=Inode+数据块，目录=目录项。

## inode

了解inode，要从文件储存说起。文件储存在硬盘上，硬盘的最小存储单位叫做"扇区"（Sector），每个扇区储存512字节（相当于0.5KB）。

操作系统读取硬盘的时候，不会一个扇区一个扇区地读取，这样效率太低，而是一次性连续读取多个扇区，即一次性读取一个"块"（block）。这种由多个扇区组成的"块"，是文件存取的最小单位。"块"的大小，最常见的是4KB，即连续八个 sector组成一个 block。

文件数据都储存在"块"中，那么很显然，我们还必须找到一个地方储存文件的元信息，比如文件的创建者、文件的创建日期、文件的大小等等。这种储存文件元信息的区域就叫做inode，中文译名为"索引节点"。

inode包含文件的元信息，具体来说有以下内容：
文件的字节数。
文件拥有者的User ID。
文件的Group ID。
文件的读、写、执行权限。
文件的时间戳，共有三个：ctime指inode上一次变动的时间，mtime指文件内容上一次变动的时间，atime指文件上一次打开的时间。
链接数，即有多少文件名指向这个inode。
文件数据block的位置。

可以用stat命令，查看某个文件的inode信息：

```java
 stat snapshot.19001f30f1
  File: `snapshot.19001f30f1'
  Size: 584787256 	Blocks: 1142176    IO Block: 4096   regular file
Device: fd02h/64770d	Inode: 5374083     Links: 1
Access: (0664/-rw-rw-r--)  Uid: (  500/  appweb)   Gid: (  500/  appweb)
Access: 2020-01-02 15:29:18.405605695 +0800
Modify: 2020-01-02 15:29:22.826613985 +0800
Change: 2020-01-02 15:29:22.826613985 +0800
```

PS: 顺便说一下page是Linux管理内存引入的概念，为了实现虚拟内存管理机制，Linux对内存实行分页管理。自内存“分页机制”提出之始，内存页面的默认大小便被设置为 4096 字节（4KB）

### inode的大小

inode也会消耗硬盘空间，所以硬盘格式化的时候，操作系统自动将硬盘分成两个区域。一个是数据区，存放文件数据；另一个是inode区（inode table），存放inode所包含的信息。

每个inode节点的大小，一般是128字节或256字节。inode节点的总数，在格式化时就给定，一般是每1KB或每2KB就设置一个inode。假定在一块1GB的硬盘中，每个inode节点的大小为128字节，每1KB就设置一个inode，那么inode table的大小就会达到128MB，占整块硬盘的12.8%。

查看每个硬盘分区的inode总数和已经使用的数量，可以使用df -i 命令。

## 数据块

inode里存放了文件数据所在磁盘数据块号，文件越大，所需要的块号就越多，这是因为文件在磁盘上的存放是不连续的。

Linux文件系统数据存放采用inode多重索引结构，分为直接指针和3个间接指针。

[![](https://pic.aikaiyuan.com/wp-content/uploads/2014/09/20140913144536_5.jpg)](https://pic.aikaiyuan.com/wp-content/uploads/2014/09/20140913144536_5.jpg)

### 直接块指针

直接块指针就是inode中直接记录了block块的号码，直接指向block块。

假如一个文件的inode是4号，而这个inode记录数据块的实际位置放置点位2，7，13，15这四个block号码，此时操作系统可以据此来排列磁盘的阅读顺序，可以直接将四个block的内容读出来，数据的读取如下图所示

[![wKioL1edbISBeYJzAAEjwJTB_4o298.png](https://s1.51cto.com/wyfs02/M00/85/38/wKioL1edbISBeYJzAAEjwJTB_4o298.png)](https://s1.51cto.com/wyfs02/M00/85/38/wKioL1edbISBeYJzAAEjwJTB_4o298.png)

### 间接块指针

间接块指针，其实和直接块指针大同小异，他们都是指针，只是直接块指针式直接指向的block块存储的是文件的数据，而间接块指针是将指向的block块继续当作指针块使用，所以间接指针指向的block块存储的是指针信息，它将继续指向下一个block块中存储的文件数据。

### 双重间接块指针

理解了间接指针块，那么双重间接指针块就容易理解了，它是在间接指针块的基础上将第二次指向的block块也当作指针使用，这样就可以指向更的的block块。有点像mysql的B数

### 存储文件的大小

- 直接块指针。直接指针有12个，一个块大小为4KB，所以直接指针可以保存48KB的文件
- 间接块指针。指向一个Block，该Block中的数据是Block指针，指向真正保存数据的Block。每个指针占用4个字节，一个块是4KB，所以可以将一个块拆分成1024个指针，那么它的存储数据1024*4KB=4MB。
- 二级间接块指针。同理可得它可以存储的数据为1024*4MB=4GB
- 三级间接块指针。可以储存文件数据大小为1024*4GB=4TB
[![20140913144536_6](https://pic.aikaiyuan.com/wp-content/uploads/2014/09/20140913144536_6.jpg)](https://pic.aikaiyuan.com/wp-content/uploads/2014/09/20140913144536_6.jpg)

这张图从左往右看就是一颗树

## Linux 4 种常见的读写文件的方式

### Linux File I/O

read/write 这个是操作系统提供的 read 和 write 两个系统调用。 调用过程是当前进程调用 read 这个系统调用，向 kernel 申请读一个文件的多少长度的内容。这里需要注意系统的 cache，如果你要读的内容刚好在系统 page cache 里面，那 kernel 就会把这部分内容直接从 内核态 复制到 用户态，这个 read 的系统调用就完成了。反之，kernel 就需要到硬盘来读取这部分数据到自己的 page cache，这个过程会block 原来的那个 Thread，等到数据都被读取到内核态的时候，Thread 被重新唤醒，然后将数据从内核态拷贝到用户态。 write 操作与 read 有点类似，当调用 write 的时候，操作系统会把数据 copy 到 page cache 里面，然后内核会在之后将数据刷入磁盘。调用过程如图

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/mysql/4.png" alt="4" style="zoom:70%;" />

### Mmap

还有一种比较高级的方法是调用 mmap 这个系统调用，直接把一个文件映射到用户空间的内存里面。这样就可以同在相应的内存区域操作字节来访问文件内容。映射的分页会在需要的时候自动从文件中加载进来。在读数据的时候，如果这块数据已经被加载到内存中，那就会直接在用户空间的内存里面读取内容，绕过 kernel 层。如果没有加载到用户内存中，就会引发一个 page-fault，然后kernel 从硬盘给这个 page 读取数据。当数据准备完成之后，这个线程会被唤醒，然后继续从内存里面读取数据。与 read 系统调用的区别是，一旦数据加载到内存中，线程可以直接读取内存中的数据，而不需要再多一次系统调用，也避免了从 kernel 到用户空间的数据拷贝。如果数据没有被加载到内存里面，则于 read 区别不大。过程如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/mysql/5.png" alt="5" style="zoom:70%;" />

### Driect I/O read/write

操作系统允许应用程序在执行磁盘 I/O 的时候绕过缓冲区的 page cache，从用户空间直接把数据传递到磁盘。也就是直接 I/O，也叫做 裸 I/O。对于普通的服务端 app 来说，用 direct I/O 可能不会比用 read/write 要好，因为操作系统本身为I/O 做了很多优化，如果用了 direct I/O ，那就没有办法受益于这些优化了。但是数据库不是这样。数据有特定的 I/O需求。而且无论是 read/write 还是 mmap，都把 I/O 的调度交给了内核，数据库为了追求性能，自己来做 I/O 调度是最好的。Direct I/O 读写在使用上跟普通的 read/write 区别不大，只是打开文件的时候，传递一个 O_DIRECT 的 flag 进去。在读的过程中，现场对用 O_DIRECT 打开的文件调用 read 系统调用，然后这个线程直接休眠，磁盘寻址，读取数据，然后直接把数据传递给用户线程。中间不再需要 kernel 中转一下。

![6](/work/dist/branch/wacai/middleware/my-boot/doc/images/mysql/6.png)

### Asynchronous direct I/O

AIO 是 DIO 的改进版本，这两个很相似，但是区别是 DIO 在调用 write 的时候，会block；而 AIO 则是调用 io_submit 这个系统调用，并且不会 block。这个线程完全控制由用户控制。同时还可以用 io_getevents 来收集 I/O 操作的结果。这些数据都不会被拷贝到 kernel page cache 里面去，而是直接被拷贝到用户空间中

![image-20200325150317886](/Users/<USER>/Library/Application Support/typora-user-images/image-20200325150317886.png)

## 参考

https://www.aikaiyuan.com/8277.html
https://zhuanlan.zhihu.com/p/40604943