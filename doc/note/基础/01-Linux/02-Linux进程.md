## 概述

本文对 Linux 基本原理进行梳理。

## 进程

首先简单介绍下Lnux进程的概念。

我们用 C 语言写一个 hello 程序，编译后得到一个可执行文件，在命令行运行就可以打印出一句 hello world，然后程序退出。在操作系统层面，就是新建了一个进程，这个进程将我们编译出来的可执行文件读入内存空间，然后执行，最后退出。

**你编译好的那个可执行程序只是一个文件**，不是进程，**可执行文件必须要载入内存，包装成一个进程才能真正跑起来**。进程是要依靠操作系统创建的，每个进程都有它的固有属性，比如进程号（PID）、进程状态、打开的文件等等，进程创建好之后，读入你的程序，你的程序才被系统执行。

那么，操作系统是如何创建进程的呢？**对于操作系统，进程就是一个数据结构**，我们直接来看 Linux 的源码：

```c++
struct task_struct {
    // 进程状态
    long              state;
    // 进程号
    pid_t              pid;
    // 指向父进程的指针
    struct task_struct __rcu  *parent;
    // 子进程列表
    struct list_head        children;
    // 存放文件系统信息的指针
    struct fs_struct        *fs;
    // 虚拟内存结构体
    struct mm_struct  *mm;
    // 一个数组，包含该进程打开的文件指针
    struct files_struct        *files;
};
```

`task_struct`就是 Linux 内核对于一个进程的描述，也可以称为「进程描述符」。源码比较复杂，我这里就截取了一小部分比较常见的。

其中比较有意思的是`mm`指针和`files`指针。`mm`指向的是进程的虚拟内存，也就是载入资源和可执行文件的地方；`files`指针指向一个数组，这个数组里装着所有该进程打开的文件的指针。

## 文件描述符是什么

先说`files`，它是一个文件指针数组。一般来说，一个进程会从`files[0]`读取输入，将输出写入`files[1]`，将错误信息写入`files[2]`。

举个例子，以我们的角度 C 语言的`printf`函数是向命令行打印字符，但是从进程的角度来看，就是向`files[1]`写入数据；同理，`scanf`函数就是进程试图从`files[0]`这个文件中读取数据。

每个进程被创建时，files的前三位被填入默认值，**分别指向标准输入流、标准输出流、标准错误流**。我们常说的「文件描述符」就是指这个文件指针数组的索引，所以程序的文件描述符默认情况下 0 是输入，1 是输出，2 是错误。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/14.png" alt="14" style="zoom:50%;" />

对于一般的计算机，输入流是键盘，输出流是显示器，错误流也是显示器，所以现在这个进程和内核连了三根线。因为硬件都是由内核管理的，我们的进程需要通过「系统调用」让内核进程访问硬件资源。

如果我们写的程序需要其他资源，比如打开一个文件进行读写，这也很简单，进行系统调用，让内核把文件打开，这个文件就会被放到`files`的第 4 个位置：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/15.png" alt="15" style="zoom:50%;" />

明白了这个原理，**输入重定向**就很好理解了，程序想读取数据的时候就会去`files[0]`读取，所以我们只要把`files[0]`指向一个文件，那么程序就会从这个文件中读取数据，而不是从键盘：

```
$ command < file.txt
```



<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/16.png" alt="16" style="zoom:50%;" />

同理，**输出重定向**就是把`files[1]`指向一个文件，那么程序的输出就不会写入到显示器，而是写入到这个文件中：

```
$ command > file.txt
```

管道符其实也是异曲同工，把一个进程的输出流和另一个进程的输入流接起一条「管道」，数据就在其中传递，不得不说这种设计思想真的很优美：

```
$ cmd1 | cmd2 | cmd3
```

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/17.png" alt="17" style="zoom:50%;" />

## 参考

- [Linux的进程、线程、文件描述符是什么](https://labuladong.gitbook.io/algo/di-wu-zhang-ji-shu-wen-zhang-xi-lie/linux-jin-cheng)

