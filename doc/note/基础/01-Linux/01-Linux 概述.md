## 概述

本文对 Linux 基本原理进行梳理。来源 RunOnLinux.pdf

本文主要采用英文。

## Basic operating systems terms and concepts

### 1、User vs Kernel  

- Execution modes
    - Kernel mode
    - User mode
- Memory protection
    - User space
    - Kernel space

Kernel and user are two terms that are often used in operating systems. Their definition is pretty straight forward: The kernel is the part of the operating system that runs with higher privileges while user (space) usually means by applications running with low privileges(特权).

However these terms are [heavily](大量的) [overloaded](重载) and might have very specific meanings in some contexts.

User mode and kernel mode are terms that may [refer specifically to](具体指) the processor execution mode. Code that runs in kernel mode can fully [[1\]](https://linux-kernel-labs.github.io/refs/heads/master/lectures/intro.html#hypervisor) control the CPU while code that runs in user mode has certain limitations.For example, local CPU interrupts can only be disabled or enable while running in kernel mode. If [such](这样的，此) an operation is attempted while running in user mode an [exception will be generated](异常被产生) and the kernel will [take over to](接管) handle it. 

例如，只能在以内核模式运行时禁用或启用本地CPU中断， 如果在用户模式下运行时尝试执行此操作，则会生成异常，内核将接管该异常。

User space and kernel space may refer specifically to memory protection or to virtual address spaces associated with either the kernel or user applications.	

![04](/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/04.png)

### 2、Typical operating system architecture

In the typical operating system architecture (see the figure below) the operating system kernel is responsible for access and sharing the hardware in a secure and fair manner with multiple applications.

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/03.png" alt="03" style="zoom:50%;" />

The kernel offers a set of APIs that applications issue which are generally referred to as “System Calls”. These APIs are different from regular library APIs because they are the boundary at which the execution mode switch from user mode to kernel mode.

In order to provide application compatibility, system calls are rarely changed. Linux particularly enforces this (as opposed to in kernel APIs that can change as needed).

### 3、 Micro kernel vs Monolithic kernel

A micro-kernel is one where large parts of the kernel are protected from each-other, usually running as services in user space. Because [significant parts of](重要部分) the kernel are now running in user mode, the remaining code that runs in kernel mode is [significantly](相当的，明显的) smaller, [hence](因此) micro-kernel term.

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/05.png" alt="05" style="zoom:50%;" />

A monolithic kernel is one where there is no access protection between the various kernel subsystems and where public functions can be directly called between various subsystems.

### 4、Address space

The address space term is an overload term that can have different meanings in different contexts.

The physical address space refers to the way the RAM and device memories are visible on the memory bus. For example, on 32bit Intel architecture, it is common to have the RAM mapped into the lower physical address space while the graphics card memory is mapped high in the physical address space.

The virtual address space (or sometimes just address space) refers to the way the CPU sees the memory when the virtual memory module is activated (sometime called protected mode or paging enabled). The kernel is responsible of setting up a mapping that creates a virtual address space in which areas of this space are mapped to certain physical memory areas.

Related to the virtual address space there are two other terms that are often used: process (address) space and kernel (address) space.

The process space is (part of) the virtual address space associated with a process. It is the “memory view” of processes. It is a continuous area that starts at zero. Where the process’s address space ends depends on the implementation and architecture.

The kernel space is the “memory view” of the code that runs in kernel mode.

### 5、User and kernel sharing the virtual address space

A typical implementation for user and kernel spaces is one where the virtual address space is shared between user processes and the kernel.

In this case kernel space is located at the top of the address space, while user space at the bottom. In order to prevent the user processes from accessing kernel space, the kernel creates mappings that prevent access to the kernel space from user mode.

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/06.png" style="zoom:50%;" />

### 6、Pageable kernel memory

A kernel supports pageable kernel memory if parts of kernel memory (code, data, stack or dynamically allocated memory) can be swapped to disk.

### 7、Kernel stack

Each [process](进程) has a kernel stack that is used to maintain the function call chain and local variables state while it is executing in kernel mode, as a result of a system call.

The kernel stack is small (4KB - 12 KB) so the kernel developer has to avoid allocating large structures on stack or recursive calls that are not properly bounded.

### 8、Asymmetric MultiProcessing (ASMP)

[Asymmetric MultiProcessing](非对称多处理) (ASMP) is a way of supporting multiple processors (cores) by a kernel, where a processor is dedicated to the kernel and all other processors run user space programs.

 ASMP是内核支持多个处理器（内核）的一种方法，其中一个处理器专用于内核，所有其他处理器运行用户空间程序。

The [disadvantage](缺点) of this approach is that the kernel throughput (e.g. system calls, interrupt handling, etc.) does not scale with the number of processors and [hence](因此) typical processes [frequently](频繁地) use system calls. The scalability of the approach is limited to very specific systems (e.g. [scientific](科学的) applications).

这种方法的缺点是内核吞吐量（例如系统调用，中断处理等）不随处理器数量而增加，因此典型进程经常使用系统调用。该方法的可扩展性仅限于非常特定的系统

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/07.png" alt="07" style="zoom:50%;" />

### 9、Symmetric MultiProcessing (SMP)

As opposed to ASMP, in SMP mode the kernel can run on any of the existing processors, just as user processes. This approach is more difficult to implement, because it creates race conditions in the kernel if two processes run kernel functions that access the same memory locations.

In order to support SMP the kernel must implement synchronization primitives (e.g. spin locks) to guarantee that only one processor is executing a critical section.

在对称多处理架构下，每个处理器的地位都是平等的，对资源的使用权限相同。现代多数的[多处理器](https://zh.wikipedia.org/wiki/多處理器)系统，都采用对称多处理架构，也被称为对称多处理系统（Symmetric multiprocessing system）

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/08.png" style="zoom:50%;" />

### 10、CPU Scalability

CPU scalability refers to how well the performance scales with the number of cores. There are a few things that the kernel developer should keep in mind with regard to CPU scalability:

- Use lock free algorithms when possible
- Use fine grained locking for high contention areas
- Pay attention to algorithm complexity

## Linux source code layout

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/09.png" alt="09" style="zoom:50%;" />

These are the top level of the Linux source code folders:

- arch - contains architecture specific code; each architecture is implemented in a specific sub-folder (e.g. arm, arm64, x86)
- block - contains the block subsystem code that deals with reading and writing data from block devices: creating block I/O requests, scheduling them (there are several I/O schedulers available), merging requests, and passing them down through the I/O stack to the block device drivers
- certs - implements support for signature checking using certificates
- crypto - software implementation of various cryptography algorithms as well as a framework that allows offloading such algorithms in hardware
- Documentation - documentation for various subsystems, Linux kernel command line options, description for sysfs files and format, device tree bindings (supported device tree nodes and format)
- drivers - driver for various devices as well as the Linux driver model implementation (an abstraction that describes drivers, devices buses and the way they are connected)
- firmware - binary or hex firmware files that are used by various device drivers
- fs - home of the Virtual Filesystem Switch (generic filesystem code) and of various filesystem drivers
- include - header files
- init - the generic (as opposed to architecture specific) initialization code that runs during boot
- ipc - implementation for various Inter Process Communication system calls such as message queue, semaphores, shared memory
- kernel - process management code (including support for kernel thread, workqueues), scheduler, tracing, time management, generic irq code, locking
- lib - various generic functions such as sorting, checksums, compression and decompression, bitmap manipulation, etc.
- mm - memory management code, for both physical and virtual memory, including the page, SL*B and CMA allocators, swapping, virtual memory mapping, process address space manipulation, etc.
- net - implementation for various network stacks including IPv4 and IPv6; BSD socket implementation, routing, filtering, packet scheduling, bridging, etc.
- samples - various driver samples
- scripts - parts the build system, scripts used for building modules, kconfig the Linux kernel configurator, as well as various other scripts (e.g. checkpatch.pl that checks if a patch is conform with the Linux kernel coding style)
- security - home of the Linux Security Module framework that allows extending the default (Unix) security model as well as implementation for multiple such extensions such as SELinux, smack, apparmor, tomoyo, etc.
- sound - home of ALSA (Advanced Linux Sound System) as well as the old Linux sound framework (OSS)
- tools - various user space tools for testing or interacting with Linux kernel subsystems
- usr - support for embedding an initrd file in the kernel image
- virt - home of the KVM (Kernel Virtual Machine) hypervisor

## Linux kernel architecture

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/10.png" alt="10" style="zoom:50%;" />

### 1、arch

- Architecture specific code
- May be further sub-divided in machine specific code
- Interfacing with the boot loader and architecture specific initialization
- Access to various hardware bits that are architecture or machine specific such as interrupt controller, SMP controllers, BUS controllers, exceptions and interrupt setup, virtual memory handling
- Architecture optimized functions (e.g. memcpy, string operations, etc.)

This part of the Linux kernel contains architecture specific code and may be further sub-divided in machine specific code for certain architectures (e.g. arm).

```
“Linux was first developed for 32-bit x86-based PCs (386 or higher). These days it also runs on (at least) the Compaq Alpha AXP, Sun SPARC and UltraSPARC, Motorola 68000, PowerPC, PowerPC64, ARM, Hitachi SuperH, IBM S/390, MIPS, HP PA-RISC, Intel IA-64, DEC VAX, AMD x86-64 and CRIS architectures.”
```

It implements access to various hardware bits that are architecture or machine specific such as interrupt controller, SMP controllers, BUS controllers, exceptions and interrupt setup, virtual memory handling.

### 2、Device drivers

The Linux kernel uses a [unified](统一) device model whose purpose is to maintain internal data structures that reflect the state and structure of the system. Such information includes what devices are present, what is their status, what bus they are attached to, to what driver they are attached, etc. This information is essential for implementing system wide power management, as well as device discovery and dynamic device removal.

Linux内核使用统一的设备模型，其目的是维护反映系统状态和结构的内部数据结构。

Each subsystem has its own specific driver interface that is tailored to the devices it represents in order to make it easier to write correct drivers and to reduce code duplication.

Linux supports one of the most diverse set of device drivers type, some examples are: TTY, serial, SCSI, fileystem, ethernet, USB, framebuffer, input, sound, etc.

### 3、Process management

Linux implements the standard Unix process management APIs such as fork(), exec(), wait(), as well as standard POSIX threads.

However, Linux processes and threads are implemented particularly different than other kernels. There are no internal structures implementing processes or threads, instead there is a `struct task_struct` that describe an abstract scheduling unit called task.

A task has pointers to resources, such as address space, file descriptors, IPC ids, etc. The resource pointers for tasks that are part of the same process point to the same resources, while resources of tasks of different processes will point to different resources.

This peculiarity, together with the clone() and unshare() system call allows for implementing new features such as namespaces.

Namespaces are used together with control groups (cgroup) to implement operating system virtualization in Linux.

cgroup is a mechanism to organize processes hierarchically and distribute system resources along the hierarchy in a controlled and configurable manner.

### 4、Memory management

Linux memory management is a complex subsystem that deals with:

- Management of the physical memory: allocating and freeing memory
- Management of the virtual memory: paging, swapping, demand paging, copy on write
- User services: user address space management (e.g. mmap(), brk(), shared memory)
- Kernel services: SL*B allocators, vmalloc

### 5、Block I/O management

The Linux Block I/O subsystem deals with reading and writing data from or to block devices: creating block I/O requests, transforming block I/O requests (e.g. for software RAID or LVM), merging and sorting the requests and scheduling them via various I/O schedulers to the block device drivers.

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/11.png" alt="11" style="zoom:50%;" />

### 6、Virtual Filesystem Switch(虚拟文件系统转换)

The Linux Virtual Filesystem Switch implements common / generic filesystem code to reduce duplication in filesystem drivers. It introduces certain filesystem abstractions such as:

- inode - describes the file on disk (attributes, location of data blocks on disk)
- dentry - links an inode to a name
- file - describes the properties of an opened file (e.g. file pointer)
- superblock - describes the properties of a formatted filesystem (e.g. number of blocks, block size, location of root directory on disk, encryption, etc.)

The Linux VFS also implements a complex caching mechanism which includes the following:

- the inode cache - caches the file attributes and internal file metadata
- the [dentry](目录项) cache - caches the directory hierarchy of a filesystem
- the page cache - caches file data blocks in memory

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/12.png" alt="12" style="zoom:50%;" />

用户通过cp命令进行文件的拷贝，对用户来说是不用关心底层是否跨越文件系统和设备的，具体都通过VFS抽象层实现对不同文件系统的读写操作。

### 7、Networking stack

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/基础/13.png" alt="13" style="zoom:50%;" />

### 8、磁盘预读

由于存储介质的特性，磁盘本身存取就比主存慢很多，再加上机械运动耗费，磁盘的存取速度往往是主存的几百分分之一，因此为了提高效率，要尽量减少磁盘I/O。为了达到这个目的，磁盘往往不是严格按需读取，而是每次都会预读，即使只需要一个字节，磁盘也会从这个位置开始，顺序向后读取一定长度的数据放入内存。这样做的理论依据是计算机科学中著名的局部性原理：

- 当一个数据被用到时，其附近的数据也通常会马上被使用。
- 程序运行期间所需要的数据通常比较集中。
- 由于磁盘顺序读取的效率很高（不需要寻道时间，只需很少的旋转时间），因此对于具有局部性的程序来说，预读可以提高I/O效率。

预读的长度一般为页（page）的整倍数。页是计算机管理存储器的逻辑块，硬件及操作系统往往将主存和磁盘存储区分割为连续的大小相等的块，每个存储块称为一页（在许多操作系统中，页得大小通常为4k），主存和磁盘以页为单位交换数据。当程序要读取的数据不在主存中时，会触发一个缺页异常，此时系统会向磁盘发出读盘信号，磁盘会找到数据的起始位置并向后连续读取一页或几页载入内存中，然后异常返回，程序继续运行。

## 小结

文本只是走马观花式的对 Linux 涉及到的知识点做个概览，并未做太多深入的分析，实际上Linux涉及到的知识面太广，比如Understanding.The.Linux.Kernel 900多页，网上有英文版的pdf。

## 参考

- [Linux Kernel Teaching](https://linux-kernel-labs.github.io/refs/heads/master/lectures/intro.html)
- [Linux系统结构详解](https://blog.csdn.net/hguisu/article/details/6122513)
- [计算机体系结构NUMA架构详解](https://houmin.cc/posts/b893097a/)

