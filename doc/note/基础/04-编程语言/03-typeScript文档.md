## 概述

文本是通过GPT产生，prompt:

> 帮我产生一份15分钟以内的ts教程，主要介绍它的语法，关键字，要简练，不需要介绍安装环境，我已经有python、java相关编程经验



以下是一个针对有 Python 和 Java 编程经验者设计的 **15 分钟 TypeScript（TS）教程**，主要介绍语法和关键字，重点突出 TS 的特性。


## **1. TypeScript 简介**

TypeScript 是 JavaScript 的超集，提供静态类型检查，可以编译成纯 JavaScript。TS 的主要优势是通过类型提高代码的可维护性和可读性。



## **2. 基础语法**

### **2.1 变量声明**

- 使用 `let`、`const` 替代 `var`（与 Java 一致）。
- **类型注解**：`变量名: 类型`。

```typescript
let age: number = 30;        // 明确类型
const name: string = "<PERSON>"; // 常量
let isHappy: boolean = true; // 布尔值
```

如果未声明类型，TS 会根据赋值自动推断类型：

```typescript
let city = "Beijing"; // 推断为 string
```



### **2.2 常见类型**

| **类型**  | **示例**                                          |
| --------- | ------------------------------------------------- |
| `number`  | `let x: number = 42;`                             |
| `string`  | `let y: string = "Hello";`                        |
| `boolean` | `let z: boolean = true;`                          |
| `any`     | `let data: any = 123;`                            |
| `array`   | `let arr: number[] = [1,2,3];` 或 `Array<number>` |
| `tuple`   | `let tuple: [string, number] = ["TS", 2024];`     |
| `enum`    | 定义一组固定值（类似 Java 的 `enum`）             |

**枚举示例**：

```typescript
enum Role {
  Admin,
  User,
  Guest
}
let role: Role = Role.Admin; // role = 0
```



### **2.3 函数**

- 类型注解包括**参数**和**返回值**。

```typescript
function add(x: number, y: number): number {
  return x + y;
}
```

- 可选参数使用 `?`，默认参数和 Python 类似。

```typescript
function greet(name: string = "Guest"): void {
  console.log(`Hello, ${name}`);
}
```

- **箭头函数**（与 Java Lambda 类似）：

```typescript
const multiply = (a: number, b: number): number => a * b;
```


### **2.4 接口**

TS 的接口类似于 Java 的接口，用于定义对象的形状。

```typescript
interface Person {
  name: string;
  age: number;
  greet(): void; // 方法声明
}

const john: Person = {
  name: "John",
  age: 30,
  greet() {
    console.log("Hello!");
  },
};
```



### **2.5 类**

TS 类和 Java 类非常类似，支持访问修饰符（`public`、`private`、`protected`）。

```typescript
class Animal {
  private name: string; // 只能在类内访问
  constructor(name: string) {
    this.name = name;
  }
  public speak(): void {
    console.log(`${this.name} makes a sound.`);
  }
}
```

- **继承与方法重写**：

```typescript
class Dog extends Animal {
  constructor(name: string) {
    super(name); // 调用父类构造器
  }
  speak(): void {
    console.log(`${this.name} barks.`);
  }
}
```


## **3. 类型系统**

### **3.1 联合类型**

允许变量有多种类型，用 `|` 分隔。

```typescript
let id: number | string;
id = 42;
id = "TS";
```


### **3.2 类型别名**

定义一个类型的别名，用 `type` 关键字。

```typescript
type StringOrNumber = string | number;
let value: StringOrNumber = 42;
```


### **3.3 泛型**

类似 Java 的泛型，用于创建类型安全的函数或类。

```typescript
function identity<T>(value: T): T {
  return value;
}
let output = identity<string>("TS"); // 明确指定类型
```



## **4. 高级特性**

### **4.1 类型断言**

告诉 TS 一个变量的具体类型（类似 Java 的强制类型转换）。

```typescript
let value: any = "TS";
let strLength: number = (value as string).length; // 类型断言
```



### **4.2 模块化**

TS 支持 ES 模块系统，使用 `export` 和 `import`。

```typescript
// math.ts
export function add(x: number, y: number): number {
  return x + y;
}

// main.ts
import { add } from './math';
console.log(add(1, 2));
```

可以把这个理解为java的package和import，如果一个class或者方法 前面有export才能被其他文件import



### **4.3 装饰器**

类似 Java 的注解，用于类或方法的扩展（需开启 `experimentalDecorators` 选项）。

```typescript
function Log(target: any, key: string) {
  console.log(`${key} was called`);
}

class Calculator {
  @Log
  add(a: number, b: number): number {
    return a + b;
  }
}
```



## **5. 类型检查和错误提示**

TypeScript 强调**静态检查**。如果代码类型不匹配，TS 会在编译时报错。

```typescript
let num: number = "string"; // 错误：类型不匹配
```



### **总结**

- TypeScript 结合了 Java 的强类型和 JavaScript 的灵活性。
- 通过类型注解、接口、类、泛型等特性，TS 提供了更安全和高效的编程体验。

短时间内掌握以上内容，已能胜任多数基础开发任务！