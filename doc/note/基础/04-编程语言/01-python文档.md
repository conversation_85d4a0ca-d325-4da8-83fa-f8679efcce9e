## 概述

记录遇到的 python 问题 



## pydantic

`pydantic` 是一个用于 Python 的类型检查和数据验证库，它允许你定义数据模型并自动验证输入数据。这个库主要用于简化数据解析和验证的工作，特别是在 Web 开发中，它可以与各种 Web 框架（如 FastAPI、Django、Flask 等）一起使用。



## Conda

conda 是 Python 软件包管理系统和环境管理系统。

用法：

```shell
conda env list #查看环境
conda activate devopsgpt  #切换环境
conda remove -n  cover-agent --all #删除环境
```

查看某个package的信息：

 ```
 pip show torch
 ```



IDEA设置

注意在Cursor/Vscode中需要设置正确的python path，通过命令面板 (最快):

```
Cmd/Ctrl + Shift + P
> Python: Select Interpreter
```





## Streamlit

streamlit： The **fastest** way to build and share data apps

参考： [数据可视化框架](https://github.com/HelloGitHub-Team/Article/blob/master/contents/Python/Streamlit/content.md)

安装：

```
pip install streamlit
streamlit hello
```

使用：

```
streamlit run app.py --server.headless true
```

## Pandas

Pandas DataFrame是一个二维表格型数据结构，它在Python的数据分析库Pandas中扮演着核心角色。DataFrame可以被看作是一个表格，其中每一列可以是不同的数据类型（数值、字符串、布尔值等），这使得它非常适合处理现实世界中的数据。

简单示例：

```python 
import pandas as pd

# 示例DataFrame
df = pd.DataFrame({
    'ColumnA': [1, 2, 3],
    'ColumnB': ['a', 'b', 'c']
})

# 访问名为'ColumnA'的列
column_a = df['ColumnA']
print(column_a)
```

这将输出：

```
0    1
1    2
2    3
Name: ColumnA, dtype: int64
```

通过行索引有两种方式：

```python
value = df.iloc[0]["ColumnA"]  # 使用基于整数的位置索引
# 或者
value = df.loc[0, "ColumnA"]    # 使用基于标签的索引，假设行的标签是整数

df.loc[0]["ColumnA"] #错误用法
```

总结一下，正确的用法应该是：

- 使用 `.iloc` 来访问基于整数位置的行数据。
- 使用列名作为键来访问基于列名的列数据。
- 使用 `.loc` 来访问基于标签的行和列数据。

