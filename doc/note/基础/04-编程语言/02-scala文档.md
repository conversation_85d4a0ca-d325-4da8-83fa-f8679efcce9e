## 函数式编程

```scala
private def maybeRemoveFromZkReassignment(shouldRemoveReassignment: 
                                          (TopicPartition, Seq[Int]) => Boolean): Unit = {
	
    val reassigningPartitions = zkClient.getPartitionReassignment
    val (removingPartitions, updatedPartitionsBeingReassigned) = reassigningPartitions.partition { case (tp, replicas) =>
      shouldRemoveReassignment(tp, replicas)
    }
}
```

shouldRemoveReassignment 是一个函数类型，接受两个参数：一个是 TopicPartition 类型的对象，另一个是 Seq[Int] 类型的序列。该函数返回一个布尔值。





##  -> 和 => 的区别

在Scala中，`->` 和 `=>` 是两个不同的符号，具有不同的含义和用途。

1. `->`: `->` 是一个特殊的操作符，用于创建键值对（key-value pair）。通常用于构建元组，其中左侧是键，右侧是值。例如：

   ```
   scalaCopy code
   val pair = "key" -> "value"
   ```

   在这个例子中，`pair` 就是一个包含两个元素的元组，`"key"` 是第一个元素（键），`"value"` 是第二个元素（值）。

2. `=>`: `=>` 是一个用于定义匿名函数的箭头符号。在函数字面量中，它分隔了参数列表和函数体。例如：

   ```
   scalaCopy code
   val addOne: Int => Int = (x: Int) => x + 1
   ```

   在这个例子中，`Int => Int` 是函数类型，表示接受一个 `Int` 类型的参数并返回一个 `Int` 类型的结果。箭头 `=>` 将参数列表和函数体分隔开来，`(x: Int)` 是参数列表，`x + 1` 是函数体。

总结：

- `->` 用于创建键值对。
- `=>` 用于定义匿名函数。