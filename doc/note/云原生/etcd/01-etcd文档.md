## 概述



## 安装

### centos 安装

在CentOS上就有对应的软件包 

```
yum -y install etcd
```

启动测试

```
# start a local etcd server
/usr/local/etcd/etcd

# write,read to etcd
/usr/local/etcd/etcdctl --endpoints=localhost:2379 put foo bar

/usr/local/etcd/etcdctl --endpoints=localhost:2379 get foo
```



### docker部署

最方便的就是使用docker进行部署的，适合用于测试

```
docker pull quay.io/coreos/etcd
docker run -d -p 2379:2379 -p 2380:2380 --name etcd  quay.io/coreos/etcd
```

测试

```
docker exec etcd /bin/sh -c "/usr/local/bin/etcd --version"

docker exec etcd /bin/sh -c "export ETCDCTL_API=3 ; /usr/local/bin/etcdctl version"

docker exec etcd /bin/sh -c "export ETCDCTL_API=3 ; /usr/local/bin/etcdctl endpoint health"
```



## 基本操作

### etcdctl

etcdctl 是etcd官方提供的客户端，使用语法如下:

```
etcdctl [global options] command [command options] [arguments...]
```

通过 --endpoints=// 设置server地址，例如：

```
etcdctl  --endpoints=//************:2379 ls /bds/metric/exp_alarm_v2/
```

输出:

```
# etcdctl  --endpoints=//************:2379 ls /bds/metric/exp_alarm_v2/
/bds/metric/exp_alarm_v2/default
/bds/metric/exp_alarm_v2/finance-switch-shoujidai
/bds/metric/exp_alarm_v2/metric_alarm
/bds/metric/exp_alarm_v2/scf2
```

常用命令：

- member list。 查看集群成员
- ls 。查看列表。支持参数：--recursive 如果目录下有子目录，则递归输出其中的内容。

### HTTP API

etcd 还支持直接通过http API访问

- http请求查看key

```
curl  http://************:2379/v2/keys/bds/metric/exp_alarm_v2/default
```

输出:

```
{"action":"get","node":{"key":"/bds/metric/exp_alarm_v2/default","dir":true,"nodes":[{"key":"/bds/metric/exp_alarm_v2/default/com.wacai.pt","dir":true,"modifiedIndex":1357000957,"createdIndex":1357000957},{"key":"/bds/metric/exp_alarm_v2/default/com.wacai.metric","dir":true,"modifiedIndex":723458809,"createdIndex":723458809},{"key":"/bds/metric/exp_alarm_v2/default/com.wacai.stanlee","dir":true,"modifiedIndex":723458813,"createdIndex":723458813},{"key":"/bds/metric/exp_alarm_v2/default/com.waci.metric","dir":true,"modifiedIndex":723458818,"createdIndex":723458818},{"key":"/bds/metric/exp_alarm_v2/default/groupId","dir":true,"modifiedIndex":723458810,"createdIndex":723458810},{"key":"/bds/metric/exp_alarm_v2/default/com.wacai.creditcard","dir":true,"modifiedIndex":852391671,"createdIndex":852391671}],"modifiedIndex":723458809,"createdIndex":723458809}}
```



- 查看版本

```
curl  http://************:2379/version
```

输出:

```
{"etcdserver":"2.3.7","etcdcluster":"2.3.0"}
```



### jurmous/etcd4j

jurmous/etcd4j 是基于netty的etcd客户端，不过看上去并不活跃，老版本还存在 recursive  被完全block住的bug，新版本更新之后才发现是一次 recursive超过了数据最大限制。 详见这篇文章:[ETCD客户端（ETCD4J）问题的定位](https://cloud.tencent.com/developer/article/1811850) 

这个问题困扰我了一天，还是官网的文档看得不仔细：

> You can set the following parameters in a config:
>
> - Connect timeout: The timeout of the Netty client itself. Default is 300ms
> - Max Frame size: The max frame size of the packages. Default is 100KiB (100 * 1024)

Google: jurmous/etcd4j 用法

初始化：

```
URI[] uris = new URI[urls.length];
IntStream.range(0, urls.length).forEach(i -> {
uris[i] = URI.create(urls[i]);
});

EtcdNettyConfig config = new EtcdNettyConfig();
config.setMaxFrameSize(3*1024*1024);//3MB
return new EtcdClient( new EtcdNettyClient(config,uris));
```



查询节点:

```java
String etcdPath = "/bds/metric/exp_alarm_v2/";
EtcdKeysResponse rsp = etcdClient.get(etcdPath).timeout(5000,TimeUnit.MILLISECONDS).send().get();
//显示所有子节点keys
List<String> nodes = rsp.getNode().getNodes().stream().map(EtcdKeysResponse.EtcdNode::getKey).collect(Collectors.toList());
```



recursive查询节点:

```java
String p = "/bds/metric/exp_alarm_v2/default"
EtcdKeysResponse resp = etcdClient.get(p).recursive().timeout(500, TimeUnit.MILLISECONDS).send().get();

List<EtcdKeysResponse.EtcdNode> childNode = resp.node.getNodes();
```

