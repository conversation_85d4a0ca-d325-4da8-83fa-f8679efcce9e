### 为什么用 etcd 而不用ZooKeeper？
阅读了“ZooKeeper 典型应用场景一览”一文的读者可能会发现，etcd 实现的这些功能，ZooKeeper都能实现。那么为什么要用 etcd 而非直接使用ZooKeeper呢？

相较之下，ZooKeeper有如下缺点：

复杂。ZooKeeper的部署维护复杂，管理员需要掌握一系列的知识和技能；而 Paxos 强一致性算法也是素来以复杂难懂而闻名于世；另外，ZooKeeper的使用也比较复杂，需要安装客户端，官方只提供了 Java 和 C 两种语言的接口。
Java 编写。这里不是对 Java 有偏见，而是 Java 本身就偏向于重型应用，它会引入大量的依赖。而运维人员则普遍希望保持强一致、高可用的机器集群尽可能简单，维护起来也不易出错。
发展缓慢。Apache 基金会项目特有的“Apache Way”在开源界饱受争议，其中一大原因就是由于基金会庞大的结构以及松散的管理导致项目发展缓慢。
而 etcd 作为一个后起之秀，其优点也很明显。

简单。使用 Go 语言编写部署简单；使用 HTTP 作为接口使用简单；使用 Raft 算法保证强一致性让用户易于理解。
数据持久化。etcd 默认数据一更新就进行持久化。
安全。etcd 支持 SSL 客户端安全认证。



### ETCD 架构
图片[https://static001.infoq.cn/resource/image/cf/94/cf0851c4bcbd2555d09674e7e2a07394.jpg]

从 etcd 的架构图中我们可以看到，etcd 主要分为四个部分。

HTTP Server： 用于处理用户发送的 API 请求以及其它 etcd 节点的同步与心跳信息请求。
Store：用于处理 etcd 支持的各类功能的事务，包括数据索引、节点状态变更、监控与反馈、事件处理与执行等等，是 etcd 对用户提供的大多数 API 功能的具体实现。
Raft：Raft 强一致性算法的具体实现，是 etcd 的核心。
WAL：Write Ahead Log（预写式日志），是 etcd 的数据存储方式。除了在内存中存有所有数据的状态以及节点的索引以外，etcd 就通过 WAL 进行持久化存储。WAL 中，所有的数据提交前都会事先记录日志。Snapshot 是为了防止数据过多而进行的状态快照；Entry 表示存储的具体日志内容。
通常，一个用户的请求发送过来，会经由 HTTP Server 转发给 Store 进行具体的事务处理，如果涉及到节点的修改，则交给 Raft 模块进行状态的变更、日志的记录，然后再同步给别的 etcd 节点以确认数据提交，最后进行数据的提交，再次同步。


通常，一个用户的请求发送过来，会经由 HTTP Server 转发给 Store 进行具体的事务处理，如果涉及到节点的修改，则交给 Raft 模块进行状态的变更、日志的记录，然后再同步给别的 etcd 节点以确认数据提交，最后进行数据的提交，再次同步。


### Eureka 架构

https://img-blog.csdnimg.cn/20190601011708827.png?x-oss-process=image/watermark,type_ZmFuZ3poZW5naGVpdGk,shadow_10,text_aHR0cHM6Ly9mb3JlenAuYmxvZy5jc2RuLm5ldA==,size_16,color_FFFFFF,t_70

http://yeming.me/2016/12/01/eureka1/
http://yeming.me/2016/12/02/eureka2/
https://blog.csdn.net/forezp/article/details/73017664	深入理解Eureka之源码解析
https://pkaq.org/2017/08/06/sc-eureka/



#### Sofa-Registry架构

#### Nacos 架构 
https://nacos.io/zh-cn/docs/architecture.html
HTTP 服务注册+健康检查


### CoreDNS 架构
CoreDNS是Golang编写的一个插件式DNS服务器，是Kubernetes 1.13 后所内置的默认DNS服务器.
架构图(https://img.draveness.me/2018-11-07-coredns-architecture.png)
https://draveness.me/dns-coredns


### Harmony架构
HTTP 服务注册+健康检查

#### 实现细节
1. domain 模糊查询通过内部维护一个H2MemDb实现。（http://harmony-node-8004.middleware.k2.test.wacai.info/api/domain/pageQuery?pageSize=30&pageIndex=1&status=1&domain=jasmineserver）



### 各大开源产品比较

|                      | Consul                 | zookeeper             | etcd            | euerka          |
| :------------------- | :--------------------- | :-------------------- | :-------------- | :-------------- |
| 服务健康检查         | 服务状态，内存，硬盘等 | (弱)长连接，keepalive | 连接心跳        | 可配支持        |
| kv存储服务           | 支持                   | 支持                  | 支持            | —               |
| 一致性               | raft                   | zab                   | raft            | —               |
| cap                  | ca                     | cp                    | cp              | ap              |
| 使用接口(多语言能力) | 支持http和dns          | 客户端                | http/grpc       | http（sidecar） |
| watch支持            | long polling           | 支持                  | long polling    | long polling    |
| 自身监控             | metrics                | —                     | metrics         | metrics         |
| 安全                 | acl /https             | acl                   | https支持（弱） | —               |

详见：https://luyiisme.github.io/2017/04/22/spring-cloud-service-discovery-products/



### 参考
https://www.infoq.cn/article/etcd-interpretation-application-scenario-implement-principle