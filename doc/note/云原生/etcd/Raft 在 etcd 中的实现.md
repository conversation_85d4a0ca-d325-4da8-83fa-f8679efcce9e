## 概述

[Raft](https://en.wikipedia.org/wiki/Raft_(computer_science))是近年来比较流行的一个一致性算法。它的原理比较容易理解，网上也有很多相关的介绍，因此这里我就不再啰嗦原理了，而是打算以raft在etcd中的实现[1](https://blog.betacat.io/post/raft-implementation-in-etcd/#fn:1)为例，从工程的角度来讲讲这个算法的一个具体实现，毕竟了解原理只算是“纸上谈兵”，离真正能把它应用起来还有很长一段距离。





## 参考

- [Raft 在 etcd 中的实现](https://blog.betacat.io/post/raft-implementation-in-etcd/)
- [视频教程](https://www.youtube.com/watch?v=sL02PsR20gE&feature=emb_title)

