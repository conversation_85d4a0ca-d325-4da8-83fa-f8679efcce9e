## 问题

在k8s 的dns具体是如何实现的？

比如请求一个service

![image-20220607102407468](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220607102407468.png)

返回的ip ************是什么？其实对应了是 cluster-ip:

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220607102604314.png" alt="image-20220607102604314" style="zoom:80%;" />

dns的解析 配置在 /etc/resolv.conf文件中

```
nameserver ***********
search quantum.svc.cluster.local svc.cluster.local cluster.local
options ndots:5
```

*********** 这个ip实际上是 coredns的 service

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220607102940066.png" alt="image-20220607102940066" style="zoom:50%;" />



再做一个实验，两个应用 consumer(*************) 和 provider(*************) 通过nc 建立长连接，通过 netstat 观测

执行如下命令：

```shell
 nc -l 1234 # provider端
 nc quantum-example-provider 1234 #consumer
```

consumer 端观测，consumer 是和 clusterIp建立长连接

```shell
[root@quantum-example-consumer-577645c574-b6jvc 1.0-SNAPSHOT]# netstat -antp | grep 1234
tcp        0      0 *************:42402         ************:1234           ESTABLISHED 3540/nc 
```



在 provider 端观测，provider 却是直接和consumer建立的连接

```shell
[root@quantum-example-provider-6f96b9548b-xscgt 1.0-SNAPSHOT]# netstat -antp | grep 1234
tcp        0      0 0.0.0.0:1234                0.0.0.0:*                   LISTEN      566/nc              
tcp        0      0 *************:1234          *************:42402         ESTABLISHED 566/nc  
```





## 参考

- [understanding-kubernetes-networking](https://medium.com/google-cloud/understanding-kubernetes-networking-pods-7117dd28727)
- [kubernetes-handbook](https://jimmysong.io/kubernetes-handbook/concepts/networking.html)

