## 概述

Ingress 是从Kubernetes 集群外部访问集群内部服务的入口。k8s定义了 Ingress 对象用于描述入口规则，ingress-nginx-controller 会根据 Ingress规则来请求具体的service，从命名也能看出 Ingress 是通过 nginx 实现的。

## 架构

下图描述了一个请求是如何从外部进入到 k8s内部 service的。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211225230416631.png" alt="image-20211225230416631" style="zoom:50%;" />

**需要注意的是ingress不是直接访问service，而是有一个定时任务把service解析成endpoint也就是podIp，然后直接访问pod**

```shell
# kubectl get svc -n kube-system
NAME                                 TYPE        CLUSTER-IP     EXTERNAL-IP   PORT(S)                      AGE
ingress-nginx-controller             NodePort    ***********    <none>        80:30080/TCP,443:30443/TCP   281d
ingress-nginx-controller-admission   ClusterIP   ************   <none>        443/TCP                      281d
ingress-nginx-controller-metrics     ClusterIP   ************   <none>        9913/TCP                     281d
kube-dns                             ClusterIP   ***********    <none>        53/UDP,53/TCP,9153/TCP       282d
```

ingress-nginx-controller 通过nodePort 暴露30080 端口给nginx

## Ingress 配置

一个简单的 Ingress 配置如下:

```
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: biz-app-wse-test-middleware-wke-office-test-wacai-info
  namespace: wse-test-middleware
spec:
  rules:
  - host: biz-app.wse-test-middleware.wke-office.test.wacai.info
    http:
      paths:
      - backend:
          serviceName: biz-app
          servicePort: 8080
        path: /
        pathType: Prefix

```

这个配置描述了通过 biz-app.wse-test-middleware.wke-office.test.wacai.info 访问会访问 biz-app 这个服务。 

