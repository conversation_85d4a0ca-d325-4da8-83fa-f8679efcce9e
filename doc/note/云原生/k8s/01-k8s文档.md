

## 记录

Yams
```
apiVersion: apps/v1
kind: Deployment
metadata:
  name: bairen-app
  namespace: bairen
  labels:
    app: bairen-app
spec:
  replicas: 1
  selector:
    matchLabels:
      app: bairen-app
  template:
    metadata:
      labels:
        app: bairen-app
    spec:
      containers:
      - name: bairen-app
        image: nginx
        #command: [ "sh", "-c", "sleep 1h" ]
        securityContext:
          capabilities:
            add: ["SYS_ADMIN"]
```



```
alpine 是一个精简版的linux，它也有包管理器apk，比如 apk add tcpdump
```



```
OCI runtime exec failed: exec failed: container_linux.go:349: starting container process caused "exec: \"bash\": executable file not found in $PATH": unknown
```

搜索：

```
use linux perf tool in k8s
```



## 网络方案

### 2层方案

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/3-7498242.png" alt="3" style="zoom:67%;" />

2层本质是pod和node的ip同网段。

这就要求在物理2层，sdn需要能够代答pod ip的arp请求，将其mac解析为pod所在node的mac地址，从而通过2层网络打通流量。

也可以理解为2层是通过网卡直接通信

### 3层方案

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/4.png" alt="4" style="zoom:60%;" />

3层方案则pod和node ip不同，其本质是在node下发pod ip的路由规则，令pod ip可以物理3层送达pod所在node。

通常我们node不会跨网段，此时实际还是2层互通能力。一旦node跨网段则需要走3层路由器来跨网，此时3层方案才会体现其优势。

### 各自的缺点

2层方案因为需要ARP代答（欺诈），所以容易坑在2层ARP各种问题，目前我接触到的就是POD IP复用，导致mac地址缓存错误问题。

3层方案会遇到路由表规模问题，默认需要在每个node下发集群所有pod的路由规则，数量很多。此时可以通过给每个node控制一个子网段来减少路由规则的数目。





## 参考

- [水寒整理](https://wiki.opskumu.com/kubernetes/arch)
- [better-way-to-use-perf-on-a-program-in-kubernetes-docker](https://serverfault.com/questions/931743/better-way-to-use-perf-on-a-program-in-kubernetes-docker)

