## 概念

**Request:** 容器使用的最小资源需求，作为容器调度时资源分配的判断依赖。只有当节点上可分配资源量>=容器资源请求数时才允许将容器调度到该节点。

**Limit:** 容器能使用资源的资源的最大值，设置为0表示使用资源无上限。

Request能够保证Pod有足够的资源来运行，而Limit则是防止某个Pod无限制地使用资源，导致其他Pod崩溃。

内存一般只支持设置Request，Limit必须强制等于Request，这样确保容器不会因为内存的使用量超过了Request但没有超过Limit的情况下被意外的Kill掉。

Kubernetes中资源通过Request和Limit的设置，能够实现容器对资源的更高效的使用。如果在多个容器同时对资源进行充分利用，资源使用尽量的接近Limit。这个时候Node节点上的资源总量要小于所有Pod中Limit的总会，就会发生资源抢占。

## 样例

```
    image: dockerhub.test.wacai.info/wse/wse-pause:0.1.0-f77ff25
    imagePullPolicy: IfNotPresent
    name: jasmine-server
    resources:
      limits:
        cpu: "2"
        memory: 2Gi
      requests:
        cpu: 250m
```

以上表示该容器要求的cpu最小资源250m，最大限制2c;



## 参考

- [Kubernetes 资源分配之 Request 和 Limit 解析](https://cloud.tencent.com/developer/article/1004976)