## changeLog

可以参考这篇重新整理：https://jared-says.cn/posts/kubernetes-resources/



## 参考

- [Kubernetes指南](https://kubernetes.feisky.xyz/)

##  设计理念

- 所有内容都是API对象
- 采用声明式语言，不要告诉k8s应该执行什么操作，而是声明性地改变系统的期望状态

## 核心组件

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/k8s/4.png" alt="4" />

Kubernetes 主要由以下几个核心组件组成：

- etcd 保存了整个集群的状态；
- apiserver 提供了资源操作的唯一入口，并提供认证、授权、访问控制、API 注册和发现等机制；
- controller manager 负责维护集群的状态，比如故障检测、自动扩展、滚动更新等；
- scheduler 负责资源的调度，按照预定的调度策略将 Pod 调度到相应的机器上；
- kubelet 负责维护容器的生命周期，同时也负责 Volume（CVI）和网络（CNI）的管理；
- Container runtime 负责镜像管理以及 Pod 和容器的真正运行（CRI）；
- kube-proxy 负责为 Service 提供 cluster 内部的服务发现和负载均衡;

除了核心组件，还有一些推荐的 Add-ons：

- kube-dns 负责为整个集群提供 DNS 服务
- Ingress Controller 为服务提供外网入口
- Heapster 提供资源监控
- Dashboard 提供 GUI
- Federation 提供跨可用区的集群
- Fluentd-elasticsearch 提供集群日志采集、存储与查询

## 体系结构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/k8s/6.png" alt="6" style="zoom:33%;" />

每一个 Kubernetes 就集群都由一组 Master 节点和一系列的 Worker 节点组成，其中 Master 节点主要负责存储集群的状态并为 Kubernetes 对象分配和调度资源。

### Master

![kubernetes-master-node](https://img.draveness.me/2018-11-25-kubernetes-master-node.png)

作为管理集群状态的 Master 节点，它主要负责接收客户端的请求，安排容器的执行并且运行控制循环，将集群的状态向目标状态进行迁移，Master 节点内部由三个组件构成：

- API 服务器：负责处理来自用户的请求，包括用于查看集群状态的读请求以及改变集群状态的写请求，也是唯一一个与 etcd 集群通信的组件。
- 调度器：为 Kubernetes 中运行的 Pod 选择部署的 Worker 节点，它会根据用户的需要选择最能满足请求的节点来运行 Pod，它会在每次需要调度 Pod 时执行。
- 控制器：运行了一系列的控制器进程，这些进程会按照用户的期望状态在后台不断地调节整个集群中的对象，当服务的状态发生了改变，控制器就会发现这个改变并且开始向目标状态迁移。

注意：注意调度器只是修改了ected中的数据，不会实际执行pod调度，具体的部署资源工作交给了控制器。

### Worker

![kubernetes-worker-node](https://img.draveness.me/2018-11-25-kubernetes-worker-node.png)

其他的 Worker 节点实现就相对比较简单了，它主要由 kubelet 和 kube-proxy 两部分组成：
- Kebelet：周期性地从 API Server 接受新的或者修改的 Pod 规范并且保证节点上的 Pod 和其中容器的正常运行，还会保证节点会向目标状态迁移，该节点仍然会向 Master 节点发送宿主机的健康状况。
- Kube-proxy：运行在各个节点上的代理服务 kube-proxy 负责宿主机的子网管理，同时也能将服务暴露给外部，其原理就是在多个隔离的网络中把请求转发给正确的 Pod 或者容器。

## pod

Pod 是 Kubernetes 中最基本的概念，它也是 Kubernetes 对象模型中我们可以创建或者部署的最小并且最简单的单元。

![kubernetes-pod](https://img.draveness.me/2018-11-25-kubernetes-pod.png)

它将应用的容器、存储资源以及独立的网络 IP 地址等资源打包到了一起，表示一个最小的部署单元，但是每一个 Pod 中的运行的容器可能不止一个，这是因为 Pod 最开始设计时就能够在多个进程之间进行协调，构建一个高内聚的服务单元，这些容器能够共享存储和网络，非常方便地进行通信。

pod网络通信包括

- 虚拟ethernet
- overlday
- uunderlay

### 为什么要引入pod，不直接使用容器？

原始是容器设计为一个进程一个容器的形式，这样便于管理，想想一下如果一个容器多个进程，某个进程奔溃之后需要恢复是非常麻烦的事情。所以需要另外一种更抽象的的结构来讲多个容器绑定在一起，并将它们作为一个单元进行管理。

k8s通过配置docker使得一个pod中的多个容器之间共享相同的主机名和网络接口。所以容器可以通过localhost与同一个pod的其他容器进行通信。

### 如何创建pod

有两种方式创建pod

通过命令直接创建单个pod

```java
kubectl run my_pod --image=tutum/dsutils -- generator=run-pod/v1 --command --sleep infinity
```

通过yaml

```java
kubectl create -f xxx.yaml    
```

### 如何删除pod

删除单个pod

```java
kubectl delete po app
```

通过标签选择器批量删除pod

```java
kubectl delete po app -l env=test1
```

通过删除ns来删除ns下所有的pod

```java
kubectl delete ns custom-ns
```

删除ns下所有pod

```java
kubectl delete po -all -n custom-ns
```

## 控制器

Kubernetes 中的控制器是用于创建和管理 Pod 的实例，能够在集群的层级提供复制、发布以及健康检查的功能，这些控制器其实都运行在 Kubernetes 集群的主节点上。

在 Kuberentes 的 [kubernetes/pkg/controller/](https://github.com/kubernetes/kubernetes/tree/master/pkg/controller) 目录中包含了官方提供的一些常见控制器，我们可以通过下面这个函数看到所有需要运行的控制器：

```java
func NewControllerInitializers(loopMode ControllerLoopMode) map[string]InitFunc {
	controllers := map[string]InitFunc{}
	controllers["endpoint"] = startEndpointController
	controllers["replicationcontroller"] = startReplicationController
	controllers["podgc"] = startPodGCController
	controllers["resourcequota"] = startResourceQuotaController
	controllers["namespace"] = startNamespaceController
	controllers["serviceaccount"] = startServiceAccountController
	controllers["garbagecollector"] = startGarbageCollectorController
	controllers["daemonset"] = startDaemonSetController
	controllers["job"] = startJobController
	controllers["deployment"] = startDeploymentController
	controllers["replicaset"] = startReplicaSetController
	controllers["horizontalpodautoscaling"] = startHPAController
	controllers["disruption"] = startDisruptionController
	controllers["statefulset"] = startStatefulSetController
	controllers["cronjob"] = startCronJobController
	// ...

	return controllers
}
```

这些控制器会随着控制器管理器的启动而运行，它们会监听集群状态的变更来调整集群中的 Kuberentes 对象的状态，在后面的文章中我们会展开介绍一些常见控制器的实现原理。

## 标签 label

### 为什么需要lable?

当pod数量越来越多，pod内部的可能存在不同发布版本，需要有一种机制来组织这些pod。

### 标签选择器

```java
kubectl get po -l env=prod
```

lable 除了可以被添加到pod上还可以添加到node上。

```java
kubectl label node 172.16.64.47 gpu=true
```

通过标签选择器可以让特定的pod被调度到指定的node上，通过编辑yaml实现。

## node

node 在k8s中表示机器，通过kubectl get nodes 可以查看集群的node信息

## namespae

- 为资源名称提供了作用域
- 可以限定ns使用资源的上限

 ### 如何创建ns?

- 在yaml中定义
- 通过kubectl create namespace

### 如何在创建资源时候指定 ns?

- 在yaml的metadata 字段中添加ns
- 通过kubectl create -f  xxx.yaml -n custom-ns

ns注意事项

- ns原生不提供网络隔离，也就是说一个ns的pod可以访问到其他ns下的pod。

## 探针

### 存活探针 livenessprobe

包含三种:

	- Http get 探针
	- Tcp 探针
	- Exce 探针

通过在yaml的spec指定 livenessprobe

pod节点上的kubelet会通过存活探针来判断pod是否健康，当检测不通过kubelet会重启容器。

但是如果节点本身崩溃，那么就无法通过pod所属的kubelet来恢复了。这时候需要使用 ReplicationControoler 或类似的机制来管理pod

### 就绪探针 readinessprobe

用于确保pod已经就绪，可以被外部服务访问，k8s会周期性的调用就绪探针，只有就绪之后才会被加入到服务的Endpoint列表。

## ReplicationControoler

ReplicationControoler 会持续监控正在运行的pod列表，并保证对应的pod数目和期望的一致。

### ReplicationControoler 扩容

方法一，通过kubectl命令

```java
kubectl scale rc my_rc --replicas=10 // 把 my_rc的pod数提升到10
```

方法二，直接编辑yaml

```java
kubectl edit rc my_rc
```

## ReplicaSet

用于取代 ReplicationControoler ，可以理解为优化版的 ReplicationControoler，ReplicationControoler最终会被弃用。

查看rs

```java
kubectl get rs --all-namespaces
```

编辑rs

```java 
kubectl edit rs nginx-9bd6cf55 -n test-20190221
```



## DaemonSet

当你希望pod在集群每个node都运行一个时就需要用到 DaemonSet 了，这种场景通常在一些底层基础服务，比如日志收集器和资源监控器上。另外k8s自己的kube-proxy进程也是这种方式。

## Job

前面的Pod会一直运行不会退出，如果需要启动一个执行单个任务，执行完之后会退出的pod那就要使用到Job了。

### 并行运行Job

编辑yaml

### Job的缩放

编辑yaml

### 定期执行的Job CronJob

编辑yaml

## 服务 Service

如果pod直接对外提供服务存在的问题:

- Pod 是短暂的
- Pod 的Ip地址会变化
- 直接提供Pod失去了负载均衡的能力

Service 为一组功能相同的 pod 提供了单一不变的接入点。当服务存在时, 它的ip和端口就不会变化，客户端通过IP和端口号建立连接，这些连接会被路由到该服务的任意一个pod。 客户端不需要知道pod的地址，集群中pod被创建和移除对客户端无感。

### 创建服务

```java
kubectl expose
```

也可以通过yaml

### 服务亲和性

可以在yaml中设置 sessionAffinity:ClientIp来实现同一个ip只会请求到同一个pod上

### 端口别名

可以在pod中定义好端口号的名字，然后在服务中引用端口别名

### 服务发现
可以通过两种方式：
- 通过环境变量
- 通过DNS

### 服务endpoint

通过命令可以查看

```java
kubectl get endpoints service_name
```

利用endpoint，可以把Service 关联到外部ip地址，这样这个pod可以通过访问这个Service来访问外部地址，Service会保证负载均衡。

### Headless Service
`Headless Service`也是一种`Service`，但不同的是会定义`spec:clusterIP: None`，也就是不需要`Cluster IP`的`Service`。

### Ingress

Ingress 提供外部系统访问服务的机制。Ingress通过Ingress控制器创建

Ingress 在HTTP应用层操作，可以提供一些服务不能实现的功能，比如cookie的回话亲和性。

通过命名查看ingress

```java
get ingress --all-namespaces    
```

## 卷

卷是pod的一部分，通过在pod的yaml文件中配置属性来使用卷，常规pod卷的类型包括

- emptyDir 卷
- gitRepo 卷
- hostPath  卷。挂载到节点的本地文件，如果pod被调度到其他机器则会存在问题。

#### 持久卷 PersistenVolume

管理员先创建好持久卷声明(PersistentVolumeClaim)之后，再在node中使用，持久卷不属于任何命名空间，它跟机器节点一样是集群层面的资源。

查看持久卷声明命令

```java
kubectl get pvc
```

查看持久卷命令

```java
kubectl get pv
```

#### 创建持久卷声明

```yaml
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: mongodb-pvc # 声明的名称，后续将声明当做pod的卷使用时需要用到
spec:
  accessModes:
    - ReadWriteOnce # 访问模式
  resources:
    requests:
      storage: 8Gi
  storageClassName: slow
  selector:
    matchLabels:
      release: "stable"
```

#### 在pod中使用pvc

在pod中使用pv，只需要引用 pvc就可以了，样例如下：

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: mongodb
spec:
  containers:
  - image: mongo
  	name: mongodb
  	volumeMounts:
  	- name: mongodb-data
      mountPath: /data/db
    ports:
    - containerPort: 27017
      protocal:  TCP
  volumes:
  - name: monodb-data
   persistentVolumeClaim:
   	claimName: mongodb-pvc # 引用 pvc
```

创建好这个pod之后可以运行

```java
kubectl exec -it mongodb mongo
```

## ConfigMaps

查看 ConfigMaps

```java
kubectl get cm
```

## PodDisruptionBudget

## StatefulSet

## 参考

https://draveness.me/understanding-kubernetes

[从零开始入门 K8s：监控与日志的可观测性](https://www.infoq.cn/article/1WFHGE5QH9Xtit57gvj5)

[从零开始入门 K8s：应用存储和持久化数据卷：存储快照与拓扑调度](https://www.infoq.cn/article/RIbaEwIpDkSUGWWFuJCu)

[从零开始入门 K8s：应用存储和持久化数据卷的核心知识](https://www.infoq.cn/article/r4fUHGFCjuOyWBHSyg3H)

[从零开始入门 K8s：应用配置管理](https://www.infoq.cn/article/OvxVJtHGElqWIfG555Gf)

[从零开始入门 K8s：应用编排与管理：Job & DaemonSet](https://www.infoq.cn/article/KceOuuS7somCYbfuykRG)

[从零开始入门 K8s：应用编排与管理](https://www.infoq.cn/article/bShSloggOsz3RyV6azfL)

[从零开始入门 K8s：K8s 的应用编排与管理](https://www.infoq.cn/article/XO4NzPUUKCTHrKNqcf6l)

[从零开始入门 K8s：详解 Pod 及容器设计模式](https://www.infoq.cn/article/xyxNdh6OiooK75vo4ZiE)

[从零开始入门 K8s：详解 K8s 容器基本概念](https://www.infoq.cn/article/te70FlSyxhltL1Cr7gzM)

[从零开始入门 K8s：详解 K8s 核心概念](https://www.infoq.cn/article/KNMAVdo3jXs3qPKqTZBw)

[从零开始入门 K8s：Kubernetes 网络概念及策略控制](https://www.infoq.cn/article/ERuLek5gPfUxdHC5cMTO)

