## 概述

Pod、Service、Volume 和 Namespace 是 Kubernetes 集群中四大基本对象，它们能够表示系统中部署的应用、工作负载、网络和磁盘资源，共同定义了集群的状态。Kubernetes 中很多其他的资源其实只对这些基本的对象进行了组合。

![kubernetes-basic-objects](https://img.draveness.me/2018-12-25-kubernetes-basic-objects.png)

作为 Kubernetes 集群中的基本单元，Pod 就是最小并且最简单的 Kubernetes 对象， 一个简单的YAML 描述如下：

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: busybox
  labels:
    app: busybox
spec:
  containers:
  - image: busybox
    command:
      - sleep
      - "3600"
    imagePullPolicy: IfNotPresent
    name: busybox
  restartPolicy: Always
```

这个 YAML 文件描述了一个 Pod 启动时运行的容器和命令以及它的重启策略，在当前 Pod 出现错误或者执行结束后是否应该被 Kubernetes 的控制器拉起来，除了这些比较显眼的配置之外，元数据 `metadata` 的配置也非常重要，`name` 是当前对象在 Kuberentes 集群中的唯一标识符，而标签 `labels` 可以帮助我们快速选择对象。

在同一个 Pod 中，有几个概念特别值得关注，首先就是容器，在 Pod 中其实可以同时运行一个或者多个容器，这些容器能够共享网络、存储以及 CPU、内存等资源。在这一小节中我们将关注 Pod 中的容器、卷和网络三大概念。

## 容器

每一个 Kubernetes 的 Pod 其实都具有两种不同的容器:

-  `InitContainer`，这种容器会在 Pod 启动时运行，主要用于初始化一些配置
- 另一种是 Pod 在 Running 状态时内部存活的 `Container`，它们的主要作用是对外提供服务或者作为工作节点处理异步任务等等。

![kubernetes-pod-init-and-regular-containers](https://img.draveness.me/2018-12-25-kubernetes-pod-init-and-regular-containers.png)

通过对不同容器类型的命名我们也可以看出，`InitContainer` 会比 `Container` 优先启动，在 `kubeGenericRuntimeManager.SyncPod` 方法中会先后启动两种容器。

```go
func (m *kubeGenericRuntimeManager) SyncPod(pod *v1.Pod, _ v1.PodStatus, podStatus *kubecontainer.PodStatus, pullSecrets []v1.Secret, backOff *flowcontrol.Backoff) (result kubecontainer.PodSyncResult) {
	// Step 1: Compute sandbox and container changes.
	// Step 2: Kill the pod if the sandbox has changed.
	// Step 3: kill any running containers in this pod which are not to keep.
	// Step 4: Create a sandbox for the pod if necessary.
	// ...

	// Step 5: start the init container.
	if container := podContainerChanges.NextInitContainerToStart; container != nil {
		msg, _ := m.startContainer(podSandboxID, podSandboxConfig, container, pod, podStatus, pullSecrets, podIP, kubecontainer.ContainerTypeInit)
	}

	// Step 6: start containers in podContainerChanges.ContainersToStart.
	for _, idx := range podContainerChanges.ContainersToStart {
		container := &pod.Spec.Containers[idx]
		
		msg, _ := m.startContainer(podSandboxID, podSandboxConfig, container, pod, podStatus, pullSecrets, podIP, kubecontainer.ContainerTypeRegular)
	}

	return
}
```

通过分析私有方法 `startContainer` 的实现我们得出：容器的类型最终只会影响在 Debug 时创建的标签，所以对于 Kubernetes 来说两种容器的启动和执行也就只有顺序先后的不同。

## 卷

每一个 Pod 中的容器是可以通过 [卷（Volume）](https://draveness.me/kubernetes-volume) 的方式共享文件目录的，这些 Volume 能够存储持久化的数据；在当前 Pod 出现故障或者滚动更新时，对应 Volume 中的数据并不会被清除，而是会在 Pod 重启后重新挂载到期望的文件目录中：

![kubernetes-containers-share-volumes](https://img.draveness.me/2018-12-25-kubernetes-containers-share-volumes.png)

## 网络

### Pod IP

k8s利用Flannel作为不同HOST之间容器互通技术时，由Flannel和etcd维护了一张节点间的路由表。Flannel的设计目的就是为集群中的所有节点重新规划IP地址的使用规则，从而使得不同节点上的容器能够获得“同属一个内网”且”不重复的”IP地址，并让属于不同节点上的容器能够直接通过内网IP通信。

每个Pod启动时，会自动创建一个镜像为gcr.io/google_containers/pause:0.8.0的容器，容器内部与外部的通信经由此容器代理，该容器的IP也可以称为Pod IP。

![kubernetes-pod-network](https://img.draveness.me/2018-12-25-kubernetes-pod-network.png)

每一个节点上都会由 k8s 的网络插件 Kubenet 创建一个基本的 `cbr0` 网桥并为每一个 Pod 创建 `veth` 虚拟网络设备，同一个 Pod 中的所有容器就会通过这个网络设备共享网络，也就是能够通过 localhost 互相访问彼此暴露的端口和服务。

可以通过 kubectl describe pod zk-0 命名查看pod的podIp:

```java
➜  ~ kubectl describe pod zk-0
Name:           zk-0
Namespace:      default
Priority:       0
Node:           ************/************
Start Time:     Mon, 02 Mar 2020 11:31:17 +0800
Labels:         app=zk
                controller-revision-hash=zk-76dd7d99ff
                statefulset.kubernetes.io/pod-name=zk-0
Annotations:    <none>
Status:         Running
IP:             ************
```



### Cluster IP

Pod IP 地址是实际存在于某个网卡(可以是虚拟设备)上的，但Service Cluster IP就不一样了，没有网络设备为这个地址负责。它是由kube-proxy使用Iptables规则重新定向到其本地端口，再均衡到后端Pod的。

当我们的Service被创建时，Kubernetes给它分配一个地址********。这个地址从我们启动API的service-cluster-ip-range参数(旧版本为portal_net参数)指定的地址池中分配，比如–service-cluster-ip-range=10.0.0.0/16。假设这个Service的端口是1234。集群内的所有kube-proxy都会注意到这个Service。当proxy发现一个新的service后，它会在本地节点打开一个任意端口，建相应的iptables规则，重定向服务的IP和port到这个新建的端口，开始接受到达这个服务的连接。

当一个客户端访问这个service时，这些iptable规则就开始起作用，客户端的流量被重定向到kube-proxy为这个service打开的端口上，kube-proxy随机选择一个后端pod来服务客户。这个流程如下图所示：
![这里写图片描述](https://c-ssl.duitang.com/uploads/item/202003/02/20200302110058_vVPVf.png)
根据Kubernetes的网络模型，使用Service Cluster IP和Port访问Service的客户端可以坐落在任意代理节点上。外部要访问Service，我们就需要给Service外部访问IP。

## 生命周期

想要深入理解 Pod 的实现原理，最好最快的办法就是从 Pod 的生命周期入手，通过理解 Pod 创建、重启和删除的原理我们最终就能够系统地掌握 Pod 的生命周期与核心原理。

Pod的`phase`是Pod生命周期中的简单宏观描述，定义在Pod的`PodStatus`对象的`phase` 字段中。

`phase`有以下几种值：

| 状态值              | 说明                                                         |
| ------------------- | ------------------------------------------------------------ |
| `挂起（Pending）`   | Pod 已被 Kubernetes 系统接受，但有一个或者多个容器镜像尚未创建。等待时间包括调度 Pod 的时间和通过网络下载镜像的时间。 |
| `运行中（Running）` | 该 Pod 已经绑定到了一个节点上，Pod 中所有的容器都已被创建。至少有一个容器正在运行，或者正处于启动或重启状态。 |
| `成功（Succeeded）` | Pod 中的所有容器都被成功终止，并且不会再重启。               |
| `失败（Failed）`    | Pod 中的所有容器都已终止了，并且至少有一个容器是因为失败终止。也就是说，容器以非0状态退出或者被系统终止。 |
| `未知（Unknown）`   | 因为某些原因无法取得 Pod 的状态，通常是因为与 Pod 所在主机通信失败。 |

![kubernetes-pod-lifecycle](https://img.draveness.me/2018-12-25-kubernetes-pod-lifecycle.png)

当 Pod 被创建之后，就会进入健康检查状态，当 Kubernetes 确定当前 Pod 已经能够接受外部的请求时，才会将流量打到新的 Pod 上并继续对外提供服务，在这期间如果发生了错误就可能会触发重启机制。

在 Pod 被删除之前都会触发一个 `PreStop` 的钩子，其中的方法之前完成之后 Pod 才会被删除，接下来我们就会按照这里的顺序依次介绍 Pod 『从生到死』的过程。

### 创建

Pod 的创建都是通过 `SyncPod` 来实现的，创建的过程大体上可以分为六个步骤：

1. 计算 Pod 中沙盒和容器的变更；
2. 强制停止 Pod 对应的沙盒；
3. 强制停止所有不应该运行的容器；
4. 为 Pod 创建新的沙盒；
5. 创建 Pod 规格中指定的初始化容器；
6. 依次创建 Pod 规格中指定的常规容器；

我们可以看到 Pod 的创建过程其实是比较简单的，首先计算 Pod 规格和沙箱的变更，然后停止可能影响这一次创建或者更新的容器，最后依次创建沙盒、初始化容器和常规容器。

### 健康检查

Pod的健康状态由两类探针来检查：`LivenessProbe`和`ReadinessProbe`。

**1. livenessProbe(存活探针)**

- 表明容器是否正在运行。
- 如果存活探测失败，则 kubelet 会杀死容器，并且容器将受到其 `重启策略`的影响。
- 如果容器不提供存活探针，则默认状态为 `Success`。

**2. readinessProbe(就绪探针)**

- 表明容器是否可以正常接受请求。
- 如果就绪探测失败，端点控制器将从与 Pod 匹配的所有 Service 的端点中删除该 Pod 的 IP 地址。
- 初始延迟之前的就绪状态默认为 `Failure`。
- 如果容器不提供就绪探针，则默认状态为 `Success`。

![kubernetes-probe-manager](https://img.draveness.me/2018-12-25-kubernetes-probe-manager.png)

### 重启策略

Pod通过`restartPolicy`字段指定重启策略，重启策略类型为：Always、OnFailure 和 Never，默认为 Always。

`restartPolicy` 仅指通过同一节点上的 kubelet 重新启动容器。

| 重启策略        | 说明                                                   |
| --------------- | ------------------------------------------------------ |
| Always （默认） | 当容器失效时，由kubelet自动重启该容器                  |
| OnFailure       | 当容器终止运行且退出码不为0时，由kubelet自动重启该容器 |
| Never           | 不论容器运行状态如何，kubelet都不会重启该容器          |

**说明**：

可以管理Pod的控制器有Replication Controller，Job，DaemonSet，及kubelet（静态Pod）。

1. RC和DaemonSet：必须设置为Always，需要保证该容器持续运行。
2. Job：OnFailure或Never，确保容器执行完后不再重启。
3. kubelet：在Pod失效的时候重启它，不论RestartPolicy设置为什么值，并且不会对Pod进行健康检查。

### 删除

当 Kubelet 在 `HandlePodRemoves` 方法中接收到来自客户端的删除请求时，就会通过一个名为 `deletePod` 的私有方法中的 Channel 将这一事件传递给 PodKiller 进行处理：

```go
func (kl *Kubelet) deletePod(pod *v1.Pod) error {
	kl.podWorkers.ForgetWorker(pod.UID)

	runningPods, _ := kl.runtimeCache.GetPods()
	runningPod := kubecontainer.Pods(runningPods).FindPod("", pod.UID)
	podPair := kubecontainer.PodPair{APIPod: pod, RunningPod: &runningPod}

	kl.podKillingCh <- &podPair
	return nil
}
```

Kubelet 除了将事件通知给 PodKiller 之外，还需要将当前 Pod 对应的 Worker 从持有的 `podWorkers` 中删除；PodKiller 其实就是 Kubelet 持有的一个 Goroutine，它会在后台持续运行并监听来自 `podKillingCh` 的事件：

![kubernetes-pod-killer](https://img.draveness.me/2018-12-25-kubernetes-pod-killer.png)

经过一系列的方法调用之后，最终调用容器运行时的 `killContainersWithSyncResult` 方法，这个方法会同步地杀掉当前 Pod 中全部的容器。对于每一个容器来说，它们在被停止之前都会先调用 `PreStop` 的钩子方法，让容器中的应用程序能够有时间完成一些未处理的操作，随后调用远程的服务停止运行的容器：

```go
func (m *kubeGenericRuntimeManager) killContainer(pod *v1.Pod, containerID kubecontainer.ContainerID, containerName string, reason string, gracePeriodOverride *int64) error {
	containerSpec := kubecontainer.GetContainerSpec(pod, containerName);

	gracePeriod := int64(minimumGracePeriodInSeconds)
	switch {
	case pod.DeletionGracePeriodSeconds != nil:
		gracePeriod = *pod.DeletionGracePeriodSeconds
	case pod.Spec.TerminationGracePeriodSeconds != nil:
		gracePeriod = *pod.Spec.TerminationGracePeriodSeconds
	}
	
	m.executePreStopHook(pod, containerID, containerSpec, gracePeriod
	m.internalLifecycle.PreStopContainer(containerID.ID)
	m.runtimeService.StopContainer(containerID.ID, gracePeriod)
	m.containerRefManager.ClearRef(containerID)

	return err
}
```

## 参考

- [谈 Kubernetes 的架构设计与实现原理](https://draveness.me/understanding-kubernetes)

- [从 Kubernetes 中的对象谈起](https://draveness.me/kubernetes-object-intro)

- [详解 Kubernetes Pod 的实现原理](https://draveness.me/kubernetes-pod)

- [详解 Kubernetes Service 的实现原理](https://draveness.me/kubernetes-service)

- [详解 Kubernetes Volume 的实现原理](https://draveness.me/kubernetes-volume)

- [详解 Kubernetes ReplicaSet 的实现原理](https://draveness.me/kubernetes-replicaset)

- [详解 Kubernetes Deployment 的实现原理](https://draveness.me/kubernetes-deployment)

- [详解 Kubernetes StatefulSet 的实现原理](https://draveness.me/kubernetes-statefulset)

- [详解 Kubernetes DaemonSet 的实现原理](https://draveness.me/kubernetes-daemonset)

- [详解 Kubernetes Job 和 CronJob 的实现原理](https://draveness.me/kubernetes-job-cronjob)

- [详解 Kubernetes 垃圾收集器 的实现原理](https://draveness.me/kubernetes-garbage-collector)

    

