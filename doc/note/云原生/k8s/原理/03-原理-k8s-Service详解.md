## 概述

集群中的每一个 Pod 都可以通过 `podIP` 被直接访问的，但是正如我们所看到的，Kubernetes 中的 Pod 是有生命周期的对象，尤其是被 ReplicaSet、Deployment 等对象管理的 Pod，随时都有可能由于集群的状态变化被销毁和创建。

![kubernetes-pods-and-pods](https://img.draveness.me/2019-01-01-kubernetes-pods-and-pods.png)

当 Kuberentes 集群中的一些 Pod 需要为另外的一些 Pod 提供服务时，我们如何为提供同一功能服务的一组 Pod 建立一个抽象并追踪这组服务中节点的健康状态，这一个抽象在 Kubernetes 中其实就是 Service。

在k8s中的service：

- 是发现后端pod服务；
- 是为一组具有相同功能的容器应用提供一个统一的入口地址；
- 是将请求进行负载分发到后端的各个容器应用上的控制器。

访问service的请求来源有两种：k8s集群内部的程序（Pod）和 k8s集群外部的程序。

采用微服务架构时，作为服务提供者，除了实现业务逻辑以外，还需要考虑如何把服务发布到k8s集群或者集群外部，使这些服务能够被k8s集群内的应用、其他k8s集群的应用以及外部应用使用。因此k8s提供了灵活的服务发布方式，用户可以通过ServiceType来指定如何来发布服务，类型有以下几种：
- **ClusterIP**：提供一个集群内部的虚拟IP以供Pod访问（service默认类型)。
- **NodePort**：在每个Node上打开一个端口以供外部访问
-  **LoadBalancer**：通过外部的负载均衡器来访问

三种类型的详细解释参考：02-k8s-外部访问service方法.md 一文

## k8s服务发现方式

虽然Service解决了Pod的服务发现问题，但不提前知道Service的IP，怎么发现service服务呢？

k8s提供了两种方式进行服务发现：

-  **环境变量**： 当创建一个Pod的时候，kubelet会在该Pod中注入集群内所有Service的相关环境变量。需要注意的是，要想一个Pod中注入某个Service的环境变量，则必须Service要先比该Pod创建。这一点，几乎使得这种方式进行服务发现不可用。
-  **DNS**：可以通过cluster add-on的方式轻松的创建KubeDNS来对集群内的Service进行服务发现。这也是k8s官方强烈推荐的方式。为了让Pod中的容器可以使用kube-dns来解析域名，k8s会修改容器的/etc/resolv.conf配置。

## k8s服务发现原理

#### endpoint

service通过selector和pod建立关联。

k8s会根据service关联到pod的podIP信息组合成一个 **endpoint**。endpoint是k8s集群中的一个资源对象，存储在etcd中，记录一个service对应的所有pod的访问地址。

若service定义中没有selector字段，service被创建时，endpoint controller不会自动创建endpoint。

通过 kubectl get endpoints 命名可以查询

```java
kubectl get endpoints
----------------------------------------------------------------------------------------------    
NAME                 ENDPOINTS                                                            AGE
kubernetes           ************:6443,************:6443,************:6443                440d
prometheus-service   <none>                                                               235d
zk-cs                *************:2181,************:2181,************:2181               23h
zk-hs                *************:3888,************:3888,************:3888 + 3 more...   23h
```

#### endpoint controller

endpoint controller是k8s集群控制器的其中一个组件，其功能如下：

- 负责生成和维护所有endpoint对象的控制器
- 负责监听service和对应pod的变化
- 监听到service被删除，则删除和该service同名的endpoint对象
- 监听到新的service被创建，则根据新建service信息获取相关pod列表，然后创建对应endpoint对象
- 监听到service被更新，则根据更新后的service信息获取相关pod列表，然后更新对应endpoint对象
- 监听到pod事件，则更新对应的service的endpoint对象，将podIp记录到endpoint中

### **service** 负载均衡策略

service 负载均衡策略有两种：

- RoundRobin：轮询模式，即轮询将请求转发到后端的各个pod上（默认模式）；
- SessionAffinity：基于客户端IP地址进行会话保持的模式，第一次客户端访问后端某个pod，之后的请求都转发到这个pod上。

## 代理模式

在 Kubernetes 集群中的每一个节点都运行着一个 kube-proxy 进程，这个进程会负责监听 Kubernetes 主节点中 Service 的增加和删除事件并修改运行代理的配置，为节点内的客户端提供流量的转发和负载均衡等功能，当前 kube-proxy 的代理模式目前来看有三种：

![kubernetes-service-proxy-mode](https://img.draveness.me/2019-01-01-kubernetes-service-proxy-mode.png)

这三种代理模式中的第一种 userspace 其实就是运行在用户空间代理，所有的流量最终都会通过 kube-proxy 本身转发给其他的服务，后两种 iptable 和 ipvs 都运行在内核空间能够为 Kubernetes 集群提供更加强大的性能支持。

### userspace

作为运行在用户空间的代理，对于每一个 Service 都会在当前的节点上开启一个端口，所有连接到当前代理端口的请求都会被转发到 Service 背后的一组 Pod 上，它其实会在节点上添加 iptables 规则，通过 iptables 将流量转发给 kube-proxy 处理。

如果当前节点上的 kube-proxy 在启动时选择了 userspace 模式，那么每当有新的 Service 被创建时，kube-proxy 就会增加一条 iptables 记录并启动一个 Goroutine，前者用于将节点中服务对外发出的流量转发给 kube-proxy，再由后者持有的一系列 Goroutine 将流量转发到目标的 Pod 上。

![kubernetes-userspace-proxy-mode](https://img.draveness.me/2019-01-01-kubernetes-userspace-proxy-mode.png)

## 参考

https://zhuanlan.zhihu.com/p/39909011