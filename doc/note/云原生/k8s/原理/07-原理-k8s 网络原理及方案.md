## 概述

公司搭建的k8s集群不是的pod可以通过外部网络直接ping通过，而通常默认情况下k8s中的podIP是一个独立的IP段，无法和外网通信的，这是如何实现的呢？同事说：“和网络模式有关，比如 Flannel 这种属于 Overlay 网络，如果要通信必须要在加入网络，测试环境使用的 Calico BGP 打通交换机，这样本地也可以访问了”。

那么什么是Overlay网络？Flannel，Calico BGP又是什么？带着这些问题网上搜索了一番。

## 术语

**IPAM：**IP地址管理；这个IP地址管理并不是容器所特有的，传统的网络比如说DHCP其实也是一种IPAM，到了容器时代我们谈IPAM，主流的两种方法： 基于CIDR的IP地址段分配地或者精确为每一个容器分配IP。但总之一旦形成一个容器主机集群之后，上面的容器都要给它分配一个全局唯一的IP地址，这就涉及到IPAM的话题。

**Overlay：**在**现有二层或三层网络**之上再构建起来一个独立的网络，这个网络通常会有自己独立的IP地址空间、交换或者路由的实现。overlay是docker支持的4种网络模式之一，包括host null overlay bridge四种。

**二层和三层网络**：二层网络仅仅通过MAC寻址即可实现通讯,三层网络需要通过IP路由实现跨网段的通讯,可以跨多个冲突域。（不明白二三层网络可以回顾下network目录下的网络基础知识）

**IPSesc：**一个点对点的一个加密通信协议，一般会用到Overlay网络的数据通道里。

**vxLAN：**由VMware、Cisco、RedHat等联合提出的这么一个解决方案，这个解决方案最主要是解决VLAN支持虚拟网络数量（4096）过少的问题。因为在公有云上每一个租户都有不同的VPC，4096明显不够用。就有了vxLAN，它可以支持1600万个虚拟网络，基本上公有云是够用的。

**网桥Bridge：** 连接两个对等网络之间的网络设备，但在今天的语境里指的是Linux Bridge，就是大名鼎鼎的Docker0这个网桥。

**BGP：** 主干网自治网络的路由协议，今天有了互联网，互联网由很多小的自治网络构成的，自治网络之间的三层路由是由BGP实现的。

**SDN、Openflow：** 软件定义网络里面的一个术语，比如说我们经常听到的流表、控制平面，或者转发平面都是Openflow里的术语。

## 容器网络方案

### 隧道方案（ Overlay Networking ）

隧道方案在IaaS层的网络中应用也比较多，大家共识是随着节点规模的增长复杂度会提升，而且出了网络问题跟踪起来比较麻烦，大规模集群情况下这是需要考虑的一个点。

- **Weave：**UDP广播，本机建立新的BR，通过PCAP互通
- **Open vSwitch（OVS）：**基于VxLan和GRE协议，但是性能方面损失比较严重
- **Flannel：**UDP广播，VxLan
- **Racher：**IPsec

### 路由方案

路由方案一般是从3层或者2层实现隔离和跨主机容器互通的，出了问题也很容易排查。

- **Calico：**基于BGP协议的路由方案，支持很细致的ACL控制，对混合云亲和度比较高。
- **Macvlan：**从逻辑和Kernel层来看隔离性和性能最优的方案，基于二层隔离，所以需要二层路由器支持，大多数云服务商不支持，所以混合云上比较难以实现。

## Flannel容器网络

Flannel本质是解决k8s集群pod在不同node之间进行网络通信的方案。

Flannel是kubernetes默认提供网络插件，由CoreOS团队开发，采用L3 Overlay模式设计， 规定宿主机下各个Pod属于同一个子网，不同宿主机下的Pod属于不同的子网。

Flannel实质上是一种“覆盖网络(overlay network)”，也就是将TCP数据包装在另一种网络包里面进行路由转发和通信，目前已经支持UDP、VxLAN、AWS VPC和GCE路由等数据转发方式。

Flannel工作原理：

![1](/work/dist/branch/wacai/middleware/my-boot/doc/images/k8s/1.png)

1、数据从源容器中发出后，经由所在主机的docker0虚拟网卡转发到flannel0虚拟网卡。flanneld服务监听在网卡的另外一端。

2、源主机的flanneld服务将原本的数据内容UDP封装后根据自己的路由表投递给目的节点的flanneld服务。

3、数据到达以后被解包，然后直接进入目的节点的flannel0虚拟网卡，然后被转发到目的主机的docker0虚拟网卡，最后就像本机容器通信一下的有docker0路由到达目标容器

Flannel通过Etcd分配了每个节点可用的IP地址段后，偷偷的修改了Docker的启动参数。

## Calico容器网络

Calico是一个纯3层的数据中心网络方案，使用虚拟路由代替虚拟交换，每一台虚拟路由通过BGP协议传播可达信息（路由）到剩余数据中心。

Calico节点组网可以直接利用数据中心的网络结构（无论是L2或者L3），不需要额外的NAT，隧道或者Overlay Network。



## 参考

- [Kubernetes网络原理及方案](https://www.kubernetes.org.cn/2059.html)
- [Calico网络的原理、组网方式与使用](https://www.lijiaocn.com/%E9%A1%B9%E7%9B%AE/2017/04/11/calico-usage.html)
- [Flannel网络原理](https://www.jianshu.com/p/165a256fb1da)
- [Flannel介绍](https://www.huweihuang.com/article/flannel/flannel-introduction/)

