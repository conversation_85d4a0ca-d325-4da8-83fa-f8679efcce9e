## 搭建步骤

### 1 、StatefulSet

准备 StatefulSet.yaml

```yaml
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: zk-pdb
spec:
  selector:
    matchLabels:
      app: zk
  maxUnavailable: 1
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  name: zk
spec:
  selector:
    matchLabels:
      app: zk
  serviceName: zk-hs
  replicas: 3 			#指定副本数
  updateStrategy:
    type: RollingUpdate
  podManagementPolicy: Parallel
  template:
    metadata:
      labels:
        app: zk
    spec:
      affinity:
        podAntiAffinity: #反亲和性，保证pod不会全部调度到同一台机器
          requiredDuringSchedulingIgnoredDuringExecution:
            - labelSelector:
                matchExpressions:
                  - key: "app"
                    operator: In
                    values:
                    - zk
              topologyKey: "kubernetes.io/hostname"
      containers:
      - name: kubernetes-zookeeper
        imagePullPolicy: Always
        #image: "k8s.gcr.io/kubernetes-zookeeper:1.0-3.4.10"
        image: "nirmata/kubernetes-zookeeper:3.5.4-beta"
        resources:
          requests:
            memory: "1Gi"
            cpu: "0.5"
        ports:
        - containerPort: 2181
          name: client
        - containerPort: 2888
          name: server
        - containerPort: 3888
          name: leader-election
        command:
        - sh
        - -c
        - "start-zookeeper \
          --servers=3 \
          --data_dir=/var/lib/zookeeper/data \
          --data_log_dir=/var/lib/zookeeper/data/log \
          --conf_dir=/opt/zookeeper/conf \
          --client_port=2181 \
          --election_port=3888 \
          --server_port=2888 \
          --tick_time=2000 \
          --init_limit=10 \
          --sync_limit=5 \
          --heap=512M \
          --max_client_cnxns=60 \
          --snap_retain_count=3 \
          --purge_interval=12 \
          --max_session_timeout=40000 \
          --min_session_timeout=4000 \
          --log_level=INFO"
        readinessProbe:
          exec:
            command:
            - sh
            - -c
            - "zookeeper-ready 2181"
          initialDelaySeconds: 10
          timeoutSeconds: 5
        livenessProbe:
          exec:
            command:
            - sh
            - -c
            - "zookeeper-ready 2181"
          initialDelaySeconds: 10
          timeoutSeconds: 5
        volumeMounts:
        - name: datadir
          mountPath: /var/lib/zookeeper
      securityContext:
        runAsUser: 0	#通过root运行
  volumeClaimTemplates: #创建pvc的模板
  - metadata:
      name: datadir
      annotations:
        volume.beta.kubernetes.io/storage-class: "anything"
    spec:
      accessModes: [ "ReadWriteOnce" ]
      resources:
        requests:
          storage: 3Gi
```

通过volumeClaimTemplates会自动创建pvc，名称叫datadir



### 2、Service 

准备 Service.yaml

zk集群之间通信

```yaml
apiVersion: v1
kind: Service
metadata:
  name: zk-hs
  labels:
    app: zk
spec:
  ports:
  - port: 2888
    name: server
  - port: 3888
    name: leader-election
  clusterIP: None
  selector:
    app: zk
```

**zk对client通信**存在两种模式：

ClusterIP 模式

```yaml
apiVersion: v1
kind: Service
metadata:
  name: zk-cs
  labels:
    app: zk
spec:
  ports:
  - port: 2181
    name: client
  selector:
    app: zk
```

nodePort模式

```yaml
apiVersion: v1
kind: Service
metadata:
  name: zk-cs
  labels:
    app: zk
spec:
  type: NodePort
  ports:
  - port: 2181
    targetPort: 2181
    name: client
    protocol: TCP
  selector:
    app: zk
```

## zk原始镜像文件

Dockerfile:

```yaml
FROM ubuntu:16.04 
ENV ZK_USER=zookeeper \
ZK_DATA_DIR=/var/lib/zookeeper/data \
ZK_DATA_LOG_DIR=/var/lib/zookeeper/log \
ZK_LOG_DIR=/var/log/zookeeper \
JAVA_HOME=/usr/lib/jvm/java-8-openjdk-amd64

ARG GPG_KEY=C823E3E5B12AF29C67F81976F5CECB3CB5E9BD2D
ARG ZK_DIST=zookeeper-3.4.10
RUN set -x \
    && apt-get update \
    && apt-get install -y openjdk-8-jre-headless wget netcat-openbsd \
	&& wget -q "http://www.apache.org/dist/zookeeper/$ZK_DIST/$ZK_DIST.tar.gz" \
    && wget -q "http://www.apache.org/dist/zookeeper/$ZK_DIST/$ZK_DIST.tar.gz.asc" \
    && export GNUPGHOME="$(mktemp -d)" \
    && gpg --keyserver ha.pool.sks-keyservers.net --recv-key "$GPG_KEY" \
    && gpg --batch --verify "$ZK_DIST.tar.gz.asc" "$ZK_DIST.tar.gz" \
    && tar -xzf "$ZK_DIST.tar.gz" -C /opt \
    && rm -r "$GNUPGHOME" "$ZK_DIST.tar.gz" "$ZK_DIST.tar.gz.asc" \
    && ln -s /opt/$ZK_DIST /opt/zookeeper \
    && rm -rf /opt/zookeeper/CHANGES.txt \
    /opt/zookeeper/README.txt \
    /opt/zookeeper/NOTICE.txt \
    /opt/zookeeper/CHANGES.txt \
    /opt/zookeeper/README_packaging.txt \
    /opt/zookeeper/build.xml \
    /opt/zookeeper/config \
    /opt/zookeeper/contrib \
    /opt/zookeeper/dist-maven \
    /opt/zookeeper/docs \
    /opt/zookeeper/ivy.xml \
    /opt/zookeeper/ivysettings.xml \
    /opt/zookeeper/recipes \
    /opt/zookeeper/src \
    /opt/zookeeper/$ZK_DIST.jar.asc \
    /opt/zookeeper/$ZK_DIST.jar.md5 \
    /opt/zookeeper/$ZK_DIST.jar.sha1 \
	&& apt-get autoremove -y wget \
	&& rm -rf /var/lib/apt/lists/*

#拷贝自动生成 zoo.cfg 的脚本到bin
COPY scripts /opt/zookeeper/bin/

# Create a user for the zookeeper process and configure file system ownership 
# for nessecary directories and symlink the distribution as a user executable
RUN set -x \
	&& useradd $ZK_USER \
    && [ `id -u $ZK_USER` -eq 1000 ] \
    && [ `id -g $ZK_USER` -eq 1000 ] \
    && mkdir -p $ZK_DATA_DIR $ZK_DATA_LOG_DIR $ZK_LOG_DIR /usr/share/zookeeper /tmp/zookeeper /usr/etc/ \
	&& chown -R "$ZK_USER:$ZK_USER" /opt/$ZK_DIST $ZK_DATA_DIR $ZK_LOG_DIR $ZK_DATA_LOG_DIR /tmp/zookeeper \
	&& ln -s /opt/zookeeper/conf/ /usr/etc/zookeeper \
	&& ln -s /opt/zookeeper/bin/* /usr/bin \
	&& ln -s /opt/zookeeper/$ZK_DIST.jar /usr/share/zookeeper/ \
	&& ln -s /opt/zookeeper/lib/* /usr/share/zookeeper 
```

说明，zk镜像会建立一系列软链接,包括:

- /opt/zookeeper --> /opt/zookeeper-3.4.10
- /opt/zookeeper/conf --> /usr/etc/zookeeper
- /opt/zookeeper/lib/* --> /usr/share/zookeeper 

copy script目录下的脚本到bin，用于自动生成zoo.cfg文件

## 百度云搭建zk

百度云提供了三种存储方式(参考[CCE-存储管理](https://cloud.baidu.com/doc/CCE/s/mjxppo8qq))，它们的使用场景

| 类型     | 特点                                                         | 性能         |
| -------- | ------------------------------------------------------------ | ------------ |
| 云磁盘   | 云硬盘是可以挂载到云主机上，和物理硬盘一样使用，pod之间的文件独立 | 毫秒以下级别 |
| 文件存储 | 云存储提供简单的API，多个pod共享同一组文件                   | 几十毫秒级别 |
| 对象存储 | 通过 API 或 SDK 管理                                         |              |

对于zk场景推荐使用[云盘CDS](https://cloud.baidu.com/doc/CCE/s/Tjxppp24p)挂载pv，流程如下：

1. 创建一个容器集群，操作步骤参考[创建集群](https://cloud.baidu.com/doc/CCE/s/zjxpoqohb)
2. 安装 CSI CDS 插件 cce-csi-cds-plugin
3. 动态挂载。动态挂载需要创建 StorageClass，创建文件 zk-storeclass.yaml

```yaml
apiVersion: storage.k8s.io/v1
kind: StorageClass
metadata:
  name: zk-cds-hp
provisioner: csi-cdsplugin
allowVolumeExpansion: true
volumeBindingMode: WaitForFirstConsumer
parameters:
  dynamicVolume: 'true'
  cdsSizeInGB: '20'
  paymentTiming: Postpaid
  storageType: hp1
  reservationLength: '3'
reclaimPolicy: Retain
```

创建 zk-statefulset.yaml

```yaml
apiVersion: apps/v1
kind: StatefulSet
metadata:
  generation: 7
  labels:
    app: zk
  managedFields:
  - apiVersion: apps/v1
    manager: dashboard
    operation: Update
  - apiVersion: apps/v1
    manager: kube-controller-manager
    operation: Update
  name: zk
  namespace: zk
spec:
  podManagementPolicy: OrderedReady
  replicas: 3
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: zk
  serviceName: zk-hs
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: zk
    spec:
      affinity:
        nodeAffinity: {}
        podAffinity: {}
        podAntiAffinity: {}
      containers:
      - env:
        - name: ZOO_SERVERS
          value: >-
            server.1=zk-0.zk-hs.zk.svc.cluster.local:2888:3888;2181 server.2=zk-1.zk-hs.zk.svc.cluster.local:2888:3888;2181 server.3=zk-2.zk-hs.zk.svc.cluster.local:2888:3888;2181
        - name: ZOO_STANDALONE_ENABLED
          value: 'false'
        - name: ZOO_LISTEN_ALLIPS_ENABLED
          value: 'yes'
        image: 'registry.baidubce.com/hz_code/zookeeper:3.8.0-gd'
        imagePullPolicy: Always
        name: zookeeper
        ports:
        - containerPort: 2181
          name: client
          protocol: TCP
        - containerPort: 2888
          name: peer
          protocol: TCP
        - containerPort: 3888
          name: leader-election
          protocol: TCP
        volumeMounts:
        - name: zookeeper-data
          mountPath: /data
        resources:
          limits:
            cpu: '1'
            memory: 1Gi
            nvidia.com/gpu: '0'
          requests:
            cpu: 500m
            memory: 512Mi
            nvidia.com/gpu: '0'
        securityContext:
          privileged: true
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
      dnsPolicy: ClusterFirst
      imagePullSecrets:
      - name: hz-image
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      terminationGracePeriodSeconds: 30
  updateStrategy:
    rollingUpdate:
      partition: 0
    type: RollingUpdate
  volumeClaimTemplates:
  - metadata:
      name: zookeeper-data
    spec:
      storageClassName: zk-cds-hp
      accessModes:
      - ReadWriteOnce
      resources:
        requests:
          storage: 5Gi
```

创建文件zk-hs

```yaml
apiVersion: v1
kind: Service
metadata:
  name: zk-hs
  labels:
    app: zk
spec:
  selector:
    app: zk
  ports:
  - port: 2888
    name: peer
  - port: 3888
    name: leader-election
  clusterIP: None

```



## 参考

- https://github.com/kow3ns/kubernetes-zookeeper
- https://www.jianshu.com/p/2633b95c244c
- https://segmentfault.com/a/1190000012244107
- [kubernetes安装zookeeper 3.5.5](https://www.ipyker.com/2019/07/20/k8s-zookeeper)
- https://blog.51cto.com/newfly/2140004