## k8s常用命令

## 1.创建资源

推荐通过kubectl apply 命令

```java
kubectl create namesapce middleware
kubectl apply -f zk-pv.yaml
kubectl create -f zk-pv.yaml     
```

## 2.变更资源

```java
kubectl apply -f zk-pv.yaml
```

也可以直接使用kubectl edit

```java
kubectl edit node zk
```

## 3.删除资源

```java
kubectl delete services zk-hs -n middleware
kubectl delete services zk-cs -n middleware
kubectl delete poddisruptionbudgets.policy zk-pdb -n middleware    
kubectl delete sts zk  -n middleware 
```

删除deployments

```java
kubectl delete deployments.apps  traefik-ingress-controller  
```



## 3.查看资源

```java
kubectl get node 172.16.64.49 -o yaml |less
kubectl get svc
kubectl get pods -w -l app=zk
kubectl get statefulset.apps/zk
kubectl get pods --watch  --all-namespaces
kubectl describe pod zk-0    
kubectl exec -it mongo-0 bash -n luanhua-test-2 
```

## 4.排查问题

查看环境 kubectl cluster-info

```java
kubectl cluster-info
Kubernetes master is running at https://172.16.64.47:443
CoreDNS is running at https://172.16.64.47:443/api/v1/namespaces/kube-system/services/kube-dns:dns/proxy
```

最常用的 kubectl describe

```java
kubectl describe node | less
kubectl describe pod | less    
```

查看yaml

```java
kubectl get node 172.16.64.49 -o yaml |less
```

查看事件 kubectl get events

```java
# kubectl get events
LAST SEEN   TYPE     REASON             KIND                  MESSAGE
17m         Normal   Killing            Pod                   Killing container with id docker://kubernetes-zookeeper:Need to kill Pod
17m         Normal   Killing            Pod                   Killing container with id docker://kubernetes-zookeeper:Need to kill Pod
17m         Normal   Killing            Pod                   Killing container with id docker://kubernetes-zookeeper:Need to kill Pod
119s        Normal   NoPods             PodDisruptionBudget   No matching pods found
17m         Normal   SuccessfulDelete   StatefulSet           delete Pod zk-2 in StatefulSet zk successful
17m         Normal   SuccessfulDelete   StatefulSet           delete Pod zk-1 in StatefulSet zk successful
17m         Normal   SuccessfulDelete   StatefulSet           delete Pod zk-0 in StatefulSet zk successful
```

## 简写

```java
kubectl get svc
```

## 其他

```yaml
kubectl proxy #启动 k8s API server 代理
```











