## 问题

使用k8s如何部署一个简单的无状态应用？

## 样例

```yaml
- apiVersion: extensions/v1beta1
  kind: Deployment
  metadata:
    labels:
      app: luanhua-test-2-nginx2
    name: nginx2
    namespace: luanhua-test-2
    resourceVersion: "46943681"
    selfLink: /apis/extensions/v1beta1/namespaces/luanhua-test-2/deployments/nginx2
    uid: 3a41d863-d2b6-11e9-b00f-c81f66facada
  spec:
    minReadySeconds: 30
    progressDeadlineSeconds: 600
    replicas: 1
    revisionHistoryLimit: 10
    selector:
      matchLabels:
        app: luanhua-test-2-nginx2
    strategy:
      rollingUpdate:
        maxSurge: 25%
        maxUnavailable: 0%
      type: RollingUpdate
    template:
      metadata:
        creationTimestamp: null
        labels:
          app: luanhua-test-2-nginx2
      spec:
        containers:
        - env:
          - name: K2_DEPLOY_TAG
            value: 2019-09-09 15:15:12.317731 +0800 CST m=+9701.159389327
          - name: APP_NAME
            value: nginx2
          - name: POD_NAMESPACE
            value: luanhua-test-2
          - name: TZ
            value: Asia/Shanghai
          - name: NODE_NAME
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: spec.nodeName
          - name: POD_IP
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: status.podIP
          - name: K2_CLUSTER_ENV
            value: production
          - name: APP_ENV
            value: production
          - name: JAVA_APP_ENV
            value: production
          - name: APP_IDC
            value: hzifc
          image: dockerhub.test.wacai.info/library/nginx:latest
          imagePullPolicy: IfNotPresent
          name: nginx2
          resources:
            limits:
              cpu: "1"
              memory: 1Gi
            requests:
              cpu: 125m
              memory: 512Mi
          terminationMessagePath: /dev/termination-log
          terminationMessagePolicy: File
          volumeMounts:
          - mountPath: /data/k2/log/
            name: nginx2-log
        dnsPolicy: ClusterFirst
        nodeSelector:
          k2.wacai.com/luanhua-test-2: k2
        
```

## apiVersion

Kubernetes的官方文档中并没有对apiVersion的详细解释，而且因为K8S本身版本也在快速迭代，有些资源在低版本还在beta阶段，到了高版本就变成了stable。

如Deployment:

```java
1.6版本之前 apiVsersion：extensions/v1beta1

1.6版本到1.9版本之间：apps/v1beta1

1.9版本之后:apps/v1
```



## namespace

namespace需要先手动创建，命令如下

```yaml
kubectl create namespace my-namespace
```

也可以通过yaml配置更多属性

## sepc属性

### NodeName

`pod.spec.nodeName`用于强制约束将Pod调度到指定的Node节点上，这里说是“调度”，但其实指定了nodeName的Pod会直接跳过Scheduler的调度逻辑，直接写入PodList列表，该匹配规则是强制匹配。

```java
apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: tomcat-deploy
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: tomcat-app
    spec:
      nodeName: k8s.node1 #指定调度节点为k8s.node1
      containers:
      - name: tomcat
        image: tomcat:8.0
        ports:
        - containerPort: 8080
```




### NodeSelector

`Pod.spec.nodeSelector`是通过kubernetes的label-selector机制进行节点选择，由scheduler调度策略`MatchNodeSelector`进行label匹配，调度pod到目标节点，该匹配规则是强制约束。

Node添加label标记：

```yaml
#标记规则：kubectl label nodes <node-name> <label-key>=<label-value>
kubectl label nodes k8s.node1 cloudnil.com/role=dev

#确认标记
root@k8s.master1:~# kubectl get nodes k8s.node1 --show-labels
NAME        STATUS    AGE       LABELS
k8s.node1   Ready     29d       beta.kubernetes.io/arch=amd64,beta.kubernetes.io/os=linux,cloudnil.com/role=dev,kubernetes.io/hostname=k8s.node1
```

Pod定义中添加nodeSelector

```yaml
apiVersion: extensions/v1beta1
kind: Deployment
metadata:
  name: tomcat-deploy
spec:
  replicas: 1
  template:
    metadata:
      labels:
        app: tomcat-app
    spec:
      nodeSelector:
        cloudnil.com/role: dev #指定调度节点为带有label标记为：cloudnil.com/role=dev的node节点
      containers:
      - name: tomcat
        image: tomcat:8.0
        ports:
        - containerPort: 8080
```

