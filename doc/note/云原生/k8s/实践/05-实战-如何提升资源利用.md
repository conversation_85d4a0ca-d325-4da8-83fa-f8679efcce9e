## 概述

业务容器化后，如何将其部署在 K8S 上？如果仅仅是将它跑起来，很简单，但如果是上生产，我们有许多地方是需要结合业务场景和部署环境进行方案选型和配置调优的。比如，如何设置容器的 Request 与 Limit、如何让部署的服务做到高可用、如何配置健康检查、如何进行弹性伸缩、如何更好的进行资源调度、如何选择持久化存储、如何对外暴露服务等。

对于这一系列高频问题，这里将会出一个 Kubernetes 服务部署最佳实践的系列的文章来为大家一一作答，本文将先围绕如何合理利用资源的主题来进行探讨。



## 设置 Request 与 Limit

### 1、Request 工作原理

Request 的值并不代表给容器实际分配的资源大小，而是用于提供给调度器。调度器会检测每个节点可用于分配的资源（可分配资源减 request 之和），同时记录每个节点已经被分配的资源（节点上所有 Pod 中定义的容器 request 之和）。如发现节点剩余的可分配资源已小于当前需被调度的 Pod 的 request，则该 Pod 就不会被调度到此节点。反之，则会被调度到此节点。

若不配置 request，调度器就无法感知节点资源使用情况，无法做出合理的调度决策，可能会造成调度不合理，引起节点状态混乱。建议给所有容器设置 request，使调度器可感知节点资源情况，以便做出合理的调度决策。集群的节点资源能够被合理的分配使用，避免因资源分配不均而导致发生故障。



### 2、重要线上应用配置

节点资源不足时，会触发自动驱逐，删除低优先级的 Pod 以释放资源使节点自愈。Pod 优先级**由低到高**排序如下：

1. 未设置 request 及 limit 的 Pod。
2. 设置 request 值不等于 limit 值的 Pod。
3. 设置 request 值等于 limit 值的 Pod。

建议重要线上应用设置 request 值等于 limit 值，此类 Pod 优先级较高，在节点故障时不易被驱逐导致线上业务受到影响。



### 3、提高资源利用率

如应用设置了较高的 request 值，而实际占用资源远小于设定值，会导致节点整体的资源利用率较低。除对时延非常敏感的业务外，敏感的业务本身并不期望节点利用率过高，影响网络包收发速度。

建议对非核心，并且资源非长期占用的应用，适当减少 request 以提高资源利用率。若您的服务支持水平扩容，则除 CPU 密集型应用外，单副本的 request 值通常可设置为不大于1核。例如，coredns 设置为0.1核，即100m即可。



### 4、避免 request 与 limit 值过大

若您的服务使用单副本或少量副本，且 request 及 limit 的值设置过大，使服务可分配到足够多的资源去支撑业务。则某个副本发生故障时，可能会给业务带来较大影响。当 Pod 所在节点发生故障时，由于 request 值过大，且集群内资源分配的较为碎片化，其余节点无足够可分配资源满足该 Pod 的 request，则该 Pod 无法实现漂移，无法自愈，会加重对业务的影响。

建议尽量减小 request 及 limit，通过增加副本的方式对您的服务支撑能力进行水平扩容，使系统更加灵活可靠。



### 5、避免测试 namespace 消耗过多资源

若生产集群有用于测试的 namespace，如不加以限制，则可能导致集群负载过高，影响生产业务。可以使用 `ResourceQuota` 限制测试 namespace 的 request 与 limit 的总大小。示例如下：

```yaml
apiVersion: v1
kind: ResourceQuota
metadata:
  name: quota-test
  namespace: test
spec:
  hard:
    requests.cpu: "1"
    requests.memory: 1Gi
    limits.cpu: "2"
    limits.memory: 2Gi
```

## 如何让资源得到更合理的分配

设置 Request 能够解决让 Pod 调度到有足够资源的节点上，但无法做到更细致的控制。如何进一步让资源得到合理的使用？我们可以结合亲和性、污点与容忍等高级调度技巧，让 Pod 能够被合理调度到合适的节点上，让资源得到充分的利用。

### 1、使用亲和性

- 对节点有特殊要求的服务可以用节点亲和性 (Node Affinity) 部署，以便调度到符合要求的节点，比如让 MySQL 调度到高 IO 的机型以提升数据读写效率。
- 可以将需要离得比较近的有关联的服务用 Pod 亲和性 (Pod Affinity) 部署，比如让 Web 服务跟它的 Redis 缓存服务都部署在同一可用区，实现低延时。
- 也可使用 Pod 反亲和 (Pod AntiAffinity) 将 Pod 进行打散调度，避免单点故障或者流量过于集中导致的一些问题。

### 2、使用污点与容忍

使用污点 (Taint) 与容忍 (Toleration) 可优化集群资源调度:

- 通过给节点打污点来给某些应用预留资源，避免其它 Pod 调度上来。
- 需要使用这些资源的 Pod 加上容忍，结合节点亲和性让它调度到预留节点，即可使用预留的资源。

## 弹性伸缩

### 1、如何支持流量突发型业务

通常业务都会有高峰和低谷，为了更合理的利用资源，我们为服务定义 HPA，实现根据 Pod 的资源实际使用情况来对服务进行自动扩缩容，在业务高峰时自动扩容 Pod 数量来支撑服务，在业务低谷时，自动缩容 Pod 释放资源，以供其它服务使用（比如在夜间，线上业务低峰，自动缩容释放资源以供大数据之类的离线任务运行) 。

使用 HPA 前提是让 K8S 得知道你服务的实际资源占用情况(指标数据)，需要安装 resource metrics (metrics.k8s.io) 或 custom metrics (custom.metrics.k8s.io) 的实现，好让 hpa controller 查询这些 API 来获取到服务的资源占用情况。早期 HPA 用 resource metrics 获取指标数据，后来推出 custom metrics，可以实现更灵活的指标来控制扩缩容。官方有个叫 [metrics-server](https://github.com/kubernetes-sigs/metrics-server) 的实现，通常社区使用的更多的是基于 prometheus 的 实现 [prometheus-adapter](https://github.com/DirectXMan12/k8s-prometheus-adapter)，而云厂商托管的 K8S 集群通常集成了自己的实现，比如 TKE，实现了 CPU、内存、硬盘、网络等维度的指标，可以在网页控制台可视化创建 HPA，但最终都会转成 K8S 的 yaml，示例:

```yaml
apiVersion: autoscaling/v2beta2
kind: HorizontalPodAutoscaler
metadata:
  name: nginx
spec:
  scaleTargetRef:
    apiVersion: apps/v1beta2
    kind: Deployment
    name: nginx
  minReplicas: 1
  maxReplicas: 10
  metrics:
  - type: Pods
    pods:
      metric:
        name: k8s_pod_rate_cpu_core_used_request
      target:
        averageValue: "100"
        type: AverageValue
```

### 2、如何节约成本

HPA 能实现 Pod 水平扩缩容，但如果节点资源不够用了，Pod 扩容出来还是会 Pending。如果我们提前准备好大量节点，做好资源冗余，提前准备好大量节点，通常不会有 Pod Pending 的问题，但也意味着需要付出更高的成本。通常云厂商托管的 K8S 集群都会实现 [cluster-autoscaler](https://github.com/kubernetes/autoscaler/tree/master/cluster-autoscaler)，即根据资源使用情况，动态增删节点，让计算资源能够被最大化的弹性使用，按量付费，以节约成本。在 TKE 上的实现叫做伸缩组，以及一个包含伸缩功能组但更高级的特性：节点池(正在灰度)

### 3、无法水平扩容的服务怎么办

对于无法适配水平伸缩的单体应用，或者不确定最佳 request 与 limit 超卖比的应用，可以尝用 [VPA](https://github.com/kubernetes/autoscaler/tree/master/vertical-pod-autoscaler) 来进行垂直伸缩，即自动更新 request 与 limit，然后重启 pod。不过这个特性容易导致你的服务出现短暂的不可用，不建议在生产环境中大规模使用。

## 参考

- [Kubernetes 服务部署最佳实践(一) 如何合理利用资源](https://imroc.io/posts/kubernetes-app-deployment-best-practice-1/)

