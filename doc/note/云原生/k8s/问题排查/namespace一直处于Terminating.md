## 问题

出现无法删除的情况，是因为kubelet 阻塞，有其他的资源在使用该namespace，比如CRD等，尝试重启kubelet，再删除该namespace 也不好使。

在尝试以下命令强制删除也不好使：

```
kubectl delete ns <terminating-namespace> --force --grace-period=0
```



## 处理办法

1、选择一个Terminating namespace，并查看namespace 中的finalizer。运行以下命令：

```
kubectl get namespace <terminating-namespace> -o json >tmp.json
```

2、编辑tmp.josn，删除 spec 字段的值：

```yaml
{
  "apiVersion": "v1",
  "kind": "Namespace",
  "metadata": {
    "creationTimestamp": "2019-11-20T15:18:06Z",
    "deletionTimestamp": "2020-01-16T02:50:02Z",
    "name": "<terminating-namespace>",
    "resourceVersion": "3249493",
    "selfLink": "/api/v1/namespaces/knative-eventing",
    "uid": "f300ea38-c8c2-4653-b432-b66103e412db"
  },
  "spec": {
    "finalizers": []
  },
  "status": {
    "phase": "Terminating"
  }
}
```

3、 开启 proxy :

```
kubectl proxy
```



6、 运行以下命令：

```
curl -k -H "Content-Type: application/json" -X PUT --data-binary @tmp.json http://127.0.0.1:8001/api/v1/namespaces/<terminating-namespace>/finalize

```



## 原因

查看 namespace 出现如下错误提示:

```json
{
"lastTransitionTime": "2021-03-22T09:39:39Z",
"message": "Discovery failed for some groups, 3 failing: unable to retrieve the complete list of server APIs: custom.metrics.k8s.io/v1beta1: the server is currently unable to handle the request, external.metrics.k8s.io/v1beta1: the server is currently unable to handle the request, metrics.k8s.io/v1beta1: the server is currently unable to handle the request",
"reason": "DiscoveryFailed",
"status": "True",
"type": "NamespaceDeletionDiscoveryFailure"
}
```

看样子是和 k8s metrics api 相关

执行 kubectl get apiservice 命令:

```
v1beta1.custom.metrics.k8s.io               knative-serving/autoscaler            False (ServiceNotFound)    426d
v1beta1.discovery.k8s.io                    Local                                 True                       20d
v1beta1.events.k8s.io                       Local                                 True                       456d
v1beta1.extensions                          Local                                 True                       456d
v1beta1.external.metrics.k8s.io             oam/keda-operator-metrics-apiserver   False (ServiceNotFound)    333d
v1beta1.flagger.app                         Local                                 True                       20d
v1beta1.metrics.k8s.io                      kube-system/metrics-server            False (MissingEndpoints)   277d
```

可以看到几个组件都不可用，删除掉这三个错误的apiservice，故障恢复。