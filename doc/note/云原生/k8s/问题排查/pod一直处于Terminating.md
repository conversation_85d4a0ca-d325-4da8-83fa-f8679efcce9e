## 问题描述

pod 一直处于Terminating状态

```
➜  wse-master git:(master) ✗ kubectl get pods -n wse-test-test-env1
NAME                               READY   STATUS        RESTARTS   AGE
paas-test-app-1-6d8498cbb6-x9sb6   2/2     Terminating   0          59m
```

## 问题原因

详见 k8s删除pod机制.md，最后发现是一台node机器挂了

```shell
➜  wse-master git:(master) ✗ kubectl get node
NAME                                    STATUS     ROLES    AGE    VERSION
web-49-11-hzifc.node.hzifc.wacai.sdc    Ready      <none>   47d    v1.16.2
web-49-146-hzifc.node.hzifc.wacai.sdc   Ready      <none>   47d    v1.16.2
web-49-67-hzifc.node.hzifc.wacai.sdc    Ready      master   317d   v1.16.2
web-49-83-hzifc.node.hzifc.wacai.sdc    Ready      <none>   229d   v1.16.2
web-49-85-hzifc.node.hzifc.wacai.sdc    Ready      <none>   316d   v1.16.2
web-49-86-hzifc.node.hzifc.wacai.sdc    NotReady   <none>   316d   v1.16.2
```

