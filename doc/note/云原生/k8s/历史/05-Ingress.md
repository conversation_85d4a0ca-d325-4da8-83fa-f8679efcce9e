## 概述

在istio中通过gateway来统一管理外部流量，gateway内部也会依赖k8s的ingress控制器，本文就来搞清楚什么是k8s的ingress。ingress控制器是一个抽象概念，表示k8s网关管理，用来管理所有的入口流量。比较常见的实现有nginx-ingress，istio-ingressgateway，Traefik。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/k8s/11.png" alt="11" style="zoom:50%;" />

比较常用的是 nginx-ingress，nginx-ingress是一个pod，外部的数据统一经过这个pod，然后通过该pod内部的 nginx反向代理到各服务（Endpoint）。



## 一、nginx-ingress工作流程

![10](/work/dist/branch/wacai/middleware/my-boot/doc/images/k8s/10.png)

nginx-ingress这个pod做了所有service的代理，在高并发情况下将承受巨大压力，我们可以增加多个pod实例。



## 二、nginx-ingress 内部原理

### 1、 nginx-ingress pod

nginx-ingress pod有两个功能，controller和nginx：

```
controller：和kubernetes api通讯实时更新nginx配置（就是读取ingress yaml资源了）
nginx：正常的反向代理
```

那么pod中的nginx端口是如何配置的呢？我们在github上找到了nginx-ingress的deployment.yaml

```
https://github.com/kubernetes/ingress-nginx/blob/master/deploy/static/provider/cloud/deploy.yaml
```

其中一段

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: nginx-ingress-controller
  namespace: ingress-nginx
  labels:
    app.kubernetes.io/name: ingress-nginx
    app.kubernetes.io/part-of: ingress-nginx
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: ingress-nginx
      app.kubernetes.io/part-of: ingress-nginx
  template:
    metadata:
      labels:
        app.kubernetes.io/name: ingress-nginx
        app.kubernetes.io/part-of: ingress-nginx
      annotations:
        prometheus.io/port: "10254"
        prometheus.io/scrape: "true"
    spec:
      # wait up to five minutes for the drain of connections
      terminationGracePeriodSeconds: 300
      serviceAccountName: nginx-ingress-serviceaccount
      containers:
        - name: nginx-ingress-controller
          image: quay.io/kubernetes-ingress-controller/nginx-ingress-controller:0.26.1
          ...
          ...
          ...
          ports:
            - name: http
              containerPort: 80
            - name: https
              containerPort: 443
```

我们看到

```
- name: http
  containerPort: 80
- name: https
  containerPort: 443
```

默认对外监听了两个端口80和443，也就是说，有这两个端口对外就可以web服务了。

### 2、ingress 资源

ingress 资源通过yaml进行管理的，比如以下

```yaml
apiVersion: extensions/v1beta1
kind: Ingress
metadata:
  name: order
spec: 
  rules:
  - host: order.example.com
    http:
      paths: /
      backend: 
        serviceName: order
        servicePort: 80
```

以上我们定义了一个单一规则的ingress，nginx-ingress 接收到外部所有的请求，将被发送到内部order服务的80端口上。接下来我们看nginx-ingress如何把ingress资源转化为该pod中的nginx反向代理配置文件

```
upstream order{
    server order:80;
}

server {
    listen 80;
    server_name  order.example.com;
    ...
    ...
    location / {
        proxy_pass_header Server;
        proxy_set_header Host $http_host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Scheme $scheme;
        proxy_pass http://order; # 对应ingress 资源 name: order
    }
}
```

nginx-ingress-pod的工作过程就是读取k8s中定义的ingress，然后把它们转为nginx-config文件。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/k8s/12.png" alt="12" style="zoom:50%;" />

### 3、nginx-ingress对外提供服务

一般来讲，pod直接对外提供服务就只有两种方式：

- create一个service，该service暴漏nodePort
- forward 映射

我们一般采用第一种。
 nginx-ingress也是一个pod，所以，为了能使外部通过该pod代理访问，还需要nginx-ingress对外提供一个nodePort的service。这个service这里也不再写了。

## 参考

- [一篇文章看明白nginx-ingress控制器](https://juejin.im/post/6844903957479817230)
- [OpenResty 在又拍云容器平台中的应用](https://zhuanlan.zhihu.com/p/61924643)

