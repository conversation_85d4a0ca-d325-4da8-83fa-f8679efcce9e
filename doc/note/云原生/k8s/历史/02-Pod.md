## 概述

*Pod* 是可以在 Kubernetes 中创建和管理的、最小的可部署的计算单元。

## 使用 Pod

### 基本用法

作为 Kubernetes 集群中的基本单元，Pod 就是最小并且最简单的 Kubernetes 对象， 一个简单的YAML 描述如下：

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: busybox
  labels:
    app: busybox
spec:
  containers:
  - image: busybox
    command:
      - sleep
      - "3600"
    imagePullPolicy: IfNotPresent
    name: busybox
  restartPolicy: Always
```



这个 YAML 文件描述了一个 Pod 启动时运行的容器和命令以及它的重启策略，在当前 Pod 出现错误或者执行结束后是否应该被 Kubernetes 的控制器拉起来，除了这些比较显眼的配置之外，元数据 `metadata` 的配置也非常重要，`name` 是当前对象在 Kuberentes 集群中的唯一标识符，而标签 `labels` 可以帮助我们快速选择对象。

在 Pod 中可以同时运行一个或者多个容器，这些容器能够共享网络、存储以及 CPU、内存等资源。

通常不需要直接创建 Pod，甚至单实例 Pod。 相反，你会使用诸如 [Deployment](https://kubernetes.io/docs/concepts/workloads/controllers/deployment/) 或 [Job](https://kubernetes.io/docs/concepts/workloads/controllers/jobs-run-to-completion) 这类工作负载资源 来创建 Pod。如果 Pod 需要跟踪状态， 可以考虑 [StatefulSet](https://kubernetes.io/docs/concepts/workloads/controllers/statefulset/) 资源。

### 重启策略

Pod的重启策略 RestartPolicy 应用于 Pod 内的所有容器，并且仅在Pod所处的 Node 上由Kubelet进行判断和重启操作，当某个容器异常退出或者健康检查失败时，Kubelet将根据RestartPolicy 的设置来进行相应的操作。

RestartPolicy 包括：Always，OnFailure 以及 Never。他的缺省值是 Always。

- Always： 当容器失效时，由Kubelet 自动重启
- OnFailure： 当容器非0退出，由Kubelet 自动重启
- Never: 不论容器运行状态如何，Kubelet 都不会重启

RestartPolicy 重启之前会有一个渐进的延迟，延迟时长是同步频率的 0、1、2、4、8…倍，上限是五分钟，成功执行 10 分钟后会复位（延迟时间）。Pod 一旦绑定到了一个 Node 上，就不会再绑定到其他 Node 了。这意味着即便是只有一个 Pod，也需要有控制器来进行操作，这样在 Node 失败的时候，才能保证 Pod 的存活。

目前有三种可用的控制器：

- Job：用来执行会结束的 Pod (例如批处理运算)。
- ReplicationController：不需要结束的 Pod (例如 Web Server)。
- DaemonSet：每台（物理）机只能运行一个的 Pod，这种 Pod 提供机器相关的系统服务。如果在 ReplicationController 或者 Daemon 之间举棋不定，可以参考 [Daemon Set vs Replication Controller](http://kubernetes.io/v1.1/docs/admin/daemons.html#daemon-set-versus-replication-controller)。

ReplicationController 是唯一符合 RestartPolicy = Always 需要的。Job 就适合另外两种。

所有三种控制器都有对应的 PodTemplate，跟 Pod 的字段一致。建议创建控制器，让控制器创建 Pod，而不是自行直接创建 Pod。这是因为 Pod 不具备适应服务器失败的能力，而控制器可以。

### 健康检查

Pod的健康状态由两类探针来检查：`LivenessProbe`和`ReadinessProbe`。

**1. livenessProbe(存活探针)**

- 表明容器是否正在运行。
- 如果存活探测失败，则 kubelet 会杀死容器，并且容器将受到其 `重启策略`的影响。
- 如果容器不提供存活探针，则默认状态为 `Success`。

**2. readinessProbe(就绪探针)**

- 表明容器是否可以正常接受请求。
- 如果就绪探测失败，端点控制器将从与 Pod 匹配的所有 Service 的端点中删除该 Pod 的 IP 地址。
- 初始延迟之前的就绪状态默认为 `Failure`。
- 如果容器不提供就绪探针，则默认状态为 `Success`。

## Pod 容器

每一个 Pod 都具有两种不同的容器，两种不同容器的职责其实十分清晰：

- 一种是 `InitContainer`，这种容器会在 Pod 启动时运行，主要用于初始化一些配置。
- 另一种是 Pod 在 Running 状态时内部存活的 `Container`，它们的主要作用就是对外提供服务或者作为工作节点处理异步任务等等了。

## Pod 生命周期

### 创建

一般来说， Pod 创建之后就不会消失，除非被手工销毁。销毁手段可能是人工、ReplicationController 或者其他控制器。唯一的例外是处于 Succeeded 或者 Failed 阶段一定时间的 Pod 会因过期（由 Master 决定）而被自动销毁。

如果一个 Node 崩溃或者从集群断开，系统内的实体（目前称为 NodeController ）会负责执行策略（例如超时）并把丢失的 Node 中的所有 Pod 标记为 Failed。

和一个个独立的应用容器一样，Pod 也被认为是相对临时性（而不是长期存在）的实体。 Pod 会被创建、赋予一个唯一的 ID（[UID](https://kubernetes.io/zh/docs/concepts/overview/working-with-objects/names/#uids)）， 并被调度到节点，并在终止（根据重启策略）或删除之前一直运行在该节点。

Pod 的状态转换流程如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/k8s/8.png" alt="8" style="zoom:50%;" />

注意: 当Pod被Deployment管理时一般只会处于Running状态，但当作为Job运行时，就会进入Succeed或者Failed状态（取决于应用程序是否正常结束）

### 终止

终止过程主要分为如下几个步骤：

1. 用户发出删除 pod 命令
2. Pod 对象随着时间的推移更新，在宽限期（默认情况下30秒），pod 被视为“dead”状态
3. 将 pod 标记为“Terminating”状态
4. 第三步同时运行，监控到 pod 对象为“Terminating”状态的同时启动 pod 关闭过程
5. 第三步同时进行，endpoints 控制器监控到 pod 对象关闭，将pod与service匹配的 endpoints 列表中删除
6. 如果 pod 中定义了 preStop 钩子处理程序，则 pod 被标记为“Terminating”状态时以同步的方式启动执行；若宽限期结束后，preStop 仍未执行结束，第二步会重新执行并额外获得一个2秒的小宽限期
7. Pod 内对象的容器收到 TERM 信号
8. 宽限期结束之后，若存在任何一个运行的进程，pod 会收到 SIGKILL 信号
9. Kubelet 请求 API Server 将此 Pod 资源宽限期设置为0从而完成删除操作

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/k8s/9.png" alt="9" style="zoom:40%;" />

### 常见的状态转换场景

| Pod的容器数  | Pod当前状态 | 发生的事件      | Pod结果状态          |                         |                     |
| :----------- | :---------- | :-------------- | :------------------- | :---------------------- | :------------------ |
|              |             |                 | RestartPolicy=Always | RestartPolicy=OnFailure | RestartPolicy=Never |
| 包含一个容器 | Running     | 容器成功退出    | Running              | Succeeded               | Succeeded           |
| 包含一个容器 | Running     | 容器失败退出    | Running              | Running                 | Failure             |
| 包含两个容器 | Running     | 1个容器失败退出 | Running              | Running                 | Running             |
| 包含两个容器 | Running     | 容器被OOM杀掉   | Running              | Running                 | Failure             |



## Pod 状态(status)

Pod 的 `status` 字段是一个 [PodStatus](https://kubernetes.io/docs/reference/generated/kubernetes-api/v1.18/#podstatus-v1-core) 对象，可以通过 kubectl get pod <pod名称>  -o json 查看Pod状态，例如

```json
 status = {
		phase: "Running",
		podIP: "************",
     	containerStatuses: [{ 省略}],
     	hostIP:************, 
     	conditions: [{省略}]
 }
```

### Pod 阶段(Phase)

Pod 阶段是 Pod 在其生命周期中所处位置的简单宏观概述。他不是一个对 Pod 或者容器层次的状态的详尽结论，也不是一个全面的状态机。

 下面是 `phase` 可能的值：

| 取值                | 描述                                                         |
| ------------------- | ------------------------------------------------------------ |
| `Pending`（悬决）   | Pod 已被 Kubernetes 系统接受，但有一个或者多个容器尚未创建亦未运行。此阶段包括等待 Pod 被调度的时间和通过网络下载镜像的时间。 |
| `Running`（运行中） | Pod 已经绑定到了某个节点，Pod 中所有的容器都已被创建。至少有一个容器仍在运行，或者正处于启动或重启状态。 |
| `Succeeded`（成功） | Pod 中的所有容器都已成功终止，并且不会再重启。               |
| `Failed`（失败）    | Pod 中的所有容器都已终止，并且至少有一个容器是因为失败终止。也就是说，容器以非 0 状态退出或者被系统终止。 |
| `Unknown`（未知）   | 因为某些原因无法取得 Pod 的状态。这种情况通常是因为与 Pod 所在主机通信失败。 |

如果某节点死掉或者与集群中其他节点失联，Kubernetes 会实施一种策略，将失去的节点上运行的所有 Pod 的 `phase` 设置为 `Failed`。

### 容器状态

在PodStatus中的containerStatuses字段表示pod中的容器状态:

```json
status = {
	"containerStatuses": [
        {
        "image": "dockerhub.test.wacai.info/com.wacai.middleware/wse-server:3-b0d646d0",
        "imageID": "docker-pullable://dockerhub.test.wacai.info/com.wacai.middleware/wse-server@sha256:abf83e150cf9ab226af9a0edba013ca00f092b12e8654c8d2c185a7fd5abf4a3",
        "restartCount": 8,
        "ready": false,
        "name": "examplesbookinforeviews",
        "started": false,
        "state": {
            "waiting": {
                "reason": "CrashLoopBackOff",
                "message": "back-off 5m0s restarting failed container=examplesbookinforeviews pod=detail-tmp-6f6c948fb4-hqtnm_wse-test(1c85e2b5-4497-464f-8422-54398efb8a3c)"
            }
        },
        "containerID": "docker://feba41e2076c83de243300bf1836a596e26a89c66814d7f94b08d0fc9caf1364"
    }, 
    {
        "image": "istio/proxyv2:1.6.5",
        "imageID": "docker-pullable://istio/proxyv2@sha256:ec2df06d76e8845fbce0ac1b4b85ab06a7beabab8a69fcc3bb2b573378b71c47",
        "restartCount": 0,
        "ready": true,
        "name": "istio-proxy",
        "started": true,
        "state": {
            "running": {
                "startedAt": "2020-08-11T03:21:45Z"
            }
        },
        "lastState": {},
        "containerID": "docker://88e40270e7406f1a3cd500b2a10f403c55911013f4b559ec738e138f25c0e3e9"
    }]
}
```

每种状态都有特定的含义

#### Waiting （等待）

如果容器并不处在 `Running` 或 `Terminated` 状态之一，它就处在 `Waiting` 状态。 处于 `Waiting` 状态的容器仍在运行它完成启动所需要的操作：例如，从某个容器镜像 仓库拉取容器镜像，或者向容器应用 [Secret](https://kubernetes.io/docs/concepts/configuration/secret/) 数据等等。 当你使用 `kubectl` 来查询包含 `Waiting` 状态的容器的 Pod 时，你也会看到一个 Reason 字段，其中给出了容器处于等待状态的原因。常见的Reason包括:

- CrashLoopBackOff；容器奔溃
- ImagePullBackOff； 镜像无法拉取 

可以通过 kubectl -n linkup describe pod biz-app-698798c6c6-7b95m 查看具体原因：

```
Events:
  Type     Reason     Age                 From               Message
  ----     ------     ----                ----               -------
  Normal   Scheduled  104s                default-scheduler  Successfully assigned linkup/biz-app-698798c6c6-7b95m to 192.168.64.5
  Normal   Pulling    29s (x4 over 104s)  kubelet            Pulling image "registry.baidubce.com/hz-code/biz-app:36-314df5db"
  Warning  Failed     28s (x4 over 104s)  kubelet            Failed to pull image "registry.baidubce.com/hz-code/biz-app:36-314df5db": rpc error: code = Unknown desc = Error response from daemon: unauthorized: authentication required
  Warning  Failed     28s (x4 over 104s)  kubelet            Error: ErrImagePull
  Normal   BackOff    4s (x6 over 104s)   kubelet            Back-off pulling image "registry.baidubce.com/hz-code/biz-app:36-314df5db"
  Warning  Failed     4s (x6 over 104s)   kubelet            Error: ImagePullBackOff
```





#### Running（运行中）

`Running` 状态表明容器正在执行状态并且没有问题发生。 如果配置了 `postStart` 回调，那么该回调已经执行完成。 如果你使用 `kubectl` 来查询包含 `Running` 状态的容器的 Pod 时，你也会看到 关于容器进入 `Running` 状态的信息。

#### Terminated（已终止）

处于 `Terminated` 状态的容器已经开始执行并且或者正常结束或者因为某些原因失败。 如果你使用 `kubectl` 来查询包含 `Terminated` 状态的容器的 Pod 时，你会看到 容器进入此状态的原因、退出代码以及容器执行期间的起止时间。

### Pod 状况(Conditions)

在PodStatus中的 conditions 字段表示Pod 中容器的就绪检测会报告就绪状况，其取值可能是 True、False 或者 Unknown。例如：

```json
conditions=[
{
	"type": "Initialized",
	"lastTransitionTime": "2020-08-11T06:54:34Z",
	"status": "True"
}, {
	"reason": "ContainersNotReady",
	"type": "Ready",
	"lastTransitionTime": "2020-08-11T06:54:05Z",
	"message": "containers with unready status: [examplesbookinforeviews]",
	"status": "False"
}, {
	"reason": "ContainersNotReady",
	"type": "ContainersReady",
	"lastTransitionTime": "2020-08-11T06:54:05Z",
	"message": "containers with unready status: [examplesbookinforeviews]",
	"status": "False"
}, {
	"type": "PodScheduled",
	"lastTransitionTime": "2020-08-11T06:54:05Z",
	"status": "True"
}]
```

`PodCondition`包含以下以下字段：

- `lastProbeTime`：Pod condition最后一次被探测到的时间戳。
- `lastTransitionTime`：Pod最后一次状态转变的时间戳。
- `message`：状态转化的信息，一般为报错信息，例如：containers with unready status: [c-1]。
- `reason`：最后一次状态形成的原因，一般为报错原因，例如：ContainersNotReady。
- `status`：包含的值有 True、False 和 Unknown。
- `type`：Pod状态的几种类型。

其中type字段包含以下几个值：

- `PodScheduled`：Pod已经被调度到运行节点。
- `Ready`：Pod已经可以接收请求提供服务。
- `Initialized`：所有的init container已经成功启动。
- `Unschedulable`：无法调度该Pod，例如节点资源不够。
- `ContainersReady`：Pod中的所有容器已准备就绪。

## 事件

一个pod的完整创建，通常会伴随着各种事件的产生，k8s种事件的种类总共只有4种：

```java
Added EventType = "ADDED"

Modified EventType = "MODIFIED"

Deleted EventType = "DELETED"

Error EventType = "ERROR"
```



### 在Pod中使用ConfigMap

首先需要创建ConfigMap

```yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: app-configs
  namespace: wse-test
data:
  appsettings: |
    log.level=ERROR
  app.config: |
    port=8080
```

上述yaml表示创建 appsettings和app.config两个文件

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: paas-test-app-1-1
  namespace: wse-test
  labels:
    app: paas-test-app
    version: paas-test-app-1-1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: paas-test-app
      version: paas-test-app-1-1
  template:
    metadata:
      labels:
        app: paas-test-app
        version: paas-test-app-1-1
    spec:
      containers:
      - name: paas-test-app-1
        image: docker.io/istio/examples-bookinfo-ratings-v2:1.15.1
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
        volumeMounts:
        - name: app-configs
          mountPath: /app-config
      volumes:
      - name: app-configs
        configMap:
          name: app-configs
  strategy:
    type: RollingUpdate
  minReadySeconds: 30
```



## 参考

- [kubelet Pod 的状态分析](https://www.jianshu.com/p/1d0721ddf4c8)
- [Kubernetes之Pod详解](https://www.huweihuang.com/article/kubernetes/kubernetes-pod-introduction/)

