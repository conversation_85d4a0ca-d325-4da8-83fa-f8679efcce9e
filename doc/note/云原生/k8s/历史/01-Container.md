## 概述

容器是一种用来打包已经编译好的代码以及运行时需要的各个依赖项的技术。您运行的每个容器都是可以重复运行的；包含依赖项的标准化意味着您在任何地点运行它都会得到相同的结果。

容器将应用程序和底层主机架构解耦，这使得在不同的云或OS环境中部署应用更加容易。

## 容器镜像

容器镜像是一个现成的软件包，包含了运行应用程序时所需要的一切：代码和任何运行时所需的东西，应用程序和系统库，以及任何基本设置的默认值。根据设计，容器是不可变的，如果您需要更改容器，您需要构建一个新的容器镜像来重新创建容器。

默认的镜像拉取策略是 `IfNotPresent`，在镜像已经存在的情况下，kubelet 将不再去拉取镜像。如果总是想要拉取镜像，您可以执行以下操作：

- 设置容器的 `imagePullPolicy` 为 `Always`。
- 省略 `imagePullPolicy`，并使用 `:latest` 作为要使用的镜像的标签。
- 省略 `imagePullPolicy` 和要使用的镜像标签。
- 启用 [AlwaysPullImages](https://kubernetes.io/docs/reference/access-authn-authz/admission-controllers/#alwayspullimages) 准入控制器（admission controller）。

注意：应避免使用 `:latest` 标签，参见[配置镜像最佳实践](https://kubernetes.io/docs/concepts/configuration/overview/#container-images) 获取更多信息

## 容器运行时

容器运行环境是负责运行容器的软件。

Kubernetes 支持多个容器运行环境: [Docker](http://www.docker.com/)、 [containerd](https://containerd.io/)、[cri-o](https://cri-o.io/)、 [rktlet](https://github.com/kubernetes-incubator/rktlet) 以及任何实现 [Kubernetes CRI (容器运行环境接口)](https://github.com/kubernetes/community/blob/master/contributors/devel/sig-node/container-runtime-interface.md)。

## 容器状态

一旦[调度器](https://kubernetes.io/docs/reference/generated/kube-scheduler/)将 Pod 分派给某个节点，`kubelet` 就通过 容器运行时 开始为 Pod 创建容器。 容器的状态有三种：`Waiting`（等待）、`Running`（运行中）和 `Terminated`（已终止）。

要检查 Pod 中容器的状态，你可以使用 kubectl describe pod <pod 名称>。 其输出中包含 Pod 中每个容器的状态：

### `Waiting` （等待）

如果容器并不处在 `Running` 或 `Terminated` 状态之一，它就处在 `Waiting` 状态。 处于 `Waiting` 状态的容器仍在运行它完成启动所需要的操作：例如，从某个容器镜像 仓库拉取容器镜像，或者向容器应用 [Secret](https://kubernetes.io/docs/concepts/configuration/secret/) 数据等等。 当你使用 `kubectl` 来查询包含 `Waiting` 状态的容器的 Pod 时，你也会看到一个 Reason 字段，其中给出了容器处于等待状态的原因。

### `Running`（运行中）

`Running` 状态表明容器正在执行状态并且没有问题发生。 如果配置了 `postStart` 回调，那么该回调已经执行完成。 如果你使用 `kubectl` 来查询包含 `Running` 状态的容器的 Pod 时，你也会看到 关于容器进入 `Running` 状态的信息。

### `Terminated`（已终止）

处于 `Terminated` 状态的容器已经开始执行并且或者正常结束或者因为某些原因失败。 如果你使用 `kubectl` 来查询包含 `Terminated` 状态的容器的 Pod 时，你会看到 容器进入此状态的原因、退出代码以及容器执行期间的起止时间。

如果容器配置了 `preStop` 回调，则该回调会在容器进入 `Terminated` 状态之前执行。



## 设置command和args

```yaml
[root@node131 yaml]# cat centos.yaml 
apiVersion: v1    
kind: Pod    
metadata:    
  name: centos  
  labels:    
    app: centos    
spec:    
  containers:    
  - name: mycentos  
    image: centos  
    imagePullPolicy: IfNotPresent  
    command: ["/bin/sh","-c","while true;do echo hello;sleep 1;done"]

```



## 参考

- https://kubernetes.io/zh/docs/concepts/containers/container-lifecycle-hooks/