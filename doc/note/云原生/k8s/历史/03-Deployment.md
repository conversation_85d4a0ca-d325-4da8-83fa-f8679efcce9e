## 概述

在k8s通过 Deployment 来管理一系列Pod。

## 更新策略

默认情况下，Kubernetes 的 Deployment 是具有滚动更新的策略来进行 Pod 更新的，该策略可以在更新应用的时候保证某些实例依然可以正常运行来防止应用 down 掉，当新部署的 Pod 启动并可以处理流量之后，才会去杀掉旧的 Pod。

举个栗子，在刚更新镜像时，Deployment不会立即杀掉之前正在运行的Pod，而是等待新的Pod能成功运行之后在杀掉老的。

在使用过程中我们还可以指定 Kubernetes 在更新期间如何处理多个副本的切换方式，比如我们有一个3副本的应用，在更新的过程中是否应该立即创建这3个新的 Pod 并等待他们全部启动，或者杀掉一个之外的所有旧的 Pod，或者还是要一个一个的 Pod 进行替换？下面示例是使用默认的滚动更新升级策略的一个 Deployment 定义，在更新过程中最多可以有一个超过副本数的容器（maxSurge），并且在更新过程中没有不可用的容器。

```yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: zero-downtime
  labels:
    app: zero-downtime
spec:
  replicas: 3
  selector:
    matchLabels:
      app: zero-downtime
  strategy:
    type: RollingUpdate
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 0
  template:
    # with image nginx
    # ...
```

上面的 `zero-downtime` 这个应用使用 nginx 这个镜像创建3个副本，该 Deployment 执行滚动更新的方式：首先创建一个新版本的 Pod，等待 Pod 启动并准备就绪，然后删除一个旧的 Pod，然后继续下一个新的 Pod，直到所有副本都已替换完成。为了让 Kubernetes 知道我们的 Pod 何时可以准备处理流量请求了，我们还需要配置上 liveness 和 readiness 探针。下面展示的就是`新旧` Pod 替换的输出信息：

```
$ kubectl get pods
NAME                            READY   STATUS              RESTARTS   AGE
zero-downtime-d449b5cc4-k8b27   1/1     Running             0          3m9s
zero-downtime-d449b5cc4-n2lc4   1/1     Running             0          3m9s
zero-downtime-d449b5cc4-sdw8b   1/1     Running             0          3m9s
...

zero-downtime-d449b5cc4-k8b27   1/1     Running             0          3m9s
zero-downtime-d449b5cc4-n2lc4   1/1     Running             0          3m9s
zero-downtime-d449b5cc4-sdw8b   1/1     Running             0          3m9s
zero-downtime-d569474d4-q9khv   0/1     ContainerCreating   0          12s
...

zero-downtime-d449b5cc4-n2lc4   1/1     Running             0          3m9s
zero-downtime-d449b5cc4-sdw8b   1/1     Running             0          3m9s
zero-downtime-d449b5cc4-k8b27   1/1     Terminating         0          3m29s
zero-downtime-d569474d4-q9khv   1/1     Running             0          1m
...

zero-downtime-d449b5cc4-n2lc4   1/1     Running             0          5m
zero-downtime-d449b5cc4-sdw8b   1/1     Running             0          5m
zero-downtime-d569474d4-q9khv   1/1     Running             0          1m
zero-downtime-d569474d4-2c7qz   0/1     ContainerCreating   0          10s
...

...

zero-downtime-d569474d4-2c7qz   1/1     Running             0          40s
zero-downtime-d569474d4-mxbs4   1/1     Running             0          13s
```

