## 实践

### 常用命令

```shell
## 构建镜像
docker build -t so0k/envtest - << EOF
FROM alpine:latest
RUN apk --update add bind-tools && apk add curl && apk add tcpdump && apk add nginx && rm -rf /var/cache/apk/*
EXPOSE 80
EOF

## 运行
docker run -it --rm --name host2 so0k/envtest sh

## 运行
docker run -it --rm alpine sh

## 执行
docker exec -it host1 sh

## 删除所有镜像
docker rm $(docker ps -a -q)
```



### 常用镜像

查找镜像:  docker search nginx

| 镜像名                           | 大小   | 作用                                                       |
| -------------------------------- | ------ | ---------------------------------------------------------- |
| busybox                          | 1.15MB | 临时测试用                                                 |
| alpine                           | 4.41MB | 一个精简版的linux，它也有包管理器apk，比如 apk add tcpdump |
| ubuntu                           | 81.1MB |                                                            |
| centos:6.8                       | 200MB  |                                                            |
| fabric8/java-alpine-openjdk8-jdk |        | https://hub.docker.com/r/fabric8/java-alpine-openjdk8-jdk  |

### 日常安装

mysql

```
 docker run -itd --name mysql-test -p 3306:3306 -e MYSQL_ROOT_PASSWORD=123456 mysql
```

busybox

```
docker run -it --rm --name busybox1 busybox sh
```



## Docker 运行

```
#  docker run -it --rm alpine sh

Usage:  docker run [OPTIONS] IMAGE [COMMAND] [ARG...]

```

注意 run 的所有 options 都在image 之前，这样执行是错误的: docker run --name debugtools -d alpine --pid=zkone:zookeeper

可以使用 bash ，这样不会自动启动容器

```
docker run -it --rm --name tomcat tomcat bash
```

### Options

详细内容见官方文档 [Docker run reference](https://docs.docker.com/engine/reference/run/) ，常见选项如下：

| 选项             | 说明                                                         | 值                 |
| :--------------- | :----------------------------------------------------------- | ------------------ |
| --attach` , `-a  | Attach to STDIN, STDOUT or STDERR                            |                    |
| --cap-add        | 添加 Linux capabilities                                      | SYS_ADMIN          |
| --env` , `-e     | 设置环境变量                                                 | -e MODE=standalone |
| --ipc            | 设置IPC mode                                                 | shareable          |
| --label` , `-l   | 设置meta data                                                |                    |
| --link           | Add link to another container                                |                    |
| --net            | Connect a container to a network                             |                    |
| --pid            | PID namespace to use                                         |                    |
| --rm             | Automatically remove the container when it exits，一般组合使用 -it --rm |                    |
| --publish` , `-p | Publish a container's port(s) to the host                    | -p 8080:8080       |
| –net             |                                                              |                    |

### Detached vs foreground

容器的运行方式有前台和后台（detached）两种模式，默认为前台运行。

Detached (-d)

| 选项 | 说明 |
| :--  | :--  |
| -d, --detach | 后台运行容器，并输出容器 id |

使用 `-d` 选项或者 `-d=true` 使得容器后台运行：

```
# docker run -d busybox sleep 20
a69a80e9e16298255612c6ba73efc94b3d43d40d7ae30e4832c5e4b41de24356
# docker attach a69a80
```

使用 `docker attach` 命令重新连接后台容器。`attach` 可以理解为在当前终端，连接到后台运行的容器，等同前台操作运行容器。

Foreground

| 选项 | 说明 |
| :--  | :--  |
| -a, --attach list | Attach to STDIN, STDOUT or STDERR (default []) |
| -t, --tty | 分配一个伪终端 |
| --sig-proxy | 转发所有的信号给进程 (默认为 true，仅在 non-tty 模式下生效) |
| -i, --interactive | 保持 STDIN 打开即使在后台运行 |

```
# docker run --rm -it busybox sh
/ # echo "This is a test"
This is a test
```

- `--rm`: 选项表示容器停止后，自动清理容器。
- `-it`: `-i`、`-t` 选项一般同时执行，用于和容器交互操作，比较常用。

**注：** 容器是否会长久运行，是和 `docker run` 指定的命令有关，和 `-d` 参数无关。

https://wiki.opskumu.com/docker/chapter4#yun-hang-shi-zi-yuan-xian-zhi)

### 重启

被杀死的容器可以通过 docker restart 重启

## 进入容器

docker attach  和 docker exec都是进入容器命令，不过从 attach 的stdin 中 exit，会导致容器的停止。

所以推荐使用docker exec

```
docker exec -it d54f576cbd68 bash
```



## 查看 docker

通过 docker info 查看 docker 信息

```
version:
  APIVersion: 3.2.3
  Built: 1632432139
  BuiltTime: Fri Sep 24 05:22:19 2021
  GitCommit: ""
  GoVersion: go1.15.14
  OsArch: linux/amd64
  Version: 3.2.3
  Docker Root Dir: /var/lib/docker
```



## 查看容器

docker inspect 查看 docker 容器情况：

```
[root@iZbp1ax83amkj74axwq5sqZ ~]# docker inspect c5e8ceba2e11
[
    {
        "Id": "c5e8ceba2e11a81112bea730b531cadfe557445f1749a64e9e0b56f6a9098d30",
        "Created": "2021-12-09T00:29:04.800275543+08:00",
        "Path": "sh",
        "Args": [
            "sh"
        ],
    	"Image": "ffe9d497c32414b1c5cdad8178a85602ee72453082da2463f1dede592ac7d5af",
        "ImageName": "docker.io/library/busybox:latest",        
        "ResolvConfPath": "/run/containers/storage/overlay-containers/c5e8ceba2e11a81112bea730b531cadfe557445f1749a64e9e0b56f6a9098d30/userdata/resolv.conf",
        "HostnamePath": "/run/containers/storage/overlay-containers/c5e8ceba2e11a81112bea730b531cadfe557445f1749a64e9e0b56f6a9098d30/userdata/hostname",
        "HostsPath": "/run/containers/storage/overlay-containers/c5e8ceba2e11a81112bea730b531cadfe557445f1749a64e9e0b56f6a9098d30/userdata/hosts",
```

## 容器共享

docker 可以通过参数和另一个容器共享同一个网络,进程信息,这调试容器情况下非常方便

```
# docker run -d tomcat
770f3efee254a5490957ac90c695e2626e25e946df93d2e7eee7adaa05821a65

# docker run -it --rm --net=container:770f3efee254 --pid=container:770f3efee254 --ipc=shareable alpine
```

- --net=container 表示使用网络共享
- --pid=container 表示PID Namespace共享
- --ipc=shareable 表示IPC Namespace共享



## Reference

- [docker-chapter4](https://wiki.opskumu.com/docker/chapter4)
