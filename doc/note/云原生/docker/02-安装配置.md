## 安装入门

上文说的 `Moby` 在 Docker 官网称为社区版，支持的系统可以参见 [Install Docker](https://docs.docker.com/engine/installation/)。从 Docker `17.03` 开始，Docker 使用基于时间的版本发行机制。支持的系统除了常见的 Linux 发行版外，还支持 macOS、Windows 系统。本文只介绍基于 macOS 和 CentOS 这两个系统的 Docker 安装，关于更多系统的安装方式参见前面提到的官网安装文档。

## 镜像地址

因为国内下载镜像比较慢的原因，所以需要额外配置一下国内的 Registry mirror 用以加速镜像下载：

通过 /etc/docker/daemon.json 配置：

```
{
  "registry-mirrors": [https://dockerproxy.com","https://hub.c.163.com/"],
  "data-root": "/data/docker-data"
}
```

dockerproxy 使用方式参考[官方文档](https://dockerproxy.com/)



## CentOS 7 Docker 安装

关于 Docker 社区版在 CentOS 上的安装，官网提供了教程 [Get Docker CE for CentOS](https://docs.docker.com/engine/installation/linux/docker-ce/centos/)

Docker 已收录在 `CentOS-Extras` 软件库内，**推荐直接通过如下方式安装**：

```
curl -fsSL https://get.docker.com | bash -s docker --mirror Aliyun
```

老方法如下：

删除之前已安装的:

```
yum remove docker \
                  docker-client \
                  docker-client-latest \
                  docker-common \
                  docker-latest \
                  docker-latest-logrotate \
                  docker-logrotate \
                  docker-engine
```

安装:

```
 yum install docker docker-compose 
```

启动 docker daemon

```
systemctl start docker
```

使用默认配置所有的镜像默认都安装在 /var/lib/docker ，会导致 root 目录空间不够。

[官方文档]( https://docs.docker.com/config/daemon/systemd/)  新版本docker通过 /etc/docker/daemon.json 配置

```json
{
  "registry-mirrors": ["https://hub.c.163.com/"],
  "data-root": "/data/docker-data"
}
```

老版本通过这种方式启动会失败：

```properties
systemctl status docker.service
● docker.service - Docker Application Container Engine
   Loaded: loaded (/usr/lib/systemd/system/docker.service; disabled; vendor preset: disabled)
   Active: failed (Result: exit-code) since Thu 2022-02-17 22:53:53 CST; 6s ago
     Docs: http://docs.docker.com
  Process: 3676289 ExecStart=/usr/bin/dockerd-current --add-runtime docker-runc=/usr/libexec/docker/docker-runc-current --default-runtime=docker-runc --exec-opt native.cgroupdriver=systemd --userland-proxy-path=/usr/libexec/docker/docker-proxy-current --init-path=/usr/libexec/docker/docker-init-current --seccomp-profile=/etc/docker/seccomp.json --graph=/data/docker-data $OPTIONS $DOCKER_STORAGE_OPTIONS $DOCKER_NETWORK_OPTIONS $ADD_REGISTRY $BLOCK_REGISTRY $INSECURE_REGISTRY $REGISTRIES (code=exited, status=1/FAILURE)
 Main PID: 3676289 (code=exited, status=1/FAILURE)
```

需要修改 /usr/lib/systemd/system/docker.service， 在 ExecStart 末尾增加 --graph=/data/docker-data 可以，比如:

```properties
ExecStart=/usr/bin/dockerd-current \
          --add-runtime docker-runc=/usr/libexec/docker/docker-runc-current \
          --default-runtime=docker-runc \
          --exec-opt native.cgroupdriver=systemd \
          --userland-proxy-path=/usr/libexec/docker/docker-proxy-current \
          --init-path=/usr/libexec/docker/docker-init-current \
          --seccomp-profile=/etc/docker/seccomp.json \
          --graph=/data/docker-data \
```





## Linux 离线安装

可参考[docker入门到是实践离线安装一节](https://yeasy.gitbook.io/docker_practice/install/offline)



## 配置说明

以下配置说明，统一以 `CentOS 7.3` 为系统环境，其它系统版本可能会有所不同。

## 相关配置文件

基本配置文件：

- /etc/sysconfig/docker
- /etc/sysconfig/docker-storage-setup
- /etc/sysconfig/docker-network
- /etc/docker/daemon.json

systemd 服务配置：

- /usr/lib/systemd/system/docker.service

Docker 从 `1.12` 开始支持通过 `/etc/docker/daemon.json` 文件管理 Docker daemon 的配置选项。

## 参考

- [docker-chapter3](https://wiki.opskumu.com/docker/chapter3)
