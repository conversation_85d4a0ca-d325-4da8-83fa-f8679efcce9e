## 理论概述

容器本质是一项隔离技术，很好的解决了他的前任 - 虚拟化未解决的问题：运行环境启动速度慢、资源利用率低，而容器技术的两个核心概念，Namespace 和 Cgroup，恰到好处的解决了这两个难题。

Namespace 作为**看起来是隔离**的技术，替代了 Hypervise 和 GuestOS，在原本在两个 OS 上的运行环境演进成一个，运行环境更加轻量化、启动快，Cgroup 则被作为**用起来是隔离**的技术，限制了一个进程只能消耗整台机器的部分 CPU 和内存。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211228221000535.png" alt="image-20211228221000535" style="zoom:50%;" />

容器本身不是一个新技术，早期的 Linux 容器是基于 LXC 去管理的，而 Docker 让容器变得更易用。对于 Docker，可以认为它是一个开源的容器引擎，可以方便的对容器进行管理，并且通过镜像交付的方式，达到更简单的环境构建，理念就是 “Build, Ship, and Run Any App, Anywhere”。



## Docker 名词介绍

### Docker 镜像

Docker 镜像是 Docker 容器运行时的只读模板，每一个镜像由一系列的层 (layers) 组成。按照官方说明，镜像是一个轻量级，独立的，可执行的，包括软件运行一切所需，囊括了代码，运行态，lib 库以及环境变量和配置文件的包。通俗的理解，可以理解为一个封装好环境的集装箱。

### Docker 容器

容器是通过 Docker 镜像创建的一个运行态的实例，可以针对 Docker 容器执行运行、开始、停止、移动和删除等操作。

### Docker Registry

`Registry` 用来存放 Docker 镜像，如果把 Docker 镜像比作集装箱的话，那么 `Registry` 可比喻成装载集装箱的大货轮。`Registry` 有公有和私有的概念，Docker 官方 `Registry` 为 [Docker Hub](https://hub.docker.com)，国内的如阿里云、网易蜂巢、时速云等也均有相关仓库。Docker 镜像仓库起到了一个集中存储和分发 Docker 镜像的作用。

三者之间的关系可以参考下图（摘自《DevOps Kubernetes》）：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211228221034806.png" alt="image-20211228221034806" style="zoom:50%;" />

## Docker 架构

Docker 使用C/S架构， Docker client 与 Docker daemon通信，Docker daemon负责打包，运行，分发你的docker容器。Docker client可以和Docker daemon运行在一台机器，也可以将 Docker 客户端连接到远程 Docker daemon，Docker client和daemon使用 REST API、UNIX sockets或 network interface进行通信。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211228221237350.png" alt="image-20211228221237350" style="zoom:50%;" />

## Docker 原理

关于 Docker 的原理需要结合 Linux 底层的 `Cgroup` 和 `Namespace` 去理解。Docker 通过 `Cgroup` 实现针对每个容器的资源管理，如 CPU、Memory、IO 等，而通过 `Namespace` 让每个容器都拥有自己的命名空间，包括 PID、USER、UTS、MNT、NET、IPC 等。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211228221140261.png" alt="image-20211228221140261" style="zoom:50%;" />



## Namespace

Linux Namespace是Linux提供的一种内核级别环境隔离的方法。

举个例子，我们都知道，Linux下的超级父亲进程的PID是1，所以，同chroot一样，如果我们可以把用户的进程空间jail到某个进程分支下，并像chroot那样让其下面的进程 看到的那个超级父进程的PID为1，于是就可以达到资源隔离的效果了（不同的PID namespace中的进程无法看到彼此）

**Linux Namespace 有如下种类**，官方文档在这里《[Namespace in Operation](https://lwn.net/Articles/531114/)》

| 分类                   | 系统调用参数  | 相关内核版本                                                 |
| :--------------------- | :------------ | :----------------------------------------------------------- |
| **Mount namespaces**   | CLONE_NEWNS   | [Linux 2.4.19](https://lwn.net/2001/0301/a/namespaces.php3)  |
| **UTS namespaces**     | CLONE_NEWUTS  | [Linux 2.6.19](https://lwn.net/Articles/179345/)             |
| **IPC namespaces**     | CLONE_NEWIPC  | [Linux 2.6.19](https://lwn.net/Articles/187274/)             |
| **PID namespaces**     | CLONE_NEWPID  | [Linux 2.6.24](https://lwn.net/Articles/259217/)             |
| **Network namespaces** | CLONE_NEWNET  | [始于Linux 2.6.24 完成于 Linux 2.6.29](https://lwn.net/Articles/219794/) |
| **User namespaces**    | CLONE_NEWUSER | [始于 Linux 2.6.23 完成于 Linux 3.8)](https://lwn.net/Articles/528078/) |

如果需要深入理解相关知识，可以通过以下文章进一步学习：

- [Docker 核心技术与实现原理](https://draveness.me/docker)
- [Docker基础技术：Linux CGroup](https://coolshell.cn/articles/17049.html)
- [Docker基础技术：Linux Namespace（上）](https://coolshell.cn/articles/17010.html)
- [Docker基础技术：Linux Namespace（下）](https://coolshell.cn/articles/17029.html)

## 参考

- [opskumu-wiki](https://github.com/opskumu/wiki)

