## 测试环境

已经在 172.16.49.96 安装了docker环境



## 镜像构建

除了可以通过 [Docker hub](https://hub.docker.com) 获取到公共镜像，还可以通过 Dockerfile 构建自定义镜像，Dockerfile 是一个包含创建镜像所有命令的文本文件，Docker 通过 docker build  命令构建镜像。

一个最简单的[例子](http://docker-saigon.github.io/post/Docker-Internals/)：

 ```shell
 ## 构建镜像
 docker build -t so0k/envtest - << EOF
 FROM alpine:latest
 RUN apk --update add bind-tools && apk add curl && apk add tcpdump && apk add nginx && rm -rf /var/cache/apk/*
 EXPOSE 80
 EOF
 ```

上述命令会创建了一个名叫  so0k/envtest 的镜像。

 docker build 使用 **Dockerfile** 和 **context 目录**来构建镜像，用法如下:

```
$ docker build -t dubbo-admin .             # 默认读取当前目录下名为 Dockerfile 的文件
$ docker build -f /path/to/a/Dockerfile .   # 指定 Dockerfile
```

通过 `-t` 可以指定镜像仓库和标签:

```
 docker build -t dubbo-admin .
```

`-t` 选项可以指定多次：

```
$ docker build -t shykes/myapp:1.0.2 -t shykes/myapp:latest .
```

为了提升构建性能，可以通过在当前构建环境根目录下创建 `.dockerignore` 文件来排除一些不必要的文件和目录（类似 `.gitignore`）。

## Context 目录

> context目录是会递归的，所以千万不要用根目录作为 context 目录，因为这样会导致把整个文件系统给 Docker daemon。

## Dockerfile 

>  官方的Dockerfile[最佳实践](https://docs.docker.com/develop/develop-images/dockerfile_best-practices/)

一个 Dockerfile 的例子：

```yaml
# sets the base image for subsequent instructions
FROM dockerhub.test.wacai.info/library/tomcat:8.0.41

# sets the environment variable
ENV APP_NAME dubbo-admin
ENV APP_DIR /data/program/$APP_NAME

# copies new files to the filesystem of the container
COPY k2/bin $APP_DIR/bin
COPY conf $APP_DIR/conf
COPY target/${APP_NAME}*.war $APP_DIR/target/$APP_NAME.war

# sets the working directory
WORKDIR $APP_DIR

# creates a mount point
VOLUME /data/k2/log

# provide defaults for an executing container
CMD ["./bin/start.sh"]

```

### FROM

 用来指定构建镜像的基础镜像，如果本地没有指定的镜像，在构建过程中会自动从相应镜像仓库 pull。如果 `FROM` 语句没有指定镜像标签，则默认使用 latest 标签。

### ENV

设置环境变量，无论是后面的其它指令，如 `RUN`，还是运行时的应用，都可以直接使用这里定义的环境变量。



### COPY& ADD

- COPY. 指令将从构建上下文目录中源路径的文件或目录复制到新的一层的镜像内的目标路径位置。
- ADD. 和COPY类似，但是功能更强大。



### WORKDIR

WORKDIR 用来切换工作目录的。Docker 默认的工作目录是/，只有 RUN 能执行 cd 命令切换目录，而且还只作用在当下下的 RUN，也就是说每一个 RUN 都是独立进行的，WORKDIR 切换影响的是全局。



###  RUN & CMD & ENTRYPOINT

- **RUN.** Mainly used to build images and install applications and packages. Builds a new layer over an existing image by committing the results.
- **CMD.** Sets default parameters that can be overridden from the Docker Command Line Interface (CLI) when a container is running.
- **ENTRYPOINT.** Default parameters that cannot be overridden when Docker Containers run with CLI parameters.

简而言之：run是镜像执行的命令，cmd和 entrypoint 是容器启动时执行的命令。

>   常犯的错是把容器启动的命令也写成了 RUN

### VOLUME

创建挂载点

### EXPOSE 

指令是声明容器运行时提供服务的端口，这只是一个声明，在容器运行时并不会因为这个声明应用就会开启这个端口的服务。在 Dockerfile 中写入这样的声明有两个好处，一个是帮助镜像使用者理解这个镜像服务的守护端口，以方便配置映射；另一个用处则是在运行时使用随机端口映射时，也就是 docker run -P 时，会自动随机映射 EXPOSE 的端口。 EXPOSE 仅仅是声明容器打算使用什么端口而已，并不会自动在宿主进行端口映射



## gitignore文件

前面已经提到过 `.dockerignore`， 它的功能类似 `.gitignore`。它需要存放在构建环境根目录下才会起作用，通过 `.dockerignore` 定义匹配规则来排除文件和目录。通过 `.dockerignore` 可以避免不必要的大型或敏感文件和目录发送给 Docker daemon，从而避免 `ADD` 或者 `COPY` . 拷贝这些文件和目录。

下面是一个 .dockerignore 文件样例:

```properties
src/
target/
!target/*.war
target/*-sources.war
target/*-javadoc.war

```

`.dockerignore` 的匹配规则遵循 Go 的 [filepath.Match](https://golang.org/pkg/path/filepath/#Match) 规则。除了该规则外，Docker 还支持了一些特殊的通配符，`**` 匹配任意层级的目录。例如，`**/*.go` 将排除构建环境根目录下所有以 `.go` 为后缀的文件。`!` 表示忽略排除，如下：

```
*.md
!README.md
```

表示排除根目录当前层级除了`README.md` 外所有以 `.md` 为后缀的文件。

## 实践

### 开源软件的镜像

- [nacos-server](https://github.com/nacos-group/nacos-docker/blob/master/build/Dockerfile)

###  构建自己的基础镜像

因为docker镜像是分层设计，我们可以构建自己的基础镜像提供给应用基础

比如一个包含 jdk 的基础镜像的Dockerfile：

```dockerfile
FROM centos:7

# https://github.com/aaukhatov/docker/blob/master/Dockerfile
# https://github.com/HariSekhon/Dockerfiles/blob/master/centos-java/Dockerfile
# -y：表示在安装过程中不需要按y进行确认

RUN yum -y update
RUN yum -y remove java
RUN yum install -y \
       java-1.8.0-openjdk \
       java-1.8.0-openjdk-devel
RUN yum install -y \
				net-tools
CMD ["/bin/bash"]       
```

构建指令：

```
 docker build --platform=linux/amd64 -t dockerhub.test.wacai.info/wse/wapilot-website:1.1 .
```

 在此基础镜像之上增加 jemalloc，Dockerfile文件如下：

```dockerfile
FROM dockerhub.test.wacai.info/wse/centos-jdk:1.8

RUN yum group install -y "Development Tools"; \
    yum install -y wget tcl which zlib-devel git docbook-xsl libxslt graphviz; \
    yum clean all

RUN mkdir -p /opt && cd /opt && git clone https://github.com/jemalloc/jemalloc.git \
    && mkdir /tmp/jprof && mkdir /tmp/nmt && mkdir /tmp/pmap \
    && mkdir /data/program/jeprof

RUN cd /opt/jemalloc && git checkout -b stable-4 origin/stable-4
RUN cd /opt/jemalloc && ./autogen.sh --enable-prof
RUN cd /opt/jemalloc && make dist
RUN cd /opt/jemalloc && make
RUN cd /opt/jemalloc && make install

ENV LD_PRELOAD="/usr/local/lib/libjemalloc.so"
ENV MALLOC_CONF="prof_leak:true,prof:true,lg_prof_interval:25,lg_prof_sample:18,prof_prefix:/tmp/jeprof"

ENV DIAGNOSTIC_DIR /data/program/jeprof
RUN mkdir -p "$DIAGNOSTIC_DIR"
COPY *.sh $DIAGNOSTIC_DIR/
```

构建指令：

```
 docker build . -t dockerhub.test.wacai.info/wse/centos-jdk-jemalloc:1.8
```

最终应用镜像Dockerfile文件:

```dockerfile
#FROM dockerhub.test.wacai.info/e2pub/jdk:1.8.0-291
FROM  dockerhub.test.wacai.info/wse/centos-jdk-jemalloc:1.8
VOLUME /data/k2/log

COPY config /config
COPY target/${artifactId}*.jar ${workDir}/lib/${artifactId}-${versionForPkg}.jar
COPY run-java.sh setenv.sh plugin.sh ${workDir}/bin/

RUN mkdir /logback && \
    mv /config/logback.xml /logback

ENV JAVA_APP_JAR=${workDir}/lib/${artifactId}-${versionForPkg}.jar
ENV GROUP=${groupId} ARTIFACT=${artifactId} VERSION=${versionForPkg}

WORKDIR ${workDir}

CMD [ "bin/run-java.sh" ]
```

### 构建Java应用镜像

```yaml
# sets the base image for subsequent instructions
FROM dockerhub.test.wacai.info/library/tomcat:8.0.41

# sets the environment variable
ENV APP_NAME dubbo-admin
ENV APP_DIR /data/program/$APP_NAME

# copies new files to the filesystem of the container
COPY k2/bin $APP_DIR/bin
#COPY conf $APP_DIR/conf
COPY target/${APP_NAME}*.war $APP_DIR/target/$APP_NAME.war

# sets the working directory
WORKDIR $APP_DIR

# creates a mount point
VOLUME /data/k2/log

# provide defaults for an executing container
CMD ["./bin/start.sh"]
```

java 应用需要考虑一个问题，jvm的堆内存是通过参数管理的，如何感知容器的内存限制做动态调整呢？[java-inside-docker](https://developers.redhat.com/blog/2017/03/14/java-inside-docker)这篇文章做了一个总结，有3种方式：

- JAVA_OPTIONS 环境变量；
- fabric8 通过启动[脚本](https://github.com/fabric8io-images/java/blob/master/images/jboss/openjdk8/jdk/run-java.sh#L162-L175)读取'/sys/fs/cgroup/memory/memory.limit_in_bytes' 中的内存limit值动态计算；
- java8 通过 -XX:+UnlockExperimentalVMOptions 和 -XX:+UseCGroupMemoryLimitForHeap 动态感知，注意jdk10以后不需要设置;

参考

- [java-inside-docker](https://developers.redhat.com/blog/2017/03/14/java-inside-docker)
- [ 让JVM感知Docker的参数](https://daocloud-labs.github.io/DX-DMP-Public-Docs/spec/jvm-docker.html)



## 常见问题

### 推送镜像到私有仓库

当 dockerFile中存在私有仓库或者需要push到私有仓库需要设置，否则会出现如下错误:

```
 unauthorized: unauthorized to access repository: wse/wse-runner, action: push
```

设置步骤如下:

1、在Docker Engine 或者  /etc/docker/daemon.json 添加到docker的可信地址：

```json
  "insecure-registries": [
    "https://dockerhub.test.wacai.info"
  ]
```

2、登录到私有仓库

```shell
➜  wse-runner git:(migrate_k2) ✗  docker login dockerhub.test.wacai.info
Username: bairen
Password:
Login Succeeded
```

３、push

通过docker tag 打标, 把dubbo-admin打上仓库和版本号(如果build时已经带上可以忽略)

```
docker tag dubbo-admin:1.0.9 dockerhub.test.wacai.info/wse/dubbo-admin:1.0.9
```

通过 docker push推送到私有仓库

```
docker push dockerhub.test.wacai.info/wse/dubbo-admin:1.0.9
```

### 镜像none的问题

docker 在build镜像过程中会产生repository和tag均为none的情况，比如：

![image-20220120100612785](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220120100612785.png)

可以通过一下命令删除 dangling images

```bash
 #删除stopped的容器
docker container prune
docker image prune
```

### 删除所有镜像

```
docker image prune -f -a
```

### 挖财push到阿里云

```bash
ssh root@172.16.49.96  #redis测试环境
docker pull dockerhub.test.wacai.info/com.wacai.middleware/hermes-proxy:261-ba2fee01
docker login --username=<EMAIL> registry.cn-hangzhou.aliyuncs.com
docker tag dockerhub.test.wacai.info/com.wacai.middleware/hermes-proxy:261-ba2fee01 registry.cn-hangzhou.aliyuncs.com/xinchuangjian/hermes-proxy:261-ba2fee01
docker push registry.cn-hangzhou.aliyuncs.com/xinchuangjian/hermes-proxy:261-ba2fee01
```

registry密码：YMkEacIZ7fC6oSXt

## 问题

### 1、构建 docker-elasticsearch 镜像报错

```
failed to solve with frontend dockerfile.v0: failed to create LLB definition: failed to do request: Head "https://dockerhub.test.wacai.info/v2/e2pub/jdk/manifests/1.8.0-291": x509: certificate is not valid for any names, but wanted to match dockerhub.test.wacai.info
```

原因： dockerhub没有走https， 导致 https://dockerhub.test.wacai.info/v2/e2pub/jdk/manifests/1.8.0-291 访问失败

新版本 docker 都有这个问题，可以先 docker pull 依赖的镜像到本地再 build

###  2、mac m1 无法运行

执行报错：

```
➜  ~ docker run --platform linux/x86_64 -it --rm --name kibana dockerhub.test.wacai.info/wse/kibana:6.8.23 bash
qemu: uncaught target signal 11 (Segmentation fault) - core dumped
```

原因: docker 依赖 qemu 实现 arm架构下虚拟化x86指令，这个应该是有bug，还没有解决

https://docs.docker.com/build/building/multi-platform/

## 参考

- [Dockerfile 指令详解](https://yeasy.gitbook.io/docker_practice/image/dockerfile)
