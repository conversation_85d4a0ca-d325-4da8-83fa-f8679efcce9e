## 概述

Compose 项目是 Docker 官方的开源项目，负责实现对 Docker 容器集群的快速编排。

通过第一部分中的介绍，我们知道使用一个 `Dockerfile` 模板文件，可以让用户很方便的定义一个单独的应用容器。然而，在日常工作中，经常会碰到需要多个容器相互配合来完成某项任务的情况。例如要实现一个 Web 项目，除了 Web 服务容器本身，往往还需要再加上后端的数据库服务容器，甚至还包括负载均衡容器等。

`Compose` 恰好满足了这样的需求。它允许用户通过一个单独的 `docker-compose.yml` 模板文件（YAML 格式）来定义一组相关联的应用容器为一个项目。

## 使用

一个 `docker-compose.yml` 看起来像这样：

```yaml
version: "3"
services:
  web:
    image: beginor/geoserver:2.11.1
    container_name: geoserver-web
    hostname: geoserver-web
    ports:
      - 8080:8080
    volumes:
      - ./web/data_dir:/geoserver/data_dir
      - ./web/logs:/geoserver/logs
    restart: unless-stopped
    links:
      - database:database
  database:
    image: beginor/postgis:9.3
    container_name: postgis
    hostname: postgis
    ports:
      - 5432:5432
    volumes:
      - ./database/data:/var/lib/postgresql/data
    environment:
      POSTGRES_PASSWORD: 1q2w3e4R
    restart: unless-stopped
```

上面的 `docker-compose.yml` 文件定义了两个服务 web 和 database， 一个服务在运行时对应一个容器的实例， 上面的文件表示要启动两个实例。

在部署时， 通常将 `docker-compose.yml` 文件放到一个目录， 表示一个应用， docker 会为这个应用创建一个独立的网络， 便于和其它应用进行隔离。

docker-compose 不仅可以根据配置文件 `docker-compose.yml` 自动创建网络， 启动响应的容器实例， 也可以根据配置文件删除停止和删除容器实例， 并删除对应的网络， 确实是 `docker run` 命令更加方便， 因此推荐在测试环境或者生产环境中使用。



**启动**

运行这个程序， 只要在这个目录下执行命令

```
 docker-compose up -d
```

>  注意容易犯一个错误：docker-compose start 不会创建，只会启动已有的服务。



**停止**

要停止上面的容器，执行

```
 docker-compose down
```



## examples

- [nacos/docker-compose](https://github.com/jiangyunpeng/dockers)
- [skywalking/docker-compose](https://github.com/jiangyunpeng/dockers/blob/master/docker-compose/skywalking/docker-compose.yml)



## 参考

- [Compose入门](https://docs.docker.com/compose/gettingstarted/)