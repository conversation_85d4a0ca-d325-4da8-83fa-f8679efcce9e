## Docker网络模型概述

docker run 创建 Docker 容器时，可以用 --network 选项指定容器的网络模式，目前docker支持以下几种网络模式：

- bridge：默认的模式。 
- host：使用宿主机的网卡。
- container：指定与某个容器实例共享网络。
- none：不创建网卡。
- overlay：用于 Swarm mode 多台机器通信，在本小节中你可以忽略它。

详细设置详见[docker run network-settings](https://docs.docker.com/engine/reference/run/#network-settings)

### Bridge

使用方式：

>  使用 --network bridge

特点：

- bridge是docker默认的网络模式，所以无需显式设置
- 容器拥有独立IP段，多个容器之间可通过容器ip通信，宿主机无法直接访问容器中的网络
- 容器之间如果希望通过 name 通信可通过--link 或自定义网络，后者是docker推荐的方式

当Docker进程启动时，会在主机上创建一个名为docker0的虚拟网桥(注:mac下不会显示):

 ```
 [root@VM-4-9-centos ~]# ip address show docker0
 3: docker0: <NO-CARRIER,BROADCAST,MULTICAST,UP> mtu 1500 qdisc noqueue state DOWN group default
 link/ether 02:42:96:7f:e0:d2 brd ff:ff:ff:ff:ff:ff
 inet **********/16 scope global docker0
 valid_lft forever preferred_lft forever
 inet6 fe80::42:96ff:fe7f:e0d2/64 scope link
 valid_lft forever preferred_lft forever
 ```

同时 docker 会自动创建一个叫bridge的network

```
[root@VM-4-9-centos ~]# docker network ls
NETWORK ID          NAME                DRIVER              SCOPE
543a3e944aed        bridge              bridge              local
23e777e4a579        host                host                local
321f95ae2ac4        none                null                local
```

此主机上启动的Docker容器会连接到这个虚拟网桥上，**虚拟网桥的工作方式和物理交换机类似**，这样主机上的所有容器就通过交换机连在了一个二层网络中。



<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211208232144826.png" alt="image-20211208232144826" style="zoom:50%;" />

容器拥有独立、隔离的网络栈，容器和宿主机以外的网络通过NAT建立通信，所以容器可以访问宿主机的网络，但是宿主机无法直接访问容器中的网络。

演示如下：

```shell
# 宿主机ip ********
# 运行一个容器
[root@iZbp1ax83amkj74axwq5sqZ ~]# docker run -it --rm --name busybox1  busybox sh
# ifconfig ip **********

#另外运行一个容器
[root@iZbp1ax83amkj74axwq5sqZ ~]# docker run -it --rm --name busybox2  busybox sh
# ifconfig ip **********
```

可以发现桥接模式下容器是独立ip段并且容器之间只能通过 ip 通信

```shell
ping ********** #成功
ping busybox1 #报错
```

原因是因为这时候使用的默认的 bridge network，docker出去安全限制只允许通过ip访问，

除非使用--link命令，演示如下：

```shell
docker run -it --rm --name busybox2 --link busybox1:busybox busybox sh
ping busybox1 #执行成功
```

--link 的本质是为新容器配置了host文件，查看新容器的 /etc/hosts 会发现，里面添加了busybox1的映射所以能通信

```
**********	busybox 1b7e4afd9dd8 busybox1
```

随着 Docker 网络的完善上述方式已经不推荐，而是通过创建自定义网桥(**User-defined bridges**)的方式实现容器通信。

下面先创建一个新的 Docker 网络。

```shell
docker network create -d bridge my-net
```

`-d`参数指定 Docker 网络类型，目前支持两种： 

- bridge 。自定义桥接模式
- overlay。用于 Swarm mode，单机可以忽略它。

运行一个容器并连接到新建的 my-net 网络

```shell
docker run -it --rm --name busybox1 --network my-net busybox sh
```

再运行一个容器并加入到 my-net 网络

```shell
docker run -it --rm --name busybox2 --network my-net busybox sh
```

再打开一个新的终端查看容器信息

```shell
$ docker container ls

CONTAINER ID        IMAGE               COMMAND             CREATED             STATUS              PORTS               NAMES
b47060aca56b        busybox             "sh"                11 minutes ago      Up 11 minutes                           busybox2
8720575823ec        busybox             "sh"                16 minutes ago      Up 16 minutes                           busybox1
```

下面通过 ping 来证明 busybox1 容器和 busybox2 容器建立了互联关系。 在 busybox1 容器输入以下命令

```shell
/ # ping busybox2
PING busybox2 (**********): 56 data bytes
64 bytes from **********: seq=0 ttl=64 time=0.072 ms
64 bytes from **********: seq=1 ttl=64 time=0.118 ms
```

用 ping 来测试连接 busybox2 容器，它会解析成 **********。 同理在 busybox2 容器执行 ping busybox1，也会成功连接到。

```shell
/ # ping busybox1
PING busybox1 (**********): 56 data bytes
64 bytes from **********: seq=0 ttl=64 time=0.064 ms
64 bytes from **********: seq=1 ttl=64 time=0.143 ms
```

这样，busybox1 容器和 busybox2 容器建立了互联关系。

如果你有多个容器之间需要互相连接，推荐使用`Docker Compose`。



### Host 

使用方式：

>  使用 --network host

如果启动容器的时候使用`host`模式，那么这个容器将不会获得一个独立的`Network Namespace`，而是和宿主机共用一个 Network Namespace。容器将不会虚拟出自己的网卡，配置自己的 IP 等，而是使用宿主机的 IP 和端口。但是，容器的其他方面，如文件系统、进程列表等还是和宿主机隔离的。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211208232118523.png" alt="image-20211208232118523" style="zoom:60%;" />

这张图表示Docker容器在host模式下复用Host主机的网卡。

特点：

- 容器中服务的端口号不能与 Docker host 上已经使用的端口号相冲突。
- 该模式不支持mac，最好在Linux下测试。

演示：

```
#查看宿主机的ip
[root@iZbp1ax83amkj74axwq5sqZ ~]# ifconfig
eth0: flags=4163<UP,BROADCAST,RUNNING,MULTICAST>  mtu 1500
        inet *************  netmask *************  broadcast **************
        
[root@iZbp1ax83amkj74axwq5sqZ ~]# docker run -it --rm --name busybox1 --network host busybox sh
Emulate Docker CLI using podman. Create /etc/containers/nodocker to quiet msg.
/ # ifconfig
eth0      Link encap:Ethernet  HWaddr 00:16:3E:1B:8D:94
          inet addr:*************  Bcast:**************  Mask:*************        
```



### Container 

使用方式：

> --network=container:NAME_or_ID 

这个模式指定新创建的容器和已经存在的一个容器共享一个 Network Namespace，而不是和宿主机共享。

新创建的容器不会创建自己的网卡，配置自己的 IP，而是和一个指定的容器共享 IP、端口范围等。同样，两个容器除了网络方面，其他的如文件系统、进程列表等还是隔离的，两个容器的进程可以通过 lo 网卡设备通信。 

Container模式示意图：

![企业微信截图_cd7197ad-3f97-4018-af16-e6d7ea42417e](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_cd7197ad-3f97-4018-af16-e6d7ea42417e.png)

演示：

```
[root@iZbp1ax83amkj74axwq5sqZ ~]# docker run -it --rm --name busybox1 busybox sh
/ # ifconfig
eth0      Link encap:Ethernet  HWaddr 8E:22:BB:21:FB:54
          inet addr:*********  Bcast:*************  Mask:***********
 
[root@iZbp1ax83amkj74axwq5sqZ ~]# docker run -it --rm --name busybox2 --network container:busybox1 busybox sh
eth0      Link encap:Ethernet  HWaddr 8E:22:BB:21:FB:54
          inet addr:*********  Bcast:*************  Mask:***********

```

### 小结

- 两个容器只有通过自定义 bridge 网络模式才可以在容器中通过 name ping通对方。
- docker run -p 和 host 网络模式看上去都是在宿主机上暴露一个端口，但背后的实现方式是不一样的，-p参数是端口映射，通过 docker-proxy 实现
- 两个容器只能通过 container 网络模式才可以直接通过localhost访问到对方端口。  

## 理解 docker 网络模型

Docker 网络模型最常用的还是桥接模型(bridge network)，那么docker是如何实现bridge network的？docker 如何做到容器之前能网络互通，同时容器内部能请求外部网络，但外部网络无法请求容器内部？

Docker主要用到了三部分底层技术：

- Network Namespace
- Linux bridge
- Iptables NAT

### Network Namespace

Network Namespace 是Linux内核提供的一项实现网络隔离的功能，它能隔离多个不同的网络空间，并且各自拥有独立的网络协议栈，这其中便包括了网络接口网卡，路由表，iptables规则等。

Linux 提供了ip命令来操作Network Namespace，下面我们通过 ip 命令创建 network namespace ，虚拟网卡方式来学习network namespace。

1、创建 network namespace 

```
ip netns add ns1
```

2、查看 netns

```
ip netns
```

3、查看创建好的 namespace 的网卡信息

```
ip netns exec ns1 ip link list
```

输出：

```
[root@VM-4-9-centos ~]# ip netns exec ns102 ip link list
1: lo: <LOOPBACK> mtu 65536 qdisc noop state DOWN mode DEFAULT group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
```

默认只有回环设备 lo

4、在ns下 执行 ping 

```
ip netns exec ns1 ping 127.0.0.1
```

lo设备状态还是DOWN,需要先设置成UP

```
ip netns exec ns1 ifconfig lo up
```

5、 和外界通信

需要在ns中再选件一对虚拟以太网卡，即所谓的veth pair。

下面命令创建了一对虚拟以太网卡，把 veth pair 的一端放到了 ns1 network namespace

```bash
 ip link add veth0 type veth peer name veth1
 ip link set veth1 netns ns1

```

上面我们创了 veth0 和 veth1 一对虚拟网卡，默认情况下，他们都应该在主机的根 network namespace中， 其中 veth1 被移动到了ns1。 现在还不能通信，因为默认创建出来的虚拟网卡都是down状态，需要手动设置为 UP。

这个步骤和上面对lo网卡的操作类似，只是多了了一步绑定ip地址：

```
ip netns exec ns1 ifconfig veth1 ********/24 up
ifconfig veth0 ********/24 up 
```

上述命令分别为 ns1 空间下的veth1 和根空间下的 veth0 绑定了ip地址，这样我们就可以ping 通 veth pair 任意一头了。

例如在主机 ping ******** (ns1空间下的虚拟网卡)，如下所示:

```
[root@VM-4-9-centos ~]# ping ********
PING ******** (********) 56(84) bytes of data.
64 bytes from ********: icmp_seq=1 ttl=64 time=0.046 ms
```

同样的在 ns1空间 ping主机:

```
[root@VM-4-9-centos ~]# ip netns exec ns1  ping ********
PING ******** (********) 56(84) bytes of data.
64 bytes from ********: icmp_seq=1 ttl=64 time=0.035 ms
```

当我们在主机上执行 ip netns add ns1 后 ，实际是在 /var/run/netns 下创建了一个 ns1 的文件

```
[root@worker3 ~]# ls /var/run/netns
ns1
```

创建自定义的 Network Namespace 其实就是创建一个文件，然后通过绑定挂载的方式将新创建的 Network Namespace 文件和进程的 /proc/self/ns/net 文件绑定在一起。

```bash
[root@worker3 ~]# touch /var/run/netns/ns2
[root@worker3 ~]# unshare --net bash
[root@worker3 ~]# mount --bin /proc/self/ns/net /var/run/netns/ns2
//上面的过程实际就是执行了ip netns add ns2 && ip netns exec ns2 bash

[root@worker3 ~]# ip link
1: lo: <LOOPBACK> mtu 65536 qdisc noop state DOWN mode DEFAULT group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00

[root@worker3 ~]# exit
//退出，回到主机默认命名空间。用ip netns list查看，已经可以看到ns2
[root@worker3 ~]# ip netns list
ns2
ns1
```

既然 docker 也是通过创建自定义Network Namespace 的，为什么当我在主机上执行 ip netns list 的时候看不到 docker 的网络命名空间?

因为 ip netns list 的时候只会显示在 /var/run/netns 下的文件，而 docker 的文件默认是创建在 /var/run/docker/netns 下的。所以我们可以通过 ls /var/run/docker/netns 来显示当前的所有容器的网络命名空间，并且通过 nsenter --net=/var/run/docker/xxx 来进入容器的网络命名空间。

```bash
[root@worker3 ~]# ls /var/run/docker/netns
5bbd5f99d403  a2eabf9acccb  b63ec59b3d9e  d6e4ff961713  default
[root@worker3 ~]# nsenter --net=/var/run/docker/netns/b63ec59b3d9e
[root@worker3 ~]# ip addr
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
    inet 127.0.0.1/8 scope host lo
       valid_lft forever preferred_lft forever
4: eth0@if3: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue state UP group default
    link/ether fa:a7:8d:05:03:a6 brd ff:ff:ff:ff:ff:ff link-netnsid 0
    inet ***********/32 scope global eth0
       valid_lft forever preferred_lft forever
```

具体可以参考[理解 Linux Network-Namespace](https://juejin.cn/post/6905955407676571655#heading-1)

### Linux bridge

Linux Bridge 是一个 kernel 模块，首先在 2.2 内核中引入，然后由 Lennert Buytenhek 重写，它是用纯软件实现的虚拟交换机，有着和物理交换机相同的功能，例如二层交换，MAC地址学习等。因此我们可以把tun/tap，veth pair等设备绑定到网桥上，就像是把设备连接到物理交换机上一样。此外它和veth pair、tun/tap一样，也是一种虚拟网络设备，具有虚拟设备的所有特性，例如配置IP，MAC地址等。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220607231331742.png" alt="image-20220607231331742" style="zoom:50%;" />

Here's how to create a bridge:

```
# ip link add br0 type bridge
# ip link set eth0 master br0
# ip link set tap1 master br0
# ip link set tap2 master br0
# ip link set veth1 master br0
```

This creates a bridge device named `br0` and sets two TAP devices (`tap1`, `tap2`), a VETH device (`veth1`), and a physical device (`eth0`) as its slaves, as shown in the diagram above.

brctl 是另外一个网桥管理命令:

```
# brctl show
bridge name	bridge id		STP enabled	interfaces
docker0		8000.0242a5a87edc	no
```

 也可以通过 ip link show命令查看：

```
[root@VM-4-9-centos ~]# ip link show
1: lo: <LOOPBACK,UP,LOWER_UP> mtu 65536 qdisc noqueue state UNKNOWN mode DEFAULT group default qlen 1000
    link/loopback 00:00:00:00:00:00 brd 00:00:00:00:00:00
2: eth0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc pfifo_fast state UP mode DEFAULT group default qlen 1000
    link/ether 52:54:00:2e:f0:35 brd ff:ff:ff:ff:ff:ff
3: docker0: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue state UP mode DEFAULT group default
    link/ether 02:42:96:7f:e0:d2 brd ff:ff:ff:ff:ff:ff
25: veth1661510@if24: <BROADCAST,MULTICAST,UP,LOWER_UP> mtu 1500 qdisc noqueue master docker0 state UP mode DEFAULT group default
    link/ether fe:e4:11:76:93:84 brd ff:ff:ff:ff:ff:ff link-netnsid 0
```



### Iptables NAT

## 参考

- [Docker 的网络模式](https://www.qikqiak.com/k8s-book/docs/7.Docker%E7%9A%84%E7%BD%91%E7%BB%9C%E6%A8%A1%E5%BC%8F.html)
- [what-is-docker-networking](https://www.oreilly.com/content/what-is-docker-networking/)
- [what-is-the-relation-between-docker0-and-eth0](https://stackoverflow.com/questions/37536687/what-is-the-relation-between-docker0-and-eth0)
- [linux-bridge-part1](https://hechao.li/2017/12/13/linux-bridge-part1/)
- [introduction-to-linux-interfaces-for-virtual-networking](https://developers.redhat.com/blog/2018/10/22/introduction-to-linux-interfaces-for-virtual-networking)
- [Linux Bridge 详解](https://zhuanlan.zhihu.com/p/293667316)
- [docker-network-namespace-invisible](https://www.baeldung.com/linux/docker-network-namespace-invisible)
- [Docker 原理篇Docker network namespace](https://www.shangyang.me/2017/01/08/docker-namespace-network/)
- [Linux Switching – Interconnecting Namespaces](http://www.opencloudblog.com/?p=66)

