## 概述

- 基本数据结构

- 持久化

- 复制
  - https://redis.io/docs/management/replication/#how-redis-replication-works

- 阻塞

- 内存管理和优化

- 集群管理

  - https://cloud.tencent.com/developer/beta/article/1862806
  - https://ljchen.net/2019/08/24/Redis-Cluster%E6%80%BB%E7%BB%93/
  - https://www.modb.pro/db/182559

- 使用场景



## 基本原理

Redis 底层存储键值对（Key-Value）的数据结构确实是基于**哈希表**（Hash Table）实现的，但它不是一个简单的数组。

### 1、Redis的哈希表不是一个简单的数组

Redis 的哈希表（在源码中称为 `dict`）是一个**数组加链表**的结构，它使用**拉链法**来解决哈希冲突。

- **数组（Bucket Array）：** 哈希表的主体是一个数组，数组中的每个位置被称为一个**桶（bucket）**。
- **链表（Linked List）：** 每个桶里存储的不是键值对本身，而是一个指向链表的指针。当不同的键经过哈希函数计算后得到相同的索引（哈希冲突）时，这些键值对会被存储在这个索引对应的链表中。

### 2、 Redis如何映射一个很大的哈希值

如果一个键计算出的哈希值（`hash`）是一个很大的数字，Redis 并不会直接使用这个巨大的数字作为数组索引。它会通过一个简单的位运算来将这个哈希值映射到哈希表数组的有效索引范围内。

具体的映射过程如下：

1. **计算哈希值：** 首先，Redis 使用一个哈希函数（通常是 MurmurHash2）对键进行计算，得到一个 64 位或 32 位的哈希值。

2. **取模运算（位运算）：** 接下来，Redis 会使用以下公式将哈希值映射到数组的索引：

   ```
   index=hash & (size−1)
   ```

其中，`size` 是哈希表数组的长度，并且 `size` 总是 2 的幂。因为 `size` 是 2 的幂，所以 `(size - 1)` 的二进制表示是一串 `1`。`&` 运算符是按位与（AND）操作，这种方式实际上是取模运算（`hash % size`）的一种更高效的实现。

举个例子，假设哈希值 `hash` 是一个很大的数字，而哈希表的数组长度 `size` 是 16。那么 `size - 1` 就是 15（二进制为 `00001111`）。通过 `hash & 15` 运算，无论 `hash` 多大，最终得到的 `index` 都会在 `0` 到 `15` 的范围内，从而确保哈希值可以正确地映射到数组中的有效位置。

### 3、渐进式 rehash（Incremental Rehashing）

值得一提的是，Redis 为了保证性能，当哈希表中的键值对数量增加到一定程度时，会进行**扩容**。但它不是一次性地将所有键值对从旧的哈希表迁移到新的更大的哈希表，因为这个过程可能会阻塞服务。

相反，Redis 使用了一种称为**渐进式 rehash** 的机制。它会同时维护两个哈希表（一个旧的、一个新的）。在每次执行命令时，它会从旧哈希表中迁移少量数据到新哈希表，然后处理客户端的请求。这样，扩容过程被分散到多次操作中，避免了单次耗时过长，从而保证了 Redis 的高吞吐量和低延迟。



## 数据结构

redis 主要提供以下5 种数据结构：

| 数据类型 | 使用场景                                                    |
| -------- | ----------------------------------------------------------- |
| 字符串   | 缓存、计数、共享Session、IP统计、分布式锁                   |
| 列表     | 有序的对象列表如朋友圈的点赞顺序列表、评论顺序列表          |
| 哈希表   | 购物车信息、用户信息、Hash类型的(key, field, value)存储对象 |
| 集合     | 无序的唯一的kv结构，好友、关注、粉丝、感兴趣的人集合        |
| 有序集合 | 访问排行榜、点赞排行、粉丝数排行                            |



### string

内部实现是SDS(simple dynamic string)，数据结构: len，free，char[]。

> NX 是not exist 的意思。

SET 命令的完整语法如下：

```
SET key value [NX | XX] [EX seconds | PX milliseconds] [KEEPTTL]
```

其中：

- NX 选项表示“仅在键不存在时设置值”（Not eXists）。
- XX 选项表示“仅在键已存在时设置值”（eXists）。
- EX 表示过期时间，秒
- PX 表示过期时间，毫秒
- KEEPTTL如果键已经存在并且有设置过期时间，使用 KEEPTTL 选项可以保留原有的过期时间。如果不使用 KEEPTTL，新的 SET 操作会覆盖原有的过期时间。



SETEX 是 Redis 中的一个命令，用于设置一个键的值，并为其指定一个过期时间（以秒为单位）。SETEX 是 "SET with EXpire" 的缩写。

```
SETEX key seconds value
```



命令：

- [ SET key value](https://www.runoob.com/redis/strings-set.html)：设置指定 key 的值
- [ SETEX key seconds value](https://www.runoob.com/redis/strings-setex.html)：设置键值含过期时间
- [ SETNX key value](https://www.runoob.com/redis/strings-setnx.html)：只有在 key 不存在时设置 key 的值
- [SETRANGE key offset value](https://www.runoob.com/redis/strings-setrange.html)：用 value 参数覆写给定 key 所储存的字符串值，从偏移量 offset 开始
- [MSET key value ](https://www.runoob.com/redis/strings-mset.html)：设置多个键值
- msetnx：设置多个不存在的键值
- [GET key](https://www.runoob.com/redis/strings-get.html)：通过键获得值
- [GETSET key value](https://www.runoob.com/redis/strings-getset.html): 将给定 key 的值设为 value ，并返回 key 的旧值
- [GETRANGE key start end](https://www.runoob.com/redis/strings-getrange.html)：返回 key 中字符串值的子字符
- [mget](https://www.runoob.com/redis/strings-mget.html):获得多个键的值。

### hash

通过以下配置来决定多少个key使用zipList来保存hash：

- hash-max-ziplist-entries 配置字段最多64个(key的个数)
- hash-max-ziplist-value 配置value最大为512字节

 命令:

- hset key field value
- hset:若key不存在就创建，否则覆盖。
- hsetnx：设置 hash field 为指定值，如果 key 不存在，则先创建。如果 field 已经存在，返回 0
- hmset:同时设置hash多个field
- hget:获取指定的hash field
- hmget:获取全部指定的hash field
- hexists:测试指定field是否存在
- hlen：返回指定的field的个数
- hdel：删除指定field。
- hkeys: 查询指定key的所有field
- hvals：获取指定key的所有value
- hgetall:获得指定key的所有field以及值  
- hkeys: 获取所有key

### list

list是基于双向链表的数据结构,操作就是入栈(push)、出栈(pop),包括左(头)入出栈、右(尾)入出栈，也含有超时阻塞的功能。

命令：

- lpush：在key对应的list的头部添加元素。
- **lrange：获得list范围的值**。 lrange mylist 0 1(获取0 1索引的值)
- rpush：在key对应的list的尾部添加元素
- linsert：在key对应的特定位置之前或者之后添加字符串元素 linsert mylist before “world”  “hello”
-  lset：设置list指定下表的元素(从0开始)
-  lrem：从key对应的list里，删除count个value相同的元素。
-  ltrim：保留指定key的值范围内的数据。
-  lpop：从list的头部删除元素，并且返回删除元素
-  rpop：从list的尾部删除元素，并且返回删除元素
- rpoplpush：第一个list的尾部移除元素并且添加到第二个list的头部
- lindex：返回名称为key的list中index位置的元素
- **llen：返回key对应list的长度**

### set

sets是无序集合，是通过hashtable实现的。额外功能有并集、交集、差集。

命令：

- sadd：向名称为key的set当中添加元素
- srem：删除名称为key的元素
- spop：随机返回并且删除set中某key元素
-  sdiff：两个set的差集
- sdiffstore:假设有set3、set1、set2-->set1与set2差集返回的元素，添加到set3中
- sinter:两个set的交集
- sinterstore：假设有set3、set1、set2-->set1与set2交集返回的元素，添加到set3中
- sunion:两个set的并集
- sunionstore：假设有set3、set1、set2-->set1与set2交集返回的元素，添加到set3中
- smove:假设有set1、set2-->删除set1的某个key值，并且添加到set2
- scard：返回set的元素个
-  sismember:测试set中是否存在某member(元素)。
-  srandmember：随机返回一个元素，但是不删除

### sorted set

sorted set(skip list|双向链表和hashtable的结合体)是set的一个升级版本，升级版本的sets，有两个纬度，一个纬度用来存顺序，一个纬度用于存value。

命令：

- zadd:向名称为key的zset中添加元素member、score用于排序。如果该元素存在，则根据score更新该元素的顺序
- zrem:删除名为key的zset的元素member
- zincrby：如果在名称为 key 的 zset 中已经存在元素 member，则该元素的 score 增加 increment；否则向集合中添加该元素，其 score 的值为 increment
-  zrank:返回名称为 key 的 zset 中 member 元素的排名(按 score 从小到大排序)即下标
- zrevrank:返回名称为 key 的 zset 中 member 元素的排名(按 score 从大到小排序)即下标
- zrange:返回名称为 key 的 zset（按 score 从小到大排序）中的 index 从 start 到 end 的所有元
- zrevrange：返回名称为 key 的 zset（按 score 从大到小排序）中的 index 从 start 到 end 的所有元素
- zrangebyscore：返回集合中 score 在给定区间的元素
- zcount:返回集合中 score 在给定区间的数量
- zcard:返回集合中元素个数
- zscore:返回给定元素对应的 score
- zremrangebyrank:删除集合中排名在给定区间的元素
- zremrangebytscore：删除集合中 score 在给定区间的元素



## Reids集群

### 数据一致性

> 参考： [Redis主从集群切换数据丢失问题如何应对？](https://developer.aliyun.com/article/982362)

Redis 集群无法保证数据 100%不丢失，原因是**redis集群采用异步复制方式**。当客户端发送写请求给master节点的时候，客户端会返回OK，然后同步到各个slave节点中。 如果此时master还没来得及同步给slave节点时发生宕机，那么master内存中的数据会丢失。

在redis的配置文件中有两个参数我们可以设置来保证尽量少的数据丢失

> ```
> min-slaves-to-write 1
> min-slaves-max-lag 10
> ```

这两个参数表示至少有1个salve的与master的同步复制延迟不能超过10s，一旦所有的slave复制和同步的延迟达到了10s，那么此时master就不会接受任何请求。

我们可以减小min-slaves-max-lag参数的值，这样就可以避免在发生故障时大量的数据丢失，一旦发现延迟超过了该值就不会往master中写入数据。

min-slaves-to-write默认情况下是0，min-slaves-max-lag默认情况下是10。



## 使用场景

### 1、内存优化

如果有大量的key-value需要保存，比如手机号到uid的kv数据，直接以key-value形式存储在redis不是一种高效的做法，一千万条这样的数据会占用1.7G的内存。在 2011年Instagram就遇到了[这样的问题](https://instagram-engineering.com/storing-hundreds-of-millions-of-simple-key-value-pairs-in-redis-1091ae80f74c)并给出了最佳实践，使用hash保存，并利用ziplist节约内存。他们的方式是把key bucket化，一个bucket也就是一个ziplist，容量为1000，所以采用 key 除以 1000，丢弃余数(remainder)，比如key为1155315，意味它对应的bucket为(1155315/1000=1155)：

```
HSET "mediabucket:1155" "1155315" "939"
HGET "mediabucket:1155" "1155315"
```

如果key为11553150，对应的bucket编号为11553，这样保证每个bucket的容量都是1000，转化关系如下图所以：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221120225432959.png" alt="image-20221120225432959" style="zoom:50%;" />

原始id中末尾3 位可以为任意数字。



### 2、分布式锁

网上八股文很多 ，比如https://mp.weixin.qq.com/s/Zt5L631fA2zwMuE5UDfLZA

重点有以下几点：

1、Redis做分布式锁的思路大概是在redis中设置一个值表示加了锁，然后释放锁的时候就把这个key删除。具体代码：

```lua
// 获取锁
// NX是指如果key不存在就成功，key存在返回false，PX可以指定过期时间
SET anyLock unique_value NX PX 30000


// 释放锁：通过执行一段lua脚本
// 释放锁涉及到两条指令，这两条指令不是原子性的
// 需要用到redis的lua脚本支持特性，redis执行lua脚本是原子性的
if redis.call("get",KEYS[1]) == ARGV[1] then
return redis.call("del",KEYS[1])
else
return 0
end
```

2、加锁一定要用SET key value NX PX milliseconds 命令来保证原子性，

3、value要具有唯一性，释放锁需要验证value是和加锁的一致才删除key

4、因为redis集群在主从切换时可能丢数据(原因是异步复制)，所以redis的分布式锁极端场景下存在锁丢失的风险

5、实践会采用Redisson

## 参考

- 书
  - redis in action
  - Redis深度历险：核心原理和应用实践
  - redis设计与实现

- 国内文章
  - [redis思维导图](https://blog.csdn.net/WuLex/article/details/100594919)
  - [Redis系列：深刻理解高性能Redis的本质](https://mp.weixin.qq.com/s/FYN1w3cXfSWs0DJkL8EXOw)
  - [Redis 配置详解](https://cs-cjl.com/2019/04_11_redis_configuration_5)
  - [Redis 高可用篇：Cluster 集群能支撑的数据有多大？](https://cn.pingcap.com/article/post/4964.html)

- 国外文章
  - [Understanding Redis hash-max-ziplist-entries](https://www.peterbe.com/plog/understanding-redis-hash-max-ziplist-entries)

- 实践

  - [Redis 内存优化在 vivo 的探索与实践](https://segmentfault.com/a/1190000041771534)
  - [Kvrocks 在货拉拉全链路 Trace 下的应用](https://zhuanlan.zhihu.com/p/597453571)
  - [携程 Redis 治理演进之路](https://www.infoq.cn/article/whptalesgpgo1cecleg8)
  - [数万实例数百 TB 数据量，携程 Redis 治理演进之路](https://www.infoq.cn/article/fwdbwgylkhomcbheorje)

- 持久化方案，解决redis内存消耗过高问题

  - [pika](https://github.com/OpenAtomFoundation/pika)
  - [kvrocks](https://kvrocks.apache.org/)
  - [swapdb](https://github.com/JingchengLi/swapdb) 已经废弃
  - [KV开源组件选型对比](https://zhuanlan.zhihu.com/p/425732242)

- 源码

  - [enjoy-binbin redis源码](https://www.yuque.com/enjoy-binbin/blog/tnc0i7)

- 注意 set options

  ```
  The SET command supports a set of options that modify its behavior:
  
  EX seconds -- Set the specified expire time, in seconds.
  PX milliseconds -- Set the specified expire time, in milliseconds.
  NX -- Only set the key if it does not already exist.
  XX -- Only set the key if it already exist.
  KEEPTTL -- Retain the time to live associated with the key.
  GET -- Return the old string stored at key, or nil if key did not exist. An error is returned and SET aborted if the value stored at key is not a string.
  ```

  