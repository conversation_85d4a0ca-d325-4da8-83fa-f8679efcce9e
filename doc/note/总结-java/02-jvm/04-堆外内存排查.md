## 问题

业务方反馈某台应用总是收到内存告警容器被杀掉

![wecom-temp-f7785589fadb0a520301539d7abb8ae1](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/wecom-temp-f7785589fadb0a520301539d7abb8ae1.png)

通过dump内存发现heap内存充裕， 那么问题只能出现在堆外内存了，在 stackoverflow [上找到一篇质量很高的回答](https://stackoverflow.com/a/35610063/16346795)：

> 可能是本地内存泄露，最可能导致本地内存泄露是没有关闭 ZipInputStream/GZIPInputStream。
>
> 通过 Class.getResource/ClassLoader.getResource 获取的URL，调用openConnection()方法就会打开一个ZipInputStream，如果忘记close就可能会触发内存泄露。
>
> 不少开源库都出现了 java.util.zip.Deflater 或者 java.util.zip.Deflater 没有关闭导致内存泄露的bug，Elastic团队开发了一个java-agent [leakchecker](https://github.com/elastic/leakchecker) 用于检测是否存在此bug，Elastic团队在[blog](https://www.elastic.co/blog/tracking-down-native-memory-leaks-in-elasticsearch)中描述了如何通过leakchecker和jemalloc检测 此bug。

SpringBoot 早期版本1.5.4.RELEASE 也存在此[bug](https://github.com/spring-projects/spring-boot/issues/13935)，在[pull/14001](https://github.com/spring-projects/spring-boot/pull/14001)中已经修复，修复的方式增加close方法

```java
class ZipInflaterInputStream extends InflaterInputStream {

	private final Inflater inflater;

	ZipInflaterInputStream(InputStream inputStream, int size) {
		super(inputStream, new Inflater(true), getInflaterBufferSize(size));
	}

	@Override
	public void close() throws IOException {
		super.close();
		this.inf.end();
	}

```

## 分析工具

对外内存有一些工具便于我们排查问题。

### 1、NMT

> Native Memory Tracking (NMT) 是Hotspot VM用来分析VM内部内存使用情况的一个功能。我们可以利用jcmd（jdk自带）这个工具来访问NMT的数据。
>
> 参考文档[native-memory-tracking-in-jvm](https://www.baeldung.com/native-memory-tracking-in-jvm)
>
> 

**注意，NMT 不会追踪 native （C/C++ 代码）申请的内存，实际尝试发现还是有内存泄露无法通过 NMT 监控到**

NMT功能默认关闭，打开NMT会带来5%-10%的性能损耗，加入以下参数来开启：

```
-XX:NativeMemoryTracking=detail
```

开启NMT功能后，就可以使用JDK提供的jcmd命令来读取NMT采集的数据了，具体命令如下：
摘要模式

```shell
jcmd 8 VM.native_memory summary scale=MB		
```
详细模式
```
jcmd 8 VM.native_memory detail scale=MB 		
```



输出结果

```shell
Native Memory Tracking:

Total: reserved=3089MB, committed=1922MB
-                 Java Heap (reserved=1024MB, committed=1024MB)
                            (mmap: reserved=1024MB, committed=1024MB) 
 
-                     Class (reserved=1150MB, committed=142MB)
                            (classes #22391)
                            (malloc=6MB #39078) 
                            (mmap: reserved=1144MB, committed=135MB) 
 
-                    Thread (reserved=480MB, committed=480MB)
                            (thread #477)
                            (stack: reserved=478MB, committed=478MB)
                            (malloc=2MB #2384) 
                            (arena=1MB #951)
 
-                      Code (reserved=263MB, committed=105MB)
                            (malloc=19MB #23044) 
                            (mmap: reserved=244MB, committed=85MB) 
 
-                        GC (reserved=41MB, committed=41MB)
                            (malloc=3MB #583) 
                            (mmap: reserved=37MB, committed=37MB) 
 
-                  Compiler (reserved=2MB, committed=2MB)
                            (malloc=2MB #3977) 
 
-                  Internal (reserved=95MB, committed=95MB)
                            (malloc=95MB #86035) 
 
-                    Symbol (reserved=28MB, committed=28MB)
                            (malloc=23MB #237740) 
                            (arena=4MB #1)
 
-    Native Memory Tracking (reserved=7MB, committed=7MB)
                            (tracking overhead=6MB)
```



指标说明：上述除了Heap之外的区域都属于堆外内存，reserved memory（保留内存） 是JVM 通过mmaped 申请的虚拟地址空间，在页表中已经存在了记录（entries），保证了其他进程不会被占用，但是JVM还不能直接使用这块内存，committed memory（提交内存） 是JVM向操作系统实际分配的内存。

上面可以发现Class 对应的Metaspace申请了1g空间，但是我们设置了 -XX:MaxMetaspaceSize=380m，为啥这里没有生效？发现 stackoverflow 也有人提问：[Java - High class reserved space running out of memory](https://stackoverflow.com/questions/45520216/java-high-class-reserved-space-running-out-of-memory)

简单来说对于64 bit VM 需要使用-XX:CompressedClassSpaceSize=380m 注意这里限制的是虚拟内存

具体信息：

| 指标      | 说明 |
| --| -----------  |
| Class     | 用来保持class的原信息，在java8之前叫永久区PermGen，通过*-XX:MetaspaceSize* 和 *-XX:MaxMetaspaceSize* 设置 |
| Thread    | 栈区，创建线程的时候被分配，用于保存本地变量和部分结果，64bit差不多一个线程消耗 1MB 内存，栈区消耗的内存是没有上限的。 |
| Code      | 当用于保存JVM把字节码编译成本地机器指令的区域 |
| GC | 保存GC 算法内部数据结构 |
| Compiler | 没找到说明 |
| Internal | 命令行解析、JVMTI。实验发现通过ByteBuffer.allocateDirect()分配的内存也在这里 |
| Symbol | 保存 String 常量池的区域，也就是String Interning ，Symbols这个命名起源 LISP，详见https://en.wikipedia.org/wiki/Interning_(computer_science) |

另外NMT支持baseline来查看增量变化

首先建立基线：

```
jcmd 7 VM.native_memory baseline
Baseline succeeded
```

通过diff查看变化

```
 jcmd 7 VM.native_memory summary.diff scale=MB	
```

输出:

```
Total: reserved=2987390KB +19363KB, committed=1765430KB +21115KB
```



### 2、pmap命令

map工具可以查看进程的内存分布情况。一般使用pmap -x pid查看。可以把pmap 看到的内存和 jcmd pid VM.native_memory detail 的结果比对，两遍的内存地址是能对应的，如果是jni库申请的内存，在nmt看不到，但是从pmap -x是能看到的，另外pmap还可以配合gdb core dump文件分析内存布局。

语法

```shell
pmap -x 7  
```

输出内容：

```
Address           Kbytes     RSS   Dirty Mode  Mapping
00000000c0000000 1079424 1034684 1034684 rw---   [ anon ]
0000000101e20000 1017728       0       0 -----   [ anon ]
0000560848d8a000       4       4       0 r-x-- java
0000560848f8a000       4       4       4 r---- java
0000560848f8b000       4       4       4 rw--- java
00005608495b4000  521320   52884   52884 rw---   [ anon ]
```

列：

- **Address**: The beginning memory address allocation
- **Kbytes**: Memory allocation in kilobytes
- **RSS**: Resident set size of the process in memory
- **Dirty**: The status of the memory pages
- **Mode**: Access mode and privileges
- **Mapping**: The user-facing name of the application or library



通过第三列排序输出前面 20：

```
pmap -x 7 | sort -rn -k3 | head -n 20	
```

输出:  RSS1.9G

```
total kB         3604576 1963632 1947880
00000000c0000000 1079424 1034684 1034684 rw---   [ anon ]
00005608495b4000  521320  503100  503100 rw---   [ anon ]
00007f1c85000000  102208  101612  101612 rwx--   [ anon ]
00007f1c94000000   65516   54000   54000 rw---   [ anon ]
00007f1c74000000   65536   40120   40120 rw---   [ anon ]
00007f1c82bfd000   36876   21400   21400 rw---   [ anon ]
00007f1c6c000000   23128   10816   10816 rw---   [ anon ]
00007f1c9aae6000   13268    9944       4 r-x-- libjvm.so
00007f1c9886e000   10240    9400    9400 rw---   [ anon ]
00007f1c99370000    5904    4892    4892 rw---   [ anon ]
00007f1c7b876000    8204    2916    2916 rw---   [ anon ]
00007f1c68be8000   19452    2376    2376 rw---   [ anon ]
00007f1c726f4000    3064    2176    2176 rw---   [ anon ]
00007f1c635d7000    3064    2172    2172 rw---   [ anon ]
00007f1c722f2000    3064    2168    2168 rw---   [ anon ]
00007f1c7983e000    3064    2164    2164 rw---   [ anon ]
00007f1c705e3000    3064    2164    2164 rw---   [ anon ]
00007f1c624d0000    3064    2156    2156 rw---   [ anon ]
00007f1c7c680000    3064    2152    2152 rw---   [ anon ]
```



### 3、/proc/7/status 

cat /proc/7/status | grep Vm

```
VmPeak: 24426520 kB
VmSize: 24426516 kB
VmLck:         0 kB
VmPin:         0 kB
VmHWM:   1429080 kB
VmRSS:   1428796 kB
VmData: 24343220 kB
VmStk:       136 kB
VmExe:         4 kB
VmLib:     24812 kB
VmPTE:      5136 kB
VmSwap:        0 kB
```

 ps -p 7 -o rss,vsz 

```
 RSS    VSZ
1681408 12050624
```



### 4、 jemalloc

>  默认的 Linux 本机内存分配器是 glibc 的 malloc。 JVM（与任何其他进程一样）将调用 C 运行时 malloc 函数以便从操作系统分配内存，然后管理 Java 应用程序的堆。 Jemalloc 是另一种 malloc 实现，并且有一个名为 jeprof 的出色工具，它可以进行内存分配分析，使我们能够直观地跟踪调用 malloc 的内容。

安装步骤可以参考[这篇文章](https://github.com/jeffgriffith/native-jvm-leaks)或者jemalloc官方[INSTALL文档](https://github.com/jemalloc/jemalloc/blob/dev/INSTALL.md)

1、[下载源码](https://github.com/jemalloc/jemalloc/releases)；

2、安装 jemalloc；注意5版本对应centos7， [4版本](https://github.com/jemalloc/jemalloc/releases/download/4.2.0/jemalloc-4.2.0.tar.bz2)对应centos6.8

注意configure必须要带上 --enable-prof  ，不然后续使用会报错

```
 yum install ghostscript #pdf需要执行命令
 
 ./configure  --enable-prof
 make 
 make install
```

3、配置；

```
export LD_PRELOAD=/usr/local/lib/libjemalloc.so
export MALLOC_CONF=prof:true,lg_prof_sample:0,prof_final:true,prof_prefix:/root/output/jeprof
```

其中lg_prof_interval:20中的20表示1MB(2^20)，如果 30 就是1G，prof:true是打开profiling。运行程序时，每分配(大约)1MB就会dump产生一个文件。

输出到/root/output路径，heap文件以jeprof开头命名

另外一个参考配置：

```
ENV MALLOC_CONF="prof_leak:true,prof:true,lg_prof_interval:25,lg_prof_sample:18,prof_prefix:/tmp/jeprof"
```

4、启动jvm，等待产生heap文件；

```
[root@540bbeb8e7b2 output]# ls
jeprof.11891.0.f.heap  jeprof.12417.0.i0.heap  jeprof.16870.0.f.heap  jeprof.16874.0.f.heap
jeprof.11962.0.f.heap  jeprof.13881.0.i0.heap  jeprof.16873.0.f.heap
```

5、通过 jeprof 分析；

jeprof 提供了不少命令分析 heap 文件，分析单个heap文件：

```shell
jeprof --text `which java` jeprof.7.13.i13.heap | head -10  
```

 输出:

```shell
[root@jasmine-server-test-79f59f957c-sxwmn data]# jeprof --text `which java` jeprof.7.13.i13.heap | head -10
Using local file /usr/bin/java.
Using local file jeprof.7.13.i13.heap.
Total: 96.2 MB
    94.5  98.2%  98.2%     94.5  98.2% prof_backtrace_impl
     0.9   0.9%  99.1%      1.0   1.0% readCEN
     0.4   0.4%  99.5%     14.5  15.1% Java_java_util_zip_Inflater_init
     0.2   0.3%  99.7%      0.2   0.3% init
     0.1   0.1%  99.9%      0.1   0.1% __GI__dl_allocate_tls
     0.1   0.1% 100.0%      0.1   0.1% _dl_check_map_versions
```

也可以分析多个heap文件

```shell
jeprof --text `which java` jeprof.*.heap | head -10  
```

 输出:

```
Total: 2359.2 MB
  2317.5  98.2%  98.2%   2317.5  98.2% prof_backtrace_impl
    21.6   0.9%  99.1%     24.7   1.0% readCEN
     7.8   0.3%  99.5%    280.7  11.9% Java_java_util_zip_Inflater_init
     6.1   0.3%  99.7%      6.1   0.3% init
     3.1   0.1%  99.9%      3.1   0.1% __GI__dl_allocate_tls
     3.1   0.1% 100.0%      3.1   0.1% _dl_check_map_versions
     0.0   0.0% 100.0%      0.1   0.0% 0x00007f217b7a9790
     0.0   0.0% 100.0%    542.6  23.0% 0x00007f217b7ae426
     0.0   0.0% 100.0%      3.1   0.1% 0x00007f217b7aff48
```

这里解释下字段含义：

- 第 1 列包含以 MB 为单位的直接内存使用量；
- 第 4 列包含过程及其所有被调用者的内存使用情况；
- 第 2 和5列是第 1 和第 4 列的百分比；
- 第 3 列 是 第 2 列的累积和(即，第三列中的第 k 个条目是第二列中前 k 个条目的总和)；

```shell
jeprof `which java` jeprof.7.560.i560.heap 

jeprof --show_bytes --pdf `which java` jeprof.7.560.i560.heap > w.pdf

jeprof --show_bytes --svg `which java` jeprof.7.0.i0.heap > o.svg

jeprof `which java` --base=jeprof.7.0.i0.heap jeprof.7.3.i3.heap
```

jemalloc 还有其他一些使用场景，详见[官方文档](https://github.com/jemalloc/jemalloc/wiki/Use-Case%3A-Leak-Checking)

我自己制作了一个带jemalloc的jvm镜像，详见 https://github.com/jiangyunpeng/dockers/tree/master/docker-files/docker-centos-jemalloc

参考：

- [jemalloc的heap profiling](https://www.yuanguohuo.com/2019/01/02/jemalloc-heap-profiling/)

### 5、通过 dd + /proc/$pid/maps

[ 一次大量 JVM Native 内存泄露的排查分析](https://heapdump.cn/article/3530243) 文章中提到通过dd查看内存信息，尝试之后发现可能只能看到 jar包的内存

1、获取MAT内存地址：

```
SELECT * FROM java.util.zip.Inflater
```



<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230328150633048.png" alt="image-20230328150633048" style="zoom:50%;" />



2、 找到对应的map区间范围

```
[root@capital-web-dd56bd988-2ssg9 ~]# cat /proc/8/maps | grep 7f8554
7f8551620000-7f8554000000 ---p 00000000 00:00 0
7f8554000000-7f8555d84000 rw-p 00000000 00:00 0
```



3、执行脚本

```
./dump.sh 8 7f8554000000
```



脚本：

```
cat /proc/$1/maps | grep -Fv ".so" | grep " 0 " | awk '{print $1}' | grep $2 | ( IFS="-"
while read a b; do
dd if=/proc/$1/mem bs=1 skip=$(( 0x$a )) count=$(( 0x$b - 0x$a )) of="$1_mem_$a.bin"
done )
```



### 6、gdb查看内存信息

通过 gdb 可以直接查看java进程中的内存信息，网上也有通过gdb排查堆外内存的思路，参考[gdb调试java基本用法](https://kkewwei.github.io/elasticsearch_learning/2016/12/20/gdb%E8%B0%83%E8%AF%95java%E5%9F%BA%E6%9C%AC%E7%94%A8%E6%B3%95/)，通常有两种思路：

- 直接 gdb -pid  $pid 在线分析，它会直接hung住进程, **如果操作需要先关闭流量**（**推荐**）。
- 通过 kill -6 $pid  产生 coredump文件进行分析，这种方式会导致容器重启。



> 注意，执行 gdb dump memory 存在一定的风险，可能会导致进程被杀死 ，遇到过
>
> (gdb) dump memory memory.bin2 0xc0000000 0x101e20000
> Killed 



1、首先通过  `gdb -pid 进程号` 进入gdb交互模式，接着可以dump某个内存中的内容放入memory.bin1中，命令语法为：

```
dump memory memory.bin1 startoffset endoffset
```



2、其中startoffset、endoffset取值可以通过smaps获取

首先通过如下命令dump出进程中申请所有内存范围：

```
cat /proc/<pid>/smaps > smaps.txt
```

我们通过pmap命令输出我们想观察的内存区域：

![image-20240102112239124](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240102112239124.png) 

 比如我们要查看红框这块内存信息，可以在 smaps.txt 找到

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240102112319546.png" alt="image-20240102112319546" style="zoom:80%;" />

内存地址范围是 5608495b4000-5608692ce000

3、现在可以继续在gdb交互模式中执行，注意加上0x

```shell
dump memory memory.bin1 0x5608495b4000 0x5608692ce000
```

4、通过strings memory.bin1 输出文本格式:

```
(Ljava/lang/String;Ljavax
invokeMe0!HW
Ljava/la
ject;Lja
lang/Obj
name
args
setMetaCl@
007w
0S7w
Code
LocalV
StackMa
 *+,
_propCreator
inessTime":"2023-12-
:null,"transferFla
pplyDate":"2023-12-15 11:51:06","applyWeChatUserId":null,"currentNode":"FLOW_END","amount":********,"amountDesc":"776000.00","tag":"11413961ab914d5e8636fa28c0716a35","businessOrderNo":null,"payerName":"
","payerAcct":"8111101012701430172","payeeName":"
","payeeAcct":"***************","payeeAcctBank":"
","remark":"
```

这块内存应该记录一部分字节码信息

5、退出gdb

可以通过

```
 quit 或者 <ctrl+d>
```

### 7、使用strace命令

```
strace -e trace=brk,mmap -k -f -tt -T  -p 7
```

输出：

```
[pid 126887] 10:56:03.845493 mmap(0x7f523c13d000, 12288, PROT_NONE, MAP_PRIVATE|MAP_FIXED|MAP_ANONYMOUS|MAP_NORESERVE, -1, 0) = 0x7f523c13d000 <0.000026>
 > /usr/lib64/libc-2.17.so(mmap64+0x3a) [0xf8fca]
 > /data/program/java/jre/lib/amd64/server/libjvm.so(os::pd_uncommit_memory(char*, unsigned long)+0x18) [0x918bf8]
 > /data/program/java/jre/lib/amd64/server/libjvm.so(os::uncommit_memory(char*, unsigned long)+0x8a) [0x90f5fa]
 > /data/program/java/jre/lib/amd64/server/libjvm.so(JavaThread::exit(bool, JavaThread::ExitType)+0xb98) [0xa86898]
 > /data/program/java/jre/lib/amd64/server/libjvm.so(JavaThread::thread_main_inner()+0x26) [0xa86b06]
 > /data/program/java/jre/lib/amd64/server/libjvm.so(JavaThread::run()+0x2d0) [0xa86ec0]
 > /data/program/java/jre/lib/amd64/server/libjvm.so(java_start(Thread*)+0x101) [0x915131]
 > /usr/lib64/libpthread-2.17.so(start_thread+0xc4) [0x7ea4]
 > /usr/lib64/libc-2.17.so(__clone+0x6c) [0xfeb0c]
```



参考：

- [记一次堆外内存泄漏排查过程](https://xie.infoq.cn/article/e6fa07979e4375367ae9c2795)

## 实验

### 1、测试代码

写了一段测试代码:

```java
 		@GetMapping("/allocate")
    public String allocate() {
        log.info("create {}", Pool.allocate());
        return "ok";
    }

    private static class Pool {
        private static List<ByteBuffer> pool = new ArrayList<>();

        public static int allocate() {
            int size = 1024 * 1024 * 20;
            ByteBuffer byteBuffer = ByteBuffer.allocateDirect(size);
            for (int i = 0; i < 1024; ++i) {
                if(!byteBuffer.hasRemaining()){
                    break;
                }
                String string = UUID.randomUUID().toString();
                byteBuffer.put(string.getBytes());
            }
            pool.add(byteBuffer);
            return byteBuffer.position();

        }
    }
```

不停请求，通过NMT观察发现Internal区在增长

```
-                  Internal (reserved=376MB, committed=376MB)
                            (malloc=376MB #18166)
-                  Internal (reserved=656MB, committed=656MB)
                            (malloc=656MB #18235)
-                  Internal (reserved=696MB, committed=696MB)
                            (malloc=696MB #18248)
```

### 2、Jasmine-Server

Jasmine 通过NMT显示内存占用1.3G:

```
[root@jasmine-server-test-64cc68558d-rkfcw 1.0.9-SNAPSHOT]# jcmd 7 VM.native_memory summary scale=MB
7:

Native Memory Tracking:

Total: reserved=2581MB, committed=1373MB
-                 Java Heap (reserved=1024MB, committed=1024MB)
                            (mmap: reserved=1024MB, committed=1024MB) 

-                     Class (reserved=1111MB, committed=97MB)
                            (classes #15362)
                            (malloc=5MB #24887) 
                            (mmap: reserved=1106MB, committed=92MB) 

-                    Thread (reserved=107MB, committed=107MB)
                            (thread #107)
                            (stack: reserved=106MB, committed=106MB)

-                      Code (reserved=254MB, committed=60MB)
                            (malloc=11MB #15519) 
                            (mmap: reserved=244MB, committed=50MB) 

-                        GC (reserved=41MB, committed=41MB)
                            (malloc=3MB #272) 
                            (mmap: reserved=37MB, committed=37MB) 

-                  Compiler (reserved=1MB, committed=1MB)

-                  Internal (reserved=18MB, committed=18MB)
                            (malloc=18MB #19075) 

-                    Symbol (reserved=21MB, committed=21MB)
                            (malloc=17MB #160553) 
                            (arena=4MB #1)

-    Native Memory Tracking (reserved=4MB, committed=4MB)
                            (tracking overhead=3MB)
```

但是通过TOP显示占用1.5g 

![image-20220719094750500](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220719094750500.png)

为什么会出现差异，难道还有一些内存消耗没有被NMT监控到？

网上找了一些资料，java 堆外内存泄露很多都是 java.util.zip.Deflater 没有被关闭导致的，而我们目前使用的 springboot 版本刚好也存在这个bug，升级了springboot版本之后发现nmt 和top 的内存消耗一致了。

### 3、RES 与 JVM 参数设置

linux的RES会受到JVM参数设置的影响吗？首先再明确一下 linux 的RES （resident memory usage） 常驻内存是**指进程当前实际使用的内存大小**，如果申请100m的内存，实际使用10m，它只增长10m。

我测试了 JVM 不同参数下RES的变化： 

| RES  | JVM OPTIONS                                                  |
| ---- | ------------------------------------------------------------ |
| 892m | -XX:NewSize=1000m -XX:MaxNewSize=1000m -XX:MetaspaceSize=50m -XX:MaxMetaspaceSize=256m -Xms1200m -Xmx1200m |
| 775m | -XX:NewSize=800m -XX:MaxNewSize=800m -XX:MetaspaceSize=50m -XX:MaxMetaspaceSize=256m -Xms1200m -Xmx1200m |
| 474m | -XX:NewSize=400m -XX:MaxNewSize=400m -XX:MetaspaceSize=50m -XX:MaxMetaspaceSize=256m -Xms1200m -Xmx1200m |
| 279m | -XX:NewSize=128m -XX:MaxNewSize=128m -XX:MetaspaceSize=50m -XX:MaxMetaspaceSize=256m -Xms1200m -Xmx1200m |

**这里对 RES 影响的是-XX:MaxNewSize**。另外通过 NMT 会发现 committed 1383MB(约等于-Xms)，为啥 RES 小于 NMT committe呢？

**原因是操作系统的内存管理是惰性的，对于已申请的内存虽然会分配地址空间，但并不会直接占用物理内存，真正使用的时候才会映射到实际的物理内存，所以committed > res也是很可能的。**

## 总结

- springboot升级能减少 200MB 的堆外内存消耗，如果内存吃紧的应用还是比较可观。
- 如果通过top命令发现 RES 占用超过 NMT占用，则很可能存在内存泄露，可以进一步通过jemalloc排查原因；
- 每个线程会消耗掉1MB堆外内存，所以线程数量不宜设置得过大，如果应用真的线程占用较多可以增大总内存；
- 通过 jmap -clstats 查看类
- 通过 /proc/pid/smaps 、 pmap、 diff 命令查看内存变化 案例

## 案例1

> 这个问题困扰了我好几个月，最后终于解决了，前面走不少弯路

### 问题

用户反馈有个应用大概 1 个星期就会出现一次物理内存告警，期间内存缓慢增加，直到触发内存告警。<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230724205537590.png" alt="image-20230724205537590" style="zoom:50%;" />

为什么内存会持续增长没有释放，最终达到容器内存限制呢？

### 排查过程 

首先通过我们的JVM监控查看 JVM堆内内存消耗发现是正常的，所以怀疑是堆外内存泄露问题。

通过前面提到的 NMT 工具排查，发现应用进程 RSS 达到 3G 时， NMT committed 内存才 2G，还有 1G 的内存占用去哪里呢？这时候通过 NMT 工具无能为力。

通过我们监控工具发现这个应用的的类加载个数非常多，达到 14 万，普通应用小于 4 万，怀疑是类加载问题，通过dump class之后发现实际达到 3万之后就不再增长，并且没有触发 Metaspace OOM 说明也不是类加载问题，隋排除这方面的原因。

尝试了pmap， /proc/pid/smaps 等系统工具都没有找到什么有价值的信息。

找一块内存块进行dump

```
gdb --batch --pid 1 -ex "dump memory a.dump 0x7fd488000000 0x7fd488000000+56124000"
```

简单分析一下内容，发现绝大部分是乱码的二进制内容，看不出什么问题。

通过阅读业务应用程序代码发现该应用大量使用groovy、生成zip压缩文件，怀疑是代码有问题，但是看了下代码都正常关闭了InflaterInputStream没有找到内存泄露点。虽然知道可能是Inflater没有正常关闭导致物理内存泄露，但是无法定位到时哪里导致了内存泄露，排查进入了瓶颈。**这里其实思路已经错了以为是内存泄露**

直到有一天看到 [携程 JEMALLOC 升级实践](https://zhuanlan.zhihu.com/p/613491120) ，这篇文章堆外内存管理提到的问题和我们情况类似，才打开了思路。

我们用

```
gdb --batch --pid 36563 --ex 'call malloc_trim()'
```



 强行回收内存后，发现RSS有明显下降，可以确定JVM没有内存泄露，而是是 glibc 没有释放物理内存给 OS 导致的。

![wecom-temp-eea01f2098c270bd48fad5fa10022d33](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/wecom-temp-eea01f2098c270bd48fad5fa10022d33.png)

那为什么 glibc 不释放内存呢？原来glibc默认的内存分配器 PTMALLOC 会池化内存，当  JVM 使用完堆外内存进行释放时，PTMALLOC只会把内存返回给内存池(arena)，这个arena默认大小是多大呢？每个arena默认是64MB，个数是 64 位系统下 `8 * number of cores`，一台16核的标准机器上最多会有128个arena，并占用高达8G的堆外内存。

可以通过下面命令验证：

```
pmap -x 8 | grep 65524 | wc -l
```

我在一台2g内存机器上通过gdb强行回收之后比较了前后pmap差异，大概回收了500m的内存，主要是一块500m的内存被释放了

```shell
Address           Kbytes     RSS   Dirty Mode  Mapping
00005608495b4000  521320  503100  503100 rw---   [ anon ]  #回收前
00005608495b4000  521320   52884   52884 rw---   [ anon ]  #回收后
```

### 解决方案

1、控制 arena 的总数上限。通过下面方式把 ARENA限制为 2个

```
export MALLOC_ARENA_MAX=2
```

2、升级使用 jemalloc 分配器

最终我们采用第一种方案。

在 [presto#8993](https://github.com/prestodb/presto/issues/8993)、[heroku](https://devcenter.heroku.com/articles/tuning-glibc-memory-behavior#what-value-to-choose-for-malloc_arena_max) 中均有提及。



## 案例2

### 问题

wone-backend 应用使用 malloc_trim强制回收没有效果，怀疑存在内存泄漏问题。

### 添加 malloc_hook

代码如下：

```c
#define _GNU_SOURCE
#include <stdio.h>
#include <stdlib.h>
#include <malloc.h>
#include <unistd.h>
#include <dlfcn.h>
#include <pthread.h>
#include <sys/syscall.h>
#include <time.h>

// 定义钩子函数指针
static void *(*old_malloc_hook)(size_t, const void *);
// 线程锁，用于避免多线程环境下的竞态条件
static pthread_mutex_t hook_lock = PTHREAD_MUTEX_INITIALIZER;

// 获取线程 ID
static pid_t gettid() {
    return syscall(SYS_gettid);
}

// 获取当前时间的字符串表示
void get_current_time(char* buffer, size_t buffer_size) {
    struct timespec ts;
    struct tm* tm_info;
    
    clock_gettime(CLOCK_REALTIME, &ts);
    tm_info = localtime(&ts.tv_sec);
    
    strftime(buffer, buffer_size, "%Y-%m-%d %H:%M:%S", tm_info);
    snprintf(buffer + 19, buffer_size - 19, ".%09ld", ts.tv_nsec);
}

// 日志记录函数
void log_malloc(size_t size, void *result) {
    char time_buffer[64];
    get_current_time(time_buffer, sizeof(time_buffer));
    pid_t tid = gettid();
    
    fprintf(stderr, "[%s] [Thread %d] malloc(%zu) = %p\n", time_buffer, tid, size, result);
}

// 自定义 malloc 钩子函数
static void *my_malloc_hook(size_t size, const void *caller) {
    void *result;

    // 恢复原始钩子，以避免递归调用
    __malloc_hook = old_malloc_hook;

    // 调用原始 malloc 函数
    result = malloc(size);

    // 记录日志
    log_malloc(size, result);

    // 恢复自定义钩子
    __malloc_hook = my_malloc_hook;

    return result;
}

// 初始化自定义钩子
void install_hooks(void) {
    pthread_mutex_lock(&hook_lock);

    old_malloc_hook = __malloc_hook;
    __malloc_hook = my_malloc_hook;

    pthread_mutex_unlock(&hook_lock);
}

// 恢复原始钩子
void remove_hooks(void) {
    pthread_mutex_lock(&hook_lock);

    __malloc_hook = old_malloc_hook;

    pthread_mutex_unlock(&hook_lock);
}

// 初始化钩子的构造函数
__attribute__((constructor))
static void init(void) {
    install_hooks();
}

// 恢复钩子的析构函数
__attribute__((destructor))
static void fini(void) {
    remove_hooks();
}

```

编译：

```
gcc -shared -o libmalloc_hook.so -fPIC malloc_hook.c -ldl -pthread
```

启动，新建脚本

```bash
#!/bin/bash

LD_PRELOAD=./libmalloc_hook.so java  -Xms2g -Xmx2g -javaagent:./agent/quantum-agent/quantum-agent.jar   -jar lib/wone-backend-0.0.1-SNAPSHOT.jar
```

输出：

```
[2024-06-17 11:55:24.873442810] [Thread 1059] malloc(65) = 0x7fb880002850
[2024-06-17 11:55:24.873452995] [Thread 1059] malloc(8) = 0x7fb880001420
[2024-06-17 11:55:24.873460598] [Thread 1059] malloc(7) = 0x7fb880002450
```



## 参考

- [growing-resident-memory-usage-rss-of-java-process](https://stackoverflow.com/questions/26041117/growing-resident-memory-usage-rss-of-java-process)
- [Native Memory — The Silent JVM Killer](https://medium.com/swlh/native-memory-the-silent-jvm-killer-595913cba8e7)
- [Using jemalloc to get to the bottom of a memory leak](https://technology.blog.gov.uk/2015/12/11/using-jemalloc-to-get-to-the-bottom-of-a-memory-leak/)
- [JVM Metaspace OOM的排障以及原理分析](https://juejin.cn/post/7127614617919946760#heading-3)
- [一次完整的JVM堆外内存泄漏故障排查记录](https://heapdump.cn/article/1821465)
- [Java – Debugging Native Memory Leaks](https://www.bro-code.in/blog/java-debugging-native-memory-leak)
- glibc 相关
  - [携程 JEMALLOC 升级实践](https://zhuanlan.zhihu.com/p/613491120)
  - [一次 Java 进程 OOM 的排查分析（glibc 篇）](https://zhuanlan.zhihu.com/p/166576293)
  - [排查docker oomkillded问题](https://pooi.app/2020/05/10/2020/2020-05-10-%E6%8E%92%E6%9F%A5docker_oomkillded%E9%97%AE%E9%A2%98/)
  - [presto#8993](https://github.com/prestodb/presto/issues/8993)
  - [内存暴涨的分析方法和工具](https://zedware.github.io/MEMORY/)

