## Java http客户端如何处理证书

HTTPS（HTTP over Secure Socket Layer），简单讲即HTTP下加入SSL层，HTTPS的安全基础是SSL。如果要实现SSL通讯，通讯双方需要设置KeyStore 和 TrustStore。

如果是单向认证，那么client侧只需要设置TrustStore, 客户端的TrustStore文件中保存着被客户端所信任的服务器的证书信息，客户端在进行SSL连接时，JSSE将根据这个文件中的证书决定是否信任服务器端的证书。


但是通常我们并不需要做这个就能正常访问HTTPS服务。比如直接使用Java的java.net.URLConnection访问微信的获取AccessToken的API接口。

```java
    @Test
    public void testHttpUrlConnection() throws IOException, URISyntaxException {
        URL url = new URL("https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=1&secret=2");
        URLConnection con = url.openConnection();
        con.connect();
        BufferedReader reader = new BufferedReader(new InputStreamReader(con.getInputStream(), "utf-8"));
        String s;
        while ((s = reader.readLine()) != null) {
            System.out.println(s);
        }
        reader.close();
    }
```

为什么不需要设置TrustStore，HTTPS客户端就能正常工作呢？

首先要说明，TrustStore文件中可以不保存服务器的证书信息，如果服务器的证书是经过CA签名的，那么只要保存着CA的根证书即可。
在SunJSSE中，有一个信任管理器类负责决定是否信任远端的证书，这个类有如下的处理规则：

- 若系统属性javax.net.ssl.trustStore指定了TrustStore文件，那么信任管理器就去jre安装路径下的lib/security/目录中寻找并使用这个文件来检查证书。
- 若该系统属性没有指定TrustStore文件，它就会去JRE安装路径下寻找默认的TrustStore文件，这个文件的相对路径为：lib/security/jssecacerts。
- 若jssecacerts不存在，但是cacerts存在（它随JRE一起发行，含有数量有限的可信任的基本证书），那么这个默认的TrustStore文件就是lib/security/cacerts。

所以微信能被信任，是因为它的根证书GeoTrust Global CA的指纹DE:28:F4...在可信任证书列表中。具体查看访问可以参考网上文章。

另外当 URL为https时， con返回的类型为 sun.net.www.protocol.https.HttpsURLConnectionImpl



## 解决证书不可信问题

如果服务器证书并没有经过签名，或者签名的根证书不是上述可信任证书。这个时候，直接使用URLConnection或者RestTemplate就会得到如下的错误：

```java
javax.net.ssl.SSLHandshakeException: sun.security.validator.ValidatorException: PKIX path building failed: sun.security.provider.certpath.SunCertPathBuilderException: unable to find valid certification path to requested target
```

解决这个问题有两个方法

### 方法一、手动导入证书

首先通过chrome浏览器访问目标域名，先从浏览器中导出证书，方法是把证书大图标拖动到本地目录中，接下来就是把证书导入到jdk，通过如下两个命令：

```shell
/data/program/java8/jre/bin/keytool -import -alias ad-hz1.proxy.wacai.info:636 -keystore jssecacerts -file /data/program/java8/jre/lib/security/ldap-proxy2705.cer


 #查看
/data/program/java8/jre/bin/keytool  -list -keystore /data/program/java8/jre/lib/security/jssecacerts  -alias ad-hz1.proxy.wacai.info:636

# -keystore 表示证书存在地址
# -file 表示要导入的证书地址

# 删除
keytool -delete -alias ad-hz1.proxy.wacai.info:636 -keystore cacerts
```

操作中需要输入默认密码：changeit

需要注意的java 提供了两个keystore:

- cacerts：  Java 默认的系统级别的密钥库文件，通常位于 `$JAVA_HOME/lib/security/cacerts`。它包含了一系列的根证书和中间证书，这些证书由 Java 运行时环境用于验证服务器的身份和建立安全连接。这些证书被用于验证 SSL/TLS 通信和数字签名等操作。Java 的安全性和信任体系会依赖于这个密钥库。
- jssecacerts： 也是一个密钥库文件，位于相同的路径下，但默认情况下是空的，不包含任何证书。这个文件的存在是为了允许用户在其上添加自定义的根证书和中间证书，以增强应用程序的安全性。



### 方法二、自己实现证书信任管理器类

实现自己的证书信任管理器类，比如**MyX509TrustManager**，该类必须实现X509TrustManager接口中的三个method。代码套路大概是:

```java
SSLContext ctx = SSLContext.getInstance("TLS");
X509TrustManager tm = new X509TrustManager() {
    @Override
    public void checkClientTrusted(X509Certificate[] chain,
                                   String authType) throws CertificateException {
    }
    @Override
    public void checkServerTrusted(X509Certificate[] chain,
                                   String authType) throws CertificateException {
    }
    @Override
    public X509Certificate[] getAcceptedIssuers() {
        return null;
    }
};
ctx.init(null, new TrustManager[]{tm}, null);
SSLSocketFactory ssf = new SSLSocketFactory(ctx,SSLSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);

```



### okhttp3处理证书信任问题

参考：[Trusting All Certificates in OkHttp](https://www.baeldung.com/okhttp-client-trust-all-certificates)

```java
	TrustManager[] trustAllCerts = new TrustManager[]{
                new X509TrustManager() {
                    @Override
                    public void checkClientTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public void checkServerTrusted(java.security.cert.X509Certificate[] chain, String authType) {
                    }

                    @Override
                    public java.security.cert.X509Certificate[] getAcceptedIssuers() {
                        return new java.security.cert.X509Certificate[]{};
                    }
                }
        };

        SSLContext sslContext = SSLContext.getInstance("SSL");
        sslContext.init(null, trustAllCerts, new java.security.SecureRandom());

        OkHttpClient.Builder newBuilder = new OkHttpClient.Builder();
        newBuilder.sslSocketFactory(sslContext.getSocketFactory(), (X509TrustManager) trustAllCerts[0]);
        newBuilder.hostnameVerifier((hostname, session) -> true);

        OkHttpClient sslClient = newBuilder.build();
```



## 数字证书

官方解释：数字证书是一个经证书授权中心数字签名的包含公开密钥拥有者信息以及公开密钥的文件。最简单的证书包含一个公开密钥、名称以及证书授权中心的数字签名。数字证书还有一个重要的特征就是只在特定的时间段内有效。数字证书是一种权威性的电子文档，可以由权威公正的第三方机构，即CA（例如中国各地方的CA公司）中心签发的证书，也可以由企业级CA系统进行签发。

### 证书校验

客户端在验证证书的有效性的时候，会逐级去寻找签发者的证书，直至根证书为结束，然后通过公钥一级一级验证数字签名的正确性。 证书链(certificate chain)可以有任意环节的长度：CA证书包括根CA证书、二级CA证书（中间证书）、三级证书.....，以下是对证书链的图解：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/java/05.png" alt="05" style="zoom:50%;" />

### Truststore和Keystore区别

keystore：存储密钥（公钥、私钥、数字签名）的容器，

- keystore一般存储本身的私钥和公钥，
- truststore则用来存储本身信赖的对象的公钥，代表了可以信任的证书。

## 参考

- [Java HTTPS客户端如何处理证书](https://blog.csdn.net/zlfing/article/details/77648323)