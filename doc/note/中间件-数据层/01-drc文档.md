## binlog知识

常用sql

```sql
select * from information_schema.processlist as process where process.command = 'Binlog Dump';
show master status; #查看binlog最新状态
show master logs; #查看历史binlog
show variables like '%binlog_%'; #查看binlog配置
show variables like '%log_bin%'; #查看binlog位置
```

可以通过 `show variables like '%binlog_%';` 查看binlog配置：

```
binlog_cache_size	32768
binlog_checksum	NONE
binlog_direct_non_transactional_updates	OFF
binlog_error_action	ABORT_SERVER
binlog_format	ROW
binlog_group_commit_sync_delay	0
binlog_group_commit_sync_no_delay_count	0
binlog_gtid_simple_recovery	ON
binlog_max_flush_queue_time	0
binlog_order_commits	ON
binlog_row_image	FULL
binlog_rows_query_log_events	OFF
binlog_stmt_cache_size	32768
```

在 MySQL 5.7.7 之前，默认的格式是 STATEMENT，在 MySQL 5.7.7 及更高版本中，默认值是 ROW。

### binlog_format

Statement

每一条会修改数据的sql都会记录在binlog中。

- 优点：不需要记录每一行的变化，减少了binlog日志量，节约了IO, 提高了性能。
- 缺点：由于记录的只是执行语句，为了这些语句能在slave上正确运行，因此还必须记录每条语句在执行的时候的一些相关信息，以保证所有语句能在slave得到和在master端执行的时候相同的结果。另外mysql的复制，像一些特定函数的功能，slave与master要保持一致会有很多相关问题。

Row

5.1.5版本的MySQL才开始支持 row level 的复制,它不记录sql语句上下文相关信息，仅保存哪条记录被修改。

- 优点： binlog中可以不记录执行的sql语句的上下文相关的信息，仅需要记录那一条记录被修改成什么了。所以row的日志内容会非常清楚的记录下每一行数据修改的细节。而且不会出现某些特定情况下的存储过程，或function，以及trigger的调用和触发无法被正确复制的问题.
- 缺点:所有的执行的语句当记录到日志中的时候，都将以每行记录的修改来记录，这样可能会产生大量的日志内容。

将二进制日志格式设置为ROW时，有些更改仍然使用基于语句的格式，包括所有DDL语句，例如CREATE TABLE， ALTER TABLE，或 DROP TABLE。

Mixed

从5.1.8版本开始，MySQL提供了Mixed格式，实际上就是Statement与Row的结合。
在Mixed模式下，一般的语句修改使用statment格式保存binlog，如一些函数，statement无法完成主从复制的操作，则采用row格式保存binlog，MySQL会根据执行的每一条具体的sql语句来区分对待记录的日志形式，也就是在Statement和Row之间选择一种。

5.7版本以后默认情况下binlog的格式设置为ROW，ROW模式记录的是每行实际数据的变更，所以将会导致对应的日志文件较大的情况，但是现在有SSD和万兆光纤网络，所以这些磁盘IO和网络IO也是问题不大的；

### binlog_row_image

上面提到了设置binlog-format为ROW后，MySql会记录每一行数据修改的细节日志，在此之上，binlog_row_image则是对ROW的配置进行了更下一步的细分；

binlog_row_image可设置的值如下：

- FULL：记录数据变更的所有前镜像和后镜像
- MINIMAL：binlog日志的前镜像只记录唯一识别列(唯一索引列、主键列)，后镜像只记录修改列。
- NOBLOB：binlog记录所有的列，就像full格式一样。但对于BLOB或TEXT格式的列，如果他不是唯一识别列(唯一索引列、主键列)，或者没有修改，那就不记录。

### 查看binlog

通过如下命令查看 binlog

```bash
show master status;
show binlog events in 'mysql-bin.000003';
```

输出：

```
mysql> show binlog events in 'mysql-bin.000004' from 756 ;
+------------------+------+----------------+-----------+-------------+--------------------------------------+
| Log_name         | Pos  | Event_type     | Server_id | End_log_pos | Info                                 |
+------------------+------+----------------+-----------+-------------+--------------------------------------+
| mysql-bin.000004 |  756 | Anonymous_Gtid |         1 |         821 | SET @@SESSION.GTID_NEXT= 'ANONYMOUS' |
| mysql-bin.000004 |  821 | Query          |         1 |         904 | BEGIN                                |
| mysql-bin.000004 |  904 | Table_map      |         1 |         969 | table_id: 109 (ucenter.wse_cluster)  |
| mysql-bin.000004 |  969 | Write_rows     |         1 |        1026 | table_id: 109 flags: STMT_END_F      |
| mysql-bin.000004 | 1026 | Xid            |         1 |        1057 | COMMIT /* xid=80 */                  |
| mysql-bin.000004 | 1057 | Rotate         |         1 |        1104 | mysql-bin.000005;pos=4               |
+------------------+------+----------------+-----------+-------------+--------------------------------------+
```

这里无法看到详细的字段信息。



通过 mysqlbinlog 查看：

```
mysqlbinlog --no-defaults  /var/lib/mysql/mysql-bin.000005  -vv --base64-output=decode-rows  --start-position=756
```

输出：

```
# at 969
#221123 15:29:51 server id 1  end_log_pos 1026 CRC32 0xc3a6625c 	Write_rows: table id 109 flags: STMT_END_F
### INSERT INTO `ucenter`.`wse_cluster`
### SET
###   @1=11 /* LONGINT meta=0 nullable=0 is_null=0 */
###   @2=1669188591 /* TIMESTAMP(0) meta=0 nullable=0 is_null=0 */
###   @3='2022-11-23 15:29:51' /* DATETIME(0) meta=0 nullable=0 is_null=0 */
###   @4='r1' /* VARSTRING(96) meta=96 nullable=1 is_null=0 */
###   @5=0 /* TINYINT meta=0 nullable=1 is_null=0 */
# at 1026
#221123 15:29:51 server id 1  end_log_pos 1057 CRC32 0xb2f9597c 	Xid = 159
COMMIT/*!*/;
```



### 测试

删除

```
BEGIN
/*!*/;
# at 1197
#221123 15:30:18 server id 1  end_log_pos 1262 CRC32 0xb75f66c0 	Table_map: `ucenter`.`wse_cluster` mapped to number 109
# at 1262
#221123 15:30:18 server id 1  end_log_pos 1451 CRC32 0xe7eef2a9 	Delete_rows: table id 109 flags: STMT_END_F
### DELETE FROM `ucenter`.`wse_cluster`
### WHERE
###   @1=4 /* LONGINT meta=0 nullable=0 is_null=0 */
###   @2=1669187555 /* TIMESTAMP(0) meta=0 nullable=0 is_null=0 */
###   @3='2022-11-23 15:12:35' /* DATETIME(0) meta=0 nullable=0 is_null=0 */
###   @4='r1' /* VARSTRING(96) meta=96 nullable=1 is_null=0 */
###   @5=0 /* TINYINT meta=0 nullable=1 is_null=0 */
### DELETE FROM `ucenter`.`wse_cluster`
### WHERE
###   @1=5 /* LONGINT meta=0 nullable=0 is_null=0 */
###   @2=1669187555 /* TIMESTAMP(0) meta=0 nullable=0 is_null=0 */
###   @3='2022-11-23 15:12:35' /* DATETIME(0) meta=0 nullable=0 is_null=0 */
###   @4='r1' /* VARSTRING(96) meta=96 nullable=1 is_null=0 */
###   @5=0 /* TINYINT meta=0 nullable=1 is_null=0 */
### DELETE FROM `ucenter`.`wse_cluster`
### WHERE
###   @1=6 /* LONGINT meta=0 nullable=0 is_null=0 */
###   @2=1669187782 /* TIMESTAMP(0) meta=0 nullable=0 is_null=0 */
###   @3='2022-11-23 15:16:22' /* DATETIME(0) meta=0 nullable=0 is_null=0 */
###   @4='r1' /* VARSTRING(96) meta=96 nullable=1 is_null=0 */
###   @5=0 /* TINYINT meta=0 nullable=1 is_null=0 */
### DELETE FROM `ucenter`.`wse_cluster`
### WHERE
###   @1=7 /* LONGINT meta=0 nullable=0 is_null=0 */
###   @2=1669187842 /* TIMESTAMP(0) meta=0 nullable=0 is_null=0 */
###   @3='2022-11-23 15:17:22' /* DATETIME(0) meta=0 nullable=0 is_null=0 */
###   @4='r1' /* VARSTRING(96) meta=96 nullable=1 is_null=0 */
###   @5=0 /* TINYINT meta=0 nullable=1 is_null=0 */
### DELETE FROM `ucenter`.`wse_cluster`
#221123 15:30:18 server id 1  end_log_pos 1482 CRC32 0xa02c3202 	Xid = 164
COMMIT/*!*/;
```



## 架构

![架构](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/drc-arch.png)

### 组件

* drc-admin: drc配置中心，管控台。
* drc-producer：集成了canal，负责从mysql抽取binlog，并发送到kafka。
* drc-streams：消费kafka中的binlog，并按表级别拆分，一个表对应一个topic，写到kafka。
* drc-sinker：用于mysql数据同步，消费表级别的binlog数据，同步到其他mysql实例的库表中，主要记账团队使用。
* drc-comparison：用于数据对账，主要针对如果drc-sinker出现同步数据不一致的情况进行检查和修正。
* mars-client: 用于mysql同步的sdk，被drc-sinker依赖 。
* drc-consumer: 用于双活业务的mysql同步业务，已下线。

### 集群

| Id   | 业务                    | drc-producer                                                 | cluster            | replicaSetId | topic              | drc-streams   |
| ---- | ----------------------- | ------------------------------------------------------------ | ------------------ | ------------ | ------------------ | ------------- |
| 1    | wac1 副本集 drc-sub集群 | [drc-producer-1](http://cloud.wacai.info/k2/prod/qsh-1/application/middleware/drc-producer-1) | drc-sub            | wac1         | drc.sub            | drc-streams-1 |
| 2    | wac2副本集 drc-sub集群  | drc-producer-2                                               | drc-sub            | wac2         | drc.sub            | drc-streams-1 |
| 3    | wac3副本集 drc-sub2集群 | drc-producer-3                                               | drc-sub2           | wac3         | drc.sub            | drc-streams-1 |
| 4    | wac4副本集 drc-sub2集群 | drc-producer-4                                               | drc-sub2           | wac4         | drc.sub            | drc-streams-1 |
| 5    | drc离线批量binlog       | drc-producer-batch                                           | drc-sub            | bigdata      | drc.sub.batch      | drc-streams-3 |
| 6    | 双活user_sharding库     | drc-producer-ucenter                                         | drc-user-sharding  | wac-ucenter  | drc.user.sharding  | drc-streams-2 |
| 7    | 双活finance_market库    | drc-producer-finance-market                                  | drc-finance-market | wac-finance  | drc.finance.market | drc-streams-2 |
| 8    | 双活wac_activity库      | drc-producer-activity                                        | drc-wac-activity   | wac-activity | drc.wac.activity   | drc-streams-2 |

> 注意：后3个双活业务用的kafka是10.1.128.54:9093，前 5个连的kafka是10.1.128.54:9092



## 配置

### 核心概念

drc配置有几个核心概念:

- **cluster和replicaSet**:  一个集群可以理解为业务分组， 目前定义的集群包括：drc-sub，drc-sub2，drc-user-sharding；在一个集群下又存在不同的replicaSet，比如drc-sub集群下又存在wac1，wac2，bigdata3个replicaSet，replicaSet有点像Tlog中的采集点。
- **task**：在控制台可以为一个replicaSet分配任务，任务定义了mysql订阅信息；
- **destinations**:  cannal中的概念，表示一个实际运行的任务，在drc中等同于task;

### zk路径

drc把配置保存在zk上， 对应的 zk 也存在下面3个路径保存上述 3 种概念：

1、/storage/drc/replsets。保存replicaSet 信息，drc-producer 启动后会写入 /storage/drc/replsets/${cluster}/${replicaSetId} 路径。

2、/storage/drc/tasks。用户在 drc-admin 新增任务时会做以下两个操作：

- 写入 /storage/drc/tasks/{cluster}/{taskID} 路径保存task配置信息，包括mysql主从信息，过滤正则

  ```json
  {
  	"taskId": "drc-daily-************:3310-************:3310",
  	"masterInstance": {
  		"host": "************",
  		"port": 3310,
  		"binlog": {
  			"binlogFile": ""
  		},
  		"username": "drc",
  		"pwd": "d77edef36384cf38f8e67ca8c91afffa"
  	},
  	"slaveInstance": {
  		"host": "************",
  		"port": 3310,
  		"binlog": {
  			"binlogFile": ""
  		},
  		"username": "drc",
  		"pwd": "d77edef36384cf38f8e67ca8c91afffa"
  	},
  	"weight": 1,
  	"filterRegex": ".*",
  	"filterBlackRegex": "heartbeat\\..*"
  }
  ```

- 写入  /storage/drc/replsets/${cluster}/${replicaSetId}  路径，此操作可以理解为 replicaSet 分配关联的 task 任务：

  ```json
  {
  	"id": "wac-ucenter",
  	"taskIds": ["drc-user-sharding-**********:3306-**********:3306",
                "drc-user-sharding-**********:3309-**********:3309"],
  	"weightSum": 2
  }
  ```

​	这里注意：一个replicaSetId下可以有存在多台机器，这些机器都会去争抢这一批taskIds

3、/storage/drc/destinations；此目录是canal操作，drc中taskId对等canal中的destination，当为canal创建destination时，canal会把binlog点位信息保存在/storage/drc/destinations/${destination}/${clientId}/cursor 目录，信息如下:

```json
[zk: localhost:22181(CONNECTED) 30] get /storage/drc/destinations/drc-user-sharding-**********:3306-**********:3306/1001/cursor

{
	"@type": "com.alibaba.otter.canal.protocol.position.LogPosition",
	"identity": {
		"slaveId": -1,
		"sourceAddress": {
			"address": "**********",
			"port": 3306
		}
	},
	"postion": {
		"included": false,
		"journalName": "mysql-bin.001514",
		"position": 505205100,
		"serverId": 96283306,
		"timestamp": 1661430285000
	}
}
```



## drc-producer

### 开始

代码 DRCCanalController.start()方法

1、创建zk路径：

- /storage/drc/destinations
- /storage/drc/tasks
- /storage/drc/replsets

2、从  /storage/drc/replsets/${cluster}/${replicaSetId}  拿到分配的taskId。比如：

```json
{
	"id": "wac-ucenter",
	"taskIds": ["drc-user-sharding-**********:3306-**********:3306",
              "drc-user-sharding-**********:3309-**********:3309"],
	"weightSum": 2
}
```



3、启动 destination

```java
private void startDRCTasks(List<DRCTask> tasks) {
   for (DRCTask drcTask : tasks) {
     	final String destination = drcTask.getTaskId();
     	if (!embededCanalServer.isStart(destination)) {
        	//通过 ServerRunningMonitor 保障Canal的HA机制
        	ServerRunningMonitor runningMonitor = ServerRunningMonitors.getRunningMonitor(destination);
        	if (!runningMonitor.isStart()) {
            	//启动
          		runningMonitor.start();        
          }
      }
   }
}
```

ServerRunningMonitor.start()方法代码如下： 

```java
public void start() {
        super.start();
  			//①
        processStart();
        if (zkClient != null) {
          	//②监听
            String path = ZookeeperPathUtils.getDestinationServerRunning(destination);
            zkClient.subscribeDataChanges(path, dataListener);
          	//③
            initRunning();
        } else {
            processActiveEnter();
        }
}
```

ServerRunningMonitor 上述代码实现了一个简单的选主策略来实现 HA，原理如下：

1. 创建zk临时节点，路径为 /storage/drc/destinations/${destination}/producers/${本机ip}:${replicaSet} ；
2. 启动canal前，在 /storage/drc/destinations/${destination}/running 添加监听器，当节点数据发生变化时，判断节点数据中的ip是不是自己，如果不是自己则调用embededCanalServer.stop()方法停止CanalServer，当该节点被删掉则把自己拉起来；
3. initRunning()方法负责写入把 ${本机ip}:${replicaSet} 数据**尝试写入**上述zk路径， 如果成功写入zk启动 embededCanalServer，如果写入失败说明老的实例还没有退出，standby；

**通过上述操作可以保证同一个destination只会存在一个CanalServer**

启动 embededCanalServer 的逻辑在 ServerRunningMonitorWrapper.addListener()中，代码如下:

```java
runningMonitor.setListener(new ServerRunningListener() {

    public void processActiveEnter() {
      try {
        MDC.put(MDC_DESTINATION, String.valueOf(destination));
        // 避免zookeeper抖动的情况下, 频繁创建MessageFetcher线程
        if (embededCanalServer.isStart(destination)) {
          return;
        }
        //①
        ClientIdentity clientId = new ClientIdentity(destination, DRCConstants.CLIENT_ID);
        embededCanalServer.start(destination);
        embededCanalServer.subscribe(clientId);
        FilterRegexMonitors.addMonitor(embededCanalServer, zkClientx, destination);
        //②
        MessageFetcher.start(embededCanalServer, clientId, destination);
      } finally {
        MDC.remove(MDC_DESTINATION);
      }
    }
}                        
```

1. 启动 embededCanalServer 的套路就是这三行代码：创建clientId，start，subscribe；
2. 启动 MessageFetcher 从canal拉取binlog；

在 embededCanalServer.start() 内部会执行如下代码:

```java
public void start(final String destination) {
  	//①
  	final CanalInstance canalInstance = canalInstances.get(destination);
  	canalInstance.start();
}
```

canalInstances 是一个map，会触发 CanalInstanceGenerator 逻辑，通过destination 初始化 CanalInstance逻辑，drc就从/storage/drc/tasks/{taskId}获取配置信息初始化CanalInstance。

这里就完成了 cannal 初始化逻辑。

### 从cannal中拉取binlog

DRCCanalMessageFetcher  是一个线程，内部会不停的调用 CanalServerWithEmbedded.getWithoutAck() 代码如下：

```java
public final class DRCCanalMessageFetcher{
    public void run() {
       try {
       		while (embededCanalServer.isStart(destination)) {
            //①
            message = embededCanalServer.getWithoutAck(clientIdentity,binlogEventBatchSize);
            if (message == null || message.getId() == -1L) { 
              // 代表没数据，等待一段时间
              applyWait(++fullTimes);
            } else {
              fullTimes = 0; // reset fullTimes
              //②
              drcCanalMessageConsumer.consumeAndAck(destination, message，/*省略无关参数*/);
            }
          }
        } catch (Throwable cause) {
           //省略告警和打印日志
        }			
    }
}
```

1. 拉取binlog，cannal提供的Message对象封装了binlog原始数据，不过需要通过RowChange解析处理过以后才能被使用。
2. 处理binlog；

### 处理binlog

DRCCanalMessageConsumerImpl.consumeAndAck负责处理binlog，代码如下:

```java
public void consumeAndAck(String destination, Message message, CanalServerWithEmbedded embededCanalServer,ClientIdentity clientIdentity, long msgFetchTime) {
  //①
  Bag<Map<String, List<List<Entry>>>, Integer> bag;
  if (AppConfig.enableTransactionMerge) {
    bag = DRCMessageUtil.splitEntriesBySchema(message.getEntries());
  } else {
    bag = DRCMessageUtil.splitEntriesBySchemaAndTransaction(message.getEntries());
  }  
  //②
  if (0 != bag.getObject2() && AppConfig.enableConsumerWorkers) {
    consumeMessageInConsumerWorkers(destination, bag, msgFetchTime);
  } else if (0 != bag.getObject2()) {
    consumeMessageInSequential(destination, bag, msgFetchTime);
  }
  //③
  embededCanalServer.ack(clientIdentity, message.getId());
}
```



1. 按照数据库维度合并binlog ，即Map的key为数据库名，value为EntryList ，并跳过事务开始和结束；
2. 如果数据不为空且开启并行消费则多线程并行处理，默认会开 4 个 worker；
3. ack ;

并行处理方案如下，启动 n 个worker，每个worker包含一个队列和一个线程，通过hash来选择worker，这样能保证同一个数据库的顺序是一致的。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220824163624027.png" alt="image-20220824163624027" style="zoom:60%;" />

处理的过程主要是把原始的 Entry List 转化为DRCMessageList，然后把DRCMessageList 序列化成byte[] 用于发送到kafka，序列化逻辑在DRCMessageList.toByteArray( ) 方法中：

```java
 public byte[] toByteArray() throws IOException{
        // 记录drc-producer消息发送的时间, 可用于计算消息到drc-consumer, drc-streams的延迟
        long timestamp = System.currentTimeMillis(); 
        int size = messageDOs.size();
        ByteArrayOutputStream baos = new ByteArrayOutputStream(DRCMessageDO.BUFFER_SIZE * size);
        DataOutputStream dos = new DataOutputStream(baos);
        dos.writeLong(timestamp); //① 这一批binlog收到的时间
        dos.writeInt(size);//② binlog条数
        for (DRCMessageDO drcMessageDO : messageDOs) {
            byte[] data = drcMessageDO.toByteArray();
            dos.writeInt(data.length);//③单条binlog的长度
            dos.write(data);//单个数据
        }
        return baos.toByteArray();
    }
```

格式如下:

![image-20220825222616330](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220825222616330.png)

drcMessageDO.toByteArray() 是单条数据的序列化，代码如下:

```java
public byte[] toByteArray() throws IOException{
	    ByteArrayOutputStream baos = new ByteArrayOutputStream(BUFFER_SIZE);
	    DataOutputStream dos = new DataOutputStream(baos);
	    dos.writeInt(db.length());	//数据库的字符串长度
	    dos.write(db.getBytes());		//数据库的byte[]
	    dos.writeInt(table.length());//表的字符串长度
	    dos.write(table.getBytes()); //表的byte[]
	    dos.writeLong(executeTime); //binlog中记录的mysql表中数据变更的时间
	    dos.writeInt(eventType.getNumber()); //eventType
	    dos.writeInt(rowDatas.size());		//这条binlog包含的行数
	    for (RowData rowData : rowDatas) { 
            byte[] rowDataByte = rowData.toByteArray();//RowData是canal原生提供的对象
            dos.writeInt(rowDataByte.length);//rowData的byte[]长度
            dos.write(rowDataByte); //rowData的byte[]
        }
        return baos.toByteArray();
	}
```

### 发送消息

```java
//topic为配置的常量，db为key，value为byte[]
drcProducer.send(AppConfig.topic, db, value, sentry, drcMessageList.size());
```

drc-producer 发送到单个topic，在application.properties中配置drc.kafka.topic：

- 测试环境： drc.single.dms2
- 生产环境：
  - drc-producer-1~3：drc.sub
  - drc-producer-batch：drc.sub.batch


## drc-stream

### 开始

drc-stream 的入口是 BinlogExecutor，实现了 springboot 的 ApplicationListener接口，在应用启动之后执行execute方法，代码如下：

```java
public class BinlogExecutor implements ApplicationListener<ApplicationReadyEvent> {
 
  	public void execute() {
  	    while (true) {
      			//①消费原始binlog 
       			List<BatchBinlogDTO> batchBinlogDTOList = binlogConsumer.consume();
         		if (batchBinlogDTOList.isEmpty()) {
              continue;
            }
            for (BatchBinlogDTO batchBinlogDTO : batchBinlogDTOList) {
              	 for (BatchBinlogDTO.Binlog binlog : batchBinlogDTO.getBinlogList()) {
                   	  String db = binlog.getDb();
                   		String table = binlog.getTable();
                   		List<BatchBinlogDTO.Binlog.RowData> rowDataList = binlog.getRowDataList();
                   		//② 创建topic
                   		String topic = KafkaUtil.getOrCreateTopic(db, table);
                      int tableTailNum = DBTableDetectUtil.getTableTailNum(db, table);
                      for (int i = 0; i < rowDataList.size(); i++) {
                        //③转化binlog
                        HermesMessage hermesMessage = new ShadowMessageBuilder(topic, i, tableTailNum).build(binlog);
                        hermesMessageList.add(hermesMessage);
                      }

                 }
            }  
          //④发送 	
          try {
              binlogProducer.send(hermesMessageList);
              binlogConsumer.commit();
            } catch (Exception e) {
                    log.error("producer send or consumer commit error ", e);
            }     
           
    		}	    
    }
}
```

execute()方法包括了 4个步骤，基本就是drc-streams的整体框架：

1. 消费原始binlog；

2. 创建topic；
3. 转化binlog数据为hermesMessage；
5. 发送消息；

### 消费原始binlog

```java
public class BinlogConsumer {
     public List<BatchBinlogDTO> consume() {
       	List<BatchBinlogDTO> batchBinlogDTOList = new ArrayList<>();
       	while (isStart) {
          try {
          	//①
            ConsumerRecords<String, byte[]> records = consumer.poll(100);
            for (ConsumerRecord<String, byte[]> record : records) {
              	//②
              	batchBinlogDTOList.add(ParseUtil.parseBatchFrom(record.value()));
            }
          	break;
          }cache(Exception e){
            Thread.sleep(2000);
            AlertUtil.alert()
          }
        }
				batchBinlogDTOList
     }
}
```

主要流程：

1. 消费拿到 records；
2. 解析 value 转为BatchBinlogDTO，解析过程详见 drc-producer中写入kafak中的binlog数据结构；

BatchBinlogDTO 结构：

```java
@Data
public class BatchBinlogDTO {

    private long drcTimestamp;
    private List<Binlog> binlogList;
    private long partition;
    private long offset;
    @Data
    public static class Binlog {
        private String db;
        private String table;
        // 即binlog中记录的mysql表中数据变更的时间
        private long executeTime;
        private String eventType;
        private List<RowData> rowDataList;
        @Data
        public static class RowData {
            private List<Column> beforeColumns;
            private List<Column> afterColumns;
            private List<Pair> props;
        }
		}
}
```





### 创建topic

topic 命名风格按照："db." + db + "." + table，比如：db.finance_market.fund_manager_info；

KafkaUtil.getOrCreateTopic()会去掉分库中的数字后缀，保证分库之后的数据也能发送到同一个topic下

### 转化binlog

通过 ShadowMessageBuilder.builder()方法实现，代码如下：

```java
public final class ShadowMessageBuilder implements MessageBuilder<Binlog> {  
  
  public ShadowMessageBuilder(String topic, int index, int tableTailNum) {
        this.topic = topic;
        this.index = index;
        this.tableTailNum = tableTailNum;
  }
  
  public HermesMessage build(Binlog binlog) {
    	String eventType = binlog.getEventType();
    	//① 根据索引拿到row数据
    	Binlog.RowData rowData = binlog.getRowDataList().get(index);
    	//② 如果是Insert或者delete
    	if (INSERT_EVENT_TYPE.equals(eventType) || DELETE_EVENT_TYPE.equals(eventType)) {
       	//省略相关实现
      //③ 如果是Update转化成两条，先delete后insert 
      } else if (UPDATE_EVENT_TYPE.equals(eventType)) {
    		//省略相关实现      
  		}
    
    	return HermesMessage.builder()
                    .setTopic(topic)
                    .setKey(messageKey.getBytes())
                    .setData(value.toString().getBytes())
                    .setHeaders(buildMessageHeader(columns, null, nullFieldAfterList)).build();
  }
	
```

这里会通过 eventType 来转化binlog数据结构：

- 如果是insert，返回变更后的Columns数据；
- 如果是delete，返回变更后的Columns数据；
- 如果是update，转为一条delete + insert格式 (**注意不是delete和insert语句**)，目的是兼容以前大数据他们用的结构；

产生的格式为：

```
key:   binlogTime，currentTime，"event_type", "binlog_time", tbl_idx, 列名 
value: event_type，binlogTime，数据
```

- 其中 tbl_idx 表示分表的序号
- 数据是value数组，按001分割

比如 update 数据如下：

key:

```properties
1662566401000'\001'1662566401610'\001'event_type'\001'binlog_time'\001'id'\001'module_code'\001'sub_module_code'\001'cfg_key'\001'cfg_value'\001'name'\001'status'\001'version'\001'creator'\001'modifier'\001'created_time'\001'updated_time
```

value:

```properties
DELETE'\001'1662566401000'\001'235530'\001'ucenter'\001'moat'\001'refreshRulesCron'\001'0 0 0 * * ?'\001'refreshRulesCron'\001'1'\001'463'\001'duqu'\001'172.30.132.84'\001'2021-08-11 16:34:51'\001'2022-09-07 00:00:01'\002'INSERT'\001'1662566401000'\001'235530'\001'ucenter'\001'moat'\001'refreshRulesCron'\001'1 0 0 * * ?'\001'refreshRulesCron'\001'1'\001'464'\001'duqu'\001'172.30.132.235'\001'2021-08-11 16:34:51'\001'2022-09-08 00:00:01
```

header包含几个字段：

```
sqlType：BIGINTVARCHARBIGINT
hermes.sendTime：1669115033064
nullFieldAfter：extensionbook_idmidcurrencycategory
primaryKey：id
```

1. 字段类型
2. hermes发送时间
3. 更新之后空字段，这些字段会自动设置为null
4. 主键id

### 发送消息

详见 BinlogProducer，做了一个优化，按照topic维度并行发送。



## drc-consumer

Drc-consumer 用于双活业务的mysql同步支持，相比drc-sink增加了冲突检测等机制更加复杂。

### 开始

springboot启动之后初始化 SyncExecutor

```java
    @Override
    public void run(String... strings) throws Exception {
        for (String topic : appConfig.getTopic().split(",")) {
            SyncExecutor syncExecutor = SpringUtil.getBean(SyncExecutor.class);
            new Thread(() -> syncExecutor.execute(topic)).start();
        }
    }
}
```

SyncExecutor代码如下：

```java
@Component
@Scope("prototype")
@Slf4j
public class SyncExecutor implements ApplicationListener<ApplicationReadyEvent> {  	
		
  	public void execute(String topic) {
      	List<BatchBinlogDTO> batchBinlogDTOList = syncConsumer.consume();
      	List<SQLInfo> sqlList = new ArrayList<>();
        //②
        for (BatchBinlogDTO batchBinlogDTO : batchBinlogDTOList) {
					sqlList.addAll(ParseUtil.convert2SQLList(kafkaReceiveTime, batchBinlogDTO));
        }
      	//③
      	long batchNum = ((Double) Math.ceil((double) sqlList.size() / appConfig.getSendThreadNum())).longValue();
        for (int i = 0; i < batchNum; i++) {
          if (i != batchNum - 1) {
            Map<String, List<Object>> sqlGroupMap = GroupStrategyUtil.getSqlGroupByKey(sqlList.subList(i * appConfig.getSendThreadNum(), (i + 1) * appConfig.getSendThreadNum()));
            syncCobarDAO.execute(sqlGroupMap);
          } else {
            Map<String, List<Object>> sqlGroupMap = GroupStrategyUtil.getSqlGroupByKey(sqlList.subList(i * appConfig.getSendThreadNum(), sqlList.size()));
            syncCobarDAO.execute(sqlGroupMap);
          }
        }
    }
  
  	@Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        //①
      	String informationSchemaJdbcUrl = "jdbc:mysql://" + jdbcUrl.split("@")[0] + "/information_schema";
      	String bizSchema = jdbcUrl.split("@")[1];
      	MysqlUKCache mysqlUKCache = new MysqlUKCache(informationSchemaJdbcUrl,
                                                     appConfig.getMysqlUser(), appConfig.getMysqlPassword(), bizSchema);
      	String db = DBTableDetectUtil.trim(bizSchema);
      	mysqlUKCache.init();
      	MysqlUKCache.setInstanceByDB(db, mysqlUKCache);
    }
}
```

主要流程：

1. 应用启动时查询mysql获取库唯一索引信息并缓存起来；
2. binlog解析成SQLInfo，并保存一些元数据信息以及内存屏障;(细节见binlog解析)
3. 将binlog按线程池大小划分，200条sql一批，防止出现正在执行的sql等待线程池外排队的sql；

这里mysql实例的密码是通过admin控制台写入到zk的，admin中的密码保存在application.properties中



## drc-sinker

用于mysql数据同步，消费表级别的binlog数据，同步到其他mysql实例的库表，目前只有记账在用，记账使用了drc_sink用于同步数据，因为记账使用corbar进行了分库，写入的目标地址是corbar地址，corbar再路由到mysql分片，过程如下：

```
drc_sink -> corbar -> [mysql实例1, mysql实例2,...]
```

### 核心逻辑

核心逻辑抽取到了mars-client，主要流程如下：

1. KafkaDataSubscriber 消费表级别的数据，也就是drc-stream产生的数据，会按照不同的eventType解析数据，封装成RowData，**delete 和 insert 只有afterColumns，update 有 beforeColumns 和 afterColumns**

2. MarsClientImpl.execute()；先调用Handler做预处理，这里可以做数据过滤

3. 调用MysqlSyncDAO.persistence()持久化，这里会通过不同的eventType调用InsertConvert，UpdateConvert，DeleteConvert 产生sql语句。




### DeleteConvert

需要构建 delete from table where 条件，迭代afterColumns，如果字段是key作为where条件加上，**如果存在cobar分区字段也需要加上**，代码如下：

```java
StringBuilder sb = new StringBuilder();
sb.append("delete from ").append(db).append(".").append(table).append(" where ");  
for (RowData.Column column : afterColumns) {
    //如果是key
    if (column.isKey()) {
      sql.append(column.getName()).append("='").append(column.getValue()).append("'  and ");
      keyColumnList.add(column);
    }
    //如果存在分区字段，则填入sql中，cobar才能正常路由
    if (column.getName().equals(partitionField)) {
    	sql.append("`").append(column.getName()).append("`='").append(column.getValue()).append("'  and ");
    }
}
```

### InsertConvert

InsertConvert 比较简单，拼接字段名和字段value就可以了。主键id也会同步过去。

### UpdateConvert

UpdateConvert 首先会根据配置决定使用 replace into还是update（两者区别自行搜索），对于update 需要构建形如 update table set ... where ... 的SQL 语句，set 加入所有非key字段，where条件带上key和cobar分区字段，代码如下：

```java
for (RowData.Column column : afterColumns) {
  //① 如果非key字段
  if (!column.isKey()) {
    if (column.getName().equals(partitionField)) {//②如果是分区字段留着，后面要加入到条件
      partitionColumn = column;
    } else {//③ 既不是分区字段也不是key字段的加入set
      String field = (String) column.getName();
      if (!column.isNull()) {
        columnValueList.add(column.getValue());
      } else {
        columnValueList.add(null);
      }
      sql.append("`").append(field).append("`=?");
      if (index != afterColumns.size() - 1 - 1) {
        sql.append(", ");
      }
    }
    index++;
  } else {
    keyColumnList.add(column);
  }
}
//省略后面where条件
```

### 持久化

通过前面步骤得到一批SQLInfo进行持久化，MysqlSyncDAO 代码如下：

```java
public void persistence(DataContext dataContext) {
        List<SQLInfo> sqlInfoList = new ArrayList<>();
        if (marsConfig.isSyncParallel()) {
            //1. 根据pk hash后合并sql
            Map<String, List<SQLInfo>> mergeSqlMap = mergeSql(sqlInfoList);
            Map<String, SQLInfo> deleteSqlMap = new HashMap<>();
            Map<String, SQLInfo> upsertSqlMap = new HashMap<>();

            for (Map.Entry<String, List<SQLInfo>> entry : mergeSqlMap.entrySet()) {
                for (SQLInfo sqlInfo : entry.getValue()) {
                    if (EventType.DELETE.equals(sqlInfo.getEventType())) {
                        deleteSqlMap.put(entry.getKey(), sqlInfo);
                    } else {
                        upsertSqlMap.put(entry.getKey(), sqlInfo);
                    }
                }
            }

            //2. 优先执行delete
            if (!deleteSqlMap.isEmpty()) {
                syncParallel(deleteSqlMap, true);
            }

            //3. 然后执行insert/update
            if (!upsertSqlMap.isEmpty()) {
                syncParallel(upsertSqlMap, false);
            }
            //4. 出现uk冲突无法处理时，改为串行模式
            if (isUKConflict) {
                syncSerial(sqlInfoList);
            }
            isUKConflict = false;
        } else {
            //串行执行
            syncSerial(sqlInfoList);
        }
    }
```

1、这里先合并SQL。主要用于合并同一主键的多个 SQL 操作。这里举个例子：比如对**同一条数据**做了三次update，明显只需要执行最后一次udpate，又比如对一条数据先insert、再update、再delete，明显只需要执行最后一次delete，代码如下：

```java
private Map<String, List<SQLInfo>> mergeSql(List<SQLInfo> sqlInfoList) {
        Map<String, List<SQLInfo>> sqlInfoMap = new HashMap<>();
        for (SQLInfo sqlInfo : sqlInfoList) {
            if (sqlInfoMap.containsKey(sqlInfo.getKeyValue())) {
                List<SQLInfo> keySqlInfoList = sqlInfoMap.get(sqlInfo.getKeyValue());
                SQLInfo lastSqlInfo = keySqlInfoList.get(keySqlInfoList.size() - 1);
                switch (lastSqlInfo.getEventType()) {
                    case INSERT:// 上一次操作是Insert
                        switch (sqlInfo.getEventType()) {
                            case UPDATE:// 如果当前是update
                                sqlInfo.getRowData().setEventType(EventType.INSERT);
                                SQLInfo mergeSqlInfo = SqlConvertFactory
                                        .getSqlConvert(EventType.INSERT, sqlInfo.getSinkTask())
                                        .convert(sqlInfo.getRowData());
                                keySqlInfoList.remove(lastSqlInfo);
                                keySqlInfoList.add(mergeSqlInfo); // 把insert+update 合并成一条insert，这里insert使用新的值
                                break;
                            case DELETE:// 如果当前是delete
                                keySqlInfoList.remove(lastSqlInfo); // 把insert+delete合并为delete
                                keySqlInfoList.add(sqlInfo);
                                break;
                        }
                        break;
                    case UPDATE:// 上一次操作是update，删除旧 UPDATE，保留新操作
                        keySqlInfoList.remove(lastSqlInfo);
                        keySqlInfoList.add(sqlInfo);
                        break;
                    case DELETE: // 上一次操作是delete，保留 DELETE 并添加新操作
                        keySqlInfoList.add(sqlInfo);
                        break;
                }
            } else {
                List<SQLInfo> temp = new ArrayList<>();
                temp.add(sqlInfo);
                sqlInfoMap.put(sqlInfo.getKeyValue(), temp);
            }
        }
        return sqlInfoMap;
    }
```

2、分为deleteSql和upsertSql，先执行deleteSql 在执行insert和update，原因是如果直接执行 `INSERT`，而记录已经存在，会引发主键冲突错误。通过先 `DELETE`，然后再 `INSERT` 或 `UPDATE`，可以避免此类错误，让同步流程更加顺畅。



其他注意事项：

- MysqlSyncDAO 通过 jdbc初始化，如果分库分表地址是cobar



## drc-sinker-ucenter

这个项目是在 drc-sinker 之上开发的，主要用户中心有一些变态的需求，而drc-sinker暂时不好满足，所以重新创建了一个新的项目：

1.  基于application.properteis 配置sink-task，而不是界面管理的方式，原因是加了一些配置，而修改界面要修改对应的数据库，比较麻烦
2. 支持双向同步，通过cell_id避免循环复制
3. 支持两张表字段不相同，同步只写入两张表交集的字段，比如 A表字段: id, age, email。B表字段: id, age, tenant_id。 A->B，只会写入id,age, 忽略 email 字段； B->A，只会写入id,age, 忽略 tenant_id 字段
4. 支持字段别名，比如A表中叫nick_name，B表叫user_name
5. 优雅停机，避免极端情况下数据写入失败但kafka点位提交，drc-sinker在停机这块非常简单
6. 复用数据源，drc-sinker每个任务都会创建一个数据源，本项目多个任务会共享同一个数据源



### 架构

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241223152349118.png" alt="image-20241223152349118" style="zoom:50%;" />

这里Filter实现是单例的，最开始Filter被设计为多例，但在Spring容器下多例听奇怪的，后来改成单例

### 数据一致性

虽然双活项目中 drc-consumer 基于 drc_check_time来保障数据一致性，但仔细分析这种方式其实并不能完全解决数据一致性问题，它只能部分解决数据覆盖的问题。

比如drc-sinker在执行delete和update操作，会自动把 drc_check_time 作为查询条件：

```
  update user set emial='<EMAIL>' where uid=1001 and updated_time<='2024-12-13 17:17'
```

如果对方在这一秒修改了这条数据那 drc_check_time 会比当前高，则这条 update不会生效，那么drc-sinker就不会覆盖用户修改的数据。

但是这种方式无法解决脏读的问题：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241223152804796.png" alt="image-20241223152804796" style="zoom:50%;" />



### 避免循环复制

基于cell_id 避免循环复制的方案

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241223152829691.png" alt="image-20241223152829691" style="zoom:50%;" />

但是这种方案也并不完美，比如user_sharding同步了一条数据到 user_shading_tenant，user_shading_tenant 删除了这条数据，如果想把这条数据同步回user_sharding，那必须先修改cell_id为2

## 问题记录

### 1、启动之后没有数据

出现错误日志:

```
com.alibaba.otter.canal.parse.exception.CanalParseException: can't read binlog because a drc instance read the binlog right now, process id:638758, process host:*************:50570, masterInfo:com.alibaba.otter.canal.parse.support.AuthenticationInfo@5e3c7d1f[address=/************:3310,username=drc,password=d77edef36384cf38f8e67ca8c91afffa,defaultDatabaseName=<null>]
```

>  drc-producer在启动之后会做这么一个检查：是否已经存在drc用户在进行binlog订阅，如果存在则抛出这样的异常，也就是一个mysql实例针对同一个用户只能订阅一个binlog。
>
> 增加这个检查的原因：根据无我描述：作为一个兜底策略，为了解决之前zookeeper抖动造成的多主问题，避免都在消费binlog。

我本地为了测试，只能临时将 MysqlEventParser.checkDRC()方法的逻辑注释掉。



### 2、 为什么update变成了delete+insert？

消费drc-stream产生的topic，发现修改jasmine的配置，收到的不是update，而是两条事件：一条delete，一条insert。问了千鸟，回答是为了兼容以前大数据用的结构。

注意这里并非是delete和insert SQL语句，只是一个event标记下，后面跟的还是变更前和变更后的数据，所以其实叫after和before也行。



### 3、为什么要采用两级kafka订阅binlog？

在架构可以了解到，drc采用两级kafka，第一级固定topic，以db作为key发送，第二级以数据库+表名作为topic发送。

为什么要采用两级kafka？直接把库名+表名作为topic发送不可以吗？

答案确实是不可以，**原因是要保证库级别的事务顺序**。如何理解？ 首先要明确binlog是按照事务来传输数据，数据结构类似这样：

> 事务开始； DDL1; DDL2; DDL3；事务结束

考虑一个业务场景：先insert A表然后基于A表的数据update B表，如果这个顺序颠倒过来会导致数据错误，所以要保证库级别下事务顺序。那需要保证多个库之间事务顺序吗？目前看来并不需要，因为多个库要么是分库，要么是不相关的库，且mysql本来就不支持跨数据库的事务。所以第一级是保证了db级别的事务顺序，第二级虽然是库+表，但数据是我们自己发送的可以控制顺序。

### 4、drc_check_time 的作用？

在双活中需要在业务的表中添加 drc_check_time 字段，这个字段的作用是什么？

考虑双活切换的流程，比如我们有青山湖和下沙两个机房，正常情况下两个机房双向复制，下图所示：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220823112457991.png" alt="image-20220823112457991" style="zoom:60%;" />

t1时刻qsh出现故障，用户被切入到下沙机房，binlog复制中断。在t3时刻恢复时，对于同一条数据可以会发生user先修改，后收到xs机房发来的binlog，**这就需要把 drc_check_time 加到sql上作为条件**，防止流量切换时导致老数据覆盖新数据。

```
update table set field where drc_check_time <=?
```

drc_check_time 主要为了解决数据同步一致性问题，应问题除了 drc_check_time 还有：

- 唯一键数据冲突，详见wacai双活方案DRC一节；
- 表结构变更同步，详见[TSDB](https://github.com/alibaba/canal/wiki/TableMetaTSDB)；

### 5、heartbeat表的作用？

cannal 支持通过心跳检查来实现自动HA，对于直接从mysql slave同步不需要开启心跳，对于从mysql master同步日志的需要开启心跳

用法：

```java
if (AppConfig.enableDetect) {
    parameter.setDetectingEnable(true);
    parameter.setDetectingIntervalInSeconds(10);
    parameter.setDetectingRetryTimes(3);
    parameter.setHeartbeatHaEnable(true);
    parameter.setDetectingSQL(AppConfig.detectSql);
}
```

开启心跳需要新建一张 heartbeat.drc_detect 表:

```sql
CREATE SCHEMA `heartbeat` DEFAULT CHARACTER SET utf8;
CREATE TABLE `heartbeat`.`drc_detect` (
`id` INT NOT NULL AUTO_INCREMENT,
`status` TINYINT(4) NOT NULL,
PRIMARY KEY (`id`));

```

检测sql:

```
update heartbeat.drc_detect set status=0 where id=1
```

实现原理：

- MysqlEventParser 会启动一个MysqlDetectingTimeTask 用于不停的发送detectSql；
- 当sql执行失败调用HeartBeatHAController.onFailed()，超过失败次数会触发主从切换。
- 主从切换逻辑在 MysqlEventParser的doSwitch()方法中实现。

### 6、订阅延迟如何处理？

- 先确认 是否是 dba或者业务大批量删数据或者归档造成的，如果是则进入下一步，如不是，先找对应库表的业务方确认再评估
- 确认下游是否需要订阅，如仍需要这些数据则下一步，如不需要，则在上游producer端直接过滤掉该表的数据
- 将实例binlog 迁移至batch topic中 或者 新建单独的topic

### 7、报错：java.io.IOException: Received error packet: errno = 1236, sqlstate = HY000 errmsg = Could not find first log file name in binary log index file

日志打印:

```
2022-12-07 19:29:51,917 WARN   c.a.o.c.p.i.m.MysqlEventParser:437 - [] [] [][bds=] prepare to find start position mysql-bin.000011:19112103:
2022-12-07 19:29:51,923 ERROR [destination = drc-daily-************] c.a.o.c.p.i.m.d.DirectLogFetcher:144 - [] [] [][bds=] I/O error while reading from client socket
java.io.IOException: Received error packet: errno = 1236, sqlstate = HY000 errmsg = Could not find first log file name in binary log index file
    at com.alibaba.otter.canal.parse.inbound.mysql.dbsync.DirectLogFetcher.fetch(DirectLogFetcher.java:95)
    at com.alibaba.otter.canal.parse.inbound.mysql.MysqlConnection.dump(MysqlConnection.java:122)
    at com.alibaba.otter.canal.parse.inbound.AbstractEventParser$3.run(AbstractEventParser.java:219)
    at java.lang.Thread.run(Thread.java:748)
```

这个binlog file 在master不存在，原因 drc初始化时会先从task中拿binlog配置，这个数据只在第一次有效，代码CanalInstanceServiceL70

解决方案，删除zk配置详见SubscribeTaskDAO.deleteCursor()

### 8、如何解决循环同步的问题？即避免 A 数据库同步到 B 数据库的数据再次被同步回 A 数据库。

所有表都包含cell_id字段，A数据库的cell_id为1，B数据库的cell_id为2，同步前过滤一下，如图：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241121172254767.png" alt="image-20241121172254767" style="zoom:50%;" />

### 9、记账团队两张表同步失败问题

记账团队两张表同步失败导致drc-sinker消息堆积。具体原因是目标表字段not null，原表字段可以为空导致同步失败。MysqlSyncDAO处理失败会无限次重试。解决方案是增加一个配置，写入的时候排除掉not null的字段



### 10、drc-consumer双写数据一致性

目前通过时间或者版本号来保证，比如drc-sinker在执行delete和update操作，会自动添加 updated_time 条件：

```
  update user set emial='<EMAIL>' where uid=1001 and updated_time<='2024-12-13 17:17'
```

如果对方在这一秒修改了这条数据那updated_time会比当前高，则这条 update不会生效，那么drc-sinker就不会覆盖用户修改的数据。

但是只实现了最终一致，不满足事务和隔离级别

比如下面这种情况:

t0时刻，原始数据：

```
uid=1, city=hangzhou, tel=110, version=1 
```

t1时刻，A 机房更新数据

```
uid=1, city=hangzhou, tel=120, version=1 
```

这时候出现drc网络延迟，t2时刻B机房更新数据：

```
 uid=1, city=shanghai, tel=110, verison=2 
```

**可以看到B机房已经出现了脏读**，tel不是最新的数据，就算drc没有覆盖机房B的数据，这个时候数据已经不一致。

### 11、测试环境drc-producer延迟高

可以通过8864查看是哪个key(数据库)的写入量比较大，联系业务方是否是在压测

## 其他

### binlog采集接入操作流程

基本流程：

* 当业务有某个库需要采集binlog时，先提供库名给DBA，@无影 @紫灵芝，给出对应的mysql实例的master和slave地址信息;
* 再根据master和slave信息到drc副本集配置上查下是否该实例已经被drc采集过;
* 若已经被采集，则直接修改对应采集任务的黑白名单即可，把需要采集的库表配到白名单上;
* 若还没采集任务，则**先检查mysql主从实例上是否已经创建drc用户及对应的heartbeat库**，然后进行新增订阅任务;
* 完成后，会在zk上创建对应的节点，drc-producer已经watch对应的节点，会收到通知，开始处理这个订阅任务;



### 分库分表

两个数据库 loan_sharding002， loan_sharding001，表结构：

```sql
CREATE TABLE `contract_agreement` (
  `id` int(10) unsigned NOT NULL COMMENT '流水号',
  `uid` int(10) unsigned NOT NULL COMMENT '用户编号',
  `biz_type` varchar(20) NOT NULL COMMENT '业务类型AID,UID,MK',
  `biz_id` varchar(30) NOT NULL COMMENT '业务编号',
  `product` varchar(20) NOT NULL COMMENT '产品名称：bt,sloc,consumer,boss等',
  `sign_time` datetime DEFAULT NULL COMMENT '签署时间',
  `end_time` datetime DEFAULT NULL COMMENT '结束时间',
  `agreement_name` varchar(32) NOT NULL DEFAULT '' COMMENT '协议名称',
  `agreement_no` varchar(32) NOT NULL DEFAULT '' COMMENT '协议编号',
  `agreement_version` varchar(10) NOT NULL DEFAULT '' COMMENT '协议版本号',
  `status` tinyint(3) unsigned NOT NULL COMMENT '协议状态',
  `pdf_group_name` varchar(45) DEFAULT NULL COMMENT '文件系统组名',
  `pdf_path` varchar(255) DEFAULT NULL COMMENT '文件系统路径',
  `html_group_name` varchar(45) DEFAULT NULL COMMENT '文件系统组名',
  `html_path` varchar(255) DEFAULT NULL COMMENT '文件系统路径',
  `realname` varchar(50) DEFAULT NULL COMMENT '姓名',
  `mobile` varchar(30) DEFAULT NULL COMMENT '手机号',
  `idno` varchar(32) DEFAULT NULL COMMENT '身份证号',
  `amount` int(10) unsigned DEFAULT NULL COMMENT '金额',
  `fund_code` varchar(20) DEFAULT NULL COMMENT '资金方代码',
  `contract_type_code` varchar(50) DEFAULT NULL COMMENT '合同类型标识码',
  `sign_status` int(11) DEFAULT '0' COMMENT '0:默认 1:初始化 2:已推送 3:合同签章成功 4:合同签章失败 5:合同下载完成',
  `agreement_type` varchar(20) DEFAULT NULL COMMENT '协议类型 借款合同，隐私协议，委托代扣协议等',
  `template_code` varchar(50) DEFAULT NULL COMMENT '模板编号',
  `template_version` int(11) DEFAULT NULL COMMENT '模板版本',
  `show_status` tinyint(2) DEFAULT '0' COMMENT '是否展示 0:否 1:是',
  `async_status` tinyint(2) DEFAULT '0' COMMENT '是否异步生成 0:否 1:是',
  `content_bizId` varchar(45) DEFAULT NULL COMMENT '合同基础服务ID',
  `createdby` varchar(30) DEFAULT NULL COMMENT '创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updatedby` varchar(30) DEFAULT NULL COMMENT '修改人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '修改时间',
  `remark` varchar(256) DEFAULT NULL COMMENT '备注',
  `oid` bigint(20) DEFAULT NULL COMMENT '关联编号',
  `type_id` int(10) DEFAULT NULL COMMENT '合同类型id',
  PRIMARY KEY (`id`),
  KEY `idx_uid` (`uid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同业务协议表-uid维度分库'


CREATE TABLE `cnt_contract_detail` (
  `id` bigint(20) unsigned NOT NULL COMMENT '主键',
  `created_by` varchar(20) NOT NULL COMMENT '创建人',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_by` varchar(20) NOT NULL COMMENT '更新人',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `version` int(11) NOT NULL COMMENT '版本号',
  `contract_id` bigint(20) NOT NULL COMMENT '合同号',
  `dis_filename` varchar(50) DEFAULT NULL COMMENT '分布式存储上的文件名',
  `dis_result` varchar(50) DEFAULT NULL COMMENT '分布式存储结果',
  `succ_dis_time` datetime DEFAULT NULL COMMENT '分布式存储的成功时间',
  `succ_tcs_time` datetime DEFAULT NULL COMMENT '成功三方存证的时间',
  `pdf_filename` varchar(50) DEFAULT NULL COMMENT 'pdf文件名',
  `pdf_dis_result` varchar(50) DEFAULT NULL COMMENT 'pdf文件存储结果',
  `is_tcs` tinyint(1) DEFAULT NULL COMMENT '是否三方存证',
  `sign_position_specified_type` varchar(20) DEFAULT NULL COMMENT '签署位置指定方式：接口传输还是模板类型指定',
  `actual_params` varchar(600) DEFAULT NULL COMMENT '实际签署参数',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_contract_id` (`contract_id`),
  KEY `idx_created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='合同详情表-contract_id维度分库'
```

### sql语句

```sql
-- drc用户不能拥有super权限, 因为drc需要依赖mysql的read-only做切换, 但是read-only对super权限无效
CREATE USER drc IDENTIFIED BY 'd77edef36384cf38f8e67ca8c91afffa';
GRANT SELECT, UPDATE, INSERT, DELETE, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'drc'@'%';
FLUSH PRIVILEGES; 

-- 用户数据同步的专有用户(主要用于测试环境)
CREATE USER drcsync IDENTIFIED BY 'd77edef36384cf38f8e67ca8c91afffa';
GRANT SELECT, UPDATE, REPLICATION SLAVE, REPLICATION CLIENT ON *.* TO 'drcsync'@'%';
FLUSH PRIVILEGES; 

mysql -h 10.1.96.18 -P 3313 -u drc -pd77edef36384cf38f8e67ca8c91afffa
```



## 参考

- [canal简介](https://github.com/alibaba/canal/wiki/%E7%AE%80%E4%BB%8B)
- [QuickStart](https://github.com/alibaba/canal/wiki/QuickStart)
- [canal server配置手册](https://github.com/alibaba/canal/wiki/AdminGuide)
- [cobar](https://developer.aliyun.com/article/304907)
