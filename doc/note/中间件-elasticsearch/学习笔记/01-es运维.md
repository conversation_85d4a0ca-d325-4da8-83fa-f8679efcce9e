## ES环境列表

### 测试

#### 1、集群：middleware-trace-test(测试公共集群)

| 角色           | 节点                                                    | 备注                          |
| :------------- | :------------------------------------------------------ | :---------------------------- |
| master         | *************，*************                            | http端口：9200，tcp端口：9300 |
| client         | *************，*************                            | http端口：9200，tcp端口：9300 |
| data           | *************，*************                            | http端口：9201，tcp端口：9301 |
| legion-gateway | legion-gateway.middleware.wse.test.wacai.info           | http端口:80                   |
| legion-admin   | http://middleware-web.middleware.wse.test.wacai.info/es | path: /es, 为配置管理入口     |
| kibana         | http://*************:5601/app/kibana                    |                               |



#### 2、集群：wacaies5 （日志集群）

| 角色   | 节点                                         | 备注                          |
| :----- | :------------------------------------------- | :---------------------------- |
| master | *************,  *************, ************* | http端口：9200，tcp端口：9300 |
| client | *************,  *************, ************* | http端口：9200，tcp端口：9300 |
| data   | *************,  *************, ************* | tcp端口：9300                 |
| kibana | http://*************:5601/app/kibana         |                               |



### 生产-挖财主站

#### 主集群：middleware-es （中间件业务集群,v6.8）

| 角色           | 节点                                                         | 备注                          |
| :------------- | :----------------------------------------------------------- | :---------------------------- |
| master         | **********3 *********** **********5                          | http端口：9200，tcp端口：9300 |
| client         | **********3 *********** **********5                          | http端口：9201，tcp端口：9301 |
| data           | **********3 *********** **********5，********** 10.1.96.50 10.1.96.51 | 关闭http服务，tcp端口：9302   |
| legion-gateway | 10.1.19.75, 10.1.19.76, 10.1.19.77, 10.1.19.78, 10.1.20.48,10.1.20.49, 10.1.20.50, 10.1.20.51 | http端口:8080                 |
| legion-admin   | http://middleware.wacai.info/es 、http://middleware.wacai.info/etl | path: /es, 为配置管理入口     |
| kibana         | http://kibana.middleware.wacai.info/                         |                               |

#### 主集群：wacaiqshes （挖财日志集群,v6.8）

| 角色         | 节点                                                         | 备注                        |
| :----------- | :----------------------------------------------------------- | :-------------------------- |
| master       | **********:9201,**********:9201,**********9:9201             |                             |
| client       | **********0:9201,**********:9201,***********:9201,***********:9201 |                             |
| data         | ***********:9201,***********:9200,**********:9200,**********0:9200, **********:9200,**********:9200,**********9:9200,***********:9200, ***********:9200,***********:9201,***********:9200 | 关闭http服务，tcp端口：9302 |
| legion-admin | http://middleware.wacai.info/es                              | path: /es, 为配置管理入口   |
| kibana       | http://logs.wacai.info/app/kibana                            |                             |

#### 主集群：wacai-es-common （运维老集群，只剩几个老业务在使用,v5.3）

| 角色         | 节点                                | 备注                        |
| :----------- | :---------------------------------- | :-------------------------- |
| master       | 10.1.136.52,10.1.136.53             | http端口9200                |
| client       | 10.1.136.52,10.1.136.53             | http端口9201                |
| data         | 10.1.136.52,10.1.136.53             | 关闭http服务，tcp端口：9301 |
| legion-admin | middleware.wacai.info/es 控制台页面 | path: /es, 为配置管理入口   |
| kibana       | http://kibana.common.wacai.info/    |                             |

#### ~~主集群：stanlee-de-es （大数据使用集群，财商业务也在里面,v6.8）~~

| 角色           | 节点                                    | 备注                        |
| :------------- | :-------------------------------------- | :-------------------------- |
| master         | 10.1.144.47,10.1.144.48,10.1.144.49     | http端口9200                |
| client         | 10.1.144.47,10.1.144.48,10.1.144.49     | http端口9201                |
| data           | 10.1.144.47,10.1.144.48,10.1.144.49     | 关闭http服务，tcp端口：9301 |
| legion-gateway | legion-gateway.middleware.k2.wacai.info | http端口:80                 |
| legion-admin   | middleware.wacai.info/es 控制台页面     | path: /es, 为配置管理入口   |
| kibana         | http://kibana.stanlee-de.wacai.info     |                             |

### AMX-望润

#### 主集群：wramc-common （望润公共集群,v6.8）

| 角色    | 节点                                                         | 备注                         |
| :------ | :----------------------------------------------------------- | :--------------------------- |
| master  | **********,10.21.32.2,10.21.32.3                             | http端口9200                 |
| client  | 10.20.32.1:9201,10.20.32.2:9201,10.20.32.3:9201              | http端口9201                 |
| data    | **********,10.21.32.2,10.21.32.3                             | 关闭http服务，tcp端口：9301  |
| kibana  | http://kibana.common.wramc.info/app/kibana#/home?_g=()       | 用户名/密码：elastic/esadmin |
| grafana | http://grafana.wramc.info/d/ioJ6EFP7k/jie-dian-jian-kong?orgId=1&from=now-6h&to=now |                              |

### AMX-挖财

#### 主集群：xamc-common （xamc公共集群,v6.8）

| 角色   | 节点                                           | 备注                        |
| :----- | :--------------------------------------------- | :-------------------------- |
| master | 10.20.32.1,10.20.32.2,10.20.32.3               | http端口9200                |
| client | 10.20.32.1,10.20.32.2,10.20.32.3               | http端口9201                |
| data   | 10.20.32.1,10.20.32.2,10.20.32.3               | 关闭http服务，tcp端口：9301 |
| kibana | http://kibana.xamc.info/app/kibana#/home?_g=() |                             |



### ES维护

#### 集群中某个节点load特别高

问题 ： 集群中某个节点load特别高，导致那个问题节点上的读写都会超时，同时可能会引起该节点和master之间心跳无法响应，被master踢出集群

导致load特别高的原因主要有2类：

1.  cpu使用率过大，一般是存在大量的写入操作，通过kibana监控可以看出那个索引写入量过高，跟该索引的业务方确认，可以在legion-gateway上把限流阈值调低
2.  磁盘 io util过大，一般是存在大量的读写操作，这里可以通过iostat 来查看读写的qps以及流量，再结合kibana监控和慢查日志，来确认是哪个索引的问题

#### 集群中某个节点挂了，查看日志发现是oom

- 优先重启节点，避免业务受影响，如果hang住了可以kill -9节点并重启
- 如果挂掉的是client节点，那基本是写入量过大，进程内存不够导致的，可以多扩些内存
- 如果挂的是data节点，那可能是写入量过大或者出现大查询，一般data节点给的内存会比较大，所以可以先查看慢查和慢写日志来定位原因
- 这种情况引起的原因一般是出现了一些大查询，消耗大量的内存，这个可以在慢查日志中发现

#### 日志收集

- 对于私有化环境，蔚然只需要es集群地址即可收集日志(如果开启了密码验证需要用户密码，参考上面用户名密码设置小节)，蔚然会去 Kibana配置pipleline;
- 对于挖财云环境，需要依赖legion的ETL;
- 有一个日志清理脚本需要定期执行，在 **********机器上 /data/program/es_ops/下面，通过crontab -l

基金日志 (模板名:w-fund) 需要保留 6 个月，是通过上述脚本单独清理，脚本会把过期索引关闭掉，指令：

```
POST  w-wac-finance-trinity-web-2021-11-09/_close
```

可以通过指令打开

```
POST  w-wac-finance-trinity-web-2021-11-09/_open
```



## 启动

执行

```
./bin/elasticsearch -d
```

### 环境问题

es 对于单机和集群采用不同的错误提示方式，一些问题在单机下只是打印警告日志，但是在集群下会启动失败，这点需要注意，比如：

```
[2022-11-04T14:46:58,953][WARN ][o.e.b.BootstrapChecks    ] [node1] granting the all permission effectively disables security
```

### kibana

配置

kibana.yml 文件增加如下内容：

```yaml
server.host: "0.0.0.0"
kibana.index: ".kibana"
elasticsearch.hosts: ["http://*************:9200"]
```

启动

命令：

```
./bin/kibana > logs/kibana.log 2>&1 &
```

开机自动启动脚本kibana_reboot：

```bash
#!/bin/bash
BASEDIR=`dirname $0`
BASEDIR=`(cd "$BASEDIR"; cd ../; pwd)`

pid_num=`ps aux|grep node|grep -v grep|wc -l`
if [ $pid_num -le 0 ] ;then
    sh /data/program/kibana/bin/kibana > /data/program/kibana/logs/kibana.log 2>&1 &
    echo "kibana reboot"
fi

echo "check normal"

```

### 监控

推荐使用[elasticsearch-metrics](https://grafana.com/grafana/dashboards/14191-elasticsearch-overview/) grafana大盘

prometheus 监控参考 [prometheus elasticsearch exporter](https://medium.com/rahasak/monitor-elasticsearch-with-prometheus-and-grafana-687a0b6712)

- ./elasticsearch_exporter --es.uri=http://*************:9200 --es.all
- grafana 使用 [14191](https://grafana.com/grafana/dashboards/14191-elasticsearch-overview/)

jmx监控 

### 7.11版本

配置文件:

```yaml
cluster.name: wacaies5
node.name: *************
node.attr.rack: r1
node.master: true
node.data: true
node.ingest: true

path.data: /data/program/es/data
node.max_local_storage_nodes: 3
indices.memory.index_buffer_size: 15%
transport.tcp.compress: true
http.compression: true
indices.queries.cache.size: 20%
cluster.routing.allocation.same_shard.host: true
bootstrap.memory_lock: true
bootstrap.system_call_filter: false
gateway.recover_after_nodes: 1
cluster.routing.allocation.node_initial_primaries_recoveries: 40
network.host: 0.0.0.0
#必须要配置，es默认单个节点1000个shard，很容易超过限制
cluster.max_shards_per_node: 5000

discovery.seed_hosts: ["***********", "*************"]
cluster.initial_master_nodes: ["***********", "*************"]

xpack.monitoring.enabled: true
xpack.monitoring.collection.enabled: true

http.cors.enabled: true
http.cors.allow-origin: "*"
http.cors.allow-headers: Authorization
xpack.security.enabled: false
xpack.ml.enabled: false
```



###  8.4版本

配置文件:

```yaml
cluster.name: quantum
node.name: ${HOSTNAME}
node.attr.rack: r1
node.roles: ["data","master"]
transport.compress: true
http.compression: true
discovery.seed_hosts: ["*************","*************"]
cluster.initial_master_nodes: ["*************", "*************"]

indices.memory.index_buffer_size: 20%
indices.queries.cache.size: 30%
cluster.routing.allocation.same_shard.host: true
path.data: /data/program/es/data
bootstrap.memory_lock: true
network.host: 0.0.0.0
cluster.max_shards_per_node: 3000

reindex.remote.whitelist: "*.*.*.*:*"
cluster.routing.allocation.node_initial_primaries_recoveries: 40
xpack.security.enabled: false
```

注意默认会自动开启安全认证



## 观察

日志集群集群写入6万qps

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-*****************.png" alt="image-*****************" style="zoom:50%;" />



### cluster health

```
curl "***********:9200/_cluster/health?pretty"
```

输出：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/wecom-temp-ae753dc045bec0f992eae215c09fadba.png" alt="wecom-temp-ae753dc045bec0f992eae215c09fadba" style="zoom:60%;" />

- status：集群状态，分为green、yellow和red。
- number_of_nodes/number_of_data_nodes:集群的节点数和数据节点数。 
- active_primary_shards：集群中所有活跃的主分片数。 
- active_shards：集群中所有活跃的分片数。 
- relocating_shards：当前节点迁往其他节点的分片数量，通常为0，当有节点加入或者退出时该值会增加。 
- initializing_shards：正在初始化的分片，刚创建第一个索引，分片都会短暂的处于 initializing 状态，分片不应该长期停留在 initializing 状态。
- unassigned_shards：未分配的分片数，通常为0，当有某个节点的副本分片丢失该值就会增加。 
- number_of_pending_tasks：是指主节点创建索引并分配shards等任务，如果该指标数值一直未减小代表集群存在不稳定因素
- active_shards_percent_as_number：集群分片健康度，活跃分片数占总分片数比例。 

集群状态

- red  存在不可用的主分片，搜索只能返回部分数据，而分配到这个分片上的写入请求会返回一个异常，已经影响到索引读写，需要重点关注；
- yellow 主分片可用但不是所有的副本分片可用，不影响集群可用性。可以把yellow想象成一个需要关注的warnning，该情况不影响索引读写，一般会自动恢复；

active_shards 、active_primary_shards

- 如果这两个数字相等说明集群没有副本分片全部都是主分片，一般 active_shards 是 active_primary_shards的两倍；

集群健康可以通过level参数进一步查看 indices 和shards

```shell
curl -XGET 'localhost:9200/_cluster/health?level=indices'
curl -XGET 'localhost:9200/_cluster/health?level=shards'
```

number_of_pending_tasks

可进一步通过  _cluster/pending_tasks 指令查看具体原因：

```
{
  "tasks" : [
    {
      "insert_order" : 3798995,
      "priority" : "URGENT",
      "source" : "create-index [w-campaign-service-2023-02-28], cause [api]",
      "executing" : true,
      "time_in_queue_millis" : 8437,
      "time_in_queue" : "8.4s"
    }
  ]
}
```

 

### cluster stats

[cluster stats](https://www.elastic.co/guide/en/elasticsearch/reference/current/cluster-nodes-stats.html) 集群状态信息 ，整个集群的一些统计信息，例如文档数、分片数、资源使用情况等信息，从这个接口基本能够获取到集群所有关键指标项

用法：

```
GET /_nodes/stats

GET /_nodes/<node_id>/stats

GET/_nodes/stats/<metric>

GET/_nodes/<node_id>/stats/<metric>

GET /_nodes/stats/<metric>/<index_metric>

GET /_nodes/<node_id>/stats/<metric>/<index_metric>
```

可以指定node_id和metric，metric包括：

- indices
- fs
- jvm
- indices
- indices

例子：

```
curl http://localhost:9200/_nodes/*************/stats/fs?pretty
curl http://localhost:9200/_nodes/*************/stats/thread_pool?pretty
GET _nodes/*************/stats/indices
```





### cluster settings

 此命令可以查看集群配置，**注意不是所有配置都属于cluster，有些配置是属于index**

```

curl ***********:9200/_cluster/settings?pretty

```

输出：

```json
curl --user elastic:esadmin  "http://**********:9200/_cluster/settings?pretty"
{
  "persistent" : {
    "action" : {
      "auto_create_index" : "+m-*,*"
    },
    "cluster" : {
      "routing" : {
        "rebalance" : {
          "enable" : "none"
        },
        "allocation" : {
          "cluster_concurrent_rebalance" : "60", 
          "node_concurrent_recoveries" : "60", 	//此参数影响集群恢复速度
          "disk" : {
            "include_relocations" : "true",
            "watermark" : {
              "low" : "15g",
              "flood_stage" : "10g",
              "high" : "10g"
            }
          },
          "node_initial_primaries_recoveries" : "80",
          "balance" : {
            "index" : "1",
            "shard" : "0"
          },
          "enable" : "all"
        }
      },
      "blocks" : {
        "read_only" : "false"
      },
      "max_shards_per_node" : "3000"
    },
}    
```

### node hot_threads

查看节点的热点线程

```
GET /_nodes/hot_threads

GET /_nodes/<node_id>/hot_thread
```

输出:

```shell
   64.7% (323.6ms out of 500ms) cpu usage by thread 'elasticsearch[web-49-100-hzifc.node.hzifc.wacai.sdc][write][T#2]'
     3/10 snapshots sharing following 17 elements
       java.lang.Throwable.fillInStackTrace(Native Method)
       java.lang.Throwable.fillInStackTrace(Throwable.java:783)
       java.lang.Throwable.<init>(Throwable.java:265)
       java.lang.Exception.<init>(Exception.java:66)
       java.lang.RuntimeException.<init>(RuntimeException.java:62)
       java.lang.IllegalArgumentException.<init>(IllegalArgumentException.java:52)
       org.elasticsearch.ingest.common.GrokProcessor.execute(GrokProcessor.java:68)
       org.elasticsearch.ingest.CompoundProcessor.execute(CompoundProcessor.java:124)
       org.elasticsearch.ingest.Pipeline.execute(Pipeline.java:100)
       org.elasticsearch.ingest.IngestService.innerExecute(IngestService.java:473)
       org.elasticsearch.ingest.IngestService.access$100(IngestService.java:68)
       org.elasticsearch.ingest.IngestService$4.doRun(IngestService.java:402)
```



### cluster thread_pool

当es变慢可以通过此命令查看线程占用情况

```
curl localhost:9200/_cat/thread_pool
```

如果要查看节点占用情况:

```
curl "************:9200/_nodes/stats/thread_pool?pretty"
```

输出信息较多；

### shards

通过以下命令可以看到集群所有的shards分布情况：

```
curl --user elastic:esadmin  "http://**********:9200/_cat/shards?v&h=index,shard,prirep,state,unassigned.reason"
```

输出：

![image-20220802110616056](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220802110616056.png)

表示该索引有两个分片，一个副本，两个主分片都在s1上，从分片分别在s2和 s3，所以一共4个分片

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220802112220778.png" alt="image-20220802112220778" style="zoom:60%;" />

也可以查询某个索引shard情况

```
GET _cat/shards/ratel_trace_index*
```

个性化参数：

```
GET _cat/shards/?bytes=mb&v&h=index,store
```

### shard allocation

通过这个命令能看到集群中分片的分配情况：

```
curl ***********:9200/_cat/allocation?v
```

输出

```
shards disk.indices disk.used disk.avail disk.total disk.percent host         ip           node
  1687       85.9gb   134.2gb      2.2tb      2.3tb            5 ************ ************ es-5-35-hzifc.node.hzifc.wacai.sdc
  1687       87.9gb   147.5gb      1.9tb      2.1tb            6 ***********  ***********  node-1
```
可以看到测试环境只有两台节点，并且每台节点的shard数量都是平均的，但是生产环境就未必平均了：

![image-20220802104101935](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220802104101935.png)

32.1这台机器的分片数远远大于另外两台，这是因为不少索引模板中指定了分配策略；

### shard allocation explain

通过此命令可以查看分片的分配失败原因

```shell
curl ***********:9200/_cluster/allocation/explain?pretty
```

输出 ：

### node

查看节点状况，包括cpu使用，磁盘空间使用等等

```
GET _cat/nodes?h=ip,port,nodeId,disk,heap.percent,name,load_1m,master,role&full_id&v&s=name
```

查看节点参数，比如该节点属于哪个rack

```
GET _cat/nodeattrs/?v
```

### template

查看模板配置

```shell
GET _template/?format=yaml 				#查看所有模板配置:index_patterns，rack，order
GET _template/base 								#查看base索引模板
```

查看模板状态

```
GET _cat/templates/?v&format=yaml  																							
```

### indices

查看索引状态

```
GET _cat/indices/?v&s=store.size&h=status,index,docs.count,store.size							//查看所有索引状态
GET _cat/indices/ratel_trace_index*?v 																						//支持*匹配索引，也支持h=自定义title
get _cat/indices/*?v&s=store.size:desc																						//排序
```



### user auth

用户认证需要添加user信息

```
curl --user elastic:esadmin
```

### analyze

查看索引是如何分词的。

样例：

```json
POST w-middleware_drc-producer-batch-2022-09-14/_analyze
{
  "field": "productPy", 
  "text": "挖财"
}
```

输出：

```json
{
  "tokens" : [
    {
      "token" : "w",
      "start_offset" : 0,
      "end_offset" : 1,
      "type" : "word",
      "position" : 0
    },
    {
      "token" : "wa",
      "start_offset" : 0,
      "end_offset" : 1,
      "type" : "word",
      "position" : 0
    },
    {
      "token" : "wc",
      "start_offset" : 0,
      "end_offset" : 2,
      "type" : "word",
      "position" : 0
    },
    {
      "token" : "c",
      "start_offset" : 1,
      "end_offset" : 2,
      "type" : "word",
      "position" : 1
    },
    {
      "token" : "cai",
      "start_offset" : 1,
      "end_offset" : 2,
      "type" : "word",
      "position" : 1
    }
  ]
}

```



## 索引

### [索引创建](https://www.elastic.co/guide/en/elasticsearch/reference/current/indices-create-index.html)

语法

```
PUT /<index>
```

还可以附加一些参数:

```
PUT /my-index
{
  "settings": {
    "index": {
      "number_of_shards": 2,  
      "number_of_replicas": 1 
    }
  },
  "mappings": {
      "properties": {
        "description": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "id": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        },
        "title": {
          "type": "text",
          "fields": {
            "keyword": {
              "type": "keyword",
              "ignore_above": 256
            }
          }
        }
      }
    }  
}
```

### 索引修改

**注意： ES不支持直接修改索引，👇🏻这样操作会报错，提示：resource_already_exists_exception**

```
PUT /<index>
```

正确的方式：

修改_settings：

```python
PUT /<index>/_settings
{
	//...
}
```

修改 mappings:

```shell
PUT fund_fundinfo/_mapping/_doc 		#6.x, _doc是type
PUT fund_fundinfo/_mapping/ 				#7.x开始type取消了
```

### 修改字段类型

ES 不支持修改已有索引的字段类型，只能新建一个索引，在通过reindex 的方式创建。

步骤如下：

1、创建新的索引，在这里修改字段 

```
PUT fund_fundinfo2
```

2、执行 reindex

```
POST _reindex
{
  "source": {
    "index": "fund_fundinfo"
  },
  "dest": {
    "index": "fund_fundinfo2"
  }
}
```

3、删除原始索引，重新创建，再反向reindex。注意这一步可能丢数据，可以联系业务方停止写入

### 模板创建

```json
PUT /_template/wac-amc-avengers-dict-amc-disposer-data
{
 "order": 10,
    "template": "wac-amc-avengers-dict-amc-disposer-data*",
    "settings": {
      "index": {
        "routing": {
          "allocation": {
            "include": {
              "rack": "r1"
            }
          }
        },
        "refresh_interval": "10s",
        "number_of_shards": "2",
        "number_of_replicas": "1"
      }
    },
    "aliases": {}	
} 
```

 日志类型索引模板:

```json
PUT _template/c-netscaler-log
{
    "order" : 3,
    "index_patterns" : [
      "c-netscaler-log*"
    ],
    "settings" : {
      "index" : {
        "codec" : "best_compression",
        "routing" : {
          "allocation" : {
            "include" : {
              "rack" : "r2"
            }
          }
        },
        "refresh_interval" : "20s",
        "number_of_shards" : "2",
        "translog" : {
          "flush_threshold_size" : "2048mb",
          "durability" : "async"
        },
        "merge" : {
          "scheduler" : {
            "max_thread_count" : "1"
          }
        },
        "query" : {
          "default_field" : "message"
        },
        "max_result_window" : "10000",
        "requests" : {
          "cache" : {
            "enable" : "true"
          }
        },
        "unassigned" : {
          "node_left" : {
            "delayed_timeout" : "3h"
          }
        },
        "number_of_replicas" : "0"
      }
    },
    "mappings" : {
      "doc" : {
        "dynamic_templates" : [
          {
            "string_template" : {
              "mapping" : {
                "index" : true,
                "type" : "keyword"
              },
              "match_mapping_type" : "string",
              "match" : "*"
            }
          }
        ],
        "properties" : {
          "msg" : {
            "type" : "keyword"
          },
          "kubernetes" : {
            "properties" : {
              "app" : {
                "type" : "keyword"
              },
              "namespace" : {
                "type" : "keyword"
              }
            }
          },
          "path" : {
            "type" : "keyword"
          },
          "@timestamp" : {
            "type" : "date"
          },
          "level" : {
            "type" : "keyword"
          },
          "extra" : {
            "properties" : {
              "app" : {
                "type" : "keyword"
              },
              "offset" : {
                "type" : "long"
              },
              "ip" : {
                "type" : "keyword"
              },
              "host" : {
                "type" : "keyword"
              },
              "source" : {
                "type" : "keyword"
              },
              "type" : {
                "type" : "keyword"
              }
            }
          },
          "host" : {
            "type" : "keyword"
          },
          "thread" : {
            "type" : "keyword"
          },
          "message" : {
            "type" : "text"
          },
          "type" : {
            "type" : "keyword"
          },
          "class" : {
            "type" : "keyword"
          },
          "process_time" : {
            "type" : "float"
          }
        }
      }
    },
    "aliases" : { }
}
```



## 集群停机

### [*官方文档*](https://www.elastic.co/guide/en/elasticsearch/reference/current/restart-cluster.html#restart-cluster-rolling)

> 1、**Disable shard allocation.** 这一步其实是可选的，因为我们索引配置 了unassigned.node_left.delayed_timeout 结点挂了超过 3 小时才会重新分配;
>
> 2、**Stop non-essential indexing and perform a flush. ** 可选，不过执行之后可以加快恢复速度;
>
> 3、**kill $(cat pid)**
>
> 4、**restart**
>
> 5、**Reenable shard allocation.**

真正需要做的事情就是 kill 和 restart

### 最佳实践

1、 禁止写入

2、 flush

3、停机

4、 恢复写入

恢复过程发现比较慢，影响的参数: 

> cluster.routing.allocation.node_concurrent_outgoing_recoveries=60



### 重启脚本

crontab
```
      1 * * * * * sh /data/program/monitor_script/kibana_reboot &
      2 * * * * * sh /data/program/monitor_script/oom_reboot &
      3 30 4 * * * /home/<USER>/clean_hermes_proxy_log.sh
```
es
```shell
$ cat /data/program/monitor_script/oom_reboot
#!/bin/bash
source ~/.bash_profile
BASEDIR=`dirname $0`
BASEDIR=`(cd "$BASEDIR"; cd ../; pwd)`

pid_num=`ps aux|grep Elasticsearch|grep -v grep|wc -l`
if [ $pid_num -le 1 ] ;then
    sh /data/program/elasticsearch/bin/elasticsearch -d
    curl -H "Content-Type: application/json" -d '{"title":"ES测试环境告警","type":"wechat","to":["qianniao"],"content":"ES测试环境节点重启"}' http://crow.devops.k2.test.wacai.info/api/message
fi
```

kibana_reboot

```shell
$ cat /data/program/monitor_script/kibana_reboot
#!/bin/bash
BASEDIR=`dirname $0`
BASEDIR=`(cd "$BASEDIR"; cd ../; pwd)`

pid_num=`ps aux|grep node|grep -v grep|wc -l`
if [ $pid_num -le 0 ] ;then
    sh /data/program/kibana/bin/kibana > /data/program/kibana/logs/kibana.log 2>&1 &
    echo "kibana reboot"
f
echo "check normal"
```



## 部署

### 系统配置

1、vm.max_map_count=500000直接写到/etc/sysctl.conf中,然后执行sysctl -p

命令：

```
echo "vm.max_map_count=500000" >> /etc/sysctl.conf
sysctl -p
```

2、添加下面两行到 /etc/security/limits.conf  ：

```
echo "appweb soft memlock unlimited" >> /etc/security/limits.conf
echo "appweb hard memlock unlimited" >> /etc/security/limits.conf
```

3、调整max open file

当前会话修改：

```
ulimit -n 655360
```

永久生效：

```
echo "appweb soft nofile 655360"  >> /etc/security/limits.conf
echo "appweb hard nofile 655360"  >> /etc/security/limits.conf
```

### 节点配置

es一般要求是最好3台机器节点主要配置：

```properties
cluster.name: wushu-cloud
node.name: ${HOSTNAME}
node.attr.rack: r1
node.master: true
node.data: true

#是否开启gateway
#node.ingest: false
#http.enabled: true

network.host: 0.0.0.0
path.data: /data/program/es/data
transport.tcp.compress: true
http.compression: true

discovery.zen.ping.unicast.hosts: ["**********", "**********", "**********"]
discovery.zen.fd.ping_interval: 30s
discovery.zen.fd.ping_retries: 6
discovery.zen.fd.ping_timeout: 120s
discovery.zen.minimum_master_nodes: 2

#被我注释了，建议不要设置，可能会OOM
#thread_pool.write.queue_size: 50000 
node.max_local_storage_nodes: 4
indices.memory.index_buffer_size: 20%
indices.queries.cache.size: 30%

gateway.recover_after_nodes: 1
cluster.routing.allocation.same_shard.host: true
cluster.routing.allocation.disk.threshold_enabled: true
cluster.routing.allocation.disk.watermark.low: 15gb
cluster.routing.allocation.disk.watermark.high: 10gb
cluster.routing.allocation.disk.watermark.flood_stage: 10gb

bootstrap.system_call_filter: false
reindex.remote.whitelist: "*.*.*.*:*"
cluster.routing.allocation.node_initial_primaries_recoveries: 40

#下面配置是开启安全认证
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
xpack.security.transport.ssl.verification_mode: certificate
xpack.security.transport.ssl.keystore.path: elastic-certificates.p12
xpack.security.transport.ssl.truststore.path: elastic-certificates.p12
```

说明：

- node.attr.rack 节点分组，node.attr.{tag} 参数为节点做个标签，在创建索引是可以按该标签来分配索引分片
- cluster.routing.allocation.same_shard.host  阻止主副本分片被分配到同一台物理机，提高可用性
- discovery.zen.minimum_master_nodes  最少master节点数，一般为master节点数的一般+1，为了防止出现选主脑裂
- discovery.zen.ping.unicast.hosts 广播节点，新节点通过该配置加入集群
- xpack.security.enabled 是否开启身份认证
- http.enabled  是否开启对外的http端口，data节点一般不开启
- bootstrap.memory_lock 是否开启物理内存锁定，避免es使用swap交换分区
- node.max_local_storage_nodes 同一个安装路径允许启动ES节点数量
- cluster.routing.allocation.node_initial_primaries_recoveries 并行恢复的shard数量，默认4个偏小

可以参考[官方文档](https://www.elastic.co/guide/en/elasticsearch/reference/7.9/settings.html)，PS: 官方文档大纲在左侧

master节点要求至少3个，如果是3台机器，则每台一个master，一个coordinate，一个data节点

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-*****************.png" alt="image-*****************" />

### 调整 _cluster/settings

集群部署完成后，调整集群配置：

```json
PUT _cluster/settings
{
	"persistent": {
		"action": {
			"auto_create_index": "*"
		},
		"cluster": {
			"routing": {
				"rebalance": {
					"enable": "all"
				},
				"allocation": {
					"cluster_concurrent_rebalance": "50",
					"node_concurrent_recoveries": "40",
					"disk": {
						"include_relocations": "true",
						"watermark": {
							"low": "15g",
							"flood_stage": "10g",
							"high": "10g"
						}
					},
					"node_initial_primaries_recoveries": "40",
					"balance": {
						"index": "0.55f",
						"shard": "0.45f"
					},
					"enable": "all"
				}
			}
		},
		"indices": {
			"breaker": {
				"fielddata": {
					"limit": "35%",
					"overhead": "1.1"
				},
				"request": {
					"limit": "40%",
					"overhead": "1.1"
				},
				"total": {
					"limit": "75%"
				}
			}
		},
		"discovery": {
			"zen": {
				"publish_timeout": "45s"
			}
		},
		"logger": {
			"index": {
				"indexing": {
					"slowlog": "WARN"
				},
				"search": {
					"slowlog": "DEBUG"
				},
				"fetch": {
					"slowlog": "DEBUG"
				}
			}
		}
	},
	"transient": {}
}
```

重要参数说明([参考elk中文文档](https://hezhiqiang.gitbook.io/elkstack/elasticsearch/principle/shard-allocate))：

- node_concurrent_recoveries： 影响节点恢复速度
- include_relocations：默认是true，意味着es在计算一个node的磁盘使用率的时候，会考虑正在分配给这个node的shard
- node_initial_primaries_recoveries：该参数用来控制**节点**重启时，允许同时恢复几个主分片。默认是 4 个。如果节点是多磁盘，且 IO 压力不大，可以适当加大
- watermark：在达到 `watermark.low` (默认 85%)的时候，新索引分片就不会再分配到这个节点上了。在达到 `watermark.high` (默认 90%)的时候，就会触发该节点现存分片的数据均衡，把数据挪到其他节点上去。这两个值不但可以写百分比，还可以写具体的字节数。有些公司可能出于成本考虑，对磁盘使用率有一定的要求，需要适当抬高这个配置

通过curl命令执行，可以把上述配置保存到json文件

```
curl -XPUT "http://localhost:9201/_cluster/settings" -H 'Content-Type: application/json' -d @settings.json
```

可以通过

```
curl "http://*************:9201/_cluster/health?pretty"
```

查看恢复情况：

```
{
  "cluster_name" : "wacaies5",
  "status" : "red",
  "timed_out" : false,
  "number_of_nodes" : 4,
  "number_of_data_nodes" : 2,
  "active_primary_shards" : 5635,
  "active_shards" : 5635,
  "relocating_shards" : 0,
  "initializing_shards" : 40,
  "unassigned_shards" : 1445,
  "delayed_unassigned_shards" : 1441,
  "number_of_pending_tasks" : 88,
  "number_of_in_flight_fetch" : 0,
  "task_max_waiting_in_queue_millis" : 2145852,
  "active_shards_percent_as_number" : 79.14325842696628
}
```

initializing_shards 就是并发恢复的分片数。



### base模板

完成后建立一个base模板，把基本的索引配置都放里面

```json
POST _template/base
{
    "order" : 0,
    "index_patterns" : [
      "*"
    ],
    "settings" : {
      "index" : {
        "codec" : "best_compression",
        "mapping" : {
          "ignore_malformed" : "false"
        },
        "search" : {
          "slowlog" : {
            "threshold" : {
              "fetch" : {
                "warn" : "1s",
                "trace" : "200ms",
                "debug" : "500ms",
                "info" : "800ms"
              },
              "query" : {
                "warn" : "10s",
                "trace" : "500ms",
                "debug" : "1s",
                "info" : "5s"
              }
            }
          }
        },
        "refresh_interval" : "30s",
        "number_of_shards" : "2",
        "translog" : {
          "flush_threshold_size" : "2048mb",
          "durability" : "async"
        },
        "merge" : {
          "scheduler" : {
            "max_thread_count" : "3"
          },
          "policy" : {
            "floor_segment" : "10mb"
          }
        },
        "max_result_window" : "10000",
        "requests" : {
          "cache" : {
            "enable" : "true"
          }
        },
        "unassigned" : {
          "node_left" : {
            "delayed_timeout" : "3h"
          }
        },
        "priority" : "5",
        "number_of_replicas" : "0"
      }
    },
    "mappings" : { },
    "aliases" : { }
}
```

> 注意该模板为生产环境日志集群，望润环境shard数为 2，副本为 1
>
> number_of_shards最低不能小于2，否则可能导致分配不平衡

```
curl -XPOST "http://***********:9201/_template/base" -H 'Content-Type: application/json' -d @base.json
```



### 索引模板

例子

```json
PUT _template/history.log
{
    "order" : 10,
    "index_patterns" : [
      "history.log"
    ],
    "settings" : {
      "index" : {
        "codec" : "best_compression",
        "mapping" : {
          "ignore_malformed" : "false"
        },
        "refresh_interval" : "30s",
        "number_of_shards" : "2",
        "priority" : "5",
        "number_of_replicas" : "0"
      }
    },
    "mappings" : { },
    "aliases" : { }
}
```



### 用户名密码设置

需要在es的config/elasticsearch.yml下配置：

```yaml
xpack.security.enabled: true
xpack.security.transport.ssl.enabled: true
xpack.security.transport.ssl.verification_mode: certificate
xpack.security.transport.ssl.keystore.path: elastic-certificates.p12
xpack.security.transport.ssl.truststore.path: elastic-certificates.p12
```

然后通过如下命令生成证书，注意是**生个一个复制到所有节点，不是每个节点都产生一个**，不要输入密码

```shell
#该命令会产生 elastic-stack-ca.p12 文件
bin/elasticsearch-certutil ca 

#产生elastic-certificates.p12
bin/elasticsearch-certutil cert --ca elastic-stack-ca.p12 

```

接下来将生成的 elastic-certificates.p12 放到每个节点的config目录下，启动ES。

首次启动完成之后，需要通过执行以下列命令**设置初始密码**：

```
 bin/elasticsearch-setup-passwords interactive
```

然后就可以访问：

```
curl --user elastic:esadmin localhost:9200/_cat/health?v
```

更改某个用户密码，需要通过管理员用户来变更: 

```
curl -XPOST -u elastic:esadmin "http://localhost:9200/_xpack/security/user/kibana/_password" -H 'Content-Type: application/json' -d' { "password": "123456" }'
```

 es的账号密码保存在一个特殊的索引。

es的内置了几个用户:elastic、apm_system、kibana等等，默认保存`.security` 索引中，参考官方文档[built-in-users](https://www.elastic.co/guide/en/elasticsearch/reference/current/built-in-users.html#built-in-users)

### es证书更新

```
curl -XPUT http://*********:9200/_xpack/license -H "Content-Type: application/json" -d @judge-yu-714de714-5e38-4285-b19b-c077a841e7fc-v5.json
```



### 模板创建

1、首先在 legion 控制台上创建索引模板，需要填写的信息包括：模板名称，匹配规则，日期格式，过期时间，rack，分片数等。legion会把这个索引信息写入数据库和ES，比如测试环境 fund_fundmanager 这个模板可以通过 GET _template/fund_fundmanager 查看；

2、模板中的 mappings 表示字段映射规则，date_detection是对日期做映射，目前没有开启，可以通过properties属性对模板字段映射做调整；

3、所有的模板默认还会继承base模板，原因是base模板匹配了*；

4、ES6之前是没有自动清理索引功能的，所以我们自己实现了自动清理索引功能，在legion-admin中；

5、ES 对于索引名并不要求一定要带上日期，对于带上日期的索引如需要跨天查询，可以用*；

创建模板语法：

```json
PUT /_template/wac-amc-avengers-amc-repay-amount-data
{
  
} 
```

## 滚动升级

官方文档：https://www.elastic.co/guide/en/elastic-stack/6.11/upgrading-elastic-stack.html

### 升级顺序

滚动升级即每个节点依次重启，按照下列顺序进行：

```
 ①升级data -> ②升级master -> ③升级client
```

升级期间kibana不可用

### 注意事项

1、升级前最好先在kibana 升级助手查看是否存在问题，**修改 legion-admin 的 application.properties 的 legion.highVersion.cluster**

2、新client节点启动后报错  

```
index [.watches] version not supported: 5.6.2 minimum compatible index version is: 6.0.0
```
原因是.watches索引是5.x版本升级到6.x遗留的，现在无法被7.x的client识别，但是这个索引是es管理watcher对象的，无法直接删除。
经过研究发现需要es增加配置，然后可以删除

```
xpack.watcher.enabled: false
```

3、 新集群记得安装老集群的插件，可以查看plugin目录，安装插件只放入目录即可; (**这步非常重要，可能会导致shard初始化失败**)

4、插件和es版本必须一致，如果没有对应ES版本的插件，可以修改插件的 plugin-descriptor.properties 文件，编辑 elasticsearch.version 项；

5、 **discovery.zen.ping.unicast.hosts配置的ip端口一定要看清楚**，出现过配错端口导致重新选举的master数据不全导致索引丢失的情况

6、遇到 Unassigned shards 错误，参考 [resolve unassigned shards](https://www.datadoghq.com/blog/elasticsearch-unassigned-shards/)

通过如下命令显示所有的UNASSIGNED的分片

```shell
 curl -XGET localhost:9200/_cat/shards?h=index,shard,prirep,state,unassigned.reason 2>&1 | grep UNASSIGNED | sort | uniq
```

通过如下命令查看unassign原因：

```shell
curl -XGET localhost:9200/_cluster/allocation/explain?pretty
```

错误如下:

```
cannot allocate because the cluster is still waitingfor the departed node holding a replica to rejoin, despite being allowed to allocate the shard to at least one other node
```

原因是我们设置了node_left.delayed_timeout为3小时，es不会自动分配，可以通过以下命令执行手动分配：

```shell
POST _cluster/reroute
  {
  "commands": [
    {
      "allocate_replica": {
        "index": "loan_goblin-case-change_2022-08",
        "shard": 0,
        "node": "mid-48-198-hzifc-2"
      }
    }
  ]
}
```

也可以先尝试执行 POST /_cluster/reroute?retry_failed=true  指令

7、升级失败之后降级报错：

```json
4) Error injecting constructor, ElasticsearchException[java.io.IOException: failed to read [id:22, legacy:false, file:/data/program/es/data/nodes/0/indices/qq1HWIlbTSiS27MY2YUfbg/_state/state-22.st]]; nested: IOException[failed to read [id:22, legacy:false, file:/data/program/es/data/nodes/0/indices/qq1HWIlbTSiS27MY2YUfbg/_state/state-22.st]]; nested: IllegalArgumentException[Unexpected field [mapping_version]];
  at org.elasticsearch.gateway.GatewayMetaState.<init>(Unknown Source)
  while locating org.elasticsearch.gateway.GatewayMetaState
    for parameter 4 at org.elasticsearch.gateway.GatewayService.<init>(Unknown Source)
  while locating org.elasticsearch.gateway.GatewayService
Caused by: ElasticsearchException[java.io.IOException: failed to read [id:22, legacy:false, file:/data/program/es/data/nodes/0/indices/qq1HWIlbTSiS27MY2YUfbg/_state/state-22.st]]; nested: IOException[failed to read [id:22, legacy:false, file:/data/program/es/data/nodes/0/indices/qq1HWIlbTSiS27MY2YUfbg/_state/state-22.st]]; nested: IllegalArgumentException[Unexpected field [mapping_version]];
```

采取办法：删除所有报错的state.st文件，然后重启集群，并重新执行reroute

8、启动kibana失败，删除kibana历史索引

```
curl -X DELETE "**********3:9200/.kibana*"
```



## 机器迁移

### 基本步骤

比如下面这种情况，现有集群机器需要下线，迁移到新机器，数据不能丢失：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-*****************.png" alt="image-*****************" style="zoom:50%;" />

只需要三步即可完成迁移，而且数据不会丢失：

1、新搭建 3 台机器先加入到老集群，新建的 3 台机器也加入到master列表

2、通过下列命令禁用老节点分配shard

```bash
PUT _cluster/settings
{
  "transient": {
    "cluster.routing.allocation.exclude._ip": "192.168.5.*"
  }
}
```

上述配置之后es会自动迁移分片，通过 _cat/allocation 命令查看老节点分片数为 0

```
shards disk.indices disk.used disk.avail disk.total disk.percent host          ip            node
   452       68.3gb    75.8gb    509.6gb    585.4gb           12 ************* ************* web-49-100-hzifc.node.hzifc.wacai.sdc
     0           0b    40.1gb      2.3tb      2.3tb            1 ************  ************  node-2
   452       41.1gb    47.1gb    538.3gb    585.4gb            8 ************* ************* web-49-102-hzifc.node.hzifc.wacai.sdc
   452       69.4gb    78.1gb    507.3gb    585.4gb           13 ************* ************* web-49-101-hzifc.node.hzifc.wacai.sdc
     0           0b    48.7gb        2tb      2.1tb            2 ***********   ***********   node-1
```



3、下线老节点。先修改配置把老节点从master中移除，再重启节点即可

### 集群重启

官方文档建议重启之前通过以下两个参数来**禁用集群rebalance和allocation**

- cluster.routing.rebalance.enable 启用或禁用分片rebalance：
- cluster.routing.allocation.enable 启用或禁用分片分配；

原因是当一个data节点被停机，master默认等待 index.unassigned.node_left.delayed_timeout (默认一分钟)后会把该节点上的数据迁移到其他节点，不过我们集群的索引都会修改为3小时，所以集群重启不需要禁用rebalance和allocation

两个参数的值：

```
all - 默认值，允许为各种分片分配分片。
primaries- 仅允许分片分配给主分片。
new_primaries- 允许分片仅用于新索引的主分片。
none- 任何索引都不允许任何类型的分片分配。
```

操作：

```shell
curl -X PUT  "http://***********:9200/_cluster/settings?pretty" -H 'Content-Type: application/json' -d'
{
   "transient" : { 
    "cluster.routing.rebalance.enable" : "all" 
  }
}'


curl -X PUT  "http://***********:9200/_cluster/settings?pretty" -H 'Content-Type: application/json' -d'
{
   "transient" : { 
    "cluster.routing.allocation.enable" : "all" 
  }
}'
```

这里的 transient 表示集群更新 API 有两种工作模式：

- 临时（Transient）这些变更在集群重启之前一直会生效。一旦整个集群重启，这些配置就被清除。
- 永久（Persistent）这些变更会永久存在直到被显式修改。即使全集群重启它们也会存活下来并覆盖掉静态配置文件里的选项。

参考 [*动态变更配置*](https://www.elastic.co/guide/cn/elasticsearch/guide/current/_changing_settings_dynamically.html)

需要禁止某个节点被分配，可以采用如下命令：

```shell
PUT _cluster/settings
{
  "transient": {
    "cluster.routing.allocation.exclude._ip": "192.168.2.*"
  }
}
```

取消可以通过值设置为null，比如：

   ```
PUT _cluster/settings
{
  "transient": {
    "cluster.routing.allocation.exclude._ip": null
  }
}
   ```

### 处理主分区 UNASSIGNED 的问题

因为一次配置失误在测试环境导致主分区出现UNASSIGNED，Cluster Health 为 RED

```
middleware_domino-filesyncedinfo  1 r STARTED          0    261b ************* *************
middleware_domino-filesyncedinfo  1 p STARTED          0    261b ************* *************
middleware_domino-filesyncedinfo  3 p STARTED    2037274 120.7mb ************* *************
middleware_domino-filesyncedinfo  3 r STARTED    2037274 120.7mb ************* *************
middleware_domino-filesyncedinfo  2 p STARTED    2036050 120.9mb ************* *************
middleware_domino-filesyncedinfo  2 r STARTED    2036050 120.9mb ************* *************
middleware_domino-filesyncedinfo  0 p UNASSIGNED                               
middleware_domino-filesyncedinfo  0 r UNASSIGNED                               
```

这种失误应该要避免，因为数据肯定是丢了，但事已至此如何补救呢？我第一反应是删除UNASSIGNED的主分区，没有找到相关操作，但是却发现可以通过[_cluster/reroute](https://www.elastic.co/guide/en/elasticsearch/reference/current/cluster-reroute.html) 的 allocate_empty_primary 指令来实现同样的效果，操作如下：

```
POST _cluster/reroute
  {
  "commands": [
    {
      "allocate_empty_primary": {
        "index": "middleware_domino-filesyncedinfo",
        "shard": 1,
        "node": "*************",
        "accept_data_loss" : true
      }
    }
  ]
}
```

这样会分配一个空的主分区来解决UNASSIGNED问题



## 备份

注意：elasticsearch 要求先通过 nfs 共享文件，所以并没有实际使用这种方式。[参考文档](https://segmentfault.com/a/1190000015125088)

### 创建仓库

首先，你要在 *elasticsearch.yml* 的配置文件中注明可以用作备份路径 *path.repo* ，如下所示：

```
path.repo: ["/data/es/es-bakcup"]
```

配置好后，就可以使用 *snapshot* api 来创建一个 repository 了:

```shell
PUT /_snapshot/backup_2022
{
  "type": "fs",
  "settings": {
    "location": "data/program/es/backup/2022"
  }
}
```

可以通过  GET /_snapshot/backup_2022 查看仓库信息。

之后我们就可以在这个 repository 中来备份数据了。

### 备份

有了 repostiroy 后，我们就可以做备份了，也叫快照，也就是记录当下数据的状态。如下所示我们创建一个名为 snapshot_1 的快照。

```shell
PUT /_snapshot/backup_2022/snapshot_1?wait_for_completion=true
```

*wait_for_completion* 为 true 是指该 api 在备份执行完毕后再返回结果，否则默认是异步执行的，我们这里为了立刻看到效果，所以设置了该参数，线上执行时不用设置该参数，让其在后台异步执行即可。

我们可以通过 `GET _snapshot/my_backup/snapshot_1`获取 snapshot_1 的执行状态。

通过上面的步骤我们成功创建了一个备份，但随着数据的新增，我们需要对新增的数据也做备份，那么我们如何做呢？方法很简单，只要再创建一个快照 *snapshot_2* 就可以了。

```shell
PUT /_snapshot/my_backup/snapshot_2?wait_for_completion=true
```

要说明的一点是，当你在同一个 repository 中做多次 snapshot 时，elasticsearch 会检查要备份的数据 segment 文件是否有变化，如果没有变化则不处理，否则只会把发生变化的 segment file 备份下来。这其实就实现了增量备份。

### 恢复

通过调用如下 api 即可快速实现恢复功能。

```shell
POST /_snapshot/my_backup/snapshot_1/_restore?wait_for_completion=true
{
  "indices": "index_1",
  "rename_replacement": "restored_index_1"
}
```

### 兼容性

你不能把一个高版本的备份在低版本恢复，比如将 6.x 的备份在 5.x 中恢复。而低版本备份在高版本恢复有一定要求：

```
A snapshot of an index created in 5.x can be restored to 6.x.
A snapshot of an index created in 2.x can be restored to 5.x.
A snapshot of an index created in 1.x can be restored to 2.x.
```

### 数据导出

尝试了下 **[elasticsearch-dump](https://github.com/elasticsearch-dump/elasticsearch-dump)** ，性能有点差，导出20多MB的数据要 20 分钟，可能可以通过参数控制

推荐docker，用法：

1. 去release页面下载安装文件

2. 打包镜像 

   ```
   docker build -t elasticdump:6.67 .
   ```

3. 执行：

   ```
   docker run --rm -ti elasticdump:6.67 \
     --input=http://production.es.com:9200/my_index \
     --output=/tmp/my_index_mapping.json \
     --type=data
   ```

   



## 常用脚本

### crud

```shell
PUT my-test										#创建索引
DELETE  my-test								#删除索引	
GET my-test/_search						#查询索引
POST my-test/_doc							#写数据
{
  "id":"2022-07-25_UN_CONNECTION_TOTAL_1",
  "code": "UN_CONNECTION_TOTAL",
  "reportDate": "2022-07-25",
  "assignBatchId": 2,
  "value": 0,
  "avengersStoryDate": 1658801750751	
}
POST  w-wac-finance-trinity-web-2021-11-09/_open #索引打开
```

### curl

```shell
curl --user elastic:esadmin localhost:9200/_cluster/settings?format=yaml

curl --user elastic:esadmin localhost:9200/_cat/health?v

curl --user elastic:esadmin localhost:9200/_cat/master
 
curl --user elastic:esadmin localhost:9200/_cat/nodes?h=ip,port,nodeId,disk,name,load_1m,cpuww,http,role&full_id

curl --user elastic:esadmin localhost:9200/_cat/fielddata

## 官方文档有 https://www.elastic.co/guide/en/elasticsearch/reference/6.8/docs-delete-by-query.html
curl -X POST "localhost:9200/twitter/_delete_by_query?pretty" -H 'Content-Type: application/json' -d'
{
  "query": { 
    "match": {
      "message": "some message"
    }
  }
}
'
curl "http://***********:9200/_cat/indices"

curl --user elastic:esadmin localhost:9200/_cluster/health?pretty

```

### 重建索引

命令如下

```java
POST _reindex
{
  "source": {
    "index": "middleware_domino-filesyncedinfo"
  },
  "dest": {
    "index": "middleware_domino-filesyncedinfo2"
  }
}
```

curl命令

```
curl -H "Content-Type:application/json" http://**********:9200/_reindex  -d '{"source": {"index": "sw_instance_traffi2-20230417"},"dest": {"index": "sw_instance_traffic3-20230417"}}}'
```

## ES 问题排查三板斧

### 1、 arthas dashboard

![企业微信截图_12b17d25-30f9-413b-832a-8f461aa09ef3](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_12b17d25-30f9-413b-832a-8f461aa09ef3.png)



### 2、查看热点线程

```
curl "http://localhost:9200/_nodes/hot_threads"
```

输出：

```
100.8% (503.9ms out of 500ms) cpu usage by thread 'elasticsearch[***********-d][search][T#12]'
     4/10 snapshots sharing following 35 elements
       org.apache.lucene.util.automaton.Operations.determinize(Operations.java:780)
       org.apache.lucene.util.automaton.Operations.getCommonSuffixBytesRef(Operations.java:1155)
       org.apache.lucene.util.automaton.CompiledAutomaton.<init>(CompiledAutomaton.java:238)
       org.apache.lucene.search.AutomatonQuery.<init>(AutomatonQuery.java:104)
       org.apache.lucene.search.AutomatonQuery.<init>(AutomatonQuery.java:81)
       org.apache.lucene.search.AutomatonQuery.<init>(AutomatonQuery.java:65)
       org.apache.lucene.search.WildcardQuery.<init>(WildcardQuery.java:56)
       org.elasticsearch.index.mapper.StringFieldType.wildcardQuery(StringFieldType.java:97)
       org.elasticsearch.index.query.WildcardQueryBuilder.doToQuery(WildcardQueryBuilder.java:192)
       org.elasticsearch.index.query.AbstractQueryBuilder.toQuery(AbstractQueryBuilder.java:106)
       org.elasticsearch.index.query.BoolQueryBuilder.addBooleanClauses(BoolQueryBuilder.java:415)
       org.elasticsearch.index.query.BoolQueryBuilder.doToQuery(BoolQueryBuilder.java:385)
       org.elasticsearch.index.query.AbstractQueryBuilder.toQuery(AbstractQueryBuilder.java:106)
       org.elasticsearch.index.query.BoolQueryBuilder.addBooleanClauses(BoolQueryBuilder.java:415)
       org.elasticsearch.index.query.BoolQueryBuilder.doToQuery(BoolQueryBuilder.java:383)
       org.elasticsearch.index.query.AbstractQueryBuilder.toQuery(AbstractQueryBuilder.java:106)
       org.elasticsearch.index.query.QueryShardContext.lambda$toQuery$2(QueryShardContext.java:319)
       org.elasticsearch.index.query.QueryShardContext$$Lambda$3651/1519149661.apply(Unknown Source
```



### 3、 查看正在执行的任务详情

```
curl "http://localhost:9200/_tasks?actions=*search&detailed"
```

输出:

```
\"productCode\":{\"wildcard\":\"*baiduboxlite://v33/dispatcher/priorityDispatcher?params=%7B%22schemes%22%3A%5B%22baiduboxapp%3A%2F%2Fvideo%2FinvokeVideoDetail%3FsystemTransition%3D0%26params%3D%257B%2522vid%2522%253A%25226837004323114737264%2522%252C%2522videoInfo%2522%253A%257B%2522seekSeconds%2522%253A%25220%2522%252C%2522vid%2522%253A%25226837004323114737264%2522%252C%2522title%2522%253A%2522%25E5%258E%259F%25E6%259D%25A5%25E8%25BF%2599%25E5%25B0%25B1%25E6%2598%25AF%25E6%258A%2595%25E8%25B5%2584%25E8%25AF%2588%25E9%25AA%2597%25E5%2595%258A%25EF%25BC%2581%2522%252C%2522duration%2522%253A95%252C%2522posterImage%2522%253A%2522http%253A%252F%252Ff7.baidu.com%252Fit%252Fu%253D917909319%252C1545229828%252
```

### 4、 查看请求来源ip

```
watch -f org.elasticsearch.client.support.AbstractClient search '{params[0].source, params[1].channel.delegate.channel}' 'params[0].indices[0]=="fund_fundinfo"'
```

### 5、查看当前节点index

```shell
#查看rt,qps
monitor -c 1 org.elasticsearch.index.shard.IndexShard applyIndexOperation 

watch -f org.elasticsearch.index.shard.IndexShard applyIndexOperation '{target.shardId()}'
```



## 问题处理

### 1、集群磁盘不足

#### 问题

ES 日志集群经常报磁盘不够用

#### 分析

首先梳理集群容量

| rack | ip列表                                | 作用     | 剩余容量              |
| ---- | ------------------------------------- | -------- | --------------------- |
| r1   | **********，**********                |          | 2t ，2.2t             |
| r2   | **********，**********0               |          | 1.5t ，1.9t           |
| r3   | **********9，***********              |          | 653g ，636g           |
| r4   | ***********，***********              | nginx    | 1.8t，  1.8t          |
| r5   | ***********                           |          | 1.3t                  |
| r6   | ***********，***********，*********** | fund备份 | 567.5g， 677.8g，6.4t |
| r7   | ***********                           | fund备份 |                       |

注意：***********，*********** 都是同时部署属于r4和r6，分别两块不同的盘

目前经常出现容量不够的是r3 和r6；

**r3问题分析**

登录到 *********** 机器 发现一些问题

1、目前只会写入 /data01/wacaiqshes 目录，而 /data02目录是空闲的;

```
/dev/sdb1       2.2T  1.4T  837G  63% /data01
/dev/sda6       1.1T   34M  1.1T   1% /data02
```

> es直接多目录写入，不过data02目录是机械磁盘，性能较差；

2、查询写入到20这台机器大于1g的索引

命令：

```shell
curl http://***********:9200/_cat/shards > shards.log #较慢
cat shards.log  | grep "120.20" | grep "gb "
```

输出：

```
w-finance_wac-finance-ezalor-provider-2022-08-07                     1 p STARTED       26535428   10.4gb *********** node-120-20
w-wcb-market-provider-2022-08-10                                     3 p STARTED        1026009    1.4gb *********** node-120-20
w-wcb-market-provider-2022-08-10                                     1 p STARTED        1023949    1.3gb *********** node-120-20
w-finance_wac-finance-alchemist-provider-2022-08-09                  1 p STARTED       20918109    3.1gb *********** node-120-20
w-ucenter_ucenter-deprecated-2022-08-10                              2 p STARTED       39715762   10.1gb *********** node-120-20
w-ucenter_ucenter-deprecated-2022-08-07                              2 p STARTED      133272848   27.2gb *********** node-120-20
w-ucenter_ucenter-deprecated-2022-08-06                              2 p STARTED      133454007   27.2gb *********** node-120-20
w-finance_wac-finance-ezalor-provider-2022-08-05                     1 p STARTED       26512517   10.5gb *********** node-120-20
```

发现 finance 和 ucenter 的索引占用比较多，需要找到对应的模板。

通过 get 索引名能看到对应rack，然后通过rack找到对应的模板：

- w-finance_wac-finance-ezalor-provide 对应的模板w-f，走r3；
-  w-ucenter_ucenter-deprecated 对应的模板w-k2-template, 走r3和r5；

经过这么分析，可以发现r3成了热点；

**r6问题分析**

对于r6，虽然有 3 台机器，但是有一台机器没有用上，原因在于m-fund索引模板的分区只有 2，而分区策略按索引级别来均分shard，es会优先选node id靠前的机器，所以91永远也不会被分配；

#### 解决方案

1、修改 w-ucenter模板，设置 w-ucenter开头的索引走r2；

2、修改 w-fund 模板，把分区数设置为 3；

3、es写入多个路径(未实施)；

### 2、索引文件清理

#### 统计索引文件大小

使用命令获取shards:

```bash
curl "http://***********:9200/_cat/indices?bytes=mb&v&h=index,status,docs.count,store.size" > indices.log
curl "http://***********:9200/_cat/shards?bytes=mb&v&h=index,state,ip,store" > shards.log
```

统计所有：

```bash
cat shards.log |  grep -v test | awk '{gsub(/\_2(\w|-)*/,""); print}'| awk '{gsub(/\-2(\w|-)*/,""); print}' | awk  '{arr[$1]+=$4}END{for (a in arr) print a, arr[a]/1024}' | sort -rnk 2 | head -50
```

按某台机器统计：

```bash
cat shards.log |  grep "128.82" | awk '{gsub(/\_2(\w|-)*/,""); print}'| awk '{gsub(/\-2(\w|-)*/,""); print}' | awk  '{arr[$1]+=$4}END{for (a in arr) print a, arr[a]/1024}' | sort -rnk 2 | head -50
```

某个索引统计：

```bash
 cat shards.log | grep "nginx" | awk '{print $3}' | sort | uniq -c | sort -nr
     40 ***********
     32 ***********
     32 ***********
      8 **********
      8 **********
      8 **********
```

统计每日新增

```
cat indices.log | grep -v close | grep 2023-02-20 |  awk '{total = total + $4}END{print total/1024" gb"}'
```



#### 清理CLOSE索引

可以通过以下命令拿到所有索引

```bash
curl "http://***********:9200/_cat/indices/?v&s=store.size&h=status,index,docs.count,store.size" > indices.log
```

这里有很多被close的索引

统计脚本：

```bash
cat indices.log | grep close | grep 2022 |  awk -F '2022' '{print $1}' | awk '{print $2}' | sort | uniq -c | sort -nr  | head -20 > close_indices.log
```

热点索引:

```
grep -f close_indices.log  indices.log  | grep "open" | grep "gb"  | awk '{print $3}' | awk -F '2022' '{print $1}' | sort | uniq > big_indices.log
```

产生删除2021年的索引的语句：

```
cat /tmp/log10 | grep 2021 | awk -F '2021' '{print $1}' | awk '{print $2}' |sort |uniq | awk '{print "DELETE " $1 "2021*"}'
```

#### 删除指定机器的索引

某台机器磁盘告警，我们想清理属于该机器的索引，应该如何操作？

首先es 把索引保存在本地磁盘，每个目录都是一个索引，不过目录名是uuid，这个 uuid可以通过 _cat/indies 查看，比如：merlin_flink_clicks-2022-11-04 索引的uuid:Ln2UWWWFTYeDVOzOsN88iQ，对应到本地目录则为：` /es-data/nodes/0/indices/Ln2UWWWFTYeDVOzOsN88iQ/2`，它的格式是：`/es-data/nodes/0/indices/{uuid}/{shardID}`

利用这一点，我们可以通过本地存储的文件反向统计出索引名。

首先查询所有 indices，注意title需要uuid

```
curl "http://***********:9200/_cat/indices?bytes=mb&v&h=index,uuid,status,docs.count,store.size" > indices.log
```

然后查询本地索引目录:
```
ll /data03/wacaiqshes/nodes/0/indices | awk '{print $9}' > indices_list.log
```

>  **2023-12-20踩坑**：**注意删除 indices_list.log 首行空行**，否则会导致全匹配

最后匹配(有点慢所以后台执行):

```
grep -f indices_list.log  indices.log  > indices_match.log &
```

接下来我们可以优先删除属于该机器的indices

```shell
cat indices_match.log | grep close | grep 2024-02 |awk  '{print $1}' | awk '{gsub(/[_-]?[0-9]{4}-[0-9]{2}-[0-9]{2}/, ""); print}' | sort | uniq | awk '{print "DELETE "$1 "-2024-02*"}'
```



### 3、每天 0 点集群不可用

通过流量监控查看每天 0 点开始有 10 分钟集群不可用

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221128101138373.png" alt="image-20221128101138373" style="zoom:50%;" />



查看ES master报错日志：

![image-20221128101257139](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221128101257139.png)

带着   failed to process cluster event (put-mapping) within 30s ， elasticsearch put mapping slowly 搜索了找到一些有价值的参考：

1、elastic discuss [failed-to-put-mappings-on-indices](https://discuss.elastic.co/t/failed-to-put-mappings-on-indices/311224)

这篇elastic上的帖子提到了 persisting state took longer than 30 seconds, which is not good，也就是这句话让我继续翻日志才发现也有类似的日志：

```
[2022-11-25T00:08:06,058][INFO ][o.e.c.m.MetaDataMappingService] [node-120-2-master] [m-ucenter_ucenter-session-provider-2022-11-25/-mRZBZl9RVi_ft49T9WMvg] update_mapping [doc]
[2022-11-25T00:08:06,801][WARN ][o.e.c.s.MasterService    ] [node-120-2-master] cluster state update task [put-mapping[doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc, doc]] took [36.2s] above the warn threshold of 30s
```

所以这个错误的原因是处理这些 put-mapping 的操作耗时超过了 30s导致。

2、Huawei [Data Write to Elasticsearch Is Slow Due to put-mapping Timeout](https://forum.huawei.com/enterprise/en/data-write-to-elasticsearch-is-slow-due-to-put-mapping-timeout/thread/703573-893)

这篇文章给出了Cause Analysis 和Solution：

> Cause Analysis 
>
> 1. indexes 和 shards 数量太大，索引是按天创建，大量的索引在数据的写入过程中被创建，集群中的分区数达到 7 万+
> 2. 批量写入数据时会自动创建索引
> 3. 索引字段数过多（数十个或数百个字段）
>
> 由于索引字段太多，索引模板中并没有设置所有字段，Elasticsearch 对数据进行 dynamic mapping，当写入大量数据和添加新字段时，会进行大量put_mapping 操作，导致Es Master阻塞，影响整个Elasticsearch集群的运行。
>
> Solution
>
> 1. 控制索引的数量，如果按照创建的索引太多则按照月创建，如果索引按照月创建，可以适量增大shard的数量。控制shards的数量，确保单个集群的分区数量不要超过5万
> 2. 提前创建索引，不要在写入数据时自动创建索引
> 3. 控制字段数。 （服务端根据实际业务需求进行改进）
> 4. 因为业务需求不能关闭动态映射。建议尽可能精确地使用映射规则

我们遇到的问题基本和华为这篇帖子的所诉一致，所以我们也采用相同的措施：

1. 梳理所有索引，发现存在大量的空索引，原因是应用在vm迁移到容器过程中，老的索引并没有删除，并且会一直自动创建，拉取了这部分数据通过脚本批量删除，跑了一天，shard数量减少 50%，从 3.5 万较少到 1.6万。
2. 保证 template 中定义的字段和索引字段一致，避免 put_mapping 操作，这部分遇到的问题是前端日志不规范，写了一个脚本对比取并集

通过上面两个操作master日志中没有再出现 persisting state took longer than 30 seconds，0 点也没有再集群不可用情况。

> 插曲：最开始以为日志格式都比较统一，所以抽取公共template会比较容易，结果发现前端应用采用 json 格式动态产生 KV，导致各个前端应用的字段都不一样，很难抽取出一个公共的结构，所以会有少量报错，但不会导致集群不可用。这也给了一个教训，对于 ES最好还是提前确定好字段。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221129163825766.png" alt="image-20221129163825766" style="zoom:50%;" />





### 4、集群报错EsRejected

报错如下：

![image-20221124150845128](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221124150845128.png)

后来发现是有一个client节点的根目录快写满了导致.

### 5、测试环境OOM

测试环境 ************* ES 挂了，查看日志发现oom:

```properties
[2023-04-24T19:01:24,919][ERROR][o.e.b.ElasticsearchUncaughtExceptionHandler] [web-49-102-hzifc.node.hzifc.wacai.sdc] fatal error in thread [Thread-1136062], exiting
java.lang.OutOfMemoryError: Java heap space
        at io.netty.buffer.UnpooledHeapByteBuf.allocateArray(UnpooledHeapByteBuf.java:88) ~[?:?]
        at io.netty.buffer.UnpooledByteBufAllocator$InstrumentedUnpooledHeapByteBuf.allocateArray(UnpooledByteBufAllocator.java:164) ~[?:?]
        at io.netty.buffer.UnpooledHeapByteBuf.<init>(UnpooledHeapByteBuf.java:61) ~[?:?]
        at io.netty.buffer.UnpooledByteBufAllocator$InstrumentedUnpooledHeapByteBuf.<init>(UnpooledByteBufAllocator.java:159) ~[?:?]
        at io.netty.buffer.UnpooledByteBufAllocator.newHeapBuffer(UnpooledByteBufAllocator.java:82) ~[?:?]
        at io.netty.buffer.AbstractByteBufAllocator.heapBuffer(AbstractByteBufAllocator.java:166) ~[?:?]
        at io.netty.buffer.AbstractByteBufAllocator.heapBuffer(AbstractByteBufAllocator.java:157) ~[?:?]
        at io.netty.buffer.Unpooled.buffer(Unpooled.java:116) ~[?:?]
        at io.netty.buffer.Unpooled.copiedBuffer(Unpooled.java:409) ~[?:?]
        at org.elasticsearch.http.netty4.Netty4HttpRequestHandler.channelRead0(Netty4HttpRequestHandler.java:73) ~[?:?]
        at io.netty.channel.SimpleChannelInboundHandler.channelRead(SimpleChannelInboundHandler.java:105) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340) ~[?:?]
        at org.elasticsearch.http.netty4.pipelining.HttpPipeliningHandler.channelRead(HttpPipeliningHandler.java:68) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340) ~[?:?]
        at org.elasticsearch.http.netty4.cors.Netty4CorsHandler.channelRead(Netty4CorsHandler.java:86) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340) ~[?:?]
        at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:102) ~[?:?]
        at io.netty.handler.codec.MessageToMessageCodec.channelRead(MessageToMessageCodec.java:111) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340) ~[?:?]
        at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:102) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:362) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.invokeChannelRead(AbstractChannelHandlerContext.java:348) ~[?:?]
        at io.netty.channel.AbstractChannelHandlerContext.fireChannelRead(AbstractChannelHandlerContext.java:340) ~[?:?]
        at io.netty.handler.codec.MessageToMessageDecoder.channelRead(MessageToMessageDecoder.java:102) ~[?:?]
```

查看oom自动保存heap文件如下

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230426102358873.png" alt="image-20230426102358873" style="zoom:50%;" />

1. byte[] 是 netty 管理的PooledByteBufAllocator管理的对象缓存，3.2g占用了 40%
2. org.elasticsearch.transport.Transport$ResponseHandlers，1.7g 占用 22%
3. org.elasticsearch.index.IndexService 1.5g，占用了18%



查看org.elasticsearch.transport.Transport$ResponseHandlers，内部是一个map，节点都是bulk 对象信息

![image-20230426105323685](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230426105323685.png)



通过 MAT 的 path2gc 查看16mb byte[] 被谁引用，发现是io.netty.buffer.PoolChunk，统计了下一共 201，每个都占用16mb，算先来一共3.2g

PoolChunk 是被 HeapArena 管理的，HeapArena按照core*2，所以是16个：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230426110050923.png" alt="image-20230426110050923" style="zoom:50%;" />

 点看任意一个HeapArena

![image-20230426110135405](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230426110135405.png)

PoolChunkList 是一个链表结构，head是头，head还有一个next，继续下钻，每个PoolChunkList会 15 个

```
Class Name                                  | Shallow Heap | Retained Heap
---------------------------------------------------------------------------
head io.netty.buffer.PoolChunk @ 0x5c2684d00|           88 |        16,520   
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x7996da430|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x7997da238|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x79ca415c0|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x63d03ae38|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x6147a9be0|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x743466b40|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x6801786a8|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x6d328d670|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x68017dba0|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x66cca52f8|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x66c386730|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x6d4abbdc8|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x6d4abbd70|           88 |        16,520
---------------------------------------------------------------------------
next io.netty.buffer.PoolChunk @ 0x6c91ebc68|           88 |        16,520
---------------------------------------------------------------------------
```



网络流量情况

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-*****************.png" alt="image-*****************" style="zoom:50%;" />



目前怀疑是磁盘IO不足，被批量 bulk 压挂了，因为OOM 之前做过索引迁移，执行过：

```
PUT _cluster/settings
{
  "transient": {
    "cluster.routing.allocation.exclude._ip": "192.168.5.*"
  }
}
```

另外这个配置也有问题：

```
thread_pool.bulk.queue_size: 50000thread_pool.bulk.queue_size: 50000
```

参考：

- https://discuss.elastic.co/t/elasticsearch-6-8-23-happen-oom/330802
- https://discuss.elastic.co/t/elasticsearch-process-is-killed-by-oom-killer/217792

### 6、无法创建索引Unknown tokenizer type [pinyin] for [pinyin]

业务方反馈无法新增字段，尝试修改索引mapings报错:

 ```java
 {
   "error": {
     "root_cause": [
       {
         "type": "remote_transport_exception",
         "reason": "[es-120-24-hzqsh][***********:9300][indices:admin/mapping/put]"
       }
     ],
     "type": "illegal_argument_exception",
     "reason": "Unknown tokenizer type [pinyin] for [pinyin]"
   },
   "status": 400
 }
 ```

登录服务器，详细报错日志如下：

```bash
[2024-01-30T22:00:32,340][DEBUG][o.e.a.a.i.m.p.TransportPutMappingAction] [es-120-24-hzqsh] failed to put mappings on indices [[[fund_fundinfo/6w6Z63MiSJWA_DvzuwE3UA]]], type [fund]
java.lang.IllegalArgumentException: Unknown tokenizer type [pinyin] for [pinyin]
  at org.elasticsearch.index.analysis.AnalysisRegistry.getAnalysisProvider(AnalysisRegistry.java:406) ~[elasticsearch-6.8.23.jar:6.8.23]
  at org.elasticsearch.index.analysis.AnalysisRegistry.buildMapping(AnalysisRegistry.java:356) ~[elasticsearch-6.8.23.jar:6.8.23]
  at org.elasticsearch.index.analysis.AnalysisRegistry.buildTokenizerFactories(AnalysisRegistry.java:189) ~[elasticsearch-6.8.23.jar:6.8.23]
  at org.elasticsearch.index.analysis.AnalysisRegistry.build(AnalysisRegistry.java:163) ~[elasticsearch-6.8.23.jar:6.8.23]
  at org.elasticsearch.index.IndexModule.newIndexMapperService(IndexModule.java:461) ~[elasticsearch-6.8.23.jar:6.8.23]
  at org.elasticsearch.indices.IndicesService.createIndexMapperService(IndicesService.java:579) ~[elasticsearch-6.8.23.jar:6.8.23]
  at org.elasticsearch.cluster.metadata.MetaDataMappingService$PutMappingExecutor.execute(MetaDataMappingService.java:225) ~[elasticsearch-6.8.23.jar:6.8.23]
  at org.elasticsearch.cluster.service.MasterService.executeTasks(MasterService.java:643) ~[elasticsearch-6.8.23.jar:6.8.23]
```

报错发生在 **AnalysisRegistry.getAnalysisProvider() L406**，代码如下：

```java
private <T> AnalysisProvider<T> getAnalysisProvider(Component component, Map<String, ? extends AnalysisProvider<T>> providerMap,String name, String typeName) {
      
  AnalysisProvider<T> type = providerMap.get(typeName); //①
  if (type == null) {
    throw new IllegalArgumentException("Unknown " + component + " type [" + typeName + "] for [" + name + "]");
  }
  return type;
}
```

可以发现是 providerMap 中没有找到对应的AnalysisProvider。进一步发现，这个map是AnalysisRegistry的成员变量tokenizers，并且在 AnalysisRegistry.buildMapping()被传递过来，于是通过 arthas 监听该方法，打印该map发现和测试环境存在比较大的差异，少了很多，并且master没有pinyin，难道master没有安装pinyin插件。

通过执行

```shell
./bin/elasticsearch-plugin list
```

发现master确实没有安装pinyin，当时只在data节点安装了pinyin，所以导致只有put mappings报错，写数据正常！后续需要注意master和data节点plugin一定要保持一致。

### 7、shard分配不均衡

发现日志集群两台机器分配不均衡导致机器负载不平衡

```
shards disk.indices disk.used disk.avail disk.total disk.percent host          ip            node
   226       50.1gb    66.6gb    518.8gb    585.4gb           11 ************* ************* *************
    90       33.9gb    51.5gb    533.8gb    585.4gb            8 ************* ************* *************
```

经过排查发现是 _cluster/settings 参数设置有问题，日志集群被设置为

```
"cluster.routing.allocation.balance.shard": 0.0,
"cluster.routing.allocation.balance.index": 1.0
```

这两个参数含义：

- cluster.routing.allocation.balance.shard 节点中**分片总数对权重的影响因子**，默认为 0.45，该值越大则各节点的分片数越趋向于相等。
- cluster.routing.allocation.balance.index 节点中**同一索引的分片数量的均衡程度**，默认为 0.55，设置得更高会导致每个节点上持有的同一索引的分片数量更加平均。

如何理解？ 

shard 只保证每个节点上的分片数量是均衡，而index会尽量让同一索引的分片不分配在同一个节点。

下面例子中，shard可能导致**b1和b2都分配到节点1上**，虽然三个节点的分片数量是一样，但是b1和b2如果是某个比较大的索引会导致节点1负载较大

```
1，2，3 //节点
a1,a2,a3
c1,c2,c3
b1,e1,e2 
b2,f1,fe
```

balance.index 就是尽量让同一个索引的不同分片分配到不同节点

**日志生产集群修改回默认值**

```
PUT _cluster/settings
{
   "persistent": {
    "cluster.routing.allocation.balance.shard": 0.45,
    "cluster.routing.allocation.balance.index": 0.55
  }
}
```

居然出现了三个分片全部分到一台机器：

![企业微信截图_53e9212f-2d72-4349-a56f-00b52eee4285](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_53e9212f-2d72-4349-a56f-00b52eee4285.png)

参数调整：

```
    "cluster.routing.allocation.balance.shard": 0.1,
    "cluster.routing.allocation.balance.index": 0.9
```

还是会导致两个分片分配到同一个节点：

![企业微信截图_a86fa96a-1d90-4b8e-be62-72ec2545bb74](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_a86fa96a-1d90-4b8e-be62-72ec2545bb74.png)

最终还是调整回：

```
"cluster.routing.allocation.balance.shard": 0.0,
"cluster.routing.allocation.balance.index": 1.0
```

调整回之后过了半个月，120.20这台机器load告警，查看分区分配情况，120.19和120.20都属于s3，但是分配的分片相差20倍。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-*****************.png" alt="image-*****************" style="zoom:50%;" />

查看w-k2-template模板配置，发现分配的r2、r3、r5，一共5台机器，而number_of_shards是2，尝试修改number_of_shards也为5，和机器数保持一致。



### 8、排查bulk流量来源

有时候想排查bulk是来自哪个ip？可以通过arthas排查

bulk底层入口是IngestService.executeBulkRequest()，可以先看下strack

```java
stack -n 2 org.elasticsearch.action.bulk.TransportBulkAction processBulkIndexIngestRequest
```

对于rest层，入口是Client.bulk()，直接tt

```
tt -t org.elasticsearch.client.support.AbstractClient bulk -n 10
```

查看数据

```properties
# 查看索引
tt -i 1012 -w params[0] -x 2

# 查看来源
tt -i 1012 -w params[1] -x 3
```



### 9、CircuitBreakingException异常

es7测试环境经常会遇到：

```properties
[2024-08-26T15:25:08,491][WARN ][o.e.c.a.s.ShardStateAction] [***********-d] unexpected failure while sending request [internal:cluster/shard/started] to [{***********-m}{FkuO2N6GSiOxKA7FFSc1cQ}] for shard entry [StartedShardEntry{shardId [[sw_cpu_thread_metrics-20240826][0]], allocationId [wag1acBCQl-TaNMpGguC0Q], primary term [12], message [after existing store recovery; bootstrap_history_uuid=false]}]
org.elasticsearch.transport.RemoteTransportException: [***********-m][***********:9301][internal:cluster/shard/started]
Caused by: org.elasticsearch.common.breaker.CircuitBreakingException: [parent] Data too large, data for [internal:cluster/shard/started] would be [**********/1.5gb], which is larger than the limit of [**********/1.5gb], real usage: [**********/1.5gb], new bytes reserved: [282/282b], usages [request=0/0b, fielddata=0/0b, in_flight_requests=1648274/1.5mb, model_inference=0/0b, accounting=0/0b]
    at org.elasticsearch.indices.breaker.HierarchyCircuitBreakerService.checkParentLimit(HierarchyCircuitBreakerService.java:335) ~[elasticsearch-7.11.2.jar:7.11.2]
    at org.elasticsearch.common.breaker.ChildMemoryCircuitBreaker.addEstimateBytesAndMaybeBreak(ChildMemoryCircuitBreaker.java:98) ~[elasticsearch-7.11.2.jar:7.11.2]
    at org.elasticsearch.transport.InboundAggregator.checkBreaker(InboundAggregator.java:200) ~[elasticsearch-7.11.2.jar:7.11.2]
    at org.elasticsearch.transport.InboundAggregator.finishAggregation(InboundAggregator.java:109) ~[elasticsearch-7.11.2.jar:7.11.2]
    at org.elasticsearch.transport.InboundPipeline.forwardFragments(InboundPipeline.java:129) [elasticsearch-7.11.2.jar:7.11.2]
    at org.elasticsearch.transport.InboundPipeline.doHandleBytes(InboundPipeline.java:106) [elasticsearch-7.11.2.jar:7.11.2]
    at org.elasticsearch.transport.InboundPipeline.handleBytes(InboundPipeline.java:71) [elasticsearch-7.11.2.jar:7.11.2]
    at org.elasticsearch.transport.netty4.Netty4MessageChannelHandler.channelRead(Netty4MessageChannelHandler.java:63) [transport-netty4-client-7.11.2.jar:7.11.2]
```

异常分析：

es 在io读取数据包时会调用HierarchyCircuitBreakerService.checkParentLimit()，代码如下：

```java
    public void checkParentLimit(long newBytesReserved, String label) throws CircuitBreakingException {
        final MemoryUsage memoryUsed = memoryUsed(newBytesReserved);//实时堆内存使用大小
        long parentLimit = this.parentSettings.getLimit(); //堆内存上限75%
        //判断内存是否超过parent的上限
        if (memoryUsed.totalUsage > parentLimit && overLimitStrategy.overLimit(memoryUsed).totalUsage > parentLimit) {
            //抛出异常
        }
    }
```

这里的parentSettings 是 BreakerSettings 限流配置，默认是堆内存上限75%

可以看到是master内存不足导致，对于master节点2g，data节点8g，测试环境发现当每个节点2500个分片基本会到达瓶颈

![企业微信截图_d29361db-c75b-4de8-a833-fc3eda267ade](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_d29361db-c75b-4de8-a833-fc3eda267ade.png)



### 10、异常NoShardAvailableActionException

异常NoShardAvailableActionException: [SERVICE_UNAVAILABLE/1/state not recovered

```
[2024-07-01T12:01:14,195][WARN ][r.suppressed             ] [*************] path: /.kibana_task_manager/_doc/_search, params: {ignore_unavailable=true, index=.kibana_task_manager, type=_doc}
org.elasticsearch.cluster.block.ClusterBlockException: blocked by: [SERVICE_UNAVAILABLE/1/state not recovered / initialized];
     at org.elasticsearch.cluster.block.ClusterBlocks.globalBlockedException(ClusterBlocks.java:191) ~[elasticsearch-6.8.23.jar:6.8.23]
     at org.elasticsearch.cluster.block.ClusterBlocks.globalBlockedRaiseException(ClusterBlocks.java:177) ~[elasticsearch-6.8.23.jar:6.8.23]
     at org.elasticsearch.action.search.TransportSearchAction.executeSearch(TransportSearchAction.java:299) ~[elasticsearch-6.8.23.jar:6.8.23]
     at org.elasticsearch.action.search.TransportSearchAction.lambda$doExecute$4(TransportSearchAction.java:195) ~[elasticsearch-6.8.23.jar:6.8.23]
     at org.elasticsearch.action.ActionListener$1.onResponse(ActionListener.java:62) ~[elasticsearch-6.8.23.jar:6.8.23]
     at org.elasticsearch.index.query.Rewriteable.rewriteAndFetch(Rewriteable.java:114) ~[elasticsearch-6.8.23.jar:6.8.23]
     at org.elasticsearch.index.query.Rewriteable.rewriteAndFetch(Rewriteable.java:87) ~[elasticsearch-6.8.23.jar:6.8.23]
     at org.elasticsearch.action.search.TransportSearchAction.doExecute(TransportSearchAction.java:217) ~[elasticsearch-6.8.23.jar:6.8.23]
```

这是一个警告，表示查询过程中发现分片还不可用，等待节点恢复，可以忽略

### 11、es节点IO使用率过高

收到告警：

```
[系统IO太高] 告警通知（持续 4m0s, 执行 3 轮）@10:36:38:【IO问题】com.wacai.ops/es: ***********的sdb IO使用率高达93.802%
```

遇到这类问题可以登录机器执行：

```
iostat -mdx 3
```

结果：

```
Device:         rrqm/s   wrqm/s     r/s     w/s    rMB/s    wMB/s avgrq-sz avgqu-sz   await r_await w_await  svctm  %util
fioa              0.00     0.00    0.00   25.33     0.00     3.73   301.89     0.00    0.78    0.00    0.78   0.00   0.00
sda               0.00     0.00    0.00    0.33     0.00     0.00     8.00     0.00   13.00    0.00   13.00  13.00   0.43
sdb               0.00     2.33    0.00  224.67     0.00    20.84   189.95   129.24  638.88    0.00  638.88   4.45 100.00
```

可以看到 sdb这块盘的性能比较差，每秒写入20MB，使用率就100%。

然后通过8864看这个节点是哪个索引写入较多，把它移走，问题解决。

### 12、时间字段的处理

对于日志收集，es是强烈建议时间时间字段带上市区信息，并且统一调整为 GMT/UTC（零时区）：2025-06-30T10:58:58Z

```
    @JsonFormat(shape = JsonFormat.Shape.STRING, pattern = "yyyy-MM-dd'T'HH:mm:ss'Z'", timezone = "GMT")
    private Date timestamp = new Date();
```

通过 Kibana 等工具查询时，如果时区设置为 Asia/Shanghai，会自动还原为 08:00。 如果你记录的是中国时区的时间在Kibana中查询反而会有问题



## 优化

### 支持 ZSTD Compressor 

lucene ZSTD 已经有[PR](https://github.com/apache/lucene/pull/439), 使用的是[zstd-jni实现](https://github.com/luben/zstd-jni) ，用法参考 ZstdCompressCtx 类， [网友实现版本](https://github.com/LuXugang/Lucene-7.x-9.x/issues/40)

- [lucene/issues/9784相关讨论](https://github.com/apache/lucene/issues/9784)
- [纯java实现的Zstandard压缩算法](https://github.com/airlift/aircompressor)
- [2023-滴滴落地ZSTD压缩算法的实践分享](https://armsword.com/2023/08/11/didi-es-zstd/)



## Next 日志平台

这里介绍ES的一些痛点。

### ES痛点

1、在Elasticsearch的倒排索引中，快速检索是**以写入速度、写入吞吐量和存储空间为代价**的。为什么？首先，tokenization、字典排序、倒排索引创建都是CPU和内存密集型操作。（**计算成本高**）

2、由于 Lucene 本身是为支持文件存储而构建的，因此它以面向行的格式存储数据。Elasticsearch 必须存储原始数据，倒排索引，以及存储在列中的额外数据副本以用于查询加速。那是三重冗余。单副本存储的压缩率整体被限制在 1.5 倍左右，远低于日志数据常见的 5 倍压缩比。 （**存储成本高**）

我看下es的空间消耗情况，测试环境 hermes-proxy有三台机器，2024-08-27一天原始日志 1118.6*3=**3355.8mb**，存入es之后的索引大小**1418mb**：

```
index                                             shard prirep state      docs   store ip            node
wke-w-wse-test-middleware_hermes-proxy-2024-08-27 1     p      STARTED 5199317 708.8mb ************* *************-d
wke-w-wse-test-middleware_hermes-proxy-2024-08-27 0     p      STARTED 5204593 710.4mb ************  ************
```

但是1118.6mb的日志如果通过tgz压缩只需要69M

### Doris

来源：

- [Apache Doris 构建新一代日志分析平台 SelectDB](https://www.infoq.cn/article/KLFciXowJFN6jppYx1xB)
- [jdon-Apache Doris](https://www.jdon.com/66728.html)

1、**写入吞吐提升**：Elasticsearch 写入的性能瓶颈在于解析数据和构建倒排索引的 CPU 消耗。相比之下，Doris 进行了两方面的写入优化：一方面利用 SIMD 等 CPU 向量化指令提升了 JSON 数据解析速度和索引构建性能；另一方面针对日志场景简化倒了排索引结构，去掉日志场景不需要的正排等数据结构，Apache Doris 以**单核 20MB/s 的速度写入数据是 Elasticsearch（5MB/s）的四倍**

2、**存储成本降低**：Elasticsearch 存储瓶颈在于正排、倒排、Docvalue 列存多份存储和通用压缩算法压缩率较低。相比之下，Doris 在存储上进行了以下优化：去掉正排，缩减了 30%的索引数据量；采用列式存储和 ZSTD 压缩算法，压缩比可达到 5-10 倍，远高于 Elasticsearch 的 1.5 倍；

### ClickHouse

来源[B站基于Clickhouse的下一代日志体系建设实践](https://cloud.tencent.com/developer/article/2143639) 

同一份日志在Elasticsearch, ClickHouse和ClickHouse(zstd)中的容量, 最终对比ES达到了 1:6



### GreptimeDB

- [ GreptimeDB Pipeline 设计与实现](https://zhuanlan.zhihu.com/p/5480361645) 和Legion ETL很类似，通过不同的processor组成pipeline依次处理

## 参考

- [大分片治理思路](http://dbaselife.com/project-16/doc-1489/)
- [Elasticsearch: 权威指南-rolling_restarts](https://www.elastic.co/guide/cn/elasticsearch/guide/current/_rolling_restarts.html#_rolling_restarts)
- [Elasticsearch 最佳实践系列之分片恢复并发故障](https://cloud.tencent.com/developer/article/1370318)