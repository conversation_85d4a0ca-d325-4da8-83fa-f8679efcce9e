## 问题

- 2G数据，256M内存怎么排序？
- 2个几十G的文件，给几十M内存，怎么快速找出两文件中相同的行？

## 排序思路

### sort命令来跑

### 使用BitMap

直接把文件读取缓存区，然后基于BitMap进行排序，详见BitMap。

### 外部排序

内存极少的情况下，利用分治策略，利用外存保存中间结果，再用多路归并来排序;

思路如下：

1、内存中维护一个极小的核心缓冲区memBuffer，将大文件bigdata按行读入，搜集到memBuffer满或者大文件读完时，对memBuffer中的数据调用内排进行排序，排序后将有序结果写入磁盘文件bigdata.xxx.part.sorted. 循环利用memBuffer直到大文件处理完毕，得到n个有序的磁盘文件：

2、现在有了n个有序的小文件，怎么合并成1个有序的大文件？把所有小文件读入内存，然后内排？(⊙o⊙)… no!

利用如下原理进行归并排序：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/算法/4.png" alt="4" style="zoom:50%;" />

我们举个简单的例子：

- 文件1：3,6,9 ；
- 文件2：2,4,8；
-  文件3：1,5,7

第一回合：文件1的最小值：3 , 排在文件1的第1行 文件2的最小值：2，排在文件2的第1行 文件3的最小值：1，排在文件3的第1行 那么，这3个文件中的最小值是：min(1,2,3) = 1 也就是说，最终大文件的当前最小值，是文件1、2、3的当前最小值的最小值，绕么？上面拿出了最小值1，写入大文件.

第二回合：文件1的最小值：3 , 排在文件1的第1行 文件2的最小值：2，排在文件2的第1行 文件3的最小值：5，排在文件3的第2行 那么，这3个文件中的最小值是：min(5,2,3) = 2 将2写入大文件.

也就是说，最小值属于哪个文件，那么就从哪个文件当中取下一行数据.（因为小文件内部有序，下一行数据代表了它当前的最小值）

最终的时间，跑了771秒，13分钟左右。这种方案适合内存有限的场景。

## 参考

- [5 亿整数的大文件，来排个序](https://cloud.tencent.com/developer/article/1592913)
- [[使用布隆过滤器（Bloom Filter）进行大数据量排序](https://my.oschina.net/bairrfhoinn/blog/209965)]

