## 初期，刷入门核心题

这是针对毫无经验的刷题选手的策略。如果已经有一定刷题心得，可以跳过这步。 

首先要给自己建立信心，我的策略是先把**最基础的入门题刷个30道左右**，熟悉coding的过程，找到刷题的感觉。 

这些是我筛选过的还不错的入门题，我愿称之为——**菜菜子必备的编程20题**： 

### 1.[整数排序](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/463/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 2.[反转一个3位整数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/37//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 3.[三数之中的最大值](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/283//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 4.[从不充值的玩家](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1921//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 5.[寻找素数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/298//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 6.[寻找最大值](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/297//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 7.[链表节点计数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/466//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 8.[矩阵面积](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/454//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 9.[打印X](https://www.lintcode.com/problem/25//?utm_source=sc-zhihuniming-sy0309-2) 

### 10.[分数超过组长的组员](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1919//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 11.[硬币翻面](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1927//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 12.[张三的故事](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1934//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 13.[寻找特定的患者](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1931//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 14.[挂科最多的同学](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1932//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 15.[查询用户邮箱](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1938//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 16.[增长的疫情感染人数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1923//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 17.[公租房信息查询](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1930//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 18.[查找重名的同学](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1920//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 19.[超过3名球员所得到的分数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1925//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

### 20.[推荐学理科的同学](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1924//%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

## 中期，按知识点tag刷题 

接下来就要真正的掌握算法和数据结构知识点。 

我的策略是逮着一个知识点使劲刷，刷到掌握了为止（不限题数）。 

但如果刷了30题以上还是不得其法，可以先放一放，不要给自己造成心理负担。 

**最让人头疼的动态规划，可以循序渐进的刷这10道题：** 

1.[栅栏染色](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/514/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[爬楼梯](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/111/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[约翰的后花园](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/749/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[单词拆分](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/683/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[书籍复印](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/437/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

6.[解码方法](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/512/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

7.[通配符匹配](https://link.zhihu.com/?target=http%3A//lintcode.com/problem/192/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

8.[旅行商问题](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/816/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

9.[青蛙跳](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/622/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

10.[骰子求和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/20/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**双指针算法，高频算法之王，变形比较多。想掌握的话，刷这些题：** 

1.[颜色分类](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/148/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[两数之和 III-数据结构设计](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/607/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[排颜色](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/143/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[最长子串覆盖](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/32/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[有效回文](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/891/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

6.[带环链表](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/102/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

7.[交错正负数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/144/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

8.[最接近的三数之和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/59/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

9.[四数之和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/58/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

10.[接雨水](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/363/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**宽度优先搜索，考察频率高，但实现不难，刷这7道题：** 

1.[岛屿的个数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/433/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[序列重构](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/605/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[拓扑排序](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/127/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[课程表](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/615/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[买卖股票的最佳时期](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/151/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

6.[安排课程](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/616/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

7.[最大子数组差](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/45/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**深度优先搜索，考察频率高，主要是考察递归会不会写。** 

1.[子集](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/17/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[图是否是树](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/178/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[子数组之和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/138/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[电话号码的字母组合](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/425/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[K数和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/90/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

6.[因式分解](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/652/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**分治法，考察频率中等，一般和二叉树一起出现和考察，题一般不难。** 

1.[子集](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/17/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[数组划分](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/31/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[验证二叉查找树](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/95/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[全排列](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/15/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[克隆图](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/137/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

6.[排颜色](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/143/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

7.[子数组之和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/138/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**哈希表，原理和应用都需要掌握，而且需要掌握代码实现。** 

1.[两数之和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/two-sum/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[最长回文串](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/longest-palindrome/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[两数组的交集](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/intersection-of-two-arrays/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[数据流中第一个唯一的数字](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/first-unique-number-in-data-stream/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[两数和 II-输入已排序的数组](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/two-sum-ii-input-array-is-sorted/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**堆，经常会用到，原理必须掌握。高频。** 

1.[丑数](https://link.zhihu.com/?target=http%3A//lintcode.com/problem/4/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[堆化](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/130/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[滑动窗口的中位数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/360/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[大楼轮廓](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/131/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[超级丑数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/518/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

6.[食物集合](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/964/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

7.[影际网络](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/808/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**贪心，考得不多，但起码要会用。** 

1.[会议室](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/919/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[俄罗斯套娃信封](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/602/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[最大乘积](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/304/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[加油站](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/187/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[最大子数组差](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/45/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**链表，中小公司考得多，大公司近年来考得少。题目一般不难，主要考察Reference。** 

1.[合并k个排序链表](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/104/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[数据流中第一个唯一的数字](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/685/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[带环链表](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/102/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[旋转链表](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/170/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[两个链表的交叉](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/380/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

6.[K组翻转链表](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/450/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**线段树，不太考。但当有的题目存在多种解法的时候，线段树可以帮忙降低思考难度。** 

1.[线段树的构造](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/201/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

2.[线段树的查询](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/202/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

3.[区间求和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/207/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

4.[区间最小数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/205/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

5.[我的日历](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1063/%3Futm_source%3Dzhihuniming-sy0309-2) 

6.[排序方案](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/290/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

7.[构造队列](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/998/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

8.[矩形面积](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1450/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

## 按公司ladder刷题

在准备面试前，我建议直接刷目标公司的高频题。熟悉这些公司的常考题、出题风格，会比漫无目的地乱刷效率高很多。 

**阿里巴巴：** 

[牛牌](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/bull-cards/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[连接字符串](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/split-concatenated-strings/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[同和分割数组](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/split-array-with-equal-sum/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[有效的括号字符串](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/valid-parenthesis-string/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[路径和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/path-sum-iv/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[数组评分](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/array-score/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**字节跳动：** 

[删除字符](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/delete-char/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[矩阵中的最长递增路径](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/longest-increasing-path-in-a-matrix/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[恢复数组](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/restorearray/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[食物组合](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/food-set/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[组合新数字](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/combine-new-numbers/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[最大点的集合](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/maximum-points-set/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**腾讯：** 

[最短休息日](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/minimum-rest-days/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[解压字符串](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/decrypt-the-string/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[中位数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/median-ii/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[移除箱子](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/remove-boxes/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[最小分解](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/minimum-factorization/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[歌曲时间](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/song-time/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**百度：** 

[等差切片](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/arithmetic-slices/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[祖玛游戏](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/zuma-game/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[生命游戏](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/game-of-life/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[字符至少出现K次的最长子串](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/longest-substring-with-at-least-k-repeating-characters/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[整数替换](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/integer-replacement/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[拆分子数组](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/split-array-largest-sum/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**美团：** 

[考试策略](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/test-strategy/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[字符串划分](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/string-partition/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[区间极值异或](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/xorsum-of-interval-extremum/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[月份天数](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/the-months-days/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[关联查询](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/associated-query/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[字符删除](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/character-deletion/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

Google 

[第k大元素](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/kth-largest-element/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[LRU缓存策略](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/lru-cache/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[图是否是树](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/graph-valid-tree/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[序列重构](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/sequence-reconstruction/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[单词拆分 I](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/word-break/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[最多有k个不同字符的最长子字符串](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/longest-substring-with-at-most-k-distinct-characters/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**Facebook** 

[三数之和](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/3sum/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[颜色分类](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/sort-colors/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[单词接龙](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/word-ladder/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[验证二叉查找树](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/validate-binary-search-tree/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[搜索旋转排序数组](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/search-in-rotated-sorted-array/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**Microsoft** 

[买卖股票的最佳时机](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/best-time-to-buy-and-sell-stock/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[滑动窗口的最大值](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/sliding-window-maximum/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[二叉树的锯齿形层次遍历](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/binary-tree-zigzag-level-order-traversal/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[两个链表的交叉](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/intersection-of-two-linked-lists/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[复制带随机指针的链表](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/copy-list-with-random-pointer/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**Amazon** 

[最长回文串](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/627/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[课程表](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/615/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[最长无重复字符的子串](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/384/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[安排课程](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/616/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[飞行棋](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1565/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[两个链表的交叉](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/380/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**领英** 

[打劫房屋](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/392/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[识别名人](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/645/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[对称树](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1360/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[划分和相等的子集](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/588/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[最大栈](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/859/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

**Apple** 

[接雨水](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/363/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[反转整数](https://link.zhihu.com/?target=http%3A//lintcode.com/problem/413/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[勒索信](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1270/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[比较版本号](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1352/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[杨辉三角](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1355/%3Futm_source%3Dsc-zhihuniming-sy0309-2) 

[序列化和反序列N叉树](https://link.zhihu.com/?target=https%3A//www.lintcode.com/problem/1532/%3Futm_source%3Dsc-zhihuniming-sy0309-2)



## 参考

作者：匿名用户
链接：https://www.zhihu.com/question/302483921/answer/1770931984
来源：知乎
著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。


      