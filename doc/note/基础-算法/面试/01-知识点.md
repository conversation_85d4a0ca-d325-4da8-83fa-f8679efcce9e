## 基础

### 算法和数据结构

- mysql有哪些索引，底层数据结构是什么，画一下b+树的结构以及数据的插入过程，聚集索引的特点，如何做索引优化，给你一个sql语句select a,b,c from t where a<某个值 and  b>某个值 and c = 1,如何建索引(可以是c,a,b或者c,b, a的联合索引，但要分析什么场景下分别用哪个);
- LRU-cache 如何实现？
- hashmap的实现原理，要把1.7和1.8的区别(红黑树)讲出来，map有哪些实现类以及使用场景，hashmap, hashtable, linkedhashmap,weakHashMap, treemap, concurrentmap？

### Java 并发编程

- 线程有哪些状态以及怎么转换的？
- 线程池有哪些实现类，ThreadPoolExecutor有哪些参数，讲下它的工作原理，在使用过程中有哪些经验(比如工作队列大小的设置,ThreadFactory和ExjectExcutionHandler)？
- ConcurrentHashMap内部如何实现？
- 使用过那些j.u.c包中的类？
- 阻塞式队列如何实现？
- 两个线程之间通信哪些方式？
- Disruptor原理?

### JVM 底层知识

- GC的常用算法以及优缺点，项目中用的什么垃圾回收算法,怎么做GC调优,对G1收集器有没有了解(要说出G1的出现主要解决了什么问题,为什么CMS算法会有内存碎片)。在介绍GC算法的时候可能会提到GC Roots，面试官会问你哪些对象可以作为GC Roots，栈里面的局部变量表里有基本数据类型和引用类型，怎么找到引用类型;有个对象引用比如Hello,hello.sayHello()调用的时候怎么找到类里的sayHello()这个方法
- 遇到线上问题的排查方法
- Java类加载机制，为什么要双亲委派
- valotile 和synchronized 关键字的实现
- NIO主要改进有哪些？
- Reactor网络模型?

### 网络基础

- HTTP 协议。HTTP1.X和HTTP2.0有什么区别？KeepAlive如何实现？
- TCP 协议。TCP 协议的握手？IO_WAIT和CLOSE_WAIT状态是如何产生的？网络拥塞算法？
- Socket需要设置参数避免延迟？

### 安全

- 如何防止CSRF
- oAuth原理

### 数据库

- 结合Innodb的实现说下数据库的隔离级别，分别是怎么解决脏读、不可重复读和幻读取的；
- 对mysql最新版本的同步机制有没有了解,比如一个主节点和多个备份节点,mysql怎么保证可用性和性能；
- 事务相关；

## 中间件

### Spring

- Spring IOC的理解以及IOC容器的初始化过程；
- Spring里的Bean是保存在哪里的；
- BeanFactory和FactoryBean的区别；
- Spring AOP怎么实现的(需要讲出来JDK动态代理和CGLib动态代理优缺点)；
- 如果一个类的某个方法没有在接口里定义,这两种动态代理方式是怎么实现的；

### 微服务

-  项目里有用到限流，具体怎么做的？
-  如何实现分布式锁

### Zookeeper

### Dubbo

- Dubbo的Filter用过吗？内部是如何实现SPI的？
- Dubbo线程模型？
- 注册中心挂掉之后Dubbo是否还能调用？
- Dubbo容错策略？
- 认使用什么序列化框架，你知道的还有哪些？
- 服务上线怎么不影响旧版本？
- Dubbo 如何优雅停机？

### Netty

- 看你对Netty有了解，能不能讲下它的工作原理,netty里 ChannelInBoundHandler 和ChannelOutBoundHandler的区别，netty在读写数据的时候是先调用head还是tail handler,既然你知道NIO，有没有听说JDK epoll的BUG，Netty是怎么解决的,如何解决TCP粘包和拆包的问题,为什么会出现粘包和拆包。


### Kfaka和RocketMQ

### Redis

- redis有哪些数据结构，分别用在哪些场景,对redis的pipeline有没有了解，解决什么问题,redis采用了什么方式来节省内存？

### ElasticSearch

- 项目里有用到ElasticSearch，能介绍ES的工作原理吗,什么是正向索引和反向索引，如何对ES进行优化

## 云原生

### Docker

### k8s

### ServiceMesh

## 综合能力

- 如果有个节点一直在做FullGC，你要怎么快速定位并恢复环境
- 如果要做一个秒杀系统，你会怎么设计，把能想到的点都说出来
- 用过哪些消息中间件,能不能说下Kafka的工作原理，如果保证分布式系统的数据的一致性,给Kafka发消息如何保证消息的幂等性
- ElasticSearch的分片机制,ES搜索过程是如何工作的,如果有很多网卡信息怎么做分片，如果一个Document有2个网卡呢



作者：我要去头条
链接：https://zhuanlan.zhihu.com/p/70610296
来源：知乎
著作权归作者所有。商业转载请联系作者获得授权，非商业转载请注明出处。



## 拼多多爬虫工程师面试题 

## **电话面：**

- http协议、tcp协议(几次握手)
- top命令
- Linu/Mac 下虚拟内存（Swap）
- 线程、进程、协程
- Async 相关、事件驱动相关
- 阻塞、非阻塞
- Python GIL
- 布隆过滤器原理：如何实现、一般要几次哈希函数

给我留下了一个作业：抓取天猫超市上某些商品的可以配送省份信息。（当时做这个也花了很久，主要是需要解决PC端的登陆问题，后来通过h5接口）

##  现场面（3小时）： 一面（技术）： 

一面是之前电话面的，主要问了之前布置的作业相关

1. 问了下之前留给我的作业，各种详细的细节，每一步怎么做的，遇到了哪些问题，自己是怎么解决的
2. 说了下淘宝登陆的两种方法，自己写的一些中间件
3. 还问了些之前的项目细节，爬虫资源配置怎么做的
4. 就我简历上的东西问了下底层的东西：线程与进程，协程用的Linux底层的是什么技术，事件驱动，MySQL的索引底层是什么，查询怎么做的等等。（这些问题都不知道）
5. 验证码如何处理，TensorFlow训练成功率多少
6. redis快的原因是什么，底层原因，你平常用到了哪些数据结构
7. 逆向是怎么学的，该怎么做，做过的项目，解决的签名
8. 爬虫失败之后的重试是如何处理的，有没有针对具体的失败去做针对性的报警和重试

## 二面（技术）：


二面感觉是做爬虫的，挑了两个项目问其中的细节。说项目遇到了两个问题，我是怎么解决的，当时解释这个项目时花了很久。
还问了有没有阅读国内爬虫大厂、搜索引擎等开源的爬虫框架、思想之类的。这个好像真没了解，也没看到有谁会写这个。

##  三面（小组长）：


三面人感觉很和善，爱笑。后来通过HR了解到是这个团队的组长，从腾讯微信挖来的。

- 他主要问了一个问题，给我一个数据抓取场景，让我设计一套数据抓取流程。我按着自己的思路画了出来，然后他会提出一些新需求，我按照他的意思在原有的爬虫系统上改进。
- 之后提的一个新需求我并没有回答好，但是他当时说：没事，已经回答的很好了。我心里想，leader很nice。
- 最后他介绍了他们团队目前正在做的事情，爬虫框架其实已经搭起来了，只是会有些粗糙，还需要有人来改进

## 参考

- [Java核心知识](https://github.com/crossoverJie/JCSprout)

- [玄翦博客](])

- [抖音、腾讯、阿里、美团春招服务端开发岗位硬核面试](https://www.jianshu.com/p/504872f66647)

- [抖音、腾讯、阿里、美团春招服务端开发岗位硬核面试（二）](https://www.jianshu.com/p/28f370e6ed70)

- [后端架构师技术图谱](https://github.com/xingshaocheng/architect-awesome)
