## 概述

曾在 Facebook 和微软工作过的 Educative.io 创始人 Fahim ul Haq 近日发文总结了**编程面试**所遇到的问题的 14 种最常见的模式，也许能帮你看清各种编程面试问题「背后的真相」。

这里我将列出最常见的 14 种模式，它们可被用于解决任何编程面试问题。

另外我还会说明如何识别每种模式，并会为每种模式提供一些问题示例。

这些内容都只是蜻蜓点水——**我强烈建议你看看课程《Grokking the Coding Interview: Patterns for Coding Questions》，里面提供了全面的解释、示例和编程实践**。

下面的模式说明假设你已经知悉了数据结构。如果你还不了解，那需要补充一下知识点哦。

我们今天将说明以下 14 种模式：

> - 1．滑动窗口
> - 2．二指针或迭代器
> - 3．快速和慢速指针或迭代器
> - 4．合并区间
> - 5．循环排序
> - 6．原地反转链表
> - 7．树的宽度优先搜索（Tree BFS）
> - 8．树的深度优先搜索（Tree DFS）
> - 9．Two Heaps
> - 10．子集
> - 11．经过修改的二叉搜索
> - 12．前 K 个元素
> - 13．K 路合并
> - 14．拓扑排序

我们开始吧！

## 1．滑动窗口

滑动窗口类型的题目经常是用来执行数组或是链表上某个区间上的操作。比如找**最长的全为1的子数组长度**。

滑动窗口一般从第一个元素开始，一直往右边一个一个元素挪动。当然了，根据题目要求，我们可能有固定窗口大小的情况，也有窗口的大小变化的情况。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/算法/6.png" alt="6" style="zoom:50%;" />

下面**是一些我们用来判断我们可能需要上滑动窗口策略的方法**：

- 这个问题的输入是一些线性结构：比如链表呀，数组啊，字符串啊之类的
- 让你去求最长/最短子字符串或是某些特定的长度要求

经典题目

- Maximum Sum Subarray of Size K (easy)
- Smallest Subarray with a given sum (easy)
- Longest Substring with K Distinct Characters (medium)
- Fruits into Baskets (medium)
- No-repeat Substring (hard)
- Longest Substring with Same Letters after Replacement (hard)
- Longest Subarray with Ones after Replacement (hard)
- 寻找字符相同但排序不一样的字符串（困难）
- 带有 K 个不同字符的最长子字符串（中等）

## 2．双指针

双指针是这样的模式：两个指针朝着左右方向移动（双指针分为同向双指针和异向双指针），直到他们有一个或是两个都满足某种条件。双指针通常用在排好序的数组或是链表中寻找对子。比如，你需要去比较数组中每个元素和其他元素的关系时，你就需要用到双指针了。

我们需要双指针的原因是：如果你只用一个指针的话，你得来回跑才能在数组中找到你需要的答案。这一个指针来来回回的过程就很耗时和浪费空间了 — 这是考虑算法的复杂度分析的时候的重要概念。虽然brute force一个指针的解法可能会奏效，但时间复杂度一般会是O(n²)。在很多情况下，双指针能帮助我们找到空间或是时间复杂度更低的解。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/算法/5.png" alt="5" style="zoom:50%;" />

经典题目

- 3Sum
- 求一个排序数组的平方
- [27.移除元素](https://mp.weixin.qq.com/s?__biz=MzUxNjY5NTYxNA==&mid=2247484304&idx=1&sn=ad2e11d171f74ad772fd23b10142e3f3)
- 比较包含回退（backspace）的字符串
- [206.反转链表](https://mp.weixin.qq.com/s?__biz=MzUxNjY5NTYxNA==&mid=2247484158&idx=1&sn=60a756f681e2edeab28962c70b603ef9&scene=21#wechat_redirect)

## 3、快慢指针类型

**这种模式，有一个非常出门的名字，叫龟兔赛跑**。咱们肯定都知道龟兔赛跑啦。但还是再解释一下快慢指针：这种算法的两个指针的在数组上（或是链表上，序列上）的移动速度不一样。通过控制指针不同的移动速度（比如在环形链表上），这种算法证明了他们肯定会相遇的。快的一个指针肯定会追上慢的一个（可以想象成跑道上面跑得快的人套圈跑得慢的人）。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/算法/7.png" alt="7" style="zoom:50%;" />

经典题目

- Remove Duplicates

## 参考

- [why哥算法最强的时候，就是准备面试的时候](https://mp.weixin.qq.com/s/g3fZvqTCjOHfERfwllzuHw)
- [Leetcode刷题顺序，看这一篇就够了](https://zhuanlan.zhihu.com/p/161036474)