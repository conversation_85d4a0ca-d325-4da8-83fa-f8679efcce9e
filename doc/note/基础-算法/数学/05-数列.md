## 等差数列

一次数学课上，老师让学生练习算数。于是让他们一个小时内算出1+2+3+4+5+6+……+100的得数。全班只有高斯用了不到20分钟给出了答案，因为他想到了用（1+100）+（2+99）+（3+98）……+（50+51）……一共有50个101，所以50×101就是1加到一百的得数，后来人们把这种简便算法称作高斯求和算法，算法为：

```
首项加末项乘以项数,再除以2
```

比如数列1，2，3，4，5，6，7，8，9=(1+9)*9/2=45

### 规律

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210531202722844.png" alt="image-20210531202722844" style="zoom:50%;" />

### 问题

![image-20210531203210376](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210531203210376.png)



## 等比数列

### 求和公式推导

![image-20210531204503291](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210531204503291.png)

q为公比，比如1,2,4,8数列，q=2。

## 参考

- [等差數列和等比數列](https://www.youtube.com/watch?v=tSN7teeeA5s)