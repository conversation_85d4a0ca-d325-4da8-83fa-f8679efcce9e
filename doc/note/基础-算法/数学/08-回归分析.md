## 什么是回归

我们先来聊聊历史，从回归这个词被发明的源头聊起。话说有一个叫高尔顿的生物学家兼统计学家在研究人类遗传问题时发现了一个现象：非常高的父亲，其儿子的身高往往要比父亲矮一点，而非常矮的父亲，儿子的身高也会比父亲高一些，也就是说，**人类的身高从高矮两个极端移向所有人的平均值，他把这种现象称为“向平均回归**（regression to the mean）”。

![image-20230302215911017](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230302215911017.png)

其实仔细想想这种现象应该是正常的才对，如果不发生这种向平均值回归的事情，那么高的人后代将越来越高，同样矮的人的后代会越来越矮，那么经过一系列的种族繁衍后，人类将变成特别高和特别矮的两极分化状态。

这是回归这个词的由来，所以我们再来理解一下什么是回归分析，首先我要去分析两个现象之间有什么关系，然后我要知道现象之间的具体形式，并用数学表达式来展示。比如上次的相关性分析中我们说到了城市化水平和离婚率之间存在着相关关系，那么这两个变量之间的关系到底深到什么程度，是谁在影响谁，这就需要我们用函数定量地去描述，这就是回归。

在说相关性的时候，我们会把两个变量之间的关系用散点图来展示，更进一步地，还会去找到一条最合适的平均线，也就是“向平均回归的线”，而这条线的函数表达式，就是我们说的回归方程，所以说，回归分析要寻找的就是变量之间的最佳拟合关系。


## 什么是方差

待补充

## 参考

- [细说回归分析](https://blog.csdn.net/data_cola/article/details/106971712)