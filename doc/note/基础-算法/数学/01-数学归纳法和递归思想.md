## 数学归纳法

计算机中迭代和递归是两种重要的编程思想，而这一思想和数学归纳法中息息相关，那么什么是数学归纳法？数学归纳和我们日常生活中根据经验总结得出的归纳是一个意思吗？其实并不是，那么什么是数学归纳法呢？

我们想证明一个结论，这个结论是于自然数有关的，那么我们可以通过这样的两个步骤来证明:

1. 证明 n=1 时结论成立。
2. 若 n=k 时结论成立，证明 n=k+1 也成立。

以上方法被称之为数学归纳法。推导过程：

>首先证明了第一个数结论成立，然后假如前一个数结论成立，后一个数结论也成立，所以可以从第一个数成立推出第二个成立，第二个成立推出第三个成立，第三个成立推出第四个成立，以此类推所有的自然数都是成立的。

数学归纳法非常像多米诺骨牌，如果你可以：

1. 证明第一张骨牌会倒。
2. 证明只要任意一张骨牌倒了，那么与其相邻的下一张骨牌也会倒。

那么便可以下结论：所有的骨牌都会倒下。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211005164713409.png" alt="image-20211005164713409" style="zoom:50%;" />



下面来给出一些例子，尝试通过数学归纳法来证明。

### 证明 1+3+5+...+2n-1 = n<sup>2</sup>

解释：

> 上述式子是求前项奇数之和等于n的平方，n表示自然数中第几个的奇数，第n位奇数的值等于2n-1，比如如第3位奇数值等于2x3-1=5，第5位奇数值等于9，第10位奇数值等于19。
>
> 若n=1, 1=1
> 若n=2, 1+3=4
> 若n=3, 1+3+5=9
> 若n=4, 1+3+5+7=16

现在来使用数据归纳法来证明上述结论。

- 当n=1。 左边1，右边1<sup>2</sup>=1，结论成立。
- 若n=k成立，也就是说 **1+3+..+(2k-1)**=k<sup>2</sup>假设成立，若 n=k+1时，左边 **1+3+..+(2k-1)**+2k+1，根据前面n=k的假设，左边等于 k<sup>2</sup>+2k+1
-  根据完全平方公式，k<sup>2</sup>+2k+1 = (k+1)<sup>2</sup>

> 注：完全平方公式 a<sup>2</sup>+2ab+b = (a+b)<sup>2</sup>



### 计算棋盘麦粒数量的问题

在棋盘第1格放1粒麦子，第2格放2粒麦子，第3格放4粒麦子，以此类推得到1,2,4,8,16,32的数列，问前64格的麦粒总数 ？

| 前n格麦子数   | 1    | 2    | 4    | 8    | 16   |
| ------------- | ---- | ---- | ---- | ---- | ---- |
| 前n格麦子数和 | 1    | 3    | 7    | 15   | 31   |

观察数字规律前n格麦子数和貌似等于2<sup>n</sup>-1 ，如何用数学归纳法来验证这个结论呢？步骤如下：

- 证明n=1，命题是否成立。
- 若 n=k 时结论成立，证明 n=k+1 也成立。注：也可以若 n=k-1 时结论成立，证明 n=k 也成立

证明过程：

> n=1时，2**1-1=1，命题成立。
>
> 假设 n=k时，1+2..+2<sup>k-1</sup> = 2<sup>k</sup>-1成立，若 n=k+1时，则左边变形为：1+2+..+2<sup>k-1</sup>+2<sup>k</sup>，根据前面n=k的假设，转化为2<sup>k</sup>-1+2<sup>k</sup>=2<sup>(k+1)</sup> 证明结论成立



### 代码实现

循环不变式主要用来辅助我们理解算法的正确性，对于循环不变式，必须证明它的三个性质

- 初始化：它在循环的第一轮迭代开始之前，应该是正确的。
- 保持：如果在某一次循环迭代开始之前是正确的，那么在下一次迭代开始之前，它也应该保持正确（假设当循环变量等于k时符合，再看执行一遍循环体后是否还符合循环不变式）。
- 结束：当循环结束时，不变式给了我们一个有用的性质，它有助于表明算法是正确的（这一步是和数学归纳法不同的一点，用循环不变式则更进一步，数学归纳法到这里就得出了一个关系式就结束，而用循环不变式，不但要先确保一个正确的关系式，还要看最后循环结束时，循环变量最后等于多少，根据循环不变式推导是否符合自己的要求。）。

编写循环时，让每次循环都成立的逻辑表达式称为循环不变式（loop invariant）。

接下来就[归并排序（Merge sort）](https://link.jianshu.com?t=https://github.com/geeklok/algorithms/blob/master/sorting/merge_sort.c)中的 merge 函数来说明一下循环不变量：

```c
1: int* merge(int* sld, int* srd, int lc, int rc)
2: {
3:     int tc = lc + rc;
4:     int* md = (int*) malloc(sizeof(int) * (tc));
5: 
6:     int li = 0; // 左数据当前元素索引
7:     int ri = 0; // 右数据当前元素索引
8:     for (int i = 0; i < tc; ++i)
9:     {
10:         if (li >= lc) //左数组数据已取完
11:             md[i] = srd[ri++];
12:         else if (ri >= rc) //右数组数据已取完
13:             md[i] = sld[li++];
14:         else if (sld[li] <= srd[ri]) //左右数据均未取完，取各自当前元素比较
15:             md[i] = sld[li++];
16:         else
17:             md[i] = srd[ri++];
18:     }
19: 
20:     return md;
21: }
```



先解释一下这个函数的作用，sld 和 srd 为已排序数组，大小分别为 lc 和 rc，循环 tc (lc + rc) 次把它们的元素进行比较并复制到新分配的数组 md 中，那要怎么证明这个算法的正确性呢。

让我们先设定循环不变量，然后看8-18行的循环能否在以上3种情况都满足这个循环不变量。

1. 初始化
    此时 i=0，li=0，ri=0，因此 md[0)，sld[0)，srd[0) 的大小均为0，满足循环不变量。

2. 迭代

    每次迭代都必须比较 sld[li] 和 srd[lr] 将较小的复制到 md[i] ，此时 md[0-i] 为 md[0-i) + min( sld[li], srd[ri] )，并将li 或 lr 增 1， 将 i 增 1 满足循环不变量。

3. 结束
    循环终止条件为 i>=tc (lc + rc) ，此时 md[0-tc) 中的元素为 sld[0-lc) 和 srd[0-rc) 元素之和排序后的结果。

结束时循环不变量给了我们一个有用信息，此时 md 已经把 sld 和 srd 中全部元素合并排序了， 从而证明了算法的正确性。



## 递归

### 汉诺塔

理解递归可以通过一个经典的数学问题-汉诺塔游戏来理解。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211006155418125.png" alt="image-20211006155418125" style="zoom:50%;" />

汉诺塔游戏是在1883年由法国数学家卢卡斯发明的，关于这个游戏有一个传说，传说在佛教里有一个神叫梵天，梵天在一个庙宇建造了三根柱子，第一个柱子有64层金片，由一个僧侣在不停的移动这些金片，而移动是有规则的：

- 每次只能移动一片；
- 必须保证每个柱子都是小片在上，大片在下。

当僧侣移动完金片，世界将会毁灭。那么64层金片移动完需要多少步骤呢？

我们先看如何移动4层原片的汉诺塔：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211006160632950.png" alt="image-20211006160632950" style="zoom:50%;" />

观察可以发现只需要3个步骤：

- 把上面3层移动到B;
- 把最下1层移动到C;
- 把上面3层移动到C;

那如何移动上面三层？可以按照上面的思想继续分解:

- 把上面2层移动到B;
- 把最下1层移动到C;
- 把上面2层移动到C;

 当只剩两层时问题就很好解决了：

- 把顶层移动到B;
- 把底层移动到C;
- 把顶层移动到C;

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211006161416336.png" alt="image-20211006161416336" style="zoom:50%;" />

 所以要移动8个圆盘就得先想办法移动7个，移动7个得先想办法移动6个，移动6个得先想办法移动5个，最终我们会把这个问题变成需要移动1个圆盘的问题。**这种把问题拆解为子问题再求解**的思想在数学上称之为递归。把上述过程写下来大概如下

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211006163324775.png" alt="image-20211006163324775" style="zoom:50%;" />

回到前面移动梵天移动64层金片需要多少步骤的问题？观察上面的图可以发现移动n层金片需要的步骤等于

```
F(n) = 2F(n-1)+1 
```

这个式子称之为递推式。我们可以带入数字:

- F(1) = 1 
- F(2) = 3
- F(3) = 7

 观察上述式子:

- 1 = 2<sup>1</sup>-1
- 3= 2<sup>2</sup>-1
- 7= 2<sup>3</sup>-1

得出结论: F(n) = 2F(n-1)+1 => F(n) = 2<sup>n</sup>-1

### 计算n格麦粒数量

前面我们通过数学归纳法找到计算前n格麦粒和等于2<sup>n</sup>，这个问题也可以通过递归的思想来解决。比如要计算前8格的总数只需要计算第8个的麦粒数+前7格的总麦粒数，而要计算前7格的的总数则需要计算第7格+前6格的总数，要计算n格的总数需要计算第n格的麦粒数+n-1格的总数，代码如下：

```java
/**
 * 计算给定格子数的麦粒数
 */
public class GridSum {

	public static int sum(int gird) {
		//边界条件
		if (gird == 1) {
			return 1;
		}
		//总数等于当前值 2^(n-1) + 前一格之和
		int sum = (1 << (gird - 1)) + sum(gird - 1);
		return sum;
	}

	public static void main(String[] args) {
		int sum = sum(5);
		System.out.println(sum);

		int sum2 = sum(10);
		System.out.println(sum2);
	}
}
```

### 计算2的n次方

同理，可以通过递归来计算2的n次方。思路：要计算2的10次方，计算2的9次方*2。

```java
	public int pow2n(int n) {
		if (n == 1) {
			return 2;
		}
		return 2 * pow2n(n - 1);
	}
```



### 有1,2,5,10的面额凑出10元，求有多少种组合方式?

组合的方式可以有10张1元，5张2元，2张5元等等，这个问题的难点在不能漏掉任何一种方式，所以必须得到1,2,5,10的全组合，再进行比较可能性，比如比较1和2组合的可能性:

| 1\2  | 0    | 1    | 2    | 3    | 4    | 5    | 6    | 7    | 8    | 9    |
| ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- | ---- |
| 1    | x    | x    | x    | x    | x    | x    | x    | x    | x    | x    |
| 2    | x    | x    | x    | x    | √    | x    | x    | x    | x    | x    |
| 3    | x    | x    | x    | x    | x    | x    | x    | x    | x    | x    |
| 4    | x    | x    | x    | √    | x    | x    | x    | x    | x    | x    |
| 5    | x    | x    | x    | x    | x    | x    | x    | x    | x    | x    |
| 6    | x    | x    | √    | x    | x    | x    | x    | x    | x    | x    |
| 7    | x    | x    | x    | x    | x    | x    | x    | x    | x    | x    |
| 8    | x    | √    | x    | x    | x    | x    | x    | x    | x    | x    |
| 9    | x    | x    | x    | x    | x    | x    | x    | x    | x    | x    |
| 10   | √    | x    | x    | x    | x    | x    | x    | x    | x    | x    |

1和2能凑成10元的取值：

- 排除2超过4的取值，即表格X大于4的全为x
- 排除1为奇数的取值，即表格Y为1,3,7,9全为x

最终的结论，1和2之间能凑成10元的排列

- 1x2+2x4
- 1x4+2X3
- 1X6+2X2
- 1X8+2X1
- 1X10+2X0

按照这种方式还要依次比较(1,5)，(1,10)，(2,5)，(1,2,5)等情况，因为要保存很多中间状态可以发现非常计算量非常大。

回忆前面的数学归纳法考虑了两种情况：

- 初始状态n=1时，命题是否成立。

- 如果n=k-1时，命题成立。那么只要证明n=k时，命题也成立。k为大于1的自然数。

将上述两点顺序调换，我们可以写出以下递推关系。

- 假设n=k-1时，问题已解，只要求解n=k时问题如何解决 。
- 初始状态,n=1时，问题如何解决。

回到当前问题，我们再把这种思想具体化：

- 假设n=k-1时，我们已知道如何去求奖赏的组合，你们只要求解n=k时会有哪些金额的选择，以及每种选择后还剩多少奖金需要支付就可以了。
- 初始状态，也就是n=1时，会有多少种奖赏。

用图来表示如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211007104528317.png" alt="image-20211007104528317" style="zoom:50%;" />

理解了这种思路代码不难写出：

```java
	static void get_reward_combination(List<Integer> rewards, int total_reward, List<Integer> selection) {
		// 满足总奖赏额度的条件，输出选择的金额
		if (total_reward == 0) {
			System.out.println(selection);
			return;
			//不满足总奖赏额度的条件，不输出	
		} else if (total_reward < 0) {
			return;
		} else {//尚未达到总奖赏额度
			for (Integer each : rewards) {
				List<Integer> newSelection = new ArrayList<Integer>(selection);
				newSelection.add(each);
				get_reward_combination(rewards, total_reward - each, newSelection);
			}
		}
	}
```

可以发现递归的关键的是找出递推式子。

### 全排列

[leetcode-46](https://leetcode-cn.com/problems/permutations/)的题目：给定一个不含重复数字的数组 `nums` ，返回其所有可能的全排列 。

比如

```
输入: [1,2,3]
输出: [1,2,3],[1,3,2],[2,1,3],[2,3,1],[3,1,2],[3,2,1]
```

思路和前面的思路一样。要找出n的全排列:

- 假设n=k-1时，我们已知道如何去找全排列，你们只要求解n=k时会有哪些的选择，以及每种选择后可能的排列方式就可以了
- 初始状态，也就是n=1时，排列是什么。



如图所示：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211007170900791.png" alt="image-20211007170900791" style="zoom:60%;" />

对于[1,2,3]的数组，第一步我们先选了1，那么还剩[2,3]，[2,3]继续拆解先选2，得到[1,2,3]，[2,3]选3，得到[1,3,2]。

代码如下:

```java
	/**
	 * 全排列
	 */
	public void permutations(List<Integer> nums, List<Integer> selections) {
		//中止条件，nums无法再排列时，打印选择的selections
		if (nums.isEmpty()) {
			System.out.println(selections);
			return;
		}
		for (Integer each : nums) {
			List<Integer> copyNums = new ArrayList<Integer>(nums);
			copyNums.remove(each);
			List<Integer> copySelections = new ArrayList<Integer>(selections);
			copySelections.add(each);
			permutations(copyNums, copySelections);
		}
	}
```



### 小结

#### 1、什么是递归

递归是一种非常高效、简洁的编码技巧，一种应用非常广泛的算法，比如DFS深度优先搜索、前中后序二叉树遍历等都是使用递归。方法或函数调用自身的方式称为递归调用，调用称为递，返回称为归。

基本上，所有的递归问题都可以用递推公式来表示，比如

- f(n) = f(n-1) + 1;  //汉诺塔问题
- f(n) = f(n-1) + f(n-2); //斐波拉数列
- f(n)=n*f(n-1);

#### 2、为什么使用递归？递归的优缺点？

- 优点：代码的表达力很强，写起来简洁。
- 缺点：空间复杂度高、有堆栈溢出风险、存在重复计算、过多的函数调用会耗时较多等问题。

#### 3、什么样的问题可以用递归解决呢？

一个问题只要同时满足以下3个条件，就可以用递归来解决：

- 问题的解可以分解为几个子问题的解。何为子问题？就是数据规模更小的问题。
- 问题与子问题，除了数据规模不同，求解思路完全一样
- 存在递归终止条件。

#### 4、如何实现递归？

- 递归代码编写
    写递归代码的关键就是找到如何将大问题分解为小问题的规律，并且基于此写出递推公式，然后再推敲终止条件，最后将递推公式和终止条件翻译成代码。
- 2.递归代码理解
    对于递归代码，若试图想清楚整个递和归的过程，实际上是进入了一个思维误区。
    那该如何理解递归代码呢？如果一个问题A可以分解为若干个子问题B、C、D，你可以假设子问题B、C、D已经解决。而且，你只需要思考问题A与子问题B、C、D两层之间的关系即可，不需要一层层往下思考子问题与子子问题，子子问题与子子子问题之间的关系。屏蔽掉递归细节，这样子理解起来就简单多了。
    因此，理解递归代码，就把它抽象成一个递推公式，不用想一层层的调用关系，不要试图用人脑去分解递归的每个步骤。

#### 5、递归常见问题及解决方案

- 警惕堆栈溢出：可以声明一个全局变量来控制递归的深度，从而避免堆栈溢出。
- 警惕重复计算：通过某种数据结构来保存已经求解过的值，从而避免重复计算。

## 参考

- [李永乐老师讲数学归纳法和模糊数学](https://www.youtube.com/watch?v=NCP8AWINxAo)
- [牛客网-拼凑面额](https://www.nowcoder.com/questionTerminal/14cf13771cd840849a402b848b5c1c93)
- [数组：每次遇到二分法，都是一看就会，一写就废](https://mp.weixin.qq.com/s?__biz=MzUxNjY5NTYxNA==&mid=2247484289&idx=1&sn=929fee0ac9f308a863a4fc4e2e44506e&scene=21#wechat_redirect)
- [算法的正确性证明方法一: 循环不变量](https://www.jianshu.com/p/c202410f7ff2)

