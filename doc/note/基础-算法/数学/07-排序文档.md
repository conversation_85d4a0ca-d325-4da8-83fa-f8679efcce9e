## 概述

排序算法是《数据结构与算法》中最基本的算法之一，目前有 10 大排序算法主要可以分为两类

1、非线性时间排序算法：通过比较来决定元素间的相对次序，由于其时间复杂度不能突破O(nlogn)，因此称为非线性时间比较类排序。这类算法包括：冒泡排序、插入排序、选择排序、希尔排序、归并排序、快速排序、堆排序。

2、线性时间非比较类排序算法：不通过比较来决定元素间的相对次序，它可以突破基于比较排序的时间下界，以线性时间运行，因此称为线性时间非比较类排序。 这类算法包括：桶排序、计数排序、基数排序。不过，虽然这几种算法的时间复杂度比较低，但是他们对要排序的数据要求也比较苛刻。

![image-20230119160412897](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230119160412897.png)

针对排序算法，所谓稳定性就是说如果待排序的序列中存在值相等的元素，经过排序之后，相等元素之间原有的先后顺序不变。经过排序算法之后，如果值相等的前后顺序没有改变，那我们把这种排序算法叫做**稳定的排序算法**。



本文会对常用的排序算法做一个梳理。

## 冒泡排序

#TODO#20230119

## 插入排序

### 基本思想

插入排序的英文是Insertion Sort。首先来看一个问题。一个有序的数组，我们往里面添加一个新的数据后，如何继续保持数据有序呢？很简单，我们只要遍历数组，找到数据应该插入的位置将其插入即可。示意图如下

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230126010411462.png" alt="image-20230126010411462" style="zoom:50%;" />

**插入排序**如何借助上面的思想来实现排序的呢？

将数组的数据分为两个区间，**已排序区间**和**未排序区间**。初始已排序区间只有一个元素，就是数组的第一个元素。算法的核心思想就是，取未排序区间中的元素，在已排序区间中找到合适的位置将其插入，并保证已排序区间的数据一直有序。重复这个过程，直到未排序区间中元素为空，算法结束。

具体流程如下图所示：

输入的数组：**1**，**10**，5，4，2

1、初始状态，第一个元素是 1，插入 10

- 和插入值中上一个元素比较，1 比 10 小，正常插入，不需要交换元素位置；

2、第二轮，插入 5

- 10 比 5 大，10需要后移一位，数组变为1，10，10，4，2
- 1比 5 小，当前位置插入 5，数组变为1，5，10，4，2

3、第三轮、插入 4

- 10 比 4 大，10需要后移一位，数组变为1，5，10，10，2
- 5 比 4 大，5需要后移一位，数组变为1，5，5，10，2
- 1比 4 小，当前位置插入4，数组变为1，4，5，10，2

3、第四轮、插入 2

- 10 比 2 大，10需要后移一位，数组变为1，4，5，10，10
- 5 比 2大，5需要后移一位，数组变为1，4，5，5，10
- 4 比 2 大，5需要后移一位，数组变为1，4，4，5，10
- 1比 2 小，当前位置插入2，数组变为1，2，4，5，10



<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230119161521837.png" alt="image-20230119161521837" style="zoom:45%;" />



代码实现：

```java
 	public static void insertionSort(int[] nums) {
      if (nums.length < 2)
        return ;

      for (int i = 1; i < nums.length; ++i) {
        int value = nums[i]; //插入的元素
        int j = i - 1; //上个元素
        for (; j >= 0; --j) {
          if (nums[j] > value) {
            nums[j + 1] = nums[j];//当前元素后移一位
          }else{
            break; //找到位置可以跳出
          }
        }
        nums[j + 1]=value;//插入数字
      }
  }
```



## 参考

- [geeksforgeeks：Sorting Algorithms](https://www.geeksforgeeks.org/sorting-algorithms/?ref=lbp)
- [极客时间王争：为什么插入排序比冒泡排序更受欢迎？](https://time.geekbang.org/column/article/41802)
- [排序算法——冒泡排序VS插入排序](https://blog.csdn.net/qq_26545305/article/details/87988991)
- [张维鹏：冒泡排序VS插入排序](https://blog.csdn.net/a745233700/article/details/86683603)
- [张维鹏：十大排序算法小结](https://blog.csdn.net/a745233700/article/details/80794084)