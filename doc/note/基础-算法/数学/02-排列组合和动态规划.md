## 排列

### 定义

从 n 个不同元素中取出 m(1≤m≤n) 个不同元素，按照一定的顺序排成一列，这个过程叫排列 (permutation)。排列按照以下以下维度进行分类：

- 当出现 m=n 时叫全排列(all permutation)，否则当m小于n为非全排列。
- 如果选出的 m 个元素允许重复，这样的排列是可重复排列(permutation with repetition)，否则就是不可重复排列(permutation without repetition) 

按照以上两种维度，可以的得到以下4种类型：

|                   | 计算公式                | 理解                                                         | 例子                   |
| ----------------- | ----------------------- | ------------------------------------------------------------ | ---------------------- |
| 可重复-全排列     | n<sup>m</sup> (其中m=n) | 转换为树，表示有n个根节点，有m层的树;(其中m=n)               | 3选3，等于3 x 3 x 3    |
| 可重复-非排列     | n<sup>m</sup>           | 转换为树，表示有n个根节点，有m层的树;                        | 3选2，等于3 x 3        |
| 不可重复-全排列   | n!                      | 转换为树，表示有n个根节点，每一层比上一层减一，有m层的树(其中m=n) | 4选4，等于4 x 3 x 2 x1 |
| 不可重复-非全排列 | n(n-1)( n-2)...(n-m+1)  | 转换为树，表示有n个根节点，每一层比上一层减一，有m层的树;    | 4选2，等于4 x3         |

### 可重复排列

可重复排列就是选出的元素中允许重复，比如允许出现 a,a,b，这点类似密码，双色球，计算数字出现的排列组合。比如A,B,C三个元素选出三个元素(即m=n全排列情况)，我们来看可重复排列结果：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210403221456810.png" alt="image-20210403221456810" style="zoom:50%;" />

观察图可以发现，所有可能性可以通过一个树状结构表达，从树的根节点到叶子节点，每种路径就是一种排列。有多少个叶子节点就有多少种全排列。

可重复排列的计算规则为：n<sup>m</sup>，表示n为底的m次方。

如上图是从3个元素中选出3种，即是3<sup>3</sup>=27。对于非全排列，表示m小于n，如从3个元素中选择2个，即3<sup>2</sup>=9，可以理解为上面的树形结构没有最后一层。

### 不可重复排列

不可重复排列就是选出的元素中不允许重复，比如以田忌赛马为例，上中下马的排列，是不允许出现上上下的情况的。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210403223724768.png" alt="image-20210403223724768" style="zoom:50%;" />

对于 n 个元素的不可重复-全排列，排列数量的计算规则为：n x(n-1) x ( n-2)x...2x1，也就是n的阶乘。从图上看，每一层都比上一层减一，因为要避免重复，所以是 n x (n-1)

对于不可重复-非全排列的计算规则也很好理解，比如上一张图如果只选出两匹马，那么去掉最后一层树即可，所以计算规则为：n x(n-1) x ( n-2)x...x(n-m+1)，比如从6个挑4个的排列规则为: 6x5x4x3=360。

因为上述式子不是阶乘，在数学上会把这个公式转化为阶乘：
$$
n*(n-1)*(n-2)*...*(n-m+1)=\frac{n!}{(n-m)!}
$$
[百度百科](https://baike.baidu.com/item/%E6%8E%92%E5%88%97/7804523?fr=aladdin)中关于这个公式的推导过程如下：
$$
n*(n-1)*(n-2)*...*(n-m+1)=\frac{[n*(n-1)...(n-m+1)][(n-m)(n-m-1)...1)]}{(n-m)(n-m-1)...1}
$$


第一眼看到这个推导过程我是没看懂，为啥前面是乘法后面变成了一个分数呢？经过询问老婆了解到，数学经常有一种思路是，一个数字可以通过分子分母同时乘以一个变量来表示，如下：
$$
a = \frac{a*b}{b}
$$
仔细观察一下前面分子和分母相同部分为 (n-m)(n-m-1)...1，那么这样变形的目的是什么呢？仔细观察发现，通过变形分子变成了n!的定义，而分母变成了n-m!，带入具体的数字验证：
$$
A(6,2) = \frac{6!}{(6-2)!}=\frac{6*5*4*3*2*1}{4*3*2*1}=6*5
$$


经过前面的讨论可以总结为全排列是乘到1为止，而非全排列是乘到(n-m+1)为止，下图是4个原始中选出2个的排列，计算结果为：24/2=12

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210403232036533.png" alt="image-20210403232036533" style="zoom:50%;" />

- 当为4选2，为4*3=12
- 当为5选3，为5x4x3=60
- 当为6选4，为6x5x4x3=360

## 组合

“组合”，它比排列更常用，组合的英文是 Combination，因此在数学符号中用 C 表示，美国和英国教材中，也常用“长括号”表示组合数。

我们常见的 C 右边会跟两个数字（或字母），右下角的数字 n 表示总数，右上角的数字 m 表示抽出的个数。整个符号的意思是“从 n 个元素中，**不计顺序地**抽出 m 个元素”，可以读作“C n 抽 m”。那么，到底什么叫做不计顺序的？

前面的田忌赛马例子中马被分为了三种不同的等级，[上中下]，[中上下]，[下中上]代表了三种不同排列，这就叫顺序。如果我们只选3匹马，而不关心3匹马的出站顺序，那么这就是组合。

比如：班里有三名同学，选出两名代表参加年级会议有几种选法？



## 动态规划



## 参考

- [程序员的数学基础课-第三章]

