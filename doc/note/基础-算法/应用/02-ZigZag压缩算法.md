### ZigZag压缩算法

在绝大多数情况下，我们使用到的整数，往往是比较小的。比如，我们会记录一个用户的id、一本书的id、一个回复的数量等等。在绝大多数系统里面，他们都是一个小整数，就像1234、1024、100等。

而我们在系统之间进行通讯的时候，往往又需要以整型（int）或长整型（long）为基本的传输类型，他们在大多数系统中，以4Bytes和8Bytes来表示。这样，为了传输一个整型（int）1，我们需要传输00000000_00000000_00000000_00000001 32个bits，除了一位是有价值的1，其他全是基本无价值的0（此处发出一个声音：浪！费！啊！）。
那怎么办呢？牛逼的工程师想出了一个小而有趣的算法：zigzag！

这个算法具体的思想是怎么样的呢？

答案也很简单，比如：00000000_00000000_00000000_00000001这个数字，我们如果能只发送一位1或者一个字节00000001，是不是就将压缩很多额外的数据呢？



## 参考

- https://blog.csdn.net/mweibiao/article/details/83038191
- [开源实现omusubi](https://github.com/koron/omusubi)

