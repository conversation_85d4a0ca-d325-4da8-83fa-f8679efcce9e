### 概述
在juc中的ScheduledThreadPoolExecutor使用了优先级队列来管理定时任务，需要最先执行的任务优先级最高，这里来谈下优先级队列的实现机制


#### 优先级队列定义

优先级队列和通常的栈和队列一样，只不过里面的每一个元素都有一个”优先级”，在处理的时候，首先处理优先级最高的。如果两个元素具有相同的优先级，则按照他们插入到队列中的先后顺序处理。

优先级队列可以通过链表，数组，堆或者其他数据结构实现。


#### 优先级队列实现

最简单的优先级队列可以通过有序或者无序数组来实现，当要获取最大值的时候，对数组进行查找返回即可。代码实现起来也比较简单，这里就不列出来了。

- 如果使用无序数组，那么每一次插入的时候，直接在数组末尾插入即可，时间复杂度为O(1)，但是如果要获取最大值，或者最小值返回的话，则需要进行查找，这时时间复杂度为O(n)。

- 如果使用有序数组，那么每一次插入的时候，通过插入排序将元素放到正确的位置，时间复杂度为O(n)，但是如果要获取最大值的话，由于元素已经有序，直接返回数组末尾的 元素即可，所以时间复杂度为O(1).

所以采用普通的数组或者链表实现，无法使得插入和排序都达到比较好的时间复杂度。所以我们需要采用新的数据结构来实现。下面就开始介绍如何采用二叉堆(binary heap)来实现优先级队列

#### 实现
参考
http://x-wei.github.io/heap-summary.html