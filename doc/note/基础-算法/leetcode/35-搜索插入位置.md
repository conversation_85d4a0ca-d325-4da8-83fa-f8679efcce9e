## 编号35：搜索插入位置

给定一个排序数组和一个目标值，在数组中找到目标值，并返回其索引。如果目标值不存在于数组中，返回它将会被按顺序插入的位置。

你可以假设数组中无重复元素。

```
示例 1:
输入: [1,3,5,6], 5
输出: 2

示例 2:
输入: [1,3,5,6], 2
输出: 1

示例 3:
输入: [1,3,5,6], 7
输出: 4

示例 4:
输入: [1,3,5,6], 0
输出: 0
```

## 思路

这道题目不难，但是为什么通过率相对来说并不高呢，我理解是大家对边界处理的判断有所失误导致的。

这道题目，要在数组中插入目标值，无非是这四种情况。

- 目标值在数组所有元素之前
- 目标值等于数组中某一个元素
- 目标值插入数组中的位置
- 目标值在数组所有元素之后

这四种情况确认清楚了，就可以尝试解题了。

接下来我将从暴力的解法和二分法来讲解此题，也借此好好讲一讲二分查找法。

## 暴力解法

```c++
class Solution {
public:
    int searchInsert(vector<int>& nums, int target) {
        for (int i = 0; i < nums.size(); i++) {
        // 分别处理如下三种情况
        // 目标值在数组所有元素之前
        // 目标值等于数组中某一个元素  
        // 目标值插入数组中的位置 
            if (nums[i] >= target) { // 一旦发现大于或者等于target的num[i]，那么i就是我们要的结果
                return i;
            }
        }
        // 目标值在数组所有元素之后的情况 
        return nums.size(); // 如果target是最大的，或者 nums为空，则返回nums的长度
    }
};
```

时间复杂度：O(n)
空间复杂度：O(1)

## 二分法

既然暴力解法的时间复杂度是O(n)，就要尝试一下使用二分查找法。大家注意这道题目的前提是数组是有序数组，这也是使用二分查找的基础条件。以后大家**「只要看到面试题里给出的数组是有序数组，都可以想一想是否可以使用二分法。」**

同时题目还强调数组中无重复元素，因为一旦有重复元素，使用二分查找法返回的元素下表可能不是唯一的。

二分查找涉及的很多的边界条件，逻辑比较简单，就是写不好。相信很多同学对二分查找法中边界条件处理不好。例如到底是 `while(left < right)` 还是 `while(left <= right)`，到底是`right = middle`呢，还是要`right = middle - 1`呢？

这里弄不清楚主要是因为**「对区间的定义没有想清楚，这就是不变量」**。要在二分查找的过程中，保持不变量，这也就是**「循环不变量」** （感兴趣的同学可以查一查）。

##  二分法第一种写法

