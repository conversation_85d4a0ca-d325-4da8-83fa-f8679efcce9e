## 概述

> 来源 kirito [海量无序数据寻找第 K 大的数](https://www.cnkirito.moe/topk/)

原文提出了一个问题：如何在一个百万级无序的 long 数组中寻找第 K 大的数值。

有这么几种方案：

1. 排序法：但是排序单纯靠想也知道不是最优的方案，因为原文提出的问题中，仅仅需要找到第 K 大的数，排序方案却兴师动众把整个数组理顺了，没必要。
2. 针对一般的 top K 问题，一般都会默认 K 很小，所以一般的 top K 问题，可以选择使用堆来解决。但是如果 K 很大，比如K == N/2，对于海量数据而言，这显示是一笔非常大的开销，所以针对我比赛的场景，堆的方案可以直接 pass。
3. Quick Select。虽然原文中说了基本思想也贴出来的代码，但是老实说当时没看懂。又查了一些资料算是搞懂了。



## 快排

Quick Select 来源于Quick Order(快排)，所以最好的方式是先搞懂什么是快排。

快排的思想是这样的：

1. 随机选择一个元素作为pivot中心轴
2. 将小于 pivot 的放到左边
3. 将大于 pivot 的放到右边，将 pivot 放到中间
4. 分别对左右子序列重复前面三步操作



实现细节: 
1. pivot的选取，可以随机选取，也可以用固定规则，例如固定选组最左边的数
2. 使用l指针与r指针，不断向中间靠拢。若l所指的数值小于pivot，那么达到要求，不必交换该数位置；若l所指数值大于pivot，应与r所指数交换。若r所指数值大于pivot，那么达到要求，不必交换该数位置；若r所指数值小于pivot，那么应该与l所指数值进行交换



具体过程可以参考 B 站动画[快速排序算法](https://www.bilibili.com/video/BV1at411T75o)

这里给出代码：

```java
public static void quickSort(int[] array, int low, int high) {
        int left = low;
        int right = high;
        if (left >= right) {
            return;
        }
        int pivot = array[left];
        while (left < right) {
            //right向中间移动
            while (left < right && array[right] >= pivot) {
                right--;
            }
          	//这里的right<pivot，进行交换
            if (left < right) {
                array[left] = array[right];
            }
          	//left向中间移动
            while (left < right && array[left] <= pivot) {
                left++;
            }
          	//这里的left>pivot，进行交换
            if (left < right) {
                array[right] = array[left];
            }
          	//发生重合的位置
            if (left >= right) {
                array[left] = pivot;
            }
        }
        quickSort(array, low, right - 1);//左边部分递归
        quickSort(array, right + 1, high);//右边部分递归
    }
```



## 参考

- [海量无序数据寻找第 K 大的数](https://www.cnkirito.moe/topk/)
- [TopK问题的最优解-快速选择算法](https://zhuanlan.zhihu.com/p/64627590)
- [数据结构与算法之美](https://time.geekbang.org/column/article/41913)