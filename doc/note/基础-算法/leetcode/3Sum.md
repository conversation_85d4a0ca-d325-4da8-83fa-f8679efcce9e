## 题目

给定一个数组，要求在这个数组中找出 3 个数之和为 0 的所有组合。

示例:

```
给定
array nums = [-1, 0, 1, 2, -1, -4]
结果有
  [-1, 0, 1],
  [-1, -1, 2]

```

## 思路

3Sum使用双指针来解非常简单，本质是需要找到数组中满足 a+b+c=0 的序列。

首先将数组排序，然后有一层for循环，i从下表0的地方开始，同时定一个下表left 定义在i+1的位置上，定义下表right 在数组结尾的位置上。

依然还是在数组中找到 abc 使得a + b +c =0，我们这里相当于 a = nums[i] b = nums[left] c = nums[right]。

接下来如何移动left 和right呢？

 如果nums[i] + nums[left] + nums[right] > 0 就说明 此时三数之和大了，因为数组是排序后了，所以right下表就应该向左移动，这样才能让三数之和小一些。

如果 nums[i] + nums[left] + nums[right] < 0 说明 此时 三数之和小了，left 就向右移动，才能让三数之和大一些，直到left与right相遇为止。

时间复杂度：O(n^2)。

```java
public static List<List<Integer>> threeSum(int[] nums) {
		//必须先排序
		Arrays.sort(nums);
		 
		List<List<Integer>> result = new ArrayList<List<Integer>>();
		for (int i = 0; i < nums.length; ++i) {
			// 排序之后如果第一个元素已经大于零，那么无论如何组合都不可能凑成三元组，直接返回结果就可以了
			if (nums[i] > 0) {
				return result;
			}
			//错误的去重，数字第一次出现就被去重了导致少执行了一次
//			if (i < nums.length-1 && nums[i] == nums[i + 1]) {
//				continue;
//			}
			//正确的去重，去掉后面重复出现的值
			if (i > 0 && nums[i] == nums[i - 1]) {
				continue;
			}

			int left = i + 1;
			int right = nums.length - 1;
			while (right > left) {
				if (nums[i] + nums[left] + nums[right] > 0) {
					right--;
				} else if (nums[i] + nums[left] + nums[right] < 0) {
					left++;
				} else {
					result.add(Lists.newArrayList(nums[i], nums[left], nums[right]));
					// 去除临近相同的元素
					while (right > left && nums[right] == nums[right - 1])
						right--;
					while (right > left && nums[left] == nums[left + 1])
						left++;
					// 找到答案时，双指针同时收缩
					right--;
					left++;
				}
			}
		}
		return result;
	}

```



## 参考

- [3sum官网](https://leetcode-cn.com/problems/3sum/solution/15-san-shu-zhi-he-ha-xi-fa-shuang-zhi-zhen-fa-xi-2/)
- [3sum](https://books.halfrost.com/leetcode/ChapterFour/0001~0099/0015.3Sum/)