## 问题

给定一个排序数组，你需要在 原地 删除重复出现的元素，使得每个元素只出现一次，返回移除后数组的新长度。不要使用额外的数组空间，你必须在 原地 修改输入数组 并在使用 O(1) 额外空间的条件下完成。

```
给定 nums = [0,0,1,1,1,2,2,3,3,4],

函数应该返回新的长度 5, 并且原数组 nums 的前五个元素被修改为 0, 1, 2, 3, 4。

你不需要考虑数组中超出新长度后面的元素。
 
```

## 思路

快慢指针，一前一后，for 循环内部逐个比较，如果内容不同，先自增慢指针，然后设置快指针的内容到慢指针处。快指针无条件一直前移，直到遍历完所有元素，返回slow + 1

```java
public static int removeDuplicates(Integer[] nums) {
    // 定义快慢j,i两个指针类型
    int slow = 0;
    for (int fast = 1; fast < nums.length; fast++) {
        //如果j不等于i，慢指针+1，慢指针对应的值赋值
        if (nums[fast] != nums[slow]) {
            slow++;
            nums[slow] = nums[fast];
            System.out.println(String.format("[%s]=[%s]", slow, fast));
        }
    }
    return slow + 1;
}
```



## 参考

[leetcode26-remove-duplicates-from-sorted-array](https://leetcode-cn.com/problems/remove-duplicates-from-sorted-array/)

