## 概述

Java中实现队列的的接口分别有

- LinkedList
- ArrayBlockingQueue
- LinkedBlockingDeque
- DelayQueue
- PriorityQueue

## LinkedList实现原理

### 数据结构

LinkedList内部维护的数据结构如下:

![01](/work/dist/branch/wacai/middleware/my-boot/doc/images/java-core/01.png)

每个Node都维护了P和N两个属性，分别表示对前一个和后一个的引用。

LinkedList的删除和新增非常快，不需要像数组一样做数据的迁移，但是LinkedList的读非常慢，需要从头开始迭代。

### 注意事项

LinkedList是非线程安全的，多个线程同时add会导致数据被覆盖，多个线程同时读写，会导致读的线程抛出异常。

## 参考

- [反转链表](https://segmentfault.com/a/1190000037518253)
- [206. 反转链表](https://leetcode-cn.com/problems/reverse-linked-list/)