# Use Cases of a Stack

Stacks find applications in various real-world scenarios. Some common use cases include:

1. Function Calls: The call stack in programming languages uses a stack to keep track of active function calls. When a function is called, it’s pushed onto the stack, and when it returns, it’s popped off.
2. Expression Evaluation: In programming, stacks are helpful for evaluating expressions, especially mathematical expressions. They can be used to convert infix expressions to postfix or prefix notation and then evaluate the expressions.
3. Undo/Redo Operations: Stacks are used to implement undo and redo functionality in applications, where the most recent state changes are stored in the stack.
4. Backtracking: In algorithms like Depth-First Search (DFS), a stack is used to keep track of visited nodes and the path followed.
5. Browser History: Web browsers use a stack to maintain a history of visited web pages. When you press the “Back” button, the last page visited is popped from the stack.



## 参考

- [Understanding the Stack Data Structure](https://medium.com/@myofficework000/understanding-the-stack-data-structure-and-evaluating-prefix-and-postfix-expressions-in-kotlin-2325cf435e53)