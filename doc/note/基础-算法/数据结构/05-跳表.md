## 概述

跳表(skipList)在计算机中大量使用的一种数据结构，在redis，lucene都能见到它的身影，那么应该如何理解这种数据结构呢？

从概念上来说，对于一个很长的有序链表，比如：

[1,3,13,101,105,108,255,256,257]

我们可以把这个 list 分成三个 block：

[**1**,3,13] [**101**,105,108] [**255**,256,257]

然后可以构建出 skip list 的第二层：

[1,101,255]

1,101,255 分别指向自己对应的 block。这样就可以很快地跨 block 的移动指向位置了。

这么理解跳表其实又有点像树。

![image-20230102030916707](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230102030916707.png)

SkipList有以下几个特征：

1. 元素排序的，对应到我们的倒排链，lucene是按照docid进行排序，从小到大。
2. 跳跃有一个固定的间隔，这个是需要建立SkipList的时候指定好，例如上图以间隔是3
3. SkipList的层次，这个是指整个SkipList有几层

![image-20230201011207288](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230201011207288.png)

有了这个SkipList以后比如我们要查找12，原来可能需要一个个扫原始链表，有了SkipList以后先访问第一层看到是然后大于12，进入第0层走到3，8，发现15大于12，然后进入原链表的8继续向下经过10和12。