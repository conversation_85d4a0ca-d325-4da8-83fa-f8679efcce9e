## 概述

BitMap 来源于一个常见的面试题，也是工作中经常遇到的问题：

> 5TB的硬盘上放满了数据，请写一个算法将这些数据进行去重。如果这些数据是一些**32bit**大小的数据该如何解决？如果是**64bit**的呢？

看到这道题我的第一反应是 32bit 和 64bit 对于去重还有什么区别吗？ 其实是有区别的，32bit可以通过BitSet来解决，64bit只能通过布隆过滤器来解决了。那么我们首先就来搞清楚什么BitSet 和布隆过滤器吧。

> 注：本文BitSet和BitMap是一个意思，会混着说

## BitSet

从 BitSet也叫位图， 从命名来看它也是一种Set，可以用于去重，不过和HashSet不同的是BitSet是基于Bit进行索引，并且不记录Value数值。基本原理是，用位来表示一个数据是否出现过，0为没有出现过，1表示出现过。通过BitSet可以大量节约内存。

假设一个场景，要存0-7以内的数字[3,5,6,1,2]，尽可能的节省空间。
一种思路就是单纯使用数组存储，但如果数据量放大百万倍甚至千万倍呢，数组的所占用的内存会非常大。
另一种思路是BitSet，我们可以用8bit的空间来存储，每个数字都在对应的位置中以1的方式表示。

| 位置7 | 位置 6 | 位置 5 | 位置 4 | 位置 3 | 位置 2 | 位置 1 | 位置 0 |
| ----- | ------ | ------ | ------ | ------ | ------ | ------ | ------ |
| 0     | 1      | 1      | 0      | 1      | 1      | 1      | 0      |

通过BitSet 去重样例代码：

```java
//利用BitSet去重字符串
String testStr = "aabbcc";
BitSet used = new BitSet();
for (int i = 0; i < testStr.length(); ++i) {
    System.out.println("设置:" + (int) testStr.charAt(i));
    used.set(testStr.charAt(i));
}
int size = used.size();
System.out.println("已使用的size:" + size);
StringBuilder sb = new StringBuilder();
for (int i = 0; i < size; i++) {
    if (used.get(i)) {
        sb.append((char) i);
    }
}
System.out.println(sb.toString());
```



### Java中Bitset的实现

Bitset这种结构虽然简单，实现的时候也有一些细节需要主要。其中的关键是一些位操作，比如如何将指定位进行反转、设置、查询指定位的状态（0或者1）等。

#### 初始化

```java
private final static int ADDRESS_BITS_PER_WORD = 6;
private final static int BITS_PER_WORD = 1 << ADDRESS_BITS_PER_WORD;//2的6次方=64
private long[] words;
public BitSet() {
    this(BITS_PER_WORD)
}
public BitSet(int nbits) {
    //省略判断参数小于0
    initWords(nbits);
    sizeIsSticky = false;
}
private void initWords(int nbits) {
    words = new long[wordIndex(nbits-1) + 1];
}
//从位索引到words索引
private static int wordIndex(int bitIndex) {
    return bitIndex >> ADDRESS_BITS_PER_WORD;
}
```

**重要信息**：

Bitset 内部采用 long 数组来保存bit，这个数组被定义为words，一个long可以保存64位，那么可以通过 bitindex >>6 来定位到是数组中的位置，比如0~63是words[0]，64~127是words[1]，以此类推。

#### 设置

```java
 public void set(int bitIndex) {
     //从位索引到words索引
     int wordIndex = wordIndex(bitIndex);
   	 //判断是否需要扩容words
     expandTo(wordIndex);
     words[wordIndex] |= (1L << bitIndex); // Restores invariants
     checkInvariants();
 }
 private static int wordIndex(int bitIndex) {
    return bitIndex >> 6;
 }
```



①把int转化为下标，因为底层是long数组，每个long能保存64bit，所以6，2 的6 次数，这个操作相当于把值除以64

②是Restores invariants这行，理解这行代码费了我一点功夫。

首先这行代码等同于：

```java
words[wordIndex] = words[wordIndex] | (1L << bitIndex);
```

本质是在标记 word 中对应的bit，下面是一段测试代码：

```java
@Test
	public void testTemp() {
		long[] words = new long[1];
		words[0] |= (1L << 16);
		printWord(words[0]); //①16 bit
		words[0] |= (1L << 32);
		printWord(words[0]); //②32 bit
		words[0] |= (1L << 63);
		printWord(words[0]); //③63 bit
		words[0] |= (1L << 97);
		printWord(words[0]); //④97%64=33 bit
	}
```

1. 把 16 位设置为 1
2. 把 32  位设置为 1
3. 把 63  位设置为 1
4. long 最多只能表示 0-63 位，97 超过64怎么呢？<< 操作符在超过数值范围之后会自动取余，等价97%64=33

输出:

```shell
10000000000000000	 #17
100000000000000010000000000000000	 #33
1000000000000000000000000000000100000000000000010000000000000000	#64
1000000000000000000000000000001100000000000000010000000000000000	#64
```

我们再举个例子，执行 Bitset.set(1000) 内部如何执行？

1. 首先找到1000对应的word，通过1000>>6(等价1000/64)得到15，即words[15]
2. 执行 words[15] |= (1L << 1000)，这步会1000%64得到40

所以Bitset.set(1000)会把words的第15位long的 40 位bit设为 1。

#### 获取

```java
  public boolean get(int bitIndex) {
        int wordIndex = wordIndex(bitIndex);
        return (wordIndex < wordsInUse)
            && ((words[wordIndex] & (1L << bitIndex)) != 0);
    }
```

这个方法里，最重要的就只有：`words[wordIndex] & (1L << bitIndex)`。这里`(1L << bitIndex)`也很明显，左位移到bitIndex位置，只有`bitIndex`位上为1，其他都为0，然后再和`words[wordIndex]`做`&`计算，如果`words[wordIndex]`数的`bitIndex`位是`0`，则结果就是`0`，以此来判断参数`bitIndex`存在不。

### Bitmap的缺点

小结下，Bitmap 本质就是用bit位来表示数字，这样一个字节就能表示 8 个数字，一个long类型可以表示 64 个数字。 

然而Bitmap不是万能的，当集合稀疏(**sparse**)时，传统位图的压缩效果很差。

下面是一个测试：

```java
	@Test
	public void testOOM() throws IOException {
		BitSet used = new BitSet();
		used.set(1000000000);//10 亿
		System.out.println(used.size());
		System.in.read();
	}
```

我们只保存了一个数字 10 亿，但是这段代码消耗了125MB 内存：

![wecom-temp-2cbb87b214c0d1b440c507fb080d3083](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/wecom-temp-2cbb87b214c0d1b440c507fb080d3083.png)

因为它需要分配  15625001 个long数组保存bit。

如果我们保存最大的Int类型(2^32)约42亿数据，就要消耗521MB，如果是最大的Long类型消耗的内存会是一个天文数字， 所以对于Long类型的数字是无法使用BitSet存储的。

```
32bit=(1<<32)/8/1024/1024=512MB
64bit=(1<<64)/8/1024/1024/1024/1024/1024=2048PB
```





## Roaring Bitmap

第一次知道 Roaring Bitmap（注意不要写成RoaringBitmap）  是在 Elasticsearch，参考

- [Elasticsearch: Frame of Reference and Roaring Bitmaps](https://www.elastic.co/blog/frame-of-reference-and-roaring-bitmaps) 
- [A primer on Roaring bitmaps: what they are and how they work](https://vikramoberoi.com/a-primer-on-roaring-bitmaps****************************work/)

Roaring Bitmap就是为了解决稀疏 bitmap 内存消耗大的问题，为何稀疏？就像上一节的例子保存一个数字就消耗了125MB。Bitmap提供比传统位图更好的压缩。重要的是，他们这样做并没有显着牺牲集合操作的性能。

### 使用

[RoaringBitmap](https://roaringbitmap.org/) 列出了各种语言的开源包，Java 语言是 [RoaringBitm](https://github.com/RoaringBitmap/RoaringBitmap)，被大量大数据项目使用

pom依赖

```java
        <dependency>
            <groupId>org.roaringbitmap</groupId>
            <artifactId>RoaringBitmap</artifactId>
            <version>0.9.25</version>
        </dependency>
```



例子:

```java
public class RoaringBitmapTest {
    @Test
    public void test(){
        RoaringBitmap rb = new RoaringBitmap();
        rb.add(100);    
        rb.add(1000);   
    }
}
```

### 原理

RoaringBitmap 实现思路是这样的：

将32bit的 Integer 数字拆分为两部分，高16位被划分为chunk，低16位为具体的值，这样两部分都可以使用char或者short来保存值。RoaringBitmap内部实现:采用char[] keys 保存高位chunk，有序但不需要像bitset那样提前分配空间。

每个chunk可以保存的数值范围：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230118175631103.png" alt="image-20230118175631103" style="zoom:50%;" />





举个实际的例子，比如数字131075, 655361

1、首先通过除65536得到商和余数获取数字的高16位和低16位，当然也可以通过位运算的方式，下面是 RoaringBitmap 的操作方式:

````java
  protected static char lowbits(int x) {
    return (char) (x & 0xFF);
  }
  protected static char highbits(int x) {
    return (char) (x >>> 16);
  }
````

2、高16位的chunk保存为char数组，比如插入的数字为(131075, 655361)对应chunk为(2,10)，数组插入排序，Bucket中的位置和Container一一对应

3、低16位按chunk中的位置保存在Container，如果Container不存在则新建，如果存在则append到Container中

要查询一个数字也很简单，先得到这个数字的高16位和低16位，通过高16位查找对应chunk，如果chunk不存在说明肯定不存在，否则拿到chunk对应的Container[]通过二分查找对应的低16位值。

需要注意一点，chunk插入排序会导致 chunk 和 Container[] 的位置发生迁移，比如下面这种情况：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230118180344923.png" alt="image-20230118180344923" style="zoom:50%;" />







## Bloom Filter

布隆过滤器是一个叫“布隆”的人提出的，为了降低Bit-Map冲突的概率，Bloom Filter使用了多个哈希函数，而不是一个。

Bloom Filter算法如下：

它本身是一个很长的二进制向量（想象成数组）和一系列随机映射函数（想象成多个 Hash 函数），二进制向量中存放的不是0，就是1。

比如要根据客户手机号做为条件查询客户信息，通常会把手机号码设置成缓存中的 Key，让我们设置一个长度为 16 的布隆过滤器。

布隆过滤器初始化都是 0；

对 13800000000 分别进行 hash1()、hash2()、hash3() 运算，得到三个结果 5、9、12，把对应位置设置成 1；

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/算法/1.png" alt="1" style="zoom:50%;" />

对 18900000000 分别进行 hash1()、hash2()、hash3() 运算，得到三个结果 2、8、12，把对应位置设置成 1，现在 2、5、8、9、12 都是 1，其余元素都是 0；

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/算法/2.png" alt="2" style="zoom:50%;" />

如果我们想要验证某个电话号码是否存在，需要怎么做呢？

对 13700000000 分别进行 hash1()、hash2()、hash3() 运算，得到三个结果 1、9、13，然后去判断第 1、9、13 位上的值是 0 还是 1，如果不全是 1 的话，就说明 13700000000 不在这个布隆过滤器上；这就确定了“某项数据肯定不存在”。

当然我们也可以看出来布隆过滤器有个问题，那就是不能保证数据肯定存在，比如对 18000000000 分别进行 hash1()、hash2()、hash3() 运算，得到的结果是 5、8、9，恰好这三位都是 1，但实际上这条数据并不存在，所以布隆过滤器有一定的误判率；

而且因为多个数据经过运算后可能会映射到同一个位置（138 和 189 的运算结果都有 12），所以布隆过滤器很难做到删除，除非要为每一位增加一个计数器，删除的时候需要给计数器减 1，直到计数器为 0 时，才将布隆过滤器对应位置修改成 0。

### 特点总结

- 可以确定一个元素肯定不存在，但是不能确定一个元素肯定存在；
- 二进制向量越长，hash函数越多，误判率越低；如果提前可以确定误判率，也可以反推出来布隆过滤器的长度；
- 可以添加元素，但是不能删除元素（除非增加计数器）；
- 在存储空间和插入查询的时间复杂度都有巨大优势。

回到本文开头的那个业务场景，为了防止缓存穿透，可以使用布隆过滤器过滤掉肯定不存在的数据，误判的请求虽然还是会放到到数据库，但已经极大地减少了穿透的数量。

### 样例代码

下面给出一个简单的Bloom Filter的Java实现代码：

```java
package com.buzz.algo.bloomfilter;

import java.util.BitSet;

public class BloomFilter {
    /* BitSet初始分配2^24个bit */
    private static final int DEFAULT_SIZE = 1 << 25;
    /* 不同哈希函数的种子，一般应取质数 */
    private static final int[] seeds = new int[] {5, 7, 11, 13, 31, 37, 61};
    private BitSet bits = new BitSet(DEFAULT_SIZE);
    /* 哈希函数对象 */
    private SimpleHash[] func = new SimpleHash[seeds.length];

    public BloomFilter() {
        for (int i = 0; i < seeds.length; i++) {
            func[i] = new SimpleHash(DEFAULT_SIZE, seeds[i]);
        }
    }

    // 将字符串标记到bits中
    public void add(String value) {
        for (SimpleHash f : func) {
            bits.set(f.hash(value), true);
        }
    }

    // 判断字符串是否已经被bits标记
    public boolean contains(String value) {
        if (value == null) {
            return false;
        }
        boolean ret = true;
        for (SimpleHash f : func) {
            ret = ret && bits.get(f.hash(value));
        }
        return ret;
    }

    /* 哈希函数类 */
    public static class SimpleHash {
        private int cap;
        private int seed;

        public SimpleHash(int cap, int seed) {
            this.cap = cap;
            this.seed = seed;
        }

        // hash函数，采用简单的加权和hash
        public int hash(String value) {
            int result = 0;
            int len = value.length();
            for (int i = 0; i < len; i++) {
                result = seed * result + value.charAt(i);
            }
            return result & (cap - 1);
        }
    }
}
```



## 参考

- [A primer on Roaring bitmaps: what they are and how they work](https://vikramoberoi.com/a-primer-on-roaring-bitmaps****************************work/)
- [Java的BitSet原理及应用](https://www.jianshu.com/p/4fbad3a6d253)
- [五分钟搞懂布隆过滤器](https://mp.weixin.qq.com/s/A9pm4dCCAXo91HiycIV0Cg)
- [通俗易懂讲布隆过滤器](https://www.cnblogs.com/jajian/articles/12749928.html)
- [大白话布隆过滤器](https://www.cnblogs.com/CodeBear/p/10911177.html)
- [Bing搜索核心技术BitFunnel原理](https://cloud.tencent.com/developer/article/1541367)