## 概述

netty 的内存分配器PoolChunk是通过一颗二叉树管理内存的，如图所示：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240914145142456.png" alt="image-20240914145142456" style="zoom:50%;" />

这里复习一下二叉树的基础知识：

1. **深度Depth**是指root节点到叶子节点的距离，图中为3
2. 一颗完全二叉树可以通过数组来表示，如图这棵树，通过数组表示[0,64k,32k,32k,16k,16k,16k,16k,8k,8k,8k,8k,8k,8k,8k,8k]，数组[0]是占位符，根节点从数组[1]开始
3. 叶子节点的个数是1<<depth，如图中1<<4等于16。数组的长度等于1<<(depth+1)，图中深度是4，那么数组长度1<<5=32
4. 树中任意节点和左子节点的位置关系是n<<2, 比如第一个32k节点，在数组中位置是2，那么它的左子节点16k位置就是2<<2=8
5. 任意节点的兄弟节点下表可以通过位运算n ^= 1获取，相当于n+=1

## 参考

- [数据结构之二叉搜索树—Java实现](https://juejin.cn/post/6844903792371040263)
- [红黑树深入剖析及Java实现](https://zhuanlan.zhihu.com/p/24367771)