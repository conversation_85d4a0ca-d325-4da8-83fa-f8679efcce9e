### 1. 大纲
- 如何基于zk实现分布式锁
- curator的InterProcessMutex实现分布式锁原理

### 2.基本原理
基于zk实现分布式锁的原理还是比较容易理解的，这里可以先把分布式锁拆分成两种：
- 非公平锁
- 公平锁

### 3.非公平锁
非公平锁的实现原理和通过redis实现锁的原理是一样的，多个客户端竞争同一个锁资源
1. get lock
三个客户端同时向Zookeeper集群注册临时节点，路径都为/zkroot/lock，，基于zk语义，这三个客户端只会有一个创建成功，其它节点均创建失败。
此时，创建成功的客户端 即成功获取到锁 。其它客户端未获取锁

2. release lock
如果获取到锁的客户端需要释放锁，只需要简单删除这个节点即可。

如果 该客户端 进程意外宕机 ，其与 Zookeeper 间的 Session 也结束，该节点由于是Ephemeral类型的节点，因此也会自动被删除。

3. watch lock
前面 创建节点失败的客户端，还会向/zkroot/lock注册一个 Watch ，一旦获取锁的节点放弃锁，所有的 Follower 会收到通知，这时候会再次竞争锁。
谁能获取到锁是不确定的，所以这也叫非公平锁

### 4.非公平式锁小结
- 非公平模式实现简单，每一轮选举方法都完全一样
-  竞争参与方不多的情况下，效率高。每个 Follower 通过 Watch 感知到节点被删除的时间不完全一样，只要有一个 Follower 得到通知即发起竞选，即可保证当时有新的 Leader 被选出
- 给Zookeeper 集群造成的负载大，因此扩展性差。如果有上万个客户端都参与竞选，意味着同时会有上万个写请求发送给 Zookeper。如《Zookeeper架构》一文所述，Zookeeper 存在单点写的问题，写性能不高。同时一旦 Leader 放弃领导权，Zookeeper 需要同时通知上万个 Follower，负载较大。
因为这类问题，非公平锁实际使用并不高

### 5.公平锁
公平锁的实现原理是利用zk的顺序临时节点特性，每个客户端都判断自己是否是当前最小节点，如果是则获取到锁，如果不是则watch在自己前面的那一个节点。这点类似图书馆排队，每个人都叫前面的人说有位置了通知我: 1<-2<-3<-4<-5

1. get lock
三个客户端同时向Zookeeper集群创建zk临时顺序节点，zk会自动为path添加序号，例如如下代码：
```java
    CuratorFramework client = CuratorFrameworkFactory.newClient("172.16.49.169:2181",
                new ExponentialBackoffRetry(1000, 3));
        client.start();

        String path = "/zkroot/lock";
        for (int i = 0; i < 3; ++i) {
            String node = client.create().creatingParentContainersIfNeeded().withProtection()
                    .withMode(CreateMode.EPHEMERAL_SEQUENTIAL).forPath(path);
            System.out.println(node);
        }

```
输出的节点为：
```
/zkroot/lock0000000000
/zkroot/lock0000000001
/zkroot/lock0000000002
```
三个客户端节点都会创建成功，每个客户端都会判断自己创建成功的节点的序号是不是当前最小的。如果是，则该客户端为 Leader，否则即为 Follower。

2. release lock
release lock和前面非公平锁一样的
如果获取到锁的客户端需要释放锁，只需要简单删除这个节点即可。
如果 该客户端 进程意外宕机 ，其与 Zookeeper 间的 Session 也结束，该节点由于是Ephemeral类型的节点，因此也会自动被删除。

3. watch lock
主要区别在于watch，非公平锁所有客户端watch 由 Leader 创建出来的节点，而公平锁 只Watch 序号刚好比自己序号小的节点
假设总共有 1、2、3 共三个节点，Client 2 Watch /zkroot/leader1，Client 3 Watch /zkroot/leader2。
一旦 Client 宕机，/zkroot/leader1被删除，Client 2可得到通知。此时Client 3由于 Watch 的是/zkroot/leader2，故不会得到通知。
Client 2得到/zkroot/leader1被删除的通知后，不会立即成为新的 Leader 。而是先判断自己的序号 2 是不是当前最小的序号。在该场景下，其序号确为最小。因此Client 2获得锁

这里要注意，如果在Client 1放弃领导权之前，Client 2就宕机了，Client 3会收到通知。此时Client 3不会立即成为Leader，而是要先判断自己的序号 3 是否为当前最小序号。
很显然，由于Client 1创建的/zkroot/leader1还在，因此Client 3不会成为新的 Leader ，并向Client 2序号 2 前面的序号，也即 1 创建 Watch。

### 6.公平锁小结
- 实现相对复杂
- 扩展性好，每个客户端都只 Watch 一个节点且每次节点被删除只须通知一个客户端
- 旧 Leader 放弃领导权时，其它客户端根据竞选的先后顺序（也即节点序号）成为新 Leader，这也是公平模式的由来
- 延迟相对非公平模式要高，因为它必须等待特定节点得到通知才能选出新的 Leader

### 7.参考文档

关于实现可靠的分布式锁其实并没有这么容易，还有一篇经典文章( Martin Kleppmann 的文章 How to do distributed locking )解释的比较清楚：
https://zhuanlan.zhihu.com/p/41327417
https://www.shenyanchao.cn/blog/2018/12/28/argument-on-Redis/
[美团分布式系统互斥性与幂等性问题的分析与解决](https://tech.meituan.com/2016/09/29/distributed-system-mutually-exclusive-idempotence-cerberus-gtis.html)

