## 架构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221014152738633.png" alt="image-20221014152738633" style="zoom:50%;" />

组件：

- fog:  ~~老的文件网关，期望被下线~~。域名：http://fog.pro.wacai.info 
- domino-fog：代理层，提供内部使用，域名：https://domino-fog.wacai.info
- domino-file-gateway：也是代理层，提供外网使用，域名：http://file.wacai.com
- domino-fileweb: 配置管理服务。
- rgw：ceph的网关，对于的域名：https://api.ceph.rgw.cache.wacai.info/ 和 https://domino-rgw.wacai.info/

文档信息：

- [环境信息](http://git.caimi-inc.com/middleware/domino/wikis/monitor_dashboards)
- [接入文档](http://pages.wacai.info/middleware/portal/docs/domino/domino_ceph_access_manual.html)
- [grafana监控](http://grafana.wacai.info/d/FSkysrciz/zong-liu-liang-cha-kan?orgId=1&from=now-6h&to=now&refresh=5s)

## 运维

### 排查文件信息

文件插入成功, 会写一条ES记录. 用户反馈文件丢失, 则可以查看ES检索文件.

挖财主体线上: http://kibana.middleware.wacai.info/app/kibana#/dev_tools/console?*g=(), 选择middleware*domino-fileinfo表

测试环境: http://172.16.48.198:5601/, 选择middleware_domino

西湖云环境: http://kibana.common.xihuyun.info/, 选择middleware_domino-fileinfo

深圳云环境: http://kibana.common.szyun.info/, 选择middleware_domino-fileinfo

