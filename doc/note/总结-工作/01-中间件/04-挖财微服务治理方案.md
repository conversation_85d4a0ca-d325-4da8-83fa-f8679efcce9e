## 挖财服务治理平台

### 一、背景

随着微服务架构的推进，挖财业务团队对于服务治理的需求一直存在，中间件团队在微服务治理方面也做了一些努力和尝试，首先回顾下历史方案：

- 2018年之前：此阶段并无统一的服务治理平台，各业务方通过 dubbo 或 http sdk 进行远程方法调用(RPC)，通过 dubbo 本地配置文件或者硬编码的方式来实现有限的服务治理能力。
- 2018年~2019年: 中间件推出 Wrpc+tempo 框架，业务方通过接入 Wrpc+tempo 客户端的方式来实现服务治理。此方案存在的问题是业务方升级比较困难，升级过程容易产生类冲突问题，推广进度缓慢。
- 2020年~2021年: 中间件推出WSE平台，基于 istio 服务网格的方式来支持服务治理。

这里重点说下基于 istio 服务网格的WSE平台，早在 18 年 istio 就凭借服务网格的先进理念火得一塌糊涂，所以中间件团队也顺势推出了WSE平台，但是为什么一直没有推广开来呢？这里说一下服务网格存在的几个问题：

- istio 本身的复杂性。istio 的复杂性包括用户操作层面和运维层面，虽然我们对 istio 的底层概念VirtualService、Destination rule进行了封装，但是业务同学还是反馈使用起来比较复杂，同时istio 将 sidecar 和其它组件引入到已经很复杂的分布式环境中，极大地增加了排查问题和操作运维的复杂性。
- istio 的无侵入性并非 100%无侵入。istio的 sidecar 无法帮助应用自动透传调用链而需要应用自行透传，所以 istio 实质上是有限的无侵入。
- istio 的提供的功能并不能完全满足挖财的需求。istio 不支持dubbo协议，自带的限流功能过于简单，如果要进行功能扩展需要熟悉 envoy 或者 wasm，这对于中间件团队是一个非常大的挑战。
- istio 存在的问题。istio对于 http header 要求非常严格，而挖财历史上对于http header使用并不规范会导致请求失败，这涉及到大量的回归测试；同时  istio 还存在 sidecar 全量数据下发导致的性能问题。

重新审视 istio，istio 主打的多语言服务治理在挖财以 java 为主要开发语言的环境下，是否真的适合，通过调研各大阿里、腾讯公有云厂商，我们发现了另外一条路，基于 java 探针的方案，它具备以下优势：

- 真正的无侵入。由于是基于 Java agent 动态植入字节码的方式，业务方无需修改一行代码即可接入服务治理平台。
- 更轻量。由于只引入了Java agent，不会存在 istio 和 k8s 的版本冲突的问题，运维部署更轻松，同时还支持非 k8s 的环境。
- 更易用。由于是和业务是同一语言栈，我们可以获取更加丰富的信息，比如基于服务的swagger API信息自动生成API文档，而基于服务网格的方式很难做到。

当然 java 探针也存在无法支持多语言的问题，但是和它带来的收益相比，这方面是可以接受的。

### 二、整体架构



<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220210202623325.png" alt="image-20220210202623325" style="zoom:50%;" />



### 三、具体方案

#### 1、服务注册与发现

下面是一个典型的分布式服务场景

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220208181445318.png" alt="image-20220208181445318" style="zoom:50%;" />

服务注册与发现流程如下：

1. 应用启动阶段，agent 会在 SpringBoot 和 Dubbo中埋点，自动上报服务信息到服务注册中心。 
2. 在应用请求之前，agent会拦截请求，通过服务中心订阅服务，把服务转化为ip地址。
3. 通过ip地址正常调用请求。



注册和发现基于配置开关的方式、注册可以选择是往zookeeper还是quantum注册（或者都注册），服务消费时是选择quantum还是zookeeper，消费只能指定一个注册中心。



#### 2、服务契约

在业务日常开发中，由于缺少服务接口的文档，对于多方联调经常造成障碍。比如方法以及参数的含义，如果通过额外文档维护常常会因为维护不及时而导致文档和代码不一致的问题。

通过我们内置的 agent 探针可以完美解决这一问题：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220208184719132.png" alt="image-20220208184719132" style="zoom:50%;" />

开发或测试人员可以通过服务治理中心查看服务相关信息，包括类名、方法、参数、返回值类型、QPS数据、RT数据等。

服务契约为api网关，回归测试，服务联调，服务压测提供了基础数据。

页面呈现的效果：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_c9800233-f075-4170-89c8-f3a32fce5aac.png" alt="企业微信截图_c9800233-f075-4170-89c8-f3a32fce5aac" style="zoom:50%;" />



#### 3、全链路灰度

微服务架构下，有一些需求开发涉及到微服务调用链路上的多个微服务同时发生了改动，需要通过灰度发布方式来更好地控制新版本服务上线的风险。

目前，主要有两种解决思路，基于物理环境隔离和基于打标的逻辑环境隔离。

##### 基于物理环境隔离

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220210164326629.png" alt="image-20220210164326629" style="zoom:50%;" />



通过不同的域名来区分线上基线环境和灰度环境，灰度环境有单独的域名可以配置，我们可以通过访问 wacai.info 来请求基准环境，访问 gray.wacai.info 走灰度环境。这种方案的缺点是整个链路上都需要搭建一套完整的环境， 搭建成本高，灵活性不够，比如业务方只想对上述应用 B 做灰度也需要部署一套完整的环境。

##### 基于打标的逻辑环境隔离

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220210164352116.png" alt="image-20220210164352116" style="zoom:50%;" />

另外一种方案是基于打标的逻辑环境隔离，流程如下： 

- 首先，应用 B 在未打标的实例之外，部署了一个打上灰度标签的实例，同时设置路由规则要求标签匹配。
- 当外部流量的 HTTP 请求头带上 X-APP-TAG=gray 时，应用 A 的 agent 会识别并透传，当应用  A 调用下游应用 B 时会匹配相同 tag 的实例，而HTTP请求头没有带上tag时 A 会正常请求基准环境。
- 当请求到达B 的灰度版本并请求下游 C 时，由于 C 不存在带灰度 tag 的实例，所以请求流向基准应用 C 。

可以发现基于打标的灰度非常灵活，所以在服务度量平台中的链路跟踪会标识出tag信息，做到有迹可循。

tag除了在灰度还有很多应用场景，比如：

- 全链路压测；
- 业务环境隔离；
- 同机房优先；

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20211227170513845.png" alt="image-20211227170513845" style="zoom:50%;" />



##### 基于权重的灰度

另一种场景是基于权重的灰度：即应用B发布新版本时，只允许少量流量被外部调用，待验证通过时再全量更新新版本，这种场景比较简单，只需在灰度发布之后配置好权重比例，调用端的agent会按照权重进行路由。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220209193405586.png" alt="image-20220209193405586" style="zoom:50%;" />

##### 灰度发布用户操作流程

页面原型图

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220210114335522.png" alt="image-20220210114335522" style="zoom:50%;" />

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220210114237796.png" alt="image-20220210114237796" style="zoom:50%;" />

操作流程：

- 步骤1：在服务治理平台-灰度管理中新建灰度规则，选择一个应用进行灰度发布。
- 步骤2：可以给灰度版本配置流量规则，分配流量，提交并等待灰度版本正常启动。
- 步骤 3：经过一段时间的运行，确认灰度版本运行正常，满足业务要求，则可以切换全部流量到灰度版本，原来版本下线。

注意：

- 灰度管理不涉及底层容器的管理，而专注在**灰度规则**的管理上。
- 生产环境下每个应用仅允许存在一个灰度。

#### 4、服务限流

服务限流主要是保护服务节点，防止瞬时流量过大造成服务和数据崩溃，导致服务不可用，当服务资源成为瓶颈时，agent会对请求做限流，启动流控保护机制。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220210154532056.png" alt="image-20220210154532056" style="zoom:50%;" />

限流的实现基于业界使用广泛的 sentinel 框架，得益于agent机制，业务方无需升级 sdk 只需在服务治理平台配置限流规则，即可接入限流。



#### 5、服务熔断

熔断是当下游的服务因为某种原因导致**服务不可用或响应过慢**时，上游服务为了保证自己整体服务的可用性，不再继续调用目标服务，直接返回，当下游服务恢复后，上游服务会恢复调用。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220210153335277.png" alt="image-20220210153335277" style="zoom:50%;" />

熔断规则基于**失败占比**和**慢请求占比**进行配置。



#### 6、服务鉴权

服务鉴权是处理微服务之间相互访问权限问题的解决方案。服务治理中心下发鉴权规则到服务，当请求到来时，服务根据鉴权规则判断鉴权结果，如果鉴权通过，则继续处理请求，否则返回鉴权失败。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220210152416134.png" alt="image-20220210152416134" style="zoom:50%;" />



### 四、结束
问题记录 

|            | 注册 | 发现  |
| ---------- | ---- | ----- |
| Dubbo      | ok   | ok    |
| SpingMVC   | ok   | -     |
| ok-http    | -    | doing |
| Async-http | -    | doing |

