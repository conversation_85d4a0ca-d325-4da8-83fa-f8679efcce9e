## 运维地址

- [网关文档](http://git.caimi-inc.com/open_platform/docs)
- [用户手册](http://pages.wacai.info/open_platform/docs/amoeba/extension/https-introduction.html)
- [网关运维手册](http://git.caimi-inc.com/middleware/guard/issues/12)
- [延迟任务](http://pages.wacai.info/open_platform/docs/DelayQueue/user/console-explain.html)

## 入口网关

### 概述

开放平台提供一套统一的标准化开放体系，统一的网关入口，统一的协议。 api 接口请求报文标准化和响应报文标准化，使得内部要开放的业务系统有了开放接口的标准，使得外部接入的三方合作机构系统有了通用的接入方式。开放平台独立的授权和鉴权系统提供统一的授权、鉴权机制，使得身份认证会使用一套统一的标准。统一的接口在线文档中心，内部开发人员可以通过开放接口的配置在开放平台这边生成统一的文档，让不同的人可以直接参阅标准的文档，提升接入的体验。

开放平台会提供流控、安全、容灾、接口版本管理、授权、鉴权等等一系列目前缺失或者不完善的功能，流控机制可以做到接口级别，做到对网关和后台系统的保护，同时也可以扩展到防止恶意调用接口。安全方面会使用 HTTPS 结合双向认证机制保证交互的安全通道不会被窃听和篡改，使开放平台成为一个安全通道。把网关相关设计做到无状态分别部署在多机房或者多数据中心做到容灾要求。会在接口上引入版本使得统一接口的不同版本做到隔离和归类。设计独立的授权、鉴权系统负责统一的授权、鉴权功能。

除了 api 接口调用功能，网关在之后版本还会增加纯粹的代理功能（类 Nginx ），可以监控全部的网络流量，从而可以做更多的事情，比如网络流量预警等等。

### 系统架构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210428171105812.png" alt="image-20210428171105812" style="zoom:40%;" />

### 配置数据

在 guard 应用启动时会调用 octopus 应用 AppInitService 服务获取网关配置数据并在本地缓存，接着本地监听 hermes 消息来保证配置数据变更的一致性。

相应的topic配置为

```
cache.kafka.topic=middleware.guard.cache
```

### 签名

在网关请求后端的业务服务接口时，可以根据用户配置在发出请求时对请求做签名，防止请求被篡改，方便业务方判断调用来源。因为考虑到是内部服务的调用，不建议内部服务也选择做签名。

```java
String signPlainText = method + "|" + bodyMd5 + "|" + headerString + "|" + paramString;
```

method 是指当前请求方法的 http method，目前支持 GET、POST 。

headerString

Headers 是指参与 Headers 签名计算的 Header 的 Key、Value 拼接的字符串，所有需要参与计算的 header 的名称都在 x-wac-signature-headers 这个 header 对应的 value 里边，以","分隔。

对需要参与计算的 header 按照 headerName 的字母表升序排列，对排序之后的 header 列表做字符串拼接，如下代码所示

```java
String headerString = header1Name + "=" + header1Value + "&" +
                      header2Name + "=" + header2Value + "&" +
                      header3Name + "=" + header3Value + "&" +
                      ...
                      headernName + "=" + headernValue;
```

**注意，headerName 在做拼接时，必须转化全小写。对于 value 为集合的话，用","分隔符号连接所有的 value 作为 value 参与到计算中。**

bodyMd5

只有是 POST 并且非 form 表单（比如 JSON ）时才需要计算 bodyMd5，不需要计算时，bodyMd5 为空字符串，计算方法如下

```java
String bodyMd5 = Base64.encodeBase64String(DigestUtils.md5(bodyBytes));
```

paramString

所有 Query 和 form 表单的参数都一起要参与计算，对需要参与计算的参数按照参数名称的字母表升序排列，对排序之后的参数列表做字符串拼接，如下代码所示

```java
String paramString = param1Name + "=" + param1Value + "&"
                     param2Name + "=" + param2Value + "&"
                     param3Name + "=" + param3Value + "&"
                     ...
                     paramnName + "=" + paramnValue;
```

对于 value 为集合的话，用","分隔符号连接所有的 value 作为 value 参与到计算中。

生成签名的算法

```java
    public static String generateSign(String plainText, String appSecret) {
        Mac mac;
        String algorithm = "hmacSha256";
        try {
            mac = Mac.getInstance(algorithm);
        } catch (NoSuchAlgorithmException e) {
            throw new RuntimeException(algorithm, e);
        }
        try {
            mac.init(new SecretKeySpec(appSecret.getBytes(StandardCharsets.UTF_8), algorithm));
        } catch (InvalidKeyException e) {
            throw new RuntimeException("invalid key appSecret : " + appSecret, e);
        }
        byte[] signatureBytes = mac.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.encodeBase64URLSafeString(signatureBytes);
    }
```

appSecret 即最初分发的秘钥。

### 执行流程

#### 1、应用入口 GuardApp

```java
@ComponentScan(value = {"com.wacai.guard.core", "com.wacai.open.token",
												"com.wacai.ocean.amoeba.script.callback"})
@ImportResource("classpath*:bean/applicationContext_core.xml")
@SpringBootApplication(exclude = {DataSourceAutoConfiguration.class})
@EnableIdcRegistery
public class GuardApp implements CommandLineRunner, ApplicationListener<ContextClosedEvent> {

	@Resource
	private  ApiGwServer gwServer;

	public static void main(String[] args) {
		SpringApplication app = new SpringApplication(GuardApp.class);
		app.setWebEnvironment(false);
		app.run(args);
	}

	@Override
	public void onApplicationEvent(ContextClosedEvent event) {
		gwServer.stop();
	}

	@Override
	public void run(String... args) {
		gwServer.start();
	}
}

```

- 通过 app.setWebEnvironment(false); 设置为非web环境
- 通过实现 CommandLineRunner  接口 触发启动   

#### 2、初始化 ApiGwServer

```java
public void start() {
    ServerBootstrap bootstrap = new ServerBootstrap();
    try {
        bootstrap.group(bossGroup, workerGroup)
            .channel(NioServerSocketChannel.class)
            .option(ChannelOption.SO_BACKLOG, config.getBacklog())
            .localAddress(new InetSocketAddress(config.getPort()))
            .childHandler(pipeLine)
            .childOption(ChannelOption.TCP_NODELAY, true);
        ChannelFuture channelFuture = bootstrap.bind().sync();
        channelFuture.addListener((ChannelFutureListener) future -> {
            if (future.isSuccess()) {
                log.info("ApiGwServer started at port {}", config.getPort());
            }
        });
    } catch (Exception e) {
        log.error("ApiGwServer error at port {}", config.getPort(), e);
    }
}
```

设置了两个ChannelOption：

- SO_BACKLOG
- TCP_NODELAY

#### 3、设置 HandlerPipeLine

```java
	protected void initChannel(SocketChannel ch) {
		ChannelPipeline pipeline = ch.pipeline();
        // 设置 Idle 管理
		pipeline.addLast(new ReadTimeoutHandler(configReader.getKeepAliveTimeOut()));
		pipeline.addLast(new WriteTimeoutHandler(configReader.getKeepAliveTimeOut()));
		// 编码和解码
		pipeline.addLast(new HttpServerCodec());
		// outBoundHandler，记录write耗时
        pipeline.addLast(baseOutBoundHandler);
        // HTTP聚合器，4M
		pipeline.addLast(new HttpObjectAggregator(4 * 1024 * 1024));
		// 处理check_backend_active.html 请求
        pipeline.addLast(healthCheckHandler);
        // 请求预处理handler
		pipeline.addLast(entryRouterHandler);
        // ninja处理器
		pipeline.addLast(ninjaHandler);
        // 限流处理器
		pipeline.addLast(frequencyHandler);
        // 内容解析处理器
		pipeline.addLast(bodyParserHandler);
        // 鉴权
		pipeline.addLast(authorizationHandler);
        // 透传，这个和 httpServiceInvokeHandler  
		pipeline.addLast(dispatchHandler);
        // 调用Http请求
		pipeline.addLast("processorHandler", httpServiceInvokeHandler);
        // 异常处理
		pipeline.addLast(exceptionHandler);
	}
```

#### 4、预处理 EntryRouterHandler

```java
@Sharable
public class EntryRouterHandler extends ChannelInboundHandlerAdapter {
     @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) {
        boolean isHttpRequest = msg instanceof HttpRequest;
        if (!isHttpRequest) {
            log.info("req not support, channel:{}, req:{}", ctx.channel(), msg);
            throw handlerRuntimeException(ErrorCode.SC_BAD_REQUEST, "只支持 HTTP 请求");
        }
		//绑定 RequestCtx 到 Channel
        RequestCtx requestCtx = set(ctx, (FullHttpRequest) msg);
        HttpRequestUtil.printReq(ctx, msg);
        // 业务自定义biztype 链路查询
        MDC.put(TRACE_ID_LOG_KEY, requestCtx.getTraceId());
        //补全 RequestCtx，包括ApiName，ApiVersion等
        resolveAndSet(ctx);
        parseUri(requestCtx);
        ctx.fireChannelRead(msg);
    
    }
}
```

#### 5、链路跟踪 NinjaHandler

继承的是ChannelDuplexHandler，同时处理入站和出站：

```java
@Sharable
@Component
public class NinjaHandler extends ChannelDuplexHandler {
	
	@Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) {
		//收到请求开启链路
        doTrace(ctx, msg);
		ctx.fireChannelRead(msg);
	}
	
	@Override
	public void flush(ChannelHandlerContext ctx) {
        //结束链路
		doFinishTrace(ctx);
		ctx.flush();
	}
}    
```

#### 6、限流 FrequencyHandler

```java
@Sharable
@Component
public class FrequencyHandler extends ChannelInboundHandlerAdapter {
    @Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) {
		RequestCtx requestCtx = RequestCtxUtil.get(ctx);
		//是否开启了流控
        if (requestCtx.isFlowControlOpen()) {
			log.info("Req do flowControl, apiName = {}", requestCtx.getRequestData().getApiName());
			try {
				checkFlowControl(requestCtx, ctx);
			} catch (NettyHandlerRuntimeException e) {
				throw e;
			} catch (Exception e) {
				log.error("frequency check error", e);
				util.alarm(GUARD_RUNTIME_ERROR, "流控系统异常", requestCtx);
			}
		}

		ctx.fireChannelRead(msg);
	}    
}
```

具体的限流策略在FreqService 中，通过redis计数器来实现。实现思路如下：

1. 把当前时间通过整除转换为时间窗口：

```java
//分钟粒度
long distance = System.currentTimeMillis()/(60*1000);
```

2、把 ip+ distance 作为 key，通过redis计数：

```java
public long ipFrequency(String ip, long distance, int expireSeconds) throws Exception {
    String hash = HashUtil.hash(key);
    Long count = redisCluster.incr(hash);
    if (count == 1) {
        //默认两分钟失效失效
        redisCluster.expire(hash, expireSeconds);
    }
    return count
}
```

3、判断计数是否超过限制，如果超过则触发限流：

```java
long ipCurrentCount = freqService.ipFrequency(clientIp, distance, 120);
if (ipCurrentCount > IP_COUNT_LIMIT) {
	log.info(clientIp + "_" + distance + "超出预设值:{}", ipCount);
	throw handlerRuntimeException(ErrorCode.FREQ_ERROR, "访问过于频繁");
}
```

#### 7、延签 BodyParserHandler

```java
@Slf4j
@Sharable
@Component
public class BodyParserHandler extends ChannelInboundHandlerAdapter {
    
    @Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) {
		parseAndCheck(RequestCtxUtil.get(ctx));
		ctx.fireChannelRead(msg);
	}
}
```

流程包括：

- 检查参数
- 检查签名
- 如果用户设置了解析参数，则解析并放入到 StandardRequestData 中

#### 8、鉴权 AuthorizationHandler

见代码注释

```java
@Slf4j
@Sharable
@Component
public class AuthorizationHandler extends BaseInboundHandler {
    @Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) {
		RequestCtx requestCtx = RequestCtxUtil.get(ctx);
		//从缓存中获取ApiBasicInfo，然后设置到RequestCtx
		resolveAndSetApiBasicInfo(requestCtx);
        //检查权限，主要通过检查api是否授权给了App
		checkPermission(requestCtx);
		ctx.fireChannelRead(msg);
	}
}
```

#### 9、流量转发 DispatchHandler





#### 10、BaseOutBoundHandler

 ```java
public class BaseOutBoundHandler extends ChannelOutboundHandlerAdapter {
    
    @Override
	public void write(ChannelHandlerContext ctx, Object msg, ChannelPromise promise)
			throws Exception {
        
        RequestCtx requestCtx = RequestCtxUtil.get(ctx);
		promise.addListener((ChannelFutureListener) future -> {
			if (!future.isSuccess()) {
				Throwable cause = future.cause();
				log.info("channel write error traceId:{},remoteAddress:{},ctx:{},emsg:{}",
				         parseTraceId(requestCtx), ctx.channel().remoteAddress().toString(), ctx, cause.getMessage(),
				         cause);
			} else {
				log.debug("channel write success traceId:{},remoteAddress:{},ctx:{},emsg:{}",
				          parseTraceId(requestCtx), ctx.channel().remoteAddress().toString(), ctx);
			}
		});
		super.write(ctx, msg, promise);	        
    }
    
    private String parseTraceId(RequestCtx requestCtx) {
		return Objects.isNull(requestCtx) ? StringUtils.EMPTY : requestCtx.getTraceId();
	}	
}
 ```

#### 11、HttpServiceInvokeHandler



## 出口网关

### 概述

[Amoeba](http://git.caimi-inc.com/open_platform/amoeba) 变形虫，又音译为“阿米巴”，寓意：通过灵活扩展适配对接不同的外部三方系统。Amoeba用于提供开放平台出口网关 安全、审计、流控等功能，目前，安全包含了 秘钥管理、加密和解密、签名和验签。

目前，主要提供了以下功能：

- 网络统一：二方系统访问外部三方系统统一通过出口网关进行对接；
- 秘钥管理：密钥的集中配置管理，减低了安全风险；
- 安全功能：提供了加密、解密、加签、验签等安全功能，
- 协议支持：支持http/https,通过配置化的方式快速实现单向认证、双向认证；
- 文件支持：支持通过ftp/sftp进行文件上传和下载,支持http同步上传和下载,
- 流量控制：流控设置和实施；
- 计量统计：访问统计、审计报表；

### 架构

略

### 配置数据

出口网关 Amoeba 本地会缓存一份meta数据，那么这份数据如何和数据库中保持一致呢？

首先，Amoeba 启动时会请求dubbo服务获取全量meta数据，然后通过订阅kafka消息来保证meta数据和数据库中信息一致。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_677ab561-3e77-4c88-86f6-54c4df88d72a.png" alt="企业微信截图_677ab561-3e77-4c88-86f6-54c4df88d72a" style="zoom:50%;" />

### 执行流程

#### 1、应用入口 OutGatewayServer

主入口是 OutGatewayServer，参数设置

```java
ServerBootstrap bootstrap = new ServerBootstrap();
            bootstrap.group(BOSS_GROUP, WORKER_GROUP)
                    .channel(NioServerSocketChannel.class)
                    .option(ChannelOption.SO_BACKLOG, configReader.getBacklog())
                    .localAddress(new InetSocketAddress(configReader.getPort()))
                    .childHandler(gatewayServerInitializer)
                    .childOption(ChannelOption.TCP_NODELAY, true);
```

pipeline 是 OutGatewayServerInitializer，设置：

```java
 public void initChannel(SocketChannel ch) {
        ChannelPipeline channelPipeline = ch.pipeline();
        //自动剔除Idle链接
        channelPipeline.addLast(new ReadTimeoutHandler(configReader.getKeepAliveReadTimeOut()));
        channelPipeline.addLast(new WriteTimeoutHandler(configReader.getKeepAliveWriteTimeOut()));
        // 编码和解码
        channelPipeline.addLast(new ChannelHandler[]{new HttpServerCodec()});
        // 请求最大大小为1M
        channelPipeline.addLast(new HttpObjectAggregator(configReader.getHttpRequestMaxSize()));
        // 健康检查handler
        channelPipeline.addLast(new CheckHealthyHandler());
        // 请求预处理handler
        channelPipeline.addLast(prepareHandler);
        // 连接总数检查handler
        channelPipeline.addLast(connectionCounterHandler);
        // 流控
        channelPipeline.addLast(rateLimiterHandler);
        // 增加审核追踪日志
        channelPipeline.addLast(auditingHandler);
        // Token处理 Handler
        channelPipeline.addLast(tokenHandler);
        // 出口网关 handler
        channelPipeline.addLast(outGatewayServerHandler);
    }
```



#### 2、元数据加载 AppConfigInitializer

出口网关的Handler会根据请求头中的 g-third-app-code 获取App对象， Amoeba 本地会缓存一份App数据，那么这份数据如何和数据库中保持一致呢？

首先，Amoeba 启动时会请求dubbo服务获取全量meta数据，然后通过订阅kafka消息来保证meta数据和数据库中信息一致。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_677ab561-3e77-4c88-86f6-54c4df88d72a.png" alt="企业微信截图_677ab561-3e77-4c88-86f6-54c4df88d72a" style="zoom:50%;" />

代码在 ThirdAppCache 、AppConfigInitializer 、启动时候全量请求 AmoebaInitService getAllApp()方法

#### 3、 编码解码 HttpServerCodec

实现类是 io.netty.handler.codec.http.HttpServerCodec ，HTTP协议没啥好说的

#### 4、健康检查 CheckHealthyHandler

当发现是 HttpRequest 类型，并且 uri 包含 check_backend_active 执行健康检查逻辑

```java
@ChannelHandler.Sharable
public class CheckHealthyHandler extends ChannelInboundHandlerAdapter {
    private static final String HEALTH_CHECK_PATH = "check_backend_active";

    @Override
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
        boolean isHttpRequest = msg instanceof HttpRequest;
        if (!isHttpRequest) {
            return;
        }

        // The flag for release resource
        boolean needRelease = false;
        try {
            HttpRequest httpRequest = (HttpRequest) msg;
            if (httpRequest.uri().contains(HEALTH_CHECK_PATH)) {
                SecondResponseSender.sendOk(ctx, httpRequest);
                needRelease = true;
            } else {
                ctx.fireChannelRead(msg);
            }
        } finally {
            if (needRelease) {
                ReferenceCountUtil.release(msg);
            }
        }
    }
}
```

#### 5、预处理 PrepareHandler 

直接上代码：

```java
@Override
public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
    try {
        // Initialize HttpRequestContext
        initRequestContext(ctx, msg);

        // Write log and statistics
        writeLogAndStat(ctx);

        // Trigger next netty/handler
        HttpRequestContext httpRequestContext = RequestCtxUtil.get(ctx);

        // Parse request(读取http-request请求)
        RequestParser.read(ctx, msg, httpRequestContext);

        // Load third app/api config
        loadConfig(ctx);

        // Trigger next netty/handler
        ctx.fireChannelRead(httpRequestContext);
    } catch (Exception ex) {
        // Record stat and logging
        writeAlarmAndStat2Error(ctx, ex, Constants.OG_LOAD_CONFIG_FAIL);
        // Response second
        sendResponse2End(ctx, null, ex);
    } finally {
        ReferenceCountUtil.release(msg);
    }
}
```

步骤如下：

1. 初始化 HttpRequestContext，并通过 RequestCtxUtil 设置到Channel.attr；

2. 记录日志和统计。日志格式："Received request,phase={},appName={},thirdAppCode={},thirdApiCode={}"。统计是写到RedAlert；

3. 从FullHttpRequest中读取header,url,body等信息，补齐到HttpRequestContext。对于body信息，需要判断表单提交，文件上传等情况，依赖HttpPostRequestDecoder；

    

另外这个类继承于BaseInboundHandler，基类提供了写统计日志，发送告警等通用功能。

#### 6、发送到第三方 OutGatewayServerHandler

入口是 OutGatewayServerHandler ，他会调用 BaseHttpAsyncClientPost 或者 BaseHttpAsyncClientGet

```java
public class OutGatewayServerHandler extends BaseInboundHandler {
    public void channelRead(ChannelHandlerContext ctx, Object msg) throws Exception {
          HttpMethod method = httpRequestContext.getHttpMethod();
            if (method == HttpMethod.GET) {
                httpAsyncClientGet.process(ctx);
            } else if (method == HttpMethod.POST) {
                httpAsyncClientPost.process(ctx);
            }
    }
    @Override
    public void channelInactive(ChannelHandlerContext ctx) throws Exception {
        super.channelInactive(ctx);
        HttpRequestContext httpRequestContext = RequestCtxUtil.get(ctx);
        // for 流控处理
        rateLimitSeqService.decrementCount(httpRequestContext);
    }
}
```

假设是POST方法，则执行 BaseHttpAsyncClientPost.process()方法 。

```java
public void process(ChannelHandlerContext ctx) throws Exception {
	 
    asyncHttpClientBuilder.doPostWithCallback(app, api,uri,requestId,
              HttpPreRequestCallback,AsyncHttpRequestCallback,HttpTracingCallback);
      
}
```

这里会执行三个回调，第一个回调在发起请求之前执行：

```java
HttpPreRequestCallback -> {
    //执行request脚本
    AppScriptRunner.preRequest(httpRequestContext);
     // Re-set request-http-header
    rewriteHeader(httpRequestContext, httpPost);
    // Re-set request-http-body
    rewriteBody(httpRequestContext, httpPost);
}
```

第二个回调，在收到响应被执行：

```java
new AsyncHttpClientBuilder.AsyncHttpRequestCallback() {
	public void onResponse(HttpResponseContext httpResponseContext) throws Exception {
        // Run response script
        AppScriptRunner.postResponse(httpResponseContext);
        // 发送给客户端
        secondResponseSender.sendResponse(ctx, httpResponseContext, secondResponseParameter);
    }      
}
```

第三个回调，在发送前执行，主要记录打点日志，代码就省略了。

doPostWithCallback 执行逻辑：

```java
 public void doPostWithCallback(){
     //执行第一个回调	
     httpPreRequestCallback.update(httpPost);
     
     // 获取Async http-client，内部会根据app缓存CloseableHttpAsyncClient
     CloseableHttpAsyncClient httpAsyncClient = HttpAsyncClientContainer.getAsyncHttpClient(app);

     //执行tracking回调
     httpTracingCallback.preRequest(requestId, httpPost);

     // 异步请求(成功、失败、异常三个Event)
     httpAsyncClient.execute(httpPost, new FutureCallback<HttpResponse>() {
         public void completed(HttpResponse httpResponse) {
			 // 读取三方的reponse
             HttpResponseContext httpResponseContext = read(httpResponse, app, api);
             // 执行第二个回调
             asyncHttpRequestCallback.onResponse(httpResponseContext);
         }
     });
 }
```



#### 7、加签逻辑 RSASignUtil

```java
//数据		
String requestContent = "<Grp><GrpHead><Version>1.0</Version><BizCode>B012</BizCode><MctCode>wacai</MctCode><SendDate>********</SendDate><SendTime>150322</SendTime><SendNo>********000003</SendNo></GrpHead><GrpBody><OrderNo>********000003</OrderNo><FundCode>003003</FundCode><FileName>TRANSFER-FAILURE</FileName><FileMd5></FileMd5><TransNum>1</TransNum><TransFee>100</TransFee><TransDate>********</TransDate><ReqDate>********</ReqDate><BankBillNo>********000003</BankBillNo></GrpBody></Grp>";
String signData = requestContent.substring(requestContent.indexOf("<GrpHead>"),
                                           requestContent.indexOf("</GrpBody>") + 10);
System.out.println("原始数据：" + signData);
		
//私钥
PrivateKey privateKey = RSAKeyUtil.getPrivateKeyOfPKCS8(				"MIICeAIBADANBgkqhkiG9w0BAQEFAASCAmIwggJeAgEAAoGBAKIGY40X+zxLaO/OTdRtrTJBNpQ1P81hEACwYvBSz8Yu1Fb6IW/AtjFGXxxUkmpwjN2Qu/Bc1/giIHip7bOxYgBVgsH8tnrY8OiuqDHxpIcSCWhQscpEmHeoeDqiFJJxXBCo2nQl4DHyCgPSAIYUcow8xtCzvs2YoG11mCB0wQffAgMBAAECgYEAls0FePvaDOtO9ut4CwAtlDmYF+dbw8bUQx/oZi9dTOKnJXDXEBE4QbJysYaRY9nvcgTUt0lihYMV39ig8X9matfzkIB2p4oPfWi0z0GnE4sfvleHflQS9+NjeHQMIt/FWjQAL+X45zuqH7NUtV+9Lz5g5FNsbldR8/ie7hO9k+ECQQDQiDz8QkbxdLK+qzuSKxYqrICMOaQlan27p62ap94AbiKrZ0wyMKTz6fecgk3SpZuoJKWfoXrCHhPm4Su8658vAkEAxugK8lSiKbVZkp9TLGixyIGCTSfTEyZjbQmlhSUzviOr4fvGXlOJVKBmmsv5KJZWesnIyQL0jQDBgSvuBWx2UQJBAK/EpeLa2oFbI+6i1it2NdDyZcfMLhd7k09BHotlQzKJop9nCkqjxeXyJ8u76WzMOk+X1CKhMZ+9SnkpAnzS/fECQCiLfke9ZrzXwaV4oqynvq2fcJ8noggCSDwevkWwuqZkgRH0j5AEuTat55xgPa6/TmuDHFGVL6JaaBdbjA5A+LECQQDCrqrS2ejFld2VnyJfpxJOawfJ9Dv1wTqdeb7TvRj3dx3D2BVOHe5/glBQVegseQoXoJw7znB7ukJ50Lls6Ms6");

SignParam signParam = new SignParam();
signParam.setSignatureAlgorithm("SHA1withRSA");
signParam.setData(signData);
signParam.setCharset("UTF-8");
signParam.setKeyOfPrivate(privateKey);

byte[] signed = RSASignUtil.sign(signParam);
String sign = Base64.encodeBase64String(signed);
System.out.println(sign);
```



#### 8、Netty-handlers之间设置共享转递值

```java
public class RequestCtxUtil {
    private final static AttributeKey<HttpRequestContext> requestCtxKey = AttributeKey.valueOf("request-ctx");

    public static HttpRequestContext get(ChannelHandlerContext ctx) {
        return ctx.channel().attr(requestCtxKey).get();
    }

    public static HttpRequestContext set(ChannelHandlerContext ctx, FullHttpRequest fullHttpRequest) {
        HttpRequestContext requestContext = new HttpRequestContext(fullHttpRequest);
        ctx.channel().attr(requestCtxKey).set(requestContext);
        return requestContext;
    }

    public static void set(ChannelHandlerContext ctx, HttpRequestContext httpRequestContext) {
        ctx.channel().attr(requestCtxKey).set(httpRequestContext);
    }
}
```

用法：

```java
  HttpRequestContext httpRequestContext = RequestCtxUtil.get(ctx);
```



## 单元接入层

### 概述

单元化接入层承担按规则分流、流量纠错、反向代理等等功能

核心功能

- 根据分片规则动态对流量做单元间级别分流；
- 反向代理，对应用集群转发流量做负载均衡；
- 前置链路跟踪埋点、单元信息埋点；
- 提供全网流量的横向切入点，比如安全部门的WAF、线下流量分析，还有理财业务线的合规(系统必须记录用户所有的操作)等等。

### 架构

#### 数据开放方案

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_c245dc46-1c22-4e7b-8521-b7ddc1c8c1ac.png" alt="企业微信截图_c245dc46-1c22-4e7b-8521-b7ddc1c8c1ac" style="zoom:40%;" />

箭头表示数据流向，虚衔表示异步。 接入层会把请求数据异步写入log，落地在进程所在的磁盘上，运维团队的日志抓取 filebeat 进程会在合适的时间点异步读取请求数据日志文件，写入 Kafka 消息中间件的指定 topic，这样各个需要接入层请求数据的业务方可以直接订阅对应的消息消费即可。

一些细节说明

- HTTP 请求 body 体格式：请求数据需要直接写入 log 文件，这样会涉及到 HTTP 请求 body 体内容的序列化，考虑到通用性，目前接入层并不解析 body 体内容，而是直接写入 upstream，所以目前考虑写入日志文件时，直接把请求 body 体的字节数组转化成 BASE64编码之后，写入消息体，这样相当于对消息体做了一些编解码的处理，所以同步也在考虑是不是需要提供一个消费方的二方库来统一封装消息体的解码。
- Kafka 集群和 topic 配置：@多宝 负责的日志收集目前是收集指定的目录的日志文件，写入到统一的应用日志 Kafka 集群，topic 按照特定的规则生成，这样的情况并不适合当前的需求，正如前面所描述的情况，当前的需求需要指定要收集的文件所在的目录，需要指定要写入的 Kafka 集群，以及指定的 topic，这个需要 @多宝 来配合。
- 一个域名下的请求对应一个 topic 还是只有一个 topic：安全部门是需要全网的请求数据，不关心域名。而理财业务线的需求是只需要 8.wacai.com 下的请求，而不关心其他域名下的请求数据。由于 Kafka 还不支持服务端消息过滤，所以如果是使用一个 topic 接受所有的消息，对于理财业务线这样的业务，就需要在消费端过滤消息了，会存在流量浪费。考虑到接入层的通用性，以及整体方案的扩展性（不用每接入一个域名就要多宝新建一个 topic），会选择使用一个 topic 来承接所有的请求数据。
- 关于数据脱敏：@福林 提出来的需求，希望请求数据中的敏感信息可以做脱敏操作，比如请求信息中的手机号、密码等等，这一点上目前还没有很合适的通用方案，因为作为接入层目前其实是不关心请求的业务语义的，不会对请求信息解码，基于现状，想做到完全的脱敏是比较困难的。折中的方案，接入层统一配置域名路径脱敏黑名单，针对在黑名单的域名路径不写入数据日志文件或者参数不写入，但是这样会不会对安全团队有额外的影响？
- 用户 token 换 uid：@福林 提出来的需求，希望写入请求数据时，如果用户已经登录了，把 token 换成对应的 uid 也写入请求数据中，这样理财业务那边就不用在消费对应消息的时候去用户中心换取 uid 了，而且可以避免消费的时候，token 已经过期的情况。目前只有在接入层白名单中的域名路径才会尝试访问用户中心服务换 token 为 uid，这个换取的 uid 会伴随着当前请求上下文，写入自然是没问题的，但是如果之前没有获取 uid，那么单独为了写日志数据而去获取 uid，对接入层来说是重要的负担。

#### 流量打标方案

业务的两个场景：

- 场景一：A/B Test,设置两组用户，一组设置为对照组，采用已有的产品或功能，另一组为实验组，采用新版产品或功能。然后，找到上述两组用户做出的不同响应，确认哪个版本的功能更好。那么要求接入层能够把指定的流量打到指定版本的机器上；
- 场景二：线上压测, 用真实的流量来评估某些业务的服务性能,比如春节期间的手Q活动等；

简单分析下，接入层要实现的是把指定的流量打到指定机房的机器上。目前我们的接入层的只有一层选择机房的路由流程图如下,机房之间的路由算法是简单的uid%2方法;策略在jasmine中的配置策略如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_65536fd6-b56b-4850-a752-6c1284e370e5.png" alt="企业微信截图_65536fd6-b56b-4850-a752-6c1284e370e5" style="zoom:80%;" />

```
{
    "shardingAlgorithm": "uid % 10",
    "mappings": [{
        "targetCellId": "hzqsh",
        "remainders": [0, 1, 2, 3, 4]
    }, {
        "targetCellId": "hzxs",
        "remainders": [5, 6, 7, 8, 9]
    }]
}
```

#### 接入层流控方案

##### 背景

接入层做为双活项目的入口，当访问流量很大或者遭受恶意流量攻击的时候,如果不对流量加以限制，很容易冲跨后面的业务系统；流控作为容灾体系中必不可少的一环，在请求量超过系统容量的时候，通过拒绝超量请求的方式，防止服务接口的雪崩和系统挂掉。

##### 业务分析

针对tudor在项目中的角色，考虑到以下维度的限流,与此同时，由于在限流的时候做了对单位时间内流量的记录，因此基于流量的统计数据,可以对突然飙高的流量或者突然降低的流量作出监控；

- ip维度的限流：限制同一个IP单位时间内的请求量;如果单位时间IP访问量一直很高,超过阀值,关进IP黑名单,发送告警;
- 域名(domai+path)维度的限流: 一个domain+path对应后端最少两台的线上的应用；目前线上为了流量均衡采用都是轮询机制；如果某些应用的线上两个机器的性能差异很大,是否要做domain+path+app(ip)维度的限流;
- tag维度: 结合流量打标的tag做限流,一个tag的限流相当于对一组的domain+path做限流;
- header纬度：某些场景下,如在header中的a_f表示渠道方，根据渠道方来做限流；

##### 技术方案

常见的限流算法有：漏桶和令牌桶算法；两者对比如下：

- 令牌桶是按照固定速率往桶中添加令牌，请求是否被处理需要看桶中令牌是否足够，当令牌数减为零时则拒绝新的请求；
- 漏桶则是按照常量固定速率流出请求，流入请求速率任意，当流入的请求数累积到漏桶容量时，则新流入的请求被拒绝；

令牌桶适用于突发流量的场景，比较符合tudor的场景(如理财618活动日场景等);Guava RateLimiter提供了令牌桶算法的实现;但RateLimiter只能用于单进程，不适用于分布式场景下的限流；而redis单线程机制可以解决最终一致性问题，因此可以基于Redis来实现RateLimiter。RateLimiter将令牌数据存放于内存中，改造后将RateLimiter的令牌数据存放在redis中,

##### 其他

在Guard和Amoeba中使用了计数法实现的流控,借助了redis的分布式锁，key= appKey + api + 时间;每次请求增加一次，一旦超过最大允许请求数，会抛一个访问频繁的异常；

### 源码实现

#### 核心接口

##### 1、HttpInvoker

发起 Http 调用。实现类是LightningBasedHttpInvoker

```java
	public void invoke(HttpInvokerContext httpInvokerContext) {
		Server server = select(httpInvokerContext.getAppInfo()); //根据负载均衡策略选择一台机器
		if (server == null) {
			log.error("Invoke server info is null, appName = {}", httpInvokerContext.getAppInfo().getAppName());
			throw new TudorException(NO_UP_SERVER);
		}
		attachSpanToChannel(httpInvokerContext.getHandlerContext());//调用链信息记录
		LightningRequest lightningRequest = buildLightningRequest(httpInvokerContext, server);
		doInvoke(server, httpInvokerContext, lightningRequest);//发起后端请求
	}
```

##### 2、LoadBalanceSelector

负载均衡。在LightningBasedHttpInvoker中使用，用于实现对后端的调用的负载均衡。

##### 3、 TudorRateLimiter

限流

##### 4、 AppInfoResolver

应用信息查询，数据保存在jasmine中

```java
public interface AppInfoResolver {
	 // 根据 domain 和 uri 获取应用名称
	AppInfo getByDomainAndUri(String domain, String uri);
	
}
```

##### 5、 UidShardingCellRule

机房间路由分片规则。接口定义：

```java
public interface CellRule {
	//根据上下文信息目标单元
	String decide(CellRuleContext context) throws CellRuleException;
}
```

输出是单元编码

```java
public class UidShardingCellRule implements CellRule {
    public String decide(CellRuleContext context) {
		if (context.getUid() == null) {
			return cellManager.getCenterCell().getId();
		}
		List<ShardingRuleEngine> engineList = this.shardingRuleEngineList;
		RequestContext requestContext = context.getRequestContext();
		String url = requestContext.getDomain().concat(requestContext.getPath());
		String targetCellId = StringUtils.EMPTY;
		for (ShardingRuleEngine engine : engineList) {
			if (engine.isPathMatching(engine.domainPathList, url)) {
				targetCellId = engine.execute(context);
				if (StringUtils.isNotBlank(targetCellId)) {
					log.info("Routing result = {}, engineContext = {}", targetCellId, engine);
					break;
				}
			}
		}
		log.info("当前解出的targetCellId = {}", targetCellId);
		if (StringUtils.isBlank(targetCellId)) {
			// 基于 uid 的分片理论上应该是覆盖所有的 uid，如果未获取到对应的单元编号，说明规则错误
			throw new CellRuleException("Can't find a target cell for uid " + context.getUid());
		}
		return targetCellId;
	}
}

```

分片逻辑委托给了ShardingRuleEngine，ShardingRuleEngine初始化如下:

```java
@PostConstruct
public void init() {
    String config = Jasmine.getConfig(MODULE, SUB_MODULE, "cell.sharding.rule.new");
    this.shardingRuleEngineList = doParse(config);
    shardingRuleEngineList.sort(COMPARATOR_SHARDING_RULE);
    log.info("init shardingRuleEngineList = {}", print(shardingRuleEngineList));
    Jasmine.addListener(MODULE, SUB_MODULE, SHARDING_RULE, new ConfigChangeListener() {

        @Override
        public Executor getExecutor() {
            return null;
        }

        @Override
        public void onChange(String config) {
            parse(config);
        }
    });
}
```

分片规则定义：

```java
	/**
	 * 分片规则
	 */
	@Data
	private static class ShardingRule {
		
		/**
		 * 分片算法
		 */
		private String shardingAlgorithm;
		private List<CellRemainderMapping> mappings;
		private List<DomainPath> shardingUrl;
	}
	@Data
	private static class CellRemainderMapping {
		
		private String targetCellId;
		
		private List<Object> remainders;
	}
	@Data
	@ToString
	private static class DomainPath {
		
		/**
		 * 请求路径
		 */
		private String domainPath;
	}
	
```

分片规则数据定义在jasmine中：

```java
[{
  "mappings": [{
    "remainders": [false],
    "targetCellId": "hzqsh"
  }, {
    "remainders": [true],
    "targetCellId": "hzxs"
  }],
  "shardingAlgorithm": "uid == 855798541",
  "shardingUrl": [{
    "domainPath": "8.wacai.com/finance/app/areaannonce.do"
  }, {
    "domainPath": "8.wacai.com/finance/app/marketinginfo.do"
  }, {
    "domainPath": "8.wacai.com/finance/app/startPage.do"
  }, {
    "domainPath": "8.wacai.com/finance/app/func-entrance.do"
  }, {
    "domainPath": "8.wacai.com/finance/app/classify/rootClassifyList.do"
  }, {
    "domainPath": "8.wacai.com/finance/app/productByClassifyId.do?classifyId=1"
  }, {
    "domainPath": "8.wacai.com/finance/app/newerhomepage/productList.do"
  }, {
    "domainPath": "8.wacai.com/finance/app/position/position.do"
  }, {
    "domainPath": "8.wacai.com/finance/app/homepage/productList.do"
  }, {
    "domainPath": "8.wacai.com/finance/app/classify/list.do"
  }]
}, {
  "mappings": [{
    "remainders": [0, 1, 2, 3, 4],
    "targetCellId": "hzqsh"
  }, {
    "remainders": [5, 6, 7, 8, 9],
    "targetCellId": "hzxs"
  }],
  "shardingAlgorithm": "uid % 10",
  "shardingUrl": [{}]
}]
```

上述规则表示 uid和10整除，余数为0~4的走杭州青山湖，余数为5~9的走hzxs。remainders也可以用boolean表示。

##### 6、CellHttpInvoker

跨单元调用

#### 调用流程

##### 1、TudorServer 

初始化ServerBootstrap。

```java
public void start() {
		try {
			ServerBootstrap bootstrap = new ServerBootstrap();
			bootstrap.group(parentGroup, workerGroup)
					.channel(NioServerSocketChannel.class)
					.option(ChannelOption.SO_BACKLOG, 128)
					.localAddress(new InetSocketAddress(serverPort))
					.childHandler(tudorServerInitializer)
					.childOption(ChannelOption.TCP_NODELAY, true);
			
			ChannelFuture channelFuture = bootstrap.bind();
			channelFuture.addListener((ChannelFutureListener) future -> {
				if (future.isSuccess()) {
					log.info("Tudor server started at port {}", serverPort);
				} else {
					log.error("Tudor server bound port failed");
					channelFuture.cause().printStackTrace();
				}
			});
			
			Channel channel = channelFuture.sync().channel();
			listenCloseInSingleThread(channel);
		} catch (Exception ex) {
			log.error("Tudor server start failed", ex);
		}
}
```



##### 2、TudorServerInitializer初始化

ChannelHandler 初始化，定义ChannelPipeline

```java
@Component
public class TudorServerInitializer extends ChannelInitializer<SocketChannel> {
		@Override
	protected void initChannel(SocketChannel ch) {
		ChannelPipeline channelPipeline = ch.pipeline();
		channelPipeline.addLast(new TudorIdleStateHandler(300, 300, 300)); //维持心跳
		channelPipeline.addLast(new HttpServerCodec());//HttpRequestDecoder和HttpResponseEncoder组合
		channelPipeline.addLast(new HttpObjectAggregator(8 * 1024 * 1024));//HTTP粘包拆包
		channelPipeline.addLast(healthCheckHandler);
		channelPipeline.addLast(wafFilterHandler);
		channelPipeline.addLast(traceStartHandler);
		channelPipeline.addLast(accessLogHandler);
		channelPipeline.addLast(requestParseHandler);
		channelPipeline.addLast(urlWhiteListHandler);
		channelPipeline.addLast(anonymousUserRequestHandler);
		channelPipeline.addLast(completeUserInfoHandler);
		channelPipeline.addLast(recordingBorrowerInfoHandler);
		channelPipeline.addLast(rateLimitingHandler);
		channelPipeline.addLast(uidShardingHandler);
		channelPipeline.addLast(requestDispatchHandler);
		channelPipeline.addLast(exceptionHandler);
	}    
}
```

##### 3、 TudorIdleStateHandler、HttpServerCodec、HttpObjectAggregator

##### 4、healthCheckHandler

##### 5、wafFilterHandler

##### 6、traceStartHandler

##### 7、accessLogHandler

##### 8、requestParseHandler

##### 9、anonymousUserRequestHandler

##### 10、completeUserInfoHandler

##### 11、recordingBorrowerInfoHandler

##### 12、rateLimitingHandler

##### 13、UidShardingHandler

uid路由分片处理器，过程如下:

- 调用 UidShardingCellRule.decide() 规则用户路由规则获取单元Id。
- 调用 cellManager.getByCellId(targetCellId) 获取单元信息。
- 调用 cellHttpInvoker.invoke(targetCell.getUrl(), ctx); 转发请求到目标单元入口。

##### 14、RequestDispatchHandler

机房内的路由。

```java
	@Override
	public void channelRead(ChannelHandlerContext ctx, Object msg) {
		RequestContext requestContext = getOrCreateContext(ctx.channel());
		
		try {
			AppInfo appInfo = appInfoResolver.getByDomainAndUri(requestContext.getDomain(), requestContext.getPath());
			if (appInfo == null) {
				log.error("AppInfo is null, domain = {}, path = {}", requestContext.getDomain(),
				          requestContext.getPath());
				throw new TudorException(ErrorCode.NO_APP_FOUND_FOR_PATH);
			}
			requestContext.setAppInfo(appInfo);
			
			InternalRouterContext routerContext = new InternalRouterContext(requestContext);
			String tag = internalRouterRule.router(routerContext);
			requestContext.getAppInfo().setAppTag(tag);
			if (StringUtils.isNotBlank(tag)) {
				NinjaClientUtil.addBusinessContext(NinjaClientUtil.APP_GROUP, tag);
			}
			httpInvoker.invoke(new HttpInvokerContext(requestContext.getAppInfo(), requestContext.getFullHttpRequest(), ctx));
		} finally {
			ReferenceCountUtil.release(msg);
		}
	}
```

调用 LightningBasedHttpInvoker .invoke()返回结果

## 消息网关

### 概述

目前公司内的二方应用在和三方应用对接时，采用的是主动调用三方应用的API来发送业务信息， 这种方案的缺点是：

- 应用耦合度高，需要对接多个三方应用，而且对接的API格式还可能不统一。
- API调用是同步的，调用时需要考虑调用失败的重试，增加了系统复杂度以及各个二方应用重复开发的成本。

出口消息网关给三方应用提供了消费消息的能力（需要进行授权），后续还将提供投递消息的能力。 需要注意的是：

- 消息网关对接的是三方应用的服务器，不直接对接终端。
- 通过websocket + ssl 保证数据传输的安全。
- 有必要的消息授权和身份验证机制，保证消息对外输出的安全。

环境地址

- [测试环境](http://cloud.wacai.info/k2/application/loan/app/baige-bridge)

### 架构

架构如下：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210410093718189.png" alt="image-20210410093718189" style="zoom:50%;" />

下面一张是习酒画的，包含了更多的细节：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210410093421782.png" alt="image-20210410093421782"/>

- ISV集群：接入出口消息网关的三方应用，通过使用baige-sdk进行消息的消费，具备心跳和断链重连机制； websocket + ssl保证数据传输的安全；
- 消息代理层：负责对三方应用进行接入控制（身份验证、消息接入授权检查）、消息消费和投递请求的转发；
- 反向代理： 提供到消息代理层的负载均衡。

- 开放平台验签服务： 以dubbo服务开放验签接口
- kafka http bridge: 挖财的Kafka消费代理，具体请参考[这里](http://git.caimi-inc.com/middleware/kafka-http-client/wikis/kafka-client-upgrade)
- 管理平台： 负责应用接入授权、消息接入授权等；
- 二方应用： 公司内部应用；

### SDK梳理

#### API

```java
String appKey = "5hdr7hq4qfdm";
String appSecret = "809374c7cc5a449281dd914f2bfca47b";
//测试环境服务器地址
String wsServerURL = "ws://baige-bridge-8888.loan.k2.test.wacai.info/ws";
//创建消息消费者
DefaultMQConsumer defaultMQConsumer = new DefaultMQConsumer(
    appKey, appSecret, wsServerURL);①

defaultMQConsumer.registerMessageListener("topic_name", new MessageListener() {

    @Override
    public ConsumeStatus consumeMessages(Message message) {
        return ConsumeStatus.SUCCESS;
    }

});②
defaultMQConsumer.start();③
```



#### 主流程

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210429144458363.png" alt="image-20210429144458363" style="zoom:50%;" />

##### 1、初始化

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210411091049217.png" alt="image-20210411091049217" style="zoom:50%;" />

注意点：

- MQClientInstance 会实例化 ClientRemotingProcessor 用于接收来自服务端的请求，并注册到底层通信 WebSocketRemotingClient 上。

- WebSocketClient 是jetty的类。

##### 2、注册

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210411091404002.png" alt="image-20210411091404002" style="zoom:50%;" />

 把 MQAtomConsumer 实例化加入到DefaultMQConsumerInnerImpl的sets中，PullMessageService相当于一个执行器。

##### 3、启动

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210411091948179.png" alt="image-20210411091948179" style="zoom:50%;" />

PullMessageAtomService 是一个独立线程，会执循环方法，不停的调用拉消息。拉消息是通过 MQClientAPIImpl 实现，通过调用底层通信并带上PullMsgCallback回调。

PullMessageAtomService 的间隔时间通过构造函数中的 pullIntervalMs参数进行控制。

##### 4、接收消息

主要流程：

- WsSocket 收到来自服务端的数据，onReceiveFrame()被触发，执行解码得到 RemotingCommand ，调用SocketListener回调。
- 调用WebSocketRemotingClient.processMessageReceived() 方法判断影响类型，如果是请求执行 ClientRemotingProcessor，这个类在前面已经被初始化。
- 如果是响应类型，执行 ResponseFuture 中的回调。如果关联到对应的ResponseFuture呢？通过opaque一个自增ID，在RemotingCommand构造函数中设置。
- ResponseFuture 中的回调 实际上就是 MQClientAPIImpl执行pullMessage方法中设置的PullMsgCallback回调，里面会查看消息的状态，是否存在，如果存在调用消息消费逻辑。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20210411093233218.png" alt="image-20210411093233218" style="zoom:50%;" />

一个 ResponseFuture 只会执行一次

```java
public void executeInvokeCallback() {
    if (invokeCallback != null) {
      if (executeCallbackOnce.compareAndSet(false ,true)) {
        invokeCallback.operationComplete(this);
      }
    }
  }
```

#### 通信实现

底层基于webSocket通信， 实现类是WebSocketRemotingClient 。

##### 请求超时

这个类会启动一个线程每隔2秒钟扫描 responseTable 中的存放的请求，如果超时调用responseFuture.executeInvokeCallback()方法。


```java
public void scanResponseTable() {
    Iterator<Entry<Integer/*opaque*/, ResponseFuture>> iterator = responseTable.entrySet().iterator();
    while (iterator.hasNext()) {
    	//判断如果已到请求超时时间。
        if(){
        	responseFuture.executeInvokeCallback();    
        }
    }
}
```

##### 重连

当 jetty 检测到 socket 断开，会调用 WsSocket 的onClose方法，触发一系列回调

```java
@OnWebSocketClose
public void onClose(int statusCode, String reason) {    
    for (SocketListener socketListener : socketListeners) {
        socketListener.onWebSocketClose(wsSocket, statusCode, reason, remoteAddr);
    }
}
```

最终调用的是 MQClientInstance 构造函数中注册的回调。

```java
  public MQClientInstance(ClientConfig clientConfig, int instanceIndex, String clientId) {
      this.mqClientAPIImpl.registerListener(new MQClientAPIImpl.Listener() {
          //省略相关代码
      }
  }
```

##### 心跳

MQClientInstance 启动时，会启动一个 MQClientFactoryScheduledThread 线程，每隔3秒调用 sendKeepAliveHeartBeatToServer() 发送心跳包。最终交给 WsSocket.sendPing() 实现:

```java
  public void sendPing() throws IOException {

    ByteBuffer byteBuffer = ByteBuffer.allocate(1);
    session.getRemote().sendPing(byteBuffer);

  }
```

### 服务端梳理

#### 执行流程

##### 1、应用启动 BridgeServerController

入口类：BridgeServerController，初始化方法：

```java
@Override
protected void doStart() throws Exception {
    /*初始化参数*/
    init();
    //初始化websocekt服务
    initWebsocketServer();
    /*注册request code处理器*/
    registerProcessor();
    this.remotingServer.start();
}
```

主要流程： 

- 初始化WebsocketRemotingServer;
- 注册Processor;

Processor包括以下三种：

```java
public void registerProcessor() {
    //code=1
    this.remotingServer
        .registerProcessor(RequestCode.PULL_MSG, this.pullMessageProcessor, configFactory.getPullMessageExecutor());
	//code=5
    this.remotingServer
        .registerProcessor(RequestCode.ACK_MSG, this.ackMessageProcessor, configFactory.getAckMessaageExecutor());
	//code=7
    this.remotingServer
        .registerProcessor(RequestCode.PUSH_MSG, this.sendMessageProcessor, configFactory.getSendMessageExecutor());

}
```

这三个Processor 在后续 ChannelHandler 收到来自客户端的请求之后会被调用。

##### 2、设置pipleline

WebsocketRemotingServer 继承 NettyRemotingServer，NettyRemotingServer 会根据配置来初始化 ServerBootstrap，设置线程池大小。WebsocketRemotingServer 会设置 pipeline:

```java
public class WebsocketRemotingServer extends NettyRemotingServer { 
    
    protected ChannelInitializer<SocketChannel> createChannelInitializer(EventExecutorGroup group
          , NettyRemotingServer nettyRemotingServer) {
        return new ChannelInitializer<SocketChannel>() {
          @Override
          protected void initChannel(SocketChannel ch) throws Exception {
            ch.pipeline().addLast(
                group,
                new HttpServerCodec(), //request decode and response encode ;
                new HttpObjectAggregator(64 * 1024),//聚合Http请求成为一个FullHttpRequest
                new HttpRequestHandler(webSocketPath, authorizeHook),//匹配path,则升级成websocket请求；否则是Http请求。
                new WebSocketServerProtocolHandler(webSocketPath),//处理websocket握手和frame解析。
                new WebSocketFrameHandler(),//负责BinaryWebSocketFrame和ByteBuf的编解码
                new NettyEncoder(),//RemotingCommand编码成ByteBuf
                new NettyDecoder(),// ByteBuf 解析成RemotingCommand;
                new NettyConnetManageHandler(NettyRemotingServer),
                new NettyServerHandler(nettyRemotingServer) //处理RemotingCommand;
            );
          }
        };
    }
}    
```

##### 3、编码器-NettyDecoder

基于 LengthFieldBasedFrameDecoder 实现。

```java
public class NettyDecoder extends LengthFieldBasedFrameDecoder{
    public  NettyDecoder() {
    /*length(4) | header length(2) | header data | body data
    * ,  此配置摘掉头4个字节 。*/
    	super(FRAME_MAX_LENGTH, 0, 4, 0, 4);
  	}
    @Override
  	public Object decode(ChannelHandlerContext ctx, ByteBuf in) throws Exception {
        ByteBuf frame = (ByteBuf) super.decode(ctx, in);
        if (null == frame) {
        	return null;
      	}
        ByteBuffer byteBuffer = frame.nioBuffer();
        return RemotingCommand.decode(byteBuffer);  
  	}
}
```

##### 4、处理连接-NettyConnetManageHandler

这个类覆写了以下几个方法：

```java
@Override
public void channelRegistered(ChannelHandlerContext ctx) throws Exception {
    final String remoteAddress = RemotingUtil.parseChannelRemoteAddr(ctx.channel());
     super.channelRegistered(ctx);

    if (this.nettyRemotingServer.getChannelEventListener() != null) {
      this.nettyRemotingServer.putNettyEvent(new NettyEvent(NettyEventType.REGISTER, remoteAddress.toString(), ctx.channel()));
    }

}

@Override
public void channelActive(ChannelHandlerContext ctx) throws Exception {
    final String remoteAddress = RemotingUtil.parseChannelRemoteAddr(ctx.channel());
     super.channelActive(ctx);
	//发送事件
    if (this.nettyRemotingServer.getChannelEventListener() != null) {
      this.nettyRemotingServer.putNettyEvent(new NettyEvent(NettyEventType.CONNECT, remoteAddress.toString(), ctx.channel()));
    }
}

@Override
public void channelInactive(ChannelHandlerContext ctx) throws Exception {
    final String remoteAddress = RemotingUtil.parseChannelRemoteAddr(ctx.channel());
     super.channelInactive(ctx);
	//发送事件
    if (this.nettyRemotingServer.getChannelEventListener() != null) {
      this.nettyRemotingServer.putNettyEvent(new NettyEvent(NettyEventType.CLOSE, remoteAddress.toString(), ctx.channel()));
    }
}

@Override
public void userEventTriggered(ChannelHandlerContext ctx, Object evt) throws Exception {
    if (evt instanceof IdleStateEvent) {
      IdleStateEvent event = (IdleStateEvent) evt;
      if (event.state().equals(IdleState.ALL_IDLE)) {
        final String remoteAddress = RemotingUtil.parseChannelRemoteAddr(ctx.channel());
        LOGGER.warn("NETTY SERVER PIPELINE: IDLE exception [{}]", remoteAddress);
        RemotingUtil.closeChannel(ctx.channel());
        if (this.nettyRemotingServer.getChannelEventListener() != null) {
          this.nettyRemotingServer
              .putNettyEvent(new NettyEvent(NettyEventType.IDLE, remoteAddress.toString(), ctx.channel()));
        }
      }
    }

    ctx.fireUserEventTriggered(evt);
}


```

都是发送相关事件。这些事件会放入 NettyRemotingAbstract 中的队列，会有一个独立线程单独调用响应的事件监听。比如 CLOSE 事件会通知 ConsumerManager.doChannelCloseEvent( ) 删除对应的 ClientChannelInfo

##### 4、请求处理器-NettyServerHandler

```java
public class NettyServerHandler extends SimpleChannelInboundHandler<RemotingCommand> {
  private final NettyRemotingServer nettyRemotingServer;
    
  @Override
  protected void channelRead0(ChannelHandlerContext ctx, RemotingCommand msg) throws Exception {
    this.nettyRemotingServer.processMessageReceived(ctx, msg);
  }
}
```

通过 msg.code 找到对应的 Processor:

- 当code为1表示 pull 请求。
- 当code为5表示 ack请求。
- 当code为7表示表示push请求。

然后把Processor扔到关联的线程池处理

```java
public void processRequestCommand(ChannelHandlerContext ctx, RemotingCommand cmd) {
    final Pair<NettyRequestProcessor, ExecutorService> matched = this.processorTable.get(cmd.getCode());
    Runnable runnable = ()->{
        //交给Processor处理
		matched.getProcessor().asyncProcessRequest(ctx,cmd,callback);
    };
    matched.getExecutor().submit(runnable)
}
```

##### 5、处理拉请求 PullMessageProcessor

实际处理拉请求。

```java
@Component
public class PullMessageProcessor extends AuthCheckProcessor {
    @Override
    protected void doAsyncProcessRequest(ChannelHandlerContext context, RemotingCommand request,
        AuthorizeData authorizeData, AsyncProcessCallback asyncProcessCallback) {		
        MessageConsumer consumer = consumerManager.getConsumer(authorizeData.getAppkey(), topic, consumerId);
    	//直接拉取，实际上是同步的
        consumer.asynFetch(callback);
    }
    
}
```

### OOM问题排查记录

错误日志：

```
2021-04-14 20:26:53,269 WARN  [WsSocketThread_3] c.w.o.b.s.p.DefaultMQProducerInnerImpl:104 - [] [] [][bds=] reconnect to ws://open.wacai.com/mq catch Exception
java.util.concurrent.ExecutionException: org.eclipse.jetty.websocket.api.UpgradeException: 101 Switching Protocols
    at java.util.concurrent.CompletableFuture.reportGet(CompletableFuture.java:357)
    at java.util.concurrent.CompletableFuture.get(CompletableFuture.java:1915)
    at com.wacai.open.baige.remoting.netty.websocket.client.WebSocketRemotingClient.reconnect(WebSocketRemotingClient.java:450)
    at com.wacai.open.baige.sdk.MQClientAPIImpl.reconnect(MQClientAPIImpl.java:410)
    at com.wacai.open.baige.sdk.MQClientInstance.reconnect(MQClientInstance.java:124)
    at com.wacai.open.baige.sdk.producer.DefaultMQProducerInnerImpl$2.onClose(DefaultMQProducerInnerImpl.java:100)
    at com.wacai.open.baige.sdk.MQClientInstance$2.onClose(MQClientInstance.java:104)
    at com.wacai.open.baige.sdk.MQClientAPIImpl$1.onClose(MQClientAPIImpl.java:97)
    at com.wacai.open.baige.remoting.netty.websocket.client.WebSocketRemotingClient$SocketListenerAdaptorImpl.onWebSocketClose(WebSocketRemotingClient.java:567)
    at com.wacai.open.baige.remoting.netty.websocket.WsSocket$1.run(WsSocket.java:107)
    at java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:511)
    at java.util.concurrent.FutureTask.run(FutureTask.java:266)
    at java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)
    at java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)
    at java.lang.Thread.run(Thread.java:748)
Caused by: org.eclipse.jetty.websocket.api.UpgradeException: 101 Switching Protocols
    at org.eclipse.jetty.websocket.client.WebSocketUpgradeRequest.onComplete(WebSocketUpgradeRequest.java:513)
    at org.eclipse.jetty.client.ResponseNotifier.notifyComplete(ResponseNotifier.java:193)
    at org.eclipse.jetty.client.ResponseNotifier.notifyComplete(ResponseNotifier.java:185)
    at org.eclipse.jetty.client.HttpReceiver.terminateResponse(HttpReceiver.java:459)
    at org.eclipse.jetty.client.HttpReceiver.responseSuccess(HttpReceiver.java:405)
    at org.eclipse.jetty.client.http.HttpReceiverOverHTTP.messageComplete(HttpReceiverOverHTTP.java:297)
    at org.eclipse.jetty.http.HttpParser.handleHeaderContentMessage(HttpParser.java:598)
    at org.eclipse.jetty.http.HttpParser.parseFields(HttpParser.java:1115)
    at org.eclipse.jetty.http.HttpParser.parseNext(HttpParser.java:1369)
    at org.eclipse.jetty.client.http.HttpReceiverOverHTTP.parse(HttpReceiverOverHTTP.java:170)
    at org.eclipse.jetty.client.http.HttpReceiverOverHTTP.process(HttpReceiverOverHTTP.java:131)
    at org.eclipse.jetty.client.http.HttpReceiverOverHTTP.receive(HttpReceiverOverHTTP.java:70)
    at org.eclipse.jetty.client.http.HttpChannelOverHTTP.receive(HttpChannelOverHTTP.java:130)
    at org.eclipse.jetty.client.http.HttpConnectionOverHTTP.onFillable(HttpConnectionOverHTTP.java:116)
    at org.eclipse.jetty.io.AbstractConnection$ReadCallback.succeeded(AbstractConnection.java:279)
    at org.eclipse.jetty.io.FillInterest.fillable(FillInterest.java:110)
    at org.eclipse.jetty.io.ChannelEndPoint$2.run(ChannelEndPoint.java:124)
    at org.eclipse.jetty.util.thread.Invocable.invokePreferred(Invocable.java:128)
    at org.eclipse.jetty.util.thread.Invocable$InvocableExecutor.invoke(Invocable.java:222)
    at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.doProduce(EatWhatYouKill.java:294)
    at org.eclipse.jetty.util.thread.strategy.EatWhatYouKill.run(EatWhatYouKill.java:199)
    at org.eclipse.jetty.util.thread.QueuedThreadPool.runJob(QueuedThreadPool.java:672)
    at org.eclipse.jetty.util.thread.QueuedThreadPool$2.run(QueuedThreadPool.java:590)
    ... 1 common frames omitted
Caused by: java.lang.OutOfMemoryError: GC overhead limit exceeded    
```

dump内存发现大量WebSocketSession对象：

![image-20231212155006277](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20231212155006277.png)

原因不断重连过程中，WebSocketSession被大量创建没有释放，[issues-2655](https://github.com/jetty/jetty.project/issues/2655)，相关代码：

```java
public class WebSocketUpgradeRequest{
    public void upgrade(HttpResponse response, HttpConnectionOverHTTP oldConn){
        WebSocketSession session = getSessionFactory().createSession(requestURI,localEndpoint,connection);
        session.addManaged(extensionStack);
        session.setFuture(fut);
        wsClient.addManaged(session);//session会被加入wsClient，并且不释放。
}
```

修改方式：

```java
public class WebSocketRemotingClient  extends NettyRemotingAbstract implements RemotingClient {
 
@Override
  public synchronized Future<Boolean> reconnect(long connectTimeoutMs) throws Exception{
	  //先关闭之前的实例
	  webSocketClient.stop();
	  webSocketClient.destroy();    
    //再次开启
    this.webSocketClient.start();
    Future<Session> future = this.webSocketClient.connect(this.wsSocket, uri, clientUpgradeRequest);
    Session session = future.get(connectTimeoutMs, TimeUnit.MILLISECONDS);
    return new Future<Boolean>();
  }
}
```



## 网关中台-octopus

网关配置信息都保存在 octopus 应用中，入口，出口网关通过dubbo服务全量获取配置，增量数据通过kafka消息通知。

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_677ab561-3e77-4c88-86f6-54c4df88d72a.png" alt="企业微信截图_677ab561-3e77-4c88-86f6-54c4df88d72a" style="zoom:50%;" />

数据库：palamidi



## 问题排查

### 修改 loan.open.precredit.repay.result.feedback 第三方回调域名

方法：

1、通过sql语句查询得到app_info为428

```
select * from app_api_service_url where service_url like '%wejoydata%';
```

2、查询428的app， 名称叫 loan.open.kds.weibo

```
select * from app_info  where id ='428';
```

3、在页面API-APP映射页面查询 loan.open.kds.weibo，修改域名



