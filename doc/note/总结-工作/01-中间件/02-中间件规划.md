

## 2022-中间件OKR

### 1、研发效能

背景和目标：支持的需求多而杂，现在的做法更多是功能的堆砌，缺乏整体规划。

行动计划：

- 支持一键部署应用到多个云环境的能力。
- 增加构建灰度的能力，达到北斗agent能按照指定应用发布。
- 构建脚本插件化，适应私有云环境下去挖财组件的能力。

### 2、小文件存储

背景和目标：domino还是三年前的架构，存在维护困难，架构不合理，稳定性差的问题。七牛云 之前出过多次问题 ， 怀瑾那边是想cdn的源站切回挖财内 ，原七牛云上面的资源文件都存domino ，用domino做cdn的源站。

行动计划：

1、一期(整体代码梳理，重构)

- domino模块合并 。

- 底层ceph接口重构，改用s3 api。
- domino-file-gateway 和domino-fog 原本访问数据库的接口 改为访问 domino-file-web。

2、二期（架构调整）

- ceph 集群合并（ 只留full集群，但full集群没有文件索引，利用es做索引）。
- 上传文件接口改造双写 ceph和七牛，兼容七牛云接口。
- 下载文件接口 优先访问domino，其次七牛云，同时兼容七牛云接口。
- domino和七牛云  冲突namespace 处理。
- domino在公⽹上的⽹关对来⾃cdn的请求做鉴权。
- ceph和七牛 文件同步kafka 去掉。
- 支持打包上传；

3、三期(需求支持)

- 如债权影音文件存储的数据安全问题。
- 文件快速迁移。
- 数据扫描分析及归档、删除等。

### 3、消息服务

背景和目标：挖掘业务方对消息中间件的需求，提升Hermes稳定性，推广新版本Hermes。

行动计划：

- 完善Hermes监控能力，SDK、直连接入控制台监控。
- 完善topic管理，清理历史topic，新增topic增加审批流程。
- 新版客户端线推广。
- 新特性功能：
  - 支持多partition顺序消息消费。
  - 支持死信队列。
  - 支持根据header头进行消息过滤。
  - 大数据消息查询。

### 4、服务链路治理

背景和目标：提升链路监控数据质量；降低部署成本，支持私有化部署。

行动计划

- 北斗trace数据标准化，对接业界标准。
- 监控告警对接云平台，支持私有化部署。

### 5、团队建设

背景和目标：打造专业、高效、主动的技术团队

行动计划

- 完善中间件文档；
- 完善新人成长机制；
- 完善review机制，关注代码质量；

### 6、创新产品

#### 1. 流量回放

流量回放是系统重构、拆分、中台化时重要的自动化回归手段。通过采集可录制流量，在指定环境回放，再逐一对比每个调用和子调用差异来发现接口代码是否存在问题。因为线上流量大、场景全面，可以有效弥补人工评估测试范围的局限性，进而降低业务快速迭代带来的风险。

参考：

- [千亿级公司低代码平台的测试体系介绍](https://tech.youzan.com/qian-yi-ji-gong-si-di-dai-ma-ping-tai-de-ce-shi-ti-xi-jie-shao/)
- [流量录制与回放技术实践](https://www.cnblogs.com/nullllun/p/15205442.html)
- [有赞流量回放在中台营销的实践](https://mp.weixin.qq.com/s/rlqXjr17u70nm1mgD8-r0g)

- 实例剥离：比如有台机器存在故障，需要保留现场，不能kill，如何做到？
- 调研监控告警。参考滴滴开源的夜莺

#### 2. APM

- 链路分析。
- 轻松部署，私有云环境支持。

参考:

- datadog

#### 3. 服务治理

- 服务查询： 查看应用列表，应用详情：包括实例情况，接口、PATH，消费者，等meta信息。
- 服务契约： API查询、Swagger注解解析、基于此提供数据mock等能力。
- 标签路由： 多版本开发测试，A/B Testing
- 离群实例摘除： Microservice Outlier Ejection。 
- 服务鉴权：  黑、白名单控制。
- 服务限流：  基于QPS的限流方案。
- 服务降级：  提供配置降级规则。
- 金丝雀发布：提供灰度能力。
- 优雅关闭： 也叫无损下线，应用停止到重启恢复服务这个阶段不影响正常的业务请求。（无需配置）

参考：

- [有赞服务注册与发现架构演进](https://mp.weixin.qq.com/s/EqNNf1tiPGNLyvMqeJ7-Kg)
- [微服务治理实践 | 金丝雀发布](https://gobea.cn/blog/detail/MrBpGOon.html)
- [微服务离群实例摘除](https://cloud.tencent.com/developer/news/491930)



## 中间件加密方案

### 一、背景

在国家《个人信息保护法》正式实施的背景下，法务要求公司内部系统对用户相关数据进行加密传输、存储。对于中间件主要涉及到Dubbo、Hermes、Cache等组件的加解密支持和秘钥统一管理。

### 二、方案设计

#### 1、Dubbo加解密

<img src="http://git.caimi-inc.com/middleware/docs/uploads/2aa310b63e879e6a591536c694c4c47a/image.png"  width = "800"   />

Dubbo加解密流程：

1. Provider应用配置需要加密的接口。
2. Consumer应用调用接口时，会从注册中心获取到是否需要加密信息。
3. 当接口需要加密时，通过中间件SDK加密，中间件SDK从配置中心获取秘钥并缓存到本地。
4. 加密后的请求数据发送到Provider。
5. Provider收到请求之后，通过中间件SDK解密，中间件SDK从配置中心获取秘钥并缓存到本地。
6. Provider完成业务处理之后，Dubbo SDK调用中间件SDK进行加密。
7. 加密后的响应数据返回给Consumer。
8. Consumer收到响应，通过中间件SDK解密。

分支流程：

- 对于未升级Consumer发送的数据，Provider 会进行兼容，通过 Dubbo header 判断。

#### 2、Hermes加解密

<img src="http://git.caimi-inc.com/middleware/docs/uploads/aa4fe14feb19a66eececaa7ea97dacd6/image.png"  width = "800"   />

Hermes加解密流程：

1. 开发人员在控制台配置Topic开启加密。
2. Producer发送消息时，会先查询Topic是否开启加密。
3. 当Topic开启加密时，通过中间件SDK加密，中间件SDK从配置中心获取秘钥并缓存到本地。
4. 发送数据到Broker。
5. Consumer消费数据。
6. Consumer收到数据会先查询Topic是否开启加密。
7. 当Topic开启加密，会调用中间件SDK解密。

#### 3、Cache加解密

<img src="http://git.caimi-inc.com/middleware/docs/uploads/db53b88756c689d103524f023fee109d/image.png"  width = "800"   />

Cache加解密流程：

1. 获取秘钥进行加密。
2. 写缓存。
3. 从缓存读取数据。
4. 获取秘钥进行解密。

#### 4、加解密SDK

中间件负责统一维护秘钥和封装加解密sdk。

### 三、开发计划

| 模块         | 开发 | 测试 | 负责人 |
| ------------ | ---- | ---- | ------ |
| 加解密SDK    | 3天  | 1天  | 柏仁   |
| Dubbo加解密  | 4天  | 2天  | 柏仁   |
| Hermes加解密 | 2天  | 1天  | 冷锋   |
| Cache加解密  | 2天  | 1天  | 千鸟   |

整体留1天buf，9月28号提交给业务方使用。

### 四、问题反馈

9月14号会议问题

- Dubbo加解密服务增加一个全局开关禁用加密，便于测试环境测试。
- 对于秘钥的管理如何保证安全。
- hermes批量消费也希望能解密。@古记 提出
- topic 数据大小限制和之前需要保持一致。
- sailfish 控制台提供解密服务。

## 中间件鉴权方案

### 一、背景

在国家《数据安全法》正式实施的背景下，法务要求公司内部系统对用户相关数据进行加密传输、存储。对于中间件主要涉及到Dubbo、Hermes、Cache等组件的加解密支持和秘钥统一管理。

### 二、方案设计

#### 1、Dubbo

<img src="http://git.caimi-inc.com/middleware/docs/uploads/85fde43cdc036263d128f02b1040f0c8/image.png"  width = "800"   />

业务流程：

1. 服务提供方配置需要鉴权的dubbo接口。
2. 服务使用方去平台申请dubbo接口的访问权限。
3. 服务使用方进行审批。
4. 审批通过之后平台会为使用方产生AccessKey和SecretKey(简称AK和SK)，同时会自动下发给consumer和provider。
5. consumer调用provider时会带上AK和签名信息，签名信息基于SK计算产生。
6. provider 通过AK对应的SK对信息做同样的计算，得到的签名和consuemr传递过来的签名相同，鉴权通过，否则返回错误。



兼容性：

1. 增加一个配置，允许开启不严格鉴权模式，即鉴权失败只打印告警日志。



#### 2、Hermes

<img src="http://git.caimi-inc.com/middleware/docs/uploads/a42a71974ad048a15249c3be8b0ffa25/image.png"  width = "800"   />

业务流程：

1. topic 负责人创建一个鉴权 topic。
2. topic使用方去平台申请topic的发送/消费权限。
3. topic 负责人进行审批。
4. 审批通过之后平台会为使用方产生UID和SK，同时会自动下发给consumer和provider。
5. Producer 收到下发信息之后会基于UID、SK、超时时间产生一个token。
6. Producer 调用时附带appName+token。
7. Broker 收到请求后对 token 进行鉴权，通过正常执行，否则返回鉴权失败错误。

兼容性处理：

1. 增加一个配置，允许开启不严格鉴权模式，即鉴权失败只打印告警日志。

#### 3、Domino

domino 小文件存储鉴权方案和Dubbo、Hemes类似，这里就不再赘述。

#### 4、Cache

<img src="http://git.caimi-inc.com/middleware/docs/uploads/97670b4f79ae6ab3f6a4ce481a2dec97/image.png"  width = "800"   />

redis 由于缓存key比较难管理，同时redis也并无数据分区的概念，所以通过集群隔离方案解决认证问题，需要认证的业务单独部署需要认证的集群。

#### 5、鉴权平台

鉴权平台提供通用鉴权能力

<img src="http://git.caimi-inc.com/middleware/docs/uploads/59befc605566bdc29f737edb74644e53/image.png"  width = "800"   />



### 三、开发计划

| 模块       | 开发                                      | 测试 | 负责人 |
| ---------- | ----------------------------------------- | ---- | ------ |
| 鉴权平台   | 5天                                       | 1天  | 柏仁   |
| dubbo鉴权  | 2天                                       | 1天  | 柏仁   |
| hermes鉴权 | 开发已完成，测试环境可用                  |      | 冷锋   |
| cache鉴权  | 已支持，需使用最新版本sailfish客户端@千鸟 |      | 千鸟   |
| domino鉴权 | 2天                                       | 1天  | 千鸟   |

## 私有化环境方案

### 背景

现在中间件主要部署了两套私有化物理集群：

- XAMC
- 望润

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220113104413269.png"  width = "1000"   />



望润物理集群比较纯粹，只跑了望润业务，这里稍微复杂的是XAMC集群，上面跑了海川慧富，海南AMC，挖财AMC，这三块业务从业务角度来看是隔离的，在wke上是通过不同的空间进行隔离，从中间件物理资源角度来看是一套集群，所以中间件需要提供业务隔离方案。



### dubbo隔离

dubbo 业务方通过配置不同的 zk.group。



### kafka隔离

kafka 业务方通过 topic 名前缀进行隔离。



### redis 隔离

redis 业务方通过配置不同的schema。



### ES 隔离

es 业务方通过索引名前缀进行隔离。



### wups、wsso、api网关、prophet

wups、wsso、api网关、prophet 这些都是无状态的应用，会为每个业务单独部署(直接部署在业务的空间)，通过不同的 http 服务进行区分，部署形态如下图：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220113104453638.png"  width = "800"   />

## 中间件周报(1.4~1.7)

### 近期目标

- 研发效能支持
- 轻量级网关推进
- Hermes/Kafka 稳定性提升
- Domino小文件存储协议重构

### 本周进展

#### 研发效能

- Jenkins稳定性改造，master节点改造云存储(CephFS pv)，生产环境迁移完成。
- 效能统计报表优化：参照建议对数据重新排版。
- 研发效能文档整理：废弃过时文档，整理新文档。
- 配合安全团队排查部分log4j漏洞的应用，历史原因QA部署了多套smurf压测平台，现已将无用集群下线。

#### 轻量级网关&wups

- 网关 accessLog方案调研 + 代码编写，开发完成。
- 网关API导入导出，对接前端，已经发布上线。
- 定位出口网关无法传递文件的BUG，产出排查文档。

#### Hermes

- 修复Hermes SDK客户端proxy模式到direct模式的offset同步问题，完善消费者的fetch和ack的offset记录。
- 财商教育业务开发@扣舷 希望客户端支持单个进程创建多个相同的消费者，并发处理顺序消息的需求，目前已经给业务测试环境升级。
- Hermes SDK在Spring项目中使用的示例项目和文档编写并提供@麦子，后续项目升级hermes SDK版本，并在测试环境部署供hermes日常测试使用。

#### 存储平台

- 针对amc @回声 那边对于静态资源上传通过s3客户端，下载通过外部cdn来直接对接ceph的需求方案讨论，需要确认下ceph这块对于cdn的请求是否支持白名单，然后还有https的支持，但不建议直接暴露ceph。
- 针对大数据的自动化创建drc订阅任务的需求，改造drc-admin并提供对应接口完成。
- domino整理了下代码，清理了一些无用代码，对于对象重复删 问题修复
- 测试环境云平台使用的ceph集群机器故障，重启后部分节点出现数据不均匀，对数据进行rebalance。

### 其他事项

- AMC环境轻量化网关和wups直连DB存在安全隐患，从ip变更为vip。
- 发布平台修改后端配置，@天星 帮忙在墨子平台搭建操作页面，Flow-E后端接口适配，提升了不少效率。



##           2021-S2规划

### 轻量化网关

背景和目标：老版网关依赖十多个系统，部署和维护复杂，业务方反馈配置繁琐，所以规划轻量化网关，目标提高系统架构的柔性，适应部署环境的变化。

行动计划：

- 优化网关流量转发性能。
- 增加轻量化网关的监控、告警、限流能力。
- 轻量化网关推广，老版网关迁移。

## 2022-S2规划

### hermes 架构治理，除去冗余应用，完善topic元数据

1、 历史遗留应用清理，下掉过去3年没有下掉的老proxy和老bridge 虚拟机。
2、 清理无用topic，按照新的业务线管理topic。
3、完善面向管理员的大盘报表，产出按业务线维度的topic资源占用可视化大盘。



## 2023-S2规划

### 服务治理

- 应用度量平台支持在线诊断能力，打通问题排查最后一公里
- wacai-boot 以及相关中间件支持jdk 11 
- harmony 稳定性提升

### 通用存储

- hermes-agent 新版本，支持延迟队列、异步消费、死信队列、广播
- 日志平台，提升Kibana易用性 [携程机票前台Trace系统的演进之路](https://mp.weixin.qq.com/s/W8Vi0VFiJsfVtKCF4pZ_YA)
- es 版本升级到7+

### 研发效能

- 代码质量建设：code review工具增强、sonar工具的接入
- 代码质量与AI的交互
-  基础镜像构建&公有云的支持

### 其他

- 业务需求支持&日常维护



## 2024-S1规划

### 服务治理

- 在线诊断平台推广和项目二期启动
- 度量平台后台管理二期
- 度量平台能力输出(trace+metrics)，扩大场景范围：业务监控、前端监控、全息排查、
- 在线诊断：
  - 代码热更新能力
  - 方法级别rt、trace诊断产品化
  - 自动触发
- 代码分析：通过字节码增强实现类似jacoco的效果，自动分析应用代码在线调用情况。


### 存储平台

- ES版本升级和治理
- kv存储kvrocks业务推广

### 研发效能

- 代码质量、发布流项目推广

- AI 智能化更多场景落地

- JDK 11升级（中间件开始）

- 应用非正常重启，自动拿到dump分析和相关日志

  



## 2024-S2规划

### 研发效能

目标：降本增效，提升业务效能。

- **应用构建时长优化**
- wone 团队数据清理
- **研发效能首页重构，work at 挖财云**
- **AI相关探索**：
  - 聊天机器人
  - 工作助手Agent
  - 辅助编程 + 代码review http://git.caimi-inc.com/


### 应用度量

- 代码分析：在线覆盖率开发和上线

- **代码分析落地场景**，产品推广，用户反馈

  

### 存储平台

- **ES稳定性提升**：ES监控、索引生命周期管理、分片分配均衡
- **herems新功能**：支持广播
- redis sdk升级：统一API支持cluster和stanldone两种模式

参考：

- [浅析 “代码可视化” | 京东云技术团队](https://testerhome.com/topics/37943)

- [如何在一分钟内实现微服务系统下的架构可视化](https://www.cnblogs.com/yunqishequ/p/10043302.html)

- 代码分析:

  - [JaCoCo计算代码覆盖率原理](https://juejin.cn/post/6844904115953205255)

  - [ 携程代码分析平台，快速实现精准测试与应用瘦身](https://mp.weixin.qq.com/s?__biz=MjM5MDI3MjA5MQ==&mid=2697275724&idx=1&sn=049f287adab6b982f139b7341efcc584)

  - [网易传媒精准测试实践](https://blog.csdn.net/weixin_48726650/article/details/106970284)

  - [严选精准测试实践（进阶篇）](https://www.modb.pro/db/441075)

  - [java代码改动影响范围分析工具](https://github.com/baikaishuipp/jcci)

    

## 2025-S1规划



1. 辅助编程工具，切入需求理解、代码编写、单元测试、CodeReview整个环节，自动添加Swagger
2. 服务治理：完善之前的注册中心，实现动态链路，参考[dubbo-kube](https://github.com/apache/dubbo-kubernetes/)
3. 降本增效：参考[koupleless](https://github.com/koupleless/koupleless)实现模块化研发框架
4. IDEA插件：WaCloudGPT整合开发、测试、部署、辅助编程等功能，重点提供**面向场景的辅助编程** 。备选名：SmartCodeAssistant

编译时增加hook，产生AI报告，自动为Controller、Service类增加

- 代码注释
- 单元测试
- Swagger注解
- 文档：READEME.md、ChangeLog.md



## 2025-S2规划

### AI项目

- wapilot 项目三期，减少幻觉(提升回答质量)、codebase 、知识库、 mcp接入

  

### 服务治理

- 



### 研发效能

### 

## 大厂技术blog

- [携程]()
- [有赞]()
- [哈罗单车]()
- [美团]()
- [B站](https://juejin.cn/post/7384350475738365987)
- [京东云技术团队]()
- [严选技术产品团队]()
- [小红书技术REDtech]()



## 基金项目

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241009150529394.png" alt="image-20241009150529394" style="zoom:50%;" />

问题汇总：

1. 应用可用性状态怎么鉴定？ 只要应用没挂就算正常运行？
2. 故障事件是不是你们自己来填？ 
3. 合格线是针对应用维度还是指标维度？
4. 核心应用列表需要提供



**2024-Q3应用软件运行状态（27个应用）**

| 应用名                            | 每日请求量 | 每日错误数 | 故障事件 |
| --------------------------------- | ---------- | ---------- | -------- |
| fund-pd-provider                  | ********   | 0          | 无       |
| fund-daq-provider                 | 8789162    | 0          | 无       |
| fund-pacific-instruct-provider    | 8534909    | 0          | 无       |
| fund_app_state                    | 7801794    | 0          | 无       |
| fund-position-provider            | 3643129    | 0          | 无       |
| fund-pacific-account-provider     | 3337312    | 0          | 无       |
| sxb-business-provider             | 3039019    | 0          | 无       |
| market-business-provider          | 1843884    | 0          | 无       |
| fund-market-app                   | 1102451    | 1          | 无       |
| fund-pacific-ordermanage-provider | 709323     | 1          | 无       |
| fund-app                          | 638223     | 5          | 无       |
| fund-business-provider            | 594045     | 0          | 无       |
| fund-pacific-antimoney-provider   | 542089     | 0          | 无       |
| fund-data-service                 | 423760     | 0          | 无       |
| fund-pacific-settle-provider      | 172922     | 0          | 无       |
| fund-aip-provider                 | 172922     | 0          | 无       |
| sxb-position-provider             | 120242     | 0          | 无       |
| market-mall-provider              | 104544     | 0          | 无       |
| fund-factor-app                   | 74144      | 0          | 无       |
| welfare-task-provider             | 56746      | 0          | 无       |
| fund-pacific-acquirer-provider    | 51553      | 0          | 无       |
| sxb-bridge-provider               | 48003      | 0          | 无       |
| fund-analyse-provider             | 42659      | 0          | 无       |
| fund-pacific-assets-provider      | 41752      | 0          | 无       |
| fund-agrt-provider                | 26916      | 0          | 无       |
| sxb-app                           | 13672      | 0          | 无       |

**总结**

- 合格线：错误占比小于1%
- 合格占比：100%
- 不合格占比：0%

