大家好！

最近一段时间，中间件团队在提升业务同学研发效率，减少排查问题成本方面做了一些尝试，上线了一系列功能，包含：

- **在线诊断**：在线Debug、热更新、性能分析、线程分析
- **发布平台**：发布流模式、在线CodeReview、Sonar代码检测
- **存储平台**：新增KV存储，兼容Redis协议
- **服务框架**：wcaiboot升级2.5，springboot升级到2.5

这里简单跟大家介绍一下，大家可以来体验使用，有任何建议欢迎联系@柏仁！



## 在线诊断

### 1.1 在线debug

有人调侃程序员，一半的时间在写bug，另外一半的时间在debug，这其实也说明排查问题是非常占用我们日常开发时间的。

去年我们上线了链路跟踪可以很好的发现应用之前的问题，但是没有一个系统可以完美解决所有问题，对于问题的根因只靠链路跟踪有些场景还是相形见绌。

比如下面这条链路：

![image-20240322112832390](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240322112832390.png)

图中我们可以定位到问题发生到 snowstorm-web应用的/asset/query接口，但为什么这个接口会报错，因为埋点信息不够我们无法看到问题的根因，这时候可以借助在线诊断平台的在线debug功能：

![image-20240327104109911](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240327104109911.png)

我们选择一个类进行调试，平台会自动反编译出源码，然后可以在任意一行代码添加断点，当该断点被执行时，会自动捕获上下文信息包括：静态变量、成员变量、局部变量、当前堆栈等信息，方便我们排查：

![image-20240322114417966](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240322114417966.png)

这里你无需担心性能问题，断点只会执行一次，变量信息的获取是存内存并异步返回给诊断平台，不会阻塞正常业务请求。

详细用法可查看中间件文档

### 1.2 热更新

所谓热更新(也称为热部署)，就是在应用正在运行时升级软件，却不需要重新启动应用。对于Java应用程序来说，热更新就是在运行时更新Java类文件。

在我们日常开发测试过程中需要频繁通过发布平台编译、打包、部署到容器平台，再加上应用启动时间，每次可能需要消耗3~5分钟，通过我们提供的热更新功能，可以实现秒级生效，无需重启应用。

比如下列红框代码从Jasmine获取配置，key误写为test1： 

![image-20240322145225843](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240322145225843.png)

通过代码热更新功能可以快速修复该问题：

![image-20240322150554412](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240322150554412.png)



选择需要热更新的类，修改代码：

![image-20240411115027506](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240411115027506.png)



![image-20240411110641140](/Users/<USER>/Library/Application Support/typora-user-images/image-20240411110641140.png)





 

等待数秒之后即更新成功!

**注意：由于jvm的限制，目前热更新不支持类结构的变更，如增加字段、方法等**，要突破jvm的限制技术难度比较大，如果大家有这方面需求可以反馈给我们。

### 1.3性能分析

如果你的应用存在CPU性能问题，可以使用我们提供的性能分析工具，通过async-profiler产生火焰图进行分析，如图：

![image-20240327105117908](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240327105117908.png)

这里对jasmine-server做了性能分析，点击开始会进行CPU采样。

 等待结束之后会产生cpu火焰图，从图中可以看出 jasmine-server 应用CPU消耗主要在字符串处理上。

![image-20240322151811369](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240322151811369.png)



## 发布流

云平台研发效能提供的构建、部署功能相信开发同学每天都会用到，但是标准模式存在一些痛点：

- 每次都需要构建、部署，如果需要部署线上多个环境，重复操作比较多
- 没有和wone打通，无法清楚知道发布对应了哪些需求，需要外部统计
- 没有和git打通，发布之后分支合并需要自己处理
- 扩展能力比较差

针对上述问题，我们提供了另外一种**迭代模式**，只需要配置一次即可一键部署到多套环境：

![image-20240411115523151](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240411115523151.png)





还支持**在线CodeReview**、**Sonar代码检查**、**自动合并分支**、**代码push自动部署** 等提效功能。

项目接入可查看[一分钟接入迭代模式文档](https://pages.wacai.info/e2/portal/)

有兴趣的同学可以联系@洛东

## kv存储

目前挖财分布式缓存只支持 redis ，对于一些内存容量消耗的业务场景使用成本比较高，我们最近测试对比发现apache kvrocks的性能和稳定性满足我们的需求，性能对比：



kvrocks 兼容redis协议，需要接入的同学只需要找我们申请集群，aplicaiton.properties配置文件修改app-id和appKey即可使用。

有兴趣的同学可以联系@柏仁 

## wacai-boot 2.5

在挖财有点年头的业务同学应该对 wacai-boot 框架并不陌生，早前我们通过 wacai-boot-starter 提供了dubbo、web-api、wrpc、分布式锁等功能，不过因为各种原因很早时间还停留在spring-boot1.x上，最近我们升级了wacai-boot，提供2.5版本，支持以下特性：

- 基于 spring-boot2.5 ，支持jdk8、11
- 移除一些不再支持的组件，比如wrpc，idc-registery、ninja
- 支持应用优雅关闭

主要目标还是sdk减负，轻量化，目前还处于开发阶段，有兴趣的同学可以联系@柏仁 

