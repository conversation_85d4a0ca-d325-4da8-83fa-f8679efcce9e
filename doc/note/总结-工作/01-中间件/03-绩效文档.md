## 2019上半年绩效考核

### 业绩-jasmine 稳定性提升

1. 完善super-diamond到jasmine迁移方案，减少迁移成本，做到无需修改代码即可迁移，推动业务部门进行迁移，迁移90%的应用。
2. 解决jasmine-server无法使用ob3发布的问题，无法使用k2环境的问题。
3. 完善单元测试，增加必要的监控，通过8864可查看当前jasmine状态

员工评论

1、 完成了super-diamond到jasmine的自动化迁移方案，代码API全兼容，历史数据自动迁移，用户无需修改java代码只需要简单升级下pom中定义的版本号即可完成迁移，得到业务方的认可，大部分应用已经迁移到jasmine，只剩下理财业务少量不重要的应用还未迁移。

2、完成了jasmine-server自动化部署改造，完成了ob3打包和容器化自动部署，同时也改造了jasmine-node，jasmine-server集群的ip之前是写死在配置文件中，现在改为自动上报到harmony，ip发生变化无需修改配置文件，极大了减少了jasmine部署运维成本。

3、 完成了jasmine-client1.0.6新版本发布，相关优化点13个，新增jasmine-client的8864监控项，秒级排查问题。

### 业绩-jasmine 多级配置开发

1、完成 jasmine 多级配置开发，支持配置按照项目方式继承和覆盖。
2、完成 jasmine 配置隔离开发，支持多个项目配置轻松隔离。
3、增加配置历史版本查看功能。

员工评论

1、 完成jasmine多级配置开发，支持配置按照项目方式继承和覆盖，新版从用户体验上和老版相比有了大幅提升。

2、因为递金云项目打乱了开发节奏，jasmine配置隔离还未开发。

3、jasmine历史版本功能后台功能完成，前端页面暂未提供。

### 业绩-nest类隔离推广

1、完成一次在大部门进行nest相关技术分享。
2、业务使用nest类隔离应用数占比提升10%

员工评论

1、完成类隔离容器在挖财技术大学分享两场。

2、上半年线上应用新接入nest容器应用数49个，主要是递金云项目。

### 价值观

主动服务

super-diamond迁移和jasmine-client稳定性优化是深入了解客户痛点之后主动推动的需求，在方案制定过程中，始终把降低用户使用成本放在第一位考虑，最终super-diamond数据迁移自动化，API全兼容，jasmine-client提供排查工具得到了业务同学的好评。

防患未然

递金云项目中虽然不是项目owner，但是能从大局出发，主动向PM，测试负责人反馈中间件迁移中可能存在的风险，防范未然。

## 2019下半年绩效考核表

### 业绩-nest2.0新版本开发，支持中间件统一运维能力

1、在nest类隔离基础上增加中间件统一运维能力。对于一些底层公共包，在不影响业务使用的情况下，中间件可以做到统一运维能力。
2、nest2.0将配合hermes去proxy改造，在hermes升级过程提供支持。
3、业务使用nest类隔离应用数占比提升15%。

员工评论

在nest类隔离基础上增加中间件统一运维能力，目前还在开发阶段

### 业绩-jasmine工程效率提升

1、去除wups权限系统依赖，jasmine自身提供用户权限管理功能。新环境减少两个系统部署成本，用户无需申请权限，无需绑定动态口令。
2、增加环境动态隔离支持，现在如果业务需要隔离一套新的环境需要完全重新搭建一套jasmine环境，申请新的数据实例，通过环境动态隔离，用户可以通过配置的方式动态隔离出一套新的Jasmine环境，节省大量运维成本。
3、增加spring-boot应用application.properties文件托管功能，因为挖财应用存在不同环境、机房，每个环境、机房的application.properties都不一样，通过jasmine业务应用可以极大减少application.properties的管理成本

员工评论

1、完成了jasmine独立的权限管理开发，不再依赖外部wups，减少jasmine搭建新环境成本，大大减少日常答疑和日常审批的工作量，同时减少了用户接入jasmine的学习成本。

2、完成了jasmine动态配置隔离功能开发，减少用户需要隔离多套不同的环境下配置的成本，在用户中心和中间件多个场景中落地。

3、完成了jasmine托管spring-boot应用application.properties功能，提供对业务用户一套代码需要部署在多个云环境的配置管理能力。

### 业绩-wRPC技术架构

对wRPC关键技术方案进行把控，确保方案能在业务团队中落地。

员工评论

1、带领团队设计wRPC新的技术方案，解决之前wRPC版本设计上的缺陷，业务方无需改造代码即可接入wRPC，全面兼容以前老的的dubbo版本。

2、原计划在消金云项目及点石团队中落地推广，因业务调整消金云项目取消暂停推广。

### 价值观

主动服务

在nest和jasmine的日常答疑中主动挖掘用户的使用痛点，进行了相关改进，提升用户接入中间件的体验，具体包括如下几个方面

1、 针对用户经常反馈的问题进行总结和梳理，纳入到文档。 

2、在代码层面上提供更直观友好的异常提示，比如针对类冲突直接告知用户需要升级某个版本，而不是返回

ClassNotFoundException。

3、提供方便快捷的本地诊断功能，这样发现问题时用户可以初步自我排查，而不是所有问题必须找系统管理员。

## 2020上半年绩效考核

### 项目-降低中间件系统维护成本

1、 jasmine，harmony在预发环境上云。
2、zookeeper部分集群在测试环境上k8s。

员工评论

1、完成 Jasmine，Harmony在预发环境上云实施。

2、完成zookeeper在k8s环境下运行部署的测试工作。

### 项目-服务框架云原生架构演进

1、开发wafe2.0服务治理框架，整合jasmine,harmony,wacai-boot等数个客户端，解决之前升级困难，容易出现类冲突的问题，提供统一，可监控，升级友好的SDK。
2、支持HTTP的服务治理能力。
3、在测试环境中落地

员工评论

1、部分完成 Jasmine，Harmony，Wacai-Boot整合工作，后期和WSE一起推广。

2、WSE立项，确定基于k8s+istio的云原生服务治理方案Wacai. Service. Engine.，已进入开发后期阶段。

### 专业能力-中间件架构演进技术调研

1、调研k8s、service mesh云原生架构方案。
2、调研 java agent字节码植入方案。探索无需业务方修改本地代码，通过打包系统自动织入服务治理逻辑，达到类似istio无侵入，动态运维的效果。

员工评论

1、调研k8s、Service Mesh，蚂蚁金服MOSN，腾讯TSF Mesh，解决了在K8S跨空间中引流的技术难题。

2、调研阿里巴巴jvm-sandbox，阿里云EDAS等字节码植入方案，为下阶段非容器环境下服务治理积累经验。

### 团队贡献-团队技术提升

团队内技术共享至少一次

员工评论

在大团队进行了技术分享--《分布式系统数据一致性漫谈》

###  价值观

- 牛：借鉴业界最新的istio技术，突破性的解决多语言服务治理问题，通过把服务治理能力下层到基础层解决了传统通过SDK需要大面积升级的老大难问题。
- 防患未然：梳理es，redis各个集群数据，索引精简化，同时对redis集群完成容器化改造，提升稳定性和failover能力

## 2020下半年绩效考核表

### 业绩-现有项目维护和稳定性保障

1、jasmine，harmony，zookeeper，dubbo 无线上故障。

2、因人员变动，快速承担了网关，wsso，wups，数据字典等项目的维护工作，均无线上故障。

3、彻底解决了zookeeper集群性能问题，解决了业务系统对分布式锁使用的后顾之忧。

### 项目-服务网格一期落地

1、服务网格动态路由，灰度发布等功能在测试环境中落地。

2、寻找种子用户，推动业务方接入wse系统。

员工评论

1、服务网格在测试环境中落地，解决istio各种各种疑难杂症。

2、中间件jasmine、harmony等应用已迁移到服务网格。

### 项目-服务网格二期功能完善

1、服务网格增加对Dubbo体系的支持。
2、丰富服务网格服务治理类型，增加限流，熔断，安全的支持。

员工评论

1、完善了服务网格中实例部署，灰度发布，动态路由的功能，为未来和挖财云打通奠定基础。

2、和发布平台打通，应用打包之后可以直接部署到服务网格。

3、增加了服务网格和中间件监控信息的整合。

### 专业能力-中间件架构演进技术调研

1、调研服务网格Dubbo协议的支持。
2、探索服务网格在k8s之外的支持

员工评论

调研服务网格Dubbo协议的支持。

### 团队贡献-团队分享机制建立

1、团队内技术共享至少一次。
2、推动基础技术部的技术分享机制和运行

员工评论

推动基础技术部的技术分享机制和运行，已分享了两期。

![image-20221221101703308](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20221221101703308.png)

### 价值观

- 主动服务
  - 下半年团队维护答疑的工作比较多，当团队内Hermes有用户反馈问题时，第一时间积极主动地帮助团队成员一起排查问题，尽早定位到问题原因，减少线上故障时间。
  - 用户反馈分布式锁偶发性超时问题，没有逃避问题，前期在SDK增加本地监控，定位到瓶颈在于创建zk节点，后期持续对zk服务集群进行磁盘IO和GC优化，最终zk集群响应非常稳定，得到了业务方的肯定。
- 防患于未然： 1、对业务在使用hermes中的配置做检测监控，同时调整lag告警阈值提早发现堆积问题；2、西湖云贷后业务调整字段类型后，在写es时出现数据异常导致丢失，legion监控先于业务发现问题，并提醒业务修复

##  2021年上半年绩效考核

### 存储优化

- redis客户端及sailfish 支持了加密验证要求，用户升级无感，授权后自动完成密码验证，同时redis客户端增加了对sentinel集群的支持，并且在集群模式下支持了pipeline功能
-  hermes新客户端细节方案：包括原有hermes-proxy的监控能力移植，业务方的平滑升级，简化推拉数据的执行流程，以及新的鉴权方案
- wacai-boot-starter 升级到2.0.0， 其中spring-boot升级到2.1.18，以及同仓库下所有相关模块的版本升级，并且已经在hermes项目中应用新版本
- 实时ETL已经完成所有原logbus的需求，同时接入挖财环境下所有日志的解析，并下线logbus
- 调研istio监控组件并部署

### 现有项目维护和稳定性保障

1、jasmine，harmony，dubbo 无线上故障。

2、网关，wsso，wups，数据字典等项目的维护工作，无线上故障。

3、zookeeper 发生了一次线上故障，事后做了预案和梳理，排查出太平洋业务使用分布式锁存在问题，把不稳定隐患扼杀在摇篮中。

### 服务网格挖财云融合及推广

1、 推动服务网格和挖财云两个独立产品融合。
2、新版服务网格在测试环境中推广

员工评论

1、完成wse从k8s无缝切换到k2，且做到了两个平台之间可以相互切换，完成公司底层资源的大统一。

2、中间件核心应用已切换到wse。

### 服务网格治理能力增强

1、增加服务限流能力。
2、增加服务熔断能力。

员工评论

对 istio 熔断，限流技术方案进行了评估调研，istio现阶段的限流和熔断还不是成熟，限流和熔断都是委托给envoy完成，由于我们还没有推广到生产环境，在日常开放环境限流和熔断的价值不是很大，所以这一块我们一直在等社区出现一款比较完善解决方案而没有急着去落地。

主管评论

完成了方案调研，目前进度不够理想，待后续加强投入

### 团队分享

团队内技术共享至少一次。

员工评论

1、完成服务网格分享；

2、完成jasmine实现原理分享；

主管评论

\- 上半年团队成员变化、资源一直比较紧张的情况下，完成了服务化相关组件的维护和稳定性保障

\- 技术基本功较好，有一定的团队影响力，也进行了多次分享和帮助团队进行招聘工作

\- wse 上半年完成了线下环境的融合和稳定性提升，服务治理和线上环境进度不理想，有待后续加强投入

### 价值观

- 顾全大局：当服务网格和云平台团队存在冲突时，能以公司整体利益为重，主动求同存异、寻找两个团队都能接受的融合方案，利用团队的力量来解决技术问题，最终达到双赢的结果。
- 防患未然：维护存储平台较多的情况下及时梳理监控项，上半年零故障

## 2021下半年绩效考核表

### 中间件团队管理

1、中间件产品整体规划和把控，推动轻量化网关、Hermes升级、研发效能私有化环境支持、小文件存储架构等几个重点项目的研发推进和推广工作。
2、做好中间件已有产品的维护工作，合理安排团队人员分工，确保不发生重大故障。

3.5
1、中间件不产生P4以上故障。
2、至少2条以上业务线的接入新版hermes客户端，至少2条业务线接入轻量化网关，完成七牛云cdn迁移回domino。

3.75
1、完善中间件所有产品的SLA机制，从制度上保证中间件的产品稳定性。
2、至少5条以上业务线的接入新版hermes客户端，至少5条业务线接入轻量化网关，完成ceph集群改造。

员工评论

1、中间件未产生P4以上故障。

2、至少2条以上业务线的接入新版hermes客户端，5条业务线接入轻量化网关。

3、完成七牛云cdn迁移回domino。

### 轻量化网关规划

轻量化网关规划

1、主导轻量化网关整体架构设计，解决老版网关配置繁琐运维部署复杂的难题，快速支撑业务方对API网关的需求。
2、推动轻量化网关的推广。
3.5
1、完成轻量化网关加密、鉴权、限流、流量转发功能研发。
2、至少2条业务线接入轻量化网关。

3.75
1、至少5条业务线接入轻量化网关。
2、有重大技术创新点。

员工评论

1、完成轻量化网关加密、鉴权、限流、流量转发功能研发。

2、5条业务线接入轻量化网关。

3、借鉴服务网格设计思想减少依赖，降低复杂度，提供在线联调功能减少用户使用成本。

### 中间件架构

1、对业务方提出的中间件技术需求能提供合理的技术方案。
2、对组内成员技术方案提供必要的指导和建议。
3、积极跟进业界技术趋势，避免闭门造车。
3.5
1、至少有1次技术方案的案例说明。
2、至少有组内新人指导2次案例说明。

3.75
1、满足以上的同时有重大技术创新。

员工评论
1、完成中间件鉴权方案的设计，提供dubbo、es、hermes、redis、小文件的全产品线鉴权方案。
2、完成hermes稳定性方案的指导。
3、完成对轻量化网关参数映射，dubbo的方案指导。

### 打造专业、高效、主动的技术团队

打造专业、高效、主动的技术团队
1、完善中间件文档。
2、完善新人成长机制。
3、完善review机制，关注代码质量。
3.5
1、完善中间件文档。
2、完善新人成长机制。
3、完善code review机制，关注代码质量。

3.75
1、小组成员到公司大团队进行技术分享。

员工评论

1、完成中间件文档的完善。

2、组内不定期codeReview和技术分享。

### 价值观

- 超出目标：接手中间件团队之后能快速适应新的角色，确保团队稳定，产品稳定。在个人保护法项目中积极主动寻找业务接入成本最低方案，提供了dubbo、mq、redis、es、小文件全中间件产品的鉴权方案，为业务快速实现个人安全法落地提供了保障。在漫财，AMC、望润、海川慧富多个私有化项目中通过落地轻量化网关解决了各个主体之间安全通信的问题，为业务快速交付提供了保障。
- 主动服务：记账业务方因其他原因在出现 数据库同步失败，缺失数据的情况以下，主动提供补偿数据方案并针对性改造以满足业务需求

## 2022年上半年绩效考核

### 中间件团队管理

1、中间件产品整体规划和把控，推动中间件服务链路治理、统一存储平台等几个重点项目的研发推进和推广工作。
2、做好中间件已有产品的维护工作，合理安排团队人员分工，确保不发生重大故障。
3.5
1、中间件不产生P4以上故障。
2、中间件服务治理平台和通用存储平台顺利上线。

3.75
1、完善中间件所有产品的SLA机制，从制度上保证中间件的产品稳定性。
2、中间件服务治理平台覆盖 50%以上的业务场景。

员工评论

1、利用OKR管理工具聚焦团队目标，规划开发了几个重大项目进展顺利；

2、完成OB3下线，发布平台多场景支持，中间件客户端多云支持改造，通用存储平台顺利上线，quantum服务治理平台发布到测试环境；

3、优化人员分工，在人员出现调整的情况下继续保证中间件的产品稳定性；

### 服务治理平台

1、主导服务治理平台架构设计，解决之前北斗运维部署复杂，数据不准确，服务治理缺失的难题，快速支撑业务方对服务治理、服务度量的需求。 

2、合理安排人员分工，确保服务治理平台一期上半年按时上线。

3.5
1、完成服务治理平台项目一期的开发工作。
2、确保不产生故障。

3.75
1、有重大技术创新点。

员工自评

1、主导服务治理平台quantum产品和技术架构设计；

2、创新性把业界领先的APM系统skywaling整合进挖财中间件体系，完成了应用大盘分析，应用上下游依赖分析，调用链路跟踪，应用0成本接入;

员工自评

3.75

员工评论

- 主导服务治理平台quantum产品和技术架构设计；
- 创新性把业界领先的APM系统skywaling整合进挖财中间件体系，完成了应用大盘分析，应用上下游依赖分析，调用链路跟踪，应用0成本接入;

主管评论

服务治理平台从技术选型、平台产品设计到版本迭代上线，在资源紧张的前提下，依然能够按质按量、如期上线。

### 专业能力

专业能力

1、在中间件产品规划中积极服务治理平台中有创新性架构。
2、对组内成员技术方案提供必要的指导和建议。
3.5
1、至少有1次技术方案的案例说明。
2、至少有组内新人指导2次案例说明。

3.75
1、满足以上的同时有重大技术创新。

员工评论
1、创新性提出中间件多云支持，解决业务一套代码运行在挖财、阿里云、私有云环境，已经发布；
2、合理安排存储平台项目推进，利用墨子平台高效快速完成系统建设，涉及新开发页面40+张，不占用部门宝贵的前端资源，已部分发布上线；
3、规划基于agent的服务治理框架，对服务治理、服务度量统一规划设计，已发布到测试环境；

### 团队贡献

打造专业、高效、积极主动的技术团队
1、在团队内建立学习成长机制。
2、增加团队内成员的技术影响力。

打分标准

- 3.5：在团队内建立学习成长机制。
- 3.75：小组成员到公司大团队进行技术分享。

员工评论

小组成员洛东进行大团队技术分享

### 价值观

- 顾全大局：在通用存储平台项目中，合理安排团队分工，利用前端团队提供的墨子系统快速搭建通用存储平台，不占用部门宝贵的前端资源，利用团队的力量解决问题；在服务治理项目中，积极补位，以整体利益为重，充当项目测试，把控项目质量。

## 2022年下半年绩效考核

### 中间件团队管理

1、中间件产品整体规划和把控，做好服务治理平台项目的规划和推进。
2、清理和下线一系列老系统，减少维护成本，去肥增瘦。
3、做好中间件已有产品的维护工作，合理安排团队人员分工，确保不发生重大故障。

### 服务治理平台

服务治理平台项目研发推进
1、主导服务治理平台架构设计，解决之前北斗运维部署复杂，数据不准确，服务治理缺失的难题，快速支撑业务方对服务治理、服务度量的需求。

2、合理安排人员分工，确保服务治理平台应用度量功能在生产环境、私有化环境中落地。

3.5
1、如期完成功能开发，生产环境、私有化环境中落地。
2、确保不产生故障。

3.75
1、完全替换北斗，北斗下线。

### 团队贡献

稳定团队成员，提升大家工作积极性和主动性
 1、根据团队成员的特点合理安排分工，做到事事有人跟进，件件有人着落。

自评

> 合理利用 OKR 工具，定期Review团队成员工作进度

## 2023年上半年绩效考核

### 业绩

中间件团队管理 

1、中间件产品整体规划和把控，做好服务治理平台项目的规划和推进。

2、做好中间件已有产品的维护工作，合理安排团队人员分工，确保不发生重大故障

3.5
1、中间件不产生P4以上故障。
2、中间件服务治理平台顺利上线。

3.75
1、 中间件服务治理平台并替换北斗，北斗下线。

自评

> 1、北斗下线，quantum度量平台顺利平替了北斗所有功能
>
> 2、发布系统增加发布流能力，支撑多环境、多分支、多需求部署方式
>
> 3、通过合理安排分工，在人员减少的情况下，规划了几个重大改造项目，均未产生严重故障

### 项目

应用度量项目迭代和产品优化

1、持续迭代和优化应用度量项目，覆盖之前北斗100%功能。
2、确保不产生故障。

hermes4.0 架构升级项目

解决hermes-proxy性能低、成本高、可维护性差等一系列问题

自评

> 1、hermes proxy 架构升级发布上线，大幅提升应用并发性能，机器成本减少60%，消费者端到端延迟减少50%
>
> 2、增加告警监控，减少问题排查时间，提升系统的可维护性
>
> 3、在没有测试资源、完全自测的情况下，发布之后0故障

### 专业能力

中间件架构落地调研

1. 积极跟踪调研业界新技术发展趋势，对中间件现有架构梳理和review、对架构进行升级更好的支撑挖财财商和AMX双线业务 ；
2. 对组内成员技术方案提供必要的指导和建议

3.5
1、至少有1次技术方案的案例说明。
2、至少有组内新人指导2次案例说明。

3.75
1、满足以上的同时有重大技术创新。

自评：

> 1、在hermes4.0项目中创新性提出通过agent增强 + http long pooling 方式实现 hermes proxy 架构升级
>
> 2、升级过程无需推动业务方改造本地客户端，对业务0干扰

### 团队贡献

没写

## 2023年下半年绩效考核

### 业绩

1、中间件产品整体规划和把控，推动在诊断平台，hermes事件中心，发布系统质量平台等几个重点项目的研发推进和推广工作。
2、做好中间件已有产品的维护工作，合理安排团队人员分工，确保不发生重大故障，不出现人员变动情况。

3.5
1、中间件不产生P4以上故障。

3.75
1、中间件不产生任何故障，人员稳定，不出现人员变动情况。

自评3.5

> 1、在线诊断平台、sailfish支持kvrocks、发布系统代码质量几个重点项目均稳步推进，完成开发工作
>
> 2、通过合理安排分工，在人员不足情况下，规划了几个重大项目，均未产生严重故障

### 项目

1、服务治理在线诊断平台一期上线，支持在线DEBUG、性能剖析，cpu线程监控等杀手级功能。

2、完成hermes架构升级二期项目开发落地，打造事件中心平台，简化用户使用体验。

3、发布系统通过 sonar+AI等方式全方位提升代码质量。

3.5
以上项目完成 80%

3.75

以上项目全部上线

自评3.5：

> 1、服务治理在线诊断平台完成主机信息、在线debug、性能分析三大功能开发、目前在测试环境中测试。
>
> 2、hermes架构升级调整为sailfish支持kvrocks，核心部署功能开发完成，目前在测试环境中测试。
>
> 3、发布系统在线code review和sonar+AI开发完成，code review已发布上线，sonar+AI目前在测试环境中测试。

### 专业能力

1、在中间件产品规划中积极服务治理平台中有创新性架构。
2、对组内成员技术方案提供必要的指导和建议。
3.5
1、至少有1次技术方案的案例说明。
2、至少有组内新人指导2次案例说明。

3.75
1、满足以上的同时有重大技术创新。

自评3.75：

> 1、多次指导团队成员解决堆外内存泄露、类加载问题、ES性能问题。
>
> 2、在线诊断平台项目中创新性基于反编译工具实现无性能损耗的在线debug。



### 团队贡献

打造专业、高效、积极主动的技术团队
1、在团队内建立学习成长机制。
2、增加团队内成员的技术影响力。

3.5
1、完善中间件文档。
2、完善新人成长机制。
3、完善code review机制，关注代码质量。

3.75
1、小组成员到公司大团队进行技术分享。

自评3.5：

> 完善中间件文档，推动团队成员对外分享，年度完成两次分享

## 2024年上半年绩效考核

### 业绩

1、中间件产品整体规划和把控，推动在线诊断二期、ES版本升级和治理、JDK版本升级等几个重点项目的研发推进和推广工作。
2、做好中间件已有产品的维护工作，合理安排团队人员分工，确保不发生重大故障。

3.5
1、规划产品如期上线，上线过程中不产生P4以上故障

3.75
1、中间件不产生任何故障，人员稳定，不出现人员变动情况。



### 项目

1、在线诊断平台二期上线，支持cpu线程监控、代码热更新、trace监控等功能。

2、规划和完成ES版本升级和治理，降低ES维护成本，提升ES 整体稳定性。

3、持续提升发布流用户体验，推广在线CodeReview和snoar AI代码提示。

3.5
以上项目完成 80%

3.75

以上项目全部上线

### 专业能力

1、主动跟踪业务技术趋势，在团队内产品规划中有创新性架构。
2、对组内成员技术方案提供必要的指导和建议。
3.5
1、至少有1次技术方案的案例说明。
2、至少有组内新人指导2次案例说明。

3.75
1、满足以上的同时有重大技术创新。

### 团队贡献

打造专业、高效、积极主动的技术团队
1、在团队内建立学习成长机制。
2、增加团队内成员的技术影响力。

3.5
1、完善中间件文档。
2、完善新人成长机制。
3、完善code review机制，关注代码质量。

3.75
1、小组成员到公司大团队进行技术分享。



## 2024年下半年绩效考核

### 业绩

1、中间件产品整体规划和把控，推动代码分析平台、Hermes产品改进、研发效能AI机器人等几个重点项目的研发推进和推广工作。
2、做好中间件已有产品的维护工作，合理安排团队人员分工，确保不发生重大故障。

3.5
1、规划产品如期上线，上线过程中不产生P4以上故障

3.75
1、中间件不产生任何故障，人员稳定，不出现人员变动情况。

自评3.5

> 1. 推动中间件统一监控大盘、代码分析平台、研发效能AI机器人和首页改版几个重大项目推进，均发布上线
> 2. 通过合理安排分工，在人员不足情况下，规划了几个重大项目，均未产生严重故障

### 项目

1、服务治理：代码分析平台上线，支持实时代码级别的覆盖率统计，并在业务中落地推广

2、存储平台：包括Hermes产品改进支持消息广播模式、Es集群治理，提升可用性

3、研发效能：对接AI机器人，支持内部知识库、智能工作协作，辅助编程，应用构建时长优化

3.5
以上项目完成 80%

3.75

以上项目全部上线

自评3.75

> 1. 服务治理完成代码分析平台的的开发和上线，支持代码实时运行覆盖率统计；
> 2. 存储平台完成Hermes消息广播的支持，补齐kafka在这方面的缺陷，ES版本升级到7.x，日志集群不可用性时间减少80%，并且下线2台机器；
> 3. 研发效能方面：基于dify搭建AI Agent，支持AI机器人和内部支持库，团队成员对AI工具链有一定的积累，辅助编程工具IDEA插件WAcopliot开发中，预计在下一年会有更多的发力点 ；

### 专业能力

1、主动跟踪业务技术趋势，在团队内产品规划中有创新性架构。

2、对组内成员技术方案提供必要的指导和建议。

3.5
1、至少有1次技术方案的案例说明。
2、至少有组内新人指导2次案例说明。

3.75
1、满足以上的同时有重大技术创新。

自评3.75

> 1. 在服务治理方面创新性提出将单元测试框架jcoco运用于线上环境，实现代码的运行时覆盖统计；
> 2. 在存储方面完成日志集群的ES版本升级，不可用性时间减少80%，性能提升20%，下线2台物理机；
> 3.  在AI实践方面，积极研究业界先进AI工具，主动提出dify Agent编排工具，并在内部AI机器人中落地；

### 团队贡献

1、保持团队成员稳定，关注团队成员的成长，帮忙团队成员制定有挑战性的目标和事情，提升个人能力。
2、通过技术分享、技术方案讨论，提升团队内的技术分享氛围。

3.5
1、团队内有技术分享案例

3.75
1、团队成员绩效review反馈个人技术能力得到提升

自评3.5

> 团队成员完成 jcocoa 字节码增强运用于代码分析平台的技术分享



## 2025年上半年绩效考核

### 业绩

1、中间件产品整体规划和把控，推动AI辅助编程工具，链路跟踪sdk，研发效能AI能力升级等几个重点项目的研发推进和推广工作。
2、做好中间件已有产品的维护工作，配合云平台升级、HR系统升级等事项， 合理安排团队人员分工，确保不发生重大故障。

3.5
1、规划产品如期上线，上线过程中不产生P4以上故障

3.75
1、中间件不产生任何故障，人员稳定，不出现人员变动情况。

> 1. 上半年配合云平台、HR系统升级改造，涉及到发布系统、ldap，均未发生重大故障
> 2. 中间件规划的AI编程辅助工具wapilot、链路跟踪sdk，ai-test平台如期发布上线
>
> 

### 项目

1、AI辅助编程工具Wapilot一期上线，支持智能问答，单元测试生成，API测试生成，代码解释，context关联等功能
2、规划和完成链路跟踪能力开放项目，提供sdk给业务方使用
3、持续提升研发效能AI能力和用户体验，完成云平台首页改版、发布流在线日志支持、AI测试用例生成


3.5
以上项目完成 80%

3.75

以上项目全部上线



> 1.  wapilot 如期上线，除了支持智能问答，单元测试生成，API测试生成，代码解释，context关联，还具备一定的agent 推理能力
> 2. 链路跟踪sdk发布，支持异步链路和自定义链路，并且在ladp-manager，api网关中落地
> 3. 发布上线云平台首页改版、发布流在线日志支持和AI测试用例自动生成，AI测试用例自动生成已在记账团队中使用

### 专业能力

1、主动跟踪业务技术趋势，在团队内产品规划中有创新性架构。
2、对组内成员技术方案提供必要的指导和建议。
3.5
1、至少有1次技术方案的案例说明。
2、至少有组内新人指导2次案例说明。

3.75
1、满足以上的同时有重大技术创新。



> 

### 团队贡献

打造专业、高效、积极主动的技术团队
1、在团队内建立学习成长机制。
2、增加团队内成员的技术影响力。

3.5
1、完善中间件文档。
2、完善新人成长机制。

3.75
1、小组成员到公司大团队进行技术分享。

> 1. 上半年在公司范围内进行了一次AI专题分享，整体满意度4.8分(满分5分)