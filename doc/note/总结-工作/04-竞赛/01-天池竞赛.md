## 总结

这里记录了kirito参加天池中间件竞赛的一些文章，非常有参考价值

- [文件 IO 操作的一些最佳实践](https://www.cnkirito.moe/file-io-best-practise/)
- [重新认识 Java 中的内存映射](https://www.cnkirito.moe/learn-mmap/)
- [文件 IO 中如何保证掉电不丢失数据](https://www.cnkirito.moe/filechannel_force/)
- [linux如何写满io](https://www.cnkirito.moe/linux-io-benchmark/)
- [如何更快地将string转换成int/long](https://www.cnkirito.moe/integer-parse/)
- [第三届数据库大赛 ADB 性能挑战赛赛题总结](https://www.cnkirito.moe/race-adb-md)
- [天池中间件大赛百万队列存储设计总结](https://www.cnkirito.moe/mq-million-queue/)
- [华为云 TaurusDB 性能挑战赛赛题总结](https://www.cnkirito.moe/taurusdb-race)
- [第一届PolarDB数据库性能大赛](https://www.cnkirito.moe/polardb-race/)
- [第二届云原生编程挑战赛-冷热读写场景的RocketMQ存储系统设计](https://www.cnkirito.moe/cloudnative-race-2021-rmq/)
- [第四届PolarDB数据库性能大赛](https://tianchi.aliyun.com/competition/entrance/531980)
- [深入剖析分布式监控 CAT —— 消息文件存储](http://www.wangyapu.com/2019/04/27/cat_logview_store/)



LSM知识：

- [DDIA 读书笔记（三）：B-Tree 和 LSM-Tree](https://www.qtmuniao.com/2022/04/16/ddia-reading-chapter3-part1/)
- [LSM 派系（不仅 LSM TREE）存储模型概述（上篇）](https://mp.weixin.qq.com/s/0V9O7urr5Zmgps-U24eknQ)
- [LSM 派系（不仅 LSM TREE）存储模型概述（下篇）](https://mp.weixin.qq.com/s/Iegl1i8-cNCGrtwQTqoEPA)



一些总结

Bitcast 模型也算是LSM的一种，只是它的索引全部放在内存中，基于hash，它的缺点如下：

- 索引全部放在内存，存在容量上限问题；不过好像也不是太严重的问题，4千万个key，100 个字节，消耗 3G 数据；
- **不支持范围查询**。由于 key 是无序的，要进行范围查询必须全表扫描；

LSM-Trees 的特点：

- 内存中不再维护全量索引，数据保存在SSTable(Sorted String Table)中；
- 内存中维护了一个有序结构MemTable， 定期flush到文件中，查询时先查找MemTable，再去 SSTable 按时间顺序由新到旧逐一查找；