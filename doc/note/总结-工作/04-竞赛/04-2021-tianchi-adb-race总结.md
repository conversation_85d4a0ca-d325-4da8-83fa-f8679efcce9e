## 赛题介绍

选手需要设计实现 quantile 分析函数，导入指定的数据，并回答若干次 quantile 查询。

> quantile(column, p) 函数定义
>
> *column:* 查询列。
>
> *p:* 百分比，范围 [0, 1]。
>
> *函数返回*：将列的所有值排序后，返回第 N * p 个值，评测保证 N * p 是整数。

样例：

> column = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10] # column 为整型，有 10个 元素
>
> quantile(column, 0.5) = 5
> quantile(column, 0.8) = 8
> quantile(column, 0.25) = 3
> quantile(column, 0) = 0
> quantile(column, 1) = 10

其实就是实现一个查询第 N 大的数函数。它的输入数据一共有两列：第一行是列名，第二行开始是列的数据。



初赛测试数据：只有一张表 lineitem，只有两列 L_ORDERKEY (bigint), L_PARTKEY (bigint)，数据量3亿行，单线程查询 10 次。

## 赛题剖析

此次的数据输入方式和之前的比赛有很大的不同，选手们需要自行去解析文件，获得输入数据，同时进行处理，如何高效的处理文件是很大的一块优化点。

初赛一共 3 亿行数据，一共 2 列，内存一共 4 G，稍微计算下就会发现，全部存储在内存中是存不下的（不考虑压缩），所以需要用到 PMem 充当存储引擎。

查询的需求是查找到第 N 大的数，所以我们的架构一定是需要做到整体有序，允许局部无序。

赛题数据的说明尤为重要：**测试数据随机，均匀分布**。看过我之前文章的读者，应当敏锐地注意到了均匀分布这个关键词，这意味着我们又可以使用数据的头 n 位来分区了。

这里先给出初赛的最终架构，明确下如何串联各个流程。

![image-20231010113329052](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20231010113329052.png)

第一步：将输入文件从逻辑上分成 12 等分，这样 12 个线程可以并发读取输入文件。可以借助预处理程序，找到等分的边界。

第二步：每个线程都需要读取各自的文件分片，将读取到的文件流在内存中解析成 long，并且需要根据`逗号`、`换行符`来区分出两列数据。

第三步：读取到 long 之后，需要根据头 n 位进行分区，例如选择头 8 位，可以获得 2^(8-1) 即 128 个分区，因为比赛中的数据都是正数，所以减了一个符号位。这样分区之后，可以保证分区之间有序，分区内部无序。

第四步：获取第 N 大的数字时，可以直接根据分区内的数据量，直接定位到最终在哪个分区，这样就可以确保只加载一部分数据到内存中。t1_pn ~ t12_pn 在逻辑上组成了 partitionN，将 partitionN 的数据加载进内存之后，这道题就变成：查询无序数组中第 N 大数的问题了。

## 实现细节

### 多线程按分区读文件

输入文件按行来分隔数据，第一时间联想到的就是 JDK 提供的 java.io.BufferedReader#readLine() 方法，但稍微懂点文件 IO 基础的读者都应该意识到，文件 IO 要想快，一定得顺序按块读，所以 readLine 这种方法，想都不用想，必定是低效的。那有人问了，博主，你给解释解释，什么叫按块读？最简单的做法是按照固定 4kb 的 buffer 来读取文件，在内存中，自行判断`逗号`、`换行符`来区分两列数据，不断移动这个 4kb 的块，这就是按块读了。

光是按块读还不够，为了充分发挥 IO 特性，还可以用上多线程，按照上图中的第一步，我的方案将文件分成了 12 份，这样 12 个线程可以齐头并进地进行读取和解析。

> 经过测试，多线程比单线程要快 70 多秒，所以没有使用多线程的选手，名次肯定不会高，这是一个通用优化点。

### Long 转换

将文件中的字节读取到内存中，一定会经过 byte[] 到 long 的转换过程，千万不要小看这个环节，这可是众多选手分数的分水岭。先来看下 demo 中给出的方案

```
String[] columns = reader.readLine().split(",");
Long.parseLong(row[i]);
```

这里面存在两个问题

1. 先转 String，再转成 Long，多了一次无效转换
2. Long.parseLong 方法比较低效，有很多无用判断

我贴一下我方案的伪代码：

```
long val = 0;
for (int i = 0; i < size; i++) {
    val = val * 10 + (readBuffer[i] - '0');
}
```

直接从字节数组中解析出 Long，解析出 Long 主要还是为了落盘的时候进行数据对齐，主流方案应该都会解析。

### 按头 n 位分桶落盘

在读取到一个 Long 之后，我们可以按照数据的头 n 位，将其写入对应的分区文件中。这其实也是一个通用的优化点，我在《华为云 TaurusDB 性能挑战赛赛题总结》也介绍过，分区之后，可以保证分区之间有序，即 partition1 中的任意数据一定小于 partition2 中的任意数据。分区之间无序，主要是为了可以实现分区文件的顺序写。至于 n 具体是多少，取决于我们想分多个区。分区太多，会导致整体写入速度下降；分区太少，读取阶段加载的数据会过多，甚至可能导致内存放不下。

### 每个写线程维护自己的分区文件

在赛题剖析里面，我给出了我最终方案的流程图，里面有一个细节，每个读取线程从 1/12 个文件分片中读取解析到的 Long 数值，写入了自己线程编号对应的文件中，进行落盘。并没有采用写入同一个分区文件这样的设计。对比下两种做法的利弊：

- 写线程写入同一个分区文件。好处是读取阶段不需要聚合多份分区文件，坏处是多个线程写入同一个分区需要加锁，会导致竞争。
- 写线程写入自己的分区文件。好处是不需要加锁写，坏处是读取阶段需要聚合读取。

也好理解，两个方案的优劣正好相反，稍微分析一下，由于初赛的查询只有 10 次，所以聚合的开销不会太大，再加上，我们本来就希望读取能做到并发，聚合没有那么可怕。反而是写入时加锁导致的冲突，会严重浪费 CPU。

> 该优化点，帮助我的方案从 80s 缩短到 50s。

### 分支预测优化

这次比赛因为有了 PMem，导致瓶颈根本不出在 IO 上，以往比赛中，大家都是想尽一切方法，把 CPU 让给 IO，而这次比赛，PMem 直接起飞了，导致大家需要考虑，怎么优化 CPU。而 JVM 虚拟机的一系列机制中，就有很多注意事项，是跟 CPU 优化相关的。如果你对 CPU 优化一无所知，我强烈建议你先去阅读下我之前的文章《[JAVA 拾遗 — JMH 与 8 个测试陷阱](https://www.cnkirito.moe/java-jmh/)》和《[JAVA 拾遗 — CPU Cache 与缓存行](https://www.cnkirito.moe/cache-line/)》。在解析 Long 时，我们需要从 4kb 的读缓冲区中解析出 Long 数值，由于文件中的数值是以不定长的字节数组形式出现的，我们只能通过判断 `逗号`、`换行符` 来解析出数值，所以难免会写出这样的代码：

```
int blockReadPosition = 0;
for (int i = 0; i < size; i++) {
    if (readBufferArray[i] == '\n') {
        partRaceEngine.add(threadNo, val);
        val = 0;
        blockReadPosition = i + 1;
    } else if(readBufferArray[i] == ',') {
        orderRaceEngine.add(threadNo, val);
        val = 0;
        blockReadPosition = i + 1;
    } else {
        val = val * 10 + (readBufferArray[i] - '0');
    }
}
```

思考下，这段代码会有什么逻辑问题吗？当然没有，相信很多选手也会这么判断。但不妨分析下，输入文件大概有 10G 左右，所有的字节都会经过 if 判断一次，而实际上，大多数的字符并不是 `\n` 和 `,` 。这会导致 CPU 被浪费在分支预测上。

我的优化思路也很简单，直接用循环，干掉 if/else 判断

```
for (int i = 0; i < size; i++) {
    byte temp = readBufferArray[i];
    do {
        val = val * 10 + (temp - '0');
        temp = readBufferArray[++i];
    } while (temp != ',');
    orderRaceEngine.add(threadNo, val);
    val = 0;
    // skip ，
    i++;
    temp = readBufferArray[i];
    do {
        val = val * 10 + (temp - '0');
        temp = readBufferArray[++i];
    } while (temp != '\n');
    partRaceEngine.add(threadNo, val);
    val = 0;
    // skip \n
}
readPosition += size;
```

一般来说，再没有办法去掉 if/else 的前提下，我们可以遵循的一个最佳实践是，将容易命中的条件放到最前面。

> 该优化帮助我从 48s 优化到了 24s。

另外，也可以利用数据特性，因为大多数数据是 19 位的数字，可以直接判断第 20 位是不是 `,` 或者 `\n` 从而减少分支预测的次数。

### quickSelect

在查询阶段，查询一个分区内第 N 大的数，最简单的思路是排序之后直接返回，a[N]，受到评测 demo 的影响，很多选手可能忽略了可以使用 quickSelect 算法。

关于 TopN 问题，我其实已经专门写过一篇文章了，对这个优化点和算法感兴趣的朋友可以阅读我之前的文章《[海量无序数据寻找第 K 大的数](https://www.cnkirito.moe/topk/)》。

> 该优化帮我从 24s 优化到 17s。

### 查询阶段多线程读分区

前文提到了为了避免写入阶段的冲突，每个线程维护了自己的分区文件，在查询时，则需要聚合多个线程的数据。这个时候不要忘记也可以多线程读取，因为初赛的评测程序是单线程测评的，IO 无法打满，需要我们控制多线程，充分利用 IO。

> 该优化帮我从 17s 优化到了 15s。

## 总结

还有很多之前我提到过的一些通用优化技巧，例如顺序读写、读写缓冲、对象复用等等，就不在这里继续赘述了，尽管 PMem 和固态硬盘这两种介质有一定的差异，但这些优化技巧依旧是通用的。

因为这次比赛，IO 的速度实在是太快了，导致优化的方向变成如何优化 CPU，合理分配 CPU，让 CPU 更多地分配给瓶颈操作，这是其他比赛中没有过的经历。

还有一些点是通过调参来实现的，例如文件分片数，读写缓冲区的大小，读写的线程数等等，也会导致成绩相差非常大，这就需要不断地肝，不断地 benchmark 了。

不光是成功的优化点值得分享，也拿一个失败的优化分享一下，例如，将一半的数据存储在内存中，最终发现，申请内存的时间，倒不如拿去进行文件 IO，最终放弃了，可以见得在合理的架构设计下，PMem 的表现的确彪悍，不属于内存存取。

这次 ADB 的性能挑战赛，虽然只参加了初赛，但收获的技能点还是不少的。印象最深的便是 PMem 这块盘的表现和我理解中的 SSD 还是有一定差距的，导致之前的一些经验不能直接在这场比赛中运用。我也大概了解了很多复赛前排选手使用到了很多的奇技淫巧，每一个看似奇葩的优化点背后，可能都蕴含着该选手对操作系统、文件系统、编程语言等方面超出常人的认知，值得喝彩。

感到遗憾的地方还是有的，这次比赛只能让 PMem 工作在 APP Direct 模式下，没有能够真正做到颠覆性。如果有一场比赛，能够支持 Memory Mode，那我应该能收获到对持久内存更加深刻的认知。

我一直反复安利我的读者尽可能地参加各类性能挑战赛，特别是在校生、实习生或者刚进入职场的新人，这种比赛是实践的最好机会，看书不是。

好了，最后，我将我的代码开源在了 github：https://github.com/lexburner/2021-tianchi-adb-race。如果你对实现细节感兴趣，欢迎与我交流。

## 参考

《[文件 IO 操作的一些最佳实践](https://www.cnkirito.moe/file-io-best-practise/)》

《[华为云 TaurusDB 性能挑战赛赛题总结](https://www.cnkirito.moe/taurusdb-race/)》

《[PolarDB 数据库性能大赛 Java 选手分享](https://www.cnkirito.moe/polardb-race/)》

《[天池中间件大赛 dubboMesh 优化总结（qps 从 1000 到 6850）](https://www.cnkirito.moe/dubboMesh/)》

《[天池中间件大赛百万队列存储设计总结【复赛】](https://www.cnkirito.moe/mq-million-queue/)》