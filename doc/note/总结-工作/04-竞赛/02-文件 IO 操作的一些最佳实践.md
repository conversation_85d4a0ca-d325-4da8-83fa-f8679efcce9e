## 1. 背景

在所有的分布式架构中都会涉及到了文件操作，合理地设计架构以及正确地压榨机器的读写性能成了比赛中获取较好成绩的关键。

本文主要关注的 Java 相关的文件操作，理解它们需要一些前置条件，比如 PageCache，Mmap(内存映射)，DirectByteBuffer(堆外缓存)，顺序读写，随机读写。

首先，文件 IO 类型的比赛最重要的一点，就是选择好读写文件的方式，那 JAVA 中文件 IO 有多少种呢？原生的读写方式大概可以被分为三种：普通 IO，FileChannel(文件通道)，MMAP(内存映射)。

面向于字节传输的传统 IO 方式遭到了我们的唾弃，我们重点探讨 FileChannel 和 MMAP 这两种读写方式的区别。

使用 FIleChannel 的方式：

```java

FileChannel fileChannel = new RandomAccessFile(new File("db.data"), "rw").getChannel();
```

获取 MMAP 的方式：

```java
MappedByteBuffer mappedByteBuffer = fileChannel.map(FileChannel.MapMode.READ_WRITE, 0, filechannel.size();
```

MappedByteBuffer 便是 JAVA 中 MMAP 的操作类。



## 2. FileChannel 读写

FileChannel 基本用法如下:

```java
// 写
byte[] data = new byte[4096];
long position = 1024L;
// 指定 position 写入 4kb 的数据
fileChannel.write(ByteBuffer.wrap(data), position);
// 从当前文件指针的位置写入 4kb 的数据
fileChannel.write(ByteBuffer.wrap(data));

// 读
ByteBuffer buffer = ByteBuffer.allocate(4096);
long position = 1024L;
// 指定 position 读取 4kb 的数据
fileChannel.read(buffer,position)；
// 从当前文件指针的位置读取 4kb 的数据
fileChannel.read(buffer);

```



FileChannel 大多数时候是和 ByteBuffer 这个类打交道，你可以将它理解为一个 byte[] 的封装类，提供了丰富的 API 去操作字节。值得一提的是 FileChannel 的 write 和 read 方法均是 **线程安全** 的，FileChannel 内部通过一把 `private final Object positionLock = new Object();` 锁来控制并发。

这点可以从FileChannel 的实现类sun.nio.ch.FileChannelImpl看到

```java
public int read(ByteBuffer dst) throws IOException {
        ensureOpen();
        if (!readable)
            throw new NonReadableChannelException();
        synchronized (positionLock) {
            int n = 0;
            int ti = -1;
            try {
                begin();
                ti = threads.add();
                if (!isOpen())
                    return 0;
                do {
                    n = IOUtil.read(fd, dst, -1, nd);
                } while ((n == IOStatus.INTERRUPTED) && isOpen());
                return IOStatus.normalize(n);
            } finally {
                threads.remove(ti);
                end(n > 0);
                assert IOStatus.check(n);
            }
        }
 }
 public int write(ByteBuffer src) throws IOException {
        ensureOpen();
        if (!writable)
            throw new NonWritableChannelException();
        synchronized (positionLock) {
            int n = 0;
            int ti = -1;
            try {
                begin();
                ti = threads.add();
                if (!isOpen())
                    return 0;
                do {
                    n = IOUtil.write(fd, src, -1, nd);
                } while ((n == IOStatus.INTERRUPTED) && isOpen());
                return IOStatus.normalize(n);
            } finally {
                threads.remove(ti);
                end(n > 0);
                assert IOStatus.check(n);
            }
        }
    }
```

## 3. FileChannel 为什么快？

FileChannel 为什么比普通 IO 要快呢？这么说可能不严谨，因为你要用对它，FileChannel 只有在一次写入 4kb 的整数倍时，才能发挥出实际的性能，这得益于 FileChannel 采用了 ByteBuffer 这样的内存缓冲区，让我们可以非常精准的控制写盘的大小，这是普通 IO 无法实现的。4kb 一定快吗？也不严谨，这主要取决你机器的磁盘结构，并且受到操作系统，文件系统，CPU 的影响，例如中间件性能挑战赛时的那块盘，一次至少写入 64kb 才能发挥出最高的 IOPS。

FileChannel 是直接把 ByteBuffer 中的数据写入到磁盘吗？

答案是：NO。ByteBuffer 中的数据和磁盘中的数据还隔了一层，这一层便是 PageCache，是用户内存和磁盘之间的一层缓存。我们都知道磁盘 IO 和内存 IO 的速度可是相差了好几个数量级。我们可以认为 filechannel.write 写入 PageCache 便是完成了落盘操作，但实际上，操作系统最终帮我们完成了 PageCache 到磁盘的最终写入，理解了这个概念，你就应该能够理解 FileChannel 为什么提供了一个 force() 方法，用于通知操作系统进行及时的刷盘。

同理，当我们使用 FileChannel 进行读操作时，同样经历了：磁盘 ->PageCache-> 用户内存这三个阶段，对于日常使用者而言，你可以忽略掉 PageCache，但作为挑战者参赛，PageCache 在调优过程中是万万不能忽视的，关于读操作这里不做过多的介绍，我们再下面的小结中还会再次提及，这里当做是引出 PageCache 的概念。

## 4. MMAP 读写

```java
// 写
byte[] data = new byte[4];
int position = 8;
// 从当前 mmap 指针的位置写入 4b 的数据
mappedByteBuffer.put(data);
// 指定 position 写入 4b 的数据
MappedByteBuffer subBuffer = mappedByteBuffer.slice();
subBuffer.position(position);
subBuffer.put(data);

// 读
byte[] data = new byte[4];
int position = 8;
// 从当前 mmap 指针的位置读取 4b 的数据
mappedByteBuffer.get(data)；
// 指定 position 读取 4b 的数据
MappedByteBuffer subBuffer = mappedByteBuffer.slice();
subBuffer.position(position);
subBuffer.get(data);

```

FileChannel 已经足够强大了，MappedByteBuffer 还能玩出什么花来呢？请容许我卖个关子先，先介绍一下 MappedByteBuffer 的使用注意点。

当我们执行 `fileChannel.map(FileChannel.MapMode.READ_WRITE, 0, 1.5 * 1024 * 1024 * 1024);` 之后，观察一下磁盘上的变化，会立刻获得一个 1.5G 的文件，但此时文件的内容全部是 0（字节 0）。这符合 MMAP 的中文描述：内存映射文件，我们之后对内存中 MappedByteBuffer 做的任何操作，都会被最终映射到文件之中。

mmap 把文件映射到用户空间里的虚拟内存，省去了从内核缓冲区复制到用户空间的过程，文件中的位置在虚拟内存中有了对应的地址，可以像操作内存一样操作这个文件，相当于已经把整个文件放入内存，但在真正使用到这些数据前却不会消耗物理内存，也不会有读写磁盘的操作，只有真正使用这些数据时，也就是图像准备渲染在屏幕上时，虚拟内存管理系统 VMS 才根据缺页加载的机制从磁盘加载对应的数据块到物理内存进行渲染。这样的文件读写文件方式少了数据从内核缓存到用户空间的拷贝，效率很高。

但是mmap 并非是文件 IO 的银弹，它只有在 **一次写入很小量数据的场景** 下才能表现出比 FileChannel 稍微优异的性能。紧接着我还要告诉你一些令你沮丧的事，至少在 JAVA 中使用 MappedByteBuffer 是一件非常麻烦并且痛苦的事，主要表现为三点：

- mmap 使用时必须实现指定好内存映射的大小，并且一次 map 的大小限制在 1.5G 左右，重复 map 又会带来虚拟内存的回收、重新分配的问题，对于文件不确定大小的情形实在是太不友好了。
- mmap 使用的是虚拟内存，和 PageCache 一样是由操作系统来控制刷盘的，虽然可以通过 force() 来手动控制，但这个时间把握不好，在小内存场景下会很令人头疼。
- mmap的回收问题，当 MappedByteBuffer 不再需要时，可以手动释放占用的虚拟内存，但…方式非常的诡异。

优先使用 FileChannel 去完成初始代码的提交，在必须使用小数据量 (例如几个字节) 刷盘的场景下，再换成 MMAP 的实现。

## 5. 顺序读比随机读快，顺序写比随机写快

首先，什么是顺序读，什么是随机读，什么是顺序写，什么是随机写？可能我们刚接触文件 IO 操作时并不会有这样的疑惑，但写着写着，自己都开始怀疑自己的理解了，不知道你有没有经历过这样类似的阶段，反正我有一段时间的确怀疑过。那么，先来看看两段代码：

写入方式一：64 个线程，用户自己使用一个 atomic 变量记录写入指针的位置，并发写入

```java
ExecutorService executor = Executors.newFixedThreadPool(64);
AtomicLong wrotePosition = new AtomicLong(0);
for(int i=0;i<1024;i++){
    final int index = i;
    executor.execute(()->{
        fileChannel.write(ByteBuffer.wrap(new byte[4*1024]),wrote.getAndAdd(4*1024));
    })
}
```

写入方式二：给 write 加了锁，保证了同步。

```java
ExecutorService executor = Executors.newFixedThreadPool(64);
AtomicLong wrotePosition = new AtomicLong(0);
for(int i=0;i<1024;i++){
    final int index = i;
    executor.execute(()->{
        write(new byte[4*1024]);
    })
}

public synchronized void write(byte[] data){
    fileChannel.write(ByteBuffer.wrap(new byte[4*1024]),wrote.getAndAdd(4*1024));
}
```

答案是方式二才算顺序写，顺序读也是同理。对于文件操作，加锁并不是一件非常可怕的事，不敢同步 write/read 才可怕！有人会问：FileChannel 内部不是已经有 positionLock 保证写入的线程安全了吗，为什么还要自己加同步？为什么这样会快？我用大白话来回答的话就是多线程并发 write 不加同步，会导致文件空洞，它的执行次序可能是

时序 1：thread1 write position[0~4096)

时序 2：thread3 write position[8194~12288)

时序 3：thread2 write position[4096~8194)

所以并不是完全的“顺序写”。不过你也别担心加锁会导致性能下降，我们会在下面的小结介绍一个优化：通过文件分片来减少多线程读写时锁的冲突。

顺序读写为什么会比随机读写要快？这两个对比其实都是一个东西在起作用：PageCache，前面我们已经提到了，它是位于 application buffer(用户内存) 和 disk file(磁盘) 之间的一层缓存。

![PageCache](https://kirito.iocoder.cn/1364556742_9652.gif)

以顺序读为例，当用户发起一个 fileChannel.read(4kb) 之后，实际发生了两件事

1. 操作系统从磁盘加载了 16kb 进入 PageCache，这被称为预读
2. 操作通从 PageCache 拷贝 4kb 进入用户内存

最终我们在用户内存访问到了 4kb，为什么顺序读快？很容量想到，当用户继续访问接下来的 [4kb,16kb] 的磁盘内容时，便是直接从 PageCache 去访问了。试想一下，当需要访问 16kb 的磁盘内容时，是发生 4 次磁盘 IO 快，还是发生 1 次磁盘 IO+4 次内存 IO 快呢？答案是显而易见的，这一切都是 PageCache 带来的优化。

一个疑问：当内存吃紧时，PageCache 的分配会受影响吗？PageCache 的大小如何确定，是固定的 16kb 吗？我可以监控 PageCache 的命中情况吗？ PageCache 会在哪些场景失效，如果失效了，我们又要哪些补救方式呢？

我进行简单的自问自答，背后的逻辑还需要读者去推敲：

- 当内存吃紧时，PageCache 的预读会受到影响，实测，并没有搜到到文献支持
- PageCache 是动态调整的，可以通过 linux 的系统参数进行调整，默认是占据总内存的 20%
- https://github.com/brendangregg/perf-tools github 上一款工具可以监控 PageCache
- 这是很有意思的一个优化点，如果用 PageCache 做缓存不可控，不妨自己做预读如何呢？

顺序写的原理和顺序读一致，都是收到了 PageCache 的影响。



## 6. 堆外内存  VS 堆内内存

前面 FileChannel 的示例代码中已经使用到了堆内内存： `ByteBuffer.allocate(4 * 1024)`，ByteBuffer 提供了另外的方式让我们可以分配堆外内存 ： `ByteBuffer.allocateDirect(4 * 1024)`。这就引来的一系列的问题，我什么时候应该使用堆内内存，什么时候应该使用直接内存？

|     |     堆内内存     | 堆外内存 |
| :---- | :----| :---- |
| **底层实现**   | 数组，JVM 内存| unsafe.allocateMemory(size) 返回直接内存|
| **分配大小限制** | -Xms-Xmx 配置的 JVM 内存相关，并且数组的大小有限制，在做测试时发现，当 JVM free memory 大于 1.5G 时，ByteBuffer.allocate(900M) 时会报错 | 可以通过 -XX:MaxDirectMemorySize 参数从 JVM 层面去限制，同时受到机器虚拟内存（说物理内存不太准确）的限制 |
|   **垃圾回收**   | 不必多说| 当 DirectByteBuffer 不再被使用时，会出发内部 cleaner 的钩子，保险起见，可以考虑手动回收：((DirectBuffer) buffer).cleaner().clean(); |
|   **内存复制**   | 堆内内存 -> 堆外内存 -> pageCache| 堆外内存 -> pageCache|

关于堆内内存和堆外内存的一些最佳实践：

1. 当需要申请大块的内存时，堆内内存会受到限制，只能分配堆外内存。
2. 堆外内存适用于生命周期中等或较长的对象。(如果是生命周期较短的对象，在 YGC 的时候就被回收了，就不存在大内存且生命周期较长的对象在 FGC 对应用造成的性能影响)。
3. 堆内内存刷盘的过程中，还需要复制一份到堆外内存，这部分内容可以在 FileChannel 的实现源码中看到细节，至于 Jdk 为什么需要这么做，可以参考我的另外一篇文章：[《一文探讨堆外内存的监控与回收》](https://www.cnkirito.moe/nio-buffer-recycle/)
4. 同时，还可以使用池 + 堆外内存 的组合方式，来对生命周期较短，但涉及到 I/O 操作的对象进行堆外内存的再使用 (Netty 中就使用了该方式)。在比赛中，尽量不要出现在频繁 `new byte[]` ，创建内存区域再回收也是一笔不小的开销，使用 `ThreadLocal` 和 `ThreadLocal` 往往会给你带来意外的惊喜 
5. 创建堆外内存的消耗要大于创建堆内内存的消耗，所以当分配了堆外内存之后，尽可能复用它。

## 7. 文件分区

前面已经提到了顺序读写时我们需要对 write，read 加锁，并且我一再强调的一点是：加锁并不可怕，文件 IO 操作并没有那么依赖多线程。**但是加锁之后的顺序读写必然无法打满磁盘 IO，如今系统强劲的 CPU 总不能不压榨吧？我们可以采用文件分区的方式来达到一举两得的效果：既满足了顺序读写，又减少了锁的冲突。**

那么问题又来了，分多少合适呢？文件多了，锁冲突变降低了；文件太多了，碎片化太过严重，单个文件的值太少，缓存也就不容易命中，这样的 trade off 如何平衡？没有理论答案，benchmark everything~

## 参考

kirito 性能挑战赛系列文章：

- [文件 IO 操作的一些最佳实践](https://www.cnkirito.moe/file-io-best-practise/)
- [Linux 环境写文件如何稳定跑满磁盘 I/O 带宽?](https://www.cnkirito.moe/linux-io-benchmark/)
- [重新认识 Java 中的内存映射](https://www.cnkirito.moe/learn-mmap/)
- [文件 IO 中如何保证掉电不丢失数据](https://www.cnkirito.moe/filechannel_force/)
- [第三届数据库大赛 ADB 性能挑战赛赛题总结](https://www.cnkirito.moe/race-adb-md)
- [天池中间件大赛百万队列存储设计总结](https://www.cnkirito.moe/mq-million-queue/)
- [PolarDB数据库性能大赛Java选手分享](https://github.com/lexburner/kiritoDB)
- [华为云 TaurusDB 性能挑战赛赛题总结](https://www.cnkirito.moe/taurusdb-race)
- [深入剖析分布式监控 CAT —— 消息文件存储](http://www.wangyapu.com/2019/04/27/cat_logview_store/)
- [第一届PolarDB数据库性能大赛](https://github.com/AlexZFX/engine)
- [第二届云原生编程挑战赛-冷热读写场景的RocketMQ存储系统设计](https://www.cnkirito.moe/cloudnative-race-2021-rmq/)
- [如何更快地将string转换成int/long](https://www.cnkirito.moe/integer-parse/)

