## 					概述

目前挖财中间件使用kafka作为底层消息存储，并研发kafka-proxy应用作为消息发送代理服务，以及kafka-bridge应用作为消息消费代理服务，形成具有挖财特色的消息中间件解决方案。  
随着这一年多来业务的发展，应用接入、消息数据量、集群规模呈指数上升，当前bridge在功能和性能方面都已存在明显瓶颈，我们着手今年重构一整套消息中间件解决方案HERMES。

特点：

- 支持配置动态下发。
- 基于RocksDB实现的优先提交模式。优先提交模式不保证消费者的顺序，但能提高消息的并发处理能力。消费者接收到消息后，先把消息存储到本地rocksdb，随后再处理该批次消息。
- 基于HTTP协议发送和消费消息。
- 支持offset回放。

proxy实现的优势：

- 简单的http协议实现，多语言支持变得容易，便于客户端升级
- 通过分布式锁实现消息轮训，consumer不存在热点问题
- consuemr重启过程不会出现kafka rebalance storms
- 查看topic和group关联关系变得容易
- 更加容易对toipc 进行治理，如限流、鉴权等
- 更加容易对kafka集群进行运维，根据业务线拆分不同集群
- 屏蔽kafka producer、consumer 容易配置错误的参数

挑战

- 如何保证http的性能
- 如何管理点位实现at-least-once

## 整体架构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/工作总结/24.png" alt="24" style="zoom:50%;" />



## 核心原理

hermes通过redis自己维护offset，这里记录一些机制：

1. 消费一批消息不能超过30秒，30秒之后锁会被释放，导致重复消费，解决方法可以调小批大小。
2. 拉取到一批消息会暂存在本地map，然后再调用用户processMessage()回调函数，如果抛出异常有三种策略：1. 直接跳过。2.无限等待。3.重试10次再写入死信队列，默认是第三个策略。
3. processMessage()执行完成之后才会将offset写入本地map，确保不会丢消息。应用停机时会从本地map中执行ack。
4. **消费点位重置**只支持proxy模式，direct不支持，如果消费重置不生效要注意应用的appName和控制台是否一致。



### commit策略

| 模式   | 消费方式 | 提交策略                                                     | 相关代码                               |
| ------ | -------- | ------------------------------------------------------------ | -------------------------------------- |
| Proxy  | 单条     | 每成功消费一条即写入到map，最后再提交map中的offset，如果失败只会提交最后一条数据 | FailFastConsumerProcessor.process()    |
| Proxy  | 批量     | 每成功消费一批数据ack最后一条，如果处理失败这一批都不会提交  | FailFastConsumerProcessor.process()    |
| Direct | 单条     | 每成功消费一条都会触发一次ack                                | DirectOrderConsumerProcessor.process() |
| Direct | 批量     | 每成功消费一批数据ack最后一条，如果处理失败这一批都不会提交  | DirectOrderConsumerProcessor.process() |

对于 Proxy 模式来说，如果是单条消费，那么每次成功消费一条就ack一条，那么效率是非常低的，**hermes采用批量提交策略**，也就是一批数据处理完成之后再提交，代码在 FailFastConsumerProcessor:

```java
class FailFastConsumerProcessor {
    
    public void process() {
        try {
            processBatchMessage(messages, timeout);
        } finally {
            new RetryTemplate<Void>().doWithRetry(3, 2000, () -> {
                //无论是否抛出异常，都会尽可能提交成功
                commit(topic, partition);
                return null;
            }, CommunicationException.class);
        }
    }
}
```

processBatchMessage()方法定义在AbstractConsumerProcessor类：

```java
abstract public class AbstractConsumerProcessor implements ConsumerProcessor {

    protected void processBatchMessage(BatchMessage batchMessage, long timeout) {
        try {
            //① 调用回调
            HermesUtil.onBatchMessageReceivedWrapper(consumer, config, batchMessage);       
            //② Map中记录offset
            consumer.getProcessedOffsetsMap().put(batchMessage.getTopic() + ":" + batchMessage.getPartition(), batchMessage.getEndOffset());
          	 //proxy是空实现
            processMessageSuccess(batchMessage);

        } catch (Throwable e) {
            processMessageError(batchMessage, e);

        }
    }

}
```

个人觉得有一个优化点： **commit()执行完成应该把map中删除**， 因为在 ConsumerWorkerThread 线程退出前会调用一次 consumer.commitAll() 提交本地消息，会重复提交，虽然不影响结果，并且这里再次执行commitAll()感觉也很没有必要

对于 Direct 模式来说，它是每条消息都会ack，因为它覆写了processMessageSuccess()方法，详见DirectOrderConsumerProcessor代码：

```java
public class DirectOrderConsumerProcessor extends OrderConsumerProcessor {

    public void processMessageSuccess(Message message) {
        consumer.ack(message.getTopic(), message.getPartition(), message.getOffset());
    }

    @Override
    public void processMessageSuccess(BatchMessage batchMessage) {
        consumer.ack(batchMessage.getTopic(), batchMessage.getPartition(), batchMessage.getAckOffset());
    }
  
    public void process() {
        try {
            List<BatchMessage> batches = consumer.fetchDirectly();
            processMessageSuccess(batch);
        }catch (Exception e) {
            consumer.rewindCursor();
            throw e;
        }
    }
}
```

这里当消费抛出异常需要调用rewindCursor() 进行回退，避免下个循环fetch无法拉取到消息。



## hermes-manager 

控制台，用于topic，生产者，消费者的管理等。对应的git代码: http://git.caimi-inc.com/middleware/hermes-parent

## hermes-proxy 

hermes消息发送的代理服务器。对应的git代码：http://git.caimi-inc.com/middleware/hermes-parent。子项目：hermes-proxy

### 查询lag

curl 命令

```
curl -XPOST -H Content-Type:application/json http://10.0.23.227:8080/hermes-proxy/offsetlaginfo/batchQuery -d '[{"appName":"bullseye-report","groupId":"bullseye.report","topic":"bullseye.report.track.data.topic"}]'
```

从 redis 查询，appId=[10019](http://sailfish.wacai.info/admin/app/index.do?appId=10019)，key为:

```
hget middleware:cache:hermes:offsetMap:qshCommon:bullseye-report:bullseye.report.track.data.topic:bullseye.report 0
```

格式:

```
hget %{schema}:offsetMap:%{cluster}:%{appName}:%{topicName}:%{groupName} partition
```

### 创建topic

```
curl -X POST \
  http://localhost:8081/hermes-center/topic/addTopic \
  -H 'content-type: application/json' \
  -d '{"clusterId":"test","topic":"bairen.test","owner":"test"}'
```

### 发送消息

发生消息目前一下几个接口：

- sendSingle 业务最常用，通过hermes客户端
- sendBatch redalert 会使用，通过hermes客户端
- publish 兼容以前老接口

redalert 代码在 KafkaReporter ：

```java
 class ConsumeSon implements Runnable{

        @Override
        public void run() {
            KafkaReporter.this.running = true;
            List<Event> events = new ArrayList<Event>(100);

            while (KafkaReporter.this.running) {
                try {
                    Event event = KafkaReporter.this.queue.take();
                    events.add(event);
                    KafkaReporter.this.queue.drainTo(events, 99);

                    KafkaReporter.this.batchSend(events);
                } catch (InterruptedException e) {
                    logError("[WARN] kafka report thread is iterrupted");
                    break;
                } catch (Exception e) {
                    logError("[ERROR] Fail to kafka", e);
                }finally {
                    events.clear();
                }
            }
        }
    }
```





## hermes-birdge 

hermes消息消费的代理服务器。对应的git代码：http://git.caimi-inc.com/middleware/hermes-parent。子项目：hermes-bridge

### 核心类

#### 1、PartitionManager 

从kafka的zk获取所有topic的分区数。通过定时任务，一分钟同步一次。

#### 2、OffsetStorage

Offset保存和获取，有redis和zk实现。redis是以 clusterId + appName + topic +groupId为key，filed为partition保存offset的：

```java
final String offsetMapKey =
                    "offsetMap:" + AppConfig.clusterId + ":" + consumerVo.getAppName() + ":" + consumerVo.getTopic()
                            + ":" + consumerVo.getGroupId();
 String offsetStr = redisCluster.hget(offsetMapKey, consumerVo.getPartition() + "");
```

zk实现是以如下路径保存:

```java
public String getOffsetPath(ConsumerVo fetcherVo) {
    return "/offsets/" + fetcherVo.getAppName() + "/" + fetcherVo.getGroupId() + "/" + fetcherVo.getTopic() + "/"
    + fetcherVo.getPartition();
}
```

#### 3、ConsumerWrapper

kafka原生客户端的包装类。提供如下方法

- pull( )；拉取消息。
- getBeginOffset( )；
- getEndOffset( )；
- getBeginOffsetByTimestamp( )；

#### 4、 ConsumerPool

birdge用于从kafka集群中消费的线程池，线程名以 “bridge-fetch-thread” 开头。

线程池大小 **AppConfig.consumerSize** 通过 AppConfigLoader 从center获取，每个集群配置的都不一样，信息保存在 tb_cluster 表中。



### 初始化

启动时候通过AppConfigLoader初始化配置，主要需要获取clusterID，clusterID对应的kafka集群，zk集群等信息。

通过请求hermes-center获取:

```
http://api.qsh.wacai.info/hermes-center/cluster/hermes-bridge/**********/8080
```

### Fetch实现

FetchController 负责接口fetch请求， 拉取消息是批量拉取模式，默认一次拉取500条数据，Hermes默认会使用自己缓存的offset消费消息。

具体消费过程是调用RedisLockFetcher.doFetch方法，doFetch方法定义在AbstractFetcher中，

主流程如下:

1、initDefaultPartition方法

如果请求参数没有设置partition会初始化partition。

具体实现逻辑是：每次请求会对redis保存的计数器加一，然后通过计数器对partition数取模达到轮训的作用。

2、 tryLock方法

通过redis加锁。

加锁策略是以AppName+GroupId+Topic+Partition为Key，ConsumerId为Value加锁，锁超时时间默认是30s。

如果加锁失败直接返回错误，提示被ConsumerId已加锁。

3、设置offset位点

如果是手动模式，参数中有Timestamp，调用kafka原生API通过时间戳获取offset。

如果是自动模式，通过hermes缓存 OffsetStorage 获取offset。如果缓存不存在会执行重置offset策略，分为最早(earlest)重置和最新(latest)重置，默认是earlest，调用ConsumerWrapper.getBeginOffset(tp)获取最早offset。latest模式调用ConsumerWrapper.getEndOffset(tp)获取最新offset。

4、调用kafka.poll拉取消息

向consumerPool提交一个消费任务，任务中通过前面初始化好的参数调用ConsumerWrapper执行消费动作，任务执行超时时间为3秒。





### Ack实现

详见AckController

## hermes-center 

### 概述

Hermes的大脑，主要负责下发生产者和消费者配置，处理客户端上报的心跳数据。

响应心跳的入口类：HbController

这里需要注意：对于consumer消费的topic所属clientId是保存在消费者配置中的，如果要迁移topic所属集群需要修改或删除消费者配置数据。

删除消费者:

```shell
curl -XDELETE http://172.30.131.253:8080/hermes-center/manager/producerConf/drc-streams/db.xhy_wac_loan_goblin.gbl_collection_case_ext
```

### SLA

- 可靠性：是否存在消息丢失可能与kafka发送消息的ack策略、副本设置、ISR设置有关。高可靠情况下写入性能会下降，需要权衡。
- 吞吐量：取决于bridge端的fetchSize设置，fetchSize越大，吞吐量越高，但消息延时会变长
- 消息延时：消息发送到被客户端接收的时间间隔，主要取决于客户端fetch延时设置

| SLA服务级别 | 适用场景                                                     | ack策略 | fetch延时 | fetchSize |
| ----------- | ------------------------------------------------------------ | ------- | --------- | --------- |
| SLA-CDT     | 最常用的场景，也是目前common集群的配置，【目前业务集群的空闲延时在1000ms】 | 1       | 500       | 1M        |
| SLA-C       | 适用于需要保证消息绝对不丢失的场景                           | -1      | 500       | 1M        |
| SLA-D       | 适用于对消息及时性要求高的场景,保证消息在200ms内到达客户端   | 1       | 50        | 1M        |
| SLA-T       | 适用于对消息及时性要求高的场景                               | 0       | 500       | 10M       |

以上参数配置根据经验评估，实际情况还需验证后确定。

### API接口

#### 获取所有集群

- http://api.qsh.wacai.info/hermes-center/clusters
- http://hermes-center.middleware.wse.test.wacai.info/hermes-center/clusters

```json
{
	"code":0,
	"data":[
		{
			"brokerList":"***********:9092,***********:9092,***********:9092",
			"clusterId":"qshCommon",
			"consumerPoolSize":60,
			"description":"青山湖业务通用集群",
			"mq":"KFK1_0",
			"producerOneSize":1,
			"producerThreeSize":1,
			"producerTwoSize":1,
			"zkServer":"***********:2181,***********:2181,***********:2181"
		},
		{
			"brokerList":"***********:9092,***********:9092,***********:9092",
			"clusterId":"drcAggr",
			"consumerPoolSize":60,
			"description":"DRC数据订阅集群",
			"mq":"KFK1_0",
			"producerOneSize":1,
			"producerThreeSize":1,
			"producerTwoSize":1,
			"zkServer":"***********:2181,***********:2181,***********:2181"
		},
		{
			"brokerList":"***********:9092,***********:9092,***********:9092",
			"clusterId":"bi",
			"consumerPoolSize":60,
			"description":"BI非加密集群",
			"mq":"KFK1_0",
			"producerOneSize":1,
			"producerThreeSize":1,
			"producerTwoSize":1,
			"zkServer":"***********:2181,***********:2181,***********:2181"
		},
		{
			"brokerList":"***********:9092,***********:9092,***********:9092,***********:9092,***********:9092,***********:9092",
			"clusterId":"newlog",
			"consumerPoolSize":60,
			"description":"新日志集群",
			"mq":"KFK1_0",
			"producerOneSize":1,
			"producerThreeSize":1,
			"producerTwoSize":1,
			"zkServer":"***********:2181,***********:2181,***********:2181/kafkalog"
		},
		{
			"brokerList":"***********:9092,***********:9092,***********:9092",
			"clusterId":"detrace",
			"consumerPoolSize":60,
			"description":"风控detrace集群",
			"mq":"KFK1_0",
			"producerOneSize":1,
			"producerThreeSize":1,
			"producerTwoSize":1,
			"zkServer":"***********:2181,***********:2181,***********:2181"
		}
	]
}
```

#### 获取单集群

http://api.qsh.wacai.info/hermes-center/cluster/qshCommon

```json
{
	"code":0,
	"data":{
		"brokerList":"***********:9092,***********:9092,***********:9092",
		"clusterId":"detrace",
		"consumerPoolSize":60,
		"description":"风控detrace集群",
		"mq":"KFK1_0",
		"producerOneSize":1,
		"producerThreeSize":1,
		"producerTwoSize":1,
		"zkServer":"***********:2181,***********:2181,***********:2181"
	}
}
```

#### 通过IP查询集群信息

http://api.qsh.wacai.info/hermes-center/cluster/hermes-bridge/**********/8080

```json
	"data":{
		"brokerList":"***********:9092,***********:9092,***********:9092",
		"clusterId":"qshCommon",
		"consumerPoolSize":60,
		"description":"青山湖业务通用集群",
		"mq":"KFK1_0",
		"producerOneSize":1,
		"producerThreeSize":1,
		"producerTwoSize":1,
		"zkServer":"***********:2181,***********:2181,***********:2181"
	}
```

对应的接口：com.waicai.hermes.center.controller.ClusterController。通过查询 tb_client 获取

#### 心跳

对应类HbController.hbClient()方法。

获取 topic 关联的 EndPoint 信息，这个信息会缓存在redis。redis集群:

```
*************:7000,*************:7002,*************:7004
```

代码在ClientServiceImpl.getCachedClientsByType()方法，保存的数据为Hash格式，key为：

```
hgetall middleware:cache:hermes:test:hermes-center:EndPoints:test:hermes-bridge
hgetall middleware:cache:hermes:test:hermes-center:EndPoints:test:hermes-proxy
```

Hash 命令：

- hgetall 获取。
- del 删除。

获取 appName + topic + groupId对应的信息在 ConsumerConfService.getCachedConsumerConf()方法中，保存的数据为普通 json 字符串，对应的key为:

```
middleware:cache:hermes:test:hermes-center:ConsumerConf:little-boy-backend:loan.push.data.ready:com.wacai.loan.boy
middleware:cache:hermes:test:hermes-center:ConsumerConf:little-boy-backend:loan.internalLetter.data.ready:com.wacai.loan.boy
middleware:cache:hermes:test:hermes-center:ConsumerConf:little-boy-backend:loan.loan.vercode.sms.data.ready:com.wacai.loan.boy
middleware:cache:hermes:test:hermes-center:ConsumerConf:little-boy-backend:loan.vercode.sms.data.ready:com.wacai.loan.boy
```

Sql:

```
select * from tb_consumer_conf where appName = 'little-boy-backend'  and groupId='com.wacai.loan.boy' 
and topic in ('loan.push.data.ready','loan.vercode.sms.data.ready','loan.internalLetter.data.ready');
```

####   offset管理

详见ConsumerController 类，不过center会调用proxy的接口获取数据，比如

- 获取lag，实际调用proxy的/hermes-proxy/offsetlaginfo/batchQuery，对应OffsetController
- 重置offset，实际调用的是proxy的 OffsetController.ack方法

#### 消费回溯

消费回溯是指：不修改当前消费者的offset，只消费特定范围的内的消息，API 如下:

```
curl -X GET \
  'http://localhost:8081/hermes-center/consumer/backTraceConsume?partition=0&startOffset=0&endOffset=10&clusterId=log&appName=buzz-wacai-middleware&topic=bairen.test&groupId=bairen-group' \
  -H 'x-login-user: bairen'
```

注意 startOffset 和 endOffset 不能超过topic的offset范围。

内部实现原理是调用hermes-client进行消费

```
curl -X POST "http://localhost:8080/agent/consume?topic=bairen.test&group=bairen-group&partition=0&offset=0&batchSize=10
```

hermes-client 通过构成一个FetchRequest消费，详见ConsumeAction

### LAG告警

hermes-center 的LagAlertJob会定期查询消费者，发现延迟超过消费者配置的阈值则触发告警。告警通知会先通过应用查询owner再发送到人。详见 AlertUtil.getAppAndHermesOwnerList()方法。

### redis key

consumer使用到了两个redis:
- **live consumer**(hash key): %schema:consumedByMap:%cluster:%topic ,即：

  ```
  middleware:cache:hermes:test:consumedByMap:test:manyou.test
  field key = link-datahub|**************|8864:link.datahub
  ```

  

- **offset**(hash key): %schema:offsetMap:%cluster:%appName:%topic:%group  即:

  ```
  middleware:cache:hermes:offsetMap:qshCommon:link-analysis:link.stt.msg:link.stt.msg.group
  ```

  

  

## hermes-client 

hermes 发送/消费一个topic不需要设置proxy地址，而是通过心跳机制下发的。

### 心跳机制

通过 GlobalHeartbeatThread 主动轮训，轮训时间默认30秒，最短3秒。轮训之前会先把本地的生产消费config都全部捞一遍组装成参数发送到center对应的HbController处理，如果有变更会更新本地，此方案的缺点是实时性比较差。



```java
 public boolean heartbeat() {
    buildHeartBeatRequest();
    buildConsumerRequest();
    buildProducerConfigsRequest();

	if (clientHBReq.getConsumerInfos().isEmpty() && clientHBReq.getProducerInfos().isEmpty()) {
		return false;
	}

	ClientHBResp clientHBResp = CenterAPI.heartbeat(HermesAppConfig.get().getCenter(), clientHBReq);

	if (clientHBResp != null) {
		HermesAppConfig.started = true;

		// 更新生产者配置
		updateProducerResponse(clientHBResp);
		// 更新消费者配置
		updateConsumerResponse(clientHBResp);
		//更新app信息
		HermesAppConfig.get().updateByClientHbResp(clientHBResp);
		//更新HermesContext 消费者和生产者配置信息 通过HermesConfigContext.cConfigMap pConfigMap传递 
		HermesConfigContext.get().updateContextByHbInfo(clientHBResp);
		//更新EndPointContext 在线的生产代理和消费代理的信息
		EndPointContext.get().updateContextByHbInfo(clientHBResp);
		// 更新kafka集群信息用于直连kafka的客户端
		KafkaClusterInfoContext.getInstance().updateContextByHbInfo(clientHBResp);
		return true;
    }
  
 }
```

心跳会返回每个topic对应的clusterId和endpoints等信息，比如

```properties
 topic=@String[erp.financial.bizitem.info],
groupId=@String[baige_sdk_consumer_group_9b7s87v5st6e],
clusterId=@String[test],
status=@String[RUNNING],
consumerNum=@Integer[1],
busyDelayMs=@Integer[200],
idealDelayMs=@Integer[200],
batchSize=@Integer[500],
filterExpress=null,
reset=@Integer[1],
pollMs=@Integer[200],
ignoreAck=@Boolean[false],
timestamp=@Long[0],
endPoints=@ArrayList[{ip='hermes-proxy.middleware.wse.test.wacai.info', port=80, type='null'}],
retryPolicy=@String[ORDER],
intervalGap=@Integer[5000],
maxAttempts=@Integer[10],
maxIntervalGap=@Integer[0],
multiplier=@Double[0.0],    
```

然后更新到 EndPointContext 中。

EndPointContext 维持 EndPoint 信息

```java
public class EndPointContext {
    
    private volatile Map<String, List<EndPoint>> proxyEndpoints = new ConcurrentHashMap<>();
    
    private volatile Map<String, List<EndPoint>> bridgeEndpoints = new ConcurrentHashMap<>();
    
    public EndPoint selectEndPoint(String clusterId, String clientType) {
        List<EndPoint> allPoints = null;
        if (ClientType.HERMES_PROXY.equalsIgnoreCase(clientType)) {
            allPoints = proxyEndpoints.get(clusterId);
        } else if (ClientType.HERMES_BRIDGE.equalsIgnoreCase(clientType)) {
            allPoints = bridgeEndpoints.get(clusterId);
        } else {
            throw new HermesException("选择endpoint传入错误的clientType:" + clientType);
        }
        if (allPoints == null || allPoints.isEmpty()) {
            GlobalHeartbeatThread.setNeedUpdate(true);
            throw new EndpointEmptyException("找不到可用的hermes节点,当前集群ID:" + clusterId + ",节点类型:" + clientType);
        }
        AtomicPositiveInteger seq = sequences.get();
        return allPoints.get(seq.getAndIncrement() % allPoints.size());
    }
}
```



### 消费消息

#### 入口

HermesAutoConfiguration 工厂类负责创建Hermes消费者实例。 关键方法是createAndStartConsumers()，会根据用户接入不同的sdk创建不同的HermesConsumerConfig，最后实例化HermesCompositeConsumer。

HermesCompositeConsumer 消费者适配器。通过不同AccessModel选择实例化不同的消费者：

- HermesProxyConsumer。http代理实现。
- HermesDirectConsumer。原生实现。
- HermesNativeConsumer。

也可以直接使用 HermesCompositeConsumer：

```java
   		/**
         * 消费者配置，设置消费消息的topic和分组groupId
         */
        HermesConsumerConfig config = new HermesConsumerConfig();
        config.setTopic("lengfeng.test.topic");
        config.setGroupId("lengfeng-single-group");

        HermesConsumer consumer = new HermesCompositeConsumer(config) {
            @Override
            public void onMessageReceived(Message message) {
                String key = new String(message.getKey(), StandardCharsets.UTF_8);
                String value = new String(message.getValue(), StandardCharsets.UTF_8);

                System.out.println(String.format("key=%s,value=%s", key, value));
            }
        };

        consumer.start();

        System.out.println("start simple consumer example");

```

先来看 http proxy 的消费者实现。

```java
public class HermesProxyConsumer extends BaseDefaultConsumer {
    public HermesProxyConsumer(HermesConsumerConfig config, HermesConsumer hermesConsumer) {
        super(config, hermesConsumer);
        this.consumerApiTemplate = new ConsumerApiTemplate(this);
    }
	protected void startWork() {
 		for (int i = 0; i < cConfig.getConsumerNum(); i++) {
            executorService.submit(new ConsumerWorkerThread(this, runLatch));
        }  		
	}
}
```

ConsumerWorkerThread 是一个task，不停执行消费动作，注意ConsumerWorkerThread持有HermesProxyConsumer的引用，调用的都是HermesProxyConsumer方法。

```java
public class ConsumerWorkerThread implements Runnable {
    @Override
    public void run() {
        HLOG.info("consumer[{}] started", consumer.getConsumerId());
        log.info("consumer[{}] started. HermesConsumerConfig={}", config);
        int sleepTime = 0;
    	while (consumer.getConsumerState() == ConsumerState.STARTING || consumer.getConsumerState() == ConsumerState.STARTED) {
            if (sleepTime > 0) {
                try {
                    TimeUnit.MILLISECONDS.sleep(sleepTime);
                } catch (InterruptedException ignore) {
                }
            }
            /**
             * bug fix on 20190516 
             * sleep之后要归0，不然在出现一次Throwable异常后，就会一直LONG_LONG_SLEEP，没有机会再恢复回去 
             */
            sleepTime = 0;
            try {
                // 消费处理策略
                consumer.getConsumerProcessor().process();
                errorCounter.set(0);
            } catch (ProcessingException pe) {
                HLOG.info("processing meet some exception:{}", pe.getMessage());
                log.info("processing meet some exception:{}", pe.getMessage());
                sleepTime = LONG_SLEEP;
            } catch (OffsetOutOfRangeException e) {
                HLOG.warn("[fetch]fetch meet out of range,continue fetch." + consumer.getGroupTopic());
                log.warn("[fetch]fetch meet out of range,continue fetch." + consumer.getGroupTopic());
                sleepTime = LONG_SLEEP;
            } catch (FetchLockException e) {
                sleepTime = config.getBusyDelayMs();
            }
		}
        //提交最有一次拉取的offsets
        consumer.commitAll();
        runLatch.countDown();
    }        
}
```

#### process()

HermesProxyConsumer默认的处理器是OrderConsumerProcessor，在父类 FailFastConsumerProcessor 定义了process()实现也是消费的整体框架：

```java
public void process() {
    //拉取消息，默认会一次拉取500条
    BatchMessage messages = consumer.fetch();
    if (messages == null || messages.getTotal() == 0) {
      sleepMs(consumer.getConsumerConfig().getIdleDelayMs());
      return;
    }  
    //根据消息延迟平均值*消息量动态计算超时时间，小于28秒，按照28秒算，上限180秒
    long timeout = estimateDynamicPause(messages);
    
    try {
            //处理消息可能会抛出异常，commit会提交异常前的处理，但消息不会丢。
            processBatchMessage(messages, timeout);
     } finally {
         new RetryTemplate<Void>().doWithRetry(3, 2000, () -> {
             //尽可能提交成功，否则会出现重复消费
             commit(topic, partition);
             return null;
         }, CommunicationException.class);
     }
}
```

整体流程如下：

1. 调用 consumer.fetch()拉取消息，默认会一次拉取500条。
2. 计算超时时间，计算方式是根据平均延迟，计算下限28，上线 180 秒
3. 对消息进行处理。
4. 发送ack。

#### fetch()

调用 ConsumerApiTemplate.fetch() ，代码比较简单就是获取消息。

#### estimateDynamicPause()

计算超时时间 代码如下：

```java
protected long estimateDynamicPause(BatchMessage batchMessage) {
		//获取 ExecuteDelayMetrics 指标，这个指标在后面的代码中会更新
  	Timer timer = HermesMetricManager.getInstance().getConsumerReporter()
      .getExecuteDelayMetrics(consumer.getConsumerId());
   	if (timer != null && timer.getSnapshot().getMean() > 0) {  
      	double meanTime = AgentReporter.convertDuration(timer.getSnapshot().getMean());
    		long adjustPauseTime = new Double(meanTime * batchMessage.getTotal() + 5000).longValue();
      	
      	//如果超过30秒需要手动pause，避免消息重复
      	if (adjustPauseTime > GlobalHermesProps.CONSUMER_PROCESSOR_ESTIMATE_PAUSE_MIN_MS) {
          	//但是也有一个上限180秒
          	long timeout = Math.min(adjustPauseTime, GlobalHermesProps.CONSUMER_PROCESSOR_ESTIMATE_PAUSE_MAX_MS);
          	consumer.pause(firstMessage.getTopic(), firstMessage.getPartition(), timeout);
                        return timeout;
          	return timeout;
        }
    }
  	//返回默认超时时间 28 秒
  	return DEFAULT_MAX_PROCESS_MS;
}
```

1. 获取业务消费耗时指标 ExecuteDelayMetrics 计算暂停时间
2. 如果超过 30 秒，发送锁续期请求，并返回超时时间
3. 返回默认的超时时间28秒

#### processBatchMessage()

在基类 AbstractConsumerProcessor 中定义了 processBatchMessage():

```java
protected void processBatchMessage(BatchMessage batchMessage, long timeout) {
  	long startTime = System.currentTimeMillis();
    List<Message> messageList = batchMessage.getMessageList();
  	switch (config.getMethodType()) {
       	case BYTEKEY:
        case BYTE_CONSUMER:
        case MESSAGE:
        case MESSAGE_CONSUMER:
        case OLD:
        case OLD_CONSUMER:
            for (Message message : messageList) {
                //超时检查，避免重复消费
                if (System.currentTimeMillis() - startTime > timeout) {
                    throw new ProcessingException("process timeout," + timeout);
                }
                //检查consumer是否在停止
                if (isConsumerStoped()) {
                    throw new ProcessingException("consumer is stopping");
                }
                try {
                    //调用业务onMessageReceived()
                    HermesUtil.onMessageReceivedWrapper(consumer, config, message);
                    //记录已处理的offset
                    consumer.getProcessedOffsetsMap().put(message.getTopic() + ":" + message.getPartition(), message.getOffset());
                    //默认是空实现
                    processMessageSuccess(message);
                } catch (Throwable e) {
                    log.error("consumer[{}] process message exception, id {} caused by:{}", this.config.getConsumerId(),
                              MessageUtil.getMessageId(message), HermesUtil.getMessage(e));
                    //处理失败钩子
                    processMessageError(message, e);
                }
            }
        		break;
        case BATCH:
            long startTime = System.currentTimeMillis();
            try {
                consumer.onMessagesReceived(messageList);
                Message message = messageList.get(messageList.size() - 1);
                consumer.getProcessedOffsetsMap()
                        .put(message.getTopic() + ":" + message.getPartition(), message.getOffset());
                processMessageSuccess(messageList);
            } catch (Throwable e) {
                processMessageError(messageList, e);
            }finally {
                try {
                    ConsumerReporter.getInstance().getExecuteDelayMetrics(config.getConsumerId())
                            .update(System.currentTimeMillis() - startTime, TimeUnit.MILLISECONDS);
                } catch (Throwable e) {
                    //ignore
                }
            }
            break;        		
  	}
       
}
```

1. 调用 HermesUtil.onMessageReceivedWrapper() ，内部会更新ExecuteDelayMetrics，调用 consumer.onMessageReceived()回调
2. 把offset信息写入map中。
3. 如果执行失败调用 processMessageError()

#### processMessageError()

代码如下：

```java
    public void processMessageError(Message message, Throwable e) {
        final String messageId = MessageUtil.getMessageId(message);
        AtomicInteger counter = retryCounter.putIfAbsennt(messageId,new AtomicInteger();
        counter.incrementAndGet();
				//超过重试次数发送ack                                                          
        if (counter.get() > cConfig.getMaxAttempts()) {
            consumer.ack(message.getTopic(), message.getPartition(), message.getOffset());
            AlertUtil.alert(message, config);
        } else {
          //lock续期
            AlertUtil.alert(message, config);
            consumer.pause(message.getTopic(), message.getPartition(), cConfig.getIntervalGap());
            throw new ProcessingException("process message error");
        }
    }
```

检查重试次数，如果超过maxAttempts(默认10次)，发送ack不再尝试， 否则调用 ConsumerApiTemplate.pause() 缩短锁时间，默认是5秒

#### commit()

```java
 protected void commit(String topic, int partition) {
        Long processedOffset = consumer.getProcessedOffsetsMap().get(topic + ":" + partition);
        if(processedOffset!= null) {
            consumer.ack(topic, config, partition, processedOffset);
            HLOG.info("[ack]consumer ack topic {} partition {} offset {}", topic, partition, processedOffset);
            ackedOffsetMap.put(topic + ":" + partition, processedOffset);
        }
    }
```

代码比较简单，从本地map中获取已经处理过的offset，调用 ConsumerApiTemplate.ack()确认offset

### 监控统计

监控统计包括如下指标：

- produce_msg_count 生产数量 producerCountMeter
- produce_msg_bytes  生产流量 flowMeter
- produce_msg_latency  生产延迟 metricsInfo
- produce_msg_fail_count 生产失败 logMetrics
- consume_msg_count 消费数量 msgCountMetrics
- consume_msg_bytes 消费流量 msgTrafficMetrics
- consume_delay_time 消息的延迟 msgDelayMetrics
- consume_end2end_time 端到端延迟 end2endMetrics

这里我们看下消费者延迟的计算规则

#### 规则
1. 消息的延迟=消息开始消费时间-消息存储在Kafka的时间（每条消息kafka都会记录一个时间戳）
2. 端到端延迟=消息开始消费时间-消息开始生产时间 (hermes记录在header里)

#### 延迟记录 

```java

public class ProcessMessageMetricsListener extends DefaultListener {
  
   @Override
    public void preProcess(Message message) {
        long processMessageStartTime=System.currentTimeMillis();
        partitionLastOffset.put(message.getPartition() + "", message.getOffset());
        config.setRecentOffset(partitionLastOffset);
        config.setLastReceiveTime(processMessageStartTime);

        // 消息的延迟=消息开始消费时间-消息存储在Kafka的时间
        consumerReporter.getMsgDelayMetrics(config.getConsumerId())
                .update(processMessageStartTime - message.getTimestamp(), TimeUnit.MILLISECONDS);

        //端到端延迟=消息开始消费时间-消息开始生产时间
        String sendTime = message.getHeaderStringByKey(HermesHeader.SEND_TIME);

        if (StringUtils.isNotBlank(sendTime)) {
            long end2end = processMessageStartTime - Long.parseLong(sendTime);
            consumerReporter.getEnd2endTimer(config.getConsumerId()).update(end2end, TimeUnit.MILLISECONDS);
        }
    }
}
```

#### 数据上报
监控信息是和心跳包一起上报到hermes-center， 构造ConsumerInfo方法如下：

```java

public class HermesMetricManager {
  public ConsumerInfo buildConsumerInfoMetrics(HermesConsumerConfig config, String topic) {
    
        ConsumerInfo consumerInfo = new ConsumerInfo();
        consumerInfo.setGroupId(config.getGroupId());
        consumerInfo.setTopic(topic);

        InfluxDbPoint msgDelayPoints = AgentReporter
                      .reportTimer("msgDelayMetrics", consumerReporter.getMsgDelayMetrics(consumerId),
                              System.currentTimeMillis(), tagMap);
        consumerInfo.setMsgDelayMetrics(JSONObject.toJSONString(msgDelayPoints));
        //...
  }
}

```

GlobalHeartbeatThread 发送代码:
```java

private void buildConsumerRequest() {
        consumerForHeartbeatMap.clear();
        List<ConsumerInfo> consumerInfoList = new ArrayList<>();

        // 单个Topic: consumerId -> HermesConsumer
        Collection<HermesConsumer> heartbeatConsumers = HttpEndpointController.getInstance().getHeartbeatConsumers();
        for (HermesConsumer consumer : heartbeatConsumers) {
            //调用 HermesMetricManager.buildConsumerInfoMetrics()
            ConsumerInfo consumerInfo = HttpEndpointController.getInstance().getMetricManager()
                    .buildConsumerInfoMetrics(consumer.getConsumerConfig());
            consumerInfoList.add(consumerInfo);
            consumerForHeartbeatMap.put(consumer.getGroupTopic(), consumer);
        }
        clientHBReq.setConsumerInfos(consumerInfoList);
    }

```



## kafka-http-proxy2

这个项目是刀哥最开始搞的 kafka 代理层。对应的git分支：http://git.caimi-inc.com/middleware/kafka-http-proxy2

通过域名发送消息：

- http://api.test.wacai.info/kafka-proxy/kafka/publish
- http://api.qsh.wacai.info/log-kafka-proxy/kafka/publish 

发送消息方式:

```json
curl  -X POST "http://api.qsh.wacai.info/kafka-proxy/kafka/publish?topic=middleware.all.version&message=test"
curl  -X POST "http://api.qsh.wacai.info/log-kafka-proxy/kafka/publish?topic=middleware.all.version&message=test"
```

对应的主机：

- 生产：**********, ************
- 测试：*************

对应的kafka集群：

- 业务集群：**********:9092,**********:9092,**********:9092

监控

```
tt -t *ServiceLocator findService -n 10 'params[0]=="loan.gateway.sms.receipt"'
tt -t com.wacai.kafka.proxy.io.ProducerWrapper sendMessage 'params!=null&&params[0].topic=="loan.gateway.letter.receipt"'
```



## hermes-replicator 

### 相关类

- ClusterPipelineManager	 SpringBean。 负责管理 ClusterPipeline，因为ClusterPipeline会存在多个。
- MirrorMakerConfig 配置信息
- ClusterPipeline 负责管理 MirrorMakerWorker 的生命周期。
- AbstractMirrorMakerTask 抽象类，定义了任务复制的公共方法。
- MirrorMakerWorker  继承 AbstractMirrorMakerTask。
- HermesKafkaConsumer 扩展kafkaConsumer实现TOPIC partition offset 监控。
- ReplicateConsumerRecordHandler 负责发送消息。

## hermes-proxy限流

hermes-proxy 具有限流能力，限流支持正则，白名单表示不限流，限流规则保存在 tb_flow_rule 表，未上线

### 主要流程

1、 加载限流规则

Hermes应用启动时， HbSchedule 的init()方法会调用 HermesFlowRuleManager.updateFlowRulePeriod(); 

从 hermes-center 获取限流规则，然后加载到sentinel
代码：

```java

public static void updateFlowRule(List<FlowRule> flowRules) {
	FlowRuleManager.loadRules(flowRules);
	ClusterFlowRuleManager.loadRules(flowRules);
	ClusterStateManager.setToClient();
}
```

2、执行限流

主要逻辑在 FlowLimitAspect，限流本身非常简单

3、限流规则的执行

```
+-----------------+     +---------------------+     +--------------------+     +---------------------------+     +---------------+
| FlowLimitAspect | --> | DefaultTokenService | --> | ClusterFlowChecker | --> | RedisGlobalRequestLimiter | --> | LuaTpsCounter |
+-----------------+     +---------------------+     +--------------------+     +---------------------------+     +---------------+
```

## hermes 鉴权

前提：topic开启了安全认证

操作步骤

1、uav申请key

2、uav申请生产者配置(未开启安全认证，hermes-center会自动创建，开启之后就无法自动创建了)

3、uav申请消费者配置

 对于hermes-client，第一次fetch会失败，然后在catch代码中基于key获取token，再发送给服务端进行认证。

## ddmq

滴滴开源的mq代理层，用户文档参考[这里](https://github.com/didi/DDMQ/blob/master/carrera-console/USAGE.md)，基本原理用户配置消费回调(可以是 HTTP、GRPC)，proxy到消费kafka消息之后会主动调用回调，相当于是主动push。

### 相关类

- ConsumerManager。 负责管理ConsumerGroupManager，内部维护了group和ConsumerGroupManager的map。
- ConsumerGroupManager。负责管理LowLevelCarreraConsumer。内部维护了group和CarreraConsumer 的map。
- CarreraConsumer。消费者的门面。内部会根据配置创建 CarreraNewRocketMqConsumer 或 CarreraKafkaConsumer 进行消费，同时它还实现了 AsyncMessageHandler 接口，当底层的消费者收到消息时，会回调它的 process 方法，process 方法中调用 UpstreamJob，UpstreamJob内部根据consumer 配置进行不同的处理，比如发送HTTP回调，写HDFS，具体可以看下Action接口的实现类。
- ConsumerServiceImpl。接收 thrift 请求的处理器。使用 consumerManager。

### 拉取消息过程

ddmq拉取消息是推拉结合，过程如下：

1、 ConsumerServiceImpl.pull() 方法负责处理收到的PullRequest，如果参group对应的CarreraConsumer不存在，则会根据ConsumerGroupConfig创建并启动CarreraConsumer。
2、 启动CarreraConsumer流程：

- 构建Action，Action包括：JsonAction，AsyncHttpAction，GroovyScriptAction，PullServerBufferAction，RedisAction，HBaseAction，通过用户配置
- 创建 ConsumerConnector 并拉取消息并触发调用 CarreraConsumer.process()方法
- CarreraConsumer 拉取到消息后会触发调用 CarreraConsumer.process()方法
- CarreraConsumer.process()会调用创建UpstreamJob执行不同的Action：
    - 对于PullServerBufferAction, 会把消息缓存到本地buffer中，同时唤醒延长任务并执行

3、从 buffer 中拉取数据，如果不为空立即返回，否则创建一个延迟任务



## hermes 踩坑

### 1、没有消息也会不停轮训proxy，大量空轮训

http://cloud.wacai.info/k2/application/middleware/app/domino-file-web

domino-file-web-c6984f6fd-g47jg ，查看网络请求和topic

```
tt -t com.wacai.hermes.common.http.HttpBuilder execute
tt -t com.wacai.hermes.agent.consumer.template.ConsumerApiTemplate  fetch
```

### 2、重复消费

由于hemres分布式锁的超时时间是 30 秒，所以需要确保consumer在 30 秒能处理完一批数据，如果发现有重复需要减少拉取的数据



### 3、测试环境 hermes-proxy-log 无法发送消息

原因：设置了环境变量ARTIFACT导致无法读取 application.properties 配置文件，注册的是默认cluster，修复之后重启发现oom，原因topic的大小限制没有修改默认 1MB，导致一直报错，触发了动态agent的OOM的bug

### 4、发消息非常慢或重复消费

排查发现，每次发送都会先失败一次。

原因EndPointContext中存在错误的Endpoint ，日志集群返回了hermes-proxy地址

```
 tt -t com.wacai.hermes.agent.core.EndPointContext selectEndPoint
```



<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230216155723531.png" alt="image-20230216155723531" style="zoom:60%;" />

在center中通过redis保存相关信息：

执行命令：

```
hgetall middleware:cache:hermes:test:hermes-center:EndPoints:log:hermes-proxy 
hgetall middleware:cache:hermes:test:hermes-center:EndPoints:log:hermes-bridge
```

可以通过 DEL 直接删除整个hash，如果删除单个field使用 HDEL

同理，消费也存在同样的问题，无法ack，需要删除 middleware:cache:hermes:test:hermes-center:EndPoints:log:hermes-bridge 

这个信息保存在数据库，如果原始数据错乱问题很大，删除：

```
delete from tb_client where clientType='hermes-bridge' and clusterId='log' and ip='hermes-proxy.middleware.wse.test.wacai.info';
```



ACTION: **hermes-center最好能提供8864端口，能查询这些信息**

### 5、fetcher 瓶颈记录

1、fetcher的线程池大小是通过 consumerPoolSize参数设置， hermes-center下发的，信息保存在 tb_cluster 表中。

 2、fetcher的qps吞吐量公式：

```
qps=concurreny*(1000/rt)
```

假设线程数设置为60，rt为 200，那么理论最大qps为 300，通过arthas可以验证：

```
monitor com.wacai.hermes.proxy.kafka.KafkaConsumerWrapper pull -c 1
```

输出的qps确实为300，启动 500 个线程消费，随机向某个topic发消息，延迟上升到5秒

![image-20230216204606317](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230216204606317.png)

### 6、批量消费如何采取最优 commit 策略

批量消费500 条消息，如果每条commit一次，效率太低，如果批量commit，执行到末尾程序失败，前面的没有commit导致消息重复，如何处理最优？

> 方案：每条消息处理之后记录到本地map，如果后续消息处理报错，提交之前本地map记录的offset即可。
>
> 不过对于 onMessagesReceived() 批量方法，只有全部执行成功才会记录点位

### 7、明明有消息有时候拉取不到

手动fetch消息，明明有消息有时候会返回空数据有时候又正常，这种原因可能是pollMs时间太短导致

### 8、经常出现线程池 RejectException

原因是之前队列大小设置128不合理，队列太小导致的，合理值500~1000 

### 9、发送消息出现延迟很高的情况

遇到应用启动 kafka producer.send() 耗时比较高的问题，达到2秒，网上也有人问过

-  [KAFKA-9151](https://issues.apache.org/jira/browse/KAFKA-9151) 
- [Why kafka producer is very slow on first message](https://stackoverflow.com/questions/64228639/why-kafka-producer-is-very-slow-on-first-message)

DDMQ 提供了[warmup方案](https://github.com/didi/DDMQ/blob/master/carrera-producer/src/main/java/com/xiaojukeji/carrera/pproxy/producer/KafkaClusterProducer.java) ，思路是通过 producer.partitionsFor(topic); 预热

### 10、hermes 消费延迟

发布新版 hermes-proxy时，由于老版hermes-proxy没有做优雅关闭导致报错，客户端会收到异常：

```
标题:hermes告警[qsh-online][legion-sinker-10.0.21.139][ 消息消费未知异常]
详情:当前topic:loan.alchemist.bill.report,当前分组:legion,异常详情:{"code":-1,"error":"Task java.util.concurrent.ScheduledThreadPoolExecutor$ScheduledFutureTask@1d731d4b rejected from java.util.concurrent.ScheduledThreadPoolExecutor@26ee3d61[Shutting down, pool size = 20, active threads = 0, queued tasks = 7, completed tasks = 23481571]"}
```

理论上这里异常不会有任何问题，但是hemes-client 2.1.3同时存在一个bug就会有问题，代码如下:

```java
public class ConsumerWorkerThread implements Runnable {
    public void run() {
      int sleepTime = 0;
      while(true){
         Thread.sleep(sleepTime);  
         try {
           	// 消费处理策略
           	consumer.getConsumerProcessor().process();    
         }catch( Throwable e){
             sleepTime = LONG_LONG_SLEEP;
         }
      }
    }
}
```

这里一旦出现异常，睡眠时间永远不会重置，后续每次消费都会等待同样的时间，导致延迟变高，排查这个问题花了不少时间

![image-20230628173328088](/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230628173328088.png)

### 11、netty出现read timeout

参考 netty benchmark 这里不再赘述



### 12、优雅关闭问题

见问题 10，重新部署 hermes-proxy 会出现部分流量被拒绝，原因在于k8s关闭pod有两个步骤是同时进行：

1. 改变网络规则；
2. 杀死pod中的容器；

具体流程参考[这篇文章](https://mp.weixin.qq.com/s/piIawEmLaf7zNKeQO4p1cg)

导致的后果是应用关闭过程中还会有流量进入，当线程池被关闭时，这部分流量就会被拒绝。文章中给出了一个方案是在 preStopHook 增加sleep时间，但测试之后发现会导致大量 write channel error,  原因是因为我这边是long-fetch，在 preStopHook 增加sleep之后导致太晚发送response，这时候ingress已经关闭了和pod之间的连接，所以**在 preStopHook 增加sleep时间方案是行不通的**

【更正】，上述猜测是错误的， 导致 write channel error 真正原因是测试环境中使用了istio，istio有如下逻辑：

1.  sidecar 也收到 `SIGTERM` 信号，立刻不再接受 inbound 新连接，但会保持存量 inbound 连接继续处理，outbound 方向流量仍然可以正常发起
2. istio 默认是会在停止开始的 5s 后强制杀死 envoy，当 envoy 进程不在了也就无法转发任何流量(不管是 inbound 还是 outbound 方向)

换言之，收到 SIGTERM 信号5 秒之后envoy进程就被强杀了，当然无法 write channel error，这个问题能排查出来是因为关闭过程中发现 redis client 被 close 之前就发现 redis ConnectionException，日志打印时间线：

```
16:46:09 server is stopping  
16:46:15 第一次跑出 com.wacai.common.redis.exceptions.ConnectionException
```

刚好在应用开始关闭 5 秒之后开始报错，符合预期。

解决方案就是避免envoy进程被强杀，在preStop中添加一个脚本：

```shell
curl -X POST localhost:15000/drain_listeners?inboundonly; while [ $(ps -e -o uid,pid,comm | grep -v '1337' | grep -v '1 pause' | grep -v 'UID' | wc -l | xargs) -ne 0 ]; do sleep 1; done
```

向 enovy 的 admin port 发送请求，将入口方向的 listener 结束。注意，这里特别设置了 inboundonly，不关闭出方向的 listeners，因为即使处于终止阶段，用于进程可能仍然需要向外发送请求。后续的 while 循环通过 ps 等待所有用户进程退出，间隔 1s。

参考

- [让 Istio sidecar 优雅终止](https://blog.fatedier.com/2021/12/16/istio-sidecar-graceful-shutdown/)
- [istio 最佳实践: 优雅终止](https://imroc.cc/post/202106/istio-graceful-termination/)

### 13、hermes控制台lag无法重置

可能原因：

1. 如果消费者没有停机可能因为并发问题导致重置没有效果
2. hermes-mananager和hermes-proxy的redis集群不是同一个



### 14、 多个消费者问题

测试环境发现 qy.mark.tag-0 存在多个消费者，但是消费组配置的是单线程，查看edu-gatling-service应用发现使用的hermes-spring，其配置如下:

```properties
spring.hermes.consumers.qy-mark-tag.topic=qy.mark.tag
spring.hermes.consumers.qy-mark-tag.group=${spring.application.name}
spring.hermes.consumers.qy-mark-tag.enabled=true

spring.hermes.consumers.qy-mark-tag1.topic=qy.mark.tag
spring.hermes.consumers.qy-mark-tag1.group=${spring.application.name}
spring.hermes.consumers.qy-mark-tag1.enabled=true

spring.hermes.consumers.qy-mark-tag2.topic=qy.mark.tag
spring.hermes.consumers.qy-mark-tag2.group=${spring.application.name}
spring.hermes.consumers.qy-mark-tag2.enabled=true
```

代码：

```java
/**
 * <AUTHOR>
 * @date 2021/9/29 11:18
 * @mail <EMAIL>
 * @description
 */
@Component
@Slf4j
@EnableBootHermes(autoStart = true)
public class KafkaQyMarkTagConsumer {

    @Resource
    private MarkTagService markTagService;

    @SpringConsumer(consumerId = "qy-mark-tag")
    public void consumer(long offset, String key, byte[] data) {
        log.info("consumer qy-mark-tag");
        consumeMarkTagMessage(offset, key, data);
    }

    @SpringConsumer(consumerId = "qy-mark-tag1")
    public void consumer1(long offset, String key, byte[] data) {
        log.info("consumer qy-mark-tag1");
        consumeMarkTagMessage(offset, key, data);
    }

    @SpringConsumer(consumerId = "qy-mark-tag2")
    public void consumer2(long offset, String key, byte[] data) {
        log.info("consumer qy-mark-tag2");
        consumeMarkTagMessage(offset, key, data);
    }

    public static void main(String[] args) {
        byte[] data = new byte[4];
        log.info("consumer qy-mark-tag2 message = {}", data);

    }


    /**
     * 打标消息处理
     *
     * @param offset
     * @param key
     * @param data
     */
    private void consumeMarkTagMessage(long offset, String key, byte[] data) {

        KafkaQyMarkTagDTO kafkaQyMarkTagDTO;
        try {
            String content = new String(data, Charsets.UTF_8);
            log.info("KafkaQyMarkTagConsumer offset:[{}], key:[{}], data:[{}]", offset, key, content);
            kafkaQyMarkTagDTO = JSON.parseObject(content).toJavaObject(KafkaQyMarkTagDTO.class);
            log.info("KafkaQyMarkTagDTO {}", JSON.toJSONString(kafkaQyMarkTagDTO));
            markTagService.handler(kafkaQyMarkTagDTO);
        } catch (Exception e) {
            log.error("KafkaQyMarkTagConsumer offset = " + offset + " kafka message deserialize error", e);
            return;
        }

    }
}
```

后续升级版本需要关注兼容性，另外 [edu.interactive.lesson.commit](http://middleware.wacai.info/hermes-web/msg-topic-detail?topic=edu.interactive.lesson.commit&clusterId=qshCommon) 这个topic 同时被原生和代理消费，可以对比p99延迟，差距还是比较大



## HTTP API

### 发送消息

```shell
curl -XPOST http://hermes-proxy-log.middleware.wse.test.wacai.info/kafka/publish -d "topic=bairen.test.1" -d "message=test"

curl -XPOST http://localhost:8080/kafka/publish -d "topic=manyou.test" -d "message=test"

curl http://localhost:8080/hermes-proxy/sendSingle\?topic\=manyou.test -d '{"data":"aGVsbG8=","headers":{},"messageId":"9cd167d4-e943-4a77-9f71-c613ce8c4b4c","partition":0,"payLoad":5,"timestamp":0,"topic":"manyou.test"}'

curl http://localhost:8080/hermes-proxy/sendSingle\?topic\=bairen.test.101 -d '{"data":"aGVsbG8=","headers":{},"messageId":"9cd167d4-e943-4a77-9f71-c613ce8c4b4c","partition":0,"payLoad":5,"timestamp":0,"topic":"bairen.test.101"}'
```

### 手动 fetch

```shell
curl "http://localhost:8080/hermes-bridge/fetch?consumerId=**************-7-CAAAF&appName=buzz-wacai-middleware&groupId=bairen-test-1&topic=manyou.test&reset=0&fetchMax=50&manualOffset=1&pollMs=200&partition=0"
 
 
curl  -H 'fetch-type:long-fetch' \
"http://localhost:8080/hermes-bridge/fetch?consumerId=**************-7-CAAAF&appName=buzz-wacai-middleware&groupId=bairen-test-1&topic=manyou.test&reset=0&fetchMax=50&manualOffset=1&pollMs=200&partition=0"
  
```

### 自动 fetch  

log集群

```shell
 curl  -H 'fetch-type:long-fetch'  "http://localhost:8080/hermes-bridge/fetch?consumerId=**************-7-CAAAF&appName=buzz-wacai-middleware&groupId=bairen-test-2&topic=linkup.record.event.prediction&reset=1&fetchMax=50&pollMs=200"
```

test集群

```shell
curl  "http://localhost:8080/hermes-bridge/fetch?consumerId=**************-7-CAAAF&appName=buzz-wacai-middleware&groupId=bairen-test-2&topic=bairen.test.2000&reset=1&fetchMax=50&pollMs=200"
  
curl  -H 'fetch-type:long-fetch' \
"http://hermes-proxy.middleware.wse.test.wacai.info/hermes-bridge/fetch?consumerId=**************-7-CAAAF&appName=buzz-wacai-middleware&groupId=bairen-test-2&topic=manyou.test&reset=1&fetchMax=50&pollMs=200"
  
  
curl  -H 'fetch-type:long-fetch' \
"http://localhost:8080/hermes-bridge/fetch?consumerId=**************-7-CAAAF&appName=buzz-wacai-middleware&groupId=bairen-test-2&topic=manyou.test&reset=1&fetchMax=50&pollMs=200"  
  
```

### ACK 

```
 curl -X GET \
 http://localhost:8080/hermes-bridge/ack?clusterId=log&consumerId=**************-33626-CAAAA&appName=buzz-wacai-middleware&groupId=bairen-test-1&topic=bairen.test.1&partition=0&offset=63 \
  -H 'content-type: application/json' 
   
```

### 消费组重置

界面上操作**最好先暂停消费，再重置**

通过 redis 操作，生产环境集群 10019

```shell
hset middleware:cache:hermes:offsetMap:qshCommon:link-analysis:link.stt.msg:link.stt.msg.group 0 15229
```

### 删除topic

```properties
curl -XDELETE  -H 'content-type: application/json' http://hermes-center.wse-test-middleware.wke-office.test.wacai.info/hermes-center/topic/delete -d '{"clusterId":"log","topic":"link.stt.msg"}'
```

### 新增topic

```shell
curl -XPOST  -H 'content-type: application/json' http://center.hermes.middleware.k2.wacai.info/hermes-center/topic/addTopic -d '{"clusterId":"drcAggr","topic":"db.edu_user.ucenter_account_noncancellation_record",owner:"bairen",replicas:2}'
```

### 重置offset

```shell
curl "http://localhost:8080/hermes-proxy/offset/reset?appName=biz-app&groupId=manyou.test.group&topic=bairen.test.2000&partition=0&offset=********&lock=true&consumerId=localhost"
```

注意：该API没有校验offset

### 客户端注册

注意：不是心跳

```
curl  'http://center.hermes.middleware.k2.wacai.info/hermes-center/client' --header 'Content-Type: application/json' --data '{
    "appName":"hermes-proxy",
    "ip": "log.proxy.hermes.middleware.k2.wacai.info",
    "port": "80",
    "clientType": "hermes-proxy-complex",
    "status": "ONLINE",
    "clientVersion": "3.0",
    "clusterId": "log"
}'
```



## hermes4 API desgin trick

hermes4 在代码实现中走了一些弯路，这里记录一些。

1、 关于 LifecycleListener 注册放在 Bootstrap 还是 HermesRuntime的问题。最开始是放在HermesRuntime 但后来发现这样实现很别扭，因为需要start()之后才能拿到HermesRuntime实例，而这时已经执行完start()动作了，所以还是放在Bootstrap， 最后的代码效果如下:

```java
  public static void main(String[] args) {
        HttpServerBootstrap bootstrap = new HttpServerBootstrap(new HermesCenterLoader());
        bootstrap.install(
                new CoreModule(),
                new KafkaModule(),
                new AdminModule(),
                new FetchModule(),
                new ProduceModule(),
                new RestModule(),
                new ProxyModule()
        )
                .registerStartListener(new Harmony.RegisterListener())
                .registerStopListener(new Harmony.UnRegisterListener())
                .start();
    }
```

2、harmony通过LifecycleListener接口注册， 强依赖Settings又不方便把 Settings 放在其构造函数中（比如通过SPI初始化），这种场景可以使用Aware接口设计模式，比如RegistryListener 继承SettingsAware，在初始化RegistryListener时候反向set进去

3、谨慎使用静态类，之前为了图简单设计了FixExecutorBuilder、ScalingExecutorBuilder、ScheduledExecutorBuilder 三个静态类用于创建线程池，后来发现创建有部分代码是可以复用的，但是静态类比较难以复用代码，后续修改为实例通过继承方式复用代码。ExecutorBuilder 为基类定义了一些通用方法，然后上述上个类去扩展。

4、 合理抽象。 

在 AbstractScheduler 中 submit()最开始是这么写

```java
  	@Override
    public void submit(Task<Req, Res> task) {
        try {
            doExecute(new TaskHandler(task, this));
        } catch (RejectedExecutionException e) {
            log.error("reject task. request={} , workerPool running={} , cause by {}");
            sendResponse(task);
        }
    }
```

这里的问题是 sendResponse()方法名过于具体，改为 complete() 会好很多。

4、关于 HttpServerBootstrap 的设计

最开始的设计是 BaseBootstrap 实现Bootstrap接口，HttpServerBootstrap 继承BaseBootstrap。

在hermes-proxy 应用中直接通过HttpServerBootstrap启动，比如：

```java
HttpServerBootstrap bootstrap = new HttpServerBootstrap(new HermesCenterLoader());
bootstrap.install().start()
```

​	搞出 HttpServerBootstrap 原本是想可以复用启动逻辑，比如proxy和center都需要启动http，那么可以都使用HttpServerBootstrap。

​	但是当 hermes-proxy 后来需要启动 grpc 时就会显得很麻烦，一个应用的启动逻辑是非常容易发生变化的，上述代码缺乏稳定性，而且 HttpServerBootstrap 也没什么内容，最后废弃掉HttpServerBootstrap， 改成在 HermesProxyBootstrap 中启动 http 和grpc等逻辑。

5、并发问题

```java
protected List<Task<Req, Res>> taskList = Collections.synchronizedList(new ArrayList<>());
```

虽然通过 Collections.synchronizedList添加了同步，但是for循环还是可能会抛出ConcurrentModificationException

4、消费者批量消费

```java
class ConsumeSon implements Runnable{

        @Override
        public void run() {
            KafkaReporter.this.running = true;
            List<Event> events = new ArrayList<Event>(100);

            while (KafkaReporter.this.running) {
                try {
                    Event event = KafkaReporter.this.queue.take();
                    events.add(event);
                    KafkaReporter.this.queue.drainTo(events, 99);

                    KafkaReporter.this.batchSend(events);
                } catch (InterruptedException e) {
                    logError("[WARN] kafka report thread is iterrupted");
                    break;
                } catch (Exception e) {
                    logError("[ERROR] Fail to kafka", e);
                }finally {
                    events.clear();
                }
            }
        }
    }
```



