## 什么是WBS？

在平时的工作中，经常有人会找 PMO 同事请教如何做任务的拆解。那在本期科普知识中，我们重点给大家介绍下项目的任务拆解方法。

在项目管理中，有一个专业名词—WBS ( Work Breakdown Structure ) ，即工作分解结构。在做 WBS 拆分时，有以下几个原则：

### 一、100% 原则

拆分的任务要 100% 的包含所有交付物。例如开发项目，在任务拆解时必须覆盖需求评估、设计、开发、测试和交付五个完整的模块，然后针对不同模块做进一步任务拆解。

### 二、拆分任务时要相互独立且完全穷尽

「相互独立」意味着不重复造轮子。「完全穷尽」才能不误事。例如采购盘子和采购餐具并存，就是不合理的拆分。

### 三、要有合理的工作包大小

项目拆解出来的工作包并非越细越好。每个工作包拆解到一个人可以独立负责，不超过1天的工作量为最佳。



## 案例

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20220105203028515.png" alt="image-20220105203028515" style="zoom:50%;" />