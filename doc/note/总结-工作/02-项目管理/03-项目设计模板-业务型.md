## FaaS 三期服务端技术设计

- 服务搜索
- 服务多 owner 支持
- 服务 QPS 管理
- 指标数据统计
- 节点数据统计
- Kafka topic 申请
- ES 查询模版定义

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/项目管理/2.png" alt="2" style="zoom:80%;" />

## 接口



## 表结构

```sql
CREATE TABLE `mozi_faas_moudle_role` (
  `id` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键',
  `mid` int(11) DEFAULT NULL COMMENT '服务 / 函数 ID',
  `module` int(11) DEFAULT NULL COMMENT '模块, service / function',
  `user` varchar(45) DEFAULT NULL COMMENT '用户名 花名',
  `role` varchar(45) DEFAULT NULL COMMENT '角色 owner',
  `created_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_mozi_faas_moudle_role_mid_module_user` (`mid`,`module`,`user`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='墨子FaaS角色表';
```



## 消息格式

