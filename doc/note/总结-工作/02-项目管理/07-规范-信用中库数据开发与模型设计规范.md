##  前言

### 背景与目的

```
    信用数据团队是一支新成立的年轻团队，集团在八年的发展过程中积累了大量金融信用数据，如何将这些数据挖掘出商业价值是信用数据团队所要承担的重要职责。想要发挥出数据价值有很多应用方式，如报表分析、机器学习、商业智能等，而这一切数据应用的基础都需要有大数据开发技术支持。信用中库作为数据仓库的子集，它在开发模式上以数据集市的形态服务于各类信用产品及应用。
    因为信用应用建设处于初期探索阶段，所以信用中库也没有明确的数据开发和模型设计规范来指导。为了避免信用中库建设过程中出现模型设计不统一、物理化开发规范不明确以及多套数据体系不兼容的风险，现急需撰写一份数据开发与模型设计规范文档来为信用中库保驾护航。
```

### 范围

```
本文档主要应用于信用中库数据模型设计和代码开发维护，所有参与信用数据建设的数据模型师、ETL开发工程师、数据分析师以及算法工程师都务必仔细阅读，确保在数据模型设计、开发、运维过程中落实到位。
```

本文档供包括但不限于以下人员阅读：

1. 数据模型师
2. ETL开发工程师
3. 数据分析师
4. 算法工程师

### 术语

| No   | 术语       | 解释                                                         |
| ---- | ---------- | ------------------------------------------------------------ |
| 1    | 信用中库   | 作为数仓子集，专注于信用业务应用建设，是信用业务主题集市和应集市的最终载体，用来支撑信用部门的用户全景图、风险模型、信用分、信用管理等应用 |
| 2    | 用户全景图 | 信用全景图定义了统一的业务逻辑视图，对上游应用屏蔽数据加工节，提供高度抽象化的应用主题数据服务，是应用主题集市的一个具对象 |
| 3    | BMART      | 业务主题集市，按照具体业务主题划分和加工数据，以雪花型或者星模型为代表的维度建模的方式来组织并存储数据，用来满足特定业务门或者用户需求 |
| 4    | AMART      | 应用集市，是针对应用系统而开发的特定数据集合，主要服务于应用系统前端、算法分析、以及为展现提供数据支持，一般情况下，各应用自己本身并不存储明细数据和事件类历史，只保留汇总数据，数据来源都是从数据服务层的其他几个区域：业务主题集市、数仓公共层；在信用部门体现为用户画像等应用 |

## 数据模型架构

### 数据体系架构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/工作总结/13.png" alt="13" style="zoom:50%;" />

### 数据层级定义

1. ODS Operational Data Store，操作型数据存储层，利用同步工具将业务源系统数据以增量或者全量的方式同步到hive分布式文件系统，它是未经加工的贴源原始数据。相当于数据仓库的一个数据准备区，同时又承担基础数据记录历史变化。
2. DWD Data Warehouse Detail，数据仓库明细层，它是利用数据仓库海量计算能力将分散独立的业务系统数据经过清洗、加工、汇总后以面向主题的方式组织数据。
3. DWS Data Warehouse Service，数据仓库高粒度汇总层，是在明细层基础上按照更高粒度汇总的数据集。
4. ADM Application Data Mart，应用数据集市，为特定业务部门或用户提供直接面向业务应用的数据集。

### 数据中库逻辑架构

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/工作总结/14.png" alt="14" style="zoom:50%;" />

- 核心层
    - 抽象业务主题，按主题组织存放数据；
    - 跨业务整合的业务核心数据；
    - 按照数据内聚性进行预关联；
    - 核心层数据加工过程必须保证指标含义和粒度不发生变化，该层数据全部存放到B层；
- 扩展层
    - 业务主题与核心层保持一致；
    - 经过深度加工产生衍生指标或者数据粒度发生变化的，放到扩展层 S表；
    - 根据雪花模型维度建模思想，构建M层；
- 应用层
    - 各应用自己本身并不存储明细数据和事件类历史，只保留汇总数据；
    - 应用中的数据加工来源都是上游的其他几个数据层级：数据明细层、公共汇总层、主题集市层；
    - 各应用根据不同的需要按优先级从主题集市层到公共数据层之间的各区取数据，但不允许到ODS层直接取数据；
    - 应用层将主要承担业务逻辑视图封装功能，不再对数据做复杂的逻辑加工，主要是从上游数据层级中根据应用需要做数据拼装；

## 信用中库设计规范

### 设计原则

```
    应用集市的使用对象是具备自助获取数据能力但在数据整合能力方面比较欠缺的同学，比如建模、分析、运营同学。线上系统、数据产品(如BO报表、专题分析、营销平台等)所依赖的接口数据，也通过应用集市对接;数据模型设计同学，需要把握中间层建设和应用集市建设的平衡度，将通用的数据整合沉淀在中间层，以支撑更多数据应用。
```

### 命名规范

- 公共字段定义
    - 数据统计日期分区字段
        - 按天分区：dt(YYYY-MM-DD)
        - 按小时分区：hour( 00-23)
        - 按分钟：mi (00-59)
            - {业务}_flag：表示布尔型数据字段。”1”,”0”表示，不允许出现空值域。
    - 编号做为标识符的属性/列，统一命名为“XX号”，后缀为Id，如“会员号user_id”等
    - 日期类型的属性/列，后缀应是Date，如“开户日期open_date”等；时间类型后缀应是Gmt，如“事件发生时间Event_gmt”等
    - 属性/列命名不超过30个字符，应尽量使用简练的英文拼写。个别超长的需要提出来，架构组统一综合考虑
    - 原则上不需要冗余分区字段
- 字段注释规范
    - 字段注释应明确标明字段含义，同时将字段中的枚举值加在字段注释后面
    - 有问题待处理的字段在字段COMMENT里需要标明（待处理）；
    - 枚举值超过10个的字段，或经常变化的枚举值，需将枚举值创建维表；
    - 字段的值做成维表的字段的comment一定要写对应的维表名。
    - 必要时标明数值类字段的单位
- 表命名规范
    - **adm\*{集市域缩写}[\*主题域缩写]\*{自定义表命名标签缩写}\*{刷新周期标识}{单分区增量全量标识}**
    - 刷新周期标识：h(表示小时刷新)、d(表示日刷新)、w(表示周刷新)、m(表示月刷新)、q(表示季度刷新)、y(表示年度刷新)。
    - 单分区增量全量标识：i(表示增量明细)、d(表示全量明细或全量汇总)、s(表示汇总)。
    - 分表规则
        - di后缀，日刷新、每个分区是一份增量明细数据
        - dd后缀，日刷新、每个分区是一份全量明细数据或全量汇总数据
        - ds后缀，日刷新、每个分区是一份增量汇总数据
        - hs后缀，小时刷新、每个分区是一份增量汇总数据
        - ws后缀，周刷新、每个分区是一份增量汇总数据
        - ms后缀，月刷新、每个分区是一份增量汇总数据
        - qs后缀，季度刷新、每个分区是一份增量汇总数据
        - ys后缀，年度刷新、每个分区是一份增量汇总数据

> Example

- adm_loa_rkl_user_risk_info_dd (用户风险信息表，日刷新、每个分区是一份全量明细数据)
- adm_loa_rkl_user_risk_info_dd (用户风险信息表，日刷新、每个分区是一份增量汇总数据)

**注意：** 应用层在命名规范上不做明细数据和汇总数据的区分，原则上明细数据整合下沉到中间层；部分特殊情况，应用层可以进行明细数据整合，使用_di结尾表示明细增量，_dd结尾表示明细全量，但_dd还会表示汇总全量，_ds/hs/ms/ws/ms/ys分表都表示增量，只是刷新周期不一样

- 主题域划分
    - “主题域”是数据模型类面向业务功能应用的概念区分，每个“主题域”由一组面向某类应用的核心“实体/表”及一组辅助“实体/表”构成。
    - “主题”是在较高层次上将企业信息系统中的数据进行综合、归类和分析利用的一个抽象概念，每一个主题基本对应一个宏观的分析领域。在逻辑意义上，它是对应企业中某一宏观分析领域所涉及的分析对象。例如“案件分析”就是一个分析领域，因此这个数据仓库应用的主题就是“案件分析”
- 数据存储及生命周期管理
    - 应用数据根据业务要求进行保存，原则上跟中间层保持一致。