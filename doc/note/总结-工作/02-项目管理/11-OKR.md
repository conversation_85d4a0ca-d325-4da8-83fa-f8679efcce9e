## 2021年

### 海贝

- O1 继续明确、推进和落实集团财富管理生态战略（投教+咨询服务+交易平台）及AMX板块的新战略。

    - KR1  跟进投教板块（钱堂教育）的规划和落地执行进展，每两周Review。重点跟进上规模过程中发现的问题、及需要的支持，早日Break Even（主体资格完善，品牌升级，业务协同等）。
    - KR2 重点跟进投教板块（信策MCN）以及“V+咨询服务模式”的规划和落地执行进展，至少每两周Review。同时重点跟进自孵模式的人员冗余问题
    - KR3 重点跟进投教相关板块（调整目标至只做C端理财师培训，并入海川慧富事业部）的规划和落地执行进展，支持第一期开始试讲。
    - KR4 财富科技板块：和  等共同确定理财师平台战略和目标，组织架构等。以及理清和基金销售、卓跃财富等的协作关系。
    - KR5 协同制订各条线2022战略规划，包括中长期的投顾牌照路线图。
    - KR7 了解、理清AMX各块业务的战略规划、短期策略和当前目标。

- O2 确定“V+投资咨询服务模式”Taskforce Team及商业目标，核算投入情况，发现和疏通跨部门协作存在的问题，推进快速落地执行。

    - KR1 1月底前建议目标116万（前提是保持客户的高满意度）。未来一年目标希望营收达到5000万-1亿。
    - KR2 重点跟进3680基金课和7800投研课产品化探索落地的进展，以及合规问题，确保能成为“咨询服务模式”的未来的成熟产品基础。

- O3 做好人力资源、硬件和架构等基础建设，确保集团稳定发展。

    - KR1 人才与创业状态：每月Review公司CORP，以及各主要业务线的Tatent Review和创业状态（创业程度：效率、投入与产出、投入度、财务/技术等信息）
    - KR2 软硬件：Review公司硬件投入和系统架构更新进展，确保能支持业务发展、稳定和费用节省。每两周开会Review一次。

- O4 建立并提升挖财品牌、咨询顾问形象和影响力。重点发展挖财公众号。

    - KR1 确定挖财品牌架构基本规则文档（挖财、钱堂教育、信策文化、记账、挖财基金、卓跃财富、海川慧富（含财智研学）、漫财等）
    - KR2 建立公众号相应的坚持更新机制。推动挖财公众号持续产出高质量咨询服务内容。招聘新人，参与面试

- O5 开始建立和加强二级市场基金FOF等投研能力。

    

### 和氏璧

- O1 非货规模提升：推动热点销售，探索机构、团队或KOL合作新模式，不断探索新的基金营销方式，改进APP体验，新增非货规模超过1亿。   @升登
    - KR1 热点销售：每月至少挖掘1个销售热点，两月总销量超1000万;
    - KR2 定投推广：持续推进定投推广，考虑引入定投型投顾策略，月定投发生额超到3500万;
    - KR3 外部合作：持续探索机构、团队以及KOL的合作模式，带入销量超过2000万;
    - KR4 体验改进：通过不断改进APP体验、营销模式，增加各类可销售品种，月交易规模超过7000万;
    - KR5 卓跃合作：探索与卓跃团队的公募合作销售模式，通过热点、封闭、新发、存单指数等基金产品合作，叠加优质存款产品，累计销量超过500万;
- O2 新客获取：深度加强财商合作，将导流范围覆盖到更大的财商客群，优化自身新客体验，累计新客数量超过3500人。 @升登
    - KR1 财商合作：与财商课程体系深度结合，进一步提升财商开户转化率，覆盖面扩展至B团、C团、财富圈等全域客群，2个月财商累计导流新客人数超过;
    - KR2 记账合作：创新合作模式，主推零钱类产品，加强场景合作，月度新增客户数超过200人;
- O3  投顾升级：引入3家外部投顾平台投顾产品，丰富投顾策略，测试销售体验，累计销量超过1000万；同时完成核心组合的策略迁移工作;
    - KR1 官方投顾产品引入：至少完成嘉实、鹏华等三家平台的投顾产品引入，策略满足固收+、股债平衡、绝对收益、定投等多种类需求，测试销售转化和后期服务模式，累计销量超过1000万;
    - KR2 组合升级：积极接触投顾平台，寻找组合升级合作伙伴，制定升级方案，完成存量主要的组合的升级（风火轮、太极图、基米系列、好基汇、独孤九剑、基少成多、博财成长、生息宝PLUS、基米打新一号、南神系列、聚宝盆策略迁移上线）

### 百川

- O1 完成2022年业务规划、业务规则、业务管理  @胖大海
    - KR1 业务规划：12月完成2022年业务规划
    - KR2 业务管理：各部门负责人到位后，1月份整体完成平台各业务规则、管理流程、制度办法
    - KR3 业务工具：12月完成公司基础市场工具（公司介绍、平台服务操作手册
    - KR4 业务协议：12月完成平台、IFA/家办、交易主体三方的所有相关协议（法律、财务）
    - KR5 生命周期管理：1月份完成海川平台产品初审、评审、风控、上线、发行、存续 管理流程及相关管理制度。（预期将涉及到交易持牌主体、财富科技层面、集团层面 
- O2 完成12月-1月的海川平台 团队建设目标、人事制度建设 @七妙
    - KR1 组织建设：12月、1月 按照人力计划完成团队招聘。各部门总或核心总监岗、业务流程及管理关键岗（关键岗位>80%），机构拓展团队、金融产品团队、家族中心团队是建设重点。
    - KR2 绩效管理：完成入职岗位考核规则和目标设定。完成薪酬及绩效考核相关制度。
- O3 完成平台研发第一期（0.5版本 alpha版本）上线
    - KR1 系统上线：12月实现0.5版本 alpha版本基础商铺支持soft lanuch
    - KR2 系统优化：1月优化并扩充0.5版本功能，目标1.0版本功能（细化开发进度后，更新项目管理目标）。
- O4 完成12月-1月的业务目标
    - KR1 用户量：12月份实现IFA/家办签约突破，1月份完成10个IFA/家办机构签约入驻。
    - KR2 业务量：1月份完成IFA/家办业务量突破
    - KR3 产品上线：12月份确定平台上线产品（证券、股权，考虑爆款策略），并完成上线产品的发行准备。
    - KR4 转化赋能建设：1月份实现家族中心/IC 业务转化赋能的雏形能力输出（产品投顾、方案制定、考虑设立家族研究院-培训-咨询-转化）
    - KR5 市场推广：1月份实现平台公众号的启动及内容运营

### 胖大海

- O1 公募业务非货规模提升（保障新增非货规模超过1亿）
    - KR1 同投研、产品、UI、运营共同讨论建设投研品牌专区建设方案讨论，并确保1月能在部门内部通过方案（方案包括：首页改版、专区建设、配套详情页氛围支持、后续持仓的投研服务支持），并启动研发；根据方案启动专区研发工作，并可以保障整体项目在1季度内完成上线；
    - KR2 1月内配合产品、运营完成各体验增强建设研发上线工作（包括：自选增强、单基金详情页改版1期-无专区增强氛围、持仓首页分析1期-月度/季度收益分析、首页改版-工具化输出）;
    - KR3 1月内完成其他功能点增强：定投专区增强、产品衍生数据计算、五星基金数据计算、机构支持基金线上交易;
- O2 投顾切换（系统保障具备接入多家机构能力；完成核心策略迁移；）@惊帆
    - KR1 官方投顾产品引入：至少完成三家平台的投顾产品引入，需要同时支持恒生版本以及协会方案2版本。
    - KR2 自有组合升级：积极接触投顾平台，寻找组合升级合作伙伴，配合产品共同制定升级方案，至少完成3款存量主要的组合的升级。
- O3 海川慧富系统分批迭代合规上线
    - KR1: 12月底保障海川慧富系统0.5期版本上线；功能具体包括：1）投资人端家办展示页，功能包括家办品牌展示，产品展示，产品预约发起，客户持仓查看；2）私募基金线上合规交易能力，包括开户、认证、电签、双录等，以及与IFA协同作业的基础作业后台能力（主要为作业状态推进的状态展示、通知、运营审核等能力）
    - KR2: 1月明确海川慧富系统1.0版本采购或自研方案，和页面共同完成需求细节沟通，并启动迭代研发工作；确保项目可以阶段提交上线，整体预期3月底完成1.0版本核心功能上线。
- O4 推进完成上挖系统性安全及合规建设
    - KR1 2022年度完成国密具体事项调研工作，发起项目执行落地方案，推进项目最终逐步落地。具体实施计划参考基金代销同业，以合适成本完成证监会的政治任务安排。同时推进上挖系统性密码安全研发建设评审工作。
    - KR2 2022年1月内完成网站等保2.0的二级事项推进，以及基金交易系统等保2.0的三级评审及续签事项。确保上挖等保合规事项推进。
    - KR3 2022年1季度内完成数据安全事项系统建设工作，包括加密存储、日志加密后续完善工作，以及hermes鉴权、dubbo鉴权、redis鉴权、domino鉴权相关开发上线工作
    - KR4 2022年内完成上挖物理隔离部署事项。1季度内完成方案梳理；2-3季度逐步开发上线；4季度保障核心系统完成隔离部署
- O5 推进业务系统完善建设工作 
    - KR1 建设交易系统业务闭环检查机制，保障交易系统稳健稳定稳固；1）2022年2季度内完成后督系统开发工作；1季度内完成系统模型分析、改造方案及系统设计工作；5-6月完成雏形开发测试上线工作；2）2022年6月前完成清算流程改造，确保所有回退功能正常；3）2022年6月前建设fog备份方案落地；4）2022年1季度内完成中后台融合全部开发工作并能上线；1月内完成交易部分合并改造；2月内完成持仓部分合并改造；3月内完成账户订单改造及收单下线；
    - KR2 季度内独立货架体系，可以同时支持多主体、多层次业务地货架体系；
    - KR3 1月内完成BIU迁移墨子工作，并能持续支持新增运营业务发展的工具需求；

### Huanjing

- O1 **持续促进**研发产能提效，提升技术架构水平，完善新业务架构解决方案输出，满足新业务应用轻量化，可弹性化，快速迭代需求
    - KR1 基础技术整体轻量级解决方案框架打造，以tamp为样板，提供轻量级，弹性的中间件，中台技术能力，本双月能实现轻量级用户中心，轻量级网关上线。
    - KR2 低码搭建产品化升级，从操作和链路上**同时提高效率**，显著**降低应用研发门槛**，中后台高频物料新增超过30，能覆盖海川汇富，财商，财富等业务中后台系统70%的增量业务场景;
    - KR3 完成wke测试集群下线，老挖财云服务迁移新云平台超过70%，容器服务存储平台达到生产标准;
    - KR4 着手建立**研发效能度量体系**，以效能维度评估技术团队的创业状态;
- O2 持续完善安全运营体系建设，确保不发生重大安全事件
    - 完成全面的数据安全管理评估，查漏补缺，合规化水平达到70分水平;
    - 持续提升研发过程安全治理水平，提升**代码静态化扫描覆盖率**及性能;
    - 提升DLP内部数据安全管理水平，根据业务形态变化升级防护策略，全员工安装率达到100%，确保不出现重大数据泄露事件;
- O3 夯实技术底座，完善技术保障措施，提供稳定的技术保障能力
    - KR1 持续**提升财商直播稳定性**，完成直播专线建设使用，保障sla高于99.99%，**全面推广srt推流协议**;
    - KR2 **持续完善监控体系**建设，本双月完成**事件中心二期**上线，监控告警准确率较老系统提升30%以上，至少完成10个系统接入;



## 2022年 2-3 月

O1 标准化对接流程、加强平台整合、推进老旧系统下线，打造轻量级技术中台（PaaS）

- 应用度量架构和产品设计方案通过评审，本双月实现测试环境调用链路可视化
- K8s 作业管理（Job/CronJob）平台化，本双月完成方案调研、平台和一期功能开发、测试环境上线；
- 整合已有多种存储平台或组件（Hermes、Sailfish、小文件、DRC 等）为大存储平台，统一门户，本双月实现相关控制台页面核心功能迁移；
- 保障 Linkup 稳定性，本双月推进 SBC 设备迁移至 4 段，完成服务迁移至生产的演练；
- 老旧系统下线：（1）obelisk3 打包应用迁移至云平台，云平台构建比例从 85% 提升至 95%；（2）挖财云生产迁移：应用迁移 20%（300 个）（3）告警任务迁移 20%（400 个）

O2 打造满足合规需求的轻量级用户中心，可私有化部署 

- 完成海川慧富 0.8 版本需求：技术方案设计评审，功能开发、上线
- 老用户中心迁移： （1）user-centre 功能全部迁移完成，通过测试，新接口上线 （2）数字财富、基金依赖 dubbo 接口迁移完成
- 平台基建：完成平台功能配置-业务应用管理模块拆分开发、通过测试

O3 打造一站式智能运营平台，可 SaaS 化对外输出

- 微信公众号模板优化升级和高级群发功能上线，提升微信公众号运营能力；
- 完成内容中台（内容运营）需求调研、产出 PRD。确定一期开发内容，完成一期上线
- 营销任务奖励和邀请好友 PRD 和技术方案产出，满足数字财富 318 需求

## 2022年 4-5 月

O1 加强平台整合，打造轻量级技术中台（PaaS）

- 产出服务治理整体 roadmap，确定关键里程碑，本双月完成应用度量和服务治理服务端开发和测试，平台侧核心功能跑通、可内测 
- 大存储平台（sailfish、hermes、domino、legion、drc）整合二期（功能全覆盖、监控报表可视化、达到生产化标准）设计、开发完成，与云平台整合到一起、测试环境上线，推进老系统下
- K8s HPA 平台化支持，完成调研，找到合适挖财当前业务的 HPA 策略，与容器服务平台融合，测试环境上线
- 加强事件中心问题定位能力，平台提供基于 IP、花名、应用名称三种维度的定位方法，可以串联应用、中间件、平台、运维等不同层面事件

O2 打造一站式智能运营平台，可 SaaS 化对外输出

- 内容运营一期（侧重自建内容生产、内容管理和质检、按场景分发）需求评审通过，研测完成上线使用
- 完成邀友助力抽奖后续迭代（功能优化、平台体验提升），交易成功页活动落地，交付数字财富运营使用 
- 持续提升触达系统（Linkup + 烽火台）稳定性，更多场景覆盖监控告警，保障业务 SLA 不低于 99.99%

O3 打造满足合规需求的轻量级用户中心，可私有化部署

- 完成 TAMP 1.0 需求（1）APP 侧用户中心接口迁移 （2）打通小鹅通账号体系：功能研发上线，不阻塞海川业务上线
- 梳理业务账号拆分方案、产出文档、内部评审通过，至少与一个业务方沟通，达成未来迁移共识；同时保证至少两周发送一次轻量级用户中心项目整体进展给业务方
- 财商和基金所依赖的 HTTP 接口和 Kafka 消息队列迁移完成

## 2022年 6-7 月

O1 加强老旧系统整合，冗余平台下线，打造轻量级技术中台（PaaS）

- 应用度量侧重链路、依赖部分研发完成，保证数据准确性，服务稳定性，与效能联动测试环境上线，覆盖 50% Java 应用；服务注册和发现后台功能开发完成，产品化设计评审通过
- 通用存储平台二期（sailfish、hermes、domino、legion、drc）全监控大盘覆盖，公司技术推广至少一次，收集反馈，持续迭代 1 月后，7 月底上生产并启动老旧系统下线规划
- 结合容器资源使用率监控，推进自动化扩缩容（HPA）功能在测试环境启动，过程中进一步优化平台体验，测试环境 K8s 整体资源 request 降低至 60%；
- CMDB 2.0 项目启动，在已有 1.0 功能基础上：覆盖 IT 资产管理，与云平台元数据和平台其他服务更好融合保持统一技术、产品体系。本双月完成需求调研和技术方案设计评审；

O2 打造一站式智能运营平台，可 SaaS 化对各业务输出

- 基于内容一期重新挖掘现有业务内容运营需求，平台自身功能（丰富：内容形式、内容管理、用户权限、数据统计等）增强，产出内容二期。本月实现内容二期上线，持续支撑（1）海川内容后续迭代（2）V+ 业务早、中、晚报和连续性标的输出需求
- 海川 MGM 需求对接，trident 平台前后端上线，后续业务活动配置支持
- 后台数据稳定性提升：资源位数据从 HBase 迁移至 TiDB、离线用户群全部切换完成；实时数据持续与数据团队沟通，本双月方案确定产出排期，启动开发；
- 持续提升 Linkup、little-boy 稳定性，更多场景覆盖监控告警，保障业务 SLA 不低于 99.99%；

O3 打造满足合规需求的轻量级用户中心，可私有化部署

- 持续跟进、满足 TAMP 账户需求（微信、小鹅通、App、MGM、新版本等），不阻塞海川版本上线
- 轻量级用户中心测试、生产部署，数据对接老用户中心，完成新老平台、接口切换充分测试，解决缓存、授权等问题，并完成内部后台系统对接新平台；
- 完善业务账号拆分方案，至少通过 1 个业务侧评审；

## 2022年 8-9 月

O1 加强老旧系统整合，冗余平台下线，打造轻量级技术中台（PaaS） 

- 应用度量完善异常监控、JVM 监控，提供告警能力，测试环境全面开启，生产上线灰度开启；注册中心服务契约、动态路由开发完毕、测试环境上线，数据准确性验证通过
- WKE 容器服务细粒度资源利用率统计平台化（各业务可直观查看资源占用情况）：针对用户（空间）和管理员（BOSS）不同视角设计功能，本双月 WKE 测试、xamc、望润三个环境上线，同步给业务
- 梳理云平台、中间件、运营触达、用户中心所有系统单元各环境地址、当前状态（待下线、持续维护、重点迭代）、使用业务方、人力占用、物理资源占用情况（物理机、虚拟机、容器，数量和规格），产出统计文档
- 老旧系统推进迁移和下线：当前 150 个老旧应用（占用 485 个虚拟机）：① 确定无流量下线 ② 有流量应用容器化 ③ 无法容器化也无法下线（会有较大成本）做好文档记录，9 月底完成至少 50%

O2 打造一站式智能运营平台，语音和短信能力持续加强

- Linkup 业务线新增机构（与 Goblin 结构一致）、结算账号管理，实现质检开关可配置、账号结算线上化，本双月完成产品设计、平台开发上线，结算数据与实际线下一致
- 运营平台内容能力拓展（公众号打通、支持音视频、规则集合等功能版本上线），持续跟进挖财宝、TAMP、V+ 等业务运营需求
- 持续加强 Linkup 稳定性，除网络、线路等外部因素外，Linkup 自身故障率为 0，本双月彻底解决电话条巡航被 WAF 拦截导致不可用问题
- Linkup 和 little-boy 私有化改造完成，通过网络隔离验证，部署文档完善，可交付业务 

O3 打造满足合规需求的轻量级用户中心，探索 ToB 场景下用户需

- 持续跟进 TAMP、财商、V+、大众财富等用户中心、合规等需求，配合业务产品迭代，不阻塞业务版本
- 推进新老用户中心迁移迁移，本双月完成生产上线，H5 授权绑定、登录态隔离、数据拆分同步等能力完善

## 2022年 10-11 月

O1 夯实技术底座，完善技术保障措施，提供稳定的技术保障能力；持续推动业务服务下线，节省运营成本；完善安全运营体系建设，确保不发生重大安全事件

- 持续降本，推动业务应用下线，本双月进行相关服务器相关应用及数据合并迁移下线，完成5个机柜退租目标，降低运营成本
- 保障主要业务sla高于99.99%，防范人员变动及应用数据下线迁移带来的稳定性风险
- 持续迭代IAST/SAST安全运营体系，完成安全运营管理平台一期上线，开启二期迭代

O2 持续促进研发产能提效，提升技术架构水平，完善新业务架构解决方案输出，满足新业务应用轻量化，可弹性化，快速迭代需求；构建并迭代新一代轻量级微服务治理平台，整体组件数量和占用资源降低至老治理平台的 1/3，功能上满足业务独立部署时对微服务治理组件的需求，持续整合打造新技术中台

- 持续构建并迭代新技术中台，应用度量新增服务异常告警能力，整体生产环境灰度上线，目标灰度 80% 左右；同时梳理 北斗平台各依赖组件，启动 北斗平台下线；容器服务管理员视角资源管理功能完成，空间层网络隔离平台化方案设计完成；CMDB 2.0 一期整体上线，IT 笔记本设备线上管理；
- 持续构建并迭代一站式智能运营中台，trident 内容运营中台新增规则内容集合、完成营销计划、详情页、福利模板等老工程迁移；Linkup 平台电话条巡航由 HTTP 改为 Websocket 灰度方案完成、通话转接功能整体上线，交付业务上线使用；结算账号统计（关联拨打记录）功能上线，与供应商结算结果基本一致；

O3 持迭代墨子前端可视化搭建中台，实现企业级低码/无码搭建生态，完成墨子SaaS化改造输出，对外影响力建设输出，尝试跑通商业变现路径

- 墨子SaaS化建设，完成基础服务架构提效改造，开放FaaS、投放链路产品能力，增加组件全生命周期管理、低代码离线项目镜像输出能力，输出权限方案设计、站点文档中心，对外提供商业化最小可用版本
- 灵活高效支撑AMX、海川、平台业务；深入AMX业务，牵头梳理定义业务模块，达成业务共识，推动完成技术实施，通过模块化选配组装、支持模块依赖项可插拔、价值数据指标可监控、DevOps支持模块构建部署，完成资管SaaS模块选装能力的底层设施建设
- 扩大墨子行业影响力，输出宣传文稿3篇，参加上海互金协会科创大赛输出影响力，商业化推广达成合作意向1家

## 2022年大团队

O1 基础技术：持续提升技术架构治理水平，完善新业务架构解决方案输出，满足新业务应用轻量化，可弹性化，快速迭代需求；构建并迭代新一代轻量级微服务治理平台，降低整体资源占用，功能上满足业务独立部署时对微服务治理组件的需求；构建并迭代一站式运营中台，完善业务运营上下游生态，提升用户触达能力；

- KR1: 持续构建并迭代轻量级应用度量平台，高效支撑AMX、海川业务应用架构梳理，强弱依赖关系分析，容量评估，线上问题排查；构建新一代轻量化通用存储平台，减少ES，消息，小文件等存储服务接入和使用成本，提升研发效能。

O2 前端技术：持迭代墨子前端可视化搭建中台，实现企业级低码/无码搭建生态，统一横向业务哈雷前端框架，推进墨子FaaS的中后台场景覆盖，进一步扩大墨子搭建在多样化业务中的定制化破局赋能范围，跑通商业变现路径；

- KR1:灵活高效支撑AMX、海川、平台业务;深入AMX业务，牵头梳理定义业务模块，达成业务共识，推动完成技术实施，通过模块化选配组装、支持模块依赖项可插拔、价值数据指标可监控、DevOps支持模块构建部署，完成资管SaaS模块选装能力的
  底层设施建设;落地业务实现模块化交付
- KR2:标准化低码流程在AMX、基础平台、数据业务持续落地，达成研发增效30%(需求承接量)目标:升级哈雷框架架构,内置
  墨子生态，开放菜单、微前端业务定制能力，抹平海川、财商等业务接入的技术栈差异，缩短资管、云平台微前端系统的构建耗时50%，多系统公共模块研发效率提升3倍以上
- KR3:墨子SaaS化改造输出，PaaS能力补齐行业50%标准，对外影响力建设，探索商业变现路径

O3 基础运维：夯实技术底座，提升技术保障措施，提供稳定的技术保障能力；持续降本增效，节省运营成本；完善安全运营体系建设，防范于未然

## 2023 年中间件

### 4月~5月

上双月成果
1、hermes代理层架构升级上线生产环境，整体减少50%资源消耗，ingress流量减少60~70%，切换过程业务无感知，未产生故障
2、应用度量完成公司范围内推广和ppt宣讲
3、研发效能发布流完成AMX冒烟&回归测试集成到发布系统和ppt宣讲

本双月计划
1、hermes 代理层持续迭代，完善功能，提升系统稳定性和易用性
2、应用度量完成agent后台管理功能开发，提升agent的维护能力
3、研发效能完善项目初始化流程，提供通用的一站式新建项目功能，规范化业务线新建项目，提高研发效率

### 6月~7月

上双月成果：
1、应用度量持续迭代，支持大数据、前端应用监控需求，上线应用性能大盘，整合主机和应用监控
2、hermes proxy架构升级，完成相关功能开发和性能测试，测试环境上线验证
3、研发效能发布流：完成AMC冒烟&回归测试集成到发布系统，外部宣讲PPT准备


本双月计划
1、应用度量持续迭代，完善产品功能，调研探索nginx和业务接入链路监控，完成外部推广和宣讲
2、hermes proxy架构升级，项目上线生产环境，减少50%以上资源消耗
3、研发效能，完成初始化新项目自动化开发，外部宣讲收集用户需求，持续优化和迭代发布流

### 8月~9月

上双月成果
1、hermes代理层架构升级上线生产环境，整体减少50%资源消耗，ingress流量减少60~70%，切换过程业务无感知，未产生故障
2、应用度量完成公司范围内推广和ppt宣讲
3、研发效能发布流完成AMX冒烟&回归测试集成到发布系统和ppt宣讲

本双月计划
1、hermes 代理层持续迭代，完善功能，提升系统稳定性和易用性
2、应用度量完成agent后台管理功能开发，提升agent的维护能力
3、研发效能完善项目初始化流程，提供通用的一站式新建项目功能，规范化业务线新建项目，提高研发效率

### 10月~11月

上双月成果
1、完成agent后台管理功能一期开发，结束之前agent发布需要通过脚本人肉发布的历史，提升quantum agent的稳定性；
2、hermes代理层持续迭代、修复控制台多个bug，提升用户体验；
3、研发效能项目初始化发布上线，提供通用的一站式新建项目功能，规范化业务线新建项目，提高研发效率；
4、基础镜像项目开发并上线，解决之前基础镜像缺少维护性的问题；

本双月计划
1、在线诊断平台立项并开始设计和开发工作，解决业务同学排查定位问题最后一公里的问题；
2、hermes代理层多语言客户端建设，解决node等其他语言接入kafka难维护、缺少监控的痛点；
3、调研持久化kv存储方案，解决财科基金业务线目前redis集群内存消耗持续增长，已超过200G存在瓶颈的痛点；
4、研发效能在线CodeReview项目和Sonar集成启动；

### 12月~1月

上双月成果

1. 在线诊断平台一期Agent和服务端接口基本开发完成，目前等待前端资源对接中；
2. Hermes代理层node客户端完成对接和上线，在光子智库项目中落地，上线之后稳定运行；
3. 持久化KV存储确定Apache Kvrocks方案，测试环境已搭建一套环境提供给财科使用；
4. 发布系统集成Soanr+AI开发后端开发基本完成，目前前端资源对接中；

本双月计划

1. 在线诊断平台一期完成前后端对接工作并上线，解决业务同学排查定位问题最后一公里；
2. 完成Sailfish支持Kvrocks自动化运维的开发并上线，Kvrocks上生产环境，解决财科基金业务线目前Redis集群内存消耗持续增长(已超过200G)；
3. 完成在线CodeReview项目和Soanr+AI项目的前后端对接并上线，基于知识库检查代码缺陷并给出修复建议，提升研发质量；



## 2024年中间件

### 2月~3月

上双月成果
1、在线诊断平台一期完成前后端对接工作并在测试环境中上线，测试环境已全量覆盖。
2、Sailfish支持Kvrocks自动化运维持续迭代，完成水平和垂直扩容，提升运维能力。
3、在线CodeReview项目和Soanr+AI项目，完成前后端对接并上线。

本双月计划
1、在线诊断平台二期规划和技术方案确定，完成代码热更新，线程级CPU性能分析。
2、Sailfish支持Kvrocks，完成节点故障自动重启功能，提升稳定性。
3、在线CodeReview和Soanr+AI项目推广，发布流用户体验提升。

### 4月~5月

上双月成果
1. 在线诊断平台二期代码热更新、线程级CPU性能分析开发完成，已发布上线
2. Sailfish支持Kvrocks开发完成，Hermes已切换到Kvrocks
3. 发布流用户体验提升开发完成，已通过邮件、群消息方式进行推广


本双月计划
1. 中间件健康看板(status page)建设，提供es、redis、kafka、zk等中间件统一健康检查能力，提升各个环境中间件稳定性
2. Wacai-boot sdk 和 Hermes sdk建设，这两类sdk已2年多未迭代，这次会解决一系列历史遗留问题，完善对Spring-boot2.5、jdk11的支持
3. 面向研发测的AI工具规划和方案设计，提升业务同学开发效率，通过内部知识库+大模型，减少人工答疑成本

### 6月~7月

上双月成果

1. 中间件健康看板(status page)开发完成并顺利上线：轻量化地实现了es、redis、kafka、zk等中间件统一健康检查能力，提升各私有化环境下中间件的稳定性
2. 中间件SDK建设：WacaiBoot和Hermes新版客户端发布，WacaiBoot支持新版SpringBoot2.5，支持jdk8、11、17，去除Ninjia等历史包袱；Hermes修复多个历史bug，优化SpringBoot注解的支持，降低用户接入成本
3. AI研发效能工具调研：重点CodeFuse、SWE-Agent等产品，规划了三个AI相关产品，下个周期会进入开发阶段
4. 新创建项目中间件支持工作：中间件答疑，Dubbo私有化环境改造等

本双月计划

1. 服务治理功能持续迭代：重点提升应用度量和在线诊断的用户体验、解决上一周期用户反馈的一些问题，包括命令太长记不住、链路跟踪中循环调用链路太长等问题
2. ES版本升级计划：目前挖财使用的ES 落后社区6年，性能和功能严重落后，存在可用性问题，本周期会尝试对ES集群进行升级，提升ES的性能和维护性
3. 挖财内部ChatBot开发：基于知识库+LLM打造挖财内部聊天机器人，减少内部技术团队答疑成本，提升研发效率

### 8月~9月

上双月成果

1. 服务治理功能持续迭代：完成指令模板功能开发，对常用问题诊断命令比如watch、trace、jad只需点击页面进行一键诊断，无需记住复杂的命令参数，功能已上线；
2. ES版本升级：调研ES7和ES8版本，并做了兼容性测试和修改，已完成测试环境日志集群的升级；
3. 挖财内部ChatBot开发：经过调研最终选定Dify作为Chatbot底座进行开发，目前基于Dify进行本地化改造，进度40%；

本双月计划

1. 代码分析平台建设，提供在线代码分析，帮助业务方快速梳理应用内服务依赖关系，代码实时覆盖率，用于老旧代码下线、代码修改影响范围评估等。
2.  ES版本升级继续推进：完成生产环境日志集群ES7的透明升级，对业务方无任何影响
3. 挖财内部ChatBot开发：完成第一个版本的上线



### 12月~1月

本双月成果

1. 中间件产品迭代：完成Hermes广播消息、轻量化网关安全增强、文件网关视频在线播放、基金安全合规等需求，支撑业务发展；
2. 降本增效相关：通过ES调优，ES生产环境下线两台物理机，优化基金日志保存策略，节约磁盘存储空间30%+，发布系统通过优化打包策略，前端wax应用构建速度提升40%+；
3. AI探索： 云平台研发首页改版开发进度65%，完成代办事宜，年度绩效日历等功能开发，热门技术资讯开发中；


本双月计划
1. 中间件产品迭代，继续优化中间件现有产品，更好的支撑业务发展；
2. 完成云平台研发首页所有开发工作，项目发布上线；
3. 挖财辅助编程IDEA插件wapilot项目完成前期技术调研和功能设计，进入开发阶段，取得阶段性成果；



## 2025年中间件

### 2月~3月

上双月成果
1. 云平台研发首页改版完成并上线，完成代办事宜，年度绩效日历，热门技术咨询
2. 挖财辅助编程IDEA插件wapilot完成基础底座开发，支持大模型智能聊天问答，单元测试代码自动生成，Api测试自动生成，进度50%
3. 钱塘用户中心拆分项目drc双向同步支持， 在线诊断平台上线控制台


本双月计划
1. 挖财辅助编程IDEA插件wapilot功能开发，完成v1版本，并在业务团队进行试用和推广
2. 研发效能系统建设：优化发布流页面布局，提升操作体验，增加发布流应用实时日志功能，探索基于AIGC生成测试用例
3. 服务治理功能迭代：设计和开发异步链路跟踪方案，支持业务id(如会员号，手机号)接入链路查询

### 3月~4月

上双月成果
1. 挖财辅助编程IDEA插件wapilot功能开发，完成第一个版本，支持智能问答、代码解释、代码优化、生成API测试，生成单元测试，自定义上下文，并在用户中心、AMX、记账等团队中小范围试用;
2. 研发效能系统建设，完善迭代模式下的用户体验，新增应用部署实时日志查看功能，解决应用发布过程遇到容器启动失败无法查看日志的痛点，支持挖财云容器平台升级;
3. 服务治理，设计和开发异步链路跟踪方案，支持业务id(如会员号，手机号)接入链路查询，已完成client端的开发;


本双月计划
1. 结合用户反馈规划wapilot二期需求和相关技术调研，进入wapilot agent功能开发，目标实现类似cursor composer效果;
2. 继续完善研发效能功能和用户体验，完成基于AIGC生成测试用例功能开发;
3. 完成全息排查，即支持业务id(如会员号，手机号)接入链路查询的前后端所有功能开发和发布上线，并在业务方中推广;

### 5月~6月

上双月成果
1. 完成wapilot二期的项目规划和相关技术调研，wapilot agent功能基本完成，具备使用本地工具实现用户需求能力，同时在公司范围内分享，整体满意度4.8分(满分5分)
2. 服务治理支持异步链路跟踪，完成前后端功能开发并上线，支持业务id(如会员号，手机号)接入链路查询，已接入中间件轻量化网关、domino等产品
3. 研发效能完善发布系统用户体验，新增云平台统一搜索入口，支持项目一键触达构建；AIGC生成测试用例持续推进，已完成通过AI对话流式生成测试用例树，产品细节打磨中
4. ldpa升级更新，支持HR和OA系统升级


下双月计划
1. wapilot agent版本正式发布，在业务团队中推广，收集用户反馈，规划MCP、多模态等功能和使用场景
2. 中间件持续迭代，应用度量支持增加主动告警能力：1. 应用内存告警时自动dump并上传内存文件；2. 应用发布后当发现阈值和上个周期出现明显变化触发主动告警，防止业务接口劣化
3. 研发效能持续完善，上线AIGC生成测试用例平台，在业务方推广



## 2025年平台技术

### 中间件和云平台

#### **总结**

##### 项目1 完善服务治理能力

**背景**：

​    ● 链路跟踪在业务异步调用场景下信息丢失

​    ● 链路跟踪只能通过traceId查询，无法通过业务属性(比如会员号、手机号)查询链路

​    ● 对于JVM OOM缺少分析手段，容器crash之后dump文件无法找回

**结果：**

​      ✓ 提供SDK支持**业务字段接**入链路和**异步链路**

​      ✓ 已在 domino-fog、ldap、 growth-external-starter等项目中落地

​      ✓ 新增内存文件分析功能，提供一站式heap dump，在线分析，下载功能

##### 项目2 打造挖财ai coding工具wapilot

**背景**：

​        ● 2025年ai coding行业爆发式发展，公司内大家对ai coding的理解参差不齐 

​        ● 国外优秀的ai coding都需要收费，增加了使用门槛

​        ● 让大家低成本的使用ai coding工具并且能结合挖财业务

**结果：**

​          ✓ wapilot 发布到 IDEA 插件市场

​          ✓ 支持智能问答、代码生成、任务规划、环境感知能力

​          ✓ 具备独立完成需求能力

##### 项目 3 研发效能产品提升

**背景**：

-  研发效能缺少一个体现效能的首页

- 缺少利用 AI 解决问题的场景

- 发布系统还不够完善

**结果：**

- 研发效能新增首页包括待办事宜、个人研效日历、技术资讯
- 探索 聊天 AI 机器人，基于 dify 接入内部工具具备独立完成需求能力 新增发布期间应用日志记录，避免因为容器启动失败后无法看到日志的问题

#### **规划**

##### 规划 1 wapilot agent 能力增强

**背景：**

​      ● 目前生成的代码偶尔还是会出现幻觉，比如调用不存在的方法和属性，返回格式不对导致渲染失败 

​      ● 缺少本地代码库索引导致不会使用已封装好的类

​      ● 对于二方包中的代码无法搜索代码

目标：

​      ● 提升生成的代码质量，减少幻觉的产生 

​      ● 实现流程可配置化，第一步先实现工单审批类流程上线

​      ● 可以自动学会使用挖财内部库比如中间件，用户中心

**实施路径：**

​      ● 完善提示词和LLM输出规范化、利用编译hint信息对结果进行检查

​      ● 利用AST+embedding+向量数据库+对代码进行索引，建立中心化的codebase，提升agent代码感知能力

​      ● 建立二方包知识库发布机制，通过MCP方式让agent能学会使用挖财内部库

##### 规划 2 利用尾部采样优化采样率

**背景：**

​        ● 链路采样比较死板，只支持按照固定比例采样，链路尾段出现异常，但头部被采样会导致链路不完整 

​        ● 插件支持还不够完善，比如不支持lighting

​        ● 监控指标方面，缺少对 dubbo 线程池、数据库连接池的监控

**目标：**

​        ● 设计一种新的链路采样思路，解决固定采样链路中出现尾段链路异常导致数据不完整的问题 

​        ● 完善插件支持和监控指标

**实施路径：**

​          ● 采用基于尾部采样的方案，当链路结束才决定是否采样，解决链路不完整的痛点

​          ● 增加对lighting中间件的链路跟踪支持

​          ● 增加对 dubbo 线程池、数据库连接池的监控

#####  规划 3 小文件存储改造

**背景：**

​          ● domino在设计之初只针对小文件，缺少对大文件的支持，随着业务发展也出现了存储大文件的需求 

​          ● 对于一些热点文件最好能提供一个缓存，减少对ceph的压力

​          ● 监控指标方面，缺少对 dubbo 线程池、数据库连接池的监控

​          ● 一些遗留应用还部署在虚拟机，可以清理并下线

**目标：**

​          ● 针对大文件上传下载进行优化，减少对系统的资源消耗 

​          ● 完善 domino 监控指标，增加监控大盘页面

​          ● 清理下线遗留应用

**实施路径：**

​          ● 针对大文件采用流失数据传输，减少对内存的消耗，避免产生FGC增加对lighting中间件的链路跟踪支持

​          ● 完善domino监控统计，对热点文件进行本地缓存，减少对ceph的压力

​          ● 对相关遗留应用进行梳理，联系业务方并下线

### 无线前端

#### **总结**

##### 项目一：钱堂投教接量期AICC项目

**目标**：覆盖接量期(D1前) CC  95%（预估5%极端场景需人工介入处理）工作内容（电话除外）

**关键技术方案**：通过分析接量期用户画像，掌握业务关键流程，和学习Top CC的服务特点，结合多模态识别与应答能力（图片/语音/表情包），树立“人感”服务系统。

**里程碑**：

  ● 2月21日立项：多方信息对齐，确定项目目标，关键技术卡点识别并解决

  ● 3月需求+工程化：明确各需求细节，确保无任一模糊信息。启动工程化研发工作，及构建黄金话术库工作

  ● 4月验收：完成语义回归验收，业务方验收，及额外体验上新需求研发

  ● 5~6月M量扩量验证：验证生产5期 AI+真人 VS 真人 班次的对比验证，数据统计+挖掘相关因子

  ● 6~7月Z量前系统升级：解决系列 AI+真人协同类、高级服务策略知识类、用户标签体系、增加备选模型 等问题，为Z量效果验证做准备

**项目结果**：

  ● AI+真人班级D1到课率稍高（35.7%  vs 32.7%）

  ● 及时回复率稍高 (92.4% vs 91.7%)

  ● D0有效沟通率显著提升（14.3% vs 6.7%）

  ● D1有效沟通率稍高（10.2% vs 8.6%）

  ● AI应答率达到98.3%，超过原定提效目标

  ● 在学籍注册率、入群率、删除率方面因M量数据复杂，难以获得准确数据，笼统数据层面表现相当

##### 项目二：前台数字人金融分析能力升级

**背景**：

  ● 财小智（小程序）的基础金融分析能力已搭建完成，项目旨在尽可能地多平台全面展示这些能力。

  ● 1.0数字人的UI较为简单，大屏全文本输出，期望能有更炫酷的展示效果。

**目标**：

  ● 最大化展示财小智金融分析能力。

  ● 针对大屏交互进行功能适配与优化。

  ● 发散性问题收敛，性能优化。

**优化内容**：

- 金融资产分析结果展示策略适配大屏、语音输入与播报优化、增加会话上下文、外挂知识库、多屏分辨率适配、语音交互延迟优化等

**项目结果**：

  ● 数字人语音互动质量达成原定目标，幻觉率≈0

  ● 成功上线前台大屏对外演示、董事长办公室、以及梯控宣传屏

##### 项目三：会员服务 - AI助教多模态升级

**背景和目标**：

提升1.0AI助教服务中人工参与的服务效率，扩大AI服务范围和提升智能化程度，其中为降低人工介入的需求为优先

**项目进程**：

2024年12月~2025年2月21，因其他项目优先级穿插，该项目陆续穿越3个月，于2月21日交付

**优化内容**：

● 增加AI助教对历史上下文中图片消息的理解

● 对图片消息内业务相关的关键信息进行强化识别，以及2K的数据回归调优

**项目结果**：

● 统计AI应答率>90%，且质量提升较为显著

● 转人工率由上线前56%降低至8%，超原定目标

**遗留问题**：

受限于1.0AI助教架构设计，在判断用户反复问同一类问题时会采取冷处理不回应，导致部分用户问题未解决。AICC项目在此之上对技术设计进行升级，取消了“同一类问题”限制，以“是否已解决用户问题”为攻克目标，借鉴AICC方案，可进一步提升应答质量。

#####  项目四：Agent 3.0

**背景和目标：**

在 Agent 2.0 能力的基础上，补足 Agentic Agent ，Multi-Agent 能力，提升 Agent 系统泛化性与自主性，降低 Agent 系统使用门槛和维护成本，覆盖更多边界场景业务。

**里程碑：**

● 4.9 项目启动

● 4.20 完成 MCP Server，Client，第一批 Tools开发

● 5.16 Multi-Agent 系统整体流程跑通，补充第二批 Tools

● 5.28 优化长程任务稳定性，完成数据标记任务与 manus 的对比测试

● 6:29 完成 aicc 分类 Prompt提取，回复 Prompt生成，Ai自主效果评估

● 7.8 完成 AMX 印尼 应答分类 Prompt 提取

● 7.9 产品化设计启动

**项目结果**

● Agent 3.0 整体架构跑通 MCP Ready，TOOLS: 8

● 支持 90+ 个子任务的长程 Job 规划执行

● aicc 高价值数据标记与聊天上下文补全任务错误率 0.08 大幅优于  manus 0.33

● aicc 回复真人对比 整体通过率：52%。排除SOP，语音回复，通过率：88%

#### **规划**

#####  规划一：钱堂投教接量期AICC项目

**目标**：将 AICC 的成功经验推广至 Z 量（大规模），通过严谨的数据验证明确 AI 的贡献，并实现生产环境中 30% CC 团队的接入

**行动路径**：

1. Z量扩量与对比验证
   - 启动AICC+真人班与纯真人班的平行对照测试
   - 细化数据分析模型：在 D1 到课率、有效沟通率等关键业务指标上，设计更精细的归因模型，量化 AICC 介入对最终效果的贡献度
   - 推动完成企微云和智域对接

2.     Agent能力深度与个性化升级
                  - 提升黄金话术库广度，信息量扩大3倍，以支持边界异常案例
                  - 深化服务策略，策略扩大3倍，以强化学员到课意向
                  - 用户画像：将沉淀的归因模型具象化在用户画像模型上，交接D1后CC服务并产生效果



#####  规划二：会员服务 - AI助教升级应答架构

**目标**：突破原架构对“同一类问题”限制，以“是否已解决用户问题”为攻克目标，借鉴AICC方案，进一步提升应答质量，将该类问题的占比从5%消灭至0。

**行动路径**：

1.     【应答流程架构升级】完成 AI 助教内部流程改造，包括素材知识库、发散性问题、员工协作边界能关键节点，确保系统能根据用户问题的解决状态，智能调整应答策略

2.     【全量回归】进行生产效果回归与重复问题重点回归



#####  规划三：投研智能体

**目标**：在现有财小智基础上，协同投研团队，扩充金融数据和升级投研框架，构建高水平投研智能体

**行动路径**：

【金融知识库】补充高质量的专业金融投研数据，如公告、研报、财报、宏观经济数据等，作为知识库的基础

【能力深化】投研分析能力突破

##### 规划四：Agent 3.0 产品化

目标：开发侧，提供方便集成的 Agent 能力和 MCP 接入接口。业务侧，职能部门，提供开箱即用的通用智能体，可以完成一般数据分析提取，报告生成任务。

**行动路径**：

​    ● mvp版本产品设计开发 & 使用手册文档站点

​    ● 开发接入与职能部门业务接入各找出1-2个场景投产





## 参考

- [敏捷绩效管理三剑客：OKR 、KPI、CFR](https://help.coding.net/insight/posts/ea45ccf0/)