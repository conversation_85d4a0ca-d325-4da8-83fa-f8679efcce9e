## FaaS 三期项目计划

### 需求

[FaaS V3 PRD](http://pages.wacai.info/client/mozi-faas-prd/)

- 增加服务网关
- 增加函数指标及监控能力
- 增加节点指标
- 二期优化需求

### 人员

- PM: AA
- 前端: BB
- 服务端: CC
- 运行时: DD

### 里程碑

| 时间 | 节点           | 完成情况 |
| :--- | :------------- | :------- |
| 6-30 | 需求评审       | ✅        |
| 7-6  | 服务端技术评审 | ✅        |
| 7-7  | 后端技术评审   | ✅        |
| 7-22 | 联调启动       | ✅        |
| 7-27 | 测试启动       | 未开始   |
| 7-29 | 发布           | 未开始   |

### 详细排期

 <img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/项目管理/1.png" alt="1" style="zoom:70%;" />

- 永光 8d
    - 消息格式 / 接口定义 1d
    - Kafka 消息接口调研 1d
    - 指标采集上报 2.5d
    - 获取指标数据统计接口 1d
    - 服务搜索 0.5d
    - 服务多 owner 支持 1d
    - 服务 QPS / Token 管理 1d
- 踏月 8d
    - 页面改造 + owner + 密钥 2d
    - 运行时节点度量 2.5d
    - 系统监控 1d
    - 业务监控 1d
    - 网关权限 / 限流 1.5d
- 苏枋 7d
    - 通信协议 2d
    - 网关路由表 1d
    - 运行时部署 1d
    - 后台运行时通信改造 2d
    - 网关 / 运行时 / 后台交互 1d

### 方案 / 技术设计

- [服务端设计](http://git.caimi-inc.com/trinity/discussions/issues/693)
- [运行时网关设计](http://git.caimi-inc.com/trinity/discussions/issues/694)

### 测试用例

[Faas网关迭代TC.xmind](http://git.caimi-inc.com/trinity/discussions/uploads/019ab7ab48e6ea2b7ee1cc1002837f38/Faas网关迭代TC.xmind)

### 测试方案

- FaaS 平台改造
- 压力测试
- 运行时
- 系统走查

#### FAAS 平台改造

- 测试: 圣汐
- 主要功能
    - 节点监控
    - 业务监控
    - 系统监控
    - 功能改造

#### 压力测试

- 测试: 永光
- 目标: 获取系统最大 QPS
- 工具: smurf

#### 运行时

- 测试: 永光 / 苏枋
- 主要功能:
    - 路由能力: 下发 / 重启 / 权限 / 限流
    - 部署: 节点状态 / 节点可用性
    - 高可用: 节点移除 / 函数部署选择

#### 系统走查

- 测试: 月菱 / 宁远

### 发布计划

// TODO