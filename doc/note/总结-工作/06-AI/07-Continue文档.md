## 概述

[Continue](https://github.com/continuedev/continue) 也是一个开源的AI code assistant，并且tongyi和marscode都是借鉴continue的方式采用独立agent模式。

[deepwiki](https://deepwiki.com/continuedev/continue/1-overview) 的文档写得太好了，这里做一些重点摘录，整体架构：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250428151813106.png" alt="image-20250428151813106" style="zoom:50%;" />

Continue uses a message-passing architecture to enable interactions between the core system, IDE extensions, and GUI components.

1. IDE Extensions- Integration with VS Code, IntelliJ, and potentially other IDEs
2. GUI System - The React-based user interface displayed within the IDE. **也就是WebView**
3. Core System -  The central orchestration layer responsible for LLM integration, codebase indexing, and configuration management



GUI 和 IDE extension 以及 Core system 之间的通信：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250428171558723.png" alt="image-20250428171558723" style="zoom:40%;" />

the communication uses defined protocols in the `core/protocol` directory:

- `ToIdeFromWebviewProtocol` - Messages from GUI to IDE
- `ToWebviewFromIdeProtocol` - Messages from IDE to GUI
- `ToCoreFromWebviewProtocol` - Messages from GUI to Core
- `ToWebviewFromCoreProtocol` - Messages from Core to GUI

## 进程通信

从前面架构可以了解，IDEA插件 和 Core是运行时分离的，IntelliJ 扩展与 Core 系统的通信主要通过 `CoreMessenger` 类实现，这个类负责在 IntelliJ 插件和 Core 系统之间传递消息。

### 1、通信初始化

通信初始化是在 `CoreMessengerManager` 类中完成的，代码如下：

```kotlin
class CoreMessengerManager(){
      init {
        //设置 CoreMessenger 实现 IDEA 和 core之间的通信
       	 setupCoreMessenger(continueCorePath) 
      }
  		private fun setupCoreMessenger(continueCorePath: String) {
        coreMessenger = CoreMessenger(project, continueCorePath, ideProtocolClient, coroutineScope)
        coreMessenger?.request("config/getSerializedProfileInfo", null, null)
      }
}
```

这里创建了 `CoreMessenger` 实例，它接收项目信息、Core 路径、`IdeProtocolClient` 和协程作用域作为参数。

CoreMessenger 内部是会通过环境变量 `USE_TCP` 来决定是否使用 TCP 通信，但默认情况下不使用 TCP。代码如下：

```kotlin
class CoreMessenger() {
    init {
        if (useTcp) {
            // 创建 Socket 连接到 127.0.0.1:3000
            val socket = Socket("127.0.0.1", 3000)
            // 设置输入输出流
            val writer = PrintWriter(socket.getOutputStream(), true) 
            val reader = BufferedReader(InputStreamReader(socket.getInputStream()))
            
            // 启动新线程读取消息
            Thread {
                while (true) {
                    val line = reader.readLine()
                    if (line != null && line.isNotEmpty()) {
                        handleMessage(line) // 处理接收到的消息
                    }
                    Thread.sleep(100)
                }
            }.start()
            
        }else{

            // 启动子进程
            val processBuilder = ProcessBuilder(continueCorePath)
            process = processBuilder.start()
            
            // 设置进程的输入输出流
            writer = OutputStreamWriter(process!!.outputStream)
            reader = BufferedReader(InputStreamReader(process!!.inputStream))
                       
            // 启动协程读取消息
            coroutineScope.launch(Dispatchers.IO) {
                while (true) {
                    val line = reader?.readLine()
                    if (line != null && line.isNotEmpty()) {
                        handleMessage(line) // 处理接收到的消息
                    }
                    delay(100)
                }
            }
        }
    }
}
```

TCP 模式使用 Socket 通信,而子进程模式使用标准输入输出流进行通信。

发送消息就很简单了，CoreMessenger的request方法()：

```kotlin

    fun request(messageType: String, data: Any?, messageId: String?, onResponse: (Any?) -> Unit) {
        val id = messageId ?: uuid()
        val message =
            gson.toJson(mapOf("messageId" to id, "messageType" to messageType, "data" to data))
        responseListeners[id] = onResponse
        write(message)
    }

```



### 2、WebView 到 Core 的消息传递

当用户在 WebView 中点击聊天按钮时，WebView 通过 JavaScript 调用向 IntelliJ 发送消息，如果消息类型在 PASS_THROUGH_TO_CORE 列表中（包括 llm/streamChat），则会将消息转发给 Core，ContinueBrowser.kt代码如下：

```kotlin
class ContinueBrowser(val project: Project, url: String) {
  	myJSQueryOpenInBrowser.addHandler { msg: String? ->                                       
				//定义回调
				val respond = fun(data: Any?) {
            sendToWebview(messageType, data, messageId ?: uuid())
        }
				//PASS_THROUGH_TO_CORE 列表在 MessageTypes.kt 中定义，其中包含了 llm/streamChat
        if (PASS_THROUGH_TO_CORE.contains(messageType)) {
            continuePluginService.coreMessenger?.request(messageType, data, messageId, respond)
            return@addHandler null
        }
		}
}
```



### 3、Core 处理 LLM 请求

Core 系统收到 `llm/streamChat` 请求后，会在 `core.ts` 中处理：

```kotlin
    on("llm/complete", async (msg) => {
      const model = (await this.configHandler.loadConfig()).config
        ?.selectedModelByRole.chat;

      if (!model) {
        throw new Error("No chat model selected");
      }

      const completion = await model.complete(
        msg.data.prompt,
        new AbortController().signal,
        msg.data.completionOptions,
      );
      return completion;
    });
```

Core 系统会加载配置，获取当前选择的聊天模型，然后调用 LLM 完成请求。

这里的字符串是 messageType，完整的messageType定义在 MessageTypes 中。

## Chat执行过程

 前面简单总结了GUI和Core之间的进程通信，这里详细分析Chat执行过程包括RAG。

### 1、GUI部分

GUI是一个基于 reduxjs/toolkit + react-redux 实现的富客户端，Chat.tsx是主要实现类。

`sendInput` 是一个回调函数，主要处理用户输入并发送消息。触发时机：

```xml
// 1. 主输入框触发
<ContinueInputBox
  isMainInput
  isEditMode={isInEditMode}
  isLastUserInput={false}
  onEnter={(editorState, modifiers, editor) =>
    sendInput(editorState, modifiers, undefined, editor)
  }
  inputId={MAIN_EDITOR_INPUT_ID}
/>
```

`sendInput` 函数执行过程：

```typescript
const sendInput = useCallback((
  editorState: JSONContent,
  modifiers: InputModifiers,
  index?: number,
  editorToClearOnSend?: Editor,
) => {
  // 1. 工具调用状态检查
  if (toolCallState?.status === "generated") {
    return console.error("Cannot submit message while awaiting tool confirmation");
  }

  // 2. 免费试用限制检查
  if (selectedChatModel?.provider === "free-trial") {
    const newCount = incrementFreeTrialCount();
    if (newCount >= FREE_TRIAL_LIMIT_REQUESTS) {
      // 显示试用限制提示...
      return;
    }
  }

  // 3. 单文件编辑模式检查
  if (isSingleRangeEditOrInsertion) {
    handleSingleRangeEditOrInsertion(editorState);
    return;
  }

  // 4. 正常消息发送流程
  const promptPreamble = isInEditMode
    ? getMultifileEditPrompt(codeToEdit)
    : undefined;
  
  dispatch(streamResponseThunk({ 
    editorState, 
    modifiers, 
    promptPreamble, 
    index 
  }));

  // 5. 清理编辑器内容
  if (editorToClearOnSend) {
    editorToClearOnSend.commands.clearContent();
  }

  // 6. 更新本地存储计数器
  const currentCount = getLocalStorage("mainTextEntryCounter");
  if (currentCount) {
    setLocalStorage("mainTextEntryCounter", currentCount + 1);
    if (currentCount === 300) {
      dispatch(setDialogMessage(<FeedbackDialog />));
      dispatch(setDialogEntryOn(false));
      dispatch(setShowDialog(true));
    }
  } else {
    setLocalStorage("mainTextEntryCounter", 1);
  }
}
```

重点关注④ streamResponseThunk() ，`streamResponseThunk` 会先收集上下文，然后发送聊天请求，对应的是 streamResponse.ts：

```typescript
export const streamResponseThunk = createAsyncThunk<
  void, // 返回类型
  { // 参数类型
    editorState: JSONContent;
    modifiers: InputModifiers;
    index?: number;
    promptPreamble?: string;
  },
  ThunkApiType // Thunk配置类型
>(
  "chat/streamResponse",
  async (
    { editorState, modifiers, index, promptPreamble },
    { dispatch, extra, getState },
  )
```

这里`"chat/streamResponse"` 是一个 Redux action type，用于标识这个异步 thunk action。这种格式通常用于命名空间/功能区分，这里表示这是聊天功能下的流式响应操作。

主要流程：

```typescript
streamThunkWrapper(async () => {
  	 // 1. 获取状态和验证
     const state = getState();
  	// 2. 初始化编辑器状态
     dispatch(submitEditorAndInitAtIndex({ index: inputIndex, editorState }),);
  	 // 3. 收集上下文
      const result = await dispatch(
          gatherContext({
            editorState,
            modifiers,
            promptPreamble,
          }),
        );
      const {
          selectedContextItems,
          selectedCode,
          content,
          slashCommandWithInput,
        } = unwrapResult(result);
  
     //4. 更新历史记录
      dispatch(
          updateHistoryItemAtIndex({
            index: inputIndex,
            updates: {
              message: {
                role: "user",
                content,
                id: uuidv4(),
              },
              contextItems: selectedContextItems,
            },
          }),
        );
    //5. 构造消息并发送
        const messages = constructMessages(
          [...updatedHistory],
          selectedChatModel?.baseChatSystemMessage,
          state.config.config.rules,
          selectedChatModel.model,
        );
  			 // 发送到流式处理
        unwrapResult(
          await dispatch(
            streamNormalInput()
            )
        );
              
}
```

可以看到Continue将 收集上下文 和 发送聊天请求 拆分为两个独立步骤。主要有以下几个原因：

关注点分离：
- 上下文收集是一个独立的功能，可以被多种操作复用
- 聊天请求处理是另一个独立功能，可以接受不同来源的上下文

用户体验优化：

- 分步处理允许GUI在收集上下文时显示加载状态
- 用户可以看到系统正在处理哪个阶段，提供更好的反馈

灵活性和可扩展性：

- 上下文收集可以独立扩展，添加新的上下文提供器
- 聊天请求处理可以独立优化，支持不同的LLM模型和交互模式

错误处理：

- 分步处理允许在上下文收集失败时提供更具体的错误信息
- 如果上下文收集成功但聊天请求失败，已收集的上下文可以保留

### 2、上下文收集

#### 上下文提供器介绍

Continue 项目中的上下文提供器（Context Providers）是 RAG（检索增强生成）功能的核心组件，它们负责为 LLM 提供相关上下文。

Continue 中的上下文提供器主要通过以下几种方式触发：
- 用户显式调用：用户在聊天输入中使用 @ 符号后跟提供器名称（如 @clipboard）
- 快捷键触发：特定快捷键会自动触发某些上下文提供器
- 配置自动触发：在配置中设置为默认使用的上下文提供器会在每次聊天中自动触发
- 系统自动触发：某些上下文提供器会在特定条件下由系统自动触发

Continue 中的上下文提供器分为三种类型：
- normal：普通类型，直接提供上下文
- submenu：子菜单类型，显示一个可搜索的下拉列表供用户选择
- query：查询类型，显示一个文本框让用户输入查询


以下是一些常见上下文提供器的触发条件：
- CodebaseContextProvider： 如果indexing打开或者用户在聊天输入中输入 @codebase
- CurrentFileContextProvider：  用户在聊天输入中输入 @currentFile
- FileContextProvider：  一个 "submenu" 类型的上下文提供器，在以下情况下触发：
    - 用户在聊天输入中输入 @file
    - 系统显示一个包含工作区文件的可搜索下拉菜单
    - 用户选择一个文件后，该文件内容会被添加到聊天上下文中 FileContextProvider.ts:18-24
- SearchContextProvider： 用户在聊天输入框中输入 @search，内部是基于 ripgrep 实现

更多可以参考[context-providers](https://docs.continue.dev/customize/context-providers)，或者询问deepwiki IContextProvider

#### 发送 getContextItems 消息

入口 gatherContext.ts 的gatherContext()，代码如下：

```typescript
async ({ modifiers, editorState, promptPreamble }, { dispatch, extra, getState }) => {
  // 1. 获取状态和验证
  const state = getState();
  const selectedChatModel = selectSelectedChatModel(state);
  const defaultContextProviders = state.config.config.experimental?.defaultContext ?? [];

  if (!selectedChatModel) {
    console.error("gatherContext thunk: Cannot gather context, no model selected");
    throw new Error("No chat model selected");
  }

  // 2. 解析编辑器输入内容为context(RAG)
  let [selectedContextItems, selectedCode, content, slashCommandWithInput] =
    await resolveEditorContent({
      editorState,
      modifiers,
      ideMessenger: extra.ideMessenger,
      defaultContextProviders,
      availableSlashCommands: state.config.config.slashCommands,
      dispatch,
    });

  // 3. 自动添加当前文件（如果未禁用上下文）
  if (!modifiers.noContext) {
    const usingFreeTrial = selectedChatModel.provider === "free-trial";
    
    // 获取当前文件
    const currentFileResponse = await extra.ideMessenger.request(
      "context/getContextItems",
      {
        name: "currentFile",
        query: "non-mention-usage",
        fullInput: "",
        selectedCode: [],
      }
    );

    // 4. 添加提示前缀（如果存在）
    if (promptPreamble) {
      if (typeof content === "string") {
        content = promptPreamble + content;
      } else if (content[0].type === "text") {
        content[0].text = promptPreamble + content[0].text;
      }
    }

    // 5. 返回收集的上下文信息
    return {
      selectedContextItems,
      selectedCode,
      content,
      slashCommandWithInput,
    };
  }
```

② resolveEditorContent.ts 向 Core 系统发送 `context/getContextItems` 消息，代码如下：

```typescript
  if (modifiers.useCodebase) {
    const result = await ideMessenger.request("context/getContextItems", {
      name: "codebase",
      query: "",
      fullInput: stripImages(parts),
      selectedCode,
    });

    if (result.status === "success") {
      contextItems.push(...result.content);
    }
  }
```

注意这里实际使用的是 fullInput，所以query是空字符串。

#### 实现RAG

Core 系统在 `getContextItems` 方法中处理这个消息

```typescript
on("context/getContextItems", this.getContextItems.bind(this));
private getContextItems = async (){
  	//查找对应的	provider
  	const provider =
      config.contextProviders?.find(
        (provider) => provider.description.title === name,
      ) ??
     
      //获取上下文          
      const items = await provider.getContextItems(query, {
        config,
        llm,
        embeddingsProvider: config.selectedModelByRole.embed,
        fullInput,
        ide: this.ide,
        selectedCode,
        reranker: config.selectedModelByRole.rerank,
        fetch: (url, init) =>
          fetchwithRequestOptions(url, init, config.requestOptions),
      });

      void Telemetry.capture(
        "useContextProvider",
        {
          name: provider.description.title,
        },
        true,
      );

      return items.map((item) => ({
        ...item,
        id,
      }));  	
}
```

对于  CodebaseContextProvider.getContextItems()  都会调用 retrieveContextItemsFromEmbeddings()方法。方法内部会创建一个检索管道（Retrieval Pipeline），根据是否启用重排序功能选择不同的实现。

检索管道有两种实现：

1. `NoRerankerRetrievalPipeline` - 不使用重排序
2. `RerankerRetrievalPipeline` - 使用重排序

NoRerankerRetrievalPipeline.run()方法代码如下：

```typescript
async run(args: RetrievalPipelineRunArguments): Promise<Chunk[]> {
	  // 1. 从选项中解构需要的参数
    const { input, nFinal, filterDirectory, config } = this.options;

    // 2. 分配检索配额
    // 将总配额(nFinal)分成三部分:
    // - 25% 给最近编辑的文件
    // - 25% 给全文搜索(FTS)
    // - 50% 给嵌入搜索
    // We give 1/4 weight to recently edited files, 1/4 to full text search,
    // and the remaining 1/2 to embeddings
    const recentlyEditedNFinal = nFinal * 0.25;
    const ftsNFinal = nFinal * 0.25;
    const embeddingsNFinal = nFinal - recentlyEditedNFinal - ftsNFinal;

		//3. 执行全文搜索(FTS)检索
		ftsChunks = await this.retrieveFts(args, ftsNFinal);

		//4. 执行嵌入检索
		embeddingsChunks = !!config.selectedModelByRole.embed
        ? await this.retrieveEmbeddings(input, embeddingsNFinal)
        : [];

		//5. 获取最近编辑的文件
		recentlyEditedFilesChunks =
        await this.retrieveAndChunkRecentlyEditedFiles(recentlyEditedNFinal);

		//6. 从仓库映射中获取相关文件
		 repoMapChunks = await requestFilesFromRepoMap(
        this.options.llm,
        this.options.config,
        this.options.ide,
        input,
        filterDirectory,
      );

		//7.合并所有检索结果
   retrievalResults.push(
        ...recentlyEditedFilesChunks,
        ...ftsChunks,
        ...embeddingsChunks,
        ...repoMapChunks,
      );
    // 8. 去重并返回结果
    const deduplicatedRetrievalResults: Chunk[] =
      deduplicateChunks(retrievalResults);

    return deduplicatedRetrievalResults;
}
```

`RerankerRetrievalPipeline` 的实现更复杂，它首先获取初始结果，然后使用重排序模型对结果进行排序。

```typescript
  async run(args: RetrievalPipelineRunArguments): Promise<Chunk[]> {
    let results = await this._retrieveInitial(args); //获取初始结果
    results = await this._rerank(args.query, results); //重排序
    }
```

### 3、构造消息并发送

这一步是调用`streamNormalInput`发送`llm/streamChat`请求给Core，streamNormalInput.ts 代码如下：

```typescript
  "chat/streamNormalInput",
  async ({ messages, legacySlashCommandData }, { dispatch, extra, getState }) => {
    // 1. 收集状态
    const state = getState();
    const selectedChatModel = selectSelectedChatModel(state);
    const toolSettings = state.ui.toolSettings;
    const toolGroupSettings = state.ui.toolGroupSettings;
    const streamAborter = state.session.streamAborter;
    const useTools = selectUseTools(state);

    if (!selectedChatModel) {
      throw new Error("Default model not defined");
    }

    // 2. 确定是否包含工具
    const includeTools = useTools && modelSupportsTools(selectedChatModel);

    // 3. 发送流式聊天请求
    const gen = extra.ideMessenger.llmStreamChat(
      {
        completionOptions: includeTools
          ? {
              tools: state.config.config.tools.filter(
                (tool) =>
                  toolSettings[tool.function.name] !== "disabled" &&
                  toolGroupSettings[tool.group] !== "exclude",
              ),
            }
          : {},
        title: selectedChatModel.title,
        messages,
        legacySlashCommandData,
      },
      streamAborter.signal,
    );

    // 4. 处理流式响应
    let next = await gen.next();
    while (!next.done) {
      // 检查是否需要中止流
      if (!getState().session.isStreaming) {
        dispatch(abortStream());
        break;
      }

      // 分发流更新
      dispatch(streamUpdate(next.value));
      next = await gen.next();
    }

    // 5. 处理完成的响应
    if (next.done && next.value) {
      // 添加提示和完成对
      dispatch(addPromptCompletionPair([next.value]));

      // 记录交互日志
      try {
        if (state.session.mode === "chat" || state.session.mode === "agent") {
          extra.ideMessenger.post("devdata/log", {
            name: "chatInteraction",
            data: {
              prompt: next.value.prompt,
              completion: next.value.completion,
              modelProvider: selectedChatModel.provider,
              modelTitle: selectedChatModel.title,
              sessionId: state.session.id,
            },
          });
        }
      } catch (e) {
        console.error("Failed to send dev data interaction log", e);
      }
    }

    // 6. 处理工具调用
    const toolCallState = selectCurrentToolCall(getState());
    if (toolCallState) {
      // 标记工具生成
      dispatch(
        setToolGenerated({
          toolCallId: toolCallState.toolCallId,
        }),
      );

      // 如果工具允许自动执行，则执行
      if (
        toolSettings[toolCallState.toolCall.function.name] ===
        "allowedWithoutPermission"
      ) {
        const response = await dispatch(callCurrentTool());
        unwrapResult(response);
      }
    }
  },
);
```





## Apply实现

Continue的Apply功能是一个将LLM生成的代码应用到现有文件的过程。

在[FastApply技术研究](https://www.cnblogs.com/boydfd/p/18759542)中提到过 Continue 的 Apply实现原理，主要采用AST分析和LLM两种实现方式，但是从源码来看没这么简单。



### VsCode实现

VsCode实现主要涉及几个关键组件：

- GUI层：用户在界面上点击"Apply"按钮
- 消息传递层：将应用请求从GUI传递到IDE扩展
- 应用管理层：处理代码应用的核心逻辑
- 差异生成层：生成原始代码和新代码之间的差异
- 差异应用层：将差异应用到文件



整个应用流程如下：

#### 1、触发onClickApply函数

用户在GUI中点击"Apply"按钮，触发onClickApply函数，StepContainerPreToolbar/index.tsx 代码如下：

```typescript
  async function onClickApply() {
    const fileUri = await getFileUriToApplyTo();
    if (!fileUri) {
      ideMessenger.ide.showToast(
        "error",
        "Could not resolve filepath to apply changes",
      );
      return;
    }

    // applyToFile will create the file if it doesn't exist
    ideMessenger.post("applyToFile", {
      streamId: codeBlockStreamId,
      filepath: fileUri,
      text: codeBlockContent,
    });

    setAppliedFileUri(fileUri);
    refreshFileExists();
  }
```

核心是 发送`applyToFile`消息到IDE扩展。

#### 2、VsCode接收消息

VsCode扩展的`VsCodeMessenger`接收消息并调用`ApplyManager` ，对应的代码[extensions/vscode/src/extension/VsCodeMessenger.ts](https://github.com/continuedev/continue/blob/8aded10a/extensions/vscode/src/extension/VsCodeMessenger.ts#L127) ：

```typescript
    this.onWebview("applyToFile", async ({ data }) => {
      const [verticalDiffManager, configHandler] = await Promise.all([
        verticalDiffManagerPromise,
        configHandlerPromise,
      ]);

      const applyManager = new ApplyManager(
        this.ide,
        webviewProtocol,
        verticalDiffManager,
        configHandler,
      );

      await applyManager.applyToFile(data);
    });
```

#### 3、ApplyManager处理应用逻辑 

`ApplyManager`处理应用逻辑 ApplyManager.ts

```typescript
async applyToFile({
    streamId,
    filepath,
    text,
    toolCallId,
  }: ApplyToFileOptions) {
  
    const hasExistingDocument = !!activeTextEditor.document.getText().trim();
		//文件是否存在
    if (hasExistingDocument) {
    	//处理文件更新
      await this.handleExistingDocument(
        activeTextEditor,
        text,
        streamId,
        toolCallId,
      );
    } else {
      await this.handleEmptyDocument(
        activeTextEditor,
        text,
        streamId,
        toolCallId,
      );
    }
  }
```

对于已有内容的文件，使用 `ApplyManager` 的`applyCodeBlock`函数来处理代码应用，`applyCodeBlock`函数是核心实现，它尝试以不同方式应用代码，应用代码有两种主要方式：

1. **即时应用（Instant Apply）**：使用确定性算法直接应用代码 applyCodeBlock.ts:23-36
2. **流式应用（Stream Apply）**：使用LLM来帮助应用代码 applyCodeBlock.ts:52-55

流式应用是当即时应用不可行时的备选方案，它使用LLM来帮助应用代码，生成差异后，系统会将差异应用到文件。

注意 applyCodeBlock 是属于core模块的，vscode会直接依赖core部分代码。

### IDEA实现

IntelliJ扩展**不直接调用** `core/edit/lazy/applyCodeBlock`函数。与VS Code扩展不同，IntelliJ扩展使用了不同的实现方式来处理代码应用功能。

在IntelliJ扩展中，Apply功能主要在IdeProtocolClient.kt文件中实现。当收到来自GUI的applyToFile消息时，IntelliJ扩展会执行以下步骤：

#### 1、GUI层触发onClickApply函数

GUI层触发：用户在GUI中点击"Apply"按钮，与VS Code扩展类似，这会触发一个事件 index.tsx:164-167

```typescript
  async function onClickApply() {
    const fileUri = await getFileUriToApplyTo();
    if (!fileUri) {
      ideMessenger.ide.showToast(
        "error",
        "Could not resolve filepath to apply changes",
      );
      return;
    }

    // applyToFile will create the file if it doesn't exist
    ideMessenger.post("applyToFile", {
      streamId: codeBlockStreamId,
      filepath: fileUri,
      text: codeBlockContent,
    });

    setAppliedFileUri(fileUri);
    refreshFileExists();
  }
```

#### 2、IDEA接收消息

在IntelliJ扩展中，IdeProtocolClient类负责处理从GUI发送的消息，包括applyToFile消息，[IdeProtocolClient.kt#L426](https://github.com/continuedev/continue/blob/8aded10a/extensions/intellij/src/main/kotlin/com/github/continuedev/continueintellijextension/continue/IdeProtocolClient.kt#L426)：

```typescript
"applyToFile" -> {
		//代码比较多....   
}
```

applyToFile 代码块主要逻辑：

1. 构建prompt
2. 创建 DiffStreamService 和 DiffStreamHandler 处理



构建prompt的如下：

```
The following code was suggested as an edit:\n```\nprivate void saveResult(){\n    System.out.println("Saving result to file...");\n    String filePath = System.getProperty("user.dir") + "/test/test-code-" + getIncrementingFileNumber() + ".md";\n    java.nio.file.Files.write(java.nio.file.Paths.get(filePath), (getTimestamp() + "\n" + out.toString()).getBytes());\n}\n```\nPlease apply it to the previous code
```

后面再接一段原始代码。

然后调用 DiffStreamHandler.streamDiffLinesToEditor()处理

#### 3、DiffStreamHandler

`DiffStreamHandler`类的 handleDiffLineResponse() 方法负责将LLM返回的代码转换为可视化的差异。它通过以下步骤工作：

1. **处理差异行**：根据差异类型（相同、新增或删除）处理每一行代码 DiffStreamHandler.kt:126-144
2. **创建差异块**：为每组相关的差异创建一个`VerticalDiffBlock`实例 DiffStreamHandler.kt:166-175
3. **处理新行和旧行**：分别处理新增和删除的代码行 DiffStreamHandler.kt:187-195 DiffStreamHandler.kt:197-203

最后效果如下，注意是直接在源文件上更新的，没有使用IDEA的diffManager

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250429143032756.png" alt="image-20250429143032756" style="zoom:50%;" />



## 代码补全

Continue 的文档非常好，我找到了他关于[autocomplete](https://github.com/continuedev/continue/blob/main/docs/docs/customize/deep-dives/autocomplete.md)的说明，这里做一些记录

1. 对于代码完成，传统的GPT大模型效果并不一定好，原因是对于autocomplete有一些特殊的prompt格式(参考[此处](https://github.com/continuedev/continue/blob/main/core/autocomplete/templating/AutocompleteTemplate.ts)prompts)，GPT并不一定能够认识
2. 外部模型推荐使用Codestral，它可以通过[Mistral API](https://console.mistral.ai/)获得
3. 本地模型推荐 qwen2.5-coder:1.5b

接下来源码阅读，为了Auto-Dev-VSCode的经验我们只需要找到是哪个类是实现了 vscode.InlineCompletionItemProvider 接口。

 ```typescript
export class ContinueCompletionProvider implements vscode.InlineCompletionItemProvider{
      const outcome =
      await this.completionProvider.provideInlineCompletionItems(
        input,
        signal,
      );

    if (!outcome || !outcome.completion) {
      return null;
    }  
}
 ```

委托给了 CompletionProvider.provideInlineCompletionItems()，主要流程:

- 记录开始时间，获取自动补全option
- 防抖处理
- 如果被预过滤，则返回 undefined
- 渲染prompt
- 如果prompt命中了cache，直接返回结果
- 调用**completionStreamer.streamCompletionWithFilters**()返回补全的stream
- 逐步构建补全结果
- 如果中止，则不进行后处理
- 保存到缓存