## 理论知识

### 1、LLM 和 前几年的 Machine Learning有什么区别？

LLM (Large Language Model) 本身就是机器学习的一个分支，机器学习模型更像是为特定任务定制的**“专科医生”，而现在的 LLM 更像是一个能够处理多种任务的“全科医生”**。

不同点
**1、模型规模和架构**

- 传统机器学习 (ML)： 模型规模相对较小，参数数量从几千到几百万不等。架构多样，包括决策树（Decision Trees）、支持向量机（Support Vector Machines）、**线性回归（Linear Regression）**以及早期的卷积神经网络 (CNN) 和循环神经网络 (RNN) 等。
- LLM： 模型规模巨大，参数通常从数十亿到万亿级别。架构主要基于Transformer，利用自注意力机制（Self-Attention Mechanism），能并行处理整个文本序列，更好地理解上下文关系。

**2、训练方式和数据类型**

- 传统机器学习 (ML)： 通常需要有标签的结构化数据。例如，要训练一个垃圾邮件分类器，你需要标记好哪些邮件是垃圾邮件，哪些不是。这个过程通常需要大量的人工标注，且模型只能解决一个特定任务。
- LLM： 主要使用无标签的海量非结构化文本数据（如书籍、文章、网页等）进行预训练（Pre-training）。通过预测下一个单词或句子来学习语言本身的模式和知识，从而获得强大的通用能力。

**3、任务类型和泛化能力**

- 传统机器学习 (ML)： 擅长处理**判别式（Discriminative）**任务，例如分类、回归和预测。比如，预测房价、识别图片中的物体、或者根据用户行为推荐商品。模型的泛化能力有限，通常只能解决它被训练来解决的那个特定问题。
- LLM： 擅长处理生成式（Generative）任务，能够生成文本、代码、甚至图片。其强大的预训练基础使其具有多任务处理能力和**上下文学习（In-context Learning）**能力。这意味着一个LLM模型可以通过简单的提示（prompt），在无需额外训练的情况下，完成摘要、翻译、问答、甚至是创意写作等多种任务。

**4、资源规模**

- 传统机器学习 (ML)： 训练和部署所需的计算资源相对较小，许多模型可以在普通的服务器或个人电脑上运行。
- LLM： **训练和部署需要海量的计算资源（GPU集群）、庞大的内存和巨大的能耗。这使得LLM的训练成本极高**，通常只有大型科技公司才能负担。



可以把机器学习想象成一个大家庭，而LLM只是这个家庭里的一个新生代成员。

机器学习这个大家庭主要包括以下几个核心分支：

**1. 监督学习 (Supervised Learning)**
这是最常见、最基础的一类。就像一个学生在老师的指导下学习一样，模型通过有标签的数据进行学习。也就是说，每一份数据都附带一个正确的“答案”。

分类 (Classification): 任务是把数据分到不同的类别中。

例子： 判断一封邮件是不是垃圾邮件、识别图片中的是猫还是狗、根据肿瘤特征判断其是否为恶性。

回归 (Regression): 任务是预测一个连续的数值。

例子： 根据房屋的面积、位置等信息预测房价、根据历史数据预测股票价格、预测一个人的年收入。

**2.无监督学习 (Unsupervised Learning)**
与监督学习不同，无监督学习就像是一个学生在没有老师指导的情况下自己摸索。模型处理的是没有标签的数据，它的任务是自己去发现数据中隐藏的模式和结构。

聚类 (Clustering): 任务是把相似的数据点分到同一个组里。

例子： 根据消费习惯对客户进行分群、将新闻文章按主题进行分组、在图像中分割不同的对象。

降维 (Dimensionality Reduction): 任务是减少数据的特征数量，同时尽量保留数据中的重要信息。

例子： 简化复杂的基因表达数据、压缩图像或音频数据。

**3.半监督学习 (Semi-supervised Learning)**
这个分支介于监督学习和无监督学习之间。它结合了少量有标签数据和大量无标签数据来训练模型。这在实际应用中非常有用，因为给数据打标签通常既耗时又昂贵。

例子： 在一个包含大量照片的数据库中，只对其中一小部分照片标注人脸，然后利用这些已标注的数据来帮助模型识别剩下未标注照片中的人脸。

**4.强化学习 (Reinforcement Learning)**
这是一种完全不同的学习方式。模型在一个环境中学习如何行动，以最大化累积奖励。它通过试错来学习，就像我们学习骑自行车一样，通过不断尝试、摔倒、再尝试，最终学会了平衡。

例子： 训练一个AI下棋（如AlphaGo）、让一个机器人学习走路、自动驾驶汽车的决策系统。

**5.深度学习 (Deep Learning)**
深度学习可以看作是机器学习的一个高级子集，它使用包含多个隐藏层的深度神经网络。这使得模型能够自动从原始数据中提取出更复杂、更高层次的特征，而不需要人工干预。

LLM（以及之前的CNN、RNN等）都属于深度学习的范畴。





### 1、ChatGPT用到了哪些技术和架构？

要学习ChatGPT相关背景知识，需要掌握以下技术和领域的基础知识：

1. 自然语言处理（Natural Language Processing，NLP）：ChatGPT是一种自然语言处理模型，因此需要了解自然语言处理的基本概念、技术和应用。
2. 机器学习（Machine Learning，ML）：ChatGPT是一种基于机器学习的模型，需要掌握机器学习的基础概念、算法和模型。
3. 深度学习（Deep Learning，DL）：ChatGPT是一种基于深度学习的模型，需要掌握深度学习的基本概念、技术和应用。
4. 神经网络（Neural Networks，NN）：ChatGPT是一种基于神经网络的模型，需要了解神经网络的基本概念、结构和训练方法。
5. 语言模型（Language Model，LM）：ChatGPT是一种语言模型，需要了解语言模型的基本概念、训练方法和应用。
6. 注意力机制（Attention Mechanism）：ChatGPT使用了**注意力机制**来加强模型对输入信息的关注，需要了解注意力机制的基本原理和应用。
7. Transformer模型：ChatGPT是一种基于**Transformer模型**的语言模型，需要了解Transformer模型的基本概念、结构和训练方法。

可以通过阅读相关的论文、书籍、课程和教程等方式来学习这些知识。

ChatGPT使用了Transformer模型，并且在此基础上进行了改进。具体来说，ChatGPT使用了多层Transformer编码器和解码器结构，以及掩码自注意力机制和位置编码等技术来实现对自然语言文本的生成和理解。



### 2、什么是Transformer呢？

> 参考：[十分钟理解Transformer](https://zhuanlan.zhihu.com/p/82312421) 

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230324115114770.png" alt="image-20230324115114770" style="zoom:50%;" />

- 基于**自注意力(self attention)机制**的神经机器翻译模型

- 相比传统的循环神经网络（RNN）或卷积神经网络（CNN），Transformer模型在处理长序列数据时更加高效，同时还具有更好的并行化能力。
- 主要包括Encoder和Decoder两个部分。Encoder用于将输入文本进行编码，Decoder用于将编码后的文本进行解码并生成输出文本。在编码和解码过程中，Transformer模型会对输入文本中的每个词汇进行注意力计算，以捕捉不同词汇之间的交互关系和语义信息。这种注意力机制使得Transformer模型在处理长序列数据时，不需要进行循环计算和逐步更新状态，从而加速了计算过程，并且还能够更好地捕捉全局上下文信息，提高了翻译质量。
- transformer 的输入通常是向量

### 3、ChatGPT处理流程？

当您提出问题时，ChatGPT会执行以下步骤来处理您的问题：

1. **文本预处理**：ChatGPT会对输入的文本进行预处理，包括去除停用词、分词、转换为小写字母等。
2. **Embeddings映射**：ChatGPT使用预训练的Embedding模型将文本中的每个单词或字符映射为一个向量表示。
3. **encoder处理**：ChatGPT将嵌入向量输入到多层Transformer编码器中，编码器会对文本进行编码，捕捉上下文信息和语义关系，从而生成一个上下文向量表示。
4. **decoder生成**：ChatGPT使用编码器生成的上下文向量作为解码器的输入，解码器会逐步生成对问题的回答。在生成回答时，ChatGPT使用了自回归生成的策略，即每个时刻只生成一个单词，并将生成的单词作为下一个时刻的输入。
5. Beam Search策略：ChatGPT使用Beam Search策略对生成的回答进行搜索，以找到最优的回答。Beam Search策略是一种启发式搜索算法，它在生成回答时保留了多个备选答案，然后根据一定的准则选择最优的答案。
6. 回答输出：ChatGPT最终会输出生成的回答。

### 4、Embeddgings model 是什么，在ChatGPT中扮演什么角色？

Embeddings是一种将离散化的输入（如单词、字符、类别等）映射为**连续的向量**表示的技术，通常是通过神经网络中的一个隐藏层实现的。这些向量被称为嵌入（Embeddings），也称为词嵌入（Word Embeddings）或向量嵌入（Vector Embeddings）。

在ChatGPT中，Embeddings起到了将自然语言文本中的单词或字符**映射为连续向量表示**的作用。具体来说，ChatGPT使用的是一种基于Transformer模型的语言模型，其中输入层包含一个Embedding层，将文本中的每个单词或字符映射为一个向量表示。这些向量嵌入（Embeddings）被输入到模型的编码器和解码器中，从而帮助模型进行对话生成和理解。

Embeddings在ChatGPT中扮演了至关重要的角色，因为它们能够将单词或字符的离散表示转换为连续的向量表示，从而让模型可以学习到它们的语义和上下文信息。这使得模型可以更好地理解和生成自然语言文本，从而提高了ChatGPT在对话生成和自然语言处理任务中的性能。

> [What are embeddings](https://platform.openai.com/docs/guides/embeddings/what-are-embeddings)



### 5、如何训练Embedding模型？

预训练Embedding模型的主要思路是通过学习语言模型，将单词映射到一个连续的向量空间中，使得相似的单词在向量空间中距离更近，而不相似的单词距离更远。

以下是预训练Embedding模型的基本步骤：

1. 构建语料库：首先需要准备大量的语料库数据，语料库可以是任何文本数据，比如新闻、百科、小说等。在构建语料库时，需要对文本进行预处理，例如去除停用词、分词、转换为小写字母等。
2. 定义模型架构：接下来需要定义模型的架构，通常使用神经网络模型，如基于CBOW或Skip-gram的模型。其中CBOW（Continuous Bag of Words）模型是预测一个单词的前后文本，而Skip-gram模型则是预测一个单词周围的文本。
3. 训练模型：使用预处理后的语料库训练Embedding模型。训练时，将每个单词表示为一个向量，然后将其输入到模型中进行训练，得到一个训练好的嵌入矩阵。
4. 应用Embedding模型：在应用时，将嵌入矩阵加载到模型中，然后将输入的单词转换为嵌入向量进行表示，从而进行后续的自然语言处理任务。



以下是一些常用的公开预训练Embedding模型：

1. GloVe: Global Vectors for Word Representation，由斯坦福大学开发，基于全局词频和局部词频的加权矩阵分解方法，提供了多个预训练模型，包括50维、100维、200维和300维等。
2. Word2Vec，由Google开发，使用基于Skip-gram和CBOW的神经网络模型，在多个大型语料库上进行了预训练，提供了多个预训练模型，包括50维、100维和300维等。
3. FastText，由Facebook开发，使用基于n-gram和CBOW的神经网络模型，在多个大型语料库上进行了预训练，提供了多个预训练模型，包括300维和600维等。

### 6、大模型训练中的”大模型“也是Embeddgings model吗？

> 进一步阅读参考：[大模型训练技术笔记总结](https://zhuanlan.zhihu.com/p/610139027)

一般来说，在深度学习中，“大模型”通常指的是具有大量参数和复杂结构的神经网络模型，而Embeddings Model只是神经网络模型中的一种组成部分，是用来将离散的词语转换成向量表示的方法。因此，在大模型训练中，Embeddings Model虽然是重要的组成部分，但不是唯一的重要组成部分，其他的组成部分如Transformer等也同样重要。

**在实际应用中，通常采用大型的神经网络模型，如GPT、BERT等，这些模型都拥有数十亿到数千亿的参数**，需要用到大量的训练数据和计算资源来训练和优化。这些模型通常需要进行分布式训练，使用多个GPU或TPU来加速训练过程，并且需要一定的专业知识和经验才能进行调优和优化。因此，在大模型训练中，除了Embeddings Model之外，还有很多技术和工具需要进行配合和使用。





## 使用chatGPT

### OPEN AI API

在程序中接入 API 只需要两步：

1. 在网页上获取 API key
2. 用这个 key 向接口去发送一个 POST 请求

使用如下例子：

```shell
curl -k https://api.openai.com/v1/completions -f \
  -H 'Content-Type: application/json' \
  -H 'Authorization: Bearer ***************************************************' \
  -d '{
  "model": "text-davinci-003",
  "prompt": "人类会有希望吗？",
  "max_tokens": 100,
  "temperature": 0
}'
```

- 上面代码使用的模型叫做 text-davinci-003，效果上有区别。真正意义上 ChatGPT 的 API 尚未发布。
- 计费的标准是 token，可以理解为字数，1000 个 tokens 大约是 750 个词，包括提问和回答。 调用的 max_tokens 参数就是限制一次请求所消耗的最大 token 数
- 注册送了 18 刀的配额，算了一下大概也就问 1000 多个问题



### prompt

提示通常由多个部分组成：

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230403154420920.png" alt="image-20230403154420920" style="zoom:50%;" />



> 图片来源：https://www.pinecone.io/learn/langchain-prompt-templates/

#### 充当英英词典(附中文解释)

> 我想让你充当英英词典，对于给出的英文单词，你要给出其中文意思以及英文解释，并且给出一个例句，此外不要有其他反馈，第一个单词是“Hello"

#### 充当前端智能思路助手

**替代**：百度、谷歌人工搜索

> 我想让你充当前端开发专家。我将提供一些关于Js、Node等前端代码问题的具体信息，而你的工作就是想出为我解决问题的策略。这可能包括建议代码、代码逻辑思路策略。我的第一个请求是“我需要能够动态监听某个元素节点距离当前电脑设备屏幕的左上角的X和Y轴，通过拖拽移动位置浏览器窗口和改变大小浏览器窗口。”

#### 担任面试官

**示例**：Java 后端开发工程师、React 前端开发工程师、全栈开发工程师、iOS 开发工程师、Android开发工程师等。 [回复截图请看这里](https://github.com/PlexPt/awesome-chatgpt-prompts-zh/blob/main/pic/p2.png)

> 我想让你担任Android开发工程师面试官。我将成为候选人，您将向我询问Android开发工程师职位的面试问题。我希望你只作为面试官回答。不要一次写出所有的问题。我希望你只对我进行采访。问我问题，等待我的回答。不要写解释。像面试官一样一个一个问我，等我回答。我的第一句话是“面试官你好”

prompt 相关参考：

- [100条ChatGPT prompts](https://zhuanlan.zhihu.com/p/607471255)
- [ChatGPT 中文调教指南](https://github.com/PlexPt/awesome-chatgpt-prompts-zh)

#### 参数 temperature

prompt 提供两个一个 temperature 参数，范围0~1：

- 设置为 0 将帮助您生成最可预测的答案，从而提供更高的准确率。
- 设置为 1 可以为用户提供最随机的答案，提供创意但准确率较低。

以下是针对多个用例的一些推荐温度设置，以展示如何在不同场景中使用这些参数:

- Code Refactoring ： 0.2 输出更具确定性和针对性。这对于生成语法准确的代码很有用。
- Data Analysis Scripting： 0.2
- Chatbot/Summarize： 0.5 这两种情况都需要多样性和一致性。为了平衡这些因素，使用 0.5 可以帮助 AI 输出更具吸引力和自然。
- Code Generation:0.8 这将使 ChatGPT 能够生成更多令人惊讶和创新的代码，这对于构建复杂的程序非常有价值。

## CodeGPT

目前有一些利用 chatGPT API 的代码辅助工具，提供的能力包括 find bug，重构，生成单元测试：

- [VS codeGPT插件](https://www.codegpt.co/docs/intro)
- [ChatGPT-CodeReview](https://github.com/anc95/ChatGPT-CodeReview)
- [error-prone是经典的find bug产品](https://github.com/google/error-prone)
- api key: ***************************************************

### GitHub Copilot

该编辑器具备以下功能：

- 集成了 GPT-4（微软亲儿子，必须安排上）；
- GitHub Copilot Chat（边写代码边跟 AI 对话）；
- Copilot for Pull Requests（AI 协助处理 PR）；
- Copilot for Docs（智能文档系统）；
- Copilot for CLI（让命令行用起来更智能）；
- Copilot Voice（直接语音生成代码）

可以尝试下[cursor](https://www.cursor.so/)

### Shell GPT

该工具直接在命令行中集成了 ChatGPT，通过它，可以帮助你快速编写 shell 命令、代码片段编写、Git 提交信息、文档、注释等内容。

用法主要有以下几种：

-  获取某个任务的执行指令；
-  直接转换时间、距离、重量、温度等单位；
- 在命令行终端搜索 shell 用法；
- 自动生成代码并存储；
- 在命令跟 ChatGPT 对话，并迭代优化。

GitHub：github.com/TheR1D/shell_gpt

##  Langchain

LangChain是 LLM 底层能力的封装，是一种 Prompt Engineering或者说是Prompt-Ops。

- 它可以接入各种不同LLM的服务，抽象了各种大语言模型的调用
- 它可以创建各种PromptTemplate，实现定制化的Prompt模版
- 它可以创建链来组合调用PromptTemplate
- 它可以通调用各种工具，实现GPT-3目前不擅长的事情，比如搜索/数学/链接私有数据库/Python代码
- 它可以使用代理， 驱动LLM 来确定采取哪些行动以及采取何种顺序。动作可以是使用工具并观察其输出，也可以是返回给用户。
- 它可以通过它的Memory模块，来实现对话历史的建模。

 参考：

- [Prompt Engineering and LLMs with Langchain (推荐)](https://www.pinecone.io/learn/langchain-prompt-templates/)
- [Tutorial: ChatGPT Over Your Data](https://blog.langchain.dev/tutorial-chatgpt-over-your-data/)
- [ Question Answering with LangChain and Qdrant without boilerplate](https://qdrant.tech/articles/langchain-integration/)
- [使用langchain打造自己的大型语言模型](https://blog.csdn.net/weixin_42608414/article/details/129493302)
- [ChatGPT分享-如何开发一个LLM应用](https://www.51cto.com/article/749570.html)

### 向量数据库

> [What is a Vector Database?](https://www.pinecone.io/learn/vector-database/#getting-started-with-vector-databases)

- [zilliz](https://zilliz.gitee.io/welcome/)
- [qdrant](https://github.com/qdrant/qdrant)

### 向量召回

推荐系统如何根据已有的用户画像和内容画像去推荐，涉及到两个关键问题：召回和排序。

**“召回（match）”指从全量信息集合中触发尽可能多的正确结果，并将结果返回给“排序”**。

- [推荐系统Embedding向量召回在即刻的工程实践](https://zhuanlan.zhihu.com/p/160120292)
- [电商搜索排序：召回](https://zhuanlan.zhihu.com/p/395626828)

### 方案

#### 1、垂直行业知识库

>  参考 [垂直行业知识库和ChatGPT搭建行业问答机器人的技术架构](https://zhuanlan.zhihu.com/p/613842066)

架构说明

<img src="/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20230324104446144.png" alt="image-20230324104446144" style="zoom:50%;" />

**1、Embeddgings model选择**

要将领域知识向量化，需要有Embeddings model，最简单的方案是使用OpenAI的Embeddings API。

由于OpenAI的Embeddings model是通用模型，对垂直行业并不是最适合的，会出现回答不准确的情况。如果数据量较大，需要反复调用Embeddgins API，效率较低、成本较高。

可以考虑自己基于知识库自训练或基于一些现成的模型，HuggingFace上有很多Embeddings model可供参考使用。

**2、向量数据库选择**

向量数据库在相似文本搜索、个性化推荐、相似图片搜索等都有很好的应用场景。开源的向量数据库有qdrant，weaviate，milvus，elasticsearch等，推荐qdrant。

qdrant对常用的向量数据库的测试报告：[https://qdrant.tech/benchmarks/](https://link.zhihu.com/?target=https%3A//qdrant.tech/benchmarks/)

**3、LLM框架**

LangChain及LlamaIndex (原*GPT Index*) 这样的LLM框架，封装了很多LLM的工具，可以极大程度提升与LLM的集成效率。

LlamaIndex (原GPT Index) 入门门槛更低，入门文档也写得比较详尽。LangChain更为强大灵活，qdrant对LangChain的集成更好。

可以参考

-  https://news.ycombinator.com/item?id=34568343
- https://www.pinecone.io/learn/langchain-prompt-templates/

**4、调用OpenAI，构建的Prompt模板**

Answer the question as truthfully the question as truthfully as possible using the provided text, and if the answer is not contained within the text below, say "I don't know"

Context:
向量数据库科搜索结果的TopN 知识的拼接

Q: 用户提问

#### 2、本地化部署

参考:

- [快速私有化部署](https://twitter.com/xinqiu_bot/status/1644962082562134021)

## GPT Engineer

GPT-Engineer 通过 prompt 生成整个代码库，参考:

- [The Dawn of AI-Powered Software Development with GPT-engineer](https://medium.com/geekculture/dawn-ai-powered-software-development-gpt-engineer-artificial-intelligence-code-openai-bard-d7fe530ac1d)

官方给出了一个通过 prompt 生成贪吃蛇的 example：

```
We are writing snake in python. MVC components split in separate files. Keyboard control.
```

需要python3和gpt4，本地尝试了gpt3，被限流了：

```properties
(venv) ➜  gpt-engineer git:(main) gpt-engineer projects/my-new-project
INFO:openai:error_code=model_not_found error_message="The model 'gpt-4' does not exist" error_param=model error_type=invalid_request_error message='OpenAI API error received' stream_error=False
Model gpt-4 not available for provided API key. Reverting to gpt-3.5-turbo. Sign up for the GPT-4 wait list here: https://openai.com/waitlist/gpt-4-api
INFO:openai:error_code=None error_message='You exceeded your current quota, please check your plan and billing details.' error_param=None error_type=insufficient_quota message='OpenAI API error received' stream_error=False
Traceback (most recent call last):
```

## Fine-tune

清华大学发布P-Tuning v2版本，其重点解决了**Prompt tuning在小模型上效果不佳的问题**

chatglm使用p tuning v2微调代码 https://github.com/THUDM/ChatGLM-6B/tree/main/ptuning

## 每月热点

### Local AI

Running LLM’s Locally: A Step-by-Step Guide  

- [使用localAI打造本地机器人](https://mudler.pm/posts/smart-slackbot-for-teams/)

###   Elasticsearch RAG

ES 8.0 引入了 **ELSER** (Elastic Learned Sparse Encoder)模型，一种开箱即用的语义搜索，不过是收费产品，需要白金权限。

- [Elasticsearch案例：百行代码实现腾讯ES帮助文档的RAG](https://cloud.tencent.com/developer/article/2354365)
- [Elastic Learned Sparse Encoder 介绍](https://www.elastic.co/search-labs/blog/articles/may-2023-launch-sparse-encoder-ai-model)
- [whats-new-elasticsearch-8-8-0](https://www.elastic.co/blog/whats-new-elasticsearch-8-8-0)
- [揭秘 ChatGPT：构建 AI 搜索的不同方法](https://www.elastic.co/search-labs/blog/articles/demystifying-chatgpt-methods-building-ai-search)
- [Elasticsearch ELSER 文档](https://www.elastic.co/guide/en/machine-learning/8.11/ml-nlp-elser.html)
- [medium Hybrid Search: SPLADE (Sparse Encoder)](https://medium.com/@sowmiyajaganathan/hybrid-search-splade-sparse-encoder-neural-retrieval-models-d092e5f46913)

Dense Retrieval vs Sparse Retrieval

- Dense vector retrieval 使用BERT等语言模型创建embeddings，然后余弦相似度等相似性度量来检索相关文档
- Sparse Retrieval 使用Okapi BM25 为每个文档-查询对生成相关性得分，它通过索引术语及其出现次数来减少搜索空间，从而实现高效检索

### [langflow](https://github.com/langflow-ai/langflow)

Langflow is a dynamic graph where each node is an executable unit. Its modular and interactive design fosters rapid experimentation and prototyping, pushing hard on the limits of creativity.

## 参考

- [Question Answering with LangChain and Qdrant without boilerplate](https://link.zhihu.com/?target=https%3A//qdrant.tech/articles/langchain-integration/)
- **[基于向量数据库与GPT3.5的通用本地知识库方案](https://github.com/GanymedeNil/document.ai)**
- [从零开始构建基于ChatGPT的嵌入式(Embedding)本地医疗客服问答机器人模型](https://juejin.cn/post/7212616585768370235)
- 
