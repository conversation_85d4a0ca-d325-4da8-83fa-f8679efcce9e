## 概述

本篇文章涉及了 RAG 流程中的数据拆分、向量化、查询重写、查询路由等等，在做 RAG 的小伙伴一定知道这些技巧的重要性。
推荐仔细阅读，建议收藏，多读几遍，好好实践。



## 知识图谱

参考： https://github.com/codefuse-ai/codefuse-chatbot/issues/3

对于知识图谱的RAG，核心在于需要一个已经构建好的知识图谱。对于此问题，可以先利用llm基于提供的refs文本来构建知识图谱，然后再根据query来进行查询。chatbot基于llama_index框架实现知识图谱构建以及结合知识图谱的回答,以下是内部版本的相关展示：
构建得到的知识图谱三元组为

```
(水王子, 娶, 王默)
(水王子, 是, 真心喜欢)
(她, 穿着, 洁白婚纱)
(水王子, 对, 王默)
(水王子, 喜欢, 王默)
(水王子, 选择, 灵公主)
(水王子, 喜欢, 温柔的女生)
(灵公主, 是, 仙子)
调用chatbot，基于该知识图谱，得到的回答为： 水王子娶了王默
```



## Rerank

**重排序模型会计算候选文档列表与用户问题的语义匹配度，根据语义匹配度重新进行排序，从而改进语义排序的结果**。其原理是计算用户问题与给定的每个候选文档之间的相关性分数，并返回按相关性从高到低排序的文档列表。常见的 Rerank 模型如：Cohere rerank、bge-reranker 等。

参考：

- [让RAG更进一步的利器：教你使用两种出色的Rerank排序模型](https://developer.volcengine.com/articles/7381768919595679782)
- [土猛的员外-Rerank——RAG中百尺竿头更进一步的神器，从原理到解决方案](https://www.luxiangdong.com/2023/11/06/rerank/)
- [土猛的员外-像光速一样搜索——HNSW算法介绍](https://www.luxiangdong.com/2023/11/06/hnsw/)
- [土猛的员外-提升RAG——选择最佳Embedding和重新排名模型](https://www.luxiangdong.com/2023/11/06/rerank-ev/)



## 扣子

参考

- [快速搭建对接企业私有知识与CRM系统的AI助手机器人](https://developer.volcengine.com/articles/7370376462484013065)

## 参考

- [Advanced RAG Techniques: an Illustrated Overview](https://pub.towardsai.net/advanced-rag-techniques-an-illustrated-overview-04d193d8fec6)
- [向量检索实验室-最全的RAG技术概览](https://discuss.nebula-graph.com.cn/t/topic/14848)
- [NebulaGraph 技术社区-关于 LLM 和图、图数据库的那些事](https://mp.weixin.qq.com/s/EoTl8_VwYwoqp8xR-Qzv2Q)

