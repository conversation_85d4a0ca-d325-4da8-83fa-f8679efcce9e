> 注：本文经 gemin 润色过，实际没有它说的那么强大，一点一点强

# Wapilot 介绍

[Wapilot](http://wapilot-website.wse-test-middleware.wke-office.test.wacai.info/) 是基于一款基于IDEA 插件而开发 AI Coding工具，提供代码智能生成、智能问答、多文件修改、编程智能体等能力，为大家带来高效、流畅的编码体验。



## 产品特色

- 借鉴业界领先的 AI coding 产品设计，交互体验上符合大家使用习惯
- 支持一键代码合并，无需手动复制、粘贴AI返回的代码(*注：目前只支持Java语言，后续会支持其他语言*)
- 支持AI一键生成单元测试、代码解释、代码优化、HTTP API接口测试
- 提供 Agent 模式，支持自动收集上下文和推理
- IDEA 原生实现，而不是基于WebView，更好的性能表现
- 未来会探索解决公司内个性化需求，比如解决 Cursor CodeBase 没有包含 jar 包的痛点



## 主要功能

下面按照使用场景来介绍功能 

### 代码生成

在任何 Java文件都可以点击右键，出现代码解释、代码优化、生成API测试、生成单元测试，作为AI coding基础能力，这里就不过多介绍：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250626175220380.png" alt="image-20250626175220380" style="zoom:50%;" />

### 方法补全 

如果你AI coding 新手，担心 AI一下子把你代码乱弄，方法补全是一个好的开始，比如在 wapilot 项目中我需要记录环境信息，我先定义了一个方法，然后让AI帮我补全：

```java
 //方法定义
 protected String getSystemPrompt() {

 }
```

输入的提示词：

```
帮我补全 TaskExecutor 的 getSystemPrompt()，创建并设置 SystemContext，然后渲染模板 
```

LLM输出的结果:

<img src="]'[]" alt="image-20250626170149033" style="zoom:50%;" />

点击 Apply 按钮出现 diff 页面，可以【接受】或者【拒绝】LLM返回的代码

![image-20250627102018213]()











### 完成真实的需求

这里我讲介绍如何实现一个真实需求， 在Wapilot项目中如何通过AI实现IDEA和挖财CAS打通，实现一键登录，产品效果：

<img src="" alt="image-20250626171638800" style="zoom:50%;" />



这里通过一个视频介绍一下，建议大家看下(视频有声音)







## 后续规划

wapilot 目前还存在一些bug和问题，后续我们会重点专注于：

1. 完善系统提示词和内建工具，目前因为系统提示词还不够完善，偶尔会出现幻觉导致输出格式存在问题
2. 代码索引构建，支持CodeBase级别的RAG
3. MCP 工具的建设

大家的有好的建议也可以找@柏仁 反馈



# Wapilot 安装指南

## 下载链接
我们对不同IDEA版本提供了兼容支持：

| IDEA 版本             | 下载版本                                                     |
| --------------------- | ------------------------------------------------------------ |
| 2024.1 到最新版本     | [wapilot-jetbrains-0.0.7-241.zip](http://domino-file-web.wse-test-middleware.wke-office.test.wacai.info/file/fastDownload/quantum-dump/wapilot-jetbrains-0.0.7-241.zip) |
| 2023.3 版本           | [wapilot-jetbrains-0.0.7-233.zip](http://domino-file-web.wse-test-middleware.wke-office.test.wacai.info/file/fastDownload/quantum-dump/wapilot-jetbrains-0.0.7-233.zip) |
| 2022.2 到 2023.2 版本 | [wapilot-jetbrains-0.0.7-223.zip](http://domino-file-web.wse-test-middleware.wke-office.test.wacai.info/file/fastDownload/quantum-dump/wapilot-jetbrains-0.0.7-223.zip) |




## 安装步骤





![image-20250627144656685]()
