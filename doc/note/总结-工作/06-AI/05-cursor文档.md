## 概述

我创建了一个 fastDB 采用cursor 辅助编程，这里是一些记录



## 1、QueryParams 增加feild过滤

最开始忘记在QueryParams增加按照field过滤，准备让cursor增加这个feature，但过程还是遇到波折。

第一次prompt：**调整 @QueryParams.java  增加List fields参数，只返回指定的filedName，你还需要修改对应的查询逻辑**。

结果curosr去修改了TSMReader文件，都没有改对地方。

第二次prompt：**调整 @QueryParams.java  增加List fields参数，只返回指定的filedName，你还需要修改对应的查询逻辑，包括TSIReader，过滤指定的filed。**

结果curosr虽然是修改TSIReader文件，但是通过if判断的方式，而不是对list取交集的方式。

第三次prompt:  **调整 @QueryParams.java  增加List fields参数，FastDB查询时候只返回filedName对应的结果，你还需要修改TSIReader.getSeriesIds()查询逻辑，增加对field 返回的 fieldSeriesIds 做交集，可以参考 @TSIWriter.java。**

最后一次终于该对了，说明prompt的细节程度对于正确率非常重要

## 2、需求：为tsi文件增加header

第一次prompt：

> 修改TSIWriter 的写入逻辑，为tsi文件增加header，增加magic,version,minTime和maxTime，和tsm文件header一样。可以参考 @TSMWriter.java  @TSIWriter.java  

发现会删除之前写的注释，逻辑是没有太大问题，可以把不要删除已有注释添加到系统rule里

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241023173515091.png" alt="image-20241023173515091" style="zoom:50%;" />

被删掉注释的代码：

```java
    private static Map<String, Map<String, List<Integer>>> createInvertedIndex(
            Map<Meta, List<OffsetEntry>> data) {
        Map<String, Map<String, List<Integer>>> invertedIndex = Maps.newHashMap();

        for (Meta meta : data.keySet()) {
            SeriesKey key = meta.key;
            String metric = key.getMetric();
            addToInvertedIndex(invertedIndex, Constants.INDEX_METRIC, metric, meta.seriesId);
            for (Map.Entry<String, String> tag : key.getTags().entrySet()) {
                String tagKey = tag.getKey();
                String tagValue = tag.getValue();
                addToInvertedIndex(invertedIndex, tagKey, tagValue, meta.seriesId);
            }
            log.info("write field index filed:{},id:{},key:{}", key.getFieldName(), meta.seriesId, meta.key);
            addToInvertedIndex(invertedIndex, Constants.INDEX_FIELDS, key.getFieldName(), meta.seriesId);
        }

        return invertedIndex;
    }
```

重新产生注释：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241023191303327.png" alt="image-20241023191303327" style="zoom:50%;" />

## 3、需求：tsi数据合并

之前Shard只会加载最新的tsi数据，让cursor改成list，并修改对应的查询语句。

Prompt:  

>  修改 FastDB.loadShard()方法逻辑，TSIReader 也应该像 TSMReader 一样是一个list，在queryShardData()会需要比较params 中的开始结束时间是否在 TSIReader list中TSIReader，如果是则读取，然后再把数据合并在一起 

回答：

> 我理解您的需求。我们需要修改 FastDB 类中的 loadShard() 和 queryShardData() 方法，使 TSIReader 成为一个列表，并在查询时根据时间范围选择合适的 TSIReader。以下是修改后的 FastDB 类的相关部分：

代码没有问题。

## 4、bugfix: Shard flush()并发问题

Prompt:  

> 目前 Shard flush()和 write()对于data存在并发问题，可以考虑使用AtomicReference包装data，flush()的时候创建一个新map替换到的，避免flush()导致data数据丢失 @Shard.java   注意不要删除代码中已经存在的注释

回答：

> 理解了，我会修改 Shard 类以解决并发问题。我们将使用 AtomicReference 来包装 data，并在 flush() 方法中创建一个新的 Map 来替换旧的 Map。
>
> 主要的修改包括：
>
> 1. 将 data 字段改为 AtomicReference<Map<SeriesKey, List<DataValue>>>。
> 2. 在 write 方法中，我们现在使用 data.get() 来获取当前的 Map。
> 3. 在 flush 方法中，我们使用 data.getAndSet(Maps.newHashMap()) 来原子地替换当前的 Map 为一个新的空 Map，并获取旧的 Map 用于写入文件。
> 4. 在 query 方法中，我们使用 data.get() 来获取当前的 Map 进行查询。

修改的代码：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241024120228228.png" alt="image-20241024120228228" style="zoom:50%;" />

这里只需要使用getAndSet()方法，并不需要比较。



## 5、代码重构: 把多个分散的字段合并到一个Config类

prompt: 

> 把 ShardMaintenanceTask 的 EIGHT_HOURS_IN_MILLIS 和FastDB构造函数中的参数 String dataDir，都抽取到一个FastDBConfig类中，传递到FastDB构造函数中，FastDBConfig中的flushInterval默认值可以是EIGHT_HOURS_IN_MILLIS

回答：

> 好的，我会创建一个 FastDBConfig 类，并修改 FastDB 和 ShardMaintenanceTask 类以使用这个新的配置类。

## 6、bugfix: 正确使用 @lombok.Builder 默认值

prompt: 

> FastDBConfig使用了@Data和@Builder，我想给  FastDBConfig flushInterval 设置默认值 ，应该如何设置

回答：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241024175446797.png" alt="image-20241024175446797" style="zoom:50%;" />

## 7、需求：新增定时任务

prompt:

> 创建一个类，这个类会定期做一些检查工作，比如扫描FastDB内所有Shard是否需要flush，如果需要则调用flush()，判断方法是检查Shard的minTime落后当前时间8个小时

创建的代码：

```java
@Slf4j
public class ShardMaintenanceTask {
    private final FastDB fastDB;
    private final ScheduledExecutorService scheduler;
    private final FastDBConfig config;

    public ShardMaintenanceTask(FastDB fastDB) {
        this.fastDB = fastDB;
        this.config = fastDB.getConfig();
        this.scheduler = Executors.newScheduledThreadPool(2);
    }

    public void start() {
        scheduler.scheduleAtFixedRate(this::checkAndFlushShards, 0, 2, TimeUnit.MINUTES);
    }
}
```



## 8、需求：定时任务增加shard过期删除操作

prompt:

> 为 ShardMaintenanceTask 增加一个任务，检查fastdb目录是否过期，只保留最近30天的shard，如果过期删除目录，可以把过期检查封装在Shard类的方法中

在 ShardMaintenanceTask 类中新增的代码：

```java
private void checkAndDeleteExpiredShards() {
        log.info("Checking for expired shards");
        Iterator<Map.Entry<Integer, Shard>> iterator = fastDB.getShards().entrySet().iterator();
        while (iterator.hasNext()) {
            Map.Entry<Integer, Shard> entry = iterator.next();
            Shard shard = entry.getValue();
            if (shard.isExpired()) {
                log.info("Deleting expired shard for date: {}", entry.getKey());
                if (shard.delete()) {
                    iterator.remove();
                    log.info("Successfully deleted expired shard for date: {}", entry.getKey());
                } else {
                    log.error("Failed to delete expired shard for date: {}", entry.getKey());
                }
            }
        }
    }
```

## 9、帮我取类名

prompt:

> 我有个类封装了 SeriesKey 和 List<Block>，建议叫什么名字。

回答：

> SeriesData、SeriesBlockContainer

## 10、添加README.md内容

第一个字打错了也没问题。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241025163115213.png" alt="image-20241025163115213" style="zoom:50%;" />

## 11、新功能：修改FastDB构造函数逻辑

prompt: 

> 修改FastDB构造函数，通过base目录的子目录实例化所有Shard并写入到map中

回答：

> 好的，我会修改 FastDB 的构造函数，使其能够通过基础目录的子目录来实例化所有的 Shard 并将它们写入到 map 中。以下是修改后的 FastDB 类：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241025163705859.png" alt="image-20241025163705859" style="zoom:50%;" />

## 12、帮你产生git commit 信息

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241025165704513.png" alt="image-20241025165704513" style="zoom:50%;" />

## 13、代码优化

prompt: 这段代码是否存在优化空间：

```java
    public void merge() {
        List<Shard> shardList = shards.values().stream().sorted((s1, s2) -> {
            int x = s1.getDate();
            int y = s2.getDate();
            return (x < y) ? -1 : ((x == y) ? 0 : 1);
        }).collect(Collectors.toList());

        for (Shard shard : shardList) {
            shard.merge();
        }
    }
```

回答：这段代码可以更简洁。这里是优化后的版本：

```java
    public void merge() {
        shards.values().stream()
            .sorted(Comparator.comparingInt(Shard::getDate))
            .forEach(Shard::merge);
    }
```

主要改进：

- 使用 Comparator.comparingInt() 替代手写的比较器，更简洁且可读性更好
- 直接使用 forEach 而不是收集到 List 再遍历
- 使用方法引用 Shard::merge 代替 lambda
- 这个版本功能完全相同，但代码更简洁优雅。

## 问题

cursor 被锁小工具

- https://github.com/fly8888/cursor_machine_id
- https://github.com/yuaotian/go-cursor-help

