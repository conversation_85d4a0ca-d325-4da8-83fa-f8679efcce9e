## autodev-codebase

[autodev-codebase](https://github.com/anrgct/autodev-codebase) 核心包括：

- 索引的构建
- 查询接口
- Tree-sitter 处理
- CLI 处理

### 运行

安装依赖和编译

```
npm install
npm run build
```

运行

```
npm run dev --log-level=debug 
```

但是加载 wasm报错：

```
Failed to load language parser for javascript: bad export type for 'tree_sitter_python_external_scanner_create': undefined
Failed to load parser for extension js: bad export type for 'tree_sitter_python_external_scanner_create': undefined
No parser available for file extension: js
```



### 索引构建

相关代码都在  src/code-index 目录下

##### 类定义

![image-20250630162411517](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250630162411517.png)

##### API 定义

```typescript
import { createNodeDependencies } from '@autodev/codebase/adapters/nodejs'
import { CodeIndexManager } from '@autodev/codebase'

const deps = createNodeDependencies({ 
  workspacePath: '/path/to/project',
  storageOptions: { /* ... */ },
  loggerOptions: { /* ... */ },
  configOptions: { /* ... */ }
})

const manager = CodeIndexManager.getInstance(deps)
await manager.initialize()
await manager.startIndexing()
```



##### 主要构建阶段

##### 1. 初始化阶段 manager.ts

索引构建从 `CodeIndexManager` 开始，通过单例模式管理整个流程： manager.ts: 

```typescript
public async initialize(): Promise<{ requiresRestart: boolean }> {
		// 1. ConfigManager Initialization and Configuration Loading
		if (!this._configManager) {
			this._configManager = new CodeIndexConfigManager(this.dependencies.configProvider)
			await this._configManager.initialize()
		}
		// Load configuration once to get current state and restart requirements
		const { requiresRestart } = await this._configManager.loadConfiguration()

		// 2. Check if feature is enabled
		if (!this.isFeatureEnabled) {
			if (this._orchestrator) {
				this._orchestrator.stopWatcher()
			}
			return { requiresRestart }
		}

		// 3. CacheManager Initialization
		if (!this._cacheManager) {

		}

		// 4. Determine if Core Services Need Recreation
		const needsServiceRecreation = !this._serviceFactory || requiresRestart

		if (needsServiceRecreation) {
			// Stop watcher if it exists
			if (this._orchestrator) {
				this.stopWatcher()
			}

			// (Re)Initialize service factory
			this._serviceFactory = new CodeIndexServiceFactory(
				this._configManager,
				this.workspacePath,
				this._cacheManager,
				this.dependencies.logger,
			)

			const ignoreInstance = ignore()
			const ignoreRules = this.dependencies.workspace.getIgnoreRules()
			ignoreInstance.add(ignoreRules)

			// (Re)Create shared service instances  
			const { embedder, vectorStore, scanner, fileWatcher } = this._serviceFactory.createServices(
				this.dependencies.fileSystem,
				this.dependencies.eventBus,
				this._cacheManager,
				ignoreInstance,
				this.dependencies.workspace,
				this.dependencies.pathUtils
			)

			// (Re)Initialize orchestrator
			this._orchestrator = new CodeIndexOrchestrator(
				this._configManager,
				this._stateManager,
				this.workspacePath,
				this._cacheManager,
				vectorStore,
				scanner,
				fileWatcher,
				this.dependencies.logger,
			)

			// (Re)Initialize search service
			this._searchService = new CodeIndexSearchService(
				this._configManager,
				this._stateManager,
				embedder,
				vectorStore,
			)
		}

		// 5. Handle Indexing Start/Restart
		// The enhanced vectorStore.initialize() in startIndexing() now handles dimension changes automatically

```

初始化过程包括配置加载、服务创建和组件协调。

##### 2. 向量存储初始化 orchestrator.ts

在开始扫描前，系统会初始化向量存储并处理集合创建：src/code-index/orchestrator.ts：

```typescript
		this.stateManager.setSystemState("Indexing", "Initializing services...")
		this.info('[CodeIndexOrchestrator] 🚀 开始索引进程...')
		try {
			this.info('[CodeIndexOrchestrator] 💾 初始化向量存储...')
			const collectionCreated = await this.vectorStore.initialize()
			this.info('[CodeIndexOrchestrator] ✅ 向量存储初始化完成, 新集合创建:', collectionCreated)

			if (collectionCreated) {
				this.info('[CodeIndexOrchestrator] 🗑️ 清理缓存文件...')
				await this.cacheManager.clearCacheFile()
				this.info('[CodeIndexOrchestrator] ✅ 缓存文件已清理')
			}

			this.stateManager.setSystemState("Indexing", "Services ready. Starting workspace scan...")
			this.info('[CodeIndexOrchestrator] 📁 开始扫描工作区:', this.workspacePath)

				this.stateManager.reportBlockIndexingProgress(cumulativeBlocksIndexed, cumulativeBlocksFoundSoFar)
			}
			this.info('[CodeIndexOrchestrator] 🔍 开始扫描目录...')
			const result = await this.scanner.scanDirectory(
				this.workspacePath,
				(batchError: Error) => {
					this.error(
						`[CodeIndexOrchestrator] ❌ 扫描批次错误: ${batchError.message}`,
						batchError,
					)
				},
				handleBlocksIndexed,
				handleFileParsed,
			)
			this.info('[CodeIndexOrchestrator] ✅ 目录扫描完成')
			this.info('[CodeIndexOrchestrator] 👀 开始文件监控...')
			await this._startWatcher()
			this.info('[CodeIndexOrchestrator] ✅ 文件监控已启动')

			this.stateManager.setSystemState("Indexed", statusMessage)
			this.info('[CodeIndexOrchestrator] ✨ 索引进程全部完成!')

```

并且使用 `FileWatcher` 实现实时文件变更监控，支持持续的增量更新





##### 3. 文件发现与过滤 scanner.ts

`DirectoryScanner` 执行文件发现，使用 `listFiles()` 递归获取所有文件： scanner.ts 的scanDirectory():

```typescript
		const directoryPath = directory
		this.debug(`[Scanner] Scanning directory: ${directoryPath}`)

		// Get all files recursively (handles .gitignore automatically)
		const [allPaths, _] = await listFiles(directoryPath, true, MAX_LIST_FILES_LIMIT, { pathUtils: this.deps.pathUtils, ripgrepPath: 'rg' })

```

然后进行多层过滤：

- 过滤目录项
- 工作区忽略规则：
- 扩展名和忽略模式



##### 4. 并发文件处理 scanner.ts

系统使用并发控制来优化处理性能： scanner.ts:

```typescript
	// Initialize parallel processing tools
		const parseLimiter = pLimit(PARSING_CONCURRENCY) // Concurrency for file parsing
		const batchLimiter = pLimit(BATCH_PROCESSING_CONCURRENCY) // Concurrency for batch processing
		const mutex = new Mutex()

		// Shared batch accumulators (protected by mutex)
		let currentBatchBlocks: CodeBlock[] = []
		let currentBatchTexts: string[] = []
		let currentBatchFileInfos: { filePath: string; fileHash: string; isNew: boolean }[] = []
		const activeBatchPromises: Promise<void>[] = []
    
```

对每个文件执行：

- 大小检查
- 内容读取和哈希计算
- 缓存检查

```typescript
	let totalBlockCount = 0

			parseLimiter(async () => {
				try {
					this.debug(`[Scanner] Processing file: ${filePath}`)
					// Check file size
					const stats = await this.deps.fileSystem.stat(filePath)
					this.debug(`[Scanner] File ${filePath} size: ${stats.size} bytes (limit: ${MAX_FILE_SIZE_BYTES})`)
					if (stats.size > MAX_FILE_SIZE_BYTES) {
						this.debug(`[Scanner] Skipping large file: ${filePath}`)
						skippedCount++ // Skip large files
						return
					}
 
					// Read file content
					const buffer = await this.deps.fileSystem.readFile(filePath)
					const content = new TextDecoder().decode(buffer)

					// Calculate current hash
					const currentFileHash = createHash("sha256").update(content).digest("hex")
					processedFiles.add(filePath)
 
					// Check against cache
					const cachedFileHash = this.deps.cacheManager.getHash(filePath)
					this.debug(`[Scanner] File ${filePath}: cachedHash=${cachedFileHash}, currentHash=${currentFileHash}`)
					if (cachedFileHash === currentFileHash) {
						// File is unchanged
						this.debug(`[Scanner] Skipping unchanged file: ${filePath}`)
						skippedCount++
						return
					}
 
```





##### 5. 代码解析与块提取  scanner.ts

这里需要把文件解析为block:

```typescript

    // File is new or changed - parse it using the injected parser function
    const blocks = await this.deps.codeParser.parseFile(filePath, { content, fileHash: currentFileHash })
    const fileBlockCount = blocks.length
    onFileParsed?.(fileBlockCount)
    codeBlocks.push(...blocks)
    processedCount++
```

block 的定义在 code-index/interfaces/file-processor.ts

```typescript
export interface CodeBlock {
	file_path: string
	identifier: string | null
	type: string
	start_line: number
	end_line: number
	content: string
	fileHash: string
	segmentHash: string
}
```

block 表示从源文件中解析出的代码片段，字段含义：

- `file_path`: 源文件的路径
- `identifier`: **代码块的标识符（如函数名、类名**），可能为 null
- `type`: **代码块的类型（如 "function"、"class"、"method" 等）**
- `start_line` / `end_line`: 代码块在源文件中的起始和结束行号
- `content`: 代码块的实际内容文本
- `fileHash`: 文件的哈希值，用于缓存和增量更新
- `segmentHash`: 代码段的哈希值



当代码块达到阈值时触发批处理:

```typescript
// Check if batch threshold is met
if (currentBatchBlocks.length >= BATCH_SEGMENT_THRESHOLD) {
  // Copy current batch data and clear accumulators
  const batchBlocks = [...currentBatchBlocks]
  const batchTexts = [...currentBatchTexts]
  const batchFileInfos = [...currentBatchFileInfos]
  currentBatchBlocks = []
  currentBatchTexts = []
  currentBatchFileInfos = []

  // Queue batch processing
  const batchPromise = batchLimiter(() =>
    this.processBatch(
      batchBlocks,
      batchFileInfos,
      onError,
      onBlocksIndexed,
    ),
  )
  activeBatchPromises.push(batchPromise)
}
```

 批处理使用 `BatchProcessor` 进行嵌入生成和向量存储：scanner.ts:284-354

```typescript
private async processBatch(
		batchBlocks: CodeBlock[],
		batchFileInfos: { filePath: string; fileHash: string; isNew: boolean }[],
		onError?: (error: Error) => void,
		onBlocksIndexed?: (indexedCount: number) => void,
	): Promise<void> {
		if (batchBlocks.length === 0) return

		// Use BatchProcessor for the actual processing
    // 定义了策略函数，就是一个配置项，包含了embedder，itemToText，后面传给 batchProcessor
		const options: BatchProcessorOptions<CodeBlock> = {
			embedder: this.deps.embedder,
			vectorStore: this.deps.qdrantClient,
			cacheManager: this.deps.cacheManager,
			
			itemToText: (block) => block.content,
			itemToFilePath: (block) => block.file_path,
			getFileHash: (block) => {
				// Find the corresponding file info for this block
				const fileInfo = batchFileInfos.find(info => info.filePath === block.file_path)
				return fileInfo?.fileHash || ""
			},
			
			itemToPoint: (block, embedding) => {
				const normalizedAbsolutePath = this.deps.pathUtils.normalize(this.deps.pathUtils.resolve(block.file_path))
				const stableName = `${normalizedAbsolutePath}:${block.start_line}`
				const pointId = uuidv5(stableName, QDRANT_CODE_BLOCK_NAMESPACE)
				//插入到向量数据库中的数据格式：
				return {
					id: pointId, //基于文件路径和行号生成的唯一标识符
					vector: embedding,
					payload: { //包含文件路径、代码内容、行号等元数据
						filePath: this.deps.workspace.getRelativePath(normalizedAbsolutePath),
						codeChunk: block.content,
						startLine: block.start_line,
						endLine: block.end_line,
					},
				}
			},
			
			getFilesToDelete: (blocks) => {
				// Get files that need to be deleted (modified files, not new ones)
				const uniqueFilePaths = Array.from(new Set(
					batchFileInfos
						.filter((info) => !info.isNew) // Only modified files (not new)
						.map((info) => info.filePath),
				))
				return uniqueFilePaths
			},
			
			onProgress: (processed, total) => {
				// Optional: could emit progress events here if needed
			},
			
			onError: (error) => {
				console.error("[DirectoryScanner] Batch processing error:", error)
				onError?.(error)
			}
		}
		
    //调用批处理器
		const result = await this.batchProcessor.processBatch(batchBlocks, options)
		
		if (result.processed > 0) {
			onBlocksIndexed?.(result.processed)
		}
		
		if (result.errors.length > 0) {
			const errorMessage = `Failed to process batch: ${result.errors.map(e => e.message).join(', ')}`
			console.error(`[DirectoryScanner] ${errorMessage}`)
			onError?.(new Error(errorMessage))
		}
	}
}
```

##### 6. 批处理 batch-processor.ts

1、将输入的项目按照 `BATCH_SEGMENT_THRESHOLD` 分段处理，避免内存问题： src/code-index/processors/batch-processor.ts ：

```typescript
	private async processItemsInBatches<T>(
		items: T[],
		options: BatchProcessorOptions<T>,
		result: BatchProcessingResult
	): Promise<void> {
		// Process items in segments to avoid memory issues and respect batch limits
		for (let i = 0; i < items.length; i += BATCH_SEGMENT_THRESHOLD) {
			const batchItems = items.slice(i, i + BATCH_SEGMENT_THRESHOLD)
			await this.processSingleBatch(batchItems, options, result, i)
		}
	}
```

2、单批次处理流程

```typescript
private async processSingleBatch<T>(
		batchItems: T[],

		let success = false
		let lastError: Error | null = null
		while (attempts < MAX_BATCH_RETRIES && !success) {
				attempts++
			
    }

```

对每个批次执行以下步骤：

**1. 文本提取和嵌入生成 **

```typescript
// Extract texts for embedding
const texts = batchItems.map(item => options.itemToText(item))
// Create embeddings
const { embeddings } = await options.embedder.createEmbeddings(texts)
```

**2. 转换为向量点**： 

```typescript
// Convert to points
const points = batchItems.map((item, index) => 
  options.itemToPoint(item, embeddings[index], startIndex + index)
)
```

**3. 存储到向量数据库**：

```
// Upsert to vector store
await options.vectorStore.upsertPoints(points)
```

**4. 更新缓存**：

```typescript
// Update cache for successfully processed items
for (const item of batchItems) {
  const filePath = options.itemToFilePath(item)
  const fileHash = options.getFileHash?.(item)
  if (fileHash) {
    options.cacheManager.updateHash(filePath, fileHash)
  }

  result.processed++
  options.onProgress?.(result.processed, result.processed + result.failed, filePath)
}
```



**最后，错误处理和进度报告**：

```typescript
if (!success && lastError) {
    result.failed += batchItems.length
    result.errors.push(lastError)

    const errorMessage = `Failed to process batch after ${MAX_BATCH_RETRIES} attempts: ${lastError.message}`
    const batchError = new Error(errorMessage)
    result.errors.push(batchError)
    options.onError?.(batchError)

    // Still report progress for failed items
    for (const item of batchItems) {
      const filePath = options.itemToFilePath(item)
      options.onProgress?.(result.processed, result.processed + result.failed, filePath)
    }
  }
```



### 搜索

#### 入口

搜索可以通过多个入口触发：

- **MCP服务器**: 通过 `search_codebase` 工具调用 http-server.ts:107

  ```typescript
    const searchResults = await this.codeIndexManager.searchIndex(query, Math.min(limit, 50));
  ```

  

- **直接API调用**: 通过 `CodeIndexManager.searchIndex()` 方法 manager.ts:267-272

  ```typescript
  	public async searchIndex(query: string, limit: number = 10): Promise<VectorStoreSearchResult[]> {
  		if (!this.isFeatureEnabled) {
  			return []
  		}
  		this.assertInitialized() //确保CodeIndexManager已经初始化
  		return this._searchService!.searchIndex(query, limit)
  	}
  ```

  

#### 执行过程

搜索通过 `CodeIndexSearchService` 的 searchIndex() 方法执行， code-index/search-service.ts :

1、**查询向量化**: 将搜索查询转换为向量表示

```typescript
// Generate embedding for query
  const embeddingResponse = await this.embedder.createEmbeddings([query])
  const vector = embeddingResponse?.embeddings[0]
  if (!vector) {
    throw new Error("Failed to generate embedding for query.")
  }
```



2、**向量搜索**: 在Qdrant向量数据库中执行相似性搜索

```typescript
const results = await this.vectorStore.search(vector, undefined, minScore)
return results
```



QdrantVectorStore 是vectorStore具体的实现类：

```typescript
	const searchRequest = {
				query: queryVector,
				filter,
				score_threshold: SEARCH_MIN_SCORE,
				limit: MAX_SEARCH_RESULTS,
				params: {
					hnsw_ef: 128,
					exact: false,
				},
				with_payload: {
					include: ["filePath", "codeChunk", "startLine", "endLine", "pathSegments"],
				},
			}	
	
  const operationResult = await this.client.query(this.collectionName, searchRequest)
  const filteredPoints = operationResult.points.filter((p) => this.isPayloadValid(p.payload))

  return filteredPoints.map(point => ({
    id: point.id,
    score: point.score,
    payload: point.payload as Payload
  })) as VectorStoreSearchResult[]
```







## cody

### 介绍

Cody is an open-source AI coding assistant that uses large language models (LLMs) and codebase context to help developers write, understand, and fix code faster. It provides features like:

- **AI Chat**: Context-aware code assistant that understands your codebase
- **Autocomplete**: Code suggestions as you type
- **Auto-edit**: Intelligent code modifications
- **Custom Commands**: Quick actions for common tasks like documenting code or generating tests

Cody supports multiple environments including VS Code (primary client), JetBrains IDEs, and web interfaces. It can connect to various LLM providers including Claude, GPT-4o, Gemini, and others.



## 方案

### 调研

基于AST对代码进行抽象之后生成embedding

```java
import org.eclipse.jdt.core.JavaCore;
import org.eclipse.jdt.core.dom.*;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

public class JavaAstExtractor {

    public static void main(String[] args) throws IOException {
        String projectRoot = "./src/main/java/"; // 假设 Java 文件都在这个目录下
        String[] javaFiles = {
                projectRoot + "com/example/order/OrderController.java",
                projectRoot + "com/example/order/OrderManager.java",
                projectRoot + "com/example/order/OrderService.java",
                projectRoot + "com/example/order/Order.java"
        };

        Map<String, String> extractedCodeInfo = new HashMap<>();

        for (String filePath : javaFiles) {
            String source = new String(Files.readAllBytes(Paths.get(filePath)));
            String fileName = new File(filePath).getName();
            String className = fileName.replace(".java", "");

            // 创建 ASTParser
            ASTParser parser = ASTParser.newParser(AST.JLS17); // 使用最新的 Java 语言规范
            parser.setSource(source.toCharArray());
            parser.setKind(ASTParser.K_COMPILATION_UNIT);

            // 设置解析选项 (可选，但推荐)
            Map<String, String> options = JavaCore.get=>DefaultOptions();
            options.put(JavaCore.COMPILER_COMPLIANCE, JavaCore.VERSION_17);
            options.put(JavaCore.COMPILER_CODEGEN_TARGET_PLATFORM, JavaCore.VERSION_17);
            options.put(JavaCore.COMPILER_SOURCE, JavaCore.VERSION_17);
            parser.setCompilerOptions(options);

            // 解析并获取 CompilationUnit (AST 的根节点)
            final CompilationUnit cu = (CompilationUnit) parser.createAST(null);

            // 用于存储当前文件的提取信息
            StringBuilder fileInfoBuilder = new StringBuilder();

            // 遍历 AST
            cu.accept(new ASTVisitor() {

                Set<String> imports = new HashSet<>();
                String packageName = "";
                String currentClassName = ""; // 用于跟踪当前所在的类

                @Override
                public boolean visit(PackageDeclaration node) {
                    packageName = node.getName().getFullyQualifiedName();
                    fileInfoBuilder.append("Package: ").append(packageName).append("\n");
                    return super.visit(node);
                }

                @Override
                public boolean visit(ImportDeclaration node) {
                    imports.add(node.getName().getFullyQualifiedName());
                    fileInfoBuilder.append("Import: ").append(node.getName().getFullyQualifiedName()).append("\n");
                    return super.visit(node);
                }

                @Override
                public boolean visit(TypeDeclaration node) {
                    // 只处理顶级类型声明，或根据需要处理嵌套类型
                    if (node.getParent() instanceof CompilationUnit || node.getParent() instanceof TypeDeclaration) {
                        currentClassName = node.getName().getIdentifier();
                        fileInfoBuilder.append("\n--- Class/Interface: ").append(currentClassName);
                        if (node.isInterface()) {
                            fileInfoBuilder.append(" (Interface)");
                        } else {
                            fileInfoBuilder.append(" (Class)");
                        }
                        if (node.getSuperclassType() != null) {
                            fileInfoBuilder.append(" extends ").append(node.getSuperclassType().toString());
                        }
                        for (Object superInterface : node.superInterfaceTypes()) {
                            fileInfoBuilder.append(" implements ").append(superInterface.toString());
                        }
                        fileInfoBuilder.append("\n");

                        // 提取注解
                        for (Object modifier : node.modifiers()) {
                            if (modifier instanceof Annotation) {
                                Annotation annotation = (Annotation) modifier;
                                fileInfoBuilder.append("  Annotation: @").append(annotation.getTypeName()).append("\n");
                            }
                        }
                    }
                    return super.visit(node);
                }

                @Override
                public boolean visit(FieldDeclaration node) {
                    for (Object fragment : node.fragments()) {
                        VariableDeclarationFragment varFrag = (VariableDeclarationFragment) fragment;
                        fileInfoBuilder.append("  Field: ")
                                .append(node.getType().toString()).append(" ") // 字段类型
                                .append(varFrag.getName().getIdentifier());   // 字段名
                        // 提取字段修饰符
                        for (Object modifier : node.modifiers()) {
                            if (modifier instanceof Modifier) {
                                fileInfoBuilder.append(" (").append(((Modifier) modifier).getKeyword().toString()).append(")");
                            } else if (modifier instanceof Annotation) {
                                Annotation annotation = (Annotation) modifier;
                                fileInfoBuilder.append(" (@").append(annotation.getTypeName()).append(")");
                            }
                        }
                        fileInfoBuilder.append("\n");
                    }
                    return super.visit(node);
                }

                @Override
                public boolean visit(MethodDeclaration node) {
                    fileInfoBuilder.append("  Method: ")
                            .append(node.getName().getIdentifier()); // 方法名
                    fileInfoBuilder.append("(");
                    // 参数
                    for (int i = 0; i < node.parameters().size(); i++) {
                        SingleVariableDeclaration param = (SingleVariableDeclaration) node.parameters().get(i);
                        fileInfoBuilder.append(param.getType().toString()).append(" ").append(param.getName().getIdentifier());
                        if (i < node.parameters().size() - 1) {
                            fileInfoBuilder.append(", ");
                        }
                    }
                    fileInfoBuilder.append(")");
                    // 返回类型
                    if (node.getReturnType2() != null) {
                        fileInfoBuilder.append(": ").append(node.getReturnType2().toString());
                    }
                    // 修饰符和注解
                    for (Object modifier : node.modifiers()) {
                        if (modifier instanceof Modifier) {
                            fileInfoBuilder.append(" (").append(((Modifier) modifier).getKeyword().toString()).append(")");
                        } else if (modifier instanceof Annotation) {
                            Annotation annotation = (Annotation) modifier;
                            fileInfoBuilder.append(" (@").append(annotation.getTypeName()).append(")");
                        }
                    }
                    fileInfoBuilder.append("\n");
                    return super.visit(node);
                }

                @Override
                public boolean visit(MethodInvocation node) {
                    // 记录方法调用
                    fileInfoBuilder.append("    Method Call: ");
                    if (node.getExpression() != null) {
                        // 如果有调用者 (e.g., object.method())
                        fileInfoBuilder.append(node.getExpression().toString()).append(".");
                    }
                    fileInfoBuilder.append(node.getName().getIdentifier()); // 被调用的方法名
                    fileInfoBuilder.append("(");
                    for (int i = 0; i < node.arguments().size(); i++) {
                        // 记录参数类型或简单的参数字符串
                        fileInfoBuilder.append(node.arguments().get(i).toString());
                        if (i < node.arguments().size() - 1) {
                            fileInfoBuilder.append(", ");
                        }
                    }
                    fileInfoBuilder.append(")\n");
                    return super.visit(node);
                }

                @Override
                public boolean visit(VariableDeclarationFragment node) {
                    // 局部变量声明，可以进一步提取
                    // fileInfoBuilder.append("    Variable Declaration: " + node.getName().getIdentifier() + "\n");
                    return super.visit(node);
                }

                @Override
                public boolean visit(ReturnStatement node) {
                    // 返回语句，可以提取返回的值或类型
                    // fileInfoBuilder.append("    Return: " + (node.getExpression() != null ? node.getExpression().toString() : "") + "\n");
                    return super.visit(node);
                }

                // 可以根据需要重写更多 visit 方法来提取其他类型的节点信息
                // 例如: IfStatement, ForStatement, TryStatement, ClassInstanceCreation (new 关键字) 等
            });
            extractedCodeInfo.put(className, fileInfoBuilder.toString());
        }

        // 打印提取的信息
        extractedCodeInfo.forEach((className, info) -> {
            System.out.println("====== Extracted Info for " + className + " ======");
            System.out.println(info);
            System.out.println("\n");
        });

        // 进一步的步骤：将这些结构化信息转换为适合Embedding的格式
        // 例如：
        // 1. 将 StringBuilder 的内容作为字符串直接输入到预训练的 CodeBERT 等模型。
        // 2. 将提取出的类、方法、字段、调用关系构建成图结构，然后使用 GNN 进行 Embedding。
        // 3. 针对不同类型的元数据（如方法签名、注释）分别生成Embedding，然后进行拼接或融合。
    }
}
```

输出：

```
====== Extracted Info for OrderController ======
Package: com.example.order
Import: org.springframework.web.bind.annotation.RestController
Import: org.springframework.web.bind.annotation.RequestMapping
Import: org.springframework.beans.factory.annotation.Autowired
Import: org.springframework.web.bind.annotation.PostMapping
Import: org.springframework.web.bind.annotation.RequestBody
Import: org.springframework.web.bind.annotation.GetMapping
Import: org.springframework.web.bind.annotation.PathVariable

--- Class/Interface: OrderController (Class)
  Annotation: @RestController
  Annotation: @RequestMapping
  Field: com.example.order.OrderService orderService (@Autowired)
  Field: com.example.order.OrderManager orderManager (@Autowired)
  Method: createOrder(Order order): String (public) (@PostMapping)
    Method Call: orderService.processOrder(order)
  Method: getOrder(Long id): Order (public) (@GetMapping)
    Method Call: orderManager.findOrderById(id)

====== Extracted Info for OrderManager ======
Package: com.example.order
Import: org.springframework.stereotype.Component
Import: java.util.Optional

--- Class/Interface: OrderManager (Class)
  Annotation: @Component
  Method: findOrderById(Long id): Order (public)
  Method: updateOrderStatus(Long orderId, String status): void (public)

====== Extracted Info for OrderService ======
Package: com.example.order
Import: org.springframework.stereotype.Service

--- Class/Interface: OrderService (Class)
  Annotation: @Service
  Method: processOrder(Order order): void (public)
    Method Call: sendOrderConfirmation(order.getId())
  Method: sendOrderConfirmation(Long orderId): void (private)

====== Extracted Info for Order ======
Package: com.example.order

--- Class/Interface: Order (Class)
  Field: Long id (private)
  Field: String itemName (private)
  Field: double amount (private)
  Method: Order(Long id, String itemName, double amount): void (public)
  Method: getId(): Long (public)
  Method: setId(Long id): void (public)
  Method: getItemName(): String (public)
  Method: setItemName(String itemName): void (public)
  Method: getAmount(): double (public)
  Method: setAmount(double amount): void (public)
```





基于方法摘要的 Embedding：

执行过程：

1. 获取到目录下所有java文件。
2. 每读取一个java文件，先让LLM为这个java文件生成摘要信息，并且为每个公开方法也生成摘要信息，然后缓存
3. 通过ast获取java的所有公开方法， 从上一步缓存中的信息获取方法摘要
4. 生成摘要写入向量库

针对方法的信息：

```json
{
  "type": "method",
  "methodName": "isRelatedFile",
  "className": "EditorManagerListener",
  "signature": "private static boolean isRelatedFile(VirtualFile file)",
  "summary_cn": "判断文件名是否以 .java 结尾，是否为相关文件",
  "summary_en": "Check if file name ends with .java",
  "code": "private static boolean isRelatedFile(VirtualFile file) { if (file == null) return false; ... }"
}
```

针对的类的信息：

```json
{
  "type": "class",
  "className": "SinkPipeline",
  "summary_cn": "用于处理 MySQL 到 Kafka 的数据同步任务。",
  "summary_en": "Handles data sync tasks from MySQL to Kafka.",
  "symbols": "函数: start, execute, close...\n变量: running, sinkTask...",
  "filePath": "/src/sinker/SinkPipeline.java"
}
```



### 总结

| 特性               | 基于代码调用/源代码的 Embedding                              | 基于方法摘要的 Embedding                                     |
| ------------------ | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **信息粒度**       | 细粒度，保留所有代码细节                                     | 高粒度，只保留核心功能和意图，丢失部分细节                   |
| **语义密度**       | 较低，需要模型自行学习高层语义                               | 高，LLM 提炼后的自然语言描述                                 |
| **代码变体鲁棒性** | 相对较低，对表层变化敏感                                     | **高**，不受代码格式、变量命名等影响                         |
| **跨语言/框架**    | 较弱，依赖特定模型，泛化能力受限                             | **强**，自然语言摘要具有更好通用性                           |
| **适用场景**       | **精确代码克隆检测**、特定 bug 模式查找、底层实现分析、代码审计 | **高层功能性语义搜索**、代码库探索、跨语言/框架代码推荐、快速功能理解 |
| **计算/实现成本**  | 代码解析和大型代码模型训练/推理                              | LLM 摘要生成（成本和质量控制） + 文本 Embedding              |
| **信息损失**       | 无                                                           | **有**，核心功能外的信息可能丢失                             |



## 测试

效果还可以

```
Update ProducerController, add headers to the sent messages, and set them to Kafka.
```



![image-20250714153258042](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250714153258042.png)



## 参考

- [和chatGPT的讨论](https://chatgpt.com/c/686ccbb4-5204-8003-ba73-e8a3bdef310c)
- [CodeIndexer](https://github.com/zilliztech/CodeIndexer) 是Zilliz开源的。
