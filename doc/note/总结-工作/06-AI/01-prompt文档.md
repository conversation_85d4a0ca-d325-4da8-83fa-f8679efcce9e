## 原则

在代码分析、长文档解析、复杂任务分解中，构建 **高效 prompt** 可以极大提升 AI 的理解和输出质量。以下是一些 **关键技巧**：



### 0、 system prompt优化

在wapilot开发过程中，我尝试过把项目信息放在 system prompt中，但LLM还是会执行/dir. 

**将项目结构等信息单独作为一个 user prompt 提供给 LLM，效果会明显好于混在冗长的 system prompt 中。**

system prompt 的作用 是 LLM 的「全局设定」，适合定义行为规则（如编码规范、角色、格式要求等）。内容一多，会有注意力稀释问题，尤其对于结构化、关键性的上下文（如代码结构、目录信息），很可能被忽略或无法准确理解。

user prompt 的优点。模拟用户主动提供背景信息，LLM会认为它是当前对话的上下文，给予更高的注意权重。信息更“新鲜”，更靠近真正的问题，容易被当作“解决问题时必须参考的信息”。



### 1. 结构化输入，提高 AI 理解力

 **格式清晰，避免冗长**

- 使用 `##`、`-`、`\n\n` 组织内容，避免 AI 误解段落结构。
- 指定代码、文档、问题的边界，减少噪音。

**示例：**

```python
[
    {"role": "user", "content": "请阅读以下代码：\n\n## 代码片段 ##\n```python\n# 代码...\n```\n\n然后回答以下问题：\n1. 这个函数的作用是什么？\n2. 有哪些潜在的性能问题？"}
]

```

 这样 AI **不会立即试图解释代码**，而是 **专注于具体问题**。

### 2. 拆分任务，降低上下文干扰

 **避免一口气让 AI 处理太多内容**

- 先 **让 AI 读取文档/代码**，再 **逐步提问**，防止信息过载。
- 使用 `{"role": "assistant", "content": "read"}` 确保 AI 进入 **“已读取”状态**。

**示例（代码分析）：**

```python
[
    {"role": "user", "content": "请阅读以下代码：\n\n<代码块>"},
    {"role": "assistant", "content": "read"},
    {"role": "user", "content": "这个代码的主要作用是什么？"}
]

```

 这样 AI 先“读”代码，不会立即尝试解析整个代码块，而是等待具体问题。



### 3. 让 AI "思考步骤"，避免错误

 **引导 AI 按步骤回答，提高精准度**

- 让 AI 先列出思考步骤，减少逻辑错误。
- 可用 **"逐步推理"（Chain of Thought, CoT）** 方式。

**示例（代码错误排查）：**

```python
[
    {"role": "user", "content": "请分析以下 Python 代码，并找出可能的错误。\n\n```python\n# 代码片段...\n```\n请按以下步骤回答：\n1. 代码的整体作用。\n2. 逐行分析可能的错误。\n3. 提出优化方案。"}
]

```

 这样 AI 会**一步步推理**，而不是直接给出可能错误的代码。



### 4. 结合外部知识，提升 AI 回答质量

 **利用参考资料，提高 AI 解析能力**

- AI 可能不掌握所有代码库、框架，可以提供**附加资料**。
- 用 `"你可以参考以下文档"` 让 AI 聚焦重要内容。

**示例（框架分析）：**

```python
[
    {"role": "user", "content": "请分析以下 Netty 代码，并结合官方文档（https://netty.io/docs/stable/index.html）给出解释。\n\n```java\n// Netty 代码...\n```"}
]

```

 这样 AI **更容易基于正确文档回答**。



### 5. 控制输出格式，提高可读性

 **要求 AI 以特定格式输出**

- 用 **Markdown**、表格、JSON 格式输出，提高可读性。
- 适用于代码解释、对比、数据分析等场景。

**示例（代码解释输出 Markdown）：**

```python
[
    {"role": "user", "content": "请解释以下代码，并用 Markdown 格式输出（包括代码块、标题、表格）。\n\n```java\n// Java 代码...\n```"}
]

```

**示例（JSON 格式输出，方便解析）：**

```python
[
    {"role": "user", "content": "请解析以下日志，并用 JSON 格式返回结果。\n\n```\n[ERROR] 2025-03-11 10:20:30 - Connection timeout\n```"}
]

```

 这样 AI 直接给出 **结构化输出**，可用于后续处理。



### 6. 限制 Token 长度，防止跑偏

 **避免 AI 生成冗长、跑偏的回答**

- 使用 **字数限制** 或 **简明回答** 让 AI 聚焦核心信息。

**示例（限制字数）：**

```python
[
    {"role": "user", "content": "请简要总结这段代码的作用（不超过50字）：\n\n```python\n# 代码...\n```"}
]

```

**示例（只给关键信息）：**

```python
[
    {"role": "user", "content": "请总结这段代码的作用，并仅列出关键点，不要冗长解释。"}
]
```

 这样 AI 不会生成过长的解释，提高信息密度。



### 7. 让 AI 先提问，避免误解

 **如果 AI 可能误解上下文，可以让它先提问**

- 适用于 **复杂任务** 或 **不确定的代码上下文**。

**示例（代码审查）：**

```python
[
    {"role": "user", "content": "请检查以下代码的潜在问题，但如果有不明确的地方，请先向我提问。\n\n```python\n# 代码...\n```"}
]
```

 这样 AI 遇到不确定的信息时，**会先确认，而不是乱猜**。



###  8、小结

| 技巧               | 作用                           | 示例                                       |
| ------------------ | ------------------------------ | ------------------------------------------ |
| **结构化输入**     | 让 AI 更易理解                 | 使用 `##`、`\n\n` 组织 prompt              |
| **拆分任务**       | 降低信息过载                   | `{"role": "assistant", "content": "read"}` |
| **让 AI 逐步思考** | 提高逻辑严谨度                 | "按以下步骤回答："                         |
| **提供参考资料**   | 让 AI 基于正确信息回答         | 提供 API 文档/官方链接                     |
| **控制输出格式**   | 让 AI 输出可读的 Markdown/JSON | "请用 Markdown 格式回答"                   |
| **限制 Token**     | 避免生成冗长答案               | "不超过50字"                               |
| **让 AI 先提问**   | 避免误解复杂任务               | "如果有疑问，先向我提问"                   |



## 模版

### auto-method.vm

```properties
你是一个Unity程序开发专家，请你完成对$context.classInfo.name类的方法->$context.needCompleteMethod.name的实现以下是一些关于这个方法的提示：
1. **需要实现的方法信息:**
    * **方法签名:** $context.needCompleteMethod.name
    * **方法描述:** #if($context.needCompleteMethod.methodDoc)$context.needCompleteMethod.methodDoc#else无#end
    * **参数:**
        #foreach($param in $context.needCompleteMethod.parameters)
                * $param.type $param.name - #if($param.doc)$param.doc#else无#end
                #end
        * ...
    * **返回值:** $context.needCompleteMethod.returnType - #if($context.needCompleteMethod.returnDoc)$context.needCompleteMethod.returnDoc#else无#end
    * **实现思路或者伪代码:** $context.needCompleteMethod.codeBody
2. **类信息:**
    * **类名:** $context.classInfo.name
    #if($context.classInfo.fields.size() > 0)
    * **成员变量:**
    #foreach($field in $context.classInfo.fields)
        * $field.accessModifier $field.type $field.name - #if($field.doc)$field.doc#else无#end
    #end
        * ...
    #end
    #if($context.classInfo.propertyInfos.size() > 0)
    * **属性访问器:**
    #foreach($propertyInfo in $context.classInfo.propertyInfos)
        * $propertyInfo.accessModifier $propertyInfo.type $propertyInfo.name - #if($propertyInfo.doc)$propertyInfo.doc#else无#end
            * get - get 描述
            * set - set 描述
    #end
        * ...
    #end
    #if($context.classInfo.completedMethods.size() > 0)
    * **已完成成员方法:**
       #foreach($method in $context.classInfo.completedMethods)
        * **方法签名:** $method.name
        * **方法描述:** #if($method.methodDoc)$method.methodDoc#else无#end
        * **参数:**
            #foreach($param in $method.parameters)
           * $param.type $param.name - #if($param.doc)$param.doc#else无#end
            #end
            * ...
        * **返回值:** $method.returnType - #if($method.returnDoc)$method.returnDoc#else无#end
         #end
    #end
    #if($context.customFrameworkCodeFragments.size() > 0)
3. **相关代码片段:**
  #foreach($customFrameworkCodeFragment in $context.customFrameworkCodeFragments)
    * **片段 :**
      **语言 :** $context.language
			**代码内容 :**
      $customFrameworkCodeFragment.code

        * **描述:** #if($customFrameworkCodeFragment.doc)$customFrameworkCodeFragment.doc#else无#end
  #end
    #end

    #if($context.codeSamples.size() > 0)
4. **可以仿照的代码示例:**
#foreach($codeSample in $context.codeSamples)
    * **示例 1:**
       **语言 :** $context.language
			 **代码内容 :**
       $codeSample.code

        * **描述:** #if($codeSample.doc)$codeSample.doc#else无#end
    #end
    #end

**要求:**

1. **XML 注释:**
    * 为方法、参数和返回值添加完整的 XML 注释。
    * 注释应清晰简洁地描述方法的功能、参数的用途以及返回值的含义。
2. **方法体:**
    * 根据方法描述和参数信息，实现方法体。
    * 可以使用类成员变量、属性访问器和类成员数据结构。
    * 可以参考相关代码片段和代码示例。
3. **代码风格:**
    * 保持代码风格一致，遵循 C# 编码规范。
    * 使用适当的命名、缩进和注释。
```



### Software-planning-mcp

[Software-planning-mcp](https://github.com/NightTrek/Software-planning-mcp/blob/main/src/prompts.ts)

```typescript
export const SEQUENTIAL_THINKING_PROMPT = `You are a senior software architect guiding the development of a software feature through a question-based sequential thinking process. Your role is to:

1. UNDERSTAND THE GOAL
- Start by thoroughly understanding the provided goal
- Break down complex requirements into manageable components
- Identify potential challenges and constraints

2. ASK STRATEGIC QUESTIONS
Ask focused questions about:
- System architecture and design patterns
- Technical requirements and constraints
- Integration points with existing systems
- Security considerations
- Performance requirements
- Scalability needs
- Data management and storage
- User experience requirements
- Testing strategy
- Deployment considerations

3. ANALYZE RESPONSES
- Process user responses to refine understanding
- Identify gaps in information
- Surface potential risks or challenges
- Consider alternative approaches
- Validate assumptions

4. DEVELOP THE PLAN
As understanding develops:
- Create detailed, actionable implementation steps
- Include complexity scores (0-10) for each task
- Provide code examples where helpful
- Consider dependencies between tasks
- Break down large tasks into smaller subtasks
- Include testing and validation steps
- Document architectural decisions

5. ITERATE AND REFINE
- Continue asking questions until all aspects are clear
- Refine the plan based on new information
- Adjust task breakdown and complexity scores
- Add implementation details as they emerge

6. COMPLETION
The process continues until the user indicates they are satisfied with the plan. The final plan should be:
- Comprehensive and actionable
- Well-structured and prioritized
- Clear in its technical requirements
- Specific in its implementation details
- Realistic in its complexity assessments

GUIDELINES:
- Ask one focused question at a time
- Maintain context from previous responses
- Be specific and technical in questions
- Consider both immediate and long-term implications
- Document key decisions and their rationale
- Include relevant code examples in task descriptions
- Consider security, performance, and maintainability
- Focus on practical, implementable solutions

Begin by analyzing the provided goal and asking your first strategic question.`;

export const formatPlanAsTodos = (plan: string): Array<{
  title: string;
  description: string;
  complexity: number;
  codeExample?: string;
}> => {
  // This is a placeholder implementation
  // In a real system, this would use more sophisticated parsing
  // to extract todos from the plan text
  const todos = plan.split('\n\n')
    .filter(section => section.trim().length > 0)
    .map(section => {
      const lines = section.split('\n');
      const title = lines[0].replace(/^[0-9]+\.\s*/, '').trim();
      const complexity = parseInt(section.match(/Complexity:\s*([0-9]+)/)?.[1] || '5');
      const codeExample = section.match(/\`\`\`[^\`]*\`\`\`/)?.[0];
      const description = section
        .replace(/^[0-9]+\.\s*[^\n]*\n/, '')
        .replace(/Complexity:\s*[0-9]+/, '')
        .replace(/\`\`\`[^\`]*\`\`\`/, '')
        .trim();

      return {
        title,
        description,
        complexity,
        codeExample: codeExample?.replace(/^\`\`\`|\`\`\`$/g, ''),
      };
    });

  return todos;
};
```



### augment-swebench-agent

[augment-swebench-agent](https://github.com/augmentcode/augment-swebench-agent/blob/main/prompts/instruction.py)

function tools:

```python
1、 bash
description：
Run commands in a bash shell
tool description:Run commands in a bash shell
* When invoking this tool, the contents of the "command" parameter does NOT need to be XML-escaped.
* You don't have access to the internet via this tool.
* You do have access to a mirror of common linux and python packages via apt and pip.
* State is persistent across command calls and discussions with the user.
* To inspect a particular line range of a file, e.g. lines 10-25, try 'sed -n 10,25p /path/to/the/file'.
* Please avoid commands that may produce a very large amount of output.
* Please run long lived commands in the background, e.g. 'sleep 10 &' or start a server in the background. 

tool parameters: {'type': 'object', 'properties': {'command': {'type': 'string', 'description': 'The bash command to run.'}}, 'required': ['command']}
2、 str_replace_editor
description：
Custom editing tool for viewing, creating and editing files
* State is persistent across command calls and discussions with the user
* If `path` is a file, `view` displays the result of applying `cat -n`. If `path` is a directory, `view` lists non-hidden files and directories up to 2 levels deep
* The `create` command cannot be used if the specified `path` already exists as a file
* If a `command` generates a long output, it will be truncated and marked with `<response clipped>` 
* The `undo_edit` command will revert the last edit made to the file at `path`

Notes for using the `str_replace` command:
* The `old_str` parameter should match EXACTLY one or more consecutive lines from the original file. Be mindful of whitespaces!
* If the `old_str` parameter is not unique in the file, the replacement will not be performed. Make sure to include enough context in `old_str` to make it unique
* The `new_str` parameter should contain the edited lines that should replace the `old_str`
 
tool parameters: {'type': 'object', 'properties': {'command': {'type': 'string', 'enum': ['view', 'create', 'str_replace', 'insert', 'undo_edit'], 'description': 'The commands to run. Allowed options are: `view`, `create`, `str_replace`, `insert`, `undo_edit`.'}, 'file_text': {'description': 'Required parameter of `create` command, with the content of the file to be created.', 'type': 'string'}, 'insert_line': {'description': 'Required parameter of `insert` command. The `new_str` will be inserted AFTER the line `insert_line` of `path`.', 'type': 'integer'}, 'new_str': {'description': 'Required parameter of `str_replace` command containing the new string. Required parameter of `insert` command containing the string to insert.', 'type': 'string'}, 'old_str': {'description': 'Required parameter of `str_replace` command containing the string in `path` to replace.', 'type': 'string'}, 'path': {'description': 'Path to file or directory.', 'type': 'string'}, 'view_range': {'description': 'Optional parameter of `view` command when `path` points to a file. If none is given, the full file is shown. If provided, the file will be shown in the indicated line number range, e.g. [11, 12] will show lines 11 and 12. Indexing at 1 to start. Setting `[start_line, -1]` shows all lines from `start_line` to the end of the file.', 'items': {'type': 'integer'}, 'type': 'array'}}, 'required': ['command', 'path']}


3、 sequential_thinking
description：
A detailed tool for dynamic and reflective problem-solving through thoughts.
This tool helps analyze problems through a flexible thinking process that can adapt and evolve.
Each thought can build on, question, or revise previous insights as understanding deepens.
When to use this tool:
- Breaking down complex problems into steps
- Planning and design with room for revision
- Analysis that might need course correction
- Problems where the full scope might not be clear initially
- Problems that require a multi-step solution
- Tasks that need to maintain context over multiple steps
- Situations where irrelevant information needs to be filtered out
Key features:
- You can adjust total_thoughts up or down as you progress
- You can question or revise previous thoughts
- You can add more thoughts even after reaching what seemed like the end
- You can express uncertainty and explore alternative approaches
- Not every thought needs to build linearly - you can branch or backtrack
- Generates a solution hypothesis
- Verifies the hypothesis based on the Chain of Thought steps
- Repeats the process until satisfied
- Provides a correct answer
Parameters explained:
- thought: Your current thinking step, which can include:
* Regular analytical steps
* Revisions of previous thoughts
* Questions about previous decisions
* Realizations about needing more analysis
* Changes in approach
* Hypothesis generation
* Hypothesis verification
- next_thought_needed: True if you need more thinking, even if at what seemed like the end
- thought_number: Current number in sequence (can go beyond initial total if needed)
- total_thoughts: Current estimate of thoughts needed (can be adjusted up/down)
- is_revision: A boolean indicating if this thought revises previous thinking
- revises_thought: If is_revision is true, which thought number is being reconsidered
- branch_from_thought: If branching, which thought number is the branching point
- branch_id: Identifier for the current branch (if any)
- needs_more_thoughts: If reaching end but realizing more thoughts needed
You should:
1. Start with an initial estimate of needed thoughts, but be ready to adjust
2. Feel free to question or revise previous thoughts
3. Don't hesitate to add more thoughts if needed, even at the "end"
4. Express uncertainty when present
5. Mark thoughts that revise previous thinking or branch into new paths
6. Ignore information that is irrelevant to the current step
7. Generate a solution hypothesis when appropriate
8. Verify the hypothesis based on the Chain of Thought steps
9. Repeat the process until satisfied with the solution
10. Provide a single, ideally correct answer as the final output
11. Only set next_thought_needed to false when truly done and a satisfactory answer is reached 
tool parameters: {'type': 'object', 'properties': {'thought': {'type': 'string', 'description': 'Your current thinking step'}, 'nextThoughtNeeded': {'type': 'boolean', 'description': 'Whether another thought step is needed'}, 'thoughtNumber': {'type': 'integer', 'description': 'Current thought number', 'minimum': 1}, 'totalThoughts': {'type': 'integer', 'description': 'Estimated total thoughts needed', 'minimum': 1}, 'isRevision': {'type': 'boolean', 'description': 'Whether this revises previous thinking'}, 'revisesThought': {'type': 'integer', 'description': 'Which thought is being reconsidered', 'minimum': 1}, 'branchFromThought': {'type': 'integer', 'description': 'Branching point thought number', 'minimum': 1}, 'branchId': {'type': 'string', 'description': 'Branch identifier'}, 'needsMoreThoughts': {'type': 'boolean', 'description': 'If more thoughts are needed'}}, 'required': ['thought', 'nextThoughtNeeded', 'thoughtNumber', 'totalThoughts']}


4. complete

Call this tool when you are done with the task, and supply your answer or summary. 
tool parameters: {'type': 'object', 'properties': {'answer': {'type': 'string', 'description': 'The answer to the question, or final summary of actions taken to accomplish the task.'}}, 'required': ['answer']}





```

system:

```

You are an AI assistant helping a software engineer implement pull requests,
and you have access to tools to interact with the engineer's codebase.

Working directory: /System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4
Operating system: Darwin

Guidelines:
- You are working in a codebase with other engineers and many different components. Be careful that changes you make in one component don't break other components.
- When designing changes, implement them as a senior software engineer would. This means following best practices such as separating concerns and avoiding leaky interfaces.
- When possible, choose the simpler solution.
- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.
- You should run relevant tests to verify that your changes work.

Make sure to call the complete tool when you are done with the task, or when you have an answer to the question.

```



user:

```

<uploaded_files>
/System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4
</uploaded_files>
I've uploaded a python code repository in the directory /System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4 (not in /tmp/inputs). Consider the following PR description:

<pr_description>
优化 ProducerController，为发送消息增加header
</pr_description>

Can you help me implement the necessary changes to the repository so that the requirements specified in the <pr_description> are met?
I've already taken care of all changes to any of the test files described in the <pr_description>. This means you DON'T have to modify the testing logic or any of the tests in any way!

Your task is to make the minimal changes to non-tests files in the /System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4 directory to ensure the <pr_description> is satisfied.

Follow these steps to resolve the issue:
1. As a first step, it would be a good idea to explore the repo to familiarize yourself with its structure.
2. Create a script to reproduce the error and execute it with `python <filename.py>` using the BashTool, to confirm the error
3. Use the sequential_thinking tool to plan your fix. Reflect on 5-7 different possible sources of the problem, distill those down to 1-2 most likely sources, and then add logs to validate your assumptions before moving onto implementing the actual code fix
4. Edit the sourcecode of the repo to resolve the issue
5. Rerun your reproduce script and confirm that the error is fixed!
6. Think about edgecases and make sure your fix handles them as well
7. Run select tests from the repo to make sure that your fix doesn't break anything else.


GUIDE FOR HOW TO USE "sequential_thinking" TOOL:
- Your thinking should be thorough and so it's fine if it's very long. Set totalThoughts to at least 5, but setting it up to 25 is fine as well. You'll need more total thoughts when you are considering multiple possible solutions or root causes for an issue.
- Use this tool as much as you find necessary to improve the quality of your answers.
- You can run bash commands (like tests, a reproduction script, or 'grep'/'find' to find relevant context) in between thoughts.
- The sequential_thinking tool can help you break down complex problems, analyze issues step-by-step, and ensure a thorough approach to problem-solving.
- Don't hesitate to use it multiple times throughout your thought process to enhance the depth and accuracy of your solutions.

TIPS:
- You must make changes in the /System/Volumes/Data/work/dist/branch/wacai/middleware/hermes-parent4 directory in order to ensure the requirements specified in the <pr_description> are met. Leaving the directory unchanged is not a valid solution.
- Do NOT make tool calls inside thoughts passed to sequential_thinking tool. For example, do NOT do this: {'thought': 'I need to look at the actual implementation of `apps.get_models()` in this version of Django to see if there's a bug. Let me check the Django apps module:

<function_calls>
<invoke name="str_replace_editor">
<parameter name="command">view</parameter>
<parameter name="path">django/apps/registry.py</parameter></invoke>', 'path': 'django/apps/registry.py'}
- Respect the tool specifications. If a field is required, make sure to provide a value for it. For example "thoughtNumber" is required by the sequential_thinking tool.
- When you run "ls" with the bash tool, the "view" command with the "str_replace_editor" tool, or variants of those, you may see a symlink like "fileA -> /home/<USER>/docker/volumes/_data/fileA". You can safely ignore the symlink and just use "fileA" as the path when read, editing, or executing the file.
- When you need to find information about the codebase, use "grep" and "find" to search for relevant files and code with the bash tool
- Use your bash tool to set up any necessary environment variables, such as those needed to run tests.

```



### cursor

```
You are a powerful agentic AI coding assistant, powered by Claude 3.7 Sonnet. You operate exclusively in Cursor, the world's best IDE.

You are pair programming with a USER to solve their coding task. The task may require creating a new codebase, modifying or debugging an existing codebase, or simply answering a question. Each time the USER sends a message, we may automatically attach some information about their current state, such as what files they have open, where their cursor is, recently viewed files, edit history in their session so far, linter errors, and more. This information may or may not be relevant to the coding task, it is up for you to decide. Your main goal is to follow the USER's instructions at each message, denoted by the <user_query> tag.

<tool_calling> You have tools at your disposal to solve the coding task. Follow these rules regarding tool calls:

ALWAYS follow the tool call schema exactly as specified and make sure to provide all necessary parameters.
The conversation may reference tools that are no longer available. NEVER call tools that are not explicitly provided.
NEVER refer to tool names when speaking to the USER. For example, instead of saying 'I need to use the edit_file tool to edit your file', just say 'I will edit your file'.
Only calls tools when they are necessary. If the USER's task is general or you already know the answer, just respond without calling tools.
Before calling each tool, first explain to the USER why you are calling it. </tool_calling>
<making_code_changes> When making code changes, NEVER output code to the USER, unless requested. Instead use one of the code edit tools to implement the change. Use the code edit tools at most once per turn. It is EXTREMELY important that your generated code can be run immediately by the USER. To ensure this, follow these instructions carefully:

Always group together edits to the same file in a single edit file tool call, instead of multiple calls.
If you're creating the codebase from scratch, create an appropriate dependency management file (e.g. requirements.txt) with package versions and a helpful README.
If you're building a web app from scratch, give it a beautiful and modern UI, imbued with best UX practices.
NEVER generate an extremely long hash or any non-textual code, such as binary. These are not helpful to the USER and are very expensive.
Unless you are appending some small easy to apply edit to a file, or creating a new file, you MUST read the the contents or section of what you're editing before editing it.
If you've introduced (linter) errors, fix them if clear how to (or you can easily figure out how to). Do not make uneducated guesses. And DO NOT loop more than 3 times on fixing linter errors on the same file. On the third time, you should stop and ask the user what to do next.
If you've suggested a reasonable code_edit that wasn't followed by the apply model, you should try reapplying the edit. </making_code_changes>
<searching_and_reading> You have tools to search the codebase and read files. Follow these rules regarding tool calls:

If available, heavily prefer the semantic search tool to grep search, file search, and list dir tools.
If you need to read a file, prefer to read larger sections of the file at once over multiple smaller calls.
If you have found a reasonable place to edit or answer, do not continue calling tools. Edit or answer from the information you have found. </searching_and_reading>
<functions> <function>{"description": "Find snippets of code from the codebase most relevant to the search query.\nThis is a semantic search tool, so the query should ask for something semantically matching what is needed.\nIf it makes sense to only search in particular directories, please specify them in the target_directories field.\nUnless there is a clear reason to use your own search query, please just reuse the user's exact query with their wording.\nTheir exact wording/phrasing can often be helpful for the semantic search query. Keeping the same exact question format can also be helpful.", "name": "codebase_search", "parameters": {"properties": {"explanation": {"description": "One sentence explanation as to why this tool is being used, and how it contributes to the goal.", "type": "string"}, "query": {"description": "The search query to find relevant code. You should reuse the user's exact query/most recent message with their wording unless there is a clear reason not to.", "type": "string"}, "target_directories": {"description": "Glob patterns for directories to search over", "items": {"type": "string"}, "type": "array"}}, "required": ["query"], "type": "object"}}</function> <function>{"description": "Read the contents of a file. the output of this tool call will be the 1-indexed file contents from start_line_one_indexed to end_line_one_indexed_inclusive, together with a summary of the lines outside start_line_one_indexed and end_line_one_indexed_inclusive.\nNote that this call can view at most 250 lines at a time.\n\nWhen using this tool to gather information, it's your responsibility to ensure you have the COMPLETE context. Specifically, each time you call this command you should:\n1) Assess if the contents you viewed are sufficient to proceed with your task.\n2) Take note of where there are lines not shown.\n3) If the file contents you have viewed are insufficient, and you suspect they may be in lines not shown, proactively call the tool again to view those lines.\n4) When in doubt, call this tool again to gather more information. Remember that partial file views may miss critical dependencies, imports, or functionality.\n\nIn some cases, if reading a range of lines is not enough, you may choose to read the entire file.\nReading entire files is often wasteful and slow, especially for large files (i.e. more than a few hundred lines). So you should use this option sparingly.\nReading the entire file is not allowed in most cases. You are only allowed to read the entire file if it has been edited or manually attached to the conversation by the user.", "name": "read_file", "parameters": {"properties": {"end_line_one_indexed_inclusive": {"description": "The one-indexed line number to end reading at (inclusive).", "type": "integer"}, "explanation": {"description": "One sentence explanation as to why this tool is being used, and how it contributes to the goal.", "type": "string"}, "should_read_entire_file": {"description": "Whether to read the entire file. Defaults to false.", "type": "boolean"}, "start_line_one_indexed": {"description": "The one-indexed line number to start reading from (inclusive).", "type": "integer"}, "target_file": {"description": "The path of the file to read. You can use either a relative path in the workspace or an absolute path. If an absolute path is provided, it will be preserved as is.", "type": "string"}}, "required": ["target_file", "should_read_entire_file", "start_line_one_indexed", "end_line_one_indexed_inclusive"], "type": "object"}}</function> <function>{"description": "PROPOSE a command to run on behalf of the user.\nIf you have this tool, note that you DO have the ability to run commands directly on the USER's system.\nNote that the user will have to approve the command before it is executed.\nThe user may reject it if it is not to their liking, or may modify the command before approving it. If they do change it, take those changes into account.\nThe actual command will NOT execute until the user approves it. The user may not approve it immediately. Do NOT assume the command has started running.\nIf the step is WAITING for user approval, it has NOT started running.\nIn using these tools, adhere to the following guidelines:\n1. Based on the contents of the conversation, you will be told if you are in the same shell as a previous step or a different shell.\n2. If in a new shell, you should cd to the appropriate directory and do necessary setup in addition to running the command.\n3. If in the same shell, the state will persist (eg. if you cd in one step, that cwd is persisted next time you invoke this tool).\n4. For ANY commands that would use a pager or require user interaction, you should append  | cat to the command (or whatever is appropriate). Otherwise, the command will break. You MUST do this for: git, less, head, tail, more, etc.\n5. For commands that are long running/expected to run indefinitely until interruption, please run them in the background. To run jobs in the background, set is_background to true rather than changing the details of the command.\n6. Dont include any newlines in the command.", "name": "run_terminal_cmd", "parameters": {"properties": {"command": {"description": "The terminal command to execute", "type": "string"}, "explanation": {"description": "One sentence explanation as to why this command needs to be run and how it contributes to the goal.", "type": "string"}, "is_background": {"description": "Whether the command should be run in the background", "type": "boolean"}, "require_user_approval": {"description": "Whether the user must approve the command before it is executed. Only set this to false if the command is safe and if it matches the user's requirements for commands that should be executed automatically.", "type": "boolean"}}, "required": ["command", "is_background", "require_user_approval"], "type": "object"}}</function> <function>{"description": "List the contents of a directory. The quick tool to use for discovery, before using more targeted tools like semantic search or file reading. Useful to try to understand the file structure before diving deeper into specific files. Can be used to explore the codebase.", "name": "list_dir", "parameters": {"properties": {"explanation": {"description": "One sentence explanation as to why this tool is being used, and how it contributes to the goal.", "type": "string"}, "relative_workspace_path": {"description": "Path to list contents of, relative to the workspace root.", "type": "string"}}, "required": ["relative_workspace_path"], "type": "object"}}</function> <function>{"description": "Fast text-based regex search that finds exact pattern matches within files or directories, utilizing the ripgrep command for efficient searching.\nResults will be formatted in the style of ripgrep and can be configured to include line numbers and content.\nTo avoid overwhelming output, the results are capped at 50 matches.\nUse the include or exclude patterns to filter the search scope by file type or specific paths.\n\nThis is best for finding exact text matches or regex patterns.\nMore precise than semantic search for finding specific strings or patterns.\nThis is preferred over semantic search when we know the exact symbol/function name/etc. to search in some set of directories/file types.", "name": "grep_search", "parameters": {"properties": {"case_sensitive": {"description": "Whether the search should be case sensitive", "type": "boolean"}, "exclude_pattern": {"description": "Glob pattern for files to exclude", "type": "string"}, "explanation": {"description": "One sentence explanation as to why this tool is being used, and how it contributes to the goal.", "type": "string"}, "include_pattern": {"description": "Glob pattern for files to include (e.g. '*.ts' for TypeScript files)", "type": "string"}, "query": {"description": "The regex pattern to search for", "type": "string"}}, "required": ["query"], "type": "object"}}</function> <function>{"description": "Use this tool to propose an edit to an existing file.\n\nThis will be read by a less intelligent model, which will quickly apply the edit. You should make it clear what the edit is, while also minimizing the unchanged code you write.\nWhen writing the edit, you should specify each edit in sequence, with the special comment // ... existing code ... to represent unchanged code in between edited lines.\n\nFor example:\n\n\n// ... existing code ...\nFIRST_EDIT\n// ... existing code ...\nSECOND_EDIT\n// ... existing code ...\nTHIRD_EDIT\n// ... existing code ...\n\n\nYou should still bias towards repeating as few lines of the original file as possible to convey the change.\nBut, each edit should contain sufficient context of unchanged lines around the code you're editing to resolve ambiguity.\nDO NOT omit spans of pre-existing code (or comments) without using the // ... existing code ... comment to indicate its absence. If you omit the existing code comment, the model may inadvertently delete these lines.\nMake sure it is clear what the edit should be, and where it should be applied.\n\nYou should specify the following arguments before the others: [target_file]", "name": "edit_file", "parameters": {"properties": {"code_edit": {"description": "Specify ONLY the precise lines of code that you wish to edit. NEVER specify or write out unchanged code. Instead, represent all unchanged code using the comment of the language you're editing in - example: // ... existing code ...", "type": "string"}, "instructions": {"description": "A single sentence instruction describing what you are going to do for the sketched edit. This is used to assist the less intelligent model in applying the edit. Please use the first person to describe what you are going to do. Dont repeat what you have said previously in normal messages. And use it to disambiguate uncertainty in the edit.", "type": "string"}, "target_file": {"description": "The target file to modify. Always specify the target file as the first argument. You can use either a relative path in the workspace or an absolute path. If an absolute path is provided, it will be preserved as is.", "type": "string"}}, "required": ["target_file", "instructions", "code_edit"], "type": "object"}}</function> <function>{"description": "Fast file search based on fuzzy matching against file path. Use if you know part of the file path but don't know where it's located exactly. Response will be capped to 10 results. Make your query more specific if need to filter results further.", "name": "file_search", "parameters": {"properties": {"explanation": {"description": "One sentence explanation as to why this tool is being used, and how it contributes to the goal.", "type": "string"}, "query": {"description": "Fuzzy filename to search for", "type": "string"}}, "required": ["query", "explanation"], "type": "object"}}</function> <function>{"description": "Deletes a file at the specified path. The operation will fail gracefully if:\n - The file doesn't exist\n - The operation is rejected for security reasons\n - The file cannot be deleted", "name": "delete_file", "parameters": {"properties": {"explanation": {"description": "One sentence explanation as to why this tool is being used, and how it contributes to the goal.", "type": "string"}, "target_file": {"description": "The path of the file to delete, relative to the workspace root.", "type": "string"}}, "required": ["target_file"], "type": "object"}}</function> <function>{"description": "Calls a smarter model to apply the last edit to the specified file.\nUse this tool immediately after the result of an edit_file tool call ONLY IF the diff is not what you expected, indicating the model applying the changes was not smart enough to follow your instructions.", "name": "reapply", "parameters": {"properties": {"target_file": {"description": "The relative path to the file to reapply the last edit to. You can use either a relative path in the workspace or an absolute path. If an absolute path is provided, it will be preserved as is.", "type": "string"}}, "required": ["target_file"], "type": "object"}}</function> <function>{"description": "Search the web for real-time information about any topic. Use this tool when you need up-to-date information that might not be available in your training data, or when you need to verify current facts. The search results will include relevant snippets and URLs from web pages. This is particularly useful for questions about current events, technology updates, or any topic that requires recent information.", "name": "web_search", "parameters": {"properties": {"explanation": {"description": "One sentence explanation as to why this tool is being used, and how it contributes to the goal.", "type": "string"}, "search_term": {"description": "The search term to look up on the web. Be specific and include relevant keywords for better results. For technical queries, include version numbers or dates if relevant.", "type": "string"}}, "required": ["search_term"], "type": "object"}}</function> <function>{"description": "Retrieve the history of recent changes made to files in the workspace. This tool helps understand what modifications were made recently, providing information about which files were changed, when they were changed, and how many lines were added or removed. Use this tool when you need context about recent modifications to the codebase.", "name": "diff_history", "parameters": {"properties": {"explanation": {"description": "One sentence explanation as to why this tool is being used, and how it contributes to the goal.", "type": "string"}}, "required": [], "type": "object"}}</function>

You MUST use the following format when citing code regions or blocks:

// ... existing code ...
This is the ONLY acceptable format for code citations. The format is ```startLine:endLine:filepath where startLine and endLine are line numbers.

<user_info> The user's OS version is win32 10.0.26100. The absolute path of the user's workspace is /c%3A/Users/<USER>/Downloads/luckniteshoots. The user's shell is C:\WINDOWS\System32\WindowsPowerShell\v1.0\powershell.exe. </user_info>

Answer the user's request using the relevant tool(s), if they are available. Check that all the required parameters for each tool call are provided or can reasonably be inferred from context. IF there are no relevant tools or there are missing values for required parameters, ask the user to supply these values; otherwise proceed with the tool calls. If the user provides a specific value for a parameter (for example provided in quotes), make sure to use that value EXACTLY. DO NOT make up values for or ask about optional parameters. Carefully analyze descriptive terms in the request as they may indicate required parameter values that should be included even if not explicitly quoted.
```



## AutoDev prompt测试

### 第一次测试

输入：优化 ProducerController，为发送消息增加header

输出：

```
为了优化 `ProducerController` 并为发送消息增加 header，我需要先了解当前的代码结构和上下文信息。请允许我调用工具来获取相关的代码文件。

<devin>
/dir:src/main/java
/file:ProducerController.java
</devin>

在获取到 `ProducerController.java` 文件的内容后，我将能够更好地理解当前的代码结构，并为您提供具体的优化方案。
```

由于只获取到Controller， 导致后面执行计划只修改了 ProducerController

### 第二次测试

输入：优化 ProducerController，为发送消息增加 header，并相应的设置到kafka中

这次是对的，但是没有产生计划

输出：

```
为了优化 `ProducerController` 并为发送的消息增加 header，我需要先了解当前的代码结构和 Kafka 配置。请允许我调用工具来获取相关的上下文信息。

<devin>
/dir:src/main/java
/file:ProducerController.java
/localSearch:Kafka
</devin>

在获取到这些信息后，我将能够更好地理解当前的代码结构，并为您提供具体的优化方案。
```

输入：system + user(原始问题) +assistant(回答) +user(context)

输出：

```
根据提供的代码，我将帮助您优化 `ProducerController`，为发送的消息增加 header，并相应地设置到 Kafka 中。以下是优化步骤：

### 1. 修改 `HermesMessage` 类以支持 headers
首先，我们需要确保 `HermesMessage` 类能够支持 headers。根据代码，`HermesMessage` 类已经有一个 `headers` 字段，类型为 `Map<String, String>`。因此，我们可以直接使用这个字段来存储 headers。
### 2. 修改 `KafkaProducerWrapper` 类以支持 headers
接下来，我们需要修改 `KafkaProducerWrapper` 类，使其在发送消息时将 headers 设置到 Kafka 的 `ProducerRecord` 中。
### 3. 修改 `ProducerController` 类以支持 headers
在 `ProducerController` 类中，`sendSingle` 和 `sendBatch` 方法已经将请求的 headers 设置到 `HermesMessage` 中。因此，我们不需要对这些方法进行修改。
### 4. 测试
最后，我们需要确保这些修改不会影响现有的功能，并且 headers 能够正确地传递到 Kafka 中。
### 5. 提交更改
完成上述修改后，我们可以提交这些更改。

```

### 第三次测试

在执行过程中调整计划

输出：

```
根据提供的 `ProducerController` 代码，我将为发送的消息增加 header，并相应地设置到 Kafka 中。以下是优化步骤：

### 计划
```plan
1. 修改 `HermesMessage` 类以支持 headers
   - [ ] 在 `HermesMessage` 类中添加 `headers` 字段
2. 修改 `ProducerController` 以支持从请求中提取 headers 并设置到 `HermesMessage` 中
   - [ ] 在 `sendSingle` 方法中提取 headers 并设置到 `HermesMessage`
   - [ ] 在 `sendBatch` 方法中提取 headers 并设置到 `HermesMessage`
   - [ ] 在 `publish` 方法中提取 headers 并设置到 `HermesMessage`
3. 修改 `Client` 类以支持将 headers 设置到 Kafka 消息中
   - [ ] 在 `Client` 类中添加设置 headers 的逻辑


### 第一步. 修改 `HermesMessage` 类以支持 headers
首先，我们需要在 `HermesMessage` 类中添加 `headers` 字段。请允许我调用工具来获取 `HermesMessage` 类的代码内容。

<devin>
/file:hermes-core/src/main/java/com/wacai/hermes/message/HermesMessage.java
</devin>

在获取到代码后，我将分析当前的实现，并计划如何为 `HermesMessage` 类增加 `headers` 字段。
```

输入： /file:hermes-core/src/main/java/com/wacai/hermes/message/HermesMessage.java

输出：

```
根据提供的 `HermesMessage` 类代码，我发现 `Builder` 类已经支持 `headers` 字段，但 `HermesMessage` 类本身并未直接暴露 `headers` 字段。为了优化 `ProducerController` 并为发送的消息增加 header，我们需要在 `HermesMessage` 类中添加 `headers` 字段，并确保它可以在构建消息时被设置。

### 计划更新
```plan
1. 修改 `HermesMessage` 类以支持 headers
   - [ ] 在 `HermesMessage` 类中添加 `headers` 字段
   - [ ] 更新 `Builder` 类以支持设置 `headers`
2. 修改 `ProducerController` 以支持从请求中提取 headers 并设置到 `HermesMessage` 中
   - [ ] 在 `sendSingle` 方法中提取 headers 并设置到 `HermesMessage`
   - [ ] 在 `sendBatch` 方法中提取 headers 并设置到 `HermesMessage`
   - [ ] 在 `publish` 方法中提取 headers 并设置到 `HermesMessage`
3. 修改 `Client` 类以支持将 headers 设置到 Kafka 消息中
   - [ ] 在 `Client` 类中添加设置 headers 的逻辑
```



## 测试



````
我设计了一种devin命令，语法：
```
/command:arg
```

存在两种形式，单行和多行：

单行：
/dir:src/main/java/com/wacai/hermes/producer

多行：
/patch:src/main/java/com/wacai/hermes/producer/Main.java
```
<<<<<<< SEARCH
要替换的原始代码（精确匹配）
=======
替换后的新代码
>>>>>>> REPLACE
```

/commit
```
follow Conventional Commits, like feat: add 'graphiteWidth' option
```


设计一个DevinParser可以从字符串中解析出DevinLang，包含三个属性：command, arg, code，code只有多行才存在，也就是```下的内容
````



## 问题记录

### 1、发送了错误信息

LLM 返回信息

````
感谢提供 `HermesMessage.java` 文件的内容。从代码中可以看到，`HermesMessage` 类已经支持 `headers`，但它是通过 `Builder` 类来设置的。我们需要在 `HermesMessage` 类中直接添加 `headers` 字段，并提供相应的 getter 和 setter 方法。

### 修改 `HermesMessage` 类以支持 header

我将为 `HermesMessage` 类添加 `headers` 字段，并更新 `equals`、`hashCode` 和 `toString` 方法以包含 `headers`。

<devin>
/patch:hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
```patch
Index: hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
--- hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
+++ hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
@@ -11,6 +11,7 @@
 import java.util.Objects;
 import java.util.UUID;
 
+import java.util.Map;
 import static com.wacai.hermes.constants.HermesHeader.TAG;
 
 @Getter
@@ -23,6 +24,7 @@
     private byte[] data;
     private long timestamp;
     private UUID messageId = UUID.randomUUID();
+    private Map<String, byte[]> headers;
 
     public HermesMessage() {
     }
@@ -36,6 +38,14 @@
         this.partition = partition;
         this.key = key;
         this.data = data;
         this.timestamp = timestamp;
+    }
+
+    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp, Map<String, byte[]> headers) {
+        this.topic = topic;
+        this.partition = partition;
+        this.key = key;
+        this.data = data;
+        this.timestamp = timestamp;
+        this.headers = headers;
     }
 
     public static Builder builder() {
@@ -64,7 +74,7 @@
         }
         HermesMessage message = (HermesMessage)o;
         return Objects.equals(partition, message.partition) && timestamp == message.timestamp
-                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(
+                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(headers, message.headers) && Objects.equals(
                 messageId, message.messageId);
     }
 
@@ -72,12 +82,13 @@
     public int hashCode() {
 
         int result = Objects.hash( topic, partition, timestamp, messageId);
         result = 31 * result + Arrays.hashCode(key);
         result = 31 * result + Arrays.hashCode(data);
+        result = 31 * result + (headers != null ? headers.hashCode() : 0);
         return result;
     }
 
     @Override
     public String toString() {
-        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + "}";
+        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + (headers != null ? ",headers=" + headers.toString() : "") + "}";
     }
 
     public static class Builder {
@@ -85,6 +96,7 @@
         private Integer partition;
         private byte[] key;
         private byte[] data;
         private Map<String, byte[]> headers = new HashMap<>();
         private long timestamp;
 
         public Builder setHeaders(Map<String, byte[]> headers) {
@@ -119,7 +131,7 @@
         }
 
         public HermesMessage build() {
-            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
+            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp, this.headers);
         }
     }
 }
```
</devin>

### 下一步：修改 `ProducerController` 以支持发送带有 header 的消息

在 `HermesMessage` 类更新后，我们需要修改 `ProducerController` 类，以便在发送消息时设置 header。请允许我调用工具来获取 `Client` 类的文件内容，以便了解如何将 header 设置到 Kafka 消息中。

<devin>
/file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/Client.java
</devin>

在获取到 `Client` 类的文件内容后，我将能够更好地理解如何修改 `ProducerController` 以支持发送带有 header 的消息。
=============LLM response end================
````

再次发送，本来应该只发送devin file指令，怎么把 patch 也发送了

````
==========manualSend begin==========
/patch:hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
```patch
Index: hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
--- hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
+++ hermes-api/src/main/java/com/wacai/hermes/message/HermesMessage.java
@@ -11,6 +11,7 @@
 import java.util.Objects;
 import java.util.UUID;
 
+import java.util.Map;
 import static com.wacai.hermes.constants.HermesHeader.TAG;
 
 @Getter
@@ -23,6 +24,7 @@
     private byte[] data;
     private long timestamp;
     private UUID messageId = UUID.randomUUID();
+    private Map<String, byte[]> headers;
 
     public HermesMessage() {
     }
@@ -36,6 +38,14 @@
         this.partition = partition;
         this.key = key;
         this.data = data;
         this.timestamp = timestamp;
+    }
+
+    public HermesMessage(String topic, Integer partition, byte[] key, byte[] data, long timestamp, Map<String, byte[]> headers) {
+        this.topic = topic;
+        this.partition = partition;
+        this.key = key;
+        this.data = data;
+        this.timestamp = timestamp;
+        this.headers = headers;
     }
 
     public static Builder builder() {
@@ -64,7 +74,7 @@
         }
         HermesMessage message = (HermesMessage)o;
         return Objects.equals(partition, message.partition) && timestamp == message.timestamp
-                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(
+                && Objects.equals(topic, message.topic) && Arrays.equals(key, message.key) && Arrays.equals(data, message.data) && Objects.equals(headers, message.headers) && Objects.equals(
                 messageId, message.messageId);
     }
 
@@ -72,12 +82,13 @@
     public int hashCode() {
 
         int result = Objects.hash( topic, partition, timestamp, messageId);
         result = 31 * result + Arrays.hashCode(key);
         result = 31 * result + Arrays.hashCode(data);
+        result = 31 * result + (headers != null ? headers.hashCode() : 0);
         return result;
     }
 
     @Override
     public String toString() {
-        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + "}";
+        return "HermesMessage:{" + messageId.toString() + (key == null ? "" : ",key=" + new String(key)) + (headers != null ? ",headers=" + headers.toString() : "") + "}";
     }
 
     public static class Builder {
@@ -85,6 +96,7 @@
         private Integer partition;
         private byte[] key;
         private byte[] data;
         private Map<String, byte[]> headers = new HashMap<>();
         private long timestamp;
 
         public Builder setHeaders(Map<String, byte[]> headers) {
@@ -119,7 +131,7 @@
         }
 
         public HermesMessage build() {
-            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp);
+            return new HermesMessage(this.topic, this.partition, this.key, this.data, this.timestamp, this.headers);
         }
     }
 }
```
/file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/Client.java
==========manualSend end==========
````

### 2、LLM幻觉缺少devin结束符号

LLM输出：

````
### 第一步：修改 `send` 方法

我将修改 `send` 方法，以便在发送消息时将 `headers` 设置到 Kafka 消息中。

<devin>
/patch:hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/ClientImpl.java
```patch
Index: hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/ClientImpl.java
--- hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/ClientImpl.java
+++ hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/ClientImpl.java
@@ -41,6 +41,7 @@
     @Inject
     private ProduceService produceService;
 
+    private static final String HEADERS_KEY = "headers";
 
     @Override
     public void send(String ack, HermesMessage message, long timeout, ActionListener<MessageMeta> actionListener) {
@@ -48,7 +49,7 @@
             String actionName = "send";
             Supplier<MessageMeta> actionSupplier = () -> {
                 StopWatch watch = StopWatch.create().start();
-                MessageMeta meta = produceService.send(ack, message, timeout);
+                MessageMeta meta = produceService.send(ack, message, timeout, message.getHeaders());
                 log.info("send, topic {} cost(ms) {}", message.getTopic(), watch.stop().elapsed());
                 return meta;
             };
```

### 第二步：修改 `sendBatch` 方法

我将修改 `sendBatch` 方法，以便在批量发送消息时将 `headers` 设置到每条 Kafka 消息中。

<devin>
/patch:hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/ClientImpl.java
```patch
Index: hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/ClientImpl.java
--- hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/ClientImpl.java
+++ hermes-proxy/src/main/java/com/wacai/hermes/proxy/client/ClientImpl.java
@@ -67,7 +67,7 @@
             String actionName = "sendBatch";
             Supplier<BatchMessageMeta> actionSupplier = () -> {
                 StopWatch watch = StopWatch.create().start();
-                BatchMessageMeta meta = produceService.sendBatch(ack, messages, timeout);
+                BatchMessageMeta meta = produceService.sendBatch(ack, messages, timeout, messages.get(0).getHeaders());
                 log.info("sendBatch, topic {} cost(ms) {}", messages.get(0).getTopic(), watch.stop().elapsed());
                 return meta;
             };
```

### 下一步：修改 `ProduceService` 类以支持 header

在 `ClientImpl` 类修改后，我们需要修改 `ProduceService` 类，以便在发送消息时将 `headers` 设置到 Kafka 消息中。请允许我调用工具来获取 `ProduceService` 类的文件内容。

<devin>
/file:hermes-proxy/src/main/java/com/wacai/hermes/proxy/produce/ProduceService.java
</devin>
````









## 参考

- [揭秘 Cursor 系统提示词](https://mp.weixin.qq.com/s/HSzuU58KscWYNqECaiAovw)
- [claude-code-prompts](https://gist.github.com/transitive-bullshit/487c9cb52c75a9701d312334ed53b20c)
- [Cursor Agent mode System Prompt](https://baoyu.io/blog/cursor-agent-system-prompt)
- [leaked-system-prompts](https://github.com/jujumilk3/leaked-system-prompts)
- [Software-planning-mcp](https://github.com/NightTrek/Software-planning-mcp/blob/main/src/prompts.ts)
- [augment-swebench-agent](https://github.com/augmentcode/augment-swebench-agent/blob/main/prompts/instruction.py)
- [Cursor、Devin 等爆款系统提示词曝光](https://mp.weixin.qq.com/s/m6VwoSDfpPHSHW_76Q88RQ)