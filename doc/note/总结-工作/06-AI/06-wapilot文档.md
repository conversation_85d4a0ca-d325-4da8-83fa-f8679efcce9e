## 一、Wapliot

**wapliot**  IDEA插件，整合开发、测试、部署、辅助编程等功能，重点提供**面向场景的辅助编程**， 核心功能：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241115155823887.png" alt="image-20241115155823887" style="zoom:50%;" />

- Chat问答
- **代码补全**
- **CodeReview**
- **FindBug**
- **生成单元测试**
- **注释生成**
- **代码重构**
- **文档生成**
- 智能终端
- Git提交日志一键生成日报
- 云平台直达，项目一键部署
- **为Controller自动生成curl API**

扩展能力：遇到XXX，提供YYY的prompt，比如遇到sql文件，提供sql语法优化

### UI设计

参考业界的插件：

![image-20241213152955448](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241213152955448.png)

右键支持的指令：

- 代码解释
- 生成单元测试
- 生成注释
- 优化代码

【生成单元测试】的本质是发送一个指令(比如：unittest)给chat，然后就会返回结果：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241213153705960.png" alt="image-20241213153705960" style="zoom:50%;" />

可以停止，可以追问问题，可以复制，插入，新建。



### 架构

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241118111539432.png" alt="image-20241118111539432" style="zoom:50%;" />

The key is contextual understanding。

Our challenge is to figure out not only what data to feed the model, but also how to best order and enter it to get the best suggestions for the developer.

**How a prompt is created**: a series of algorithms first select relevant code snippets or comments from your current file and other sources (which we’ll dive into below). These snippets and comments are then prioritized, filtered, and assembled into the final prompt.



**Prompt 生成策略**是 核心功能，它可以根据你的代码上下文，生成最佳的代码提示

![image-20241213154136531](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241213154136531.png)

通常来说，一个指令对应的 prompt 会由以下五部分组成：

- Action 类型。比如：`Code complete`，`Translate to Kotlin` 等。
- 语言上下文 （结合规范）。比如：`Java`，`Kotlin`，`Python` 对应的规范。
- 技术栈上下文 （结合规范）。比如 Controller，Service，Repository 对应的规范。
- 相关上下文(ClassProvider)。比如：当前文件，当前文件夹，当前项目，当前项目的所有文件等。
- 代码(PsiElement)。当前的代码

不同语言会基于自己的模块，实现 ContextPrompter，比如 JavaContextPrompter，KotlinContextPrompter 等。



参考了 Intellij Rust、JetBrains AI Assistant 的模块化架构方式，如下图所示：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241213154308052.png" alt="image-20241213154308052" style="zoom:50%;" />

由每个语言模块基于抽象接口实现对应的：**语言上下文**、**技术栈上下文**，为此需要读取依赖相关的信息，如 gradle，maven，package.json 等。

AutoDev 提供了以下几种相关上下文：

- 基于静态代码分析的方式，即结合 import 语法和函数的输入、输出，生成对应的上下文信息。
  - 对应实现类：[JavaContextPrompter]
- 通过 Cosine Similarity 来计算最近打开 20 个文件代码块的相似度。即 GitHub Copilot、JetBrains AI Assistant 的实现方式之一。
  - 对应实现类：[SimilarChunksWithPaths]

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241213154432886.png" alt="image-20241213154432886" style="zoom:50%;" />



### 使用场景

这里记录一些场景，用于测试各种模型准确度

1. 对legion ETL生成单元测试
2. 数据库新增一个字段，自动修改相关代码
3. hermes consumer用法
4. drc代码解释



### 本地部署大模型

之前使用 ollama 部署本地大模型，发现 ollama支持的模型有限，比如FastApply-1.5B-v1.0 就没有，那么有没有开源工具可以把 huggingface 上的模型部署到本地，并且提供一个openAI api格式的http服务？调研了一下比较专业的是vllm

安装：

```
pip install vllm
```

vLLM 支持直接加载 Hugging Face 上的模型，因此 **不需要提前手动下载**：

```
python3 -m vllm.entrypoints.openai.api_server \
  --model Kortix/FastApply-1.5B-v1.0 \
  --port 8000
```

vLLM 默认会把从 Hugging Face 下载的模型缓存到 **`~/.cache/huggingface`** 目录下，这是由 [ Hugging Face Transformers](https://github.com/huggingface/transformers) 底层库控制的。

测试：

```
curl http://localhost:8000/v1/chat/completions \
  -H "Content-Type: application/json" \
  -d '{
    "model": "Kortix/FastApply-1.5B-v1.0",
    "messages": [{"role": "user", "content": "你是谁？"}],
    "temperature": 0.7
  }'
```

对于docker镜像我们希望提前手动下载，方法：

```python
#安装 huggingface_hub
pip install huggingface_hub

# 下载模型
from huggingface_hub import snapshot_download
snapshot_download(repo_id="Kortix/FastApply-1.5B-v1.0", local_dir="./model/FastApply-1.5B-v1.0")

```

启动

```bash
python3 -m vllm.entrypoints.openai.api_server \
  --model ./model/FastApply-1.5B-v1.0 \
  --port 8000 \
```

下面是一个本地推理的例子：

```python
from vllm import LLM, SamplingParams
import transformers

model_path = "XuanYuan-FinX1-Preview"
system_message = "你是一位擅长深度思考的助手。在回答问题之前，你会像人类一样展现出一步一步的思考过程，包括问题理解、分析推理、自我质疑、反思验证等环节，之后你会基于思考过程，作出准确的回答。同时总是拒绝参与不道德、不安全、有争议的问题。"

query = "你是谁"
messages = [
    [
        {"role": "system", "content": system_message},
        {"role": "user", "content": query},
    ]
]

sampling_params = SamplingParams(
    temperature=0.7, max_tokens=9000, repetition_penalty=1.02
)

# tensor_parallel_size=4 要与你的 GPU 数量相匹配
llm = LLM(
    model_path,
    tensor_parallel_size=1,
    gpu_memory_utilization=0.85,
    disable_custom_all_reduce=True,
)
tokenizer = transformers.AutoTokenizer.from_pretrained(model_path)
prompt = tokenizer.apply_chat_template(
    messages, tokenize=False, add_generation_prompt=True
)

result = llm.generate(prompt, sampling_params)
print(result[0].outputs[0].text)




```





### 模块划分

对于模块的划分有两种思路，之前一直没有梳理很清楚，参考了roo-code，感觉第二种更合适：

1、高内聚，每个子模块都有自己的ui、context、service

```
我在开发一个AI coding 的IDEA 插件，主要包括智能聊天(chat)和agent(职能体)两个Panel，用户通过聊天和LLM交互完成用户的需求。 这个项目按照业务职责， 我的模块(package)目前是这样的，你看是否合理
- action
    - ui
    - context
    - service
- agent
    - ui
    - context
    - service
- chat
    - ui
    - context
    - service
- ui
    - component
    - search
    - toolwindow
- base 
    - context
    - util
    - resource
- llm
- codebase
```

这种方案在实践工作中，感觉并不灵活，有点混乱

2、ui层统一在一起，只在service层按照职责划分：

```
- core： 核心业务流程，包括一些子模块
    - pipline: 包含了 Task、TaskExecutor、AgentExecutor、ChatExecutor等类。这 AgentExecutor依赖service模块下的devin  
    - prompt：包含提示词实现
    - tools： 包含内置工具实现，可能会依赖service下的某个服务
- ui:
    - chat
    - agent
    - action/intention
    - action/diff
    - action/toolbar
    - common/search
    - common/component

- service
    - devin
    - intention
    - history
```

chatGPT认为第二种划分方式明显体现出**架构分层+业务内聚的思想，它更贴近DDD（领域驱动设计）+ 插件架构模式**。相比第一种方案，这种方式更利于未来演进和扩展，原因

- 你未来可能实现让 Agent 调用 Chat，或者多 Agent 协同。第二种结构中，`pipeline` 负责 orchestrate，`service` 提供能力，是**自然的 AI Orchestration 模式**。
- `core/prompt` 和 `core/tools` 可以看作 Plugin 系统，也更容易支持如 LangChain/RAG/Code Interpreter 形式的扩展
- IDE 插件容易逻辑和 UI 混在一起，后期不好维护。第二种结构让 UI 仅做视图绑定，业务流程清晰集中在 core 和 service。

两种结构对比

| 维度             | 第一种结构                                                   | 第二种结构                                                   |
| ---------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| 📦 包组织粒度     | 基于业务模块（chat/agent）拆分，每个模块有 ui/context/service | 核心逻辑集中在 core，UI 和服务分离，强调核心逻辑复用性       |
| 🧠 模块职责清晰度 | 职责按模块内聚（chat 相关内容都在 chat 包）                  | 职责按横向层级划分（core 负责流程，service 提供能力，ui 只是展现） |
| 🔁 模块之间耦合   | chat/agent/context 等容易形成环依赖                          | core 调用 service，service 可以调用其他 service，结构更清晰，有利于单测 |
| 🧩 插件扩展性     | 扩展新模块需要复制 chat/agent 的结构模板                     | 可以新增 pipeline/task 类型、tools、prompt 等，结构对功能增长友好 |
| 🧪 可测试性       | 每个模块的 service 可以测试，但流程耦合在具体模块中          | `core/pipeline` 层流程集中管理，可单元测试执行逻辑，mock service 层 |
| 🤝 职能分离能力   | chat/agent 重心在“用户入口”而非“功能职责”                    | 明确分离“功能实现（service）” vs “流程编排（pipeline）” vs “界面渲染（ui）” |
| 🔄 LLM 调用结构   | 混在 chat/agent service 中调用 LLM                           | LLM 调用抽象在 prompt + pipeline + tools 中，便于实现 tool-augmented LLM agent 的架构 |





### Diff调研

一篇非常好的参考：

- [FastApply技术研究](https://www.cnblogs.com/boydfd/p/18759542)
- [fast-apply](https://x.com/markokraemer/status/1848960259890524543)
- [reddit introducing_fast_apply_replicate_cursors_instant](https://www.reddit.com/r/LocalLLaMA/comments/1ga25gj/introducing_fast_apply_replicate_cursors_instant/)
- [continue调用1.5B小模型实现代码fast-apply](https://www.cnblogs.com/mesopotamiaa/p/18524948)



Augment 这种只返回代码片段，使用 AST 就很难merge，只能通过LLM实现：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_420898fd-d2cf-4e4b-b2ac-94b9f7dd2e1f.png" alt="企业微信截图_420898fd-d2cf-4e4b-b2ac-94b9f7dd2e1f" style="zoom:50%;" />

LLM 会返回169行到末尾的所有代码内容，比较神奇的是它会使用项目内的 FontUtil 方法

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250508110646420.png" alt="image-20250508110646420" style="zoom:50%;" />





Augment agent 会多一个file list:

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250512190644936.png" alt="image-20250512190644936" style="zoom:50%;" />

点击会打开diff窗口，注意这个时候已经修改了文件，diff只是告诉你改了什么，没有accept和reject



trae:

![image-20250321170941597](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250321170941597.png)

当失败：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250325180602389.png" alt="image-20250325180602389" style="zoom:50%;" />





Cursor，直接展示代码改动，感觉并不是合适

![image-20250321174950363](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250321174950363.png)



### CodeBase调研

Augment blog:

- [代码库的实时索引：安全、个性化、可扩展](https://www.augmentcode.com/blog/a-real-time-index-for-your-codebase-secure-personal-scalable)
- [我们如何使用量化向量搜索将 1 亿多行代码库的代码搜索速度提高 40%](https://www.augmentcode.com/blog/repo-scale-100M-line-codebase-quantized-vector-search)

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250723111004650.png" alt="image-20250723111004650" style="zoom:50%;" />

Aider

- [repomap](https://aider.chat/docs/repomap.html)
- [使用 tree sitter 构建更好的存储库地图](https://aider.chat/2023/10/22/repomap.html)

Aider 的思路是为代码构建一个repo map，这个repo map只包含类的骨架，比如：

```python
aider/coders/base_coder.py:
⋮...
│class Coder:
│    abs_fnames = None
⋮...
│    @classmethod
│    def create(
│        self,
│        main_model,
│        edit_format,
│        io,
│        skip_model_availabily_check=False,
│        **kwargs,
⋮...
│    def abs_root_path(self, path):
⋮...
│    def run(self, with_message=None):
⋮...

aider/commands.py:
⋮...
│class Commands:
│    voice = None
│
⋮...
│    def get_commands(self):
⋮...
│    def get_command_completions(self, cmd_name, partial):
⋮...
│    def run(self, inp):
⋮...

```

Aider 通过仅发送**最相关的** 仓库地图的部分内容给LLM，避免context被打满，相关性计算通过图排名(graph ranking)算法

> Of course, for large repositories even just the repo map might be too large for GPT’s context window. Aider solves this problem by sending just the **most relevant** portions of the repo map. It does this by analyzing the full repo map using a **graph ranking algorithm,** computed on a graph where each source file is a node and edges connect files which have dependencies. Aider optimizes the repo map by selecting the most important parts of the codebase which will fit into the token budget assigned by the user (via the `--map-tokens` switch, which defaults to 1k tokens).

Aider这种方式不支持语义化搜索，个人觉得已经过时了



### 2.0

1、自定义知识库和自定义Action。自定义 Action 是提供把经常需要重复用到的任务抽取出来定义为prompt，自定义知识库是把中间件、业务文档形成知识库，让AI生成代码的时候可以参考。



代码逆向为自然语言文档后，在巨量的代码库和自然语言文档下，我的代码库会被拆解为各种场景，不同场景下加载不同的代码自然语言文档，从而我在写代码时，涉及到类似的需求时，能迅速知道这种需求的场景，从而加速代码实现，从而场景经验的学习不需要那么多的项目来训练累积。

1、给需求给大模型, 让大模型给出逻辑实现步骤, 再去让大模型去检索RAG知识库, 对实现的代码进行关联性检索, 然后在给出最终的结果，类似aider的效果：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250212114605913.png" alt="image-20250212114605913" style="zoom:30%;" />

2、自定义prompt作为context

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250212163448741.png" alt="image-20250212163448741"  />

![image-20250307105427769](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250307105427769.png)

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250212163725805.png" alt="image-20250212163725805" style="zoom:50%;" />



### 3.0

DOTO LIST

1. system prompt 优化，丰富更多的内建tools
2. team prompt
3. checkpoint机制
4. mcp支持
5. 更好的代码apply支持
6. **codebase，如何让LLM知道已存在的代码库**
7. context token 大小显示
8. 历史增加搜索功能
9. agent 限制最大轮次，避免无限循环(好像还好，超过context_size就会退出)

codebase的一些调研，询问chatGPT:

> 我在打造一款ai coding工具，我可以通过grep项目本地文件构建合适的context给LLM然后完成用户的提问，但对于java项目存在maven依赖，这些依赖的二方库我无法搜索，你有什么好的建议解决这类问题吗？



构建依赖类的结构化语义索引

假设用户在你的 AI Coding 工具中提问：

“ObjectMapper 怎么把一个 JSON 字符串转换成 Java 对象？”

这个类是 Jackson 提供的，用户项目中并没有定义这个类，而是通过 Maven 引入的依赖。

在构建依赖库索引时，使用 javaparser 构建索引，把这些结果存在本地 SQLite、MapDB 或 JSON 文件中，构成一个 “符号名 → 上下文” 的快速查找表。

```
{
  "symbol": "ObjectMapper",
  "fqcn": "com.fasterxml.jackson.databind.ObjectMapper",
  "filepath": "/path/to/ObjectMapper.java",
  "javadoc": "This mapper provides functionality for reading and writing JSON...",
  "methods": [
    {
      "name": "readValue",
      "signature": "readValue(String content, Class<T> valueType)",
      "returnType": "T",
      "javadoc": "Reads JSON content and binds it into specified Java type."
    },
    ...
  ]
}

```





### 开发问题

#### 1、回答问题没有实时渲染

详见 [issues/252](https://github.com/unit-mesh/auto-dev/issues/252)，参考了tongyi的实现



#### 2 、ChatCodingPanel 代码重构

目前聊天的执行过程：

```
ChatCodingPanel.inputSection.onSubmit() ->  ChatCodingService.handlePromptAndResponse() -> ChatCodingPanel.addMessage()
```

存在问题：

- 循环依赖
- Action无法复用



#### 3、Mermaid 渲染问题

autodev渲染Mermaid采用的是 com.intellij.mermaid 原生实现，代码如下：

```java
public class WapilotToolWindowPanel {
     private String diagram;

    public WapilotToolWindowPanel(Project project, Disposable disposable) {
        this.project = project;
        this.disposable = disposable;
        this.setupUI();
    }

    private void setupUI() {
        mainPanel = new JPanel(new BorderLayout());

        FileEditor fileEditor = FileEditorPreviewSketch.createMermaidEditor(project,diagram);
        mainPanel.add(fileEditor.getComponent());
    }


    private static class FileEditorPreviewSketch {

        private static FileEditor createMermaidEditor(Project project,String diagram) {

            LightVirtualFile virtualFile = new LightVirtualFile("mermaid.mermaid", diagram);
            virtualFile.putUserData(TextEditorWithPreview.DEFAULT_LAYOUT_FOR_FILE,
                    Layout.SHOW_EDITOR_AND_PREVIEW);

            return buildEditorProvider("MermaidEditorWithPreviewProvider")
                    .createEditor(project, virtualFile);
        }

        private static FileEditorProvider buildEditorProvider(String withPreviewEditorId) {
            return FileEditorProvider.EP_FILE_EDITOR_PROVIDER
                    .getExtensionList()
                    .stream()
                    .filter(it -> it.getClass().getSimpleName().equals(withPreviewEditorId))
                    .findFirst()
                    .orElse(TextEditorProvider.getInstance());
        }

    }

}
```

存在的问题是**强制需要用户安装com.intellij.mermaid插件**，所以最近决定采用 tongyi 的方案通过js渲染

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_f6779393-f8a5-4403-907a-38860d2a2e18.png" alt="企业微信截图_f6779393-f8a5-4403-907a-38860d2a2e18" style="zoom:50%;" />

#### 4、JBList+DefaultListCellRenderer

发现JBList 实现列表限制太多了，主要是无法监听item中的事件，处理起来非常麻烦，还不如通过Jpanel实现

### 参考资料

2025-04

- [Fast Apply 模型](https://www.reddit.com/r/LocalLLaMA/comments/1ga25gj/introducing_fast_apply_replicate_cursors_instant/)
- [OpenAI SWE-bench 介绍](https://openai.com/index/introducing-swe-bench-verified/)
- [anthropic blog 介绍SWE-bench的Agent](https://www.anthropic.com/engineering/swe-bench-sonnet)
- [augmentcode 技术细节](https://www.augmentcode.com/blog/1-open-source-agent-on-swe-bench-verified-by-combining-claude-3-7-and-o1)
- [augment-swebench-agent](https://github.com/augmentcode/augment-swebench-agent/blob/main/prompts/instruction.py)

earlier

- [devins](https://ide.unitmesh.cc/devins/quick-start.html)
- [**AI 研发提效：构建 AI 辅助编码助手**](https://unit-mesh.github.io/build-your-ai-coding-assistant/)
- [3 ways to customize an LLM](https://www.linkedin.com/pulse/3-ways-customize-llm-why-you-should-github-1hpyc/)
- [How GitHub Copilot is getting better at understanding your code](https://github.blog/ai-and-ml/github-copilot/how-github-copilot-is-getting-better-at-understanding-your-code/)
- [花了大半个月，我终于逆向分析了Github Copilot](https://github.com/mengjian-github/copilot-analysis)
- [Copilot Internals ](https://thakkarparth007.github.io/copilot-explorer/posts/copilot-internals)
- [大模型微调指南](https://hackernoon.com/creating-a-domain-expert-llm-a-guide-to-fine-tuning)
- [**Tabby AI coding assistant**](https://github.com/TabbyML/tabby) 但是官方没有任何文档解释架构
- [FauxPilot-GitHub Copilot serve](https://github.com/fauxpilot/fauxpilot)
- [**Aider-Building a better repository map with tree sitter**](https://aider.chat/2023/10/22/repomap.html)
- [SweepAI](https://docs.sweep.dev/)
- [ChatGLM-6B](https://github.com/THUDM/ChatGLM-6B)
- https://monica.im/en/code
- [aider-分离coding和推理模型](https://aider.chat/2024/09/26/architect.html)

GitHub项目：

- [DevoxxGenieIDEAPlugin](https://github.com/devoxx/DevoxxGenieIDEAPlugin) 一个有意思的AI插件
- [bloop](https://github.com/BloopAI/bloop)

InfoQ:

- [InfoQ-Copilot 应用构建实践](https://aicon.infoq.cn/2024/beijing/track/1660)
- [InfoQ-大模型技术重塑智能研发新范式](https://www.infoq.cn/article/xgO2lNpRrQTerHK2LDxL)
- [AI 驱动的智能化单元测试生成：字节跳动的实践与创新](https://www.infoq.cn/article/8mVuhrLMMEJ8MDjo1U4e)

phodal方法论：

- [asie总结](https://aise.phodal.com/index.html)
- [云端与 IDE 智能体整合：解决工具碎片化，实现 AI 全流程自动编码](https://zhuanlan.zhihu.com/p/718809716)
- [一线落地 AI 辅助研发的实践心得：从工程、工具到未来展望](https://www.phodal.com/blog/my-aise-summary-2024/)

HyDE：

- [HyDE 提升文档检索](https://framework.unitmesh.cc/patterns/hyde.html)
- [HyDE产生假设代码的prompt](https://github.com/unit-mesh/auto-dev-vscode/blob/master/prompts/genius/en/hyde/code/propose.vm)
- [how-to-prompt-code-llama](https://ollama.com/blog/how-to-prompt-code-llama)
- [CodeQwen1.5 fim指令](https://github.com/QwenLM/Qwen2.5-Coder/blob/b082fc3f5302cb6a63efa8fcc9dbf572a3c2303e/examples/CodeQwen1.5-base-fim.py#L8)

Prompt:

- 记录一些提示词

- [群里贡献的OiAnthony-prompt](https://github.com/OiAnthony/continue-prompts/blob/master/.prompts/commit.prompt)

- [awesome-cursorrules](https://github.com/PatrickJS/awesome-cursorrules)

```
你是一个业务分析师，而我是领域专家，我会提供用户故事
==USER STORY
{userstory}}
===END OF USER STORY
你忽略技术细节，并思考用户故事中不清晰的部分，并向我提出问题帮助你澄清这个用户故事请注意:
-每次只问一个问题
如果你觉得对用户故事了解了足够多的内容，就列出所有场景。使用 Given/When/Then 的格式表述，Given/When/Then 带上的内容用中文回答
至少提问 3个问题，至多提问 10 个问题
当用户输入"开始"时，你才提出问题
```





```properties
为以下代码编写单元测试
- 你正在开发一个使用 Java SDK 1.8 版本的项目
- 测试文件应完整且可编译，无需进一步操作。
- 确保每个测试聚焦于单个用例，以保持清晰性和可读性。
- 不要使用@BeforeEach方法进行设置，应在每个独立的测试方法内包含所有必要的代码初始化，不要编写参数化测试。
- 本项目使用 JUnit 4，你应导入org.junit.Test并使用@Test注解。

以下是相关类的信息：
class HermesMessage {
    String topic; 
    Integer partition; 
    byte[] key;
    byte[] data;
    Map<String, byte[]> headers;
}

以下是当前类的信息：
package com.wacai.hermes.proxy.controller.ProducerController;
@RestController
class ProducerController {
  	
  	@Autowired
    private KafkaProducerService producerService;
  	
  	@PostMapping("/hermes-proxy/sendSingle")     
  	public WebApiResponse sendSingle( @RequestBody HermesMessage hermesMessage);
  	
  	@PostMapping({"/kafka/publish","/kafka-proxy/kafka/publish"})     
  	public String publish(HttpServletRequest request);
}
```







## 二、业界方案

### 1、Tongyi插件

国内大厂原理都差不多，插件只是一层皮，核心是一个独立进程，中间通过websocket通信

#### 基本原理

tongyi会在用户目录下创建.lingma 目录，通过下面命令启动服务

```
/Users/<USER>/.lingma/bin/1.3.14/aarch64_darwin/Lingma start
```

会启动一个 36510 端口

日志路径：

```
~/Library/Logs/JetBrains/IdeaIC2023.3/idea.log
```

开启debug日志：

>在插件开发期间，可以在 Help -> Diagnostic Tools -> Debug Log Settings... 中配置日志级别。输入插件类的完整包名（#com.alibabacloud.intellij）注意#代表debug。

.info文件包含两个数字分别是端口和pid，默认36510

Cosy 初始化会通过connectCosyServer()连接CosyServer：

```java
private boolean connectCosyServer(Project project, File homeDir, boolean debugMode) throws IOException {
    LanguageWebSocketService languageService = LanguageWebSocketService.createService((int)port);
    languageService.connect(); //内部会为CosyWebSocketClient 创建LanguageServer 动态代理

    //按照项目维度建立缓存
    languageServiceMap.put(project.getLocationHash(), languageService);

    //开启心跳
    Thread heartBeat = new Thread((Runnable)new CosyHeartbeatRunner(project, languageService.getWebSocketClient().getSession(), languageService.getServer()));
    heartBeat.start();    
}
```

LanguageWebSocketService.connect()比较重要，内部会为CosyWebSocketClient的 LanguageServer 接口创建动态代理，依赖 org.eclipse.lsp4j.jsonrpc.Launcher 实现的，通过 JSON-RPC，Launcher 会为远程对象创建代理，以便本地代码可以像调用本地对象一样调用远程服务。

具体可以参考TongyiSpy

模块组成

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241115105623754.png" alt="image-20241115105623754" style="zoom:50%;" />

chat模块

定义了很多和用户交互的入口。action定义了用户动作：

- CosyCodeGenerateCommentGenerationAction： 产生注释
- CosyCommitMessageGenerationAction： 产生commit
- CosyExplainCodeGenerationAction: 代码解释
- CosyOptimizeCodeGenerationAction： 代码优化
- CosySelectionChatAction： 选中聊天
- CosyTerminalFixAction： 修复终端
- CosyTestcaseGenerationAction： 单元测试产生

#### UI

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250219173619594.png" alt="image-20250219173619594" style="zoom:50%;" />



<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250219173632802.png" alt="image-20250219173632802" style="zoom:50%;" />

Plugin 配置：

```xml
<toolWindow id="Code Search" secondary="false" anchor="right" icon="LingmaIcons.ToolWindowIcon" canCloseContents="true"
                factoryClass="com.alibabacloud.intellij.cosy.ui.search.SearchToolWindowFactory"/>
```

ToolWindow 的工厂类是 com.alibabacloud.intellij.cosy.ui.search.SearchToolWindowFactory，实现了createToolWindowContent()方法：

```java
  private static void createSearchMainPanel(@NotNull Project project, @NotNull ToolWindow toolWindow, Consumer<Project> consumer) {
        //获取 IDEA 的ContentFactory
        ContentFactory contentFactory = ContentFactory.SERVICE.getInstance();
        //初始化 LingmaToolWindowPanel
        LingmaToolWindowPanel mainPanel = new LingmaToolWindowPanel(project);
        mainPanel.delayInit();
        //创建content
        Content content = contentFactory.createContent(mainPanel.getMainPanel(), I18NConstant.MAIN_CONTENT_NAME, false);
        content.setCloseable(false);
        toolWindow.setTitle(I18NConstant.COSY_PLUGIN_NAME);
        //添加content
        toolWindow.getContentManager().addContent(content);

        //为工具窗口（ToolWindow）的标题栏添加自定义的动作按钮:1.历史会话，2.使用反馈，3.帮助文档
        SearchToolWindowFactory.createToolWindowActions(project, toolWindow);
        if (consumer != null) {
            consumer.consume((Object)project);
        }
    }
```







com.intellij.psi.PsiElement是IDEA 平台下 PSI（Program Structure Interface）中的一个核心接口。PSI 是一个用于表示和操作程序源代码结构的抽象层。
它是所有代表程序元素（如类、方法、变量、表达式等）的节点的基类。在 Java 开发环境中，当 IntelliJ IDEA 解析 Java 源代码时，会构建一个基于PsiElement的树状结构，其中每个节点都对应源代码中的一个元素。

主要作用

1. **语法树构建**。用于构建代表源代码结构的语法树。例如，对于以下简单的 Java 代码public class HelloWorld { public static void main(String[] args) { System.out.println("Hello, World!"); } }，IntelliJ IDEA 会将这段代码解析为一个由PsiElement组成的树。其中，PsiClass（实现了PsiElement接口）代表HelloWorld类，PsiMethod代表main方法，PsiExpressionStatement代表方法体中的语句等。
2. **代码导航和分析**。提供了在代码结构中进行导航的功能。通过PsiElement，可以方便地在代码元素之间移动。例如，从一个方法调用找到其对应的方法声明，或者从一个变量引用找到变量的定义。这对于代码导航操作（如在编辑器中通过快捷键跳转到某个元素的定义处）非常重要。支持代码分析工具。可以检查代码结构是否符合语言规范，进行语义检查等。比如，检查方法是否被正确调用，变量是否在使用前被初始化等。开发插件时，可以利用PsiElement遍历代码树，查找可能的代码问题。
3. **代码修改和生成**。能够对代码进行修改。可以通过操作PsiElement来添加、删除或修改代码元素。例如，在插件开发中，为代码自动添加注释、插入新的方法调用或者修改方法签名等操作。
   辅助代码生成。在根据某些模板或规则生成代码时，PsiElement可以帮助确定生成代码的位置和结构。比如，自动生成 Java 的实体类（包括属性、构造函数、getter 和 setter 方法等），通过PsiElement可以将生成的代码正确地插入到现有的代码结构中。

模块ui

**GenerateContentForm** 是一个非常重要的类，负责订阅消息，触发chat动作

**SearchToolWindowFactory**负责管理ToolWIndow,

```java
public class SearchToolWindowFactory implements DumbAware, ToolWindowFactory {
    public static void showToolWindow(Project project) {
        ToolWindow toolWindow = ToolWindowManager.getInstance((Project)project).getToolWindow("Code Search");
        if (toolWindow != null) {
            toolWindow.show(null);
            ((LingmaOpenToolWindowNotifier)project.getMessageBus().syncPublisher(LingmaOpenToolWindowNotifier.LINGMA_OPEN_TOOL_WINDOW_NOTIFIER_TOPIC)).openToolWindow();
        }
    }

    public static void hideToolWindow(Project project) {
        ToolWindow toolWindow = ToolWindowManager.getInstance((Project)project).getToolWindow("Code Search");
        if (toolWindow != null && toolWindow.isVisible()) {
            toolWindow.hide(null);
        }
    }

    public void createToolWindowContent(@NotNull Project project, @NotNull ToolWindow toolWindow) {
        SearchToolWindowFactory.createSearchMainPanel((Project)project, (ToolWindow)toolWindow, null);
    }
}
```

#### Context 选择

当点击下面这个+号回弹出一个框，这个框对应的类叫SuggestPromptPopupPanel：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250220174419782.png" alt="image-20250220174419782" style="zoom:50%;" />

SuggestPromptPopupPanel 里面的项目就是promptItemList，每个promptItemList.mouseClicked() 事件，点击会触发流程如下：

```
SuggestPromptPopupPanel.handleChosenSuggest() -> ChatInputSuggester.triggerAddContextTag() -> ChatInputSuggester.addContextTag() -> AskInputPanel.addContextTag() ->ChatContextTagsPanel.addOrUpdateTag()
```

对应了添加文件的流程。

SuggestPromptPopupPanel是如何显示的呢？

ChatContextAddButton 是自定义的按钮实现类，点击会调用它的 handleClick()，代码如下：

```java
    public void handleClick() {
        this.setResidentFocus(true);
        suggestPromptPopupPanel.displaySuggestPrompts("",
                "context",
                this.tagsPanel.getChatInputContext().getInputMainPanel().getInputPanel().getLocationOnScreen(),
                "contextPanel", null);
    }
```

当选择一个文件时，会调用 ChatInputSuggester.triggerChooserSuggestPromptSelection()

选中代码会更新对应的context，入口在AskInputPanel.notifySelectedCode() 



#### Action到Chat的触发流程

单元测试、代码解释底层逻辑都是类似，这里以单元测试为例记录触发流程。

主要流程：

```
BaseCosySelectGenerationAction.syncPublisher() -> GenerateContentForm.anyGenerate() -> GenerateContentForm.prepareAnswer() -> GenerateContentForm.asynchronousChat()
```



1、 BaseCosySelectGenerationAction 发送事件通知

```java
public void actionPerformed(@NotNull AnActionEvent e) {
     //① 获取聊天上下文
    ChatContext chatContext = this.buildChatContext(project, psiFile, code, selectionModel, range);
    //② 获取右侧的ToolWindow窗口，如果为空进行创建
    ToolWindow toolWindow = ToolWindowManager.getInstance((Project)project).getToolWindow("Code Search");
    if (toolWindow == null) {
        ToolWindowManager.getInstance((Project)project).registerToolWindow(RegisterToolWindowTask.closable((String)"Code Search", (Icon)CommonIcons.AI, (ToolWindowAnchor)ToolWindowAnchor.RIGHT));
        toolWindow = ToolWindowManager.getInstance((Project)project).getToolWindow("Code Search");
    }
    //③发送消息
    project.getMessageBus().syncPublisher(AnyGenerateNotifier.ANY_GENERATE_NOTIFICATION))
                    .anyGenerate(context, genInput, requestId, this.needClearContext(chatTask);
}
```



2、GenerateContentForm.anyGenerate() 收到事件

```java
private void anyGenerate(){
    
    //①切换到聊天tab
    project.getMessageBus().syncPublisher(LingmaTabChangeNotifier.CHANGE_MAIN_TAB_NOTIFICATION)).changeTab("chat");

    //②触发提问
    triggerAsk(input, requestId);
}
```

3、更新 GenerateContentForm 内的Panel 

GenerateContentForm 的 triggerAsk()方法 会触发 GenerateContentForm.prepareAndAsynchronousChat()，它会调用 prepareAnswer() 和 asynchronousChat() ，前者是更新UI，后者是异步发送请求

```java
public void prepareAnswer(GenerateInput input, String chatTask, String displayText, String requestId) {
    
    //①创建GenerateAnswerPanel
    this.answerPanel = new GenerateAnswerPanel(this.project, this, this.basePanel, chatTask, chatAskInput, SessionTypeEnum.CHAT);
    //② 创建ChatQuestionForm
    this.chatQuestionForm = new ChatQuestionForm(this.project, this.basePanel, extraTextFormChatContext, null, chatAskInput, requestId, chatTask, SessionTypeEnum.CHAT.getType(), this.answerPanel);

     //③ 将 ChatQuestionForm 和 GenerateAnswerPanel 的组件添加到流程面板中
    this.flowPanel.add(this.chatQuestionForm.getComponent());
    this.flowPanel.add(this.answerPanel.getComponent());
}

```

流程：

1. 创建ChatQuestionForm 用户提问的Panel
2. 创建GenerateAnswerPanel AI回答的Panel
3. 都会被加入到flowPanel

4、 发送 question 请求

这一步主要流程：

- GenerateContentForm.asynchronousChat() 内部主要创建一个 创建后台任务，调用CosyService.chatAsk()
- 收到CosyServer的回答，触发LanguageClient.answer()，会调用ChatAnswerProcessor.updateAnswer()
- ChatAnswerProcessor.updateAnswer()组装回答，并发送 GenerateStreamPushNotifier.GENERATE_STREAM_PUSH_NOTIFICATION 事件
- GenerateContentForm 监听 GENERATE_STREAM_PUSH_NOTIFICATION，执行pushGenerate()

5、回答完成

- 当回答完毕，CosyServer会主动调用 LanguageClientImpl.finish() 触发 ChatFinishProcessor.finish() 发送 GenerateStreamStopNotifier.GENERATE_STREAM_STOP_NOTIFICATION 事件。
- GenerateContentForm 订阅该事件，触发 stopGenerate()方法。



#### 代码补全

在自动代码补全上，国内的厂商主要参考的是 GitHub Copilot 的实现，其主要是在 Action 里监听用户的输入，然后:

| 功能               | 快捷键      | 说明                                       |
| ------------------ | ----------- | ------------------------------------------ |
| requestCompletions | `Alt` + `/` | 获取当前的上下文，然后通过模型获取补全结果 |
| applyInlays        | `TAB`       | 将补全结果展示在 IDE 上                    |
| disposeInlays      | `ESC`       | 取消补全                                   |
| cycleNextInlays    | `Alt` + `]` | 切换到下一个补全结果                       |
| cyclePrevInlays    | `Alt` + `[` | 切换到上一个补全结果                       |

主要通过 `EditorFactoryListener` 监听用户的输入，然后：根据不同的输入，触发不同的补全结果

```java

public class CosyEditorListener implements EditorFactoryListener {
     private final CosyInlayCompletionSelectionListener selectionListener = new CosyInlayCompletionSelectionListener();

    public void editorCreated(@NotNull EditorFactoryEvent event) {
        Editor editor;
        Project project;
        if (event == null) {
            CosyEditorListener.$$$reportNull$$$0((int)0);
        }
        if ((project = (editor = event.getEditor()).getProject()) == null || project.isDisposed() || !CosyInlayManager.getInstance().isAvailable(editor)) {
            return;
        }
        Disposable editorDisposable = Disposer.newDisposable("cosyEditorListener");
        EditorUtil.disposeWithEditor(editor, editorDisposable);
        editor.getCaretModel().addCaretListener(new CosyCaretListener(editor), editorDisposable);
        editor.getDocument().addDocumentListener(new CosyDocumentListener(editor), editorDisposable);
        editor.getSelectionModel().addSelectionListener(this.selectionListener, editorDisposable);
    }
}
```



比较重要的是 CosyDocumentListener，内部会监听用户输入，会自动保存DocumentIndex和触发CosyInlayManagerImpl.editorChanged()。该方法会进一步调用InlayPreviewRequest.generate()，然后调用CosyCompletionServiceImpl.asyncCompletionInlay()异步发送请求，

#### LLM聊天

tongyi实现了实时代码块解析，通过 **GenerateAnswerPanel** 和  **MarkdownStreamPanel** 实现，GenerateAnswerPanel 代表整个回答容器，包括按钮等操作，
MarkdownStreamPanel 是其中的回答的内容，入口在 MarkdownStreamPanel.append()方法，主要流程如下：

1. append()接收llm返回的增量结果，首先会被append到本地StringBuffer
2. 调用 parseBlock()解析StringBuffer得到 List<MarkdownBlock> 然后和实例中的blocks合并。
3. 调用renderComponent()渲染blocks：迭代block，如果NeedUpdate为false则跳过，否则如果block的Component为空创建TextMarkdownComponent或者CodeMarkdownHighlightComponent，并加入到当前panel，调用block.getComponent().updateText()



深入CodeMarkdownHighlightComponent细节，updateText()会触发内部染代码块，主要流程：

1. CodeMarkdownHighlightComponent 内部包含了一个 **CodeTextPane**，updateText()会调用 CodeTextPane.setText()
2. **CodeTextPane 是一个 JTextPane**，其 ContentType是text/html，当setText()被调用时，会通过 Prism4j 把java代码高亮显示为html，比如这段java代码被渲染为html

```
<span class="keyword">public</span> <span class="class-name">String</span> <span class="function">convert</span><span class="punctuation">(</span><span class="class-name">String</span> code<span class="punctuation">,</span> <span class="class-name">String</span> language<span class="punctuation">)</span> <span class="punctuation">{</span>\n    <span class="class-name">Grammar</span> grammar <span class="operator">=</span> <span class="keyword">this</span><span class="punctuation">.</span>prism4j<span class="punctuation">.</span><span class="function">grammar</span><span class="punctuation">(</span>language<span class="punctuation">)</span><span class="punctuation">;</span>\n    <span class="keyword">if</span> <span class="punctuation">(</span>grammar <span class="operator">==</span> <span class="keyword">null</span><span class="punctuation">)</span> <span class="punctuation">{</span>\n        <span class="keyword">return</span> <span class="keyword">null</span><span class="punctuation">;</span>\n    <span class="punctuation">}</span>\n    <span class="class-name">StringBuilder</span> sb <span class="operator">=</span> <span class="keyword">new</span> <span class="class-name">StringBuilder</span><span class="punctuation">(</span><span class="punctuation">)</span><span class="punctuation">;</span>\n    <span class="class-name">List</span><span class="generics"><span class="punctuation">&lt;</span><span class="class-name">Node</span><span class="punctuation">&gt;</span></span> nodes <span class="operator">=</span> t

```

我尝试研究过Prism4j，发现问题很大，tongyi使用的Prism4j-1.0-SNPAHOST，怀疑是阿里内容维护的，[开源版本](https://github.com/noties/Prism4j)只有2.0而且已经不维护，问了chatGPT和deepseek全是乱说的，产生的代码API对不上，编译不过，直接使用Prism4j-1.0-SNPAHOST产生的html发现不能用，还需要维护一个body模版，需要自己维护不同的皮肤，最后还是放弃了



#### AI 程序员

tongyi在25年1月份推出的2.0版本引入了AI程序员，新增一个 **ChatContentPanel** 用来封装AI程序员面板，当点击AITab会显示 **ChatContentPanel**，可以看到 **ChatContentPanel**也会复用 **GenerateAnswerPanel** 和 **AskInputPanel**

![image-20250122103128570](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250122103128570.png)



<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250122110007158.png" alt="image-20250122110007158" style="zoom:50%;" />

1、工作区这些文件是如何产生的？

tongyi后端服务在回答完问题之后，会主动推送调用 LanguageClientImpl.syncAllSnapshots() 发送 SNAPSHOT_SYNC_NOTIFICATION 事件，触发WorkingSpacePanel.syncWorkspaceFileAdded()构建 FileItem，然后添加到**AIDevFilePanel**。 AIDevFilePanel 属于 WorkingSpacePanel 中的一部分

2、如何实现editor内的 diff 

触发路径：

```
AIDevFilePanel.mouseClicked() -> AppliedFileContextProvider.handleFileClicking() -> InlineDiffManagerImpl.showMultipleDiff()
```

在 InlineDiffManagerImpl.showMultipleDiff() 内部会经过处理最终会调用 showSingleDiff()，在该方法末尾会通过 DiffEditableTab 显示Diff。



### 2、Marscode插件

字节的marscode架构和tongyi类似，也是把关键的rag放在了外面服务通过websocket进行交互。

```
GenerateUnitTestsAction -> IChatService ->AIServer
```

通过 arthas 对AIServer.chatCompletion()监控，关键参数和tongyi都和类似：

```
scene=generate_unittests
locale=zh
uri=file:///System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/buzz-java/src/main/java/com/buzz/titan/DataTree.java
selectedCode=getChildren
wholeRange=@Range[
                start=@Position[Position(line=72, character=24)],
                end=@Position[Position(line=72, character=35)],
            ],
selection=@Selection[
    start=@Position[Position(line=72, character=24)],
    end=@Position[Position(line=72, character=35)],
    active=@Position[Position(line=72, character=24)],
    anchor=@Position[Position(line=72, character=35)],
],
```



### 3、Copilot

#### 基本原理

TODO

#### prompt的示例

Copilot 的prompt示例

```json
{
  "prefix": "# Path: codeviz\\app.py\n# Compare this snippet from codeviz\\predictions.py:\n# import json\n# import sys\n# import time\n# from manifest import Manifest\n# \n# sys.path.append(__file__ + \"/..\")\n# from common import module_codes, module_deps, module_categories, data_dir, cur_dir\n# \n# gold_annots = json.loads(open(data_dir / \"gold_annotations.js\").read().replace(\"let gold_annotations = \", \"\"))\n# \n# M = Manifest(\n#     client_name = \"openai\",\n#     client_connection = open(cur_dir / \".openai-api-key\").read().strip(),\n#     cache_name = \"sqlite\",\n#     cache_connection = \"codeviz_openai_cache.db\",\n#     engine = \"code-davinci-002\",\n# )\n# \n# def predict_with_retries(*args, **kwargs):\n#     for _ in range(5):\n#         try:\n#             return M.run(*args, **kwargs)\n#         except Exception as e:\n#             if \"too many requests\" in str(e).lower():\n#                 print(\"Too many requests, waiting 30 seconds...\")\n#                 time.sleep(30)\n#                 continue\n#             else:\n#                 raise e\n#     raise Exception(\"Too many retries\")\n# \n# def collect_module_prediction_context(module_id):\n#     module_exports = module_deps[module_id][\"exports\"]\n#     module_exports = [m for m in module_exports if m != \"default\" and \"complex-export\" not in m]\n#     if len(module_exports) == 0:\n#         module_exports = \"\"\n#     else:\n#         module_exports = \"It exports the following symbols: \" + \", \".join(module_exports)\n#     \n#     # get module snippet\n#     module_code_snippet = module_codes[module_id]\n#     # snip to first 50 lines:\n#     module_code_snippet = module_code_snippet.split(\"\\n\")\n#     if len(module_code_snippet) > 50:\n#         module_code_snippet = \"\\n\".join(module_code_snippet[:50]) + \"\\n...\"\n#     else:\n#         module_code_snippet = \"\\n\".join(module_code_snippet)\n#     \n#     return {\"exports\": module_exports, \"snippet\": module_code_snippet}\n# \n# #### Name prediction ####\n# \n# def _get_prompt_for_module_name_prediction(module_id):\n#     context = collect_module_prediction_context(module_id)\n#     module_exports = context[\"exports\"]\n#     module_code_snippet = context[\"snippet\"]\n# \n#     prompt = f\"\"\"\\\n# Consider the code snippet of an unmodule named.\n# \nimport json\nfrom flask import Flask, render_template, request, send_from_directory\nfrom common import *\nfrom predictions import predict_snippet_description, predict_module_name\n\napp = Flask(__name__)\n\<EMAIL>('/')\ndef home():\n    return render_template('code-viz.html')\n\<EMAIL>('/data/<path:filename>')\ndef get_data_files(filename):\n    return send_from_directory(data_dir, filename)\n\<EMAIL>('/api/describe_snippet', methods=['POST'])\ndef describe_snippet():\n    module_id = request.json['module_id']\n    module_name = request.json['module_name']\n    snippet = request.json['snippet']\n    description = predict_snippet_description(\n        module_id,\n        module_name,\n        snippet,\n    )\n    return json.dumps({'description': description})\n\n# predict name of a module given its id\<EMAIL>('/api/predict_module_name', methods=['POST'])\ndef suggest_module_name():\n    module_id = request.json['module_id']\n    module_name = predict_module_name(module_id)\n",
  "suffix": "if __name__ == '__main__':\r\n    app.run(debug=True)",
  "isFimEnabled": true,
  "promptElementRanges": [
    { "kind": "PathMarker", "start": 0, "end": 23 },
    { "kind": "SimilarFile", "start": 23, "end": 2219 },
    { "kind": "BeforeCursor", "start": 2219, "end": 3142 }
  ]
}
```

完整的prompt参考：[prompt-full](https://thakkarparth007.github.io/copilot-explorer/posts/prompt-full)  

最重要的是前缀的计算：

- 有6中不同的element: BeforeCursor 、 AfterCursor 、 SimilarFile 、 ImportedFile 、 LanguageMarker 、 PathMarker
- 由于提示大小有限，因此愿望清单按优先级和插入顺序排序，然后将元素添加到提示中，直到达到大小限制
- 后缀的逻辑]相对简单 - 只需用光标中可用的任何后缀填充可用的令牌预算即可



### 4、Aider

#### 使用

目前没有找到如何接入火山的方法，主要火山的模型和地址都不一样，尝试了几种配置都不行，比如：

```
export  DEEPSEEK_API_BASE=https://ark.cn-beijing.volces.com/api/v3/chat/completions
export  DEEPSEEK_API_KEY=b76b6732-4312-4c9e-b533-4b91cb177387
export  DEEPSEEK_MODEL=deepseek-v3-241226
```

最后只能使用deepseek官方的方式：

```shell
export DEEPSEEK_API_KEY=*********************************** 
/Users/<USER>/.local/bin/aider --model deepseek/deepseek-chat
```



### 6、DevoxxGenieIDEAPlugin

[DevoxxGenieIDEAPlugin](https://github.com/devoxx/DevoxxGenieIDEAPlugin) 是无意中发现一个轻量级AI IDEA插件，比较好奇他的diff 实现 所以调研了下。

#### 基本聊天

入口是 ActionButtonsPanelController.handlePromptSubmission()  ，代码如下:

```java
    ChatMessageContext currentChatMessageContext =
            ChatMessageContextUtil.createContext(project,
                    userPromptText,
                    getSelectedLanguageModel(),
                    chatModelProvider,
                    actionCommand,
                    editorFileButtonManager,
                    projectContext,
                    isProjectContextAdded);

    return promptExecutionControlle        .handlePromptSubmission(currentChatMessageContext);
```

ChatMessageContext 包含了LanguageModel等信息，我改了getSelectedLanguageModel()直接返回 deepseek 接入公司一直失败



#### diff 

我打开一个文件，然后聊天框中输入 add java doc，它会自动打开一个新的Diff:

![image-20250123111115725](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250123111115725.png)

实现原理： 在  ResponseDocumentPanel 处理LLM结果中会解析 markDownResponse， 然后通过 DiffManagerEx 对比两个文件的差异。说起来也挺简单的。

代码：ResponseDocumentPanel

```java
public ResponseDocumentPanel(@NotNull ChatMessageContext chatMessageContext) {
    
    // ai 返回的结果
    String markDownResponse = chatMessageContext.getAiMessage().text();  
    // 处理 diff
    processGitDiff(chatMessageContext, document);

}


private void processGitDiff(@NotNull ChatMessageContext chatMessageContext, @NotNull Node document) {
    //获取原始文件  
    EditorInfo editorInfo = chatMessageContext.getEditorInfo();

    //diff实现，内部通过DiffManagerEx实现 
    GitMergeService.getInstance().showDiffView(
                    chatMessageContext.getProject(),
                    files.get(0),
                    modifiedContents.isEmpty() ? "" : modifiedContents.get(0));      
}
```





### 7、augment

augment一个AI辅助编程新星， 借鉴了 Anthropic 的架构，但自己搞定了他们没公开的“规划”工具 (用的是 sequential_thinking tool)



一个需求包含3部分： 1. context收集。2. Plan。 3。Action

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250410200521267.png" alt="image-20250410200521267" style="zoom:50%;" />



#### 代码合并

augment的代码合并可能是通过LLM实现的。方法重构它也能完美实现，比如这个需求是 重构 **calculateTextAreaHeight**() 为 **calculateTextAreaSize**():

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/企业微信截图_d27e26ab-6685-456e-ad55-9e9ba7bea248.png" alt="企业微信截图_d27e26ab-6685-456e-ad55-9e9ba7bea248" style="zoom:50%;" />

注意这里只显示了2个方法，但通过arthas监控 com.augmentcode.intellij.chat.SmartPasteManager getReplacement ，可以看到合并的时候会自动补上中间缺失的方法：

```java
LLM返回代码合并后的结果，会自动补上getMaxWidth()

@SmartPasteResult(startLine=60, endLine=123, replacementText=        // 初始设置尺寸
        Dimension textSize = calculateTextAreaSize(textArea);
        textArea.setMaximumSize(new Dimension(getMaxWidth(), Integer.MAX_VALUE));
        textArea.setPreferredSize(textSize);
        panel.add(textArea, BorderLayout.CENTER);

        // 添加组件大小变化监听器
        addComponentListener(new ComponentAdapter() {
            @Override
            public void componentResized(ComponentEvent e) {
                // 窗口大小变化时重新计算尺寸
                Dimension newSize = calculateTextAreaSize(textArea);
                textArea.setPreferredSize(newSize);
                textArea.setMaximumSize(new Dimension(getMaxWidth(), Integer.MAX_VALUE));
                panel.revalidate();
            }
        });

        return panel;
    }

    private int getMaxWidth(){
        return (int) (parent.getWidth()*0.8);
    }

    private Dimension calculateTextAreaSize(JTextArea textArea) {
        FontMetrics fm = textArea.getFontMetrics(textArea.getFont());
        String[] lines = content.split("\n");

        // 计算最大行宽
        int maxLineWidth = 0;
        for (String line : lines) {
            int lineWidth = fm.stringWidth(line);
            maxLineWidth = Math.max(maxLineWidth, lineWidth);
        }

        // 计算目标宽度（考虑最大宽度限制）
        int maxWidth = getMaxWidth();
        int targetWidth = Math.min(maxLineWidth + 20, maxWidth);

        // 计算高度
        int lineHeight = fm.getHeight();
        int totalHeight = 0;

        for (String paragraph : content.split("\n")) {
            if (paragraph.isEmpty()) {
                totalHeight += lineHeight;
                continue;
            }

            // 计算段落在给定宽度下需要的行数
            int paragraphWidth = fm.stringWidth(paragraph);
            int wrappedLines = Math.max(1, (int)Math.ceil((double)paragraphWidth / (targetWidth - 10)));
            totalHeight += wrappedLines * lineHeight;
        }

        // 添加一点额外空间
        totalHeight += 10;

        return new Dimension(targetWidth, totalHeight);
    }
}
```

最后diff效果，从60行开始替换，但是中间的 getMaxWidth() 都会自动补上：

![image-20250506170143214](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250506170143214.png)







通过arthas对 com.augmentcode.intellij.api.AugmentAPIImpl.chat() 监控，查看chatHistory

chatHistory 数据结构是下面这个list

```java
public class Exchange {
    public String requestMessage; //用户提问
    public String responseText;   //LLM响应
    public String requestId;
    public List<ChatRequestNode> request_nodes; //请求nodes
    public List<ChatResultNode> response_nodes; //响应nodes
}
```

ChatResultNode:

```java
public class ChatResultNode {
    public Integer id;
    public Integer type;//类型，1或者5
    public String content;
    public ChatResultToolUse toolUse;//使用tool
}

```



### 8、Cursor 

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250425144428034.png" alt="image-20250425144428034" style="zoom:50%;" />

Context 收集阶段会自动找到ProducerController和对应的HermesMessage代码，而且神奇的是会发现 KafkaProducerWrapper。

修改阶段：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250425144841601.png" alt="image-20250425144841601" style="zoom:50%;" />





##  三、Auto-Dev插件

### 架构

[来源](https://github.com/unit-mesh/auto-dev-vscode/blob/master/docs/development/local-architecture.md)

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20241120174504301.png" alt="image-20241120174504301"  />

相关代码依赖于**静态代码分析**，主要借助于代码的结构信息，如：AST、CFG、DDG 等。在不同的场景和平台之下，我们可以结合不同的静态代码分析工具， 如下是常见的一些静态代码分析工具：

- [TreeSitter](https://tree-sitter.github.io/tree-sitter/)，由 GitHub 开发的用于生成高效的自定义语法分析器的框架。
- [Intellij PSI](https://plugins.jetbrains.com/docs/intellij/psi.html) （Program Structure Interface），由 JetBrains 开发的用于其 IDE 的静态代码分析接口。
- [LSP](https://langserver.org/)（Language Server Protocol），由微软开发的用于 IDE 的通用语言服务器协议。
- [Chapi](https://github.com/phodal/chapi) (common hierarchical abstract parser implementation) ，由笔者（@phodal）开发的用于通用的静态代码分析工具。



记录如何实现自动创建单元测试过程

### Chat聊天

#### 基本流程

ChatCodingService 负责所有的AI提问, 入口方法是 handlePromptAndResponse()

内部流程：

1. 通过 ApplicationManager.getApplication().executeOnPooledThread ()异步执行请求makeChatBotRequest()，避免 
2. 通过 ui.updateMessage(response) 流式更新结果

哪些上层应用会调用 ChatCodingService.handlePromptAndResponse() 

1. 直接通过 ChatCodingPanel 提问。 详见 inputSection.onSubmit()
2. FixThisAction ，右键Action
3. ExplainThisAction , 右键Action
4. CodeReviewAction

ChatCodingPanel  是右侧面板UI，AI回答问题会更新右侧面板输出。

#### 返回的回答

一个 MessageView 对象代表一条LLM返回的结果，通过 renderInPartView()方法显示返回的内容，主要流程：

1. layoutAll()把所有的内容拆分为 MessageBlock List
2. 迭代MessageBlock List，根据成员创建对应的 CodeBlockView 和 TextBlockView，并调用其initialize()，对CodeBlockView，会创建 CodePartEditorInfo 包括内部的 EditorFragment
3. 调用CodeBlockView.getComponent()，也就是EditorFragment内部创建的，并添加到 centerPanel



#### input框文件关联

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250220165410392.png" alt="image-20250220165410392" style="zoom:50%;" />

原理也很简单，只需要订阅文件打开事件，添加到对应的list，代码在 AutoDevInputSection 中的setupEditorListener()方法：

```kotlin
    private fun setupEditorListener() {
        project.messageBus.connect().subscribe(
            FileEditorManagerListener.FILE_EDITOR_MANAGER,
            object : FileEditorManagerListener {
                override fun selectionChanged(event: FileEditorManagerEvent) {
                    val file = event.newFile ?: return
                    ApplicationManager.getApplication().invokeLater {
                        listModel.addIfAbsent(file, true)
                    }
                }
            }
        )
    }
```

订阅 FILE_EDITOR_MANAGER ，收到file之后添加到listModel，文件列表通过 JBList实现，变量名elementsList，添加了点击事件。

#### input框提示

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250220165513849.png" alt="image-20250220165513849" style="zoom:50%;" />

IDEA中通过扩展CompletionContributor实现，参考 DevInCompletionContributor文件，另外还需要订阅 LookupManagerListener.TOPIC 

```kotlin
    private fun setupRelatedListener() {
        project.messageBus.connect()
            .subscribe(LookupManagerListener.TOPIC, AutoDevInputLookupManagerListener(project) {
                ApplicationManager.getApplication().invokeLater {
                    val relatedElements = RelatedClassesProvider.provide(it.language)?.lookup(it)
                    updateElements(relatedElements)
                }
            })
    }
```



### Action-生成 Api Test流程

#### 流程

```
ChatBaseAction.executeAction() ->  ChatCodingService.handlePromptAndResponse() -> JavaContextPrompter.createPrompt()
```

#### 1、GenerateApiTestAction

```java
public final class GenerateApiTestAction extends ChatCheckForUpdateAction {
    
   public String addAdditionPrompt(@NotNull Project project2, @NotNull Editor editor, @NotNull PsiElement element) {
        ChatTemplateContext chatTemplateContext = this.getActionType().getContext();        
        chatTemplateContext.setCode(element.getText());
        chatTemplateContext.setLanguage(element.getLanguage().getDisplayName());
        return "";
    }
}
```

在父类 ChatBaseAction 的 executeAction()方法会执行：


```java
public abstract class ChatBaseAction extends AnAction {

    public void executeAction(AnActionEvent event) {

        //①通过语言获取到对应的sontextPrompter，比如java对应JavaContextPrompter
        ContextPrompter prompter = ContextPrompter.prompter(object);

        //②初始化prompt
        prompter.initContext()
        
        ChatContext chatContext = ChatContext(chatCompletedPostAction(event, panel),prefixText,suffixText)
        //③请求prompt
        chatCodingService.handlePromptAndResponse(panel,prompter,chatContext,keepHistory = true);
    }
}
```

#### 2、创建Prompt

JavaContextPrompter 继承于 ContextPrompter，在 JavaContextPrompter的initContext()会进行相关解析：

```java
public class JavaContextPrompter extends ContextPrompter {
    
    private String additionContext;

    private JavaPsiElementDataBuilder psiElementDataBuilder = new JavaPsiElementDataBuilder();

    public void initContext(ChatActionType actionType, String selectedText,  PsiFile file,Project project, int offset, PsiElement element){
        this.mvcContextService = new MvcContextService(project);
        this.creationContext = new ChatCreationContext(ChatOrigin.ChatAction, chatActionType, file, CollectionsKt.emptyList(), element);
    }

    private TextTemplatePrompt createPrompt(String selectedText) {
        this.additionContext = "";
        ChatActionType chatActionType = this.getAction();
        //① 根据不同的类型创建 TextTemplatePrompt
        TextTemplatePrompt prompt = chatActionType.instruction(this.getLang(), this.getProject());
        

        //② 根据不同的类型补充不同的Context
        if(chatActionType==ChatActionType.CODE_COMPLETE){//代码补全
            if(MvcUtil.isController(fileName, lang)){
                additionContext += mvcContextService.controllerPrompt(file)
            }else if(MvcUtil.isService(fileName, lang)){
                additionContext += mvcContextService.servicePrompt(file)
            }else{
                additionContext = SimilarChunksWithPaths.createQuery(file!!) ?: ""
            }

        }else if(chatActionType==ChatActionType.FIX_ISSUE){ //修复bug
            addFixIssueContext(selectedText);

        }else if(chatActionType==ChatActionType.GENERATE_TEST_DATA){ //单元测试
            prepareDataStructure(creationContext,action); //准备数据
        }

        //③调用TextTemplatePrompt.renderTemplate()解析prompt
        return prompt.renderTemplate();
        
    }

}
```

核心方法是 createPrompt(),主要流程：

1. 根据不同的类型创建 TextTemplatePrompt
2. 根据不同的类型补充不同的Context
3. 调用TextTemplatePrompt.renderTemplate()解析prompt

#### 3、发送prompt

发送prompt的逻辑在ChatCodingService.handlePromptAndResponse()方法中

```java
public final class ChatCodingService {
    
    private  LLMProvider llmProvider;

    public ChatCodingService(){
        this.llmProvider = new LlmFactory().create(this.project);
    }

    public void handlePromptAndResponse(ChatCodingPanel ui, ContextPrompter prompter){
        var requestPrompt = prompter.requestPrompt()
        var displayPrompt = prompter.displayPrompt()

        //如果开启自定义RAG
        if (project.customAgentSetting.enableCustomRag && ui.hasSelectedCustomAgent()) {
            //省略...
        }

        //更新ui
        ui.addMessage(requestPrompt, true, displayPrompt)
        ui.addMessage(AutoDevBundle.message("autodev.loading"))

        //发送请求
        val response =this.makeChatBotRequest(requestPrompt, keepHistory)

    }

    private final Flow<String> makeChatBotRequest(String requestPrompt, boolean newChatContext) {
        return llmProvider.stream(requestPrompt, "", !newChatContext);
    }

}

```

可以发现委托给了 llmProvider.steram()方法，在构造函数中通过LlmFactory会返回不同的实现。

利用 /genius/en/code/test-gen-api.vm 模板

### Action-产生单元测试

#### 流程

```
AutoTestInMenuAction -> TestCodeGenTask
```

#### 入口

入口: AutoTestInMenuAction

```java
public final class AutoTestInMenuAction extends AnAction {
    
    public void actionPerformed(@NotNull AnActionEvent e) {
        //创建任务
        TestCodeGenTask task = new TestCodeGenTask()
        //运行任务
        ProgressManager.getInstance().runProcessWithProgressAsynchronously(task, BackgroundableProcessIndicator(task))
    }
}
```



#### TestCodeGenTask

TestCodeGenTask 直接产生单元测试的任务，是整个流程的核心，主要步骤：

1. 根据项目获取对应的AutoTestService
2. 创建 TestFileContext
3. 收集 ChatContextItem
4. 处理 related classes and current class
5. 使用模板产生 prompt 

```java
public final class TestCodeGenTask{
    
    public void run(ProgressIndicator indicator){
        //①根据项目获取对应的AutoTestService
        AutoTestService autoTestService = AutoTestService.context(this.request.getElement());
        if (autoTestService == null) {
            return;
        }    

        indicator.isIndeterminate = false
        indicator.fraction = 0.1
        //③更新UI文案："准备上下文中"
        indicator.text = AutoDevBundle.message("intentions.chat.code.test.step.prepare-context")
        //通知UI,内部实现是利用idea的 message 机制
        AutoDevStatusService.notifyApplication(AutoDevStatus.InProgress)

        //③创建 TestFileContext
        TestFileContext testContext = autoTestService.findOrCreateTestFile(request.file, request.project, request.element)

        //④ 收集 ChatContextItem
        List<ChatContextItem> contextItems = CompletableFuture.supplyAsync(() -> {
            return ChatContextProvider.collectChatContextList(request.getProject(), creationContext);
        }).get();


        //⑤ 处理 related classes and current class
        ReadAction.compute(() -> {
            if (!testContext.getRelatedClasses().isEmpty()) {
                testPromptContext.setRelatedClasses(testContext.getRelatedClasses().stream()
                        .map(it -> it.format())
                        .flatMap(s -> Arrays.stream(s.split("\n")))
                        .map(line -> comment + " " + line)
                        .collect(Collectors.joining("\n")));
            }

            String currentObject = ReadAction.compute(() -> testContext.getCurrentObject());
            testPromptContext.setCurrentClass(currentObject != null ?
                    Arrays.stream(currentObject.split("\n"))
                            .map(line -> comment + " " + line)
                            .collect(Collectors.joining("\n")) : "");
            return null;
        });


        //⑥ 使用模板产生 prompt 
        templateRender.setContext(testPromptContext);
        String prompter = templateRender.renderTemplate(template); 

    }
}
```

### Team Prompt

#### 初始化

Team prompt  是通过 IntentionsActionGroup 实现的， IntentionsActionGroup是一个ActionGroup可以动态创建Action，代码如下：

```java
public class IntentionsActionGroup extends ActionGroup implements DumbAware {
    public IntentionsActionGroup() {
        super(AutoDevBundle.message("intentions.assistant.name"), true);
    }

    @Override
    public AnAction[] getChildren(AnActionEvent e) {
        Project project = e.getProject();
        if (project == null) return AnAction.EMPTY_ARRAY;

        Editor editor = e.getData(CommonDataKeys.EDITOR);
        if (editor == null) return AnAction.EMPTY_ARRAY;

        PsiFile file = e.getData(CommonDataKeys.PSI_FILE);
        if (file == null) return AnAction.EMPTY_ARRAY;

        List<IntentionAction> intentions = IntentionHelperUtil.getAiAssistantIntentions(project, editor, file);

        return intentions.stream()
                .map(action -> DumbAwareAction.create(action.getText(), (event) -> action.invoke(project, editor, file)))
                .toArray(AnAction[]::new);
    }
}
```

这里主要逻辑是调用 IntentionHelperUtil.getAiAssistantIntentions() 创建对应的IntentionAction，IntentionsAction 可以用 Alt + Enter 触发， 是 IDEA 的最佳实践。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250331161522808.png" alt="image-20250331161522808" style="zoom:50%;" />

IntentionHelperUtil.getAiAssistantIntentions() 方法会尝试加载4种IntentionAction：

- builtinIntentions：内置的Action，图中前三个
- customActionIntentions
- livingDocIntentions
- teamPromptsIntentions： 当我们在项目中新增prompt目录，并添加两个模板文件 Tasking.vm 和 TDD-Red.vm就会显示，图中后两个



teamPrompt 通过TeamPromptsBuilder.default()创建，内部会查看项目中是否存在prompt目录，如果存在会调用 CustomActionPrompt.fromContent()解析为CustomActionPrompt，然后创建 TeamPromptBaseIntention。

#### 模板语法

[team-prompts](https://ide.unitmesh.cc/custom/team-prompts)   支持比较复杂的语法，文档包含三部分：

- frontmatter 配置，即 YAML 格式的提示配置
- 聊天角色，例如 `system` 、 `user` ，将以 ``` 开头，以 ``` 结尾，例如：```system```
- 聊天内容，又称prompt内容

通过 CustomActionPrompt.fromContent() 实现的

例子：

```
---
interaction: AppendCursorStream
---
```user```

你是一个资深的软件开发工程师，你擅长使用 TDD 的方式来开发软件，你现在需要帮助帮手开发人员做好 Tasking，以方便于编写测试用例。

- Tasking 产生的任务都是具有独立业务价值的，每完成一条，都可以独立交付、产生价值。
- 采用面向业务需求的 Tasking 采用业务语言描述任务列表，更有助于开发人员和业务人员对需求进行详细的沟通和确认。
- 采用 Given When Then 的书写格式，其中 When 中所代表系统行为。
- 要考虑业务场景覆盖率，尽可能考虑到边界条件
  
请严格按照以下的格式输出。

示例如下：

Question: 开发一个出租车计费功能，它的计算规则是这样的：不超过8公里时每公里收费0.8元，超过8公里则每公里加收50%长途费，停车等待时每分钟加收0.25元。
Answer: ###
${commentSymbol} Given 出租车行驶了5公里（8公里以内），未发生等待，When 计费，Then 收费4元
${commentSymbol} Given 出租车行驶了5公里（8公里以内），等待10分钟，When 计费，Then 收费6.5元
${commentSymbol} Given 出租车恰好行驶了8公里，未发生等待，When 计费，Then 收费6.4元
${commentSymbol} Given 出租车恰好行驶了8公里，等待10分钟，When 计费，Then 收费8.9元
###
Question: ${selection}
Answer: ###

```

interaction:

- `ChatPanel` 用于直接输出在右侧的聊天窗口；
- `AppendCursorStream` 则是用 Stream （打字机效果）的方式在当前文档输出。



#### 执行

当点击Action会触发TeamPromptBaseIntention.invoke()，主要两步：

1. VariableTemplateCompiler.compiler() 
2. TeamPromptExecTask.run()



VariableTemplateCompiler.compiler() 负责渲染模板得到最终的prompt，TeamPromptExecTask内部会根据InteractionType执行不同的逻辑：

1. 如果是ChatPanel，交给 ChatPanel执行
2. 如果是AppendCursorStream，通过BaseCompletionTask执行
3. 如果是OutputFile，通过FileGenerateTask执行
4. 如果是ReplaceSelection，通过BaseCompletionTask执行

### sketch chat

#### 基本原理

sketch是一个agent，agent和chat最大的区别是chat只有一轮，agent内部会自动下一轮的对话，通过devin指令驱动，流程如下：

```
+------+     +--------+     +------+
| User | --> | Sketch | --> | LLM  |
+------+     +--------+     +------+
                 ^             |
                 |             v
                 +-------------+
                 |    循环     |
                 +-------------+
```

Sketch 会不停地请求LLM，直到LLM返回的的数据没有包含指令。

这里简单介绍两个阶段：

1、User请求

用户发问的入口，入口在 SketchInputListener.onSubmit()

执行过程：

```
SketchInputListener.onSubmit() -> SketchInputListener.manualSend() -> llmProvider.stream() -> toolWindow.onFinish()
```

这里有个细节，SketchInputListener.manualSend()会执行devin指令，SketchInputListener的输入是包含了devin指令的文本，对于devin patch返回内容为空，所以不会调用LLM

2、 LLM response

因为是流式响应，所以LLM返回分为两部分 SketchToolWindow.onUpdate() 和 SketchToolWindow.onFinish()，这两个方法都是在SketchInputListener.manualSend()方法中调用：

1. onUpdate()响应中。 主要解析输出内容，这里包括会解析patch diff部分，注意一个细节，这个过程中并不会执行devin指令，对于devin patch只是生产了一个diffUI控件。
2. onFinish()响应完成。得到完整的输出内容，解析响应内容，当发现内容包含有devin指令会再次调用manualSend()， 代码在AutoSketchMode.start()

#### sketch chat执行

ChatCodingPanel 对应了普通聊天， SketchToolWindow是Sketch聊天，输入都是AutoDevInputSection（两个实例），当submit会调用相应的 Listener：

```
SketchToolWindow -> AutoDevInputSection --submit() -> SketchInputListener.onSubmit()
ChatCodingPanel  -> AutoDevInputSection --submit() -> 匿名的Listener
```
然后会一路调用大模型发送prompt，SketchInputListener还会从模板读取 sketch.vm 作为system prompt。 

chat执行流程

```
SketchInputListener.onSubmit() -> SketchInputListener.manualSend() -> chatCodingService.sketchRequest() -> CustomLLMProvider.stream()-> SketchToolWindow.onUpdate()
```

#### devin指令如何执行

在LLM信息返回完成之后会触发的onFinish()

```kotlin
fun onFinish(text: String) {
    if (AutoSketchMode.getInstance(project).isEnable && !isInterrupted) {
            AutoSketchMode.getInstance(project).start(text, <EMAIL>)
    }
}
```

### sketch diff

sketch 是AutoDev2.0支持的agent能力，这里我重点关注其代码 diff 功能  

#### diff 的执行路径

```
SketchToolWindow.onFinish() ->  blockViews.onDoneStream() -> CodeHighlightSketch.onDoneStream() -> DiffLangSketchProvider.create() 
```

#### 基本原理

基本原理：利用prompt告诉LLM按照devin语法结构修改代码，最开始采用4o-mini发现LLM返回的格式不对：

````properties
<devin>
/patch:src/main/java/com/phodal/shire/demo/service/BlogService.java
```patch
Index: BlogService.java
===================================================================
@@ -1,6 +1,7 @@
package com.phodal.shire.demo.service;
```
</devin>
````

会导致 DiffLangSketch 中的 PatchReader 读取报错。

改为deepseek是正确的：

````properties
<devin>
/patch:src/main/java/com/phodal/shire/demo/controller/BlogController.java
```patch
Index: src/main/java/com/phodal/shire/demo/controller/BlogController.java
--- src/main/java/com/phodal/shire/demo/controller/BlogController.java
+++ src/main/java/com/phodal/shire/demo/controller/BlogController.java
@@ -234,4 +234,15 @@
+
+    /**
+     * Delete Blog by id
+     *
+     * @param id The id of the blog post to delete
+     */
+    @DeleteMapping("/{id}")
+    public void deleteBlog(@PathVariable Long id) {
+        blogService.deleteBlog(id);
+    }
 }
```
 </devin>
````

#### 基本流程

1、入口方法是 SketchToolWindow.onFinish()，也就是等数据全部返回完成
2、CodeHighlightSketch.onDoneStream()首先会调用 CodeFence.parseAll()解析内容，这里会被解析为devin指令中的patch
3、调用DiffLangSketchProvider.create()会初始化DiffLangSketch，利用IDEAPatchReader读取Patcher，同时初始化SingleFileDiffSketch，创建apply、view等按钮

点击view按钮可以对比差异，代码：

```kotlin
fun showSingleDiff(project: Project, patchContent: String, handleAccept: (() -> Unit)?) {
    val editorProvider = FileEditorProvider.EP_FILE_EDITOR_PROVIDER.extensionList.firstOrNull {
        it.javaClass.simpleName == "DiffPatchFileEditorProvider" || it.javaClass.simpleName == "DiffEditorProvider"
    }

    if (editorProvider != null) {
        val virtualFile = LightVirtualFile("AutoDev-Diff-Lang.diff", patchContent)
        val editor = editorProvider.createEditor(project, virtualFile)
        object : DialogWrapper(project) {
            init {
                title = "Diff Preview"
                setOKButtonText("Accept")
                init()
            }

            override fun doOKAction() {
                handleAccept?.invoke()
                super.doOKAction()
            }

            override fun createCenterPanel(): JComponent {
                return editor.component
            }
        }.show()
    } else {
        MyApplyPatchFromClipboardDialog(project, patchContent).show()
        return
    }
}
```

#### patch生效

patch的改写是通过PatchInsCommand实现的，主要是利用 GenericPatchApplier.apply()

### DevInFile

#### 基础知识

首先Devin是需要一个DevInFile，这个是扩展PsiFileBase，比如内容：

```
/dir:src/main/java
/localSearch:Blog
```

这个DevInFile会有两个children，表示两个命令。

Devin定义了命令接口 InsCommand 和实现类：
- DirInsCommand      查看目录
- LocalSearchInsCommand  搜索文件内容
- PatchInsCommand 

#### 解析过程

解析过程 通过DevInsCompiler 解析命令入口是 compile() 方法

```kotlin
class DevInsCompiler(){
  
    fun compile(): DevInsCompiledResult {

        val children = runReadAction { file.children }
        children.forEach {
            when (it.elementType) {
                DevInTypes.TEXT_SEGMENT -> output.append(text)
                DevInTypes.NEWLINE -> output.append("\n")
                DevInTypes.USED -> processUsed(it as DevInUsed)
            }
        }
    }    
  
}
```

指令对应的就是 USED 执行processUsed() 内对会执行对应的Command



### CodeFence解析

比如这样一段内容：

````
现在，我将生成相应的代码变更
<devin> //①
/write:src/main/java/cc/unitmesh/untitled/demo/application/BlogApplicationService.java
、、、java
    @Service
    public class BlogApplicationService {
        public void deleteBlog(Long id) {
            blogRepository.deleteById(id);
        }

    }
、、、
</devin> //②

//③
配置过程:
```python
println("test")
```

```java
println("test")
```
````

执行过程：

1. 定义currentIndex，初始为0
2. 循环匹配<devin>，找到位置startIndex ①
3. startIndex 大于 currentIndex，说明<devin>前面存在文本，调用 parseMarkdownContent()解析
4. 匹配</devin>，找到位置endIndex ②，截取devin内部的内容，更新currentIndex
5. 如果循环结束，判断currentIndex是否达到content末尾，如果没有说明还有内容，调用parseMarkdownContent()解析剩余内容③

代码如下：

```java
public static List<CodeFence> parseAll(String content) {
        List<CodeFence> codeFences = new ArrayList<>();
        int currentIndex = 0;

        Matcher startMatcher = devinStartRegex.matcher(content);
        while (startMatcher.find()) {
            int startIndex = startMatcher.start();
            if (startIndex > currentIndex) {//前面有text
                String beforeText = content.substring(currentIndex, startIndex);
                if (!beforeText.isEmpty()) {
                    parseMarkdownContent(beforeText, codeFences);
                }
            }

            String searchRegion = content.substring(startIndex);
            Matcher endMatch = devinEndRegex.matcher(searchRegion);
            boolean isComplete = endMatch.find();

            String devinContent = isComplete ?
                    searchRegion.substring(startMatcher.group().length(), endMatch.start()).trim() :
                    searchRegion.substring(startMatcher.group().length()).trim();

            codeFences.add(new CodeFence("devin", devinContent, isComplete, "devin"));
            currentIndex = isComplete? startIndex + endMatch.end() : content.length();
        }

        if (currentIndex < content.length()) {
            String remainingContent = content.substring(currentIndex);
            parseMarkdownContent(remainingContent, codeFences);
        }

        return codeFences.stream()
                .filter(it -> "devin".equals(it.originLanguage) || !it.text.isEmpty())
                .toList();
    }
```



## 四、Auto-coder

### 环境搭建

[auto-coder](https://github.com/allwefantasy/auto-coder) 非常易用的，只需要执行 pip install -U auto-coder 安装

进入项目目录运行：

```
auto-coder.chat --lite --debug
```

第一次会提示你设置llm key ，如果要重新配置key只需要删除 /.auto-coder/keys/ 这个目录



执行 /chat context_pruner.py 这个文件的作用是什么，效果非常牛逼：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250304102025001.png" alt="image-20250304102025001" style="zoom:50%;" />

### 源码分析

#### 项目入口 chat_auto_coder.py

项目入口在chat_auto_coder.py 的main方法，可以执行：

```
python3 src/autocoder/chat_auto_coder.py
```

main()的主要逻辑：
- initialize_system() 初始化环境，调用 auto_coder_runner.py 的 init_project() 初始化
- 打印logo
- 输出支持的命令
- 进入一个while 循环，监听用户的输入的命令，调用 auto_coder_runner.py 对应的方法，比如index_query， chat

#### auto_coder_runner.py

auto_coder_runner.py 文件是 AutoCoder 项目的核心运行器，主要负责处理项目的初始化、配置管理、文件管理、代码生成、代码审查、模型管理等功能。

它包含了一系列命令处理器，如/add_files、/remove_files、/coding、/chat、/commit 等，用于执行与代码生成、代码审查、文件管理相关的操作。此外，它还负责与模型进行交互，生成代码、执行代码审查、管理模型配置等。

该文件是 AutoCoder项目的主要入口，负责协调和管理整个项目的运行流程。  

### Ask 实现

#### 调用流程

```properties
auto_coder_runner.py#ask() -> auto_coder.py#main() ->project_reader.py#run()
```

#### 1、auto_coder_runner.py#ask() 

入口，非常简单：

```python
def ask(query: str):
    auto_coder_main(["agent", "project_reader", "--file", execute_file])

```
这里注意几个参数： agent=project_reader，后面会用到

#### 2、auto_coder.py#main()

main方法非常长，主要判断 agent_command 然后执行对应的逻辑，这里对应的是project_reader，非常简单，调用 project_reader.py#run()


```python
def main(input_args: Optional[List[str]] = None):
    if raw_args.agent_command == "project_reader": #L888
      	from autocoder.agent.project_reader import ProjectReader
        project_reader = ProjectReader(args, llm)
        v = project_reader.run(args.query)

```

#### 3、project_reader.py#run()

```python 

    def run(self, query: str, max_iterations: int = 20):
        from byzerllm.apps.llama_index.byzerai import ByzerAI
        agent = ReActAgent.from_tools(
            tools=self.tools,
            llm=ByzerAI(llm=self.llm),
            verbose=True,
            max_iterations=max_iterations,
            context=context.prompt(
                project_map=self.get_tree_like_directory_structure(),
            ),
        )
        r = agent.chat(message=query)
        return r.response

```

ReActAgent 是llama-index提供的工具，通过tools参数可以自定义的函数让llm执行。在项目中 project_reader 定义的函数有：
- ask_user
- run_python_code
- find_files_by_content
- find_files_by_name
- auto_run_job
- get_project_map

context 的prompt 如下：

```properties
  你的目标是帮助用户阅读和理解一个项目。请仔细阅读以下信息，以便更好地完成任务。

    环境信息:

    操作系统: {{ env_info.os_name }} {{ env_info.os_version }}
    Python版本: {{ env_info.python_version }}
    {%- if env_info.conda_env %}
    Conda环境: {{ env_info.conda_env }}
    {%- endif %}
    {%- if env_info.virtualenv %}
    虚拟环境: {{ env_info.virtualenv }}
    {%- endif %}
    {%- if env_info.has_bash %}
    支持Bash
    {%- else %}
    不支持Bash
    {%- endif %}

    {{ project_map }}

    可用工具及使用指南:

    1. get_related_files_by_symbols(query: str) -> str
       - 根据类名、函数名或文件用途描述，返回项目中相关文件的路径列表。
       - 返回结果为逗号分隔的文件路径。

    2. read_files(paths: str) -> str
       - 读取指定文件的内容。
       - 输入为逗号分隔的文件路径列表（支持文件名或绝对路径）。
       - 建议每次最多读取5-6个最相关的文件。

    3. run_python_code(code: str) -> str
       - 运行指定的Python代码。
       - 返回代码的标准输出或错误信息。
       - 使用时需指定项目根目录。

    4. run_shell_code(script: str) -> str
       - 运行指定的Shell代码，用于编译、运行、测试等任务。
       - 返回代码的输出或错误信息。
       - 注意：不允许执行包含rm命令的脚本。

    5. get_project_map() -> str
       - 返回项目中已索引文件的信息，包括文件用途、导入包、定义的类、函数、变量等。
       - 返回JSON格式文本。
       - 仅在其他方法无法获得所需信息时使用。

    6. find_files_by_name(keyword: str) -> str
        - 根据关键字搜索项目中的文件名。
        - 返回文件名包含关键字的文件路径列表，以逗号分隔。

    7. find_files_by_content(keyword: str) -> str
        - 根据关键字搜索项目中的文件内容。
        - 返回内容包含关键字的文件路径列表，以逗号分隔。   

    工作流程建议:

    1. 首先使用get_related_files_by_symbols/find_files_by_name/find_files_by_content获取相关文件路径。
    2. 然后使用read_files读取这些文件的内容(优先阅读markdown类文件)。        
    3. 需要时，可以多次组合使用get_related_files_by_symbols/find_files_by_name/find_files_by_content和read_files以获取更全面的信息。

    ## 特殊指导1
    对于需要计算的问题（如代码行数、文件数量等），优先使用run_python_code。

    ## 特殊指导2
    如需执行Shell命令，使用run_shell_code，但要注意环境兼容性。 

    ## 特殊指导3
    如果用户问该项目的一个功能特性如何使用，优先通过 find_files_by_content 找到包含关键字的文件，然后优先阅读markdown类文件，如果还不行，则
    思考应该先找到相关的类或函数，再通过 read_files 读取文件内容。 

    ## 特殊指导4
    为了梳理一个项目中特定特性的实现，可以遵循以下流程：

    1. 确定特性的入口点：
    - 使用 find_files_by_content 搜索与特性相关的关键词，找到可能的入口点文件。
    - 优先查看 README.md 或其他文档文件，了解特性的概述。

    2. 分析核心实现：
    - 使用 get_related_files_by_symbols 找到与特性相关的核心类或函数。
    - 用 read_files 读取这些文件的内容，分析核心逻辑。

    3. 追踪依赖关系：
    - 分析核心实现中import的模块和调用的其他函数。
    - 再次使用 get_related_files_by_symbols 找到这些依赖的实现。

    4. 分析配置和初始化：
    - 查找与特性相关的配置文件或初始化代码。
    - 使用 find_files_by_name 搜索可能的配置文件。

    5. 检查测试用例：
    - 使用 find_files_by_name 搜索测试文件，通常包含 "test" 或 "spec" 在文件名中。
    - 阅读测试用例，了解特性的预期行为和边界条件。

    6. 查看API接口：
    - 如果特性涉及API，查找API定义文件或路由配置。
    - 使用 find_files_by_content 搜索相关的API端点。

    7. 检查数据流：
    - 分析数据如何在不同组件间传递和处理。
    - 可能需要多次使用 get_related_files_by_symbols 和 read_files 来追踪数据流。

    8. 查看文档和注释：
    - 仔细阅读相关文件中的文档字符串和注释。
    - 特别注意 TODO 或 FIXME 等特殊注释。

    9. 分析版本变化（如果可能）：
    - 如果项目使用版本控制，可以查看相关文件的提交历史。

    10. 总结和验证：
        - 使用 run_python_code 或 run_shell_code 来验证关键部分的行为。
        - 综合所有信息，总结特性的实现流程、主要组件和关键点。

    在这个过程中，根据需要多次使用工具，特别是 get_related_files_by_symbols、read_files 和 find_files_by_content，以确保全面理解特性的实现。如果遇到不清楚的地方，我会提出进一步的问题或建议更深入的分析。   

    请根据用户的具体需求，灵活运用这些工具来分析和理解项目。提供简洁、准确的回答，并在需要时主动提供深入解释的选项。
```

执行：

```
 /ask context_pruner.py 作用是什么
```

它的思考过程如下：

```properties
Thought: The current language of the user is: Chinese. I need to find out what `context_pruner.py` does by locating and reading the file.
Action: find_files_by_name
Action Input: {'keyword': 'context_pruner.py'}
Observation: /System/Volumes/Data/work/dist/branch/opensource/auto-coder/notebooks/test_context_pruner.py,/System/Volumes/Data/work/dist/branch/opensource/auto-coder/src/autocoder/common/context_pruner.py
Thought: I have found two files related to `context_pruner.py`. I will read the content of the main implementation file to understand its purpose.
Action: read_files
Action Input: {'paths': '/System/Volumes/Data/work/dist/branch/opensource/auto-coder/src/autocoder/common/context_pruner.py'}
Thought: I have read the content of `context_pruner.py` and can now summarize its purpose.
Answer: `context_pruner.py` 的主要作用是处理超出 token 限制的文件内容。它通过以下几种策略来减少文件内容的 token 数量：

```

基本上就是先使用 find_files_by_name 找到对应的文件，然后使用read_files读取源代码，最后总结

总结 ask 的执行流程还是比较简单的，主要就是利用大模型的 function call能力，client是ReActAgent，提供了一些本地函数让llm能调用。



### Chat 实现

同样的问题发现/chat命令会比/ask丰富得多，我们来研究下内部发生的魔法

#### 调用流程

```
auto_coder_runner.py#chat() -> auto_coder.py#main() ->project_reader.py#run()
```



#### 1、auto_coder_runner.py#chat()


```python

    def execute_ask():
        cmd = ["agent", "chat", "--file", execute_file]
        if is_new:
            cmd.append("--new_session")
        auto_coder_main(cmd)

    execute_ask()
```

主要构造 yml配置文件，然后调用auto_coder.py#main()，参数agent=chat



#### 2、auto_coder.py#main() 

下面是 raw_args.agent_command == "chat" 执行的代码片段

```python
    #①加载历史聊天
    memory_file = os.path.join(memory_dir, "chat_history.json")

    #② 如果参数中带有context，加入
    if args.context:  
        context = json.loads(args.context)
        pre_conversations.append(
                    {
                        "role": "user",
                        "content": f"请阅读下面的代码和文档：\n\n <files>\n{context_content}\n</files>",
                    },
                )

    if args.project_type == "ts":
        pp = TSProject(args=args, llm=llm)
    elif args.project_type == "py":
        pp = PyProject(args=args, llm=llm)
    else:
        pp = SuffixProject(args=args, llm=llm, file_filter=None)
    pp.run()
    #③获取项目源码
    sources = pp.sources   
    model_filter = ModelPathFilter.from_model_object(chat_llm, args) #过滤不相关的文件？

    #④如果没有context，需要从索引中获取                
    if "no_context" not in args.action:

        #返回query相关的源码文件
        s = build_index_and_filter_files(
            llm=llm, args=args, sources=filtered_sources).to_str()                        
        
        #添加到提示词
        if s:
            pre_conversations.append(
                {
                    "role": "user",
                    "content": f"请阅读下面的代码和文档：\n\n <files>\n{s}\n</files>",
                }
            )
            pre_conversations.append(
                {"role": "assistant", "content": "read"})
            source_count += 1                 
        
   #⑤ 合并提示词
   loaded_conversations = pre_conversations +  chat_history["ask_conversation"] 

    #⑥ 发送chat请求，获取stream #L1353
    v = stream_chat_with_continue(
        llm=chat_llm,
        conversations=loaded_conversations,
        llm_config={}
    )

     

```

这里总结下核心流程，和代码中的序号没有对应，不影响理解：

1. 检测参数是否包含context，如果没有context则会调用 build_index_and_filter_files()构建context
2. build_index_and_filter_files()构建context的逻辑是把所有源码的摘要、import、method、发给LLM，问它和问题相关的文件以及其依赖
3. 然后把 context 和 原始问题合并为一个prompt发给LLM

pompt如下：

```
[
{'role':'user','content': '请阅读下面的代码和文档：\n\n <files>\n##File: ....'},
{'role': 'assistant', 'content': 'read', },
{'role': 'user', 'content': '/chat context_pruner.py 作用是什么'}
]
```

第一行就是content 就是通过 build_index_and_filter_files() 构建的，第二行是一个prompt优化手段先 **让 AI 读取文档/代码**，再 **逐步提问**，防止信息过载

接下来看下 build_index_and_filter_files()的具体实现

#### 3、entry.py# build_index_and_filter_files()

主要步骤如下：

1、quick_filter.py#filter

首先利用 quick_filter_files()，使用了下面的提示词模板，让llm选出和query相关的文件列表，如果发现token数量超过了52k会截断成chunk处理

```markdown
当用户提一个需求的时候，我们要找到两种类型的源码文件：
1. 根据需求需要被修改的文件，我们叫 edited_files
2. 为了能够完成修改这些文件，还需要的一些额外参考文件, 我们叫 reference_files
3. 因为修改了 edited_files 文件，可能有一些依赖 edited_files 的文件也需要被修改，我们叫 dependent_files

现在，给定下面的索引文件：

<index>
{{ content }}
</index>

索引文件包含文件序号(##[]括起来的部分)，文件路径，文件符号信息等。            

下面是用户的查询需求：

<query>
{{ query }}
</query>

请根据用户的需求，找到相关的文件，并给出文件序号列表。请返回如下json格式：

```json
{
    "file_list": [
        file_index1,
        file_index2,
        ...
    ]
}
```

特别注意:    
1. 如果用户的query里有 @文件 或者 @@符号，并且他们在索引文件中，那么被@的文件或者@@的符号必须要返回。
2. 根据需求以及根据 @文件 或者 @@符号 找到的文件，猜测需要被修改的edited_files文件，然后尝试通过索引文件诸如导入语句等信息找到这些文件依赖的其他文件得到 reference_files,dependent_files。
3. file_list 里的文件序号，按被 @ 或者 @@ 文件，edited_files文件，reference_files,dependent_files文件的顺序排列。注意，reference_files 你要根据需求来猜测是否需要，过滤掉不相关的，避免返回文件数过多。
4. 如果 query 里是一段历史对话，那么对话里的内容提及的文件路径必须要返回。        
5. 如果用户需求为空，则直接返回空列表即可。        
6. 返回的 json格式数据不允许有注释

比如用户的查询是：“/chat context_pruner.py 作用是什么”。llm会成功找到4个文件列表：

```

- /System/Volumes/Data/work/dist/branch/opensource/auto-coder/src/autocoder/common/context_pruner.py                                                     
- /System/Volumes/Data/work/dist/branch/opensource/auto-coder/src/autocoder/rag/token_counter.py                                                           
- /System/Volumes/Data/work/dist/branch/opensource/auto-coder/notebooks/test_context_pruner.py                                                             
- /System/Volumes/Data/work/dist/branch/opensource/auto-coder/notebooks/test_context_prune_v2.py   


```

2、context_pruner.handle_overflow()
调用 context_pruner.handle_overflow 执行裁剪流程，后续分析

3、返回结果数据



### Coding 实现

auto-coder.chat的 /coding 实现参考了aider，我这里用 shire-demo 项目为例，分析执行过程下列任务的过程：

```shell
/coding 为Blog新增删除接口
```



#### 调用流程

```
auto_coder_runner.py#coding -> auto_coder.py#main -> action.py# ActionSuffixProject#run() -> CodeAutoGenerateEditBlock.single_round_run() -> code_auto_merge_editblock.py#_merge_code()
```



#### 1、auto_coder_runner.py#coding()

```python
def coding(query: str):

    #① prepare_chat_yaml, raw_args.command == "next"
    auto_coder_main(["next", "chat_action"])
    
    #② 
    cmd = ["--file", execute_file]
    auto_coder_main(cmd)
```

代码很简单，主要创建命令然后调用 auto_coder.py#main() ，参数：--file actions/000000000103_chat_action.yml



#### 2、auto_coder.py#main()

```python
dispacher = Dispacher(args, llm)
dispacher.dispach()
```



#### 3、action.py#ActionSuffixProject#run()

 首先利用entry.py#build_index_and_filter_files() 构建需求相关的文件列表，这个逻辑和/chat 一样

```python
    def run(self):
        pp = SuffixProject(args=args, llm=self.llm)
        pp.run()
        source_code_list = SourceCodeList(pp.sources)
        #获取context
        source_code_list = build_index_and_filter_files(llm=self.llm, args=args, sources=pp.sources)
        self.process_content(source_code_list)
```

然后调用 process_content()，代码如下：

```python

    def process_content():

        #对应了三种不同的prompt
        if args.auto_merge == "diff":
            generate = CodeAutoGenerateDiff(
                llm=self.llm, args=self.args, action=self
            )
        elif args.auto_merge == "strict_diff":
            generate = CodeAutoGenerateStrictDiff(
                llm=self.llm, args=self.args, action=self
            )
        elif args.auto_merge == "editblock": #默认是editblock
            generate = CodeAutoGenerateEditBlock(
                llm=self.llm, args=self.args, action=self
            )
       	else: #对应 wholefile
            	generate = CodeAutoGenerate(llm=self.llm, args=self.args, action=self)
        if self.args.enable_multi_round_generate:
            generate_result = generate.multi_round_run(
                query=args.query, source_code_list=source_code_list
            )
        else: #默认是单轮
            generate_result = generate.single_round_run( 
                query=args.query, source_code_list=source_code_list
            )

```

#### 4、三种代码修改模式

默认使用的是 editblock，我这里使用LLM总结了下editblock、diff、strict_diff三者的区别：

1、editblock:

- 使用 `<<<<<<< SEARCH`/`=======`/`>>>>>>> REPLACE` 标记代码块
- 完全匹配 SEARCH 部分后替换
- 支持相似度阈值（默认 0.7）模糊匹配

示例输出：REPLACE 完全替换SEARCH部分，====作为分隔符

```properties
##File: src/utils.py
<<<<<<< SEARCH
def deprecated_func():
    print("废弃方法")
=======
def new_feature():
    print("新功能实现")
>>>>>>> REPLACE
```

该模式优点:
1. 可以减少token消耗
2. 生成格式复杂度也不高，较多大模型能够支持

缺点:
1. 因为是根据 SEARCH 不分进行替换，有概率一个文件中有多个 SEARCH，导致合并错误
2. 串行执行，当一个文件有多个 SEARCH 时，会串行自行，期间也可能导致 1 的问题。

2、diff 模式

这是一种宽松的diff模式，AutoCoder 会要求大模型直接生成 diff 格式，但是该 diff 格式不要求大模型生成行号，只需要生成 @@ *** @@ 这种格式即可。
然后通过字符匹配的方式找到修改的点，进行合并。这种模式对模型要求较低，并且大部分情况下都能很好的工作，但是对于一些特殊情况，可能会出现合并错误。

示例输出：

```properties
--- src/utils.py
+++ src/utils.py
@@ ... @@
-def old_func():
+def new_func(param):
     print("原有代码")
```

实现逻辑

- 使用 `difflib` 进行上下文匹配
- 优先尝试精确匹配，失败时采用模糊搜索
- 适合需要灵活调整代码位置的场景

缺点和 editblock 类似。

3、strict_diff

- 生成 带精确行号的 diff（强制包含 @@ 行号标记）
- 严格依赖行号定位修改位置
- 使用 patch 库直接应用变更
- 使用大规模重构、多文件协同修改等需要精确控制的场景

示例输出：

```properties
--- src/utils.py
+++ src/utils.py
@@ -12,7 +12,7 @@
 def old_func():
-    print("旧实现")
+    print("新实现")
```

实现逻辑

- 依赖 `patch` 库的严格行号匹配
- 必须包含 `@@` 行号标记
- 变更失败时会抛出明确的行号不匹配错误

**模式对比表**

| 维度           | diff 模式  | strict_diff 模式 | editblock 模式     |
| :------------- | :--------- | :--------------- | :----------------- |
| **行号要求**   | 可选       | 必须             | 不需要             |
| **变更粒度**   | 文件级     | 行级精确         | 代码块级           |
| **模糊匹配**   | 支持       | 不支持           | 支持（可配置阈值） |
| **多文件变更** | 容易       | 容易             | 需要逐文件指定     |
| **适用场景**   | 常规修改   | 精确重构         | 局部替换/模式修改  |
| **错误反馈**   | 上下文提示 | 行号级报错       | 相似度报告         |

使用经验(参考)

- 目前推荐使用 wholefile 和 editblock 两种模式。editblock 模式兼具 diff 优势又相对来说易于阅读。
  需要程序员有个预判，如果会有较多文件修改，但每个文件修改不大，那么使用 editblock 模式。如果修改较大，那么使用 wholefile 模式。
- 因为不同的模型支持能力不一致，如果你发现 editblock 经过多次修改描述后依然合并错误，再使用 auto-coder revert 进行回滚，
  可以再次尝试 diff 模式。

可通过 auto_merge 参数指定模式：

```
/conf auto_merge:wholefile
```

对应 aider的实现:https://github.com/Aider-AI/aider/blob/main/aider/coders/editblock_prompts.py#L149

本来想让LLM帮我整理aider的实现，但发现ChatGP幻觉太多，很多乱说的。

除了以上三种还有 wholefile，也就是让LLM生成修改后的完整代码，合并的时候会把生成的代码直接替换到源码中。缺点当修改很小时也需要消费大量Token,并且大部分模型都很难做到几乎原模原样输出修改后的完整代码。



#### 5、CodeAutoGenerateEditBlock

默认使用的 editblock 对应的是 CodeAutoGenerateEditBlock，代码如下：

```python
    def single_round_run(): 
        #转为源码
        source_content = source_code_list.to_str()

        #template 默认是common
        if self.args.template == "common":
            init_prompt = self.single_round_instruction.prompt(
                instruction=query, content=source_content, context=self.args.context
            )
        elif self.args.template == "auto_implement":
            init_prompt = self.auto_implement_function.prompt(
                instruction=query, content=source_content
            ) 
        
        #加入conversations    
        conversations = []
        if self.args.system_prompt and self.args.system_prompt.strip() == "claude":
            conversations.append(
                {"role": "system", "content": sys_prompt.claude_sys_prompt.prompt()})
        elif self.args.system_prompt:
            conversations.append(
                {"role": "system", "content": self.args.system_prompt})

        conversations.append({"role": "user", "content": init_prompt})
        
        #注意发送给llm是conversations
        futures.append(executor.submit(
                            chat_with_continue, llm=llm, conversations=conversations, llm_config=llm_config))
        
```

发送给LLM 的prompt格式:

```
{"role": "user", "content": prompt}
```

single_round_instruction 对应的prompt如下：

```properties
如果你需要生成代码，对于每个需要更改的文件,你需要按 *SEARCH/REPLACE block* 的格式进行生成。

        # *SEARCH/REPLACE block* Rules:

        Every *SEARCH/REPLACE block* must use this format:
        1. The opening fence and code language, eg: {{ fence_0 }}python
        2. The file path alone on a line, starting with "##File:" and verbatim. No bold asterisks, no quotes around it, no escaping of characters, etc.
        3. The start of search block: <<<<<<< SEARCH
        4. A contiguous chunk of lines to search for in the existing source code
        5. The dividing line: =======
        6. The lines to replace into the source code
        7. The end of the replace block: >>>>>>> REPLACE
        8. The closing fence: {{ fence_1 }}

        Every *SEARCH* section must *EXACTLY MATCH* the existing source code, character for character, including all comments, docstrings, etc.

        *SEARCH/REPLACE* blocks will replace *all* matching occurrences.
        Include enough lines to make the SEARCH blocks unique.

        Include *ALL* the code being searched and replaced!

        To move code within a file, use 2 *SEARCH/REPLACE* blocks: 1 to delete it from its current location, 1 to insert it in the new location.

        If you want to put code in a new file, use a *SEARCH/REPLACE block* with:
        - A new file path, including dir name if needed
        - An empty `SEARCH` section
        - The new file's contents in the `REPLACE` section

        ONLY EVER RETURN CODE IN A *SEARCH/REPLACE BLOCK*!

        下面我们来看一个例子：

        当前项目目录结构：
        1. 项目根目录： /tmp/projects/mathweb
        2. 项目子目录/文件列表(类似tree 命令输出)
        flask/
            app.py
            templates/
                index.html
            static/
                style.css

        用户需求： Change get_factorial() to use math.factorial

        回答： To make this change we need to modify `/tmp/projects/mathweb/flask/app.py` to:

        1. Import the math package.
        2. Remove the existing factorial() function.
        3. Update get_factorial() to call math.factorial instead.

        Here are the *SEARCH/REPLACE* blocks:

        {{ fence_0 }}python
        ##File: /tmp/projects/mathweb/flask/app.py
        <<<<<<< SEARCH
        from flask import Flask
        =======
        import math
        from flask import Flask
        >>>>>>> REPLACE
        {{ fence_1 }}

        {{ fence_0 }}python
        ##File: /tmp/projects/mathweb/flask/app.py
        <<<<<<< SEARCH
        def factorial(n):
            "compute factorial"

            if n == 0:
                return 1
            else:
                return n * factorial(n-1)

        =======
        >>>>>>> REPLACE
        {{ fence_1 }}

        {{ fence_0 }}python
        ##File: /tmp/projects/mathweb/flask/app.py
        <<<<<<< SEARCH
            return str(factorial(n))
        =======
            return str(math.factorial(n))
        >>>>>>> REPLACE
        {{ fence_1 }}

        用户需求： Refactor hello() into its own file.

        回答：To make this change we need to modify `main.py` and make a new file `hello.py`:

        1. Make a new hello.py file with hello() in it.
        2. Remove hello() from main.py and replace it with an import.

        Here are the *SEARCH/REPLACE* blocks:

        {{ fence_0 }}python
        ##File: /tmp/projects/mathweb/hello.py
        <<<<<<< SEARCH
        =======
        def hello():
            "print a greeting"

            print("hello")
        >>>>>>> REPLACE
        {{ fence_1 }}

        {{ fence_0 }}python
        ##File: /tmp/projects/mathweb/main.py
        <<<<<<< SEARCH
        def hello():
            "print a greeting"

            print("hello")
        =======
        from hello import hello
        >>>>>>> REPLACE
        {{ fence_1 }}

        现在让我们开始一个新的任务:

        {%- if structure %}
        {{ structure }}
        {%- endif %}

        {%- if content %}
        下面是一些文件路径以及每个文件对应的源码：
        <files>
        {{ content }}
        </files>
        {%- endif %}

        {%- if context %}
        <extra_context>
        {{ context }}
        </extra_context>
        {%- endif %}     

        下面是用户的需求：

        {{ instruction }}
```

这段 prompt 来源aider的 [editblock_prompts.py](https://github.com/Aider-AI/aider/blob/main/aider/coders/editblock_prompts.py#L149)，具体内容参考[Aider SEARCH/REPLACE 实践](https://juejin.cn/post/7482695183476146195) ，主要目的让LLM只返回需要修改的代码，类似：

```java
##File: /System/Volumes/Data/work/dist/branch/opensource/shire-demo/src/main/java/com/phodal/shire/demo/service/BlogService.java
<<<<<<< SEARCH
    public BlogPost updatePost(Long id, BlogPost blogDto) {
        return blogRepository.findById(id).map(blog -> {
            blog.setTitle(blogDto.getTitle());
            blog.setContent(blogDto.getContent());
            return blogRepository.save(blog);
        }).orElse(null);
    }
}
=======
    public BlogPost updatePost(Long id, BlogPost blogDto) {
        return blogRepository.findById(id).map(blog -> {
            blog.setTitle(blogDto.getTitle());
            blog.setContent(blogDto.getContent());
            return blogRepository.save(blog);
        }).orElse(null);
    }

    /**
     * Delete a blog post by id
     *
     * @param id The id of the blog post to delete
     */
    public void deleteBlog(Long id) {
        blogRepository.deleteById(id);
    }
}
>>>>>>> REPLACE
```

接下来处理这些文本了。

需要回到 action.py# ActionSuffixProject#process_content()

```python
def process_content():
    if args.auto_merge == "diff":
        code_merge = CodeAutoMergeDiff(llm=self.llm, args=self.args)
        merge_result = code_merge.merge_code(generate_result=generate_result)
    elif args.auto_merge == "strict_diff":
        code_merge = CodeAutoMergeStrictDiff(llm=self.llm, args=self.args)
        merge_result = code_merge.merge_code(generate_result=generate_result)
    elif args.auto_merge == "editblock": #默认使用editblock合并
        code_merge = CodeAutoMergeEditBlock(llm=self.llm, args=self.args)
        merge_result = code_merge.merge_code(generate_result=generate_result)
    else:
        code_merge = CodeAutoMerge(llm=self.llm, args=self.args)
        merge_result = code_merge.merge_code(generate_result=generate_result)

```
可以发现是通过 code_merge.merge_code 实现代码合并

#### 6、code_auto_merge_editblock.py#_merge_code()

主要思想是是把文本解析为(path,search,replace)的list，然后依次替换，如果失败需要采用相似性block替换

```python
def _merge_code(self, content: str, force_skip_git: bool = False):
    
    #① 把llm返回的文本内容解析为(path,search,replace)的list
    codes = self.get_edits(content)

    #② 迭代codes
    for block in codes:

        file_path, head, update = block

        #如果文件路径不存在，说明是新增的文件
        if not os.path.exists(file_path):
            file_content_mapping[file_path] = update
            merged_blocks.append((file_path, "", update, 1))
        else:
            #读取文件内容
            if file_path not in file_content_mapping:
                    file_content_mapping[file_path] = FileUtils.read_file(file_path)

            #路径得到对应内容
            existing_content = file_content_mapping[file_path]
            
            #如果head存在则替换为update，否则将update添加到末尾
            new_content = (
                existing_content.replace(head, update, 1)
                if head
                else existing_content + "\n" + update
            )

            #如果发生了变更
            if new_content != existing_content:
                changes_to_make.append(
                        (file_path, existing_content, new_content))
                    file_content_mapping[file_path] = new_content #更新新的内容
                    merged_blocks.append((file_path, head, update, 1)) #记录到 merged_blocks
                    changes_made = True
            else:
                # 如果没有找到尝试使用similarity找到block

    #③ 如果存在未merge的block，记录错误！
    if unmerged_blocks:
        return

    #④ lint check 代码静态检查
    if file_path.endswith(".py"):
        self.run_pylint(new_content)

    #⑤ 更新文件
    for file_path, new_content in file_content_mapping.items():
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    with open(file_path, "w") as f:
        f.write(new_content)

    #⑥ commit 变更
    commit_result = git_utils.commit_changes(
                        self.args.source_dir, f"auto_coder_{file_name}_{md5}\n{self.args.query}"
                    )
```

替换完成之后还会调用pylint进行语法检查，然后才会更新源码，最终提交git



## 五、Phodal 

Phodal人称挖坑不填，确实挖了太多坑。

### Shire

[Shire](https://github.com/phodal/shire)是一种可执行的promot，两个视频教程的比较清楚：

- [Shire - AI Coding Agent Language](https://www.youtube.com/watch?v=z1ijWOL1rFY)
- [最强自定义 AI 多文件编辑 ](https://www.bilibili.com/video/BV1AZC6YBEqZ/)
- [shire-lang](https://shire.phodal.com/shire/shire-lang.html)

刚开始不太理解Shire所谓的可执行，个人理解更像是强大的IDE扩展能力，比如:

```properties
---
name:"解释代码++"
description:"Here is a description of the action." 
interaction: RunPanel
actionLocation: ContextMenu
---
解释如下的代码：
$selection
```

它会在右键增加一个【解释代码++】Action。

对于下面的代码它会自定义 ChatBox的能力：

```shell
---
name: "shire multiple file edit"
description: "Shire Multiple File Edit"
onStreaming: { logging }
interaction: RightPanel
actionLocation: ChatBox
---

根据用户的要求和现有的代码编写 Java 代码。要求：

1. 使用 diff patch 的方式。
2. 如果是新文件也使用 patch 的格式。
3. 每个文件的修改也请用 diff 的形式给出。

用户的需求如下：

$chatPrompt
```

但是对于下面这种shire代码，我还不清楚这里的$input 和 $code 代表什么?

```shell
---
name: "Swagger Doc to Yaml"
variables:
  "code": /any/ { cat($input) }
onStreamingEnd: { parseCode | saveFile($input, $output) }
---

为如下的代码，生成 Swagger Doc。不修改任务代码，只添加对应的注解。

文件路径：$input
代码：$code

只返回最后的代码，方便我使用。
```



经过一番探索我算是搞明白了，首先$code 是通过 variables 自定义的上下文，这里是来源$input 是内置变量，表示输入，原来这个文件需要被其他文件嵌入执行：

```shell
---
name: "Generate Swagger Doc"
variables:
  "controllers": /.*.Controller.java/ { print }
  "gen-swagger": /any/ { batch("controller-with-swagger.shire", $controllers) }
---

hi
```

所以 controller-with-swagger.shire 的 $input 其实是这里这里传入的变量$controllers， controller-with-swagger.shire 是无法被单独运行的!

#### 聊天

入口是：ShireChatBoxInput，编译为shire语言再调用OpenAILikeProvider。

Shire 演示的内容也很简单，需要手动指定上下文才能实现对应的效果，具体的prompt如下:

````
根据用户的要求和现有的代码编写 Java 代码。要求：

1. 使用 diff patch 的方式。
2. 如果是新文件也使用 patch 的格式。
3. 每个文件的修改也请用 diff 的形式给出。

用户的需求如下：

帮我实现删除博客的API
```Java
// file path src/main/java/com/phodal/shire/demo/controller/BlogController.java
package com.phodal.shire.demo.controller;
...后面是BlogController剩余的java代码
```

````

#### Diff Patch

我这里重点关注Diff，我发现和AutoDev中的Diff实现有细微差别

流程：

```
ShirePanelView.onUpdate() ->LanguageSketchProvider.provide("diff").create() -> DiffLangSketch() -> SingleFileDiffView()
```

**1、 ShirePanelView.onUpdate()**

```kotlin
fun onUpdate(text: String) {
    //① 将LLM返回的字符串解析为代码块
    codeFenceList = CodeFence.parseAll(text)

    for(codeFence: codeFenceList){

        //② 创建对应的langSketch
        langSketch = LanguageSketchProvider.provide(codeFence.originLanguage)
                            ?.create(project, codeFence.text)

        //③ 加入到对应的list
        blockList.add(langSketch.getComponent(), index)                            
    }
}
```
每个 langSketch都会有一个mainPanel，加入到blockList。

**2、DiffLangSketch()**

LanguageSketchProvider.provide("diff").create()就一行代码初始化DiffLangSketch，所以我们直接看DiffLangSketch构造函数，主要流程如下:

 1. createHeaderAction()：创建acceptButton、rejectButton、viewDiffButton, viewDiffButton基于
 2. 通过 PatchReader 读取 TextFilePatch List(因为一个内容可能多个地方有改动，所以是list)
 3. 通过 myProject.findFile(patch.beforeFileName!!) 获取原来 VirtualFile
 4. 调用 SingleFileDiffView(originFile, patch)

**3、SingleFileDiffView() 主要流程:**

1. 创建JBLabel，包含文件名和icon
2. 为文件添加点击事件，点击会调用showDiff()，这里的showDiff()采用DiffEditorTabFilesManager()实现

### Auto-Dev-VSCode

#### 项目初始化

这里记录初始化的地方

1. **AutoDevExtension** 初始化了项目所有底层组件，比如 retrieval 是 DefaultRetrieval，并且默认是 LanceDbIndex。
2. **CommandsService** 的register()方法**向vscode注册所有支持的功能**，可以理解为web项目的**endpoints定义**，比如

```typescript
            // General Commands
            commands.registerCommand(CMD_OPEN_SETTINGS, this.openSettins, this),
            commands.registerCommand(CMD_SHOW_TUTORIAL, this.showTutorial, this),
            commands.registerCommand(CMD_FEEDBACK, this.feedback, this),
            commands.registerCommand(CMD_SHOW_SYSTEM_ACTION, this.showSystemAction, this),
            // Chat Commands
            commands.registerCommand(CMD_SHOW_CHAT_PANEL, this.showChatPanel, this),
            commands.registerCommand(CMD_QUICK_CHAT, this.quickChat, this),
            commands.registerCommand(CMD_NEW_CHAT_SESSION, this.newChatSession, this),
            commands.registerCommand(CMD_SHOW_CHAT_HISTORY, this.showChatHistory, this),
            // ContextMenu Commands
            commands.registerCommand(CMD_EXPLAIN_CODE, this.explainCode, this),
            commands.registerCommand(CMD_OPTIMIZE_CODE, this.optimizeCode, this),
            commands.registerCommand(CMD_FIX_THIS, this.fixThis, this),
            commands.registerCommand(CMD_QUICK_FIX, this.quickFix, this),
            commands.registerCommand(CMD_GEN_DOCSTRING, this.generateDocstring, this),
            commands.registerCommand(CMD_GEN_CODE_METHOD_COMPLETIONS, this.generateMethod, this),
            commands.registerCommand(CMD_CREATE_UNIT_TEST, this.generateUnitTest, this),
            // Codebase Commands
            commands.registerCommand(CMD_CODEBASE_INDEXING, this.startCodebaseIndexing, this),
            commands.registerCommand(CMD_CODEBASE_RETRIEVAL, this.showCodebasePanel, this),
            // Chat Slash Commands
            commands.registerCommand(CMD_CODEASPACE_ANALYSIS, this.codespaceCodeAnalysis, this),
            commands.registerCommand(CMD_CODEASPACE_KEYWORDS_ANALYSIS, this.codespaceKeywordsAnalysis, this),
            //Terminal Commands
            commands.registerCommand(
                CMD_TERMINAL_EXPLAIN_SELECTION_CONTEXT_MENU,
                this.explainTerminalSelectionContextMenu,
                this,
            ),
            commands.registerCommand(CMD_TERMINAL_SEND_TO, this.terminalSendTo, this),
            commands.registerCommand(CMD_TERMINAL_DEBUG, this.terminalDebug, this),
            // Other Commands
            commands.registerCommand(CMD_GIT_MESSAGE_COMMIT_GENERATE, this.generateCommitMessage, this),
```

#### Natural Language search

Natural Language search 这个是auto-dev-vscode特有的。**入口在Catalyser.ts**，定义了两种语义搜索方式：

- SemanticSearchKeyword 类
- SemanticSearchCode 类

这里使用了一种叫HyDE(Hypothetical Document Search)的技术。 HyDE先让大模型根据用户问题生成一段假设文档，然后通过文档再去从知识库检索，这样得到的信息会更丰富。

比如用户问题是地球上的植物如何通过光合作用储存能量。传统RAG，用户问题被直接用作查询，向知识库发送检索请求，返回的文档可能包括：文档 A：提到光合作用的基本原理，但未提及储能细节。文档 B：偏重植物对环境的影响，没有直接回答问题 。生成结果：“光合作用是植物通过叶绿体吸收光能的过程，它有助于植物生长。”（问题中的“储存能量”未得到充分解答，回答较泛化。）HyDE流程：1. 先让大模型根据用户问题生成一段“假设”文档，2.假设文档被转化为向量，用于语义检索，获取更相关的知识库文档。3.模型结合检索到的文档生成答案。

| **维度**       | **传统 RAG**                                                 | **HyDE**                                                     |
| -------------- | ------------------------------------------------------------ | ------------------------------------------------------------ |
| **检索相关性** | 用户问题直接用于检索，可能返回与问题无关或部分相关的文档。   | 假设文档捕捉了问题的核心（“储存能量”），因此检索返回了与用户意图高度相关的文档。 |
| **生成质量**   | 模型只能基于检索到的部分相关文档生成答案，导致回答泛化或不完整。 | 模型结合假设文档和高相关性文档，回答具体、详尽，并直接聚焦于问题中的关键点。 |
| **幻觉现象**   | 可能生成与文档信息不一致或凭空的答案（如未提到“储能”细节）。 | 高相关的文档减少了幻觉现象，答案更有依据和准确性。           |
| **用户满意度** | 回答片面或模糊，可能需要用户进一步提问。                     | 回答聚焦问题核心，提供清晰、有依据的完整解释。               |

流程

```
Catalyser.query() ->HydeCodeStrategy.execute()
```

在 HydeCodeStrategy.execute()内部主要流程：

1. generateDocument(): 调用大模型的chat方法，生成一段假设代码，并解析出code
2. retrieveChunks(): 从DefaultRetrieval查询一批chunk，一个chunk包括content、startLine、endLine、filepath等，内部会经历**文本检索**和**向量检索**。
3. promptManager.renderHydeTemplate(): 通过模板渲染最终的prompt

#### Completion

代码补全是通过 **AutoDevCodeInlineCompletionProvider** 这个类实现，最开始发现这个类的方法没有被任何地方调用以为没有被实际用到，其实它是SPI扩展vscode.InlineCompletionItemProvider。

AutoDevCodeInlineCompletionProvider 主要通过实现 provideInlineCompletionItems 方法来提供 VSCode 的内联代码补全功能。以下是关键实现：

1. **核心方法 `provideInlineCompletionItems`**:

```typescript
async provideInlineCompletionItems(
    document: vscode.TextDocument,
    position: vscode.Position,
    context: vscode.InlineCompletionContext,
    token: vscode.CancellationToken,
): Promise<vscode.InlineCompletionItem[]> {
    // 只响应自动触发的补全
    if (context.triggerKind !== vscode.InlineCompletionTriggerKind.Automatic) {
        return [];
    }
    // ... 其他检查逻辑 ...
    
    // 发送补全请求并返回结果
    const result = await this.sendRequest(document, position, context, token);
    return result ? [new vscode.InlineCompletionItem(result)] : [];
}
```

2. **辅助方法**:

- sendRequest(): 处理补全请求，提取文档上下文
- completion(): 构建提示词并调用语言模型
- formatCompletionPrompt(): 格式化补全提示词
- extractDocPrompt(): 从文档内容中提取前缀和后缀文本

主要工作流程：

1. 接收编辑器触发的补全请求

2. 检查是否满足补全条件（文件大小、上下文长度等）
3. 提取光标位置的上下文

支持 FIM (Fill In Middle) 模式的提示词格式

provideInlineCompletionItems() 方法内部实现了防抖机制

```typescript
// 获取防抖延迟时间，默认500ms
const requestDelay = config.get<number>('completions.requestDelay', 500);

// 核心防抖实现
await new Promise<void>(resolve => {
    // 设置定时器
    const timerId = setTimeout(resolve, requestDelay);
    
    // 如果在等待期间收到取消信号，清除定时器并立即resolve
    token.onCancellationRequested(() => {
        clearTimeout(timerId);
        resolve();
    });
});

// 检查是否在等待期间被取消
if (token.isCancellationRequested) {
    logger.debug('(inline completions): during debounce');
    return [];
}
```

1、延迟机制

- 使用 Promise 包装 setTimeout，实现异步等待
- 默认等待 500ms 后才会继续执行后续代码

2、取消机制

- 通过 token.onCancellationRequested 监听取消事件
- 如果用户在等待期间触发了新的补全，之前的请求会被取消
- 取消时会清除定时器并立即结束等待

3、状态检查

- 等待结束后立即检查 token.isCancellationRequested
- 确保只有最后一次请求会真正发送到 AI 服务



#### Auto-ops

Audo-dev定义了一系列Action都复用一套流程包括：

- AutoDoc
- AutoMethod
- AutoTest
- GenApiData

可以在 PromptManager.generateInstruction() 方法中找到对应的prompt-template：

```typescript
async generateInstruction(type: ActionType, context: TemplateContext): Promise<string> {
    switch (type) {
      case ActionType.AutoDoc:
        template = await templateRender.getTemplate(`prompts/genius/${humanLanguage}/code/auto-doc.vm`);
        break;
      case ActionType.AutoMethod:
        template = await templateRender.getTemplate(`prompts/genius/${humanLanguage}/code/auto-Method.vm`);
        break;
      case ActionType.AutoTest:
        template = await templateRender.getTemplate(`prompts/genius/${humanLanguage}/code/test-gen.vm`);
        break;
      case ActionType.GenApiData:
        template = await templateRender.getTemplate(`prompts/genius/${humanLanguage}/code/gen-api-data.vm`);
        break;
      case ActionType.Rename:
        template = await templateRender.getTemplate(`prompts/genius/${humanLanguage}/practises/rename.vm`);
        break;
      case ActionType.GenCommitMessage:
        template = await templateRender.getTemplate(`prompts/genius/${humanLanguage}/practises/gen-commit-msg.vm`);
        break;
      case ActionType.LlmReranker:
        template = await templateRender.getTemplate(`prompts/model/${humanLanguage}/reranker/llm-reranker.vm`);
        break;
      default:
        break;
    }
}
```

具体流程：

1. 创建NamedElementBuilder解析document结构，内部依赖TreeSitterFileManager
2. 通过 ToolchainContextManager 补全Context，比如team prompt，自定义prompt这类
3. 通过 TestGenProvider.additionalTestContext()收集单元测试信息，比如 TestFramework、BuildTool等
4. 通过promptManager.generateInstruction()产生最终的prompt
5. 调用lm.chat()返回结果



## 六、IDEA插件开发指南基础

### 环境准备

1. jdk 17
1. IDEA 需要安装 DevKit Plugin  
1. 通过IDEA - New Project wizard 选择 IDEA Plugin即可。

最开始是通过[intellij-platform-plugin-template](https://github.com/JetBrains/intellij-platform-plugin-template) 自动产生了一个项目，但是初始化gradle失败，最后还是通过IDEA自带的创建Plugin 方式，立马就可以了。

```
org.gradle.api.GradleException: No IntelliJ Platform dependency found.
Please ensure there is a single IntelliJ Platform dependency defined in your project and that the necessary repositories, where it can be located, are added.
See: https://plugins.jetbrains.com/docs/intellij/tools-intellij-platform-gradle-plugin-dependencies-extension.html
    at org.jetbrains.intellij.platform.gradle.utils.UtilsKt.platformPath(utils.kt:78)
    at org.jetbrains.intellij.platform.gradle.extensions.IntelliJPlatformDependenciesHelper$special$$inlined$provider$IntelliJPlatformGradlePlugin$1.call(IntelliJPlatformDependenciesHelper.kt:1170)
    at org.gradle.api.internal.provider.DefaultProvider.calculateOwnValue(DefaultProvider.java:72)
    at org.gradle.api.internal.provider.AbstractMinimalProvider.calculateValue(AbstractMinimalProvider.java:115)
    at org.gradle.api.internal.provider.TransformBackedProvider.calculateOwnValue(TransformBackedProvider.java:81)
    at org.gradle.api.internal.provider.AbstractMinimalProvider.calculateValue(AbstractMinimalProvider.java:115)
    at org.gradle.api.internal.provider.TransformBackedProvider.calculateOwnValue(TransformBackedProvider.java:81)
    at org.gradle.api.internal.provider.AbstractMinimalProvider.calculateOwnPresentValue(AbstractMinimalProvider.java:80)
    at org.gradle.api.internal.provider.AbstractMinimalProvider.get(AbstractMinimalProvider.java:100)
    at org.jetbrains.intellij.platform.gradle.extensions.IntelliJPlatformDependenciesHelper$createDependency$1$buildNumber$2.invoke(IntelliJPlatformDependenciesHelper.kt:998)
    at org.jetbrains.intellij.platform.gradle.extensions.IntelliJPlatformDependenciesHelper$createDependency$1$buildNumber$2.invoke(IntelliJPlatformDependenciesHelper.kt:998)
    at kotlin.SynchronizedLazyImpl.getValue(LazyJVM.kt:74)
```



### 日志

默认日志路径

```
/work/dist/branch/opensource/auto-dev/build/idea-sandbox/IU-2024.3/log/idea.log
/work/dist/branch/wacai/middleware/wapilot/build/idea-sandbox/IC-2024.1/log/idea.log
~/Library/Logs/JetBrains/IdeaIC2023.3/idea.log
```

### 打包命令

```shell
  # 启动
  ./gradlew :runIde -PbaseIDE=idea
  # 打包插件
 ./gradlew buildPlugin   
```





### 自定义 ToolWindow 

自定义 ToolWindow 通常需要以下几个步骤:

1、配置插件描述文件（plugin.xml
首先要在 plugin.xml 文件里声明工具窗口的相关信息，包含工具窗口的 ID、显示位置、初始状态等。示例如下：
```xml
<idea-plugin>
    <!-- 其他插件配置 -->
    <extensions defaultExtensionNs="com.intellij">
        <!-- 注册工具窗口工厂 -->
        <toolWindow id="MyCustomToolWindow"
                    anchor="right"
                    factoryClass="com.example.MyToolWindowFactory"
                    icon="/icons/myToolWindowIcon.svg"
                    initialState="visible"/>
    </extensions>
</idea-plugin>
```
上述代码中，各属性的含义如下：
- id：工具窗口的唯一标识符。
- anchor：工具窗口的停靠位置，例如 right 表示停靠在右侧，还可以是 left、bottom 等。
- factoryClass：工具窗口工厂类的全限定名，该类用于创建工具窗口的内容。
- icon：工具窗口的图标路径。
- initialState：工具窗口的初始状态，visible 表示初始时可见。

2、创建工具窗口工厂类

```java
public class MyToolWindowFactory implements ToolWindowFactory {
    @Override
    public void createToolWindowContent(Project project, ToolWindow toolWindow) {
        // 创建自定义的 JPanel 作为工具窗口的内容
        JPanel panel = new JPanel();
        JLabel label = new JLabel("This is my custom tool window.");
        panel.add(label);

        // 创建 Content 对象并添加到工具窗口
        ContentFactory contentFactory = ContentFactory.SERVICE.getInstance();
        Content content = contentFactory.createContent(panel, "", false);
        toolWindow.getContentManager().addContent(content);
    }
}

```
在 createToolWindowContent 方法中，创建了一个包含标签的 JPanel 作为工具窗口的内容，然后通过 ContentFactory 创建 Content 对象，并将其添加到工具窗口的内容管理器中。


3、管理ToolWindow

可以通过 ToolWindowManager.getInstance(project).getToolWindow(id); 获取ToolWindow

### Topic的使用

Topic 是消息总线（MessageBus）机制的核心组成部分，它用于定义消息的类型，方便不同组件之间进行消息的发布与订阅，实现松耦合的通信。下面详细介绍 Topic 的用法。
比如 Action 通过 MessageBus 机制发送给 ChatPannel 触发聊天

定义Topic:

```java
    public class AppTopics {
        public static final Topic<Consumer<ChatMessage>> CHAT_TOPIC = Topic.create("chat.message", Consumer.class);
    }
```


订阅Topic：
```java

public class ChatCodingPanel {
    public ChatCodingPanel(Project project, ChatCodingService chatService) {
        // ... existing code ...
        
        project.getMessageBus().connect().subscribe(ChatMessageTopic.CHAT_TOPIC, message -> {
            chatService.handlePromptAndResponse(
                this,
                new SimplePrompter(message.prompt()),  // 创建一个简单的prompter
                new ChatContext(),  // 根据需要传入context
                message.keepHistory()
            );
        });
    }
}
```


Action中发送Toipc

```java

public class ChatBaseAction extends AnAction {
    @Override
    public void actionPerformed(AnActionEvent e) {
        Project project = e.getProject();
        MessageBus messageBus = project.getMessageBus();
        
        // 发送消息到ChatCodingPanel
        messageBus.syncPublisher(ChatMessageTopic.CHAT_TOPIC)
            .accept(new ChatMessage("你的prompt", "显示的prompt", true));
    }
}
```

### 布局问题

之前实现方式如下：

```java
  JPanel headPanel = new JPanel(new BorderLayout());
  headPanel.setPreferredSize(new Dimension(-1, 34)); 
  
  JPanel leftPanel = new JPanel(new FlowLayout(FlowLayout.LEFT, 8, 3));
  leftPanel.add(createButton());//按钮
  titlePanel.add(new JLabel("HermesMessage.java"));//文件名
  titlePanel.add(new JBLabel("src/main/java/com/wacai/hermes/message/HermesMessage.java"));  //添加路径

  JPanel rightPanel = new JPanel(new FlowLayout(FlowLayout.LEFT,8, 3));
  rightPanel.setBackground(Color.red);
  rightPanel.add(new JBLabel("Apply"));
  rightPanel.add( new JBLabel("a...a"));

  headPanel.add(leftPanel);
  headPanel.add(rightPanel,BorderLayout.EAST);

```



![image-20250508094643598](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250508094643598.png)

可以看到右边Apply是顶格的，没有对齐。

当使用FlowLayout布局方式，容器内元素默认是顶格的，左边没有顶格是因为左边的 icon 顶起来了，如果去掉也会顶格。正确的方式是使用 BoxLayout布局方式。



```java
JPanel headPanel = new JPanel(new BorderLayout());       
//省略了leftPanel，很rightPanel类似
JPanel rightPanel = new JPanel();
rightPanel.setOpaque(false);//使用headPanel的背景
rightPanel.setLayout(new BoxLayout(rightPanel, BoxLayout.X_AXIS));
rightPanel.setBorder(BorderFactory.createEmptyBorder(3, 0, 3, 8)); //如果不设置会导致内部元素和边框没有间隙

JLabel moreLabel = new JLabel("test");
moreLabel.setBackground(ColorUtil.getEditorBackgroundColor());
moreLabel.setForeground(ColorUtil.getGreyFontColor());
moreLabel.setAlignmentY(Component.CENTER_ALIGNMENT); // 垂直居中
moreLabel.setBorder(BorderFactory.createEmptyBorder(8, 6, 8, 6)); // 上下左右边距
rightPanel.add(moreLabel);

headPanel.add(leftPanel, BorderLayout.WEST);
headPanel.add(rightPanel, BorderLayout.EAST);
```





### Opaque

opaque 是Swing中比较重要的概念，表示非透明，不同的组件有不同的默认值：

- JLable默认是false，也就是透明的
- JPanle默认是true，就是非透明的

实践中注意的问题：

- 什么JLabel 修改背景色没有效果？ 因为JLabel 的Opaque默认是false，也就是透明的，要修改背景色，需要setOpaque(true)


- 对于父 Panle和子 Panle，如果想让parent的背景色显示出来，则需要把子Panle的opaque设置为false。

### JPanel

JPanel 继承自JComponent，属于轻量级容器，用于组合其他 Swing 组件（如按钮、文本框、标签等），并通过布局管理器（LayoutManager）管理子组件的排列方式

用法：

```java
// 创建默认流式布局（FlowLayout）的 JPanel
JPanel panel = new JPanel();

// 使用指定布局管理器（如 BorderLayout）
JPanel panelWithLayout = new JPanel(new BorderLayout());

// 在主线程中更新 UI
ApplicationManager.getApplication().invokeLater(() -> {
    panel.add(new JButton("New Button"));
    panel.revalidate();
    panel.repaint();
});

```

常用方法：

- add(Component comp)：向面板中添加子组件（如按钮、文本框等）。
- getComponents(): 获取面板中所有子组件
- remove(): 移除单个子组件或清空所有子组件





### JBList 悬浮效果

需要 JBList  和 ListCellRenderer 配合：

JBList:

```java
 ListUtil.installAutoSelectOnMouseMove(this.fileList);//实现选中状态
 ScrollingUtil.installActions(this.fileList);//实现UI选中效果
```

ListCellRenderer.getListCellRendererComponent() 中：

```java
 // 设置背景色 - 根据选中状态设置不同的背景色
Color backgroundColor = isSelected ?
        ColorUtil.getItemSelectionBackground() :
        ColorUtil.getEditorBackgroundColor();

panel.setBackground(backgroundColor);
leftPanel.setBackground(backgroundColor);
buttonsPanel.setBackground(backgroundColor);
nameLabel.setBackground(backgroundColor);
pathLabel.setBackground(backgroundColor);
```





### Inlay

1. **Inlay 是 IntelliJ 平台的一种功能：**
   - 它允许你在编辑器中的某些位置插入额外的内容，比如注释、参数提示、错误提示、交互式按钮等。
   - 这些内容是**非破坏性**的，意味着它们不会修改源代码本身，只是附加的信息展示。
2. **典型使用场景：**
   - 方法参数提示：在调用方法时，显示参数名称（类似 `arg1: value1`）。
   - 行内错误提示：直接在代码旁边展示错误信息或建议修复的按钮。
   - 行内工具：比如快捷编辑按钮、小图标、或者显示变量的实时值。
   - 插件功能：许多 IDEA 插件会用 Inlay 来增强用户体验。
3. **InlayPanel 的作用：** 如果你看到一个类名为 `InlayPanel`，这可能是一个封装了 Inlay 的面板，用于定义如何渲染这些嵌入式内容。例如：
   - `InlayPanel` 可能负责管理 Inlay 的生命周期。
   - 定义如何将自定义组件（如 `JPanel` 或其他 Swing 组件）嵌入到编辑器中。
   - 提供交互逻辑，比如处理鼠标点击或键盘事件。
4. **相关 API：** IntelliJ 提供了相关的 API 支持自定义 Inlay，例如：
   - **`com.intellij.openapi.editor.Inlay`**：代表编辑器中的单个 Inlay。
   - **`InlayModel`**：用于创建、管理和移除 Inlay 的模型。
   - **`addInlineElement`** 和 **`addBlockElement`**：分别用于在代码中行内或块间添加 Inlay。



###  diff patch 

**diff patch** 通常用于描述文件或代码的**差异文件**，以及如何将这些差异应用到原始文件中的一种方式，涉及到diff 和patch工具。

`diff` 是一个常用的工具或命令（例如 Linux 的 `diff` 命令），用于比较两个文件（或两个目录）之间的差异。它会生成一个包含这些差异的输出结果，通常被称为 **diff 文件** 或 **差异文件**。

假设有两个文件

file1.txt

```

Hello World
This is line 2
```

file2.txt ：

```
Hello Universe
This is line 2
```

运行命令：

```
diff file1.txt file2.txt
```

输出的差异可能是：

```
1c1
< Hello World
---
> Hello Universe
```

这表明第 1 行发生了变化：从 `Hello World` 改为 `Hello Universe`。

`patch` 是另一个工具，用于将 `diff` 生成的差异文件应用到原始文件，从而更新它们。

假设差异文件为 `changes.patch`：

```
1c1
< Hello World
---
> Hello Universe

```

运行命令：

```
patch file1.txt changes.patch
```

执行后，`file1.txt` 会被更新为：

```
Hello Universe
This is line 2
```

cursor使用了一种类似于 **diff patch** 的模式来实现 **增量更新**。

IntelliJ 的 `com.intellij.diff` 包提供了强大的 Diff 和 Patch 支持，以下是关键的 API：

**生成 Diff**:

```java
import com.intellij.diff.DiffContentFactory;
import com.intellij.diff.DiffManager;
import com.intellij.diff.requests.SimpleDiffRequest;

// 示例：比较两个代码片段
void showDiff(Project project, String oldText, String newText) {
    var content1 = DiffContentFactory.getInstance().create(project, oldText);
    var content2 = DiffContentFactory.getInstance().create(project, newText);
    var request = new SimpleDiffRequest("Diff Viewer", content1, content2, "Old Version", "New Version");
    DiffManager.getInstance().showDiff(project, request);
}

```

**应用 Patch**:

使用 `com.intellij.openapi.vcs.patch.PatchApplier` 来应用补丁。

```java
import com.intellij.openapi.vcs.patch.PatchApplier;
import com.intellij.openapi.project.Project;
import com.intellij.openapi.vfs.VirtualFile;

void applyPatch(Project project, VirtualFile patchFile, VirtualFile baseDir) {
    PatchApplier patchApplier = new PatchApplier(project, baseDir, patchFile, null, null);
    patchApplier.execute();
}

```

shire 使用了 DiffProcessor 来处理 diff 功能， PatchProcessor 处理 patch功能，还有 [java-diff-utils](https://github.com/java-diff-utils/java-diff-utils) 这个框架

### IntentionAction

IntentionAction 是 IntelliJ IDEA 中一个强大的功能，它利用代码分析和智能提示，为开发者提供了众多的代码改进和优化操作。

当代码中存在可使用 IntentionAction 的情况时，通常会在代码编辑器中显示一个小灯泡图标，点击该图标，会弹出一个菜单，其中包含一系列的 IntentionAction 建议。

你也可以使用快捷键（默认是 `Alt + Enter`），在代码的相应位置上触发 IntentionAction 的菜单。

###  问题记录

1、ClassNotFoundException： com.intellij.psi.PsiJavaFile 

在 plugin.xml中需要定义：

``` 
    <dependencies>
        <plugin id="com.intellij.modules.java"/>
        <plugin id="org.jetbrains.plugins.gradle"/>
        <plugin id="org.jetbrains.idea.maven"/>
    </dependencies>
```

之前是

```
    <depends>com.intellij.modules.platform</depends>
```

### PSI

在 IntelliJ IDEA 的 PSI（Program Structure Interface）体系里 ，PsiParameter表示方法的参数。PsiParameter.getType(）返回PsiType，PsiType是一个抽象基类。

1. 如果参数类型是一个类、接口或者枚举时，getType()返回的是 PsiClassReferenceType
1. 如果参数类型是 Java 的基本数据类型时，getType()返回的是 PsiPrimitiveType
1. 如果参数类型是数组时，getType() 方法会返回 PsiArrayType 实例

### MCP

MCP 是一种由 Aider 提出的、结构化地与 LLM 进行交互的协议，目标是让 LLM 能够更可靠地处理工具调用、搜索、代码修改等任务。目前主流 LLM（比如 OpenAI GPT-4, Claude 3, Gemini, Mistral, LLaMA）并不是都“原生支持” MCP 协议。大多数实现都是：MCP client 把 MCP 格式转成当前模型能理解的 function call 结构或 prompt。

在 OpenAI 的 GPT-4 的 tools API 中，你可以定义 tools（函数），传入函数调用历史，LLM 会返回 function_call 信息； MCP client 会接收这个 response，执行函数，再把结果打包回复给 LLM。

整个流程是：
```
用户输入 → MCP 客户端封装为 prompt + function 调用 → 发送给 LLM → LLM 调用工具 → 客户端执行 → 返回结果给 LLM → 继续交互
```

总结，MCP 是客户端层协议，大多通过 function-calling/tool-calling 机制间接实现。当前像 Aider、OpenDevin、Continue.dev 等项目实现了基于 MCP 的编辑器交互，兼容主流 LLM。

### 单元测试

可以继承下面两个类其中之一：

- **`UsefulTestCase`**：不会对 IDE 平台的环境进行初始化，所以它的测试环境较为轻量级，启动速度快。不过，这也意味着在测试中无法直接访问 IDE 的各种服务和组件。
- **`BasePlatformTestCase`**：会对 IDE 的核心组件和环境进行初始化，从而为测试提供完整的 IDE 上下文。但这也会使测试环境的启动时间变长，因为要进行大量的初始化工作。

### 自定义语言

参考样例项目：[my-pilot](https://github.com/jiangyunpeng/my-pilot)

#### 词法分析器

Flex（Fast Lexical Analyzer Generator）文件一般由三部分组成，它们由两个%%分隔：

```
定义部分
%%
规则部分
%%
用户代码部分
```

定义变量：

```
VARIABLE_ID=[a-zA-Z0-9][_\-a-zA-Z0-9]*
AGENT_ID=[a-zA-Z0-9][_\-a-zA-Z0-9]*
COMMAND_ID=[a-zA-Z0-9][_\-a-zA-Z0-9]*
LANGUAGE_ID=[a-zA-Z][_\-a-zA-Z0-9 .]*
CODE_CONTENT=[^\n]+
COMMENTS=\[ ([^\]]+)? \] [^\t\r\n]*
NEWLINE= \n | \r | \r\n
```



#### 基础概念

- <状态>: 代表一个词法状态，每个状态下有不同的匹配规则。
- {TOKEN}: 代表一个正则匹配的标记，通常在 %{ %} 里定义的 Java 方法中会返回相应的 IElementType。
- yypushback(n): 让词法分析器回退 n 个字符，以便下次匹配。
- yybegin(STATE): 切换到 STATE 状态。

动作代码块(Action Block)的语法:
```
<状态> {
    {TOKEN}    {return Token}
    {TOKEN}    {yybegin(状态)} 
    {TOKEN}    {yypushback } 

}
```
表示匹配到对应的 Token 执行对应的动作, 可以return Token，也可以修改状态，还可以回退



#### YYINITIAL 初始状态

```flex
<YYINITIAL> {
  {CODE_CONTENT}          { return content(); }
  {NEWLINE}               { return NEWLINE;  }
  "["                     { yypushback(yylength()); yybegin(COMMENT_BLOCK);  }
  [^]                     { yypushback(yylength()); return TEXT_SEGMENT; }
}

```

- 识别 CODE_CONTENT，调用 content() 方法，确定它属于代码块还是普通文本。
- 识别 NEWLINE（换行符），直接返回 NEWLINE 标记。
- [ 开头的内容可能是注释，进入 COMMENT_BLOCK 解析。
- 其他字符被识别为 TEXT_SEGMENT（普通文本）。


#### COMMAND_BLOCK 解析命令

```flex
<COMMAND_BLOCK> {
  {COMMAND_ID}            { return COMMAND_ID; }
  {COLON}                 { yybegin(COMMAND_VALUE_BLOCK); return COLON; }
  " "                     { yypushback(1); yybegin(YYINITIAL); }
  [^]                     { yypushback(1); yybegin(YYINITIAL); }
}
```

识别 COMMAND_ID（命令名称） 遇到 冒号 进入 COMMAND_VALUE_BLOCK 解析命令值。空格或其他字符都回退，并返回 YYINITIAL 处理。


#### COMMAND_VALUE_BLOCK - 解析命令参数

```
<COMMAND_VALUE_BLOCK> {
  {COMMAND_PROP}          { return COMMAND_PROP;  }
  " "                     { yypushback(1); yybegin(YYINITIAL); }
  [^]                     { yypushback(1); yybegin(YYINITIAL); }
}

```
识别 COMMAND_PROP（命令的参数）并返回。空格或其他字符都回退，并返回 YYINITIAL 处理。


#### VARIABLE_BLOCK 识别变量

```
<VARIABLE_BLOCK> {
  {VARIABLE_ID}        { yybegin(YYINITIAL); return VARIABLE_ID; }
  [^]                  { return TokenType.BAD_CHARACTER; }
}
```
变量标识符 VARIABLE_ID 解析后回到初始状态，其他字符返回 BAD_CHARACTER。 VARIABLE_ID定义的是[a-zA-Z0-9][_\-a-zA-Z0-9]*


####  codeContent() 方法

该方法在解析 代码块内容 时被调用。


```
private IElementType content() {
    String text = yytext().toString().trim();

    //①
    if (isCodeStart == true && text.equals("```")) {
            return codeContent();
    }

    //②
    if (isCodeStart == false && text.startsWith("```")) {
        isCodeStart = true; 
        yypushback(yylength() - 3);
        yybegin(LANG_ID);
        return CODE_BLOCK_START;
    }
    //③
    if (isCodeStart) {
        return CODE_CONTENT;
    }

    //④
    char first  = text.charAt(0);
    if (first == '@') {
        yypushback(yylength() - 1);
        yybegin(AGENT_BLOCK);
        return AGENT_START;
    }
    //⑤
    else if (first == '/') {
        yypushback(yylength() - 1);
        yybegin(COMMAND_BLOCK);
        return COMMAND_START;
    }//⑥
    else if (first == '$') {
        yypushback(yylength() - 1);
        yybegin(VARIABLE_BLOCK);
        return VARIABLE_START;
    }else {
        return TEXT_SEGMENT;
    }
}
```

1. 如果当前在代码块内 (isCodeStart == true)，且又遇到了代码块标志 ("```")，则调用 codeContent() 处理。
2. 如果当前不在代码块内 (isCodeStart == false)，且文本以 ``` 开头： 回退 yylength() - 3 个字符，使得 LANG_ID 状态可以重新分析 语言标识符（比如 ```java）。 切换到 LANG_ID 状态，以便进一步解析代码块的语言类型。返回 CODE_BLOCK_START，表示 代码块开始。
3. 如果当前在代码块内，默认返回 CODE_CONTENT 作为词法类型。
4. 如果文本以 @ 开头：yypushback(yylength() - 1); 回退 1 个字符，让 AGENT_BLOCK 重新解析整个标记。切换到 AGENT_BLOCK 状态，返回 AGENT_START。
5. 如果文本以 / 开头，类似 @ 处理逻辑，进入 COMMAND_BLOCK 状态，返回 COMMAND_START
6. 如果文本以 $ 开头，进入 VARIABLE_BLOCK 解析变量，返回 VARIABLE_START。

#### 参考

- [IDEA兼容性问题](https://github.com/unit-mesh/auto-dev/blob/master/docs/development/compatible-strategy.md)
- [官方文档](https://plugins.jetbrains.com/docs/intellij/developing-plugins.html)
- [IntelliJ Platform Gradle Plugin 2.0 Is Out](https://blog.jetbrains.com/platform/2024/07/intellij-platform-gradle-plugin-2-0/)

## 七、MCP

- mcp官方地址：https://modelcontextprotocol.io/introduction
- 官方例子： https://modelcontextprotocol.io/quickstart/client
- java sdk: https://modelcontextprotocol.io/sdk/java/mcp-client
- 讨论： https://chatgpt.com/c/686b7638-45f4-8003-ab12-16b24e1f588d



### 协议

1、LLM 如何判断用户的问题需要访问哪个 MCP？

当接入多个 MCP（如代码搜索、测试生成、代码执行等）时，LLM 必须能够理解它们各自的能力，以便根据用户问题选择调用哪个 MCP。常见的设计方式包括：

- 在 **System Prompt 或 Tool 描述中写明每个 MCP 的功能和调用方式**，让 LLM 自行判断。
- 使用结构化方式注册功能（如 Function Calling 中的 `functions` 参数），通过参数 schema + 描述，引导 LLM 选择合适工具。
- 实现一个「意图识别模块」或「Router Agent」先分类，再路由到对应的 MCP。

本质上，无论是否使用官方标准，**关键是你要“让 LLM 知道 MCP 能干什么，并学会如何调用它”**。



2、MCP 本质：结构化描述外部能力，替代自然语言 prompt 的方式

MCP（Model Context Protocol）是用结构化方式定义外部工具（如符号检索、代码执行等）的能力，并将其注册到 LLM 可调用的范围内。它的核心作用是：

- 用标准格式描述工具（Tool）、资源（Resource）、调用参数与返回结构
- 替代传统的自然语言提示，更高效、精确地引导模型完成工具调用

例如：
 假如你有一个 `search_code` 的 MCP，传统做法是写一段 prompt 让模型理解其作用，而使用 MCP 的结构化方式可以直接传入：

```json
{
  "name": "search_code",
  "description": "查找指定类名或方法名在源码中的定义位置",
  "parameters": {
    "type": "object",
    "properties": {
      "symbol": { "type": "string", "description": "类名或方法名" }
    },
    "required": ["symbol"]
  }
}
```

这样 LLM 就能根据用户输入的问题自动选择调用 `search_code` 并正确填参。

###  MCP Server

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250807142929619.png" alt="image-20250807142929619" style="zoom:50%;" />

Remote MCP Server 通常集成了认证授权、状态管理、数据库访问等企业级功能，能够为多用户提供服务。

Remote MCP Server 通过集中化部署和管理，解决了Local MCP 的一些问题：

- ***\*拓宽使用场景\******：**非技术用户可以通过网页或移动应用等，随时随地通过互联网使用 MCP 能力。
- ***\*集中化安全管控\******：**企业可以在远程服务器上实施严格的访问控制、加密和审计机制，确保敏感凭证的安全。
- ***\*统一权限管理\******：**通过集中化的身份验证和授权系统，企业可以精确控制每个用户对不同资源的访问权限。
- ***\*简化部署与维护\******：**只需维护中央服务器，大大降低了运维成本和复杂性。

一些开源的 [MCP Server](https://github.com/modelcontextprotocol/servers)：

这里介绍[higress](https://mcp.higress.ai/)提供的托管：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250807160113716.png" alt="image-20250807160113716" style="zoom:50%;" />

在服务层面，Higress 支持3种 MCP Server 接入方式：

- 通过 Wasm 插件在 Higress 内部实现的内置 MCP Server，适用于对接现有 SaaS API 生态提供 MCP 能力。
- 直接转发给外部已经支持 MCP 协议的服务，适用于对接已有的外部 MCP 能力。
- 通过服务注册中心（如 Nacos）动态发现外部 MCP Server，并且可以通过 Nacos 配置中心的能力动态更新 MCP Server 的工具定义，适用于企业将传统业务 API 升级为 MCP 能力。**Nacos 下周将发布 MCP Registry，实现存量应用接口“0改动”升级到 MCP 协议。**



协议卸载的优势：

目前 MCP 一共有两个版本的协议，分别是20241105版本和20250326版本。从协议发展来看，标准的沉淀和优化速度，难以跟上迅猛发展的 MCP Server 生态。这也为 MCP 的早期采用者埋下隐患，当构建了大量 MCP Server，面对未来协议版本升级，需要进行繁重的升级改造工作。

而在 Higress 这层网关上，刚好可以帮助屏蔽多个版本的协议细节，甚至**实现MCP to REST/Dubbo 等等协议转换的能力**。在 Higress 这层网关完成所有版本的 MCP 协议卸载。就像我们用 Higress 作为传统的 API 网关时一样，统一卸载 HTTP1/HTTP2/HTTP3 的客户端协议。

目前，Higress 可以在一个接入点上同时支持 MCP 的20241105版本和20250326版本两种协议。**在协议的传输层上，支持目前 AI 工具采用最广泛的POST+SSE 模式，以及最新的 Streamable HTTP 模式**。

### Nacos MCP Route

Nacos MCP Router（以下简称 Router）。Router 是一个遵循 MCP 协议的标准 MCP Server，其核心能力是**根据用户任务的语义描述和关键词，智能地从 MCP 注册中心中筛选出最匹配的 MCP Server，并将这些候选工具提供给大模型进行决策**。

**智能路由模式**

智能路由模式下，Router 是一个标准 MCP Server，提供 3 个 MCP 工具：

- search_mcp_server：根据任务描述及关键字智能筛选 MCP Server；
- add_mcp_server：初始化指定的 MCP Server；如果是 stdio 协议，执行安装及初始化逻辑；如果是 SSE 或 streamable HTTP 协议，执行建连及初始化逻辑；
- use_mcp_tool：使用目标 MCP Server 的某个工具，Router 会工具代理请求至目标MCP Server。



<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20250807175020287.png" alt="image-20250807175020287" style="zoom:50%;" />



nacos这边统一管理mcp server，大概有两种思路：
1. 提供sdk，让应用把mcp 注册到nacos
2. 通过网关做协议转化，就是你在nacos 控制台配置好mcp的描述信息，指定好后端的http/dubbo接口地址， nacos server这边来会做一层协议转化

|       | 优点                                                         | 缺点                     |
| ----- | ------------------------------------------------------------ | ------------------------ |
| 方案1 | 接口统一定义在应用项目中，参数调整不会出现遗漏               | 需要修改应用代码         |
| 方案2 | 业务方是不需要修改任何代码只需要配置就可以完成mcp的部署，并且调整prompt不需要重新部署应用 | 调整接口定义需要修改两处 |
|       |                                                              |                          |



### 例子

下面是一个[查询快递的mcp](https://github.com/kuaidi100-api/kuaidi100-MCP)

```python
import os

import httpx
from mcp.server.fastmcp import FastMCP
from pydantic import Field

# 创建MCP服务器实例
mcp = FastMCP(
    name="kuaidi100_mcp",
    instructions="This is a MCP server for kuaidi100 API."
)

"""
获取环境变量中的API密钥, 用于调用快递100API
环境变量名为: KUAIDI100_API_KEY, 在客户端侧通过配置文件进行设置传入
获取方式请参考：https://poll.kuaidi100.com/manager/page/myinfo/enterprise
"""

kuaidi100_api_key = os.getenv('KUAIDI100_API_KEY')
kuaidi100_api_url = "https://api.kuaidi100.com/stdio/"

@mcp.tool(name="query_trace", description="查询物流轨迹服务，传入快递单号和手机号，获取对应快递的物流轨迹")
async def query_trace(kuaidi_num: str = Field(description="快递单号"),
                      phone: str = Field(description="手机号，当快递单号为SF开头时必填；如果用户没告知手机号，则不调用服务，继续追问用户手机号是什么"), default="") -> str:
    """
    查询物流轨迹服务, 根据快递单号查询物流轨迹
    """
    method = "queryTrace"

    # 调用查询物流轨迹API
    params = {
        "key": f"{kuaidi100_api_key}",
        "kuaidiNum": f"{kuaidi_num}",
        "phone": f"{phone}",
    }

    response = await http_get(kuaidi100_api_url + method, params)
    return response


@mcp.tool(name="estimate_time", description="通过快递公司编码、收寄件地址、下单时间、业务/产品类型来预估快递可送达的时间，以及过程需要花费的时间；用于寄件前快递送达时间预估")
async def estimate_time(kuaidi_com: str = Field(description="快递公司编码，一律用小写字母；目前仅支持：京东：jd，跨越：kuayue，顺丰：shunfeng，顺丰快运：shunfengkuaiyun，中通：zhongtong，德邦快递：debangkuaidi，EMS：ems，EMS-国际件：emsguoji，邮政国内:youzhengguonei，国际包裹：youzhengguoji，申通：shentong，圆通：yuantong，韵达：yunda，宅急送：zhaijisong，芝麻开门：zhimakaimen，联邦快递：lianbangkuaidi，天地华宇：tiandihuayu，安能快运：annengwuliu，京广速递：jinguangsudikuaijian，加运美：jiayunmeiwuliu，极兔速递：jtexpress"),
                        from_loc: str = Field(description="出发地（地址需包含3级及以上），例如：广东深圳南山区；如果没有省市区信息的话请补全，如广东深圳改为广东省深圳市南山区"),
                        to_loc: str = Field(description="目的地（地址需包含3级及以上），例如：北京海淀区；如果没有省市区信息的话请补全，如广东深圳改为广东省深圳市南山区。如果用户没告知目的地，则不调用服务，继续追问用户目的地是哪里"),
                        order_time: str = Field(description="下单时间，格式要求yyyy-MM-dd HH:mm:ss，例如：2023-08-08 08:08:08；如果用户没告知下单时间，则不传", default=""),
                        exp_type: str = Field(description="业务或产品类型，如：标准快递")) -> str:
    """
    通过快递公司编码、收寄件地址、下单时间和业务/产品类型来预估快递可送达的时间，以及过程需要花费的时间；用于寄件前快递送达时间预估",
    """
    method = "estimateTime"

    # 调用查询物流轨迹API
    params = {
        "key": f"{kuaidi100_api_key}",
        "kuaidicom": f"{kuaidi_com}",
        "from": f"{from_loc}",
        "to": f"{to_loc}",
        "orderTime": f"{order_time}",
        "expType": f"{exp_type}",
    }
    response = await http_get(kuaidi100_api_url + method, params)

    return response


@mcp.tool(name="estimate_time_with_logistic", description="通过快递公司编码、收寄件地址、下单时间、历史物流轨迹信息来预估快递送达的时间；用于在途快递的到达时间预估")
async def estimate_time_with_logistic(kuaidi_com: str = Field(description="快递公司编码，一律用小写字母；目前仅支持：京东：jd，跨越：kuayue，顺丰：shunfeng，顺丰快运：shunfengkuaiyun，中通：zhongtong，德邦快递：debangkuaidi，EMS：ems，EMS-国际件：emsguoji，邮政国内:youzhengguonei，国际包裹：youzhengguoji，申通：shentong，圆通：yuantong，韵达：yunda，宅急送：zhaijisong，芝麻开门：zhimakaimen，联邦快递：lianbangkuaidi，天地华宇：tiandihuayu，安能快运：annengwuliu，京广速递：jinguangsudikuaijian，加运美：jiayunmeiwuliu，极兔速递：jtexpress"),
                                      from_loc: str = Field(description="出发地（地址需包含3级及以上），例如：广东深圳南山区；如果没有省市区信息的话请补全，如广东深圳改为广东省深圳市南山区"),
                                      to_loc: str = Field(description="目的地（地址需包含3级及以上），例如：北京海淀区；如果没有省市区信息的话请补全，如广东深圳改为广东省深圳市南山区。如果用户没告知目的地，则不调用服务，继续追问用户目的地是哪里"),
                                      order_time: str = Field(description="下单时间，格式要求yyyy-MM-dd HH:mm:ss，例如：2023-08-08 08:08:08；如果用户没告知下单时间，则不传",default=""),
                                      exp_type: str = Field(description="业务或产品类型，如：标准快递"),
                                      logistic: str = Field(description="历史物流轨迹信息，用于预测在途时还需多长时间到达；一般情况下取query_trace服务返回数据的历史物流轨迹信息转为json数组即可，数据格式为：[{\"time\":\"2025-05-09 13:15:26\",\"context\":\"您的快件离开【吉林省吉林市桦甸市】，已发往【长春转运中心】\"},{\"time\":\"2025-05-09 12:09:38\",\"context\":\"您的快件在【吉林省吉林市桦甸市】已揽收\"}]；time为物流轨迹节点的时间，context为在该物流轨迹节点的描述")) -> str:
    """
    通过快递公司编码、收寄件地址、下单时间和业务/产品类型、历史物流轨迹信息来预估快递送达的时间；用于在途快递的到达时间预估。接口返回的now属性为当前时间，使用arrivalTime-now计算预计还需运输时间
    """
    method = "estimateTimeWithLogistic"

    # 调用查询物流轨迹API
    params = {
        "key": f"{kuaidi100_api_key}",
        "kuaidicom": f"{kuaidi_com}",
        "from": f"{from_loc}",
        "to": f"{to_loc}",
        "orderTime": f"{order_time}",
        "expType": f"{exp_type}",
        "logistic": f"{logistic}",
    }
    response = await http_get(kuaidi100_api_url + method, params)
    return response


@mcp.tool(name="estimate_price", description="通过快递公司、收寄件地址和重量，预估快递公司运费")
async def estimate_price(kuaidi_com: str = Field(description="快递公司的编码，一律用小写字母；目前仅支持：顺丰：shunfeng，京东：jd，德邦快递：debangkuaidi，圆通：yuantong，中通：zhongtong，申通：shentong，韵达：yunda，EMS：ems"),
                         rec_addr: str = Field(description="收件地址，如”广东深圳南山区”；如果没有省市信息的话请补全，如广东深圳改为广东省深圳市。如果用户没告知收件地址，则不调用服务，继续追问用户收件地址是哪里"),
                         send_addr: str = Field(description="寄件地址，如”北京海淀区”；如果没有省市信息的话请补全，如广东深圳改为广东省深圳市。如果用户没告知寄件地址，则不调用服务，继续追问用户寄件地址是哪里"),
                         weight: str = Field(description="重量，默认单位为kg，参数无需带单位，如1.0；默认重量为1kg")) -> str :
    """
    通过快递公司、收寄件地址和重量，预估快递公司运费
    """
    method = "estimatePrice"

    # 调用查询物流轨迹API
    params = {
        "key": f"{kuaidi100_api_key}",
        "kuaidicom": f"{kuaidi_com}",
        "recAddr": f"{rec_addr}",
        "sendAddr": f"{send_addr}",
        "weight": f"{weight}",
    }
    response = await http_get(kuaidi100_api_url + method, params)
    return response


async def http_get(url: str, params: dict) -> str:
    try:
        async with httpx.AsyncClient() as client:
            response = await client.get(url, params=params)
            response.raise_for_status()

    except httpx.HTTPError as e:
        raise Exception(f"HTTP request failed: {str(e)}") from e
    except KeyError as e:
        raise Exception(f"Failed to parse response: {str(e)}") from e
    return response.text


if __name__ == "__main__":
    mcp.run(transport="stdio")

```





### 参考

- [Nacos MCP Router 部署最佳实践](https://mp.weixin.qq.com/s/KDpAHO4BCGgl7vcG2PEyrw)
- [Higress 开源 Remote MCP Server 托管方案，并将上线 MCP 市场](https://mp.weixin.qq.com/s/WYlqHm2YZSiRv4USUNUBgg)
- [Higress 新增 MCP 服务管理，助力构建私有 MCP 市场](https://mp.weixin.qq.com/s/CoFyJMX7cHEKUFSSbKtvZQ)
- [higress mcp市场](https://mcp.higress.ai/server/server9018)
- [火山mcp市场](https://www.volcengine.com/mcp-marketplace)
- [Spring AI Alibaba联合Higress发布业界首个Streamable HTTP实现方案](https://mp.weixin.qq.com/s/wZ47ZETmjO0RwsJIbCFdJw)



## 八、上下文工程



### 参考

- [llamaindex-context-engineering-what-it-is-and-techniques-to-consider](https://www.llamaindex.ai/blog/context-engineering-what-it-is-and-techniques-to-consider)
- [datacamp-what is context engineering](https://www.datacamp.com/blog/context-engineering)
- [12-factor-agents](https://github.com/humanlayer/12-factor-agents)
- [how-to-fix-your-context](https://www.dbreunig.com/2025/06/26/how-to-fix-your-context.html)
