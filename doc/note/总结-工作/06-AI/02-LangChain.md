## 概述

对于LLM我们应该把精力花在哪里？

我们看一下 LLM 技术的演进，以行业龙头 OpenAI 来看，在 2022 年 11 月底推出基于人类反馈强化学习 （RLHF）的 ChatGPT，特别是在 2023 年 3 月份推出 GPT4.0 提高了复杂任务的推理能力以及多模态能力之后，就已经不在基础模型上做进一步的提升，而是把精力放到工程化应用上，简单来说就是怎么把大模型结合具体场景用起来，聚焦在 Prompt 工程、Agent、工具使用（RAG、函数调用）、更低的成本和更多的 token 等应用领域。

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240516152541875.png" alt="image-20240516152541875" style="zoom:50%;" />



## 基本概念

###  Text completion(文本完成) module 和 Chat module  

openAI中 Text completion 和Chat 是使用LLM生成或操作文本两种不同方式。

Text Completion 。“文本完成”模型旨在根据给定的提示生成或操作文本。它们为任何 OpenAI 模型提供了一个简单但功能强大的接口。您输入一些文本作为提示，模型将生成一个文本完成，尝试匹配您提供的任何上下文或模式。例如，如果您向 API 发出提示：

```shell
As Descartes said, I think, therefore #正如笛卡尔所说，我思，所以
```

该模型将返回类似以下内容：

```shell
I am. #我在
```

主要特点：

- **text in, text out**
- 适用于根据单个输入提示生成响应的单轮任务
- 常适合内容生成、摘要、问答等
- 允许通过最大标记、温度和 top-p 等参数自定义生成的文本



比如langchain例子：

```python
prompt_template = PromptTemplate.from_template(
    "Tell me a {adjective} joke about {content}."
)
```



Chat models。“聊天”模型旨在生成对话文本，例如对话、聊天、电子邮件等。它们将一系列消息作为输入，并返回模型生成的消息作为输出。聊天的主要挑战是它比文本完成需要更多的数据和处理能力。。您还必须处理模型的令牌限制，这意味着您可能必须缩短或截断长对话。该模型还可能生成不相关、不一致或不准确的响应。

主要功能：

- 专为多个角色的多轮对话而设计
- 输入是message，可以设置role，另外可以指定多条message
- 通过处理整个对话历史记录来维护对话上下文
- 非常适合需要来回交互的聊天机器人应用程序和任务



比如langchain例子：

```python
chat_template = ChatPromptTemplate.from_messages(
    [
        ("system", "You are a helpful AI bot. Your name is {name}."),
        ("human", "Hello, how are you doing?"),
        ("ai", "I'm doing well, thanks!"),
        ("human", "{user_input}"),
    ]
)
```



### prompt

提示词（prompt）是一种与生成性人工智能模型（如Kimi）进行交互的方式，它通过简单的语言指令来引导模型产生期望的输出。提示词可以是问题、指令或者任何形式的文本输入，旨在激发模型生成特定类型的响应或完成特定的任务。

提示词工程（prompt engineering）则是一门发挥想象力的艺术，它涉及创造性地设计和优化这些提示词，以便从语言模型中获得最佳的输出结果。在提示词工程中，不需要具备编程经验，创造力和坚持不懈的精神将极大地帮助你在与模型的互动中取得成功。通过不同类型的提示词，如直接提示（Zero-shot）、带有示例的提示（One-shot, few-shots, multi-shots）以及思维链提示（Chain-of-Thought, CoT），可以更有效地指导模型完成各种任务。

常用prompt模板：

**直接提示（Zero-shot）**

不给例子。

```
请列出一些适合初次访问北京市的游客的博客文章创意。
```

**带有示例的提示（One-shot）**

除了任务说明外, 只给一个例子

```
请你作为一个专业的社交媒体网红，列出一些适合初次访问北京市的游客的博客文章创意。
比如：接下来的北京，比你想的还要美～
```

**带有多个示例的提示（Few-shot）**

```
请你作为一个专业的美食评论家，推荐几家北京的特色餐厅，并给出推荐理由。
比如：全聚德烤鸭店 - 历史悠久，烤鸭皮脆肉嫩。
比如：南门涮肉 - 老北京涮羊肉，传统风味，服务亲切。
```

**思维链提示（Chain-of-Thought, CoT）**

标准的prompt让大模型直接做数学题，果然大模型一问一个胡说八道，证明它确实没有推理能力。（[思维链](https://mp.weixin.qq.com/s/tka-6HeznuJMzTj5l57l1g)）则在one-shot当中加入了解题的中间过程，诱导大模型“按步骤解题”，不是直接给出计算结果。这一回大模型终于推导出了正确的答案。

```
这组数字中的奇数相加得到一个偶数：4, 8, 9, 15, 12, 2, 1。
提示：所有奇数（9, 15, 1）相加后（9 + 15 + 1），实际得到25。所以答案是False。

现在我想知道：这组数字中的偶数相加得到一个奇数吗？：3, 6, 8, 10, 12, 14, 16。
```



参考：

- [chat-vs-text-completion](https://dreamsavior.net/docs/add-on/chatgpt-translator/chat-vs-text-completion/)
- [OpenAI GPT — Completions vs Chat](https://medium.com/@sherazsiddiqidotcom/openai-gpt-completions-vs-chat-completions-a-comprehensive-comparison-a3a17cf3466e)

## Langchain 官方文档

参考：

- [LangChain quickstart](https://python.langchain.com/v0.1/docs/get_started/quickstart/)
- [LangChain 工程架构解析](https://mp.weixin.qq.com/s/YaYjo0i2kbafvgj-EjAMfA)

官方文档结构：

- Quickstart： 介绍了LLM Chain、Retrieval Chain、Agent的基本用法
- Use case: 详细介绍了场景，比如RAG、Chatbots、Query analysis

### 基本用法

用法代码在:https://github.com/jiangyunpeng/buzz-llm

参考：[integrations-openai](https://python.langchain.com/v0.1/docs/integrations/text_embedding/openai/)

#### LLM

```python
llm = AzureChatOpenAI(
    azure_deployment="gpt40",
    temperature=0.5,
    azure_endpoint="http://172.30.134.204:8080",
    openai_api_key="45a7a7914f0b4bac807f03088d97f20a",
    openai_api_version="2023-03-15-preview",
)
msg = HumanMessage(content='你好，llm是什么？')
print(llm.invoke([msg]))
```

该方式封装的是  text completion 模型

#### Chat

```python
from langchain_core.messages import HumanMessage
from langchain_openai import AzureChatOpenAI

# 使用 AzureChatOpenAI
llm = AzureChatOpenAI(
    azure_deployment="gpt40",
    temperature=0.5,
    azure_endpoint="http://172.30.134.204:8080",
    openai_api_key="45a7a7914f0b4bac807f03088d97f20a",
    openai_api_version="2023-03-15-preview",
)

msg = HumanMessage(content='你好，hermes是什么？')
print(llm.invoke([msg]))
```

该方式封装的是  chat 模型

#### Embedding Model

```python
from langchain_openai import OpenAIEmbeddings

embeddings = AzureOpenAIEmbeddings(
    azure_deployment="embedding",
    model="text-embedding-ada-002-2",
    azure_endpoint="http://172.30.134.253:8080",
    openai_api_type="azure",
    openai_api_key="45a7a7914f0b4bac807f03088d97f20a",
    openai_api_version="2023-03-15-preview"
)

doc_result = embeddings.embed_documents([text])

```

### LCEL

LangChain Expression Language ([LCEL](https://python.langchain.com/v0.1/docs/expression_language/interface/))  是LangChain提供的类似linux 管道命令，基于  [Runnable interface](https://python.langchain.com/v0.1/docs/expression_language/interface/) 实现。

基本用法如下：

```python
from langchain_core.output_parsers import StrOutputParser
from langchain_core.prompts import ChatPromptTemplate
from langchain_openai import ChatOpenAI

model = AzureChatOpenAI(
        azure_deployment="gpt40",
        temperature=0.5,
        azure_endpoint="http://172.30.134.204:8080",
        openai_api_key="45a7a7914f0b4bac807f03088d97f20a",
        openai_api_version="2023-03-15-preview",
    )

prompt = ChatPromptTemplate.from_template("tell me a short joke about {topic}") # ①
output_parser = StrOutputParser()

chain = prompt | model | output_parser

chain.invoke({"topic": "ice cream"}) #②
```

1. ChatPromptTemplate 返回一个 PromptValue

2. chain.invoke()会依次调用 prompt.invoke() 、model.invoke() 、output_parser.invoke()，整个流程如下：
   1. We pass in user input on the desired topic as `{"topic": "ice cream"}`
   2. The `prompt` component takes the user input, which is then used to construct a PromptValue after using the `topic` to construct the prompt.
   3. The `model` component takes the generated prompt, and passes into the OpenAI LLM model for evaluation. The generated output from the model is a `ChatMessage` object.
   4. Finally, the `output_parser` component takes in a `ChatMessage`, and transforms this into a Python string, which is returned from the invoke method.

等同于：

```java
output_parser.invoke(model.invoke(prompt.invoke())
```

 

### RAG 

RAG is a technique for augmenting LLM knowledge with additional data.

LLMs 可以推理广泛的主题，但他们的知识仅限于他们接受训练的特定时间点的公共数据。如果您想要构建能够推理私有数据或模型截止日期之后引入的数据的 AI 应用程序，you need to **augment** the knowledge of the model with the specific information it needs。

The process of bringing the appropriate information and inserting it into the model prompt is known as **Retrieval Augmented Generation** (RAG).



典型的 RAG 应用程序有两个主要组件：

- **Indexing**: 用于从源获取数据并为其建立索引的管道。这通常发生在离线状态。
- **Retrieval**: 实际的 RAG 链，它在运行时接受用户查询并从索引中检索相关数据，然后将其传递给模型



主要流程如下：

 <img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240516113751750.png" alt="image-20240516113751750" style="zoom:50%;" />

索引阶段：

- 加载：首先我们需要加载数据。这是通过 DocumentLoaders 完成的。
- 分割：文本分割器将大的 `Documents` 分成更小的块。这对于索引数据和将其传递到模型都很有用，因为大块更难搜索并且不适合模型的有限上下文窗口。
- Embeddings：使用大模型把doc转为向量 
- 存储：我们需要某个地方来存储和索引我们的向量，以便以后可以搜索它们。

检索和生成：

- 检索：给定用户输入，使用检索器从存储中检索相关分割。
- 生成：ChatModel / LLM 使用提示生成答案，其中包括问题和检索到的数据



代码执行：

```python
    # 省略前面初始化
    rag_chain = (
            {"context": retriever | format_docs, "question": RunnablePassthrough()}
            | prompt
            | model
            | StrOutputParser()
    )

    print(rag_chain.invoke("What is Task Decomposition?"))
```

对应流程：



```mermaid
graph LR;
    Question-->RunnableParallel;
    RunnableParallel-->Retriever;
    RunnableParallel-->RunnablePass;
    Retriever-->ChatModel;
    RunnablePass-->ChatModel;
    ChatModel-->Result;
```





#### ChatHistory

在许多问答应用程序中，我们希望允许用户进行来回对话，这意味着应用程序需要对过去的问题和答案进行某种“记忆”，以及将这些内容合并到当前思维中的一些逻辑。

相比前面我们需要更新两件事：

- **Prompt**： 更新我们的 prompt 以支持历史消息作为输入。
- **Contextualizing questions**：添加一个子链，该子链接受最新的用户问题并在聊天历史记录的上下文中重新表述它。例如，如果用户提出诸如“您能详细说明第二点吗？”之类的后续问题，则如果没有前一条消息的上下文，则无法理解该问题。因此我们无法对这样的问题进行有效的检索。



**Contextualizing questions**

首先，我们需要改写提问，使得context中包含历史信息。

我们将使用一个包含名为“chat_history”的 `MessagesPlaceholder` 变量的prompt。这允许我们使用“chat_history” 变量将消息列表传递到prompt，这些消息将插入到 **system message** 之后和包含最新问题的 **human message** 之前。

`create_history_aware_retriever` 构造一个接受键 `input` 和 `chat_history` 作为输入的chian，并具有与 retriever 相同的输出:

```python
from langchain.chains import create_history_aware_retriever
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder

contextualize_q_system_prompt = """Given a chat history and the latest user question \
which might reference context in the chat history, formulate a standalone question \
which can be understood without the chat history. Do NOT answer the question, \
just reformulate it if needed and otherwise return it as is."""
contextualize_q_prompt = ChatPromptTemplate.from_messages(
    [
        ("system", contextualize_q_system_prompt),
        MessagesPlaceholder("chat_history"),
        ("human", "{input}"),
    ]
)
history_aware_retriever = create_history_aware_retriever(
    llm, retriever, contextualize_q_prompt
)
```

**Chain with chat history**

这里我们使用 create_stuff_documents_chain 生成一个 `question_answer_chain` ，其中包含输入键 `context` 、 `chat_history` 和 `input` ——它接受检索到的上下文、对话历史记录以生成答案。

我们使用 create_retrieval_chain 构建最终的 `rag_chain` 。该链按顺序应用 `history_aware_retriever` 和 `question_answer_chain` ，为方便保留中间输出，例如检索到的context。它有输入键 `input` 和 `chat_history` ，并包括 `input` 、 `chat_history` 、 `context` 和 `answer` 在其输出中。



```mermaid
graph LR
    A[create_retrieval_chain] --> B1[create_history_aware_retriever]
    A --> B2[create_stuff_documents_chain]
    B1 --> C1[history]
    B1 --> C2[retriever]
    B2 --> C3[qa_prompt]
```



```python
from langchain.chains import create_retrieval_chain
from langchain.chains.combine_documents import create_stuff_documents_chain

qa_system_prompt = """You are an assistant for question-answering tasks. \
Use the following pieces of retrieved context to answer the question. \
If you don't know the answer, just say that you don't know. \
Use three sentences maximum and keep the answer concise.\

{context}"""
qa_prompt = ChatPromptTemplate.from_messages(
    [
        ("system", qa_system_prompt),
        MessagesPlaceholder("chat_history"),
        ("human", "{input}"),
    ]
)


question_answer_chain = create_stuff_documents_chain(llm, qa_prompt)

rag_chain = create_retrieval_chain(history_aware_retriever, question_answer_chain)
```



#### Streaming

大型语言模型可能需要几秒钟才能生成对查询的完整响应。这远远慢于应用程序对最终用户的响应速度约为 200-300 毫秒的阈值。

让应用程序感觉响应更快的关键策略是显示中间进度；即，按令牌流式传输模型令牌的输出(to stream the output from the model **token by token**.)。

参考：

- [rag streaming](https://python.langchain.com/v0.1/docs/use_cases/question_answering/streaming/)
- [expression_language streaming](https://python.langchain.com/v0.1/docs/expression_language/streaming/)



## 实践

### 使用 Langchain 构建TDesign知识库

参考：[微信-用ChatGPT搭建代码知识库，提升开发效率](https://mp.weixin.qq.com/s/MpF9xBHYjgnCHNkFn1AsOA)

关键词：

- Langchain Text Splitter工具：RecursiveCharacterTextSpliter
- prompt 中通过 few shots 优化

原理

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240510142250773.png" alt="image-20240510142250773" style="zoom:50%;" />

实现细节

1、**数据格式**

参考了 github 上面的 MrRanedeer 项目：[ https://github.com/JushBJJ/Mr.-Ranedeer-AI-Tutor](https://github.com/JushBJJ/Mr.-Ranedeer-AI-Tutor) ，借鉴他的知识描述方式和信息组织的格式，综合对比之后采用了 JSON 的数据格式；

数据结构如下：**组件 -> 使用场景 -> {场景描述 & 代码}。**

2、**数据向量化**

我们将使用常见的 LLM 对接工具 Langchain 和它的 Text Splitter 工具。具体来说，我们使用的是**RecursiveCharacterTextSpliter**，它能在尽可能保证句子语义完整的前提下根据 ChunkSize 进行分段。但是由于 chunkSize 的局限和知识文章长度的不确定导致很多时候，切片后语义的丢失。比如：

`{"小明的自我介绍": "大家好叫小明,我的爱好是足球和绘画"}`，如果文本在小明这里被截断，后续搜索"小明的介绍"，大概率不会将"小明"和后面的"我的爱好是足球和绘画"的信息匹配到一起，而导致在数据召回阶段没办法得到准确的知识。

关于这一点的优化我会在后面说明。

3、**数据检索**

这个阶段主要是通过提出的问题，搜索向量数据库中匹配的信息，与系统 prompt 整合之后传给 openai competition 完成知识检索。

4、**效果展示**

在 30 个常见的问题中，一共存在 7 个 bad case(错误答案，存在大量幻觉(hullucination))，3 个 not perfect(回答正确，但是有瑕疵，比如上传图片实现为上传文件)，其余回答正确。正确率 20/30 = 66.7%，可用率 23/30 = 76.7%。

原因分析：

1. 多维度知识匹配能力有限，比如同时检索 form,button,input,select 等组件组合的问题，由于 vectorStore.similaritySearch 过程中 topK 召回的数量有限，且 context 长度有限，会造成多维度知识检索的能力偏弱
2. 知识切片不连贯导致的上下文信息丢失，正如上文提到的小明的例子

`{"小明的自我介绍": "大家好叫小明,我的爱好是足球和绘画"}`，如果文本在小明这里被截断，后面的信息就丢失了"小明的自我介绍"的上下问信息，导致召回失败。

5、**方案优化**

1、针对上面提到两点影响因素，第一个方案可以通过优化 chunkSize 和 topK 的参数进行微调试错，但是总的来说当查询维度提升，所需的上下文信息也会相应增多，但这可能受到 LLM 的上下文长度限制的约束

2、针对第二点切片的导致的上下问信息丢失，笔者想出的方案是：通过 JS 解释器将文档信息转换成 Javascript AST(抽象语法树)，每次切片记录当前索引所在的 scope 信息，从而标记出当前切片的上下文信息。

通过学习 langchain 中 RecursiveCharacterTextSpliter 的源码，我们是可以通过 indexChunk 的值得到每次切片时的索引。

如果套用到前文提到的小明的例子的话，第二段"我的爱好是足球和绘画"的 scope 信息就是 {startScope: "小明的自我介绍",endScope: ""},如果我们通过特定格式将他拼接到知识信息中去就会是：

```
> > > startScopeStr:"小明的自我介绍"<<<,我的爱好是足球和绘画>>>endScopeStr:""<<<
```

现在如果使用"小明的兴趣爱好"来匹配并召回 embedding 片段，并喂给 LLM，就能得到准确的答案了。

最后可以在 prompt 中通过 few shots 进一步优化匹配，到此为止优化流程就完成了。

`````
Now, you will act as a senior frontend engineer and you are a expert in JS coding and programing. And you will help people study design, tdesign is a vue3 ui framework, it has nothing to do with other framework like elementUl, you will tell people how to use it, If you don't know the answer, just say you don't know. DO NOT try to make up an answer.Answer it in Chinese

-----
{{{context}}}
-----

you should keep in mind that "startScopeStr" and "endScopeStr" is the mark where the origin document is chunked, here is a example:

````
>>>startScopeStr: "animal>fish1" <<<;console.log>>>endScopeStr:
animal>bird2<<‹
>>>startScopeStr: animal> bird2});('i like pizza');«{endScopeStr:
animal> human3<<‹
the first line endScopeStr(animal> bird2) is next to the second line startScopeStr(animal>bird3) ,so it used to be a complete expression:
console.log ('i like pizza"');
````
`````





### build private chatbot with langchain

参考：[Building a Q&A chatbot on private data](https://github.com/Ammar2k/private_qa_bot)

关键词：

- streamlit 
- langchain
- chroma

运行：

```
pip install -r requirements.txt
streamlit run app.py
```



## 参考

- [Prompt Engineering Guide](https://www.promptingguide.ai/)