## 智能体

一个智能体应具备类似人类的思考和规划能力，拥有记忆甚至情感，并具备一定的技能以便与环境、智能体和人类进行交互。

可以将智能体想象成环境中的数字人，其中

> 智能体 = 大语言模型（LLM） + 观察 + 思考 + 行动 + 记忆



这个公式概括了智能体的功能本质。为了理解每个组成部分，让我们将其与人类进行类比：

1. 大语言模型（LLM）：LLM作为智能体的“大脑”部分，使其能够处理信息，从交互中学习，做出决策并执行行动。
2. 观察(Perception)：这是智能体的感知机制，使其能够感知其环境。智能体可能会接收来自另一个智能体的文本消息、来自监视摄像头的视觉数据或来自客户服务录音的音频等一系列信号。这些观察构成了所有后续行动的基础。
3. 思考(Brain)：思考过程涉及分析观察结果和记忆内容并考虑可能的行动。这是智能体内部的决策过程，其可能由LLM进行驱动。
4. 行动(Action)：这些是智能体对其思考和观察的显式响应。行动可以是利用 LLM 生成代码，或是手动预定义的操作，如阅读本地文件。此外，智能体还可以执行使用工具的操作，包括在互联网上搜索天气，使用计算器进行数学计算等。
5. 记忆：智能体的记忆存储过去的经验。这对学习至关重要，因为它允许智能体参考先前的结果并据此调整未来的行动。

一张经典图：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240509164100472.png" alt="image-20240509164100472" style="zoom:50%;" />

## 多智能体

但对于更复杂的场景，例如复杂代码的开发，单一功能的LLM Agent显然难以胜任。因此，社区开始发展出多Agent的组合玩法，比如:

- [MetaGPT](https://docs.deepwisdom.ai/main/zh/guide/tutorials/concepts.html): MetaGPT内部包括**产品经理 / 架构师 / 项目经理 / 工程师**，它提供了一个**软件公司**的全过程与精心调配的SOP
- GPT-Engineer
- chatDev
- AutoGen
- muAgent : 蚂蚁开源的可扩展、易于使用的Multi-Agent框架

多智能体系统可以视为一个智能体社会，其中

> 多智能体 = 智能体 + 环境 + 标准流程（SOP） + 通信 + 经济

这些组件各自发挥着重要的作用：

1. 智能体：在上面单独定义的基础上，在多智能体系统中的智能体协同工作，每个智能体都具备独特有的LLM、观察、思考、行动和记忆。
2. 环境：环境是智能体生存和互动的公共场所。智能体从环境中观察到重要信息，并发布行动的输出结果以供其他智能体使用。
3. 标准流程（SOP）：这些是管理智能体行动和交互的既定程序，确保系统内部的有序和高效运作。例如，在汽车制造的SOP中，一个智能体焊接汽车零件，而另一个安装电缆，保持装配线的有序运作。
4. 通信：通信是智能体之间信息交流的过程。它对于系统内的协作、谈判和竞争至关重要。
5. 经济：这指的是多智能体环境中的价值交换系统，决定资源分配和任务优先级。



这是一个简单的例子，展示了智能体如何工作：

![image-20240509171630320](/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240509171630320.png)

- 在环境中，存在三个智能体Alice、Bob和Charlie，它们相互作用。
- 他们可以将消息或行动的输出结果发布到环境中，同时也会被其他智能体观察到。
- 下面将揭示智能体Charlie的内部过程，该过程同样适用于Alice和Bob。
- 在内部，智能体Charlie具备我们上述所介绍的部分组件，如LLM、观察、思考、行动。Charlie思考和行动的过程可以由LLM驱动，并且还能在行动的过程中使用工具。
- Charlie观察来自Alice的相关文件和来自Bob的需求，获取有帮助的记忆，思考如何编写代码，执行写代码的行动，最终发布结果。
- Charlie通过将结果发布到环境中以通知Bob。Bob在接收后回复了一句赞美的话。

## MetaGPT

下面为 [software startup example](https://github.com/geekan/MetaGPT/blob/main/metagpt/software_company.py) 的节选。如果你使用`git clone`方法进行安装，只需简单执行

```
metagpt "write a cli blackjack game"
```





## SWE-Agent

[SWE-agent](https://github.com/princeton-nlp/SWE-agent) 通过智能体-计算机接口（Agent-Computer Interface, ACI）与代码库进行交互，执行代码的浏览、编辑、测试和执行等任务。

 主要工作流程包括以下几个步骤：

1. **理解问题**：SWE-agent 使用自然语言处理技术理解 GitHub 存储库中的问题描述，依赖于其内部集成的大型语言模型。
2. **智能体-计算机接口（ACI）**：SWE-agent 使用 ACI 与代码库进行交互，通过 ACI 可以浏览代码库、搜索文件、查看和编辑代码，甚至执行代码。
3. **代码分析与修复**：理解问题后，SWE-agent 分析相关代码，定位可能的错误或漏洞，并生成修复方案。
4. **自动化测试**：SWE-agent 能够自动编写和执行测试用例，验证代码更改是否解决了原始问题，并且没有引入新的错误。
5. **性能反馈**：SWE-agent 执行的每一步操作都会产生反馈，这些反馈用于评估其工作的效果。
6. **迭代与优化**：SWE-agent 设计允许不断的迭代和优化，研究团队通过收集使用中的反馈和性能数据，不断改进 ACI 设计。



## CodeFuse

CodeFuse是蚂蚁开源的CodeLLM，为软件开发全流程提供AI支持：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240510104841764.png" alt="image-20240510104841764" style="zoom:50%;" /> 

 开源了几个核心产品：

- [Test-Agent](https://github.com/codefuse-ai/Test-Agent)：智能测试助理
- [CodeFuse-ChatBot](https://github.com/codefuse-ai/codefuse-chatbot)：智能助手
- [CodeFuse-MuAgent](https://github.com/codefuse-ai/CodeFuse-muAgent)：: Multi-Agent 框架

### CodeFuse-MuAgent

蚂蚁开源可扩展、易于使用的[Multi-Agent](https://github.com/codefuse-ai/CodeFuse-muAgent)框架:

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240510103157981.png" alt="image-20240510103157981" style="zoom:50%;" />

主要构成

- Agent Base： 提供智能体提供任务的执行
- Communication： 通过Message和Parse Message 实体完成Agent间的信息传递
- Context Manager：负责管理Context，包括用户的Input、Prompt、历史记录等等
- Component：用于构建Agent的辅助生态组件

在Agent层面，提供四种基本的Agent类型支撑各种场景的基础活动，所有的Action都由Agent执行：

- BaseAgent：提供基础问答、工具使用、代码执行的功能，根据Prompt格式实现 输入 => 输出
- ReactAgent：提供标准React的功能，根据问题实现当前任务
- ExecutorAgent：对任务清单进行顺序执行，根据 User 或 上一个Agent编排的计划，完成相关任务 Agent接受到任务清单([List[task])，对这个任务清单Task进行循环执行（中间也可添加 Feedback Agent来进行任务重新优化），直到任务完成
- SelectorAgent：提供选择Agent的功能，根据User 或 上一个 Agent的问题选择合适的Agent来进行回答.

Communication

为了让Agent之间进行更好的交互，以及能够让每一个Agent接受到足够的信息完成它们特定任务，我们将Message信息体分成了多个部分:

- System Content：用于存储管理当前LLM输出的时间，Role信息等
- Info Content：LLM辅助信息，比如像知识库查询信息、代码库检索信息、工具信息、Agent信息等
- LLM Content：直接存储和传递LLM 产生的信息

Context Manager

**Prompt Manager – 大脑核心**

muAgent将Prompt Manager模块中分为 System Prompt、Context Prompt、Customized Prompt三部分:

- System Prompt 包括 Role Name、Role Description、Task等，即希望模型执行的特定任务。
- Context Prompt 包括 Doc Context、Code Context、Tool Context、Agent Context、Session Context等，即希望模型理解的请求所需的背景信息。
- Customized Prompt 则是 自定义的一些 Input 和 Ouput，即模型需要处理的数据和期望的输出类型或格式的信号。



参考：

- [muAgent-概览](https://github.com/codefuse-ai/CodeFuse-muAgent/blob/main/docs/overview/o1.muagent.md)
- [muAgent 编排](https://github.com/codefuse-ai/CodeFuse-muAgent/blob/main/docs/overview/o2.agent-flow.md)
- [quick-start](https://github.com/codefuse-ai/CodeFuse-muAgent/blob/main/docs/overview/o3.quick-start.md)
- [多Agent框架MuAgent带你解锁代码开发新姿势](https://mp.weixin.qq.com/s/pNTXJfms1X7K1NS3YuaQMA)

### CodeFuse-ChatBot

[CodeFuse-ChatBot](https://github.com/codefuse-ai/codefuse-chatbot) 基于Codefuse-MuAgent开发的开源AI智能助手。

架构如下：

<img src="/System/Volumes/Data/work/dist/branch/wacai/middleware/my-boot/doc/images/all/image-20240510104135283.png" alt="image-20240510104135283" style="zoom:50%;" />

- **Multi-Agent Schedule Core:** 多智能体调度核心，简易配置即可打造交互式智能体。
- 🕷️ **Multi Source Web Crawl:** 多源网络爬虫，提供对指定 URL 的爬取功能，以搜集所需信息。
- 🗂️ **Data Processor:** 数据处理器，轻松完成文档载入、数据清洗，及文本切分，整合不同来源的数据。
- 🔤 **Text Embedding & Index:**：文本嵌入索引，用户可以轻松上传文件进行文档检索，优化文档分析过程。
- 🗄️ **Vector Database & Graph Database:** 向量与图数据库，提供灵活强大的数据管理解决方案。
- 📝 **Prompt Control & Management:**：Prompt 控制与管理，精确定义智能体的上下文环境。
- 🚧 **SandBox:**：沙盒环境，安全地执行代码编译和动作。
- 💬 **LLM:**：智能体大脑，支持多种开源模型和 LLM 接口。
- 🛠️ **API Management:：** API 管理工具，实现对开源组件和运维平台的快速集成。

### 本地启动

local_config.json需要修改

```
  "embedding_model_dict": {
    "openai": "GanymedeNil/text2vec-large-chinese"
  },
```

需要魔改muagent/llm_model/openai_model 使用AzureChatOpenAI：

```
        model = AzureChatOpenAI(
                streaming=True,
                verbose=True,
                openai_api_key=llm_config.api_key,
                azure_endpoint=llm_config.api_base_url,
                openai_api_version="2023-03-15-preview",
                model_name=llm_config.model_name,
                temperature=llm_config.temperature,
                stop=llm_config.stop
            )
```

### 知识库管理

#### 新建知识库

调用路径：

```
document.py#L133 ->  webui/utils.py#ApiRequest.create_knowledge_base() -> muagent/service/kb_api.py#create_kb()
```

#### 上传文件

调用路径：

```
document.py#L154 -> webui/utils.py#ApiRequest.upload_kb_doc() -> muagent/service/kb_api.py#upload_doc() -> muagent/service/faiss_db_service.py#do_add_doc()
```

#### 新建代码知识库

调用路径：

```
code.py#L110 -> webui/utils.py#ApiRequest.create_code_base() -> muagent/service/cb_api.py#create_cb()
```

cb_api.py#create_cb() 代码如下:

```python

def create_cb()：
	cbh = CodeBaseHandler(cb_name, code_path, embed_config=embed_config, llm_config=llm_config)
	vertices_num, edge_num, file_num = cbh.import_code(zip_file=zip_file, do_interpret=do_interpret) //①
	add_cb_to_db(cb_name, cbh.code_path, vertices_num, file_num, do_interpret) //②

```
核心逻辑都委托给CodeBaseHandler.import_code()进行处理:

```python

def import_code(self, zip_file='', do_interpret=True):
	 code_dict = self.crawl_code(zip_file) //①
	 static_analysis_res, interpretation = code_analyzer.analyze(code_dict, do_interpret=do_interpret) //②
	 code_importer.import_code(static_analysis_res, interpretation, do_interpret=do_interpret) //③

```

基本流程： 
1. 解压zip文件，读取每个java文件。 详见CodeBaseHandler.crawl_code()
2. 使用python javalang 分析java原理，产生AST，记录类名、方法名。详见CodeAnalyzer.analyze()
3. 如果有使用NebulaGraph，会保存到知识图谱，否则会把 static_analysis_res 作为json序列化保存到本地，同时如果开启了代码解释也会embeddings保存到chromadb向量库。

具体说下如何分析java代码：

1、 JavaStaticAnalysis.analyze()


```python
 def analyze(self, java_code_dict):
     
        tree_dict = self.preparse(java_code_dict)
        res = self.multi_java_code_parse(tree_dict)
        return res

```

主要流程：

1. 参数中 java_code_dict 是key为文件路径，value是源码的dict。
2. preparse()会通过javalang转化为 key为java源码，value为 tree对象的tree_dict。
3. multi_java_code_parse()会迭代解析每个java

2、 JavaStaticAnalysis.multi_java_code_parse()


```python

def multi_java_code_parse(self, tree_dict):

	res_dict = {}
    for fp, value in tree_dict.items():
    	#解析单个java文件
    	res_dict[java_code] = self.single_java_code_parse(tree, fp)
	return res_dict
```


3、JavaStaticAnalysis.single_java_code_parse()

该方法主要把源码解析为如下格式：

```python
res = {
    'pac_name': pac_name, #包名
    'class_name_list': class_name_list, #类名，可能有内部类，所以是多个
    'func_name_dict': func_name_dict, #方法名
    'import_pac_name_list': import_pac_name_list #导入类名
}
```

### 问答

问答分为几种模式：
- LLM问答
- 知识库问答
- 代码知问答
- 搜索引擎问答
- Agent问答

和liangchain类似，不过做了封装 ，不同问答对应了不同实现：
- base_chat.py
- llm_chat.py
- knowledge_chat.py
- code_chat.py
- search_chat.py
- agent_chat.py

所有问答都会调用XXXChat对象.chat(), 基类定义在 base_chat.py#Chat.chat() 代码如下:

```python 
  def chat(self):
    	llm_config: LLMConfig = LLMConfig(**params) //①
    	return self._chat(query, history, llm_config, embed_config, **kargs) 

	def _chat(self):
		service_status = self.check_service_status() //②
		
		def chat_iterator(query: str, history: List[History]):
			model = getChatModelFromConfig(llm_config) //③
			result, content = self.create_task(query, history, model, llm_config, embed_config, **kargs) //④

            if self.stream:
                for token in content["text"]:
                    result["answer"] = token
                    yield json.dumps(result, ensure_ascii=False)

		return StreamingResponse(chat_iterator(query, history),
                                     media_type="text/event-stream")

```

base_chat.py#Chat.chat() 定义了模板方法，处理流程如下：

1. 把参数组装为LLMConfig对象
2. 通过 check_service_status() 方法check状态，比如knowledge_chat会检查知识库是否存在
3. 初始化chatModel
4. 模板方法 create_task() 构建 llm 生成任务，不同子类覆写

#### LLM问答

调用过程：

```
dialogue.py#L298 -> webui/utils.py#ApiRequest.chat_chat() -> muagent/chat/llm_chat.py#LLMChat.chat() -> muagent/chat/base_chat.py#Chat.chat() -> _chat()

```

对于 LLMChat的create_task() 实现：

```python

def create_task(self):
    '''构建 llm 生成任务'''

    chat_prompt = ChatPromptTemplate.from_messages(
        [i.to_msg_tuple() for i in history] + [("human", "{input}")]
    )

    chain = LLMChain(prompt=chat_prompt, llm=model) #组合为链
    content = chain({"input": query})
    return {"answer": "", "docs": ""}, content	

```

代码非常简单。


#### 知识库问答

调用过程：

```
dialogue.py#L402 -> webui/utils.py#ApiRequest.knowledge_base_chat() -> muagent/chat/knowledge_chat.py#KnowledgeChat.chat() -> muagent/chat/base_chat.py#Chat.chat()
```

对于 KnowledgeChat的create_task() 会调用 _process()方法创建chain

```python
def _process(self, query: str):
	docs = search_docs(str) #①

	source_documents = []
	for inum, doc in enumerate(docs):
		filename = os.path.split(doc.metadata["source"])[-1]
		text = f"""出处 [{inum + 1}] [{filename}]({url}) \n\n{doc.page_content}\n\n""" #②
		source_documents.append(text)

    chat_prompt = ChatPromptTemplate.from_messages(
        [i.to_msg_tuple() for i in history] + [("human", ORIGIN_TEMPLATE_PROMPT)] #③
    )
    chain = LLMChain(prompt=chat_prompt, llm=model) #④
    result = {"answer": "", "docs": source_documents}
    return chain, context, result

```
主要流程： 
1. 通过search_docs()查询prompt对应的docs
2. 迭代docs，记录文档出处
3. 创建ChatPromptTemplate
4. 组合成chain

## Code Agent

### Aider

- [aider](https://github.com/paul-gauthier/aider)： aider uses [tree sitter](https://tree-sitter.github.io/tree-sitter/) to build the map. It specifically uses the [py-tree-sitter-languages](https://github.com/grantjenks/py-tree-sitter-languages) python module, which provides simple, pip-installable binary wheels for [most popular programming languages](https://github.com/paul-gauthier/grep-ast/blob/main/grep_ast/parsers.py). 。可以参考[aider-Building a better repository map with tree sitter](https://aider.chat/2023/10/22/repomap.html) 这篇文章，写的非常好。
- [plandex](https://github.com/plandex-ai/plandex)
- [OpenDevin](https://github.com/OpenDevin/OpenDevin)
- [Create Aider Agent](https://github.com/OpenDevin/OpenDevin/issues/120)
- [tabby](https://tabby.tabbyml.com/) AI coding assistant

aider摘要：

> GPT-4 非常适合“自包含”编码任务，例如编写或修改没有外部依赖项的纯函数。 GPT 可以轻松处理诸如“编写斐波那契函数”或“使用列表推导式重写此循环”之类的请求，因为它们不需要所讨论的代码之外的上下文。
>
> 大多数真实的代码不是纯粹的和独立的，它与存储库中许多不同文件的代码交织在一起并依赖于这些代码。如果你要求GPT“将Foo类中的所有打印语句切换为使用BarLog日志系统”，它需要查看并修改Foo类中的代码，但它也需要了解如何使用项目的BarLog子系统。
>
> 一个简单的解决方案是将整个代码库连同每个更改请求一起发送到 GPT。现在 GPT 拥有所有上下文！但这对于中等大小的存储库也不起作用，因为它们不适合上下文窗口。
>
> aider 通过构建一个code map来解决 code context过量的问题



### code-review-gpt

Prompt:

> You are a experienced Java programmer, your job is to review others Java code, and focus on the rules below only, you don’t need to review other aspect.
>
> Do the work step by step.
>
> 1. Understand the code structure , find the java methods annotation and its input if exist.
> 2. If the found annotation in step 1 does not includes Spring Web module like @PostMapping @GetMapping and similar, ingore all following steps and finsih the review by saying no violation.
> 3. If the found annotation in step 1 does not includes Spring Web module like @PostMapping @GetMapping and similar , ingore this and following steps and finsih the review by saying no violation. , If the found annotation in step 1 includes Spring Web module like @PostMapping @GetMapping and similar , check if the methods have input variable.
> 4. If the found annotation in step 1 does not includes Spring Web module like @PostMapping @GetMapping and similar , ingore this and following steps and finsih the review by saying no violation. , In the condition of the Spring Web module like @PostMapping @GetMapping or similar in the annotation, and the methods have input variable, check the method body, it is required to have logging of the passed in values.
> 5. If the found annotation in step 1 does not includes Spring Web module like @PostMapping @GetMapping and similar , ingore this and following steps and finsih the review by saying no violation. , If the Spring Web module like @PostMapping @GetMapping or similar in the annotation exist, and the methods have input variable, and If the logging is missed, it violate the rule.
> 6. Give final summary based on above steps with words Summary.
>
> Let me know your work above step by step.
>
> Do not modify or correct the original code, finish the review by adding <end-fo-code> in the end.



Prompt:

> You are a senior developer tasked with reviewing the provided code patch. Your review should identify and categorize issues, highlighting potential bugs, suggesting performance optimizations, and flag security issues. Please be aware there maybe libraries or technologies present which you do not know. Format the review output as valid JSON. Each identified issue should be an object in an array, with each object including the following fields: 'category', 'description', 'suggestedCode', and 'codeSnippet'. The category should be one of 'Bugs', 'Performance', 'Security' or 'Style'. The suggestedCode should be an empty string if the recommendation is general or you do not have any code to fix the problem, otherwise return the suggested code to fix the problem. Make sure to escape any special characters in the suggestedCode and in the problematic codeSnippet. Output format: [{"category": "Bugs", "description": "<Describe the problem with the code>", "suggestedCode": "<Insert a code suggestion in the same language as the patch which fixes the issue>", "codeSnippet": "<Insert the problematic code from the patch>"}]. Return the array nothing else.

参考：

- [code-review-gpt](https://github.com/mattzcarey/code-review-gpt)

### Auto-Coder

 Github Copilot 的产品本质决定了他只是一个更加smart的提示工具，而不是模拟人去编程。这个虽然说不上逆AGI潮流，但确实不够 AI Native, 没有把AI 充分利用起来。

人类单纯编程部分，无非是

1. 理解需求
2. 搜索看别人怎么解决类似问题，理清思路
3. 看已有项目的代码
4. 看要用到的第三方库的源码或者文档

AutoCoder 会在阅读已有代码，参考文档，自动进行搜索引擎获取相关信息，最后尝试去理解程序员的需求，有了这些信息后，才最后进行生成代码，最终 能够生成足够好的代码。

### MarsCode

[豆包 marscode](https://www.marscode.cn/workbench)是字节的辅助编程，可以作为Github Copilot的平替

### Continue

[Continue](https://github.com/continuedev/continue) is the leading open-source AI code assistant. You can connect any models and any context to build custom autocomplete and chat experiences inside VS Code and JetBrains

### Tabby

应该是和Continue类似的。 Opensource, self-hosted AI coding assistant 

## Cursor

### 快捷键

```
Command+i 显示composer
Command+k 
Command+Shift+P 显示命令
Command+Alt+B 	Go to implementation

```

参考： https://marketplace.visualstudio.com/items?itemName=k--kato.intellij-idea-keybindings

### 技巧

1、让cursor 记录 readme文档
2、使用 git 管理版本
3、常用@codebase 避免幻觉
4、添加常用文档docs，聊天过程中可以使用@参考文档连接
5、设置 Rules for AI，比如记录 readme文档
6、可以delete 原账号，重新生成
7、可以为项目单独设置.cursorrules


### Rules for A

```
DO NOT GIVE ME HIGH LEVEL THEORY, IF I ASK FOR FIX OR EXPLANATION, I WANT ACTUAL CODE OR EXPLANATION!!! I DON'T WANT "Here's how you can blablabla"
- Do not delete existing comments in the code
- Be casual unless otherwise specified
- Be terse and concise
- Suggest solutions that I didn't think about—anticipate my needs
- Treat me as an expert
- Be accurate and thorough
- Focus on readability over being performant.
- Give the answer immediately. Provide detailed explanations and restate my query in your own words if necessary after giving the answer
- Split into multiple responses if one response isn't enough to answer the question
```



````
- 为项目自动产生README.md文档，README.md文档首先简要说明项目功能，然后按照下面格式进行描述
```
## 功能特点
## 使用方法
## 技术栈
## 项目结构
## 开发计划
```
- 每次变更自动修改 README.md文档内容
````



### 案例

#### 1、浏览器插件-页面二维码生成

新开发

```
请帮我创建在chrome浏览器上，打开任意网页时，右下角展示当前网站二维码的插件。展示的这个二维码应该有以下几个特性：
1. 二维码大小为256*256
2. 二维码的中间件部分获取展示当前网站的logo
3. 二维码的下方，分两行分别展示网站名词和网页标题，网页标题要加粗，最多展示15个字

```

bug:

```
在开发 chrome 插件过程中，为什么在 content.js中无法获取到qrcode.in.js中定义的QRCode对象，一直报错：content.js:19 Uncaught (in promise) ReferenceError: QRCode is not defined
    at createAndShowQRCode (content.js:19:14)
    at content.js:61:3
```

优化1：

```
我希望再做一个优化，默认情况下不打开二维码 ，而是只在右下角显示logo，当用户点击时才产生二维码

```

优化2：

```
点击按钮产生二维码需要一点时间，这段时间弹出的窗口给出文案提示：“正在产生二维码”，成功产生之后再替换为二维码
```



#### 2、后端页面

新开发

```
请帮我创建一个管理elasticsearch的系统，采用Ant Design Value 开发。左侧菜单栏包括一个集群管理页面，集群管理页面是一个列表页面，包含集群名称，健康状态，创建时间，数据展示mock返回一些静态假数据。
- 请添加相应应的package.json，确保我能启动整个项目
- 请添加README.md 记录功能和技术栈
- 请确保文件完整项目能成功启动
```



优化1：

```
很好，现在可以正常工作了，但是现在缺少左侧导航栏，模仿这个UI界面增加导航栏
```

优化2：

```
记录 version 1.0 改动点，添加到README.md
```

优化3：

```
我们开发现在version2.0 ，需求：新增一个仪表盘页面，页面类似上传的图片，折线图可以先采用mock数据
```

#### 3、TSDB

优化

```
@Codebase 重构Shard类，把TSM文件和TSI文件写入逻辑从Shard类中抽取出来另外创建一个类，并且使用FileChannel写入数据，而不要使用FileOutputstream
```

