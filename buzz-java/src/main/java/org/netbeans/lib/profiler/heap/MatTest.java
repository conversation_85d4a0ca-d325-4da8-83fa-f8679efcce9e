package org.netbeans.lib.profiler.heap;

import lombok.extern.slf4j.Slf4j;
import org.junit.Before;
import org.junit.Test;
import org.netbeans.lib.profiler.heap.Heap;
import org.netbeans.lib.profiler.heap.HeapFactory2;
import org.netbeans.modules.profiler.oql.engine.api.OQLEngine;
import org.netbeans.modules.profiler.oql.engine.api.OQLEngine.ObjectVisitor;
import org.netbeans.modules.profiler.oql.engine.api.OQLException;
import org.netbeans.lib.profiler.heap.InstanceDump;

import java.io.File;
import java.io.IOException;

/**
 * eclipse mat 官方并没有release 一个简单的包到maven仓库。 eclipse jifa 采用osgi的方式使用 mat，比较麻烦。
 * 可以考虑使用 heaplib 来分析内存
 */
@Slf4j
public class MatTest {


    Heap heap;

    @Before
    public void initHeapDump() throws IOException {
        String dumpPath = "/Users/<USER>/Downloads/oom/oom.bin"; // path to dump file
        log.info("load dump file");
        heap = HeapFactory.createHeap(new File(dumpPath));// createFastHeap(new File(dumpPath));
    }


    /**
     * https://github.com/aragozin/heaplib/blob/master/hprof-oql-engine/src/test/java/org/netbeans/modules/profiler/oql/engine/api/impl/OQLEngineTest.java
     *
     * @throws IOException
     */
    @Test
    public void testOQL() throws IOException, OQLException {
        log.info("execute oql");
        OQLEngine instance = new OQLEngine(heap);
        instance.executeQuery("select s from org.elasticsearch.index.IndexService s",
                (i) -> {
                    System.out.println("result="+i);
                    if( i instanceof  InstanceDump){
                        InstanceDump instanceDump = (InstanceDump) i;
                        System.out.println(instanceDump.getInstanceNumber());
                        System.out.println(instanceDump.getRetainedSize());
                        System.out.println(instanceDump.getReachableSize());
                        System.out.println(instanceDump.getSize());
                    }
                    return true;
                });
    }
}
