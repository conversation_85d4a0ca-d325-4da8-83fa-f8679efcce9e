package com.buzz.aggregate.series;

import com.buzz.aggregate.kv.KeyValue;
import com.buzz.aggregate.tuple.Tuple;
import lombok.AllArgsConstructor;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 **/
public class MeasureMap {

    private final String[] groupKeys;

    private final String[] filedKeys;

    private Aggregator aggregator;

    private RevertIndex revertIndex;

    public MeasureMap(String[] groupKeys, String[] filedKeys) {
        this.groupKeys = groupKeys;
        this.filedKeys = filedKeys;
        this.aggregator = createAggregator(groupKeys, filedKeys);
        this.revertIndex = new RevertIndex(groupKeys);

    }

    private Aggregator createAggregator(String[] groupKeys, String[] aggregationKeys) {
        Aggregator aggregator = new Aggregator(groupKeys);
        for (String aggregationKey : aggregationKeys) {
            aggregator.addAggregation(new Aggregator.Aggregation(aggregationKey));
        }
        return aggregator;
    }


    public void put(KeyValue incomingValue) {
        //write data
        Tuple groupValue = aggregator.aggregate(incomingValue);
        if (groupValue == null) {
            return;
        }
        //write index
        revertIndex.write(incomingValue, groupValue);
    }

    public List<KeyValue> get(String tag, Pair condition) {
        List<KeyValue> keyValueList = new ArrayList<>();
        if (condition != null) {
            //拿到tagKeySet
            Set<Tuple> tagKeySet = revertIndex.read(condition.getVal1(), condition.getVal2());
            for (Tuple tagKey : tagKeySet) {
                KeyValue keyValue = aggregator.getResult(tagKey);
                keyValueList.add(keyValue);
            }
        } else {
            keyValueList = aggregator.getResult();
        }
        Aggregator localAggregator = new Aggregator(new String[]{tag});
        localAggregator.setAggregationList(aggregator.getAggregationList());
        for (KeyValue keyValue : keyValueList) {
            localAggregator.aggregate(keyValue);
        }
        try {
            return localAggregator.getResult();
        } finally {
            localAggregator.clean();
        }
    }

    public Aggregator getAggregator() {
        return aggregator;
    }

    @Data
    @AllArgsConstructor
    public static class Pair {
        private String val1;
        private String val2;
    }
}