package com.buzz.aggregate.series;

import com.buzz.aggregate.kv.KeyValue;
import com.buzz.aggregate.tuple.Tuple;

import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description
 **/
public class RevertIndex {

    //private Map<String/*tagKey*/, Map<String/*tagValue*/, Set<Tuple>/*groupKey*/>> innerMap = new ConcurrentHashMap<>();

    private String[] groupKeys;

    private Map<String/*tagValue*/, Set<Tuple>/*groupKey*/>[] innerMaps;

    public RevertIndex(String[] groupKeys) {
        this.groupKeys = groupKeys;
        innerMaps = new ConcurrentHashMap[groupKeys.length];
        for (int i = 0; i < groupKeys.length; ++i) {
            innerMaps[i] = new ConcurrentHashMap<>();
        }
    }


    private int getKeySlot(String key) {
        return Math.abs(key.hashCode()) % groupKeys.length;
    }

    public void write(KeyValue incomingValue, Tuple groupValue) {
        for (String groupKey : groupKeys) {
            int slot = getKeySlot(groupKey);
            Set<Tuple> groupValues = innerMaps[slot].computeIfAbsent(incomingValue.getString(groupKey), key -> new HashSet<>());
            groupValues.add(groupValue);
        }
    }

    public Set<Tuple> read(String tagKey, String tagValue) {
        int slot = getKeySlot(tagKey);
        return innerMaps[slot].get(tagValue);
    }


}