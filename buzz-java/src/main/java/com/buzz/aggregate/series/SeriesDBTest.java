package com.buzz.aggregate.series;

import com.alibaba.fastjson.JSON;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.junit.Test;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 * @description
 **/
public class SeriesDBTest {

    @Data
    @AllArgsConstructor
    static class SeqData {
        KeyValue tags;
        Map<String, Long> values;
    }


    private static SeqData getSeqData(String ip, String idc) {
        KeyValue tags = KeyValue.create();
        tags.put("ip", ip);
        tags.put("idc", idc);
        Map<String, Long> values = new HashMap<>();
        values.put("count", 1l);

        return new SeqData(tags, values);
    }

    @Test
    public void test1() {
        KVTable table = new KVTable();
        table.write(getSeqData("172.2", "hj"));
        table.write(getSeqData("172.3", "hj"));
        table.write(getSeqData("172.4", "hj"));
        table.write(getSeqData("10.2", "qsh"));

        System.out.println(JSON.toJSONString(table.get(new Tag("idc", "hj"))));
        System.out.println(JSON.toJSONString(table.get(new Tag("idc", "qsh"))));

    }

    @Data
    @AllArgsConstructor
    private final class Tag {
        private String key;
        private String value;
    }

    @Data
    @AllArgsConstructor
    private final class Field {
        private String key;
        private Long value;

    }

    private static class KeyValue extends LinkedHashMap<String, String> {
        public static KeyValue create() {
            return new KeyValue();
        }
    }

    @Data
    private final class KVTable {
        private static final String KEY_SEPARATOR = "|"; //"\001";
        private Map<String/*seriesKey*/, Fields> dataMap = new ConcurrentHashMap();
        private Map<String/*tagKey*/, Map<String/*tagValue*/, Set<String>/*seriesKey*/>> invertedIndex = new ConcurrentHashMap<>();

        public void write(SeqData seqData) {
            write(seqData.getTags(), seqData.getValues());
        }

        public void write(Map<String/*tagKey*/, String /*tagVal*/> tags, Map<String, Long> values) {
            String seriesKey = buildSeriesKey(tags);//tag的value作为seriesKey
            System.out.println("seriesKey="+seriesKey);


            //write index
            for (String tagKey : tags.keySet()) {
                String tagValue = tags.get(tagKey);
                Map<String, Set<String>> subMap = invertedIndex.computeIfAbsent(tagKey, key -> new ConcurrentHashMap<>());
                Set<String> seriesKeySet = subMap.computeIfAbsent(tagValue, key -> new HashSet());
                seriesKeySet.add(seriesKey);
            }

            //write data
            Fields value = dataMap.computeIfAbsent(seriesKey, key -> new Fields());
            value.sum(values);
        }

        public Fields get(Tag tag) {
            Map<String/*tagValue*/, Set<String>/*seriesKey*/> subMap = invertedIndex.get(tag.getKey());
            if (subMap.isEmpty()) {
                throw new RuntimeException("no data");
            }
            Set<String> seriesKeySet = subMap.get(tag.getValue());
            Fields accumulator = null;
            for (String seriesKey : seriesKeySet) {
                Fields values = dataMap.get(seriesKey);
                if (accumulator == null) {
                    accumulator = values;
                } else {
                    accumulator.sum(values.getValues());
                }
            }
            return accumulator;
        }

        protected String buildSeriesKey(Map<String, String> labels) {
            return String.join(KEY_SEPARATOR, labels.values());
        }
    }


    @Data
    private static class Fields {
        private Map<String, Long> values = new HashMap<>();

        public Fields() {
        }

        public void sum(Map<String, Long> newValue) {
            for (String key : newValue.keySet()) {
                Long value = values.computeIfAbsent(key, k -> 0l);
                value += newValue.get(key);
                values.put(key, value);
            }
        }
    }
}
