package com.buzz.aggregate.series;

import com.buzz.aggregate.kv.KeyValue;
import com.buzz.aggregate.tuple.Tuple;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @description
 **/
public class Aggregator {

    private static final int MAX_SIZE = 50000;

    private Map<Tuple, Tuple> innerMap = new ConcurrentHashMap<>();

    private String[] groupKeys;

    private List<Aggregation> aggregationList = new ArrayList<>();

    public Aggregator(String[] groupKeys) {
        this.groupKeys = groupKeys;
    }

    public int size() {
        return innerMap.size();
    }

    public Tuple aggregate(KeyValue incomingValue) {
        if (innerMap.size() > MAX_SIZE) {
            return null;
        }
        Tuple groupValue = createKey(incomingValue);
        Tuple values = innerMap.computeIfAbsent(groupValue, key -> initValues(incomingValue));

        for (int i = 0; i < aggregationList.size(); ++i) {
            Aggregation aggregation = aggregationList.get(i);
            aggregation.aggregate(values.getValueAt(i), incomingValue);
        }
        return groupValue;
    }

    private Tuple createKey(KeyValue incomingValue) {
        Object[] values = new Object[groupKeys.length];

        for (int i = 0; i < groupKeys.length; ++i) {
            String groupKey = groupKeys[i];
            values[i] = incomingValue.getString(groupKey);
        }

        return Tuple.create(values);
    }

    private Tuple initValues(KeyValue incomingValue) {
        Object[] values = new Object[aggregationList.size()];

        for (int i = 0; i < aggregationList.size(); ++i) {
            Aggregation aggregation = aggregationList.get(i);
            values[i] = aggregation.initValue(incomingValue);
        }

        return Tuple.create(values);
    }

    public KeyValue getResult(Tuple group) {
        Tuple value = innerMap.get(group);
        if (value == null) {
            throw new IllegalArgumentException("group " + group + " not in map");
        }
        return createGroupValue(group, value);
    }

    private KeyValue createGroupValue(Tuple group, Tuple value) {
        KeyValue keyValue = KeyValue.create();

        for (int i = 0; i < groupKeys.length; ++i) {
            keyValue.put(groupKeys[i], group.getValueAt(i));
        }

        for (int i = 0; i < aggregationList.size(); ++i) {
            Aggregation aggregation = aggregationList.get(i);
            keyValue.put(aggregation.getOperandKey(), value.getValueAt(i));
        }

        return keyValue;
    }

    public List<KeyValue> getResult() {
        List<KeyValue> kvList = new ArrayList<>();
        innerMap.entrySet().stream().forEach(entry -> {
            Tuple group = entry.getKey();
            Tuple value = entry.getValue();
            kvList.add(createGroupValue(group, value));
        });
        return kvList;
    }


    public void setAggregationList(List<Aggregation> aggregationList) {
        this.aggregationList = aggregationList;
    }

    public List<Aggregation> getAggregationList() {
        return aggregationList;
    }

    public void addAggregation(Aggregation aggregation) {
        aggregationList.add(aggregation);
    }


    //只支持sum
    @Data
    public static class Aggregation {
        private String operandKey;

        public Aggregation(String operandKey) {
            this.operandKey = operandKey;
        }

        public AtomicLong initValue(KeyValue incomingValue) {
            return new AtomicLong();
        }

        void aggregate(Object value, KeyValue incomingValue) {
            AtomicLong atomicLong = (AtomicLong) value;
            atomicLong.addAndGet(incomingValue.getInteger(operandKey));
        }
    }

    public void clean() {
        innerMap.clear();
    }
}
