package com.buzz.aggregate.tuple;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description
 **/
public class Tuple {
    private Object[] values;
    private int cachedHashCode = -1;

    public Tuple(Object... values) {
        this.values = values;
    }

    public static void main(String[] args) {
        Tuple t1 = Tuple.create("drc-daily-************:33", "information_schema");
        Tuple t2 = Tuple.create("drc-daily-************:33", "information_schema");
        System.out.println(t1.hashCode() + "\t" + t2.hashCode());
        System.out.println(t1.equals(t2));
    }

    public static Tuple create(Object... values) {
        return new Tuple(values);
    }

    @Override
    public int hashCode() {
        if (cachedHashCode > -1) {
            return cachedHashCode;
        } else {
            final int prime = 31;
            int result = 1;
            result = prime * result + Arrays.hashCode(values);
            cachedHashCode = result;
            return result;
        }
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj)
            return true;
        if (obj == null)
            return false;
        if (getClass() != obj.getClass())
            return false;
        Tuple other = (Tuple) obj;
        if (! Arrays.equals(values,other.values))
            return false;
        return true;
    }



    public Object getValueAt(int i){
        return values[i];
    }



//    public void add(Object obj) {
//        values.add(obj);
//    }
//
//    public Long putLongIfAbsent(int ix, long value) {
//        if (values.size() < ix + 1) {
//            values.add(value);
//            return Long.valueOf(value);
//        } else {
//            return getLong(ix);
//        }
//    }

//    public Long getLong(int ix) {
//        return Long.parseLong(getString(ix));
//    }
//
//    public String getString(int ix) {
//        return values.get(ix).toString();
//    }
}
