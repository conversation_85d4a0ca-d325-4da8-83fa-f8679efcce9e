package com.buzz.aggregate.tuple;

import com.buzz.aggregate.kv.KeyValue;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 **/
public class TupleAggregator {

    private Map<Tuple, Tuple> innerMap = new HashMap<Tuple, Tuple>();
    private String[] groupKeys;
    private List<Aggregation> aggregationList = new ArrayList<>();

    public TupleAggregator(String[] groupKeys) {
        this.groupKeys = groupKeys;
    }

    public void addAggregation(Aggregation aggregation) {
        this.aggregationList.add(aggregation);
    }

    private Tuple createGroupKey(KeyValue incomingKVs) {
        Object[] values = new Object[groupKeys.length];
        //放入group by key对应的值
        for (int i = 0; i < groupKeys.length; ++i) {
            values[i] = incomingKVs.get(groupKeys[i]);
        }
        return Tuple.create(values);
    }

    private Tuple initAggregation(KeyValue incomingKVs) {
        Object[] values = new Object[aggregationList.size()];
        for (int i = 0; i < aggregationList.size(); ++i) {
            values[i] = aggregationList.get(i).initValue(incomingKVs);
        }
        return Tuple.create(values);
    }

    public void aggregate(KeyValue incomingKVs) {
        Tuple groupKey = createGroupKey(incomingKVs);
        Tuple aggValue = innerMap.computeIfAbsent(groupKey, x -> initAggregation(incomingKVs));
        for (int i = 0; i < aggregationList.size(); ++i) {
            Aggregation aggregation = aggregationList.get(i);
            aggregation.aggregate(aggValue.getValueAt(i), incomingKVs);
        }
    }

    public List<KeyValue> getResult() {
        List<KeyValue> result = new ArrayList<>();
        innerMap.entrySet().stream().forEach(entry -> {
            Tuple group = entry.getKey();
            Tuple aggValue = entry.getValue();
            KeyValue kv = KeyValue.create();
            result.add(kv);

            //写入group数据
            for (int i = 0; i < groupKeys.length; ++i) {
                kv.put(groupKeys[i], group.getValueAt(i));
            }
            //写入聚合数据
            for (int i = 0; i < aggregationList.size(); ++i) {
                aggregationList.get(i).generateKeyValues(kv, aggValue.getValueAt(i));
            }
        });
        return result;
    }
}
