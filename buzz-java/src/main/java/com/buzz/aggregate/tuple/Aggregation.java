package com.buzz.aggregate.tuple;

import com.buzz.aggregate.kv.KeyValue;

import java.util.concurrent.atomic.AtomicLong;

/**
 * <AUTHOR>
 * @description
 **/
public interface Aggregation<T> {

    void aggregate(T t, KeyValue incomingValue);

    T initValue(KeyValue incomingValue);

    void generateKeyValues(KeyValue targetMap, T aggObj);


     class CountAggregation implements Aggregation<AtomicLong>{

         private String targetKey;

         public CountAggregation(String targetKey) {
             this.targetKey = targetKey;
         }

         @Override
        public void aggregate(AtomicLong atomicLong, KeyValue incomingValue) {
             atomicLong.getAndIncrement();
        }

        @Override
        public AtomicLong initValue(KeyValue incomingValue) {
            return new AtomicLong(0);
        }

        @Override
        public void generateKeyValues(KeyValue targetMap, AtomicLong aggObj) {
            targetMap.put(targetKey,aggObj.get());
        }
    }

    class SumAggregation implements Aggregation<AtomicLong>{

         private String operandKey;

         private String targetKey;

        public SumAggregation(String operandKey, String targetKey) {
            this.operandKey = operandKey;
            this.targetKey = targetKey;
        }

        @Override
        public void aggregate(AtomicLong atomicLong, KeyValue incomingValue) {
            atomicLong.getAndAdd(incomingValue.getLong(operandKey));
        }

        @Override
        public AtomicLong initValue(KeyValue incomingValue) {
            return new AtomicLong();
        }

        @Override
        public void generateKeyValues(KeyValue targetMap, AtomicLong aggObj) {
            targetMap.put(targetKey,aggObj.get());
        }
    }
}
