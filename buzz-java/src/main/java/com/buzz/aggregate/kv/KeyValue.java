package com.buzz.aggregate.kv;

import java.util.LinkedHashMap;

/**
 * <AUTHOR>
 * @description
 **/
public class KeyValue extends LinkedHashMap<String, Object> {

    public KeyValue() {
        super();
    }

    public KeyValue(int initialCapacity) {
        super(initialCapacity);
    }

    /**
     * 创建一个空的KeyValue
     *
     * @return
     */
    public static KeyValue create() {
        return new KeyValue();
    }


    /**
     * 通过key返回字符串
     *
     * @param key
     * @return
     */
    public String getString(String key) {
        Object val = this.get(key);
        return val != null ? val.toString() : null;
    }

    /**
     * 通过key返回Integer
     *
     * @param key
     * @return
     */
    public Integer getInteger(String key) {
        Object val = this.get(key);
        return val != null ? Integer.parseInt(val.toString()) : null;
    }

    public Long getLong(String key) {
        Object val = this.get(key);
        return val != null ? Long.parseLong(val.toString()) : null;
    }

    /**
     * 通过key返回Integer,如果为null返回默认值
     *
     * @param key
     * @param defaultVal
     * @return
     */
    public Integer getInteger(String key, int defaultVal) {
        Integer val = getInteger(key);
        return val == null ? defaultVal : val;
    }

}
