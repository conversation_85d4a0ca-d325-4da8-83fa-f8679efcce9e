package com.buzz.aggregate.kv;

import com.buzz.util.Assert;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 **/
public class KeyValueAggregator {
    private Map<KeyValue, KeyValue> innerMap = new HashMap<KeyValue, KeyValue>();

    private String[] groupKeys;

    private List<AggFunc> aggFuncList = new ArrayList<>();

    public KeyValueAggregator(String[] groupKeys) {
        this.groupKeys = groupKeys;
    }

    public void addAggFunc(AggFunc aggFunc) {
        this.aggFuncList.add(aggFunc);
    }

    private KeyValue toGroupKey(KeyValue kv) {
        KeyValue groupKV = KeyValue.create();

        for (String groupKey : groupKeys) {
            String value = kv.getString(groupKey).intern();
            Assert.notNull(value, groupKey + " is not exist in " + kv);
            groupKV.put(groupKey, value);
        }

        return groupKV;
    }

    public void aggregate(KeyValue data) {
        KeyValue groupKV = toGroupKey(data);

        KeyValue aggKV = innerMap.get(groupKV);
        if (aggKV == null) {
            aggKV = KeyValue.create();
            innerMap.put(groupKV, aggKV);
        }

        for (AggFunc aggFunc : aggFuncList) {
            String key = aggFunc.getKey();
            AggFuncOperator operator = aggFunc.getOperator();
            if (operator == AggFuncOperator.SUM) {
                Integer sum = data.getInteger(key) + aggKV.getInteger(key, 0);
                aggKV.put(key, sum);
            }else if(operator == AggFuncOperator.COUNT) {
                aggKV.put(key, 1);
            }else {
                throw new RuntimeException("not support agg func");
            }
        }
    }

    public Tuple getResult() {
        Tuple t = Tuple.create();
        innerMap.entrySet().stream().forEach(i -> {
            KeyValue group = i.getKey();
            KeyValue agg = i.getValue();
            group.putAll(agg);
            t.add(group);
        });
        return t;
    }

    public void clear(){
        innerMap.clear();
    }

    public static class AggFunc {
        private String key;
        private AggFuncOperator operator;

        public AggFunc(String key, AggFuncOperator operator) {
            super();
            this.key = key;
            this.operator = operator;
        }

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }

        public AggFuncOperator getOperator() {
            return operator;
        }

        public void setOperator(AggFuncOperator operator) {
            this.operator = operator;
        }

    }

    public static enum AggFuncOperator {
        SUM,
        COUNT
    }
}
