package com.buzz.aggregate;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 第一版失败的MetricRegistry，失败点：
 * - 不应该用TagMap作为Map的Key
 * - TagMap和FiledMap不够抽象
 * - 聚合函数没有想清楚
 *
 * 原因：没有想清楚内部实现就开始编写对外api，设计最好从内向外，先想好内部实现，最后会发现外部api很简单。
 * <AUTHOR>
 * @description
 **/
public class MetricRegistry0 {
    private static final MetricRegistry0 INSTANCE = new MetricRegistry0();

    private Map<TagMap/*groupKey*/, FiledMap/*groupValue*/> dataMap = new ConcurrentHashMap<>();
    private Map<String/*tagKey*/, Map<String/*tagValue*/, Set<TagMap>/*groupKey*/>> tagIndexMap = new ConcurrentHashMap<>();
    private int maxSize = 50000;

    public void write(Metric metric) {
        //metric.tagMap;

    }

    private void write(TagMap tagMap, FiledMap fieldMap) {
        //write data
        FiledMap existedFiledMap = dataMap.computeIfAbsent(tagMap, k -> new FiledMap());
        for (String filedName : fieldMap.keySet()) {
            AtomicLong atomicLong = existedFiledMap.computeIfAbsent(filedName, k -> new AtomicLong());
            atomicLong.getAndAdd(fieldMap.get(filedName).get());
        }

        //write index
        for (String tagKey : tagMap.keySet()) {
            String tagValue = tagMap.get(tagKey);
            Map<String, Set<TagMap>> subTagMap = tagIndexMap.computeIfAbsent(tagKey, key -> new ConcurrentHashMap<>());
            Set<TagMap> subTagSet = subTagMap.computeIfAbsent(tagValue, key -> new HashSet());
            subTagSet.add(tagMap);
        }
    }

    public void get(String groupBy) {
        get(groupBy, null);
    }



    public void get(String groupBy, TagMap condition) {
        if (condition != null) {
            String key = condition.keySet().iterator().next();
            String value = condition.values().iterator().next();
            Set<TagMap> subTagSet = tagIndexMap.get(key).get(value);
            Map<TagMap/*groupKey*/, FiledMap/*groupValue*/> subDataMap = new HashMap<>();
            for (TagMap subTagMap : subTagSet) {
                TagMap tagMap = new TagMap();
                tagMap.put(groupBy,subTagMap.get(groupBy));
                subDataMap.computeIfAbsent(tagMap,k->new FiledMap());
            }
        }
    }

    public static MetricRegistry0 getInstance() {
        return INSTANCE;
    }


    public static void main(String[] args) {
        String dest = "drc-daily-192.168.5.14:3309-192.168.5.15:3309";
        String db = "wac_platform";
        long size = 1l;
        long bytes = 100l;
        Metric metric = Metric.builder()
                .tag("destination", dest)
                .tag("schema", db)
                .field("size", size)
                .field("bytes", bytes)
                .build();

        MetricRegistry0 metricRegistry = MetricRegistry0.getInstance();
        metricRegistry.write(metric);
    }

    static class TagMap extends HashMap<String/*tagKey*/, String /*tagValue*/> {
        public TagMap() {
        }

        public TagMap(int initialCapacity) {
            super(initialCapacity);
        }
    }

    static class FiledMap extends HashMap<String/*fieldKey*/, AtomicLong /*fieldValue*/> {
        public FiledMap() {
        }

        public FiledMap(int initialCapacity) {
            super(initialCapacity);
        }

    }

    static class Metric {
        private TagMap tagMap;
        private FiledMap fieldMap;

        public Metric(TagMap tagMap, FiledMap fieldMap) {
            this.tagMap = tagMap;
            this.fieldMap = fieldMap;
        }

        static Builder builder() {
            return new Builder();
        }

        static class Builder {
            private TagMap tagMap = new TagMap(8);
            private FiledMap fieldMap = new FiledMap(4);

            public Builder tag(String tagKey, String tagValue) {
                tagMap.put(tagKey, tagValue);
                return this;
            }

            public Builder field(String fieldKey, Long fieldValue) {
                fieldMap.put(fieldKey, new AtomicLong(fieldValue));
                return this;
            }

            public Metric build() {
                return new Metric(tagMap, fieldMap);
            }
        }

    }

}
