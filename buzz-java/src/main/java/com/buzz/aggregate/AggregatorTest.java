package com.buzz.aggregate;

import com.buzz.aggregate.kv.KeyValue;
import com.buzz.aggregate.kv.KeyValueAggregator;
import com.buzz.aggregate.kv.Tuple;
import com.buzz.aggregate.tuple.Aggregation;
import com.buzz.aggregate.tuple.TupleAggregator;
import com.buzz.util.FormatUtils;
import org.junit.Test;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.ArrayList;
import java.util.List;

import static com.buzz.aggregate.kv.KeyValueAggregator.AggFunc;
import static com.buzz.aggregate.kv.KeyValueAggregator.AggFuncOperator;

/**
 * 7560条数据，基于Tuple占用内存大约 9MB, 基于kv占用内存14mMB
 *
 * <AUTHOR>
 * @description
 **/
public class AggregatorTest {

    private static final String[] destinations = "drc-daily-************:3309-************:3309,drc-daily-************:3307-************:3307,drc-daily-************:3321-************:3321,drc-daily-************:3318-************:3318,drc-daily-************:3310-************:3310,drc-daily-************:3312-************:3312,drc-daily-************:3320-************:3320".split(",");
    private static final String[] origin_database = ("information_schema,credit_common,credit_crawler,credit_sharding001,credit_sharding002,csw_flow,csw_secret," +
            "dbmonitor,dedecms,discuz,dj_matrix,dj_middleware,fu_search,heartbeat,imock,k2doc,mysql,ofa_data,ofa_mongo,ofa_order,ofa_product,ofa_quartz," +
            "performance_schema,prophet,quanzijizhang,sec_skynet,social_security,sys,test,ucenter,wac_advert,wac_apply,wac_apply_data_migration,wac_credit_mgr,wac_crt,wac_money" +
            ",wac_msg,information_schema,accounting_2322,accounting_2422,accounting_common,accounting_common_demo,accounting_hzgm,accounting_kingdee_common,accounting_kingdee_common_demo,accounting_oa_prod,accounting_ww54a142ee172647a3,accounting_ww5e968dcffea8f783,accounting_wwebedf197a28ecd29,accounting_wwffd870703e8fb988,approve_adanos,client_content,client_waes,client_waes_qa,cq_loan,cqxd,dbmonitor,eagle_jinguang,eagle_xuetian,eshop,feature_manager,ghost_test1,ghost_test2,hainan_compass,hainan_phoebe,heartbeat,heros_qa,hive_config_web,hive_config_web_pro,hny_wac_loan_goblin,it_asset,loan_normandy,loan_pharos,loan_saas_cloud,loan_sharding001,loan_sharding002,loan_thunder,loan_urule,log_2311,log_2322,log_ww5e968dcffea8f783,mysql,performance_schema,pf_loan,pt_test,scy_wac_loan_goblin,scy_wac_loan_hobbit,stanlee_hive,stock_report,sys,szy_ns_wac_loan_backend,szy_ns_wac_loan_goblin,szy_ns_wac_loan_hobbit,szy_pay_monitor,szy_wac_pay_datapull,szy_wac_pay_limbic,tars,test,test_wac_loan_goblin,test_wac_loan_hobbit,ued,wac_asset,wac_diamond,wac_flow,wac_flow1,wac_flow2,wac_handel,wac_hawk_eye,wac_loan,wac_loan_account,wac_loan_account_archiving,wac_loan_alchemist,wac_loan_alitar,wac_loan_backend,wac_loan_backend_tenant,wac_loan_coffer,wac_loan_cube,wac_loan_cuiniao,wac_loan_data,wac_loan_feeder,wac_loan_funder,wac_loan_goblin,wac_loan_gringotts,wac_loan_lumosduo,wac_loan_material,wac_loan_mc,wac_loan_medivh,wac_loan_op,wac_loan_tail,wac_loan_wai,wac_loan_workflow,wac_loan_yo,wac_loan_yo_bro,wac_loan_yo_freeswitch,wac_loan_yo_opensips,wac_loan_yo_report,wac_missile,wac_nlp,wac_paganini,wac_payment,wac_pisces_cqxx,wac_schumann,wac_tchaikovsky,wac_tchaikovsky_quiet,wac_threat_info,wac_trike_khadgar,wac_trike_malone,wac_trike_message,wac_trike_mirana,wac_trike_orianna,wac_trike_skathi,wac_turgenev,wac_wealth_planet,wacai_compass,wacai_phoebe,wcc_wac_loan_hobbit,wdp_cm,wr_sherlock,wr_yo_bro,xhy_contract,xhy_pay_monitor,xhy_wac_acct,xhy_wac_acct_history,xhy_wac_berkshire,xhy_wac_common,xhy_wac_eos,xhy_wac_loan_alchemist,xhy_wac_loan_alitar,xhy_wac_loan_backend,xhy_wac_loan_cuiniao,xhy_wac_loan_goblin,xhy_wac_loan_hobbit,xhy_wac_loan_medivh,xhy_wac_loan_xiezhi,xhy_wac_pay,xhy_wac_pay_boa,xhy_wac_pay_datapull,xhy_wac_pay_limbic,xhy_wac_pay_mars,xhy_wac_pay_validate,xhy_wac_payment,xhy_wac_payment_decision,xhy_wac_payment_sc,xhy_wac_trike_mirana,yhy_wac_loan_backend,yhy_wac_loan_goblin,yhy_wac_loan_hobbit").split(",");

    enum Mode {
        BIG, TINY
    }

    private List<KeyValue> getFeedData(Mode mode) {
        List<KeyValue> dataList = new ArrayList<>();
        if (mode == Mode.TINY) {
            String[] destinations = "drc-daily-************:3309-************:3309".split(",");
            String[] databases = "information_schema,credit_common,credit_crawler,csw_flow,csw_secret".split(",");

            System.out.println("destinations.size=" + destinations.length);
            System.out.println("databases.size=" + databases.length);

            for (int i = 0; i < 100; ++i) {
                KeyValue item = KeyValue.create();
                item.put("destination", destinations[i % destinations.length]);
                item.put("schema", databases[i % databases.length]);
                item.put("size", 5);
                item.put("bytes", 10);
                dataList.add(item);
            }
            return dataList;
        } else if (mode == Mode.BIG) {

            List<String> databases = new ArrayList<>();
            for (String db : origin_database) {
                databases.add(db);
                databases.add("xhy." + db);
                databases.add("xamc." + db);
                databases.add("hnamc." + db);
                databases.add("wramc." + db);
                databases.add("hchf." + db);
            }
            System.out.println("destinations.size=" + destinations.length);
            System.out.println("databases.size=" + databases.size());

            for (int i = 0; i < 50000; ++i) {
                KeyValue item = KeyValue.create();
                item.put("destination", destinations[i % destinations.length]);
                item.put("schema", databases.get(i % databases.size()));
                item.put("size", 5);
                item.put("bytes", 10);
                dataList.add(item);
            }
            databases.clear();
            return dataList;
        } else {
            return dataList;
        }
    }

    @Test
    public void testTupleAggregator() throws Exception {
        TupleAggregator aggregator = new TupleAggregator(new String[]{"destination", "schema"});
        aggregator.addAggregation(new Aggregation.CountAggregation("count"));
        aggregator.addAggregation(new Aggregation.SumAggregation("size", "sizeSum"));
        aggregator.addAggregation(new Aggregation.SumAggregation("bytes", "bytesSum"));
        Mode mode = Mode.BIG;
        List<KeyValue> feedDataList = getFeedData(mode);
        for (KeyValue data : feedDataList) {
            aggregator.aggregate(data);
        }
        feedDataList.clear();
        System.out.println("======result========");
        List<KeyValue> tuple = aggregator.getResult();
        System.out.println(tuple.size());
        if (mode == Mode.TINY) {
            tuple.forEach(i -> System.out.println(i));
        }
        tuple.clear();
        //aggregator.clear();
        System.out.println("======result========");
        System.gc();
        Thread.sleep(1000);
        printMemory();
    }

    @Test
    public void testKeyValueAggregator() throws Exception {
        KeyValueAggregator aggregator = new KeyValueAggregator(new String[]{"destination", "schema"});
        aggregator.addAggFunc(new AggFunc(null, AggFuncOperator.COUNT));
        aggregator.addAggFunc(new AggFunc("size", AggFuncOperator.SUM));
        aggregator.addAggFunc(new AggFunc("bytes", AggFuncOperator.SUM));
        Mode mode = Mode.BIG;

        List<KeyValue> feedDataList = getFeedData(mode);
        for (KeyValue data : feedDataList) {
            aggregator.aggregate(data);
        }
        feedDataList.clear();
        System.out.println("======result========");
        Tuple tuple = aggregator.getResult();
        System.out.println(tuple.size());
        if (mode == Mode.TINY) {
            tuple.forEach(i -> System.out.println(i));
        }
        tuple.clear();
        //aggregator.clear();
        System.out.println("======result========");
        System.gc();
        Thread.sleep(1000);
        printMemory();
    }


    static void printMemory() {
        MemoryMXBean memBean = ManagementFactory.getMemoryMXBean();
        MemoryUsage heapMemoryUsage = memBean.getHeapMemoryUsage();
        System.out.println(FormatUtils.humanReadableByteSize(heapMemoryUsage.getMax())); // max memory allowed for jvm -Xmx flag (-1 if isn't specified)
        System.out.println(FormatUtils.humanReadableByteSize(heapMemoryUsage.getCommitted())); // given memory to JVM by OS ( may fail to reach getMax, if there isn't more memory)
        System.out.println(FormatUtils.humanReadableByteSize(heapMemoryUsage.getUsed())); // used now by your heap
        System.out.println(FormatUtils.humanReadableByteSize(heapMemoryUsage.getInit())); // -Xms flag
    }
}
