package com.buzz.websocket;

import java.io.IOException;
import java.net.URI;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import java.util.zip.DataFormatException;

import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.server.ServerConnector;
import org.eclipse.jetty.util.component.LifeCycle;
import org.eclipse.jetty.websocket.api.Session;
import org.eclipse.jetty.websocket.api.annotations.OnWebSocketClose;
import org.eclipse.jetty.websocket.api.annotations.OnWebSocketConnect;
import org.eclipse.jetty.websocket.api.annotations.OnWebSocketFrame;
import org.eclipse.jetty.websocket.api.annotations.WebSocket;
import org.eclipse.jetty.websocket.api.extensions.ExtensionFactory;
import org.eclipse.jetty.websocket.api.extensions.Frame;
import org.eclipse.jetty.websocket.client.ClientUpgradeRequest;
import org.eclipse.jetty.websocket.client.WebSocketClient;
import org.eclipse.jetty.websocket.servlet.ServletUpgradeRequest;
import org.eclipse.jetty.websocket.servlet.ServletUpgradeResponse;
import org.eclipse.jetty.websocket.servlet.WebSocketCreator;
import org.eclipse.jetty.websocket.servlet.WebSocketServletFactory;


/**
 * https://github.com/eclipse/jetty.project/issues/2655
 * 
 * <AUTHOR>
 *
 */
public class JettyClientCloseBug extends org.eclipse.jetty.websocket.server.WebSocketHandler implements WebSocketCreator {
    
    private static int PORT = 8080;

    @Override
    public void configure(WebSocketServletFactory factory) {
        factory.register(WebSocketHandler.class);
        factory.setCreator(this);
    }

    @Override
    public Object createWebSocket(ServletUpgradeRequest req, ServletUpgradeResponse resp) {
        return new WebSocketHandler();
    }
    
    private void startServer() throws Exception {
        Server server = new Server();

        ServerConnector connector = new ServerConnector(server);
        connector.setPort(8080);
        server.addConnector(connector);
        server.setHandler(this);
        server.start();
    }
    
    private void closeSessionTest(int round) throws Exception {
        URI uri = new URI("ws://localhost:" + PORT);
        
        ClientUpgradeRequest request = new ClientUpgradeRequest();
        request.setRequestURI(uri);
        WebSocketClient client = new WebSocketClient();
        client.start();
        
        ExtensionFactory ef = client.getExtensionFactory();
        ef.unregister("permessage-deflate");
        ef.unregister("deflate-frame");
        ef.unregister("x-webkit-deflate-frame");
        Set<String> ext = ef.getExtensionNames();
        request.addExtensions(ext.toArray(new String[ext.size()]));

        for (int i = 0; i < round; i++) {
            Session session = client.connect(new WebSocketHandler(), uri, request).get(15, TimeUnit.SECONDS);
            //session.getRemote().sendPing(applicationData);
            session.close();
        }
        //虽然调用了 session.close()，但是client还是持有session的实例
        System.out.println("Put a breakpoint here");
        client.stop();
        client.destroy();
    }
    
    @WebSocket()
    public static class WebSocketHandler {
        int sec, bytes, frames;
        @OnWebSocketFrame
        public void onWebSocketFrame( Session session,  Frame frame) throws ClassNotFoundException, IOException, DataFormatException {
            System.out.println("receive  : " + session);
        }
        
        @OnWebSocketConnect
        public void onWebSocketConnect( Session session) {
            System.out.println("open  sess: " + session);
        }
        
        @OnWebSocketClose
        public void onWebSocketClose( Session session, int statusCode,  String reason){
            System.out.println("close sess: " + session + " reason: " + reason);
        }
    }

    
    public static void main(String[] args) throws Exception {
        JettyClientCloseBug inst = new JettyClientCloseBug();
        inst.startServer();
        Thread.sleep(2000);
        
        inst.closeSessionTest(100);
        
    }
}