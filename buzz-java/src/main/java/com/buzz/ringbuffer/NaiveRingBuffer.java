package com.buzz.ringbuffer;

public class NaiveRingBuffer {

    private Integer[] queue; // 底层用数组实现
    private int size; // 队列大小
    private int tail; // 尾部指针
    private int head; // 头部指针

    public NaiveRingBuffer(int k) {
        this.queue = new Integer[k];
        this.size = 0;
        this.tail = 0; // 初始化两个指针都指向数组开头
        this.head = 0;
    }

    public boolean offer(int value) {
        if (size == queue.length)
            return false; // 队列大小=数组空间，满
        else {
            queue[tail] = value;
            if (tail == queue.length - 1)
                tail = 0; // 判断rear下一步是否循环还是自增
            else
                tail++;
            size++;
            return true;
        }
    }

    public boolean remove() {
        if (size == 0)
            return false; // 队列为空
        else {
            queue[head] = null;
            if (head == queue.length - 1)
                head = 0; // 判断head下一步是否循环还是自增
            else
                head++;

            size--;
            return true;
        }
    }

    //// 返回队列头部元素
    public int poll() {
        if (isEmpty())
            return -1;
        else {
            return queue[head];
        }
    }

    // 判断队列是否为空
    public boolean isEmpty() {
        return size == 0;
    }

    // 判断队列是否为满
    public boolean isFull() {
        return size == queue.length;
    }

    static void test(NaiveRingBuffer rb) {

        for (int i = 0; i < 5; ++i) {
            System.out.println(rb.offer(i));
        }
        for (int i = 0; i < 5; ++i) {
            System.out.println(rb.poll());
            rb.remove();
        }

    }

    public static void main(String[] args) {
        NaiveRingBuffer rb = new NaiveRingBuffer(4);
        test(rb);
        test(rb);
    }
}
