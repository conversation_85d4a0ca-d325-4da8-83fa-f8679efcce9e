package com.buzz.ringbuffer;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.Serializable;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * https://mp.weixin.qq.com/s/hmtx8BnUDp1onCJ99rdQ9g
 * https://github.com/lkxiaolou/skywalking/blob/d153b8357ac8cdc8fdcfca7483158f8059f35e9d/apm-commons/apm-datacarrier/src/main/java/org/apache/skywalking/apm/commons/datacarrier/common/AtomicRangeInteger.java
 */
@Slf4j
public class AtomicRangeIntegerTest {

    private static AtomicRangeInteger ATOMIC_NEW = new AtomicRangeInteger(0, 10);
    private static AtomicRangeInteger ATOMIC_ORI = new AtomicRangeInteger(0, 10);

    @Test
    public void testNew() {
        for (int i = 0; i < 100; ++i) {
            log.info("ix="+ATOMIC_NEW.getAndIncrement_new());
        }

    }

    @Test
    public void testOld() {
        for (int i = 0; i < 100; ++i) {
            ATOMIC_NEW.getAndIncrement_old();
        }

    }

    static class AtomicRangeInteger extends Number implements Serializable {
        private AtomicInteger value;
        private int startValue;
        private int endValue;

        public AtomicRangeInteger(int startValue, int maxValue) {
            this.value = new AtomicInteger(startValue);
            this.startValue = startValue;
            this.endValue = maxValue - 1;
        }

        public final int getAndIncrement_old() {
            int current;
            int next;
            do {
                current = this.value.get();
                next = current >= this.endValue ? this.startValue : current + 1;
            }
            while (!this.value.compareAndSet(current, next));

            return current;
        }

        public final int getAndIncrement_new() {
            int next;
            do {
                //虽然incrementAndGet内部也是cas，但是jdk8之后对incrementAndGet做了优化，fetch-and-add是cpu指令，性能上fetch-and-add要强很多
                next = this.value.incrementAndGet();
                //当next超过最大时，重置value
                if (next > endValue && this.value.compareAndSet(next, startValue)) {
                    return endValue;
                }
            } while (next > endValue);

            return next - 1;
        }

        public final int get() {
            return this.value.get();
        }

        public int intValue() {
            return this.value.intValue();
        }

        public long longValue() {
            return this.value.longValue();
        }

        public float floatValue() {
            return this.value.floatValue();
        }

        public double doubleValue() {
            return this.value.doubleValue();
        }
    }
}
