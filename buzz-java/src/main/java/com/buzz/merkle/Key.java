package com.buzz.merkle;

import java.nio.ByteBuffer;
import java.security.MessageDigest;

public class Key {

	public static int[] hashToBytes(long hash) {
		//buf中存放半个字节4位
		int[] buf = new int[16];

		int shift = 0;
		for (int i = 0; i < buf.length; ++i) {
			shift = (4 * (15 - i));
			int idx = (int) ((hash >> shift)) & 0xf;
			System.out.println(i + ":" + idx);
			buf[i] = idx;
		}
		return buf;
	}

	public static long byteToLong(byte[] data) {
		ByteBuffer buf = ByteBuffer.wrap(data);
		return buf.getLong();
	}

	public static void main(String[] args) throws Exception {

//		CRC32 crc = new CRC32();
//		crc.update("abc".getBytes());
//		long hash = crc.getValue();
//		System.out.println("hash=" + hash);
//		hashToBytes(hash);

//		MessageDigest md = MessageDigest.getInstance("MD5");
//		md.update("Hello".getBytes("UTF-8"));
//		byte[] ret= md.digest();
//		long hash = byteToLong(ret);
//		hashToBytes(hash);
//		System.out.println(hash);
//		System.out.println(Long.toHexString(hash));
//		System.out.println(Long.toBinaryString(hash));
		int bucketID =0;
		bucketID <<= 4;
	}

}
