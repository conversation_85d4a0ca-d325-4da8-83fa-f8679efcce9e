package com.buzz.merkle;

import java.util.ArrayList;
import java.util.List;

/**
 * http://www.ehcoo.com/MerkleTree.html
 * https://medium.com/@vinayprabhu19/merkel-tree-in-java-b45093c8c6bd
 * 
 * <AUTHOR>
 *
 */
public class MerkleTree {

	// transaction List
	List<String> txList;
	// Merkle Root
	String root;

	/**
	   * constructor
	   * @param txList transaction List 交易List
	   */
	public MerkleTree(List<String> txList) {
		this.txList = txList;
		root = "";
	}

	/**
	 * execute merkle_tree and set root.
	 */
	public void merkle_tree() {

		//copy to tempTxList
		List<String> tempTxList = new ArrayList<String>();
		tempTxList.addAll(txList);
		
		List<String> newTxList = getNewTxList(tempTxList);
		
		while (newTxList.size() != 1) {
			System.out.println("newTxList="+newTxList);
			newTxList = getNewTxList(newTxList);
		}
		
		System.out.println(newTxList);
		this.root = newTxList.get(0);
	}

	/**
	 * return Node Hash List.
	 * @param tempTxList
	 * @return
	 */
	private List<String> getNewTxList(List<String> tempTxList) {

		List<String> newTxList = new ArrayList<String>();
		int index = 0;
		while (index < tempTxList.size()) {
			// left
			String left = tempTxList.get(index);
			index++;
			// right
			String right = "";
			if (index != tempTxList.size()) {
				right = tempTxList.get(index);
			}
			// sha2 hex value
			String sha2HexValue = getSHA2HexValue(left + right);
			newTxList.add(sha2HexValue);
			index++;

		}

		return newTxList;
	}

	/**
	 * Return hex string
	 * @param str
	 * @return
	 */
	public String getSHA2HexValue(String str) {
//		byte[] cipher_byte;
//		try {
//			MessageDigest md = MessageDigest.getInstance("SHA-256");
//			md.update(str.getBytes());
//			cipher_byte = md.digest();
//			StringBuilder sb = new StringBuilder(2 * cipher_byte.length);
//			for (byte b : cipher_byte) {
//				sb.append(String.format("%02x", b & 0xff));
//			}
//			return sb.toString();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}

		return str;
	}

	/**
	 * Get Root
	 * @return
	 */
	public String getRoot() {
		return this.root;
	}

	public static void main(String[] args) {
		List<String> txList = new ArrayList<String>();
		txList.add("a");
		txList.add("b");
		txList.add("c");
		txList.add("d");
		txList.add("e");

		MerkleTree tree = new MerkleTree(txList);
		tree.merkle_tree();
		System.out.println(tree.root);
	}

}
