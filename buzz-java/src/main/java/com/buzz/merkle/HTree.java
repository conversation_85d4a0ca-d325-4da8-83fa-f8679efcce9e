package com.buzz.merkle;

/**
 * 	// level[0][0] is root of a htree,
 *	// Node stores the summary info of its childs.
 * 
 * 
 * <AUTHOR>
 *
 */
public class HTree {

	int depth;
	int bucketID;
	Node[][] levels;
	//leafs is the place to store key related info (e.g. keyhash, version, vhash etc.)
	SliceHeader[] leafs;

	public HTree(int height) {
		int size = 1;
		for (int i = 0; i < height; ++i) {
			levels[i] = new Node[size];
			size = size * 16;
		}
		size /= 16;

		//叶子节点上一层
		Node[] leafnodes = levels[height - 1];
		for (int i = 0; i < size; ++i) {
			leafnodes[i].isHashUpdated = true;
		}
		leafs = new SliceHeader[size];
	}

	private static class Node {
		int count;
		int hash;
		boolean isHashUpdated;
	}

	private static class SliceHeader {
		String data;
		int len;
	}
}
