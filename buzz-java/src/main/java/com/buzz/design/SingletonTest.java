package com.buzz.design;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import lombok.extern.slf4j.Slf4j;

/**
 * 如果没有dubbo check 会导致Plugin重复初始化
 * <AUTHOR>
 *
 */
@Slf4j
public class SingletonTest {

	private static void testGetPlugin(final PluginLoader loader) {
		for (int i = 0; i < 10; ++i) {
			loader.getPlugin(String.valueOf(i));
		}
	}

	public static void main(String[] args) {
		PluginLoader loader = new PluginLoader();

		for (int i = 0; i < 10; ++i) {
			Thread t = new Thread(() -> {
				testGetPlugin(loader);
			});
			t.start();
		}

	}

	static class PluginLoader {

		private static final Map<String, Plugin> PLUGINS = new ConcurrentHashMap<String, Plugin>();

		public Plugin getPlugin(String name) {
			Plugin plugin = PLUGINS.get(name);
			if (plugin == null) {
				synchronized (this) {
					//double check
					plugin = PLUGINS.get(name);
					if (plugin == null) {
						plugin = new Plugin(name);
						PLUGINS.put(name, plugin);
					}
				}
			}

			return plugin;

		}

	}

	static class Plugin {

		private String name;

		public Plugin(String name) {
			super();
			this.name = name;
			log.info("loading... " + name + " plugin");
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

		public String toString() {
			return "plugin-" + name;
		}
	}
}
