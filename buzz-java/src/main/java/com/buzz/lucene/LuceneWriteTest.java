package com.buzz.lucene;

import com.buzz.util.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.Field;
import org.apache.lucene.document.LongPoint;
import org.apache.lucene.document.SortedDocValuesField;
import org.apache.lucene.document.StoredField;
import org.apache.lucene.document.TextField;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.util.BytesRef;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 两个字段 title, message
 */
@Slf4j
public class LuceneWriteTest {

    @Test
    public void test_WriteBuzzCode() throws IOException {
        writeIndex("buzz-code", getBuzzCode());
    }

    @Test
    public void test_WriteSentence() throws IOException {
        List<String[]> list = new ArrayList<>();
        for (int i = 0; i < 100; ++i) {
            list.add(new String[]{"china", "i come from china, china is my home country"});
            list.add(new String[]{"usa", "i come from USA, USA is my home country"});
            list.add(new String[]{"java", "java is a programing language that popular in china"});
            list.add(new String[]{"python", "python is a programing language"});
        }
        writeIndex("sentence", list);

    }

    private Random random = new Random();

    private void writeIndex(String topic, List<String[]> dataList) throws IOException {
        IndexWriter writer = LuceneBuilder.indexWriterBuilder()
                .topic(topic)
                .simpleTextCodec(false)
                .build();
        long begin = System.currentTimeMillis();
        for (String[] data : dataList) {
            Document doc = new Document();
            doc.add(new TextField("title", data[0], Field.Store.YES));
            doc.add(new TextField("message", data[1], Field.Store.YES));
            long offset = random.nextInt(9999);
            // 使用 LongPoint 索引值，支持范围查询
            doc.add(new LongPoint("offset", offset));
            // 使用 StoredField 存储原始值
            doc.add(new StoredField("offset", offset));

            // 使用 SortedDocValuesField 排序字段
            doc.add(new SortedDocValuesField("title", new BytesRef(data[0])));
            try {
                writer.addDocument(doc);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        log.info("add doc cost:{}", System.currentTimeMillis() - begin);
        begin= System.currentTimeMillis();
        writer.flush();
        log.info("flush doc cost:{}", System.currentTimeMillis() - begin);
        begin= System.currentTimeMillis();
        writer.commit();
        log.info("commit doc cost:{}", System.currentTimeMillis() - begin);
        writer.close();
    }

    private List<String[]> getBuzzCode() throws IOException {
        File file = new File("/work/dist/branch/wacai/middleware/my-boot/buzz-java/src/main/java/com/buzz");
        List<String[]> list = new ArrayList<>();
        for (File subFile : file.listFiles()) {
            if (subFile.isDirectory()) {
                for (File javaFile : subFile.listFiles()) {
                    if (javaFile.isFile()) {
                        String content = IOUtils.readFile(javaFile);
                        list.add(new String[]{javaFile.getName(), content});
                    }
                }
            }
        }
        return list;
    }
}
