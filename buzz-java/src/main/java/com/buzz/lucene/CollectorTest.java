package com.buzz.lucene;

import lombok.extern.slf4j.Slf4j;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.LeafReaderContext;
import org.apache.lucene.index.Term;
import org.apache.lucene.search.*;
import org.junit.Test;

import java.io.IOException;

@Slf4j
public class CollectorTest {

    @Test
    public void test() throws IOException {
        IndexWriter writer = LuceneBuilder.indexWriterBuilder()
                .topic("buzz-code")
                .build();
        IndexSearcher searcher = LuceneBuilder.indexSearcherBuilder()
                .writer(writer)
                .build();
        Query query = new TermQuery(new Term("message", "java"));
        searcher.search(query, new Collector() {

            @Override
            public LeafCollector getLeafCollector(LeafReaderContext context) throws IOException {
                final int docBase = context.docBase;//起始docId
                return new LeafCollector() {
                    @Override
                    public void setScorer(Scorer scorer) throws IOException {
                    }

                    @Override
                    public void collect(int doc) throws IOException {
                        log.info("collect docBase:{} doc:{}", docBase, doc);
                    }
                };
            }

            @Override
            public boolean needsScores() {
                return false;
            }
        });
    }
}
