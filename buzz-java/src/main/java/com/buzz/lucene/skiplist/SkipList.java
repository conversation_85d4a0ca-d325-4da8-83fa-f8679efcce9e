package com.buzz.lucene.skiplist;

public class SkipList {

    // Maximum level for this skip list
    int maxLevel;

    // P is the fraction of the nodes with level
    // i pointers also having level i+1 pointers
    float P;

    // current level of skip list
    int level;

    // pointer to header node
    Node header;

    SkipList(int maxLevel, float P) {
        this.maxLevel = maxLevel;
        this.P = P;
        level = 0;

        // create header node and initialize key to -1
        header = new Node(-1, maxLevel);
    }

    int randomLevel() {
        float r = (float) Math.random();
        int lvl = 0;
        while (r < P && lvl < maxLevel) {
            lvl++;
            r = (float) Math.random();
        }
        return lvl;
    }

    Node createNode(int key, int level) {
        Node n = new Node(key, level);
        return n;
    }

    // Insert given key in skip list

    public void insert(int data) {
        Node current = header;

        // create update array and initialize it
        Node update[] = new Node[maxLevel + 1];

            /* start from highest level of skip list
                    move the current pointer forward while
               key is greater than key of node next to
               current Otherwise inserted current in update
               and move one level down and continue search
            */
        for (int i = level; i >= 0; i--) {
            while (current.forward[i] != null
                    && current.forward[i].data < data)
                current = current.forward[i];
            update[i] = current;
        }

            /* reached level 0 and forward pointer to
            right, which is desired position to
            insert key.
            */
        current = current.forward[0];

            /* if current is NULL that means we have reached
            to end of the level or current's key is not
            equal to key to insert that means we have to
            insert node between update[0] and current node
          */
        if (current == null || current.data != data) {
            // Generate a random level for node
            int rlevel = randomLevel();

            // If random level is greater than list's
            // current level (node with highest level
            // inserted in list so far), initialize
            // update value with pointer to header for
            // further use
            if (rlevel > level) {
                for (int i = level + 1; i < rlevel + 1;i++)
                    update[i] = header;

                // Update the list current level
                level = rlevel;
            }

            // create new node with random level
            // generated
            Node n = createNode(data, rlevel);

            // insert node by rearranging pointers
            for (int i = 0; i <= rlevel; i++) {
                n.forward[i] = update[i].forward[i];
                update[i].forward[i] = n;
            }
            System.out.println("Successfully Inserted key " + data+" level "+rlevel);
        }
    }

    public void search(int data){
        Node current = header;
            /* start from highest level of skip list
                    move the current pointer forward while
            key is greater than key of node next to
            current    and move one level down and continue search
            */
        for (int i = level; i >=0 ; i--) { //下一层移动
            while (current.forward[i] != null && current.forward[i].data < data) {
                current = current.forward[i];//同一层移动
            }
        }
        current = current.forward[0];
        // current has to be the key if it is present
        if(current !=null && current.data==data) {
            System.out.println("Key found");

        }
        else{
            System.out.println("Key not found");
        }
    }

    // Display skip list level wise
    void displayList() {
        System.out.println("\n*****Skip List*****");
        for (int i = 0; i <= level; i++) {
            Node node = header.forward[i];
            System.out.print("Level " + i + ": ");
            while (node != null) {
                System.out.print(node.data + " ");
                node = node.forward[i];
            }
            System.out.println();
        }
    }


    // Class to implement node
    static class Node {
        int data;

        // Array to hold pointers to node of different level
        Node forward[];

        Node(int data, int level) {
            this.data = data;

            // Allocate memory to forward
            forward = new Node[level + 1];
        }
    }

    ;
}
