package com.buzz.lucene.skiplist;

import org.junit.Test;

public class SkipListTest {


    @Test
    public void testSkipList() {
        SkipList lst = new SkipList(2, 0.5f);
        lst.insert(3);
        lst.insert(6);
        lst.insert(7);
        lst.insert(9);
        lst.insert(12);
        lst.insert(19);
        lst.insert(17);
        lst.insert(26);
        lst.insert(21);
        lst.insert(25);
        lst.displayList();

        lst.search(17);

    }

    @Test
    public void testSkipList2() {
        SkipList2 list2 = new SkipList2();
        for (int i = 1; i < 30; i+=3) {
            list2.insert(i);
        }
        list2.printAll_beautiful();
        list2.find(6);
    }
}
