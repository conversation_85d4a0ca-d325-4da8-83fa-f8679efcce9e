package com.buzz.lucene;

import com.buzz.lucene.LuceneSearcher.DocumentInfo;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.LongPoint;
import org.apache.lucene.index.IndexReader;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.search.IndexSearcher;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.Sort;
import org.apache.lucene.search.SortField;
import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;

/**
 * https://www.baeldung.com/lucene
 * 查询前使用 LuceneWriteTest 构建
 */
public class LuceneSearchTest {
    @Test
    public void test_ReadBuzzCode() throws IOException {
        LuceneSearcher.queryByTerm("buzz-code", "java", null);
    }

    /**
     * 范围查询
     */
    @Test
    public void testRangeQuery() throws IOException {

        Query query = LongPoint.newRangeQuery("offset", 1700, 1750);
        ArrayList<DocumentInfo> result = LuceneSearcher.doQuery("sentence", query, null);
        result.stream().forEach(item -> System.out.println(item));
    }

    @Test
    public void testQueryAll() throws IOException {
        IndexWriter writer = LuceneBuilder.indexWriterBuilder()
                .topic("sentence")
                .build();
        IndexSearcher searcher = LuceneBuilder.indexSearcherBuilder()
                .writer(writer)
                .build();
        IndexReader reader = searcher.getIndexReader();
        int num = reader.numDocs();
        for (int i = 0; i < num; i++) {
            Document d = reader.document(i);
            System.out.println("d=" + d);
        }
    }

    @Test
    public void testTermQuery() throws IOException {
        LuceneSearcher.queryByTerm("sentence", "home", null);
        LuceneSearcher.queryByTerm("sentence", "my home", null);//没有结果
    }

    @Test
    public void testTextQuery() throws IOException {
        //该查询会被构建BooleanQuery里面包含两个TermQuery(come) TermQuery(home) 关系为should
        LuceneSearcher.queryByText("sentence", "come home", null);
        LuceneSearcher.queryByString("sentence", "come home", null);
    }

    @Test
    public void testStringQuery() throws IOException {
        LuceneSearcher.queryByString("sentence", "message:home", null);
        LuceneSearcher.queryByString("sentence", "message:home AND NOT message:usa", null);
    }

    @Test
    public void testSortQuery() throws IOException {
        Sort sort = new Sort(new SortField("title", SortField.Type.STRING));
        LuceneSearcher.queryByString("sentence", "message:home", sort);

        sort = new Sort(new SortField("title", SortField.Type.STRING, true));
        LuceneSearcher.queryByString("sentence", "message:home", sort);
    }


}
