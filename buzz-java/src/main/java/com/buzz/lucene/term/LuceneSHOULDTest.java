package com.buzz.lucene.term;

import com.buzz.lucene.LuceneBaseTest;
import org.apache.lucene.document.Document;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.search.Query;
import org.junit.Test;

import java.io.IOException;
import java.util.function.BiConsumer;

/**
 * BooleanClause: MUST、SHOULD、MUST_NOT、FILTER
 */
public class LuceneSHOULDTest extends LuceneBaseTest {
    @Override
    protected BiConsumer<Document, Long> getIndexBuilder() {
        return (doc, i) -> {
        };
    }

    @Override
    protected Query getQuery() throws ParseException {
        return should();
    }

    @Test
    public void testWrite() throws IOException {
        super.write("LongPoint");
    }

    @Test
    public void testRead() throws IOException {
        super.write("LongPoint");
        super.read("LongPoint");
    }
}
