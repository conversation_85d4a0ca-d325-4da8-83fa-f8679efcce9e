package com.buzz.lucene.packed;

import org.junit.Test;

/**
 * https://juejin.cn/post/7163560186823573517#heading-9
 */
public class ZigZagTest {

    public static int zigZagEncode(int i) {
        // (i >> 31)：处理符号位，把所有的位都设置为符号位，等待和0（数据位左移1位空出来的0）做异或，就能保留符号位在最后一位
        // (i << 1)：处理数据位，数据为左移1位，把最后一位用来存符号位，其他数据为都和符号位做异或编码
        return (i >> 31) ^ (i << 1);
    }

    public static int zigZagDecode(int i) {
        // (i >>> 1)：还原数据位，最后和符号位做异或解码
        // -(i & 1)：还原符号位，i的最后一位是符号位，该表达式把每一位都设置为符号位
        return ((i >>> 1) ^ -(i & 1));
    }

    @Test
    public void testTemp(){
        System.out.println(10&1);
        System.out.println(Integer.toBinaryString(-1));
        System.out.println(Integer.toBinaryString(-2));
        System.out.println(Integer.toBinaryString(-3));

    }
    @Test
    public void testNegative() {
        //测试负数
        runZigZagEncode(-1);
        runZigZagEncode(-2);
        runZigZagEncode(-3);
        runZigZagEncode(-10);
    }

    @Test
    public void testPositive() {
        for (int i = 1; i < 10; ++i) {
            runZigZagEncode(i);
        }
    }

    private static void runZigZagEncode(int i){
        int v = zigZagEncode(i);
        System.out.println(i + " => " + v + "\t "+Integer.toBinaryString(i)+"=>"+ Integer.toBinaryString(v));
    }


}
