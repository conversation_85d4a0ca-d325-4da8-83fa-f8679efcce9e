package com.buzz.lucene.packed;

import org.junit.Test;

public class BulkOperationPackedTest {


    @Test
    public void testMedia() {
        //bitsPerValue为12时，values:blocks是2:3，每2个数字需要3个byte来存储,结构为[8],[4,4],[8],[8],[4,4],[8]
        long[] values = new long[]{1115, 1116, 1117, 1118};
        byte[] blocks = new byte[6];

        new MyBulkOperationPacked(12).encode(values, 0, blocks, 0);
        printResult(blocks);
    }

    @Test
    public void testSmall() {
        //bitsPerValue为6时，value比blocks为4:3， 结构为[6,2],[4,4],[2,6]
        long[] values = new long[]{37,38,39,40};//4
        byte[] blocks = new byte[3];//3
        new MyBulkOperationPacked(6).encode(values, 0, blocks, 0);
        printResult(blocks);
    }

    private void printResult(byte[] blocks){
        System.out.println("==============");
        for (byte block : blocks) {
            System.out.println(block);
        }
    }
    static class MyBulkOperationPacked {

        private int bitsPerValue = 12;

        public MyBulkOperationPacked(int bitsPerValue) {
            this.bitsPerValue = bitsPerValue;
        }

        //把long[] 编码成byte[]
        public void encode(long[] values, int valuesOffset, byte[] blocks, int blocksOffset) {
            int nextBlock = 0; //当前块
            int bitsLeft = 8;  //当前剩余可用的bit位

            for (int i = 0; i < values.length; ++i) {
                final long v = values[valuesOffset++];

                System.out.println("bitsLeft=" + bitsLeft +"\t"+v);

                if (bitsPerValue < bitsLeft) {//当bitsPerValue小于一个字节，也就是value小于128
                    //直接保存到当前块
                    nextBlock |= v << (bitsLeft - bitsPerValue);//左位移到高位并暂存到当前块
                    bitsLeft -= bitsPerValue; //当前块还剩余的可用bit位
                } else {
                    // 当前值无法完全放入当前块中，保存到数组
                    int bits = bitsPerValue - bitsLeft;

                    // 将当前块和value的高位存入块数组
                    blocks[blocksOffset++] = (byte) (nextBlock | (v >>> bits));

                    // 如果待写入位数超过8位，直接将剩余的位数存入块数组
                    while (bits >= 8) {
                        bits -= 8;
                        blocks[blocksOffset++] = (byte) (v >>> bits);
                    }

                    bitsLeft = 8 - bits;
                    System.out.println("bits="+bits+"\t"+v);
                    //保存剩余位数，并提到最高位
                    nextBlock = (int) ((v & ((1L << bits) - 1)) << bitsLeft);
                }
            }
        }
    }
}
