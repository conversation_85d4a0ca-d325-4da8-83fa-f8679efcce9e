package com.buzz.lucene;

import com.buzz.util.TextTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.document.Document;
import org.apache.lucene.index.IndexWriter;
import org.apache.lucene.index.Term;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.queryparser.classic.QueryParser;
import org.apache.lucene.search.*;
import org.apache.lucene.util.QueryBuilder;

import java.io.IOException;
import java.util.ArrayList;
import java.util.function.Consumer;

public class LuceneSearcher {

    private static final Consumer<ArrayList<DocumentInfo>> QUERY_HANDLER = (documentInfos) -> {
        TextTable table = new TextTable();
        table.addRow("id", "tile", "message");
        for (DocumentInfo documentInfo : documentInfos) {
            Document document = documentInfo.getDocument();
            int id = documentInfo.getId();
            String title = document.getField("title").stringValue();
            String messageContent = document.getField("message").stringValue();
            messageContent = messageContent.length() > 49 ? messageContent.substring(0, 49) : messageContent;

            table.addRow(
                    String.valueOf(id),
                    title,
                    messageContent
            );
        }
        System.out.println(table.toString());
    };

    public static void queryByTerm(String topic, String message, Sort sort) {
        Query query = new TermQuery(new Term("message", message));
        query(topic, query, sort, QUERY_HANDLER);
    }

    //第1种方式 通过QueryBuilder，ES采用这种
    public static void queryByText(String topic, String message, Sort sort) {
        QueryBuilder queryBuilder = new QueryBuilder(new StandardAnalyzer());
        Query query = queryBuilder.createBooleanQuery("message", message);
        query(topic, query, sort, QUERY_HANDLER);
    }

    //第2种方式 通过QueryParser, QueryParser把字符串作为Lucene Query String（查询字符串）进行解析
    public static void queryByString(String topic, String queryString, Sort sort) {
        QueryParser parser = new QueryParser("message", new StandardAnalyzer());
        try {
            Query query = parser.parse(queryString);
            query(topic, query, sort, QUERY_HANDLER);
        } catch (ParseException e) {
            e.printStackTrace();
        }
    }

    public static void query(String topic, Query query, Sort sort) {
        query(topic, query, sort, QUERY_HANDLER);
    }

    public static void query(String topic, Query query, Sort sort, Consumer<ArrayList<DocumentInfo>> consumer) {
        System.out.println("Your query=" + query + " [" + query.getClass().getSimpleName() + "] sort=" + sort);
        consumer.accept(doQuery(topic, query, sort));
    }

    public static ArrayList<DocumentInfo> doQuery(String topic, Query query, Sort sort) {
        IndexWriter writer = null;
        try {
            writer = LuceneBuilder.indexWriterBuilder()
                    .topic(topic)
                    .build();
            IndexSearcher searcher = LuceneBuilder.indexSearcherBuilder()
                    .writer(writer)
                    .build();

            TopDocs topDocs = null;
            if (sort != null) {
                topDocs = searcher.search(query, 10, sort);
            } else {
                topDocs = searcher.search(query, 10);
            }
            ArrayList<DocumentInfo> documents = new ArrayList<>();
            for (ScoreDoc scoreDoc : topDocs.scoreDocs) {
                int docId = scoreDoc.doc;
                Document doc = searcher.doc(docId);
                documents.add(new DocumentInfo(docId, doc));
            }
            return documents;
        } catch (Exception e) {
            throw new RuntimeException(e);
        } finally {
            try {
                writer.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }


    }

    @Data
    @AllArgsConstructor
    public static class DocumentInfo {
        private int id;
        private Document document;
    }

}
