package com.buzz.lucene;

import org.apache.lucene.util.MathUtil;
import org.junit.Test;

public class LuceneTempTest {

    static int skipMultiplier = 8;

    private void log(int i) {
        int value = MathUtil.log(i, skipMultiplier);
        System.out.println(i + "=> " + value);
    }

    @Test
    public void testMathLog() {

        for (int i = 8; i < 10000; i = i * 8) {
            log(i);
        }

        log(366);
    }

}
