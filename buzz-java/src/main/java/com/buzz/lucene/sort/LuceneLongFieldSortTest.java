package com.buzz.lucene.sort;

import com.buzz.lucene.LuceneBaseTest;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.LongPoint;
import org.apache.lucene.document.StoredField;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.search.Query;
import org.junit.Test;

import java.io.IOException;
import java.util.function.BiConsumer;

/**
 * 该单元测试会报错！
 * 原因是排序必须要使用DocValues
 */
public class LuceneLongFieldSortTest extends LuceneBaseTest {
    @Override
    protected BiConsumer<Document, Long> getIndexBuilder() {
        return (document, i) -> {
            document.add(new LongPoint("age", i));//倒排索引
            document.add(new StoredField("age", i));

            document.add(new LongPoint("version", i));//倒排索引
            document.add(new StoredField("version", i));
        };
    }

    @Override
    protected Query getQuery() throws ParseException {
        return super.must();
    }


    @Test
    public void testWrite() throws IOException {
        super.write("LongPoint");
    }

    @Test
    public void testRead() throws IOException {
        testWrite();
        super.read("LongPoint");
    }
}
