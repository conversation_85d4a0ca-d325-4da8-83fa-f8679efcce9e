package com.buzz.lucene.sort;

import com.buzz.lucene.LuceneBaseTest;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.LongPoint;
import org.apache.lucene.document.SortedNumericDocValuesField;
import org.apache.lucene.document.StoredField;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.search.Query;
import org.apache.lucene.search.SortField;
import org.apache.lucene.search.SortedNumericSelector;
import org.apache.lucene.search.SortedNumericSortField;
import org.junit.Test;

import java.io.IOException;
import java.util.function.BiConsumer;

public class LuceneSortedNumericDocValuesSortTest extends LuceneBaseTest {
    @Override
    protected BiConsumer<Document, Long> getIndexBuilder() {
        return (document, i) -> {

            document.add(new LongPoint("age", i));//倒排索引
            document.add(new StoredField("age", i));
            document.add(new SortedNumericDocValuesField("age", i));
            document.add(new SortedNumericDocValuesField("age", i * 10));

            document.add(new LongPoint("version", i));
            document.add(new StoredField("version", size() - i));
            document.add(new SortedNumericDocValuesField("version", 1100 - i));
        };
    }

    @Override
    protected Query getQuery() throws ParseException {
        return super.must();
    }

    @Override
    protected SortField getSortField(){
        //SortedNumericDocValuesField可以包含多个值，比如使用SortedNumericSortField
        return new SortedNumericSortField("age", SortField.Type.LONG, true, SortedNumericSelector.Type.MAX);
    }


    @Test
    public void testWrite() throws IOException {
        super.write("LongPoint");
    }

    @Test
    public void testRead() throws IOException {
        testWrite();
        super.read("LongPoint");
    }
}
