package com.buzz.lucene.sort;

import com.buzz.lucene.LuceneBaseTest;
import org.apache.lucene.document.Document;
import org.apache.lucene.document.LongPoint;
import org.apache.lucene.document.NumericDocValuesField;
import org.apache.lucene.document.StoredField;
import org.apache.lucene.queryparser.classic.ParseException;
import org.apache.lucene.search.Query;
import org.junit.Test;

import java.io.IOException;
import java.util.Random;
import java.util.function.BiConsumer;


/**
 * 测试发现现排序必须要使用DocValues
 * <p>
 * 基于DocValues（NumericDocValuesField） 的排序
 */
public class LuceneNumericDocValuesSortTest extends LuceneBaseTest {
    private long age = 1115;
    private Random random = new Random();
    @Override
    protected BiConsumer<Document, Long> getIndexBuilder() {
        return (document, i) -> {
            //age += 1;
            age=random.nextInt(1000000);
            document.add(new LongPoint("age", age));//倒排索引
            document.add(new StoredField("age", age));
            document.add(new NumericDocValuesField("age", age));


        };
    }

    @Override
    protected Query getQuery() throws ParseException {
        return super.must();
    }

    @Test
    public void testWrite() throws IOException {
        super.write("LongPoint");
    }

    @Test
    public void testRead() throws IOException {
        super.read("LongPoint");
    }
}
