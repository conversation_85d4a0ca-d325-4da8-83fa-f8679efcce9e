package com.buzz.lucene;

import org.apache.lucene.analysis.Analyzer;
import org.apache.lucene.analysis.TokenStream;
import org.apache.lucene.analysis.core.WhitespaceAnalyzer;
import org.apache.lucene.analysis.en.EnglishAnalyzer;
import org.apache.lucene.analysis.standard.StandardAnalyzer;
import org.apache.lucene.analysis.tokenattributes.CharTermAttribute;
import org.apache.lucene.analysis.tokenattributes.OffsetAttribute;
import org.apache.lucene.analysis.tokenattributes.PositionIncrementAttribute;
import org.junit.Test;

import java.io.IOException;

/**
 * +----------+     +-----------+     +--------+
 * | Analyzer | --> | Tokenizer | --> | Filter |
 * +----------+     +-----------+     +--------+
 *
 * <AUTHOR>
 * @description
 **/
public class LuceneAnalyzerTest {

    /**
     *
     * PositionIncrementAttribute 用于确定此 token 相对于 TokenStream 中前一个token的位置，用于 phrase 搜索。
     *
     * https://self-learning-java-tutorial.blogspot.com/2021/06/lucene-positionincrementattribute-get.html
     *
     * 也可以参考es  TransportAnalyzeAction.simpleAnalyze
     * @throws IOException
     */
    @Test
    public void test_Analyzers() throws IOException {
        evaluateAnalyzer(new WhitespaceAnalyzer(),"Java is a Programming Language");
        System.out.println("==========");
        evaluateAnalyzer(new StandardAnalyzer(),"Java is a Programming Language");
        System.out.println("==========");
        evaluateAnalyzer(new EnglishAnalyzer(),"Java is a Programming Language");
    }


    public void evaluateAnalyzer(Analyzer analyzer,String message) throws IOException {
        TokenStream tokenStream = analyzer.tokenStream("content", message);
        //term
        CharTermAttribute charTermAttribute = tokenStream.addAttribute(CharTermAttribute.class);
        //offset
        OffsetAttribute offset = tokenStream.addAttribute(OffsetAttribute.class);
        //PositionIncrement
        PositionIncrementAttribute posIncr = tokenStream.addAttribute(PositionIncrementAttribute.class);
        tokenStream.reset();
        int pos = -1;
        while (tokenStream.incrementToken()) {
            String token = charTermAttribute.toString();
            int increment = posIncr.getPositionIncrement();
            if (increment > 0) {
                pos = pos + increment;
            }
            System.out.printf("%s: %d -> %d ,%d\n", token, offset.startOffset(), offset.endOffset(),pos);
        }
        tokenStream.end();
    }


}
