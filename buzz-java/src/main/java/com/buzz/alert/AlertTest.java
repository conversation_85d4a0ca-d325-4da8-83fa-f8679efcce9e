package com.buzz.alert;

import org.junit.Test;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

public class AlertTest {

    private Alert alert = createAlert("http://crow.cloud.wke-office.test.wacai.info/crow/api/v1/", "81285f912da1c20317ccfc52445924e9", "bairen");

    private Alert createAlert(String crowURL, String token, String receivers) {
        Reporter crowHttpReporter = CrowHttpReporter.builder()
                .crowURL(crowURL)
                .token(token)
                .receivers(receivers)
                .encoder(new DefaultEncoder())
                .build();
        Reporter wrapper = (list) -> {
            //we can add a switch in here
            crowHttpReporter.send(list);
        };
        Alert alert = new CoolDownAlert(wrapper, 1, TimeUnit.MINUTES);
        return alert;
    }

    @Test
    public void testCoolDown() throws IOException {
        alert.send(AlarmMsg.builder().message("test").build());
        alert.close();
    }
}
