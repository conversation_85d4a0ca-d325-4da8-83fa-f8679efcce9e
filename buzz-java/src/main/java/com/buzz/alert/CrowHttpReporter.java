package com.buzz.alert;

import com.buzz.util.HttpUtil;
import lombok.Builder;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Builder
public class CrowHttpReporter implements Reporter {

    private String crowURL;
    private String token;
    private String receivers;
    private String contentType;
    private String method;
    private Encoder encoder;

    @Override
    public void send(Collection<AlarmMsgInfo> messages) throws Exception {
        setDefaultValue();
        for (AlarmMsgInfo message : messages) {
            send(encoder.encode(message));
        }
    }

    protected void send(String msg) throws Exception {
        //send post
        HttpUtil.builder().build().httpPostJson(checkCrowURL(), getHeader(), convert(msg));
    }

    private void setDefaultValue() {
        if (encoder == null) {
            throw new IllegalArgumentException("encoder must not be null");
        }
        if (contentType == null) {
            contentType = "text";
        }
        if (method == null) {
            method = "wechat";
        }
        if (receivers == null) {
            receivers = "bairen";
        }
    }

    private String checkCrowURL() {
        //append last /
        String url = crowURL;
        if (url.charAt(url.length() - 1) != '/') {
            url += "/";
        }
        //append method
        url += method;
        return url;
    }

    private Map<String, String> getHeader() {
        Map<String, String> header = new HashMap<>();
        header.put("X-Login-User", "sailfish");
        header.put("X-Token", token);
        return header;
    }

    private Map<String, Object> convert(String message) {
        Map data = new HashMap();
        data.put("content", message);
        data.put("type", contentType);
        data.put("tos", Stream.of(receivers.split(",")).collect(Collectors.toSet()));
        return data;
    }
}
