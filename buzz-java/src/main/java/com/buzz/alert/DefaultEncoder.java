package com.buzz.alert;

import com.buzz.util.AppUtils;
import com.buzz.util.FormatUtils;
import com.buzz.util.NetUtils;
import com.buzz.util.SystemToolkit;

import java.util.concurrent.TimeUnit;

public class DefaultEncoder implements Encoder {

    @Override
    public String encode(AlarmMsgInfo msgInfo) throws Exception {
        AlarmMsg msg = msgInfo.getAlarmMsg();
        StringBuilder sb = new StringBuilder();
        String exceptionMsg = msg.getException() == null ? "none" : msg.getException().getClass().getSimpleName() + ":" + msg.getException().getMessage();
        sb.append("[" + getAppName() + " " + getEnvName() + "环境告警] \r\n");
        sb.append(formatTime(msgInfo));
        sb.append("主机: " + NetUtils.getLocalHost() + "\r\n");
        sb.append("内容: " + msg.getMessage() + "\r\n");
        sb.append("异常: " + exceptionMsg + "\r\n");
        return sb.toString();
    }

    protected String formatTime(AlarmMsgInfo msgInfo) {
        if (msgInfo.getCount().get() == 1) {
            return "时间: " + FormatUtils.toDateTimeString(msgInfo.getStartTime()) + "\r\n";
        } else {
            String format = "时间: %s ，发生%s次，持续%s分钟 \r\n";
            return String.format(format,
                    FormatUtils.toDateTimeString(msgInfo.getStartTime()),
                    msgInfo.getCount().get(),
                    TimeUnit.MILLISECONDS.toSeconds(msgInfo.getCoolDownTimeMs())
                    );
        }
    }

    protected String getAppName() {
        return AppUtils.APP_ARTIFACT_ID;
    }

    protected String getEnvName() {
        String appEnv = SystemToolkit.getSystemProperty("APP_ENV");
        return appEnv == null ? "dev" : appEnv;
    }
}
