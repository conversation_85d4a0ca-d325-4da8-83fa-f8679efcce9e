package com.buzz.alert;

import lombok.extern.slf4j.Slf4j;

import java.util.*;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
public class CoolDownAlert implements Alert, Runnable {

    private ArrayBlockingQueue<AlarmMsg> queue = new ArrayBlockingQueue<AlarmMsg>(1024);
    private Reporter report;
    private AtomicBoolean running = new AtomicBoolean(true);
    private CountDownLatch waitClose = new CountDownLatch(1);
    private long coolDownMs;
    private Map<String, AlarmMsgInfo> alarmMsgInfoMap = new ConcurrentHashMap<>();
    private Thread thread;

    public CoolDownAlert(Reporter report) {
        this(report, 5, TimeUnit.MINUTES);
    }

    public CoolDownAlert(Reporter report,
                         long coolDownTime,
                         TimeUnit unit) {
        this.report = report;
        this.coolDownMs = unit.convert(coolDownTime, TimeUnit.MILLISECONDS);
        startThread();
    }

    private void startThread() {
        thread = new Thread(this, "async-alert-thread");
        thread.setDaemon(true);
        thread.start();
    }

    @Override
    public void send(AlarmMsg alarmMsg) {
        if (!queue.offer(alarmMsg)) {
            log.warn("send alarm too frequently");
        }
    }

    @Override
    public void run() {
        while (running.get()) {
            try {
                AlarmMsg alarmMsg = queue.take();
                List<AlarmMsg> alarmMsgList = new ArrayList<>();
                List<AlarmMsgInfo> alarmMsgInfoList = new ArrayList<>();
                alarmMsgList.add(alarmMsg);
                queue.drainTo(alarmMsgList);
                long now = System.currentTimeMillis();

                for (Iterator<AlarmMsg> it = alarmMsgList.iterator(); it.hasNext(); ) {
                    AlarmMsg msg = it.next();
                    String category = msg.getCategory() == null ? "default" : msg.getCategory();
                    AlarmMsgInfo alarmMsgInfo = alarmMsgInfoMap.computeIfAbsent(category, (k) -> new AlarmMsgInfo(
                            msg,
                            now,
                            now + coolDownMs,
                            new AtomicInteger()));
                    if (alarmMsgInfo.getCount().get() == 0) {
                        alarmMsgInfoList.add(alarmMsgInfo);
                    }
                    //超过冷却时间删除
                    if (now > alarmMsgInfo.getCoolDownTimeMs()) {
                        alarmMsgInfoMap.remove(category);
                    }
                    //增加计数器
                    alarmMsgInfo.getCount().incrementAndGet();
                }

                batchSend(alarmMsgInfoList);
            } catch (InterruptedException e) {
                break;
            } catch (Exception e) {
                log.info("send alert error", e);
            }
        }
        waitClose.countDown();
    }

    private void batchSend(Collection<AlarmMsgInfo> parcel) {
        try {
            report.send(parcel);
        } catch (Exception e) {
            log.info("send alert error", e);
        }
    }

    @Override
    public void close() {
        if (running.compareAndSet(true, false)) {
            try {
                thread.interrupt();
                waitClose.await(2, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
            }
        }
    }


}
