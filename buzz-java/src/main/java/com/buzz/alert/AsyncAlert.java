package com.buzz.alert;

import lombok.extern.slf4j.Slf4j;

import java.util.Collection;
import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 会在内存憋1一秒再批量提交
 */
@Slf4j
public class AsyncAlert implements Alert, Runnable {

    private ArrayBlockingQueue<AlarmMsg> queue = new ArrayBlockingQueue<AlarmMsg>(1024);
    private Reporter report;
    private AtomicBoolean running = new AtomicBoolean(true);
    private CountDownLatch waitClose = new CountDownLatch(1);

    public AsyncAlert(Reporter report) {
        this.report = report;
        Thread thread = new Thread(this, "async-alert-thread");
        thread.setDaemon(true);
        thread.start();
    }

    @Override
    public void send(AlarmMsg msg) {
        if (!queue.offer(msg)) {
            log.warn("send msg too frequently");
        }
    }

    @Override
    public void run() {
        long lastSendTime = System.currentTimeMillis();
        Set<AlarmMsg> parcel = new HashSet<>();
        while (running.get()) {
            try {
                AlarmMsg msg = queue.poll(1, TimeUnit.SECONDS);
                if (msg == null && parcel.isEmpty()) {
                    continue;
                }
                if (msg != null) {
                    parcel.add(msg);
                    queue.drainTo(parcel);
                }
                long now = System.currentTimeMillis();
                if ((now - lastSendTime) > 1000) {
                    batchSend(parcel);
                    parcel.clear();
                }
            } catch (InterruptedException e) {
                break;
            } catch (Exception e) {
                parcel.clear();
                log.info("send alert error", e);
            }
        }
        waitClose.countDown();
    }

    private void batchSend(Collection<AlarmMsg> parcel) {
        try {
            //report.send(parcel);
        } catch (Exception e) {
            log.info("send alert error", e);
        }
    }

    @Override
    public void close() {
        if (running.compareAndSet(true, false)) {
            try {
                waitClose.await(2, TimeUnit.SECONDS);
            } catch (InterruptedException e) {
            }
        }
    }
}
