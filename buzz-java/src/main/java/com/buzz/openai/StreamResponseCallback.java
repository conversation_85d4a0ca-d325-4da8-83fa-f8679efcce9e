package com.buzz.openai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Call;
import okhttp3.Callback;
import okhttp3.Response;
import org.jetbrains.annotations.NotNull;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.concurrent.SubmissionPublisher;

@Slf4j
public class StreamResponseCallback implements Callback {

    SubmissionPublisher<String> publisher;

    public StreamResponseCallback(SubmissionPublisher<String> publisher) {
        this.publisher = publisher;
    }

    @Override
    public void onFailure(@NotNull Call call, @NotNull IOException e) {
        e.printStackTrace();
        call.cancel();
        publisher.close();
    }

    @Override
    public void onResponse(@NotNull Call call, @NotNull Response response) throws IOException {
        log.info("receive response");

        if (!response.isSuccessful()) {
            System.err.println("Request failed: " + response.code());
            System.err.println(response.body().string());
            publisher.close();
            return;
        }

        // 处理流式响应
        try (BufferedReader reader = new BufferedReader(response.body().charStream())) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (!line.isBlank()) {
                    //System.out.println(line);
                    //收到结束关闭
                    if (line.equals("data: [DONE]")) {
                        //log.info("receive DONE");
                        call.cancel();
                        publisher.close();
                        break;
                    }
                    //删除data:
                    line = line.substring("data: ".length());
                    JSONObject json = JSON.parseObject(line);

                    for (JSONObject item : json.getJSONArray("choices").toArray(new JSONObject[0])) {
                        String finishReason = item.getString("finish_reason");
                        if (finishReason != null && finishReason.equals("stop")) {
                            //finish_reason包括：
                            // null: 生成尚未完成（更多内容会继续流回来）
                            // stop: 模型生成已完成，并返回了完整的内容
                            // length: 生成因长度限制而提前终止。
                            // content_filter: 生成被内容过滤器中断
                            //System.out.println("\r\n stop");
                            //System.out.println(line);
                            break;
                        }
                        //获取内容
                        JSONObject delta = item.getJSONObject("delta");
                        if (delta != null) {
                            String content = delta.getString("content");
                            publisher.submit(content);
                        }
                    }
                    //System.out.println("Received chunk: " + line);
                }
            }
        }
    }
}
