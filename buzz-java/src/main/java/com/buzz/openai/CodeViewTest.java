package com.buzz.openai;

import com.buzz.util.IOUtils;
import com.buzz.util.Md5Util;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Slf4j
public class CodeViewTest {

    protected static final Pattern CODE_MATCHER = Pattern.compile("```([\\w#+]*\n*)?(.*?)`{2,3}|`{2,3}([\\w#+]+\n*)?(.*)", 32);

    @Test
    public void test() throws IOException {
        List<MarkdownBlock> markdownBlockList = parseBlock(IOUtils.readFile("/tmp/test.txt"));
        log.info("size={}", markdownBlockList.size());
        for (MarkdownBlock block : markdownBlockList) {
            log.info("language:[{}] {}",block.getLanguage(),block.content);
        }

    }


    private static List<MarkdownBlock> parseBlock(String markdown) {
        ArrayList<MarkdownBlock> result = new ArrayList<MarkdownBlock>();
        StringBuilder sb = new StringBuilder();
        Matcher m = CODE_MATCHER.matcher(markdown);
        while (m.find()) {
            MarkdownBlock codeBlock;
            String content = m.group(2);
            String language = m.group(1);
            if (content != null) {
                codeBlock = new MarkdownBlock(content, "code", language);
            } else {
                content = m.group(4);
                language = m.group(3);
                codeBlock = new MarkdownBlock(content, "code", false, language);
            }
            m.appendReplacement(sb, "");
            if (!"".equals(sb.toString().trim())) {
                MarkdownBlock textBlock = new MarkdownBlock(sb.toString(), "content");
                result.add(textBlock);
            }
            result.add(codeBlock);
            sb.setLength(0);
        }
        m.appendTail(sb);
        if (!"".equals(sb.toString().trim())) {
            MarkdownBlock textBlock = new MarkdownBlock(sb.toString(), "content", false);
            result.add(textBlock);
        }
        return result;
    }

    @Data
    static class MarkdownBlock {

        public static final String TEXT_BLOCK = "content";
        public static final String CODE_BLOCK = "code";
        private String identifier;
        private String content;
        private String type;
        private String language;
        private boolean completed;
        private boolean needUpdate = true;

        public MarkdownBlock(String content, String type) {
            this.content = content;
            this.type = type;
            this.completed = true;
            this.identifier = Md5Util.encode((byte[]) content.getBytes());
        }

        public MarkdownBlock(String content, String type, boolean completed) {
            this.content = content;
            this.type = type;
            this.completed = completed;
            this.identifier = Md5Util.encode((byte[]) content.getBytes());
        }

        public MarkdownBlock(String content, String type, String language) {
            this.content = content;
            this.type = type;
            this.completed = true;
            this.language = language == null ? null : language.trim();
            this.identifier = Md5Util.encode((byte[]) content.getBytes());
        }

        public MarkdownBlock(String content, String type, boolean completed, String language) {
            this.content = content;
            this.type = type;
            this.completed = completed;
            this.language = language == null ? null : language.trim();
            this.identifier = Md5Util.encode((byte[]) content.getBytes());
        }
    }
}
