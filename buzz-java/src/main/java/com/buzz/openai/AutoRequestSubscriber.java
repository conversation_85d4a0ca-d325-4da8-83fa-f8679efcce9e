package com.buzz.openai;

import java.util.concurrent.Flow;

public abstract class AutoRequestSubscriber<T> implements Flow.Subscriber<T> {

    private Flow.Subscription subscription;

    @Override
    public void onSubscribe(Flow.Subscription subscription) {
        this.subscription = subscription;
        subscription.request(1); // 初始请求一个数据
    }

    @Override
    public void onNext(T item) {
        try {
            handleItem(item); // 处理数据
        } finally {
            subscription.request(1); // 自动请求下一个数据
        }
    }

    @Override
    public void onError(Throwable throwable) {
        throwable.printStackTrace(); // 默认打印错误
    }

    @Override
    public void onComplete() {
        System.out.println("Completed!"); // 默认完成日志
    }

    // 抽象方法，子类实现具体数据处理逻辑
    protected abstract void handleItem(T item);
}