package com.buzz.openai;

import okhttp3.*;

import java.util.concurrent.Flow;
import java.util.concurrent.SubmissionPublisher;

public class LlmProvider {

    private static final String API_URL = "http://chatgpt.tiny-test.wke-office.test.wacai.info/";
    private static final String API_KEY = "45a7a7914f0b4bac807f03088d97f20a";


    public static Flow.Publisher<String> stream(){
        // 创建 SubmissionPublisher (Publisher 的默认实现)
        SubmissionPublisher<String> publisher = new SubmissionPublisher<>();

        OkHttpClient client = new OkHttpClient();

        // 构造 JSON 请求体
        String requestBody = """
                    {
                        "model": "gpt-4",
                        "messages": [
                            {"role": "system", "content": "You are a helpful assistant."},
                            {"role": "user", "content": "Hello, Who are you?"}
                        ],
                        "stream": true
                    }
                """;

        String url = API_URL + "/openai/deployments/gpt-4o-omni/chat/completions?api-version=2024-06-01";
        Request request = new Request.Builder()
                .url(url)
                .header("api-key", API_KEY)
                .header("Content-Type", "application/json")
                .post(RequestBody.create(requestBody, MediaType.get("application/json")))
                .build();

        // 异步请求以处理流式响应
        Call call = client.newCall(request);
        call.enqueue(new StreamResponseCallback(publisher));
        return publisher;
    }
}
