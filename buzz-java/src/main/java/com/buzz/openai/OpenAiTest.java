package com.buzz.openai;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.theokanning.openai.completion.chat.ChatMessage;
import com.theokanning.openai.completion.chat.ChatMessageRole;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.junit.Test;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Flow;

@Slf4j
public class OpenAiTest {

    private static final String API_URL = "http://chatgpt.tiny-test.wke-office.test.wacai.info/";
    private static final String API_KEY = "45a7a7914f0b4bac807f03088d97f20a";

    @Test
    public void test101() throws InterruptedException {

        CountDownLatch latch = new CountDownLatch(1);
        OkHttpClient client = new OkHttpClient();

        // 构造 JSON 请求体
        String requestBody = """
                    {
                        "model": "gpt-4",
                        "messages": [
                            {"role": "system", "content": "You are a helpful assistant."},
                            {"role": "user", "content": "Hello, GPT-4! How can I use streaming?"}
                        ],
                        "stream": true
                    }
                """;

        String url = API_URL + "/openai/deployments/gpt-4o-omni/chat/completions?api-version=2024-06-01";
        Request request = new Request.Builder()
                .url(url)
                .header("api-key", API_KEY)
                .header("Content-Type", "application/json")
                .post(RequestBody.create(requestBody, MediaType.get("application/json")))
                .build();

        // 异步请求以处理流式响应
        Call call = client.newCall(request);
        call.enqueue(new Callback() {
            @Override
            public void onFailure(Call call, IOException e) {
                e.printStackTrace();
            }

            @Override
            public void onResponse(Call call, Response response) throws IOException {
                log.info("receive response");

                if (!response.isSuccessful()) {
                    System.err.println("Request failed: " + response.code());
                    System.err.println(response.body().string());
                    return;
                }

                // 处理流式响应
                try (BufferedReader reader = new BufferedReader(response.body().charStream())) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        if (!line.isBlank()) {
                            //System.out.println(line);
                            //收到结束关闭
                            if(line.equals("data: [DONE]")){
                                log.info("receive DONE");
                                call.cancel();
                                break;
                            }
                            //删除data:
                            line = line.substring("data: ".length());
                            JSONObject json = JSON.parseObject(line);

                            for (JSONObject item : json.getJSONArray("choices").toArray(new JSONObject[0])) {
                                String finishReason = item.getString("finish_reason");
                                if(finishReason!=null&& finishReason.equals("stop")){
                                    //finish_reason包括：
                                    // null: 生成尚未完成（更多内容会继续流回来）
                                    // stop: 模型生成已完成，并返回了完整的内容
                                    // length: 生成因长度限制而提前终止。
                                    // content_filter: 生成被内容过滤器中断
                                    break;
                                }
                                //获取内容
                                String content = item.getJSONObject("delta").getString("content");
                                System.out.print(content);

                            }
                            //System.out.println("Received chunk: " + line);
                        }
                    }
                }
                latch.countDown();
            }
        });

        latch.await();
    }


    //通过java9 响应式流实现
    @Test
    public void test102() throws InterruptedException {

        CountDownLatch latch = new CountDownLatch(1);
        Flow.Publisher<String> flow = LlmProvider.stream();
        flow.subscribe(new AutoRequestSubscriber<String>() {
            @Override
            protected void handleItem(String item) {
                System.out.print(item);
            }

            public void onComplete() {
                System.out.println();
                log.info("completed");
                latch.countDown();
            }
        });

        latch.await();
    }
}
