package com.buzz.event;

import java.lang.reflect.GenericArrayType;
import java.lang.reflect.ParameterizedType;

public class ClassUtils {

	/**
	 * 为指定类型实例化
	 * 
	 * @param <T>
	 * @param type
	 * @return
	 */
	public static <T> T newInstance(Class<T> type) {
		try {
			return type.newInstance();
		} catch (InstantiationException | IllegalAccessException e) {
			throw new RuntimeException(e);
		}
	}

	/**
	 * 获取类的泛型类
	 * 
	 * @param type
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> Class<T> getGenericClass(Class<T> type) {

		ParameterizedType superclass = (ParameterizedType) (type.getGenericSuperclass());
		if (superclass == null) {
			throw new IllegalStateException(type + " is not generic class");
		}
		return (Class<T>) superclass.getActualTypeArguments()[0];
	}

	/**
	 * 获取接口的泛型类
	 * 
	 * @param <T>
	 * @param type
	 * @param index，多个接口的位置
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public static <T> Class<T> getGenericInterface(Class<T> cls, int index) {

		ParameterizedType parameterizedType = ((ParameterizedType) cls.getGenericInterfaces()[0]);
		Object genericClass = parameterizedType.getActualTypeArguments()[index];

		if (genericClass instanceof ParameterizedType) { // 处理多级泛型
			return (Class<T>) ((ParameterizedType) genericClass).getRawType();
		} else if (genericClass instanceof GenericArrayType) { // 处理数组泛型
			return (Class<T>) ((GenericArrayType) genericClass).getGenericComponentType();
		} else {
			return (Class<T>) genericClass;
		}

	}

	/**
	 * 获取接口的泛型类
	 * 
	 * @param <T>
	 * @param type
	 * @return
	 */
	public static <T> Class<T> getGenericInterface(Class<T> type) {
		return getGenericInterface(type, 0);
	}
}
