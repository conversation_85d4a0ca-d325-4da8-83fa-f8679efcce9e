package com.buzz.event;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;


 

/**
 * 
 * <AUTHOR>
 * @Component
 */
public class EventService {

	private final Map<Class<?>, List<WseEventHandler<?>>> registry = new ConcurrentHashMap<>();

	public void register(WseEventHandler<?> handler) {
		Class<?> cls = null;
		
		if(handler.getClass().isInterface()) {
			cls = ClassUtils.getGenericInterface(handler.getClass());
		}else {
			cls = ClassUtils.getGenericClass(handler.getClass());
		}
		
		List<WseEventHandler<?>> handlers = registry.get(cls);
		if (handlers == null) {
			handlers = new ArrayList<WseEventHandler<?>>();
			registry.put(cls, handlers);
		}
		handlers.add(handler);
	}

	public void publish(WseEvent<?> event) {

		List<WseEventHandler<?>> handlers = registry.get(event.getClass());
		fireEvent(handlers, event);

		handlers = registry.get(WseEvent.class);
		fireEvent(handlers, event);
	}

	@SuppressWarnings({ "rawtypes", "unchecked" })
	private void fireEvent(List<WseEventHandler<?>> handlers, WseEvent<?> event) {
		if (handlers != null) {
			for (WseEventHandler hander : handlers) {
				hander.onEvent(event);
			}
		}
	}

}
