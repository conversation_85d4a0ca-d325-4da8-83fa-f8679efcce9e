package com.buzz.tongyi;

public enum AuthWhitelistStatusEnum {
    NOT_WHITELIST(1),
    WAIT_PASS(2),
    PASS(3),
    UNKNOWN(4),
    NO_LICENCE(5),
    ORG_ORDER_EXPIRED(6);

    private int value;

    private AuthWhitelistStatusEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return this.value;
    }

    public static AuthWhitelistStatusEnum getAuthStateEnum(int value) {
        for (AuthWhitelistStatusEnum authStateEnum : AuthWhitelistStatusEnum.values()) {
            if (authStateEnum.getValue() != value) continue;
            return authStateEnum;
        }
        return null;
    }
}