package com.buzz.tongyi;

import org.junit.Test;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.List;

public class MarsCodeTest {


    @Test
    public void test() {
        //String test = "Generate API test request (with `http request` code block) based on given code and request/response info.\nSo that we can use it to test for APIs.\n\n// base URL route: \n// compare this request body relate info:\n// \n// compare this related class maybe you can use\n// []\n\nYou should use the following format to output API, which use Intellij platform `http request` language:\n\n```http request\nGET https://examples.http-client.intellij.net/get\n\nPOST http://localhost:80/api/item\nContent-Type: application/json\n{\n\"name\": \"item1\"\n}\n```\n\nGiven code:\n```Java\npublic void createNode(String path, String data) {\n\t\tcheckPath(path);\n\n\t\tint lastSlash = path.lastIndexOf('/');\n\t\tString parentName = path.substring(0, lastSlash);\n\t\tString childName = path.substring(lastSlash + 1);\n\n\t\tDataNode parent = nodes.get(parentName);\n\t\tif (parent == null) {\n\t\t\tthrow new NodeException(\"parent node is null! parentName:\" + parentName);\n\t\t}\n\n\t\tsynchronized (parent) {\n\n\t\t\tSet<String> children = parent.getChildren();\n\t\t\tif (children != null) {\n\t\t\t\tif (children.contains(childName)) {\nthrow new NodeException();\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tDataNode child = new DataNode(parent, data);\n\t\t\tparent.addChild(childName);\n\t\t\tnodes.put(path, child);\n\t\t}\n\n}\n```\n\n\nYou are working on a project that uses Spring MVC to build business logic.\n```Java\n\tpublic void createNode(String path, String data) {\n\n```";
        
        String test="cc.unitmesh.android.provider.AndroidChatContextProvider\nclass AndroidChatContextProvider {\n  \n  + @Override     public boolean isApplicable( Project project2, ChatCreationContext creationContext)\n  + @Override     @Nullable     public Object collect(@NotNull Project project2, @NotNull ChatCreationContext creationContext, @NotNull Continuation<? super List<ChatContextItem>> $completion)\n  + /*      * WARNING - void declaration      */     private final Integer getProjectAndroidTargetSdkVersion(Project project2)\n}";
        System.out.println(test);
    }

    public static void main(String[] args) {
        // 指定要加载jar文件的目录
        String jarDirPath = "/Users/<USER>/Downloads/marscode/marscode/lib";

        try {
            // 获取目录下所有的jar文件的URL列表
            List<URL> jarUrls = getJarUrls(jarDirPath);

            // 创建URLClassLoader
            URLClassLoader urlClassLoader = new URLClassLoader(jarUrls.toArray(new URL[0]));
            //Class<?> targetClass = urlClassLoader.loadClass("com.aiserver.services.AIServer");
            //System.out.println(targetClass);

            URL url = urlClassLoader.findResource("com/aiserver/services/AIServer.class");
            System.out.println(url);

            // 在这里可以使用加载的类，例如加载某个特定类并实例化
            // Class<?> loadedClass = urlClassLoader.loadClass("com.example.SomeClass");
            // Object instance = loadedClass.newInstance();

            // 记得关闭类加载器（如果需要）
            // urlClassLoader.close();

        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static List<URL> getJarUrls(String jarDirPath) throws MalformedURLException {
        List<URL> jarUrls = new ArrayList<>();
        File jarDir = new File(jarDirPath);
        if (jarDir.exists() && jarDir.isDirectory()) {
            File[] jarFiles = jarDir.listFiles((dir, name) -> name.endsWith(".jar"));
            if (jarFiles != null) {
                for (File jarFile : jarFiles) {
                    jarUrls.add(jarFile.toURI().toURL());
                }
            }
        }
        return jarUrls;
    }
}
