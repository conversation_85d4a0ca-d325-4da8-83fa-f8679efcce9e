package com.buzz.tongyi;

import org.eclipse.lsp4j.jsonrpc.Endpoint;
import org.eclipse.lsp4j.jsonrpc.Launcher;
import org.eclipse.lsp4j.jsonrpc.RemoteEndpoint;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;
import org.eclipse.lsp4j.jsonrpc.services.ServiceEndpoints;
import org.eclipse.lsp4j.jsonrpc.MessageConsumer;
import javax.websocket.Session;
import java.util.Collection;
import java.util.Map;

public class CosyWebSocketLauncherBuilder<T> extends Launcher.Builder<T> {
    protected Session session;

    public Collection<Object> getLocalServices() {
        return this.localServices;
    }

    public CosyWebSocketLauncherBuilder<T> setSession(Session session) {
        this.session = session;
        return this;
    }

    public Launcher<T> create() {
        if (this.localServices == null) {
            throw new IllegalStateException("Local service must be configured.");
        }
        if (this.remoteInterfaces == null) {
            throw new IllegalStateException("Remote interface must be configured.");
        }
        MessageJsonHandler jsonHandler = this.createJsonHandler();
        RemoteEndpoint remoteEndpoint = this.createRemoteEndpoint(jsonHandler);
        this.addMessageHandlers(jsonHandler, remoteEndpoint);
        T remoteProxy = this.createProxy(remoteEndpoint);
        return this.createLauncher(null, remoteProxy, remoteEndpoint, null);
    }

    protected MessageJsonHandler createJsonHandler() {
        Map supportedMethods = this.getSupportedMethods();
        return this.configureGson != null ? new CosyMessageJsonHandler(supportedMethods, this.configureGson) : new CosyMessageJsonHandler(supportedMethods);
    }

    protected RemoteEndpoint createRemoteEndpoint(MessageJsonHandler jsonHandler) {
        CosyWebSocketMessageConsumer outgoingMessageStream = new CosyWebSocketMessageConsumer(this.session, jsonHandler);
        Endpoint localEndpoint = ServiceEndpoints.toEndpoint(this.localServices);
        RemoteEndpoint remoteEndpoint = this.exceptionHandler == null ? new RemoteEndpoint(outgoingMessageStream, localEndpoint) :
                new RemoteEndpoint(outgoingMessageStream, localEndpoint, this.exceptionHandler);
        jsonHandler.setMethodProvider(remoteEndpoint);
        this.wrapMessageConsumer(remoteEndpoint);
        return remoteEndpoint;
    }

    protected void addMessageHandlers(MessageJsonHandler jsonHandler, RemoteEndpoint remoteEndpoint) {
        MessageConsumer messageConsumer = this.wrapMessageConsumer(remoteEndpoint);
        this.session.addMessageHandler(new CosyWebSocketMessageHandler(messageConsumer, jsonHandler, remoteEndpoint));
    }


}
