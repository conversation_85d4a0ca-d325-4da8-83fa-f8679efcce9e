package com.buzz.tongyi;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.lsp4j.jsonrpc.MessageConsumer;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;
import javax.websocket.Session;
import org.eclipse.lsp4j.jsonrpc.MessageConsumer;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;
import org.eclipse.lsp4j.jsonrpc.messages.Message;
import java.io.IOException;
import java.nio.charset.StandardCharsets;

@Slf4j
public class CosyWebSocketMessageConsumer implements MessageConsumer {
    private final Session session;
    private final MessageJsonHandler jsonHandler;
    private String encoding;

    public CosyWebSocketMessageConsumer(Session session, MessageJsonHandler jsonHandler) {
        this.session = session;
        this.jsonHandler = jsonHandler;
        this.encoding = StandardCharsets.UTF_8.name();
    }

    public Session getSession() {
        return this.session;
    }

    public void consume(Message message) {
        String content = this.jsonHandler.serialize(message);
        try {
            byte[] contentBytes = content.getBytes(this.encoding);
            int contentLength = contentBytes.length;
            String header = this.getHeader(contentLength);
            this.sendMessage(header + content);
        }
        catch (IllegalStateException exception) {
            log.warn("Failed to send message: " + exception.getMessage() + ", close session, caused by " + exception, (Throwable)exception);
            try {
                this.session.close();
            }
            catch (IOException iOException) {}
        }
        catch (IOException exception) {
            log.warn("Failed to send message: " + exception.getMessage() + ", caused by " + exception, (Throwable)exception);
        }
    }

    /*
     * WARNING - Removed try catching itself - possible behaviour change.
     */
    protected void sendMessage(String message) throws IOException {
        Session session = this.session;
        synchronized (session) {
            if (this.session.isOpen()) {
                int length = message.length();
                if (length <= this.session.getMaxTextMessageBufferSize()) {
                    this.session.getBasicRemote().sendText(message);
                } else {
                    int currentOffset = 0;
                    while (currentOffset < length) {
                        int currentEnd = Math.min(currentOffset + this.session.getMaxTextMessageBufferSize(), length);
                        this.session.getBasicRemote().sendText(message.substring(currentOffset, currentEnd), currentEnd == length);
                        currentOffset = currentEnd;
                    }
                }
            } else {
                log.debug("Ignoring message due to closed session: " + message);
            }
        }
    }

    protected String getHeader(int contentLength) {
        StringBuilder headerBuilder = new StringBuilder();
        this.appendHeader(headerBuilder, "Content-Length", (Object)contentLength).append("\r\n");
        if (!StandardCharsets.UTF_8.name().equals(this.encoding)) {
            this.appendHeader(headerBuilder, "Content-Type", (Object)"application/json");
            headerBuilder.append("; charset=").append(this.encoding).append("\r\n");
        }
        headerBuilder.append("\r\n");
        return headerBuilder.toString();
    }

    protected StringBuilder appendHeader(StringBuilder builder, String name, Object value) {
        return builder.append(name).append(": ").append(value);
    }
}