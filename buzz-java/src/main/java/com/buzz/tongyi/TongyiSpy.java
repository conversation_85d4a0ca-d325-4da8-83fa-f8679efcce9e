package com.buzz.tongyi;

import com.buzz.tongyi.TestcaseGenerationDto.ChatContextFeature;
import com.buzz.tongyi.TestcaseGenerationDto.Position;
import com.buzz.tongyi.TestcaseGenerationDto.Range;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.lsp4j.jsonrpc.Launcher;

import javax.websocket.ClientEndpoint;
import javax.websocket.ContainerProvider;
import javax.websocket.DeploymentException;
import javax.websocket.Session;
import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.Collections;
import java.util.UUID;

/**
 * 参考 Cosy.connectCosyServer()
 */
@ClientEndpoint
@Slf4j
public class TongyiSpy {
    public static final String COSY_URI = "ws://127.0.0.1:36510";

    private Session session;
    private URI uri;
    private LanguageClient client = new LanguageClientImpl();
    private LanguageServer server;

    public TongyiSpy() throws URISyntaxException {
        this(new URI(COSY_URI));
    }

    public TongyiSpy(URI uri) {
        this.uri = uri;
    }

    public void connect() throws DeploymentException, IOException {
        this.session = ContainerProvider.getWebSocketContainer().connectToServer(this, this.uri);
        log.info("connect session:{}", session);

        Launcher launcher = new CosyWebSocketLauncherBuilder()
                .setSession(session)
                .setLocalService(client)
                .setRemoteInterface(LanguageServer.class)
                .validateMessages(true)
                .create();
        server = (LanguageServer) launcher.getRemoteProxy();
    }

    public void ask(ChatAskParam chatAskParam) {
        server.getChatService().ask(chatAskParam);
    }

    public static void main(String[] args) throws Exception {
        TongyiSpy spy = new TongyiSpy();
        spy.connect();
        String requestId = UUID.randomUUID().toString();
        String sessionId = "158593d2-e6ec-4e55-9701-0c0ceb1ccb05";

        String code = "public class LogAction {\\n   @Override\\n    public void actionPerformed(@NotNull AnActionEvent e) {\\n        Project project = e.getProject();\\n        Document document = e.getData(CommonDataKeys.EDITOR).getDocument();\\n\\n        int lineCount = document.getLineCount();\\n\\n        Messages.showInfoMessage(project, \\\"该java代码包含\\\" + lineCount + \\\"行\\\", \\\"Line Count\\\");\\n    }\\n}";
        String selectionCode = "    public void actionPerformed(@NotNull AnActionEvent e) {";
        String filePath = "/System/Volumes/Data/work/dist/branch/my/wcgpt-intellij-plugin/src/main/java/com/wacai/middleware/wcgpt/action/LogAction.java";
        String fileCode = "package com.wacai.middleware.wcgpt.action;\\n\\nimport com.intellij.openapi.actionSystem.AnAction;\\nimport com.intellij.openapi.actionSystem.AnActionEvent;\\nimport com.intellij.openapi.actionSystem.CommonDataKeys;\\nimport com.intellij.openapi.editor.Document;\\nimport com.intellij.openapi.project.Project;\\nimport com.intellij.openapi.ui.Messages;\\nimport org.jetbrains.annotations.NotNull;\\n\\npublic class LogAction extends AnAction {\\n\\n    @Override\\n    public void actionPerformed(@NotNull AnActionEvent e) {\\n        Project project = e.getProject();\\n        Document document = e.getData(CommonDataKeys.EDITOR).getDocument();\\n\\n        int lineCount = document.getLineCount();\\n\\n        Messages.showInfoMessage(project, \\\"该java代码包含\\\" + lineCount + \\\"行\\\", \\\"Line Count\\\");\\n    }\\n\\n    @Override\\n    public void update(@NotNull AnActionEvent e) {\\n        // 只在编辑器中显示该选项\\n        e.getPresentation().setVisible(e.getData(CommonDataKeys.EDITOR) != null);\\n    }\\n}\\n";

        TestcaseGenerationDto dto = TestcaseGenerationDto.builder()
                .code(code)
                .features(Collections.singletonList(new ChatContextFeature("/generate unit test", "GENERATE_TESTCASE", "task")))
                .selectionCode(selectionCode)
                .filePath(filePath)
                .fileCode(fileCode)
                .selectionRange(new Range(new Position(0, 13), new Position(59, 13)))
                .preferredLanguage("zh")
                .build();

        ChatAskParam chatAskParam = ChatAskParam.builder()
                .chatTask("GENERATE_TESTCASE")
                .chatContext(dto)
                .requestId(requestId)
                .isReply(false)
                .source(1)
                .sessionId(sessionId)
                .codeLanguage("Java")
                .questionText("/generate unit test ```\\n    public void actionPerformed(@NotNull AnActionEvent e) {```\\n")
                .stream(Boolean.TRUE)
                //.taskDefinitionType("system")
                .build();
        spy.ask(chatAskParam);

        System.in.read();
    }

}
