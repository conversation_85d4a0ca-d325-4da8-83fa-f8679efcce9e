package com.buzz.tongyi;

public class AuthStatus {
    public static final AuthStatus NOT_LOGIN = new AuthStatus(AuthStateEnum.NOT_LOGIN, Integer.valueOf(AuthWhitelistStatusEnum.NOT_WHITELIST.getValue()));
    public static final AuthStatus ERROR_LOGIN = new AuthStatus(AuthStateEnum.ERROR, Integer.valueOf(AuthWhitelistStatusEnum.NOT_WHITELIST.getValue()));
    String messageId;
    private Integer status;
    private String name;
    private String id;
    private String accountId;
    private String token;
    private int quota;
    private Integer whitelist;
    private String orgId;
    private String orgName;
    private String yxUid;
    private String avatarUrl;
    private String userType;

    public AuthStatus() {
    }

    public AuthStatus(AuthStateEnum status, Integer whitelist) {
        this.status = status.getValue();
        this.whitelist = whitelist;
    }

    public boolean isAllow() {
        return this.status != null && this.status.intValue() == AuthStateEnum.LOGIN.getValue() && this.whitelist != null && AuthWhitelistStatusEnum.PASS.getValue() == this.whitelist.intValue();
    }
}
