package com.buzz.tongyi;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.CompletableFuture;

@Slf4j
public class LanguageClientImpl implements LanguageClient {

    public CompletableFuture<Boolean> answer(ChatAnswerParams chatAnswerParams) {
        if (chatAnswerParams == null) {
            log.error("Answering chatAnswerParams is null!");
            return failed(new Exception("chatAnswerParams is null."));
        }
        updateAnswer(chatAnswerParams);
        return CompletableFuture.supplyAsync(() -> updateAnswer(chatAnswerParams));
    }

    private boolean updateAnswer(ChatAnswerParams chatAnswerParams) {
        log.info("Answering sessionId={}, requestId={},text={}",
                chatAnswerParams.getSessionId(),
                chatAnswerParams.getRequestId(),
                chatAnswerParams.getText()
        );
        return true;
    }


    public CompletableFuture<Boolean> processStepCallback(ChatProcessStepCallbackParams chatProcessStepCallbackParams) {
        if (chatProcessStepCallbackParams == null) {
            return failed(new Exception("chatProcessStepCallbackParams is null."));
        }
        //ChatProcessStepCallbackParamsProcessor chatProcessStepCallbackParamsProcessor = new ChatProcessStepCallbackParamsProcessor(chatProcessStepCallbackParams);
        return CompletableFuture.supplyAsync(() -> (updateProcessStep(chatProcessStepCallbackParams)));
    }

    public boolean updateProcessStep(ChatProcessStepCallbackParams chatProcessStepCallbackParams) {
        log.info("Process step callback. sessionId={},requestId={},result={},status={},",
                chatProcessStepCallbackParams.getSessionId(),
                chatProcessStepCallbackParams.getRequestId(),
                chatProcessStepCallbackParams.getResult(),
                chatProcessStepCallbackParams.getStatus()
        );
        return true;
    }

    public static <R> CompletableFuture<R> failed(Throwable error) {
        CompletableFuture future = new CompletableFuture();
        future.completeExceptionally(error);
        return future;
    }
}
