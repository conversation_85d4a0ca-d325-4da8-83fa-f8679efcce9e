package com.buzz.tongyi;
import javax.websocket.MessageHandler;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.lsp4j.jsonrpc.MessageConsumer;
import org.eclipse.lsp4j.jsonrpc.MessageIssueException;
import org.eclipse.lsp4j.jsonrpc.MessageIssueHandler;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;
import org.eclipse.lsp4j.jsonrpc.messages.Message;

@Slf4j
public class CosyWebSocketMessageHandler implements MessageHandler.Partial<String>, MessageHandler.Whole<String> {
    private final MessageConsumer callback;
    private final MessageJsonHandler jsonHandler;
    private final MessageIssueHandler issueHandler;
    private StringBuilder partialMessage;

    public CosyWebSocketMessageHandler(MessageConsumer callback, MessageJsonHandler jsonHandler, MessageIssueHandler issueHandler) {
        this.callback = callback;
        this.jsonHandler = jsonHandler;
        this.issueHandler = issueHandler;
    }

    public void onMessage(String content, boolean lastChunk) {
        try {
            if (lastChunk) {
                String wholeMessage = content;
                if (this.partialMessage != null) {
                    this.partialMessage.append(content);
                    wholeMessage = this.partialMessage.toString();
                    this.partialMessage = null;
                }
                Message message = this.jsonHandler.parseMessage((CharSequence)wholeMessage);
                this.callback.consume(message);
            } else {
                if (this.partialMessage == null) {
                    this.partialMessage = new StringBuilder();
                }
                this.partialMessage.append(content);
            }
        }
        catch (MessageIssueException var3) {
            this.issueHandler.handle(var3.getRpcMessage(), var3.getIssues());
        }
        catch (Exception e) {
            log.warn("fail to parse message:" + content, (Throwable)e);
        }
    }

    public void onMessage(String content) {
        try {
            Message message = this.jsonHandler.parseMessage((CharSequence)content);
            this.callback.consume(message);
        }
        catch (MessageIssueException var3) {
            this.issueHandler.handle(var3.getRpcMessage(), var3.getIssues());
        }
    }
}
