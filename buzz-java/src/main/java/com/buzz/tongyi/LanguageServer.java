package com.buzz.tongyi;

import org.eclipse.lsp4j.jsonrpc.services.JsonDelegate;
import org.eclipse.lsp4j.InitializedParams;
import org.eclipse.lsp4j.WorkDoneProgressCancelParams;
import org.eclipse.lsp4j.jsonrpc.services.JsonDelegate;
import org.eclipse.lsp4j.jsonrpc.services.JsonNotification;
import org.eclipse.lsp4j.jsonrpc.services.JsonRequest;
import org.eclipse.lsp4j.services.WorkspaceService;

import java.util.concurrent.CompletableFuture;

public interface LanguageServer {

    @JsonRequest
    public CompletableFuture<InitializeResultExt> initialize(InitializeParamsWithConfig var1);

    @JsonNotification
    default public void initialized(InitializedParams params) {
        this.initialized();
    }

    @Deprecated
    default public void initialized() {
    }

    @JsonRequest
    public CompletableFuture<Object> shutdown();

    @JsonNotification
    public void exit();

    @JsonDelegate
    public ChatService getChatService();
}
