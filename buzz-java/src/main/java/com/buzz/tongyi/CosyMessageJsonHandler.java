package com.buzz.tongyi;

import com.google.gson.GsonBuilder;
import com.google.gson.JsonParseException;
import org.eclipse.lsp4j.jsonrpc.json.MessageJsonHandler;

import java.io.Reader;
import java.io.StringReader;
import java.util.Map;
import java.util.function.Consumer;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import org.eclipse.lsp4j.jsonrpc.json.JsonRpcMethod;
import org.eclipse.lsp4j.jsonrpc.messages.Message;

public class CosyMessageJsonHandler extends MessageJsonHandler {
    public CosyMessageJsonHandler(Map<String, JsonRpcMethod> supportedMethods) {
        super(supportedMethods);
    }

    public CosyMessageJsonHandler(Map<String, JsonRpcMethod> supportedMethods, Consumer<GsonBuilder> configureGson) {
        super(supportedMethods, configureGson);
    }

    public Message parseMessage(CharSequence input) throws JsonParseException {
        StringReader reader = new StringReader(WebSocketUtils.removeContentLengthHeader((CharSequence)input));
        return this.parseMessage((Reader)reader);
    }

    static class WebSocketUtils{
        public static final Pattern CONTENT_LENGTH_HEADER_PATTERN = Pattern.compile("Content-Length: \\d+\r\n");
        public static final Pattern ITEMS_NULL_PATTERN = Pattern.compile(".*(\"items\":null).*");
        public static final String ITEMS_NULL_REPLACE_STR = "\"items\":[]";

        public static String removeContentLengthHeader(CharSequence input) {
            Matcher matcher = CONTENT_LENGTH_HEADER_PATTERN.matcher(input);
            if (!matcher.find()) {
                throw new JsonParseException("Message missingContent-Length");
            }
            input = input.subSequence(matcher.end(), input.length());
            return input.toString();
        }

        public static CharSequence replaceItemsNull(CharSequence input) {
            Matcher matcher = ITEMS_NULL_PATTERN.matcher(input);
            if (matcher.find()) {
                return input.toString().replace(matcher.group(1), ITEMS_NULL_REPLACE_STR);
            }
            return input;
        }
    }
}
