package com.buzz.tongyi;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;

import java.util.List;

@Data
@Builder
public class TestcaseGenerationDto {
    //following fields are BaseChatTaskDto
    private String localeLang;
    private String text;
    List<ChatContextFeature> features;
    private String preferredLanguage;


    //following fields are TestcaseGenerationDto
    private String code;
    private String selectionCode;
    private Range selectionRange;
    private String fileCode;
    private String language;
    private String filePath;


    @Data
    @AllArgsConstructor
    public static class Range {
        private Position start;
        private Position end;
    }

    @Data
    @AllArgsConstructor
    public  static class Position {
        private int character;
        private int line;
    }

    @Data
    @AllArgsConstructor
    public static class ChatContextFeature {
        private String name;
        private String id;
        private String type;
    }
}