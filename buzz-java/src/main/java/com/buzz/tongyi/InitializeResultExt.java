package com.buzz.tongyi;

import com.google.gson.annotations.JsonAdapter;
import org.eclipse.lsp4j.InitializeResult;
import org.eclipse.lsp4j.jsonrpc.json.adapters.JsonElementTypeAdapter;

public class InitializeResultExt  extends InitializeResult {
    @JsonAdapter(value= JsonElementTypeAdapter.Factory.class)
    private Object experimental;

    public Object getExperimental() {
        return this.experimental;
    }

    public void setExperimental(Object experimental) {
        this.experimental = experimental;
    }
}

