package com.buzz.tongyi;

public enum AuthStateEnum {
    NOT_LOGIN(1),
    LOGIN(2),
    LOGIN_EXPIRED(3),
    ERROR(4),
    NETWORK_ERROR(5),
    IP_BANNED_ERROR(6),
    APP_DISABLED_ERROR(7);

    private int value;

    private AuthStateEnum(int value) {
        this.value = value;
    }

    public int getValue() {
        return this.value;
    }

    public static AuthStateEnum getAuthStateEnum(int value) {
        for (AuthStateEnum authStateEnum : AuthStateEnum.values()) {
            if (authStateEnum.getValue() != value) continue;
            return authStateEnum;
        }
        return null;
    }
}
