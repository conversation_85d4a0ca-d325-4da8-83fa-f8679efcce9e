package com.buzz.tongyi;

import org.eclipse.lsp4j.jsonrpc.services.JsonRequest;

import java.util.concurrent.CompletableFuture;

public interface LanguageClient {

    @JsonRequest(value="chat/process_step_callback")
    default public CompletableFuture<Boolean> processStepCallback(ChatProcessStepCallbackParams chatProcessStepCallbackParams) {
        throw new UnsupportedOperationException();
    }

    @JsonRequest(value="chat/answer")
    default public CompletableFuture<Boolean> answer(ChatAnswerParams chatAnswerParams) {
        throw new UnsupportedOperationException();
    }

    @JsonRequest(value="chat/finish")
    default public CompletableFuture<Boolean> finish(ChatFinishParams chatAnswerParams) {
        throw new UnsupportedOperationException();
    }

    @JsonRequest(value="chat/filterTimeout")
    default public CompletableFuture<Boolean> filterTimeout(ChatFilterTimeoutParams chatFilterTimeoutParams) {
        throw new UnsupportedOperationException();
    }

    @JsonRequest(value="auth/report")
    default public CompletableFuture<Void> authReport(AuthStatus authStatus) {
        throw new UnsupportedOperationException();
    }
}
