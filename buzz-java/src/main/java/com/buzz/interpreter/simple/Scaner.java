package com.buzz.interpreter.simple;

import org.apache.commons.lang.math.NumberUtils;

import java.util.ArrayList;
import java.util.List;

/**
 *
 * Spring EL, Template, JSON
 * <AUTHOR>
 * @description
 * @date 2022-06-23 18:24
 **/
public class Scaner {
    private static final char LEFT = '(';

    private static final char RIGHT = ')';

    private static final char QUOTE = '\'';

    private static final char COMMA = ',';

    private State state = State.BEGIN_LINE;

    private Reader reader;

    private List<Line> lines = new ArrayList();

    private Line curLine;

    public Scaner(String data) {
        this.reader = new Reader(data);
    }

    public void start() {
        while (true) {
            switch (state) {
                case BEGIN_LINE: {
                    char c = reader.next();
                    if (c != LEFT) {
                        throw new IllegalArgumentException("except ( ,actually :" + c);
                    }
                    curLine = new Line();
                    lines.add(curLine);
                    state = State.BEGIN_ITEM;
                    break;
                }
                case BEGIN_ITEM: {
                    char c = reader.next();
                    if (c == 'n') { //null
                        Token token = readNull();
                        curLine.add(token);
                    } else if (NumberUtils.isNumber(String.valueOf(c))) { //number
                        Token token = readNumber(c);
                        curLine.add(token);
                    } else if (c == QUOTE) {//string
                        Token token = readString();
                        curLine.add(token);
                    } else {
                        throw new IllegalArgumentException("except null,number,string! actually: " + c);
                    }
                    //判断item结束
                    if (c == RIGHT) {

                        state = State.BEGIN_ITEM;
                    } else if (c != COMMA && c != -1) {
                        throw new IllegalArgumentException("except: ) or , actually: " + c);
                    }
                    break;
                }
            }
        }
    }

    private Token readNull() {
        reader.next(3);
        return StringItem.NULL;
    }

    private Token readNumber(char c) {
        StringBuffer sb = new StringBuffer(c);
        while (isNumber(c)) {
            c = reader.next();
            sb.append(c);
        }
        reader.back();
        return new StringItem(sb.toString());
    }

    private Token readString() {
        StringBuffer sb = new StringBuffer();
        char c;
        do {
            c = reader.next();
            sb.append(c);
        } while (c != QUOTE);
        return new StringItem(sb.toString());
    }

    private static enum State {
        BEGIN_LINE,
        BEGIN_ITEM
    }

    private static boolean isNumber(char c) {
        byte b = (byte) c;
        return b >= 30 && b <= 39;
    }
}
