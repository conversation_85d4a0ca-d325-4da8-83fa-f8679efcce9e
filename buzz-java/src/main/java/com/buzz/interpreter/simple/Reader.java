package com.buzz.interpreter.simple;

/**
 * <AUTHOR>
 * @description
 * @date 2022-06-23 18:17
 **/
public class Reader {

    private char[] data;
    private int pos;
    private int length;

    public Reader(String data) {
        this.data = data.toCharArray();
        this.length = data.length();
    }

    public char next() {
        if (pos >= length) {
            return (char) -1;
        }
        return data[++pos];
    }

    public char next(int size) {
        if (pos+size >= length) {
            return (char) -1;
        }
        pos += size;
        return data[pos];
    }

    public char back() {
        --pos;
        if (pos < 0) {
            pos = 0;
        }
        return data[pos];
    }

    public int pos() {
        return pos;
    }
}
