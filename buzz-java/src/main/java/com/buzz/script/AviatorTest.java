package com.buzz.script;

import com.googlecode.aviator.AviatorEvaluator;
import com.googlecode.aviator.Expression;
import org.junit.Assert;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 **/
public class AviatorTest {

    @Test
    public void testStartWith() {
        Expression expression = AviatorEvaluator.getInstance()
                .compile("string.startsWith(name,'middleware-hermes')");
        Map<String, Object> context = new HashMap<>();
        context.put("name", "middleware-hermes-proxy-8080");
        Assert.assertEquals(true, (Boolean) expression.execute(context));

        context.put("name", "middleware-8080");
        Assert.assertEquals(false, (Boolean) expression.execute(context));
    }

    @Test
    public void testRegularExpress() {
        String expressStr = "!(name=~/^middleware-hermes.*/ || name=~/^middleware-pre-hermes.*/)";
        Expression expression = AviatorEvaluator.getInstance().compile(expressStr);
        Map<String, Object> context = new HashMap<>();
        context.put("name", "middleware-hermes-proxy-8080");
        System.out.println(expression.execute(context));
        context.put("name", "mozi-client");
        System.out.println(expression.execute(context));
    }
}


