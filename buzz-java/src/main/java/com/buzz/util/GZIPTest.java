package com.buzz.util;

import org.junit.Test;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;

/**
 * <AUTHOR>
 * @description
 **/
public class GZIPTest {

    private byte[] compress(String raw) throws IOException {
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        GZIPOutputStream gzip = new GZIPOutputStream(out);
        gzip.write(raw.getBytes());
        gzip.close();
        return out.toByteArray();
    }

    private String uncompress(byte[] data) throws IOException {
        ByteArrayInputStream input = null;
        GZIPInputStream gzip = null;
        byte[] ret = new byte[data.length];
        try {
            input = new ByteArrayInputStream(data);
            gzip = new GZIPInputStream(input);
            gzip.read(ret, 0, data.length);
            return new String(ret);
        } finally {
            if (gzip != null)
                gzip.close();
        }
    }

    @Test
    public void test() throws IOException {
        String string = "this is a test text";
        byte[] compressedData = compress(string);
        System.out.println(new String(compressedData));
        String s2 = uncompress(compressedData);
        System.out.println(s2);

    }
}
