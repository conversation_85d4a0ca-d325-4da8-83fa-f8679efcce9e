package com.buzz.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;

import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 **/
public class HttpUtil {
    private int connectTimeout = 1000;
    private int readTimeout = 5000;

    public HttpUtil(int connectTimeout, int readTimeout) {
        this.connectTimeout = connectTimeout;
        this.readTimeout = readTimeout;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private int connectTimeout = 1000;
        private int readTimeout = 5000;

        public Builder connectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
            return this;
        }

        public Builder readTimeout(int readTimeout) {
            this.readTimeout = readTimeout;
            return this;
        }

        public HttpUtil build() {
            return new HttpUtil(connectTimeout, readTimeout);
        }
    }

    public String httpPostJson(String urlStr, Map<String, String> header, Object content) {
        if (header == null) {
            header = new HashMap<>();
        }
        header.put("Content-Type", "application/json;charset=utf-8");
        return httpPost(urlStr, header, JSON.toJSONString(content));
    }

    public String httpPost(String url, Map<String, String> headers, String content) {
        HttpURLConnection conn = null;
        try {
            conn = (HttpURLConnection) new URL(url).openConnection();
            conn.setRequestMethod("POST");
            conn.setConnectTimeout(connectTimeout);
            conn.setReadTimeout(readTimeout);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            //set headers
            setHeaders(conn, headers);
            conn.getOutputStream().write(content.getBytes());

            int httpCode = conn.getResponseCode();
            InputStream is = (httpCode == 200) ? conn.getInputStream() : conn.getErrorStream();
            if (httpCode == 200) {
                String res = IOUtils.parseString(is, "UTF-8");
                JSONObject jsonObject = JSON.parseObject(res);
                int code = jsonObject.getIntValue("code");
                String msg = jsonObject.getString("error");
                if (code == 0) {
                    return res;
                } else {
                    throw new HttpException(msg);
                }
            } else {
                String errMsg = StringUtils.EMPTY;
                if (is != null) {
                    errMsg = IOUtils.parseString(is, "utf-8");
                }
                throw new HttpException("url:" + url + ",httpCode:" + httpCode + ",content:" + errMsg);
            }
        } catch (Exception e) {
            throw new HttpException(e.getMessage() + ",url:" + url, e);
        } finally {
            if (null != conn) {
                conn.disconnect();
            }
        }
    }

    private void setHeaders(HttpURLConnection conn, Map<String, String> headers) {
        if (null != headers) {
            for (Iterator<String> iter = headers.keySet().iterator(); iter.hasNext(); ) {
                String key = iter.next();
                conn.addRequestProperty(key, headers.get(key));
            }
        }
    }
}
