package com.buzz.util;

import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;

public class UtfTest {

    private static String read(File file) throws IOException {
        return new String(Files.readAllBytes(Paths.get(file.getAbsolutePath())), "GBK");
    }

    private static void write(File file, String content) throws IOException {
        Files.write(Paths.get(file.getAbsolutePath()), content.getBytes(StandardCharsets.UTF_8));
    }

    public void visit(File file) throws IOException {
        if (file.isDirectory()) {
            for (File children : file.listFiles()) {
                visit(children);
            }
        }
        if (file.getName().endsWith(".java")) {
            System.out.println(file);
            String content = read(file);
            //System.out.println(content);
            write(file, content);
            System.out.println("========================");
        }
    }


    public static void main(String[] args) throws IOException {
        UtfTest test = new UtfTest();
        test.visit(new File("/Users/<USER>/.m2/repository/com/taobao/eagleeye/eagleeye-core/1.4.7/source/com"));
    }
}
