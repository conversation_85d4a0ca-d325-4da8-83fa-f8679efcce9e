package com.buzz.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.Enumeration;
import java.util.regex.Pattern;

/**
 * 网络层辅助方法集。
 *
 * @since 2019-06-21
 */
public final class NetUtils {

        private static final Logger logger = LoggerFactory.getLogger(NetUtils.class);

        private static final String LOCALHOST = "127.0.0.1";
        private static final String ANYHOST = "0.0.0.0";
        private static final Pattern IP_PATTERN = Pattern.compile("\\d{1,3}(\\.\\d{1,3}){3,5}$");

        /**
         * 实例本地IP
         */
        private static volatile InetAddress LOCAL_ADDRESS = null;

        private NetUtils() {
            throw new AssertionError("non instance");
        }

        /**
         * 返回实例本地IP。
         */
        public static String getLocalHost() {
            InetAddress address = getLocalAddress();
            return address == null ? LOCALHOST : address.getHostAddress();
        }

        /**
         * 遍历本地网卡，返回第一个合理的IP。
         *
         * @return 本地网卡IP
         */
        public static InetAddress getLocalAddress() {
            if (LOCAL_ADDRESS != null) {
                return LOCAL_ADDRESS;
            }
            InetAddress localAddress = getLocalAddress0();
            LOCAL_ADDRESS = localAddress;
            return localAddress;
        }

        private static InetAddress getLocalAddress0() {
            InetAddress localAddress = null;
            try {
                localAddress = InetAddress.getLocalHost();
                if (isValidAddress(localAddress)) {
                    return localAddress;
                }
            } catch (Throwable e) {
                logger.warn("Failed to retrieve ip address, " + e.getMessage(), e);
            }
            try {
                Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
                if (interfaces != null) {
                    while (interfaces.hasMoreElements()) {
                        try {
                            NetworkInterface network = interfaces.nextElement();
                            Enumeration<InetAddress> addresses = network.getInetAddresses();
                            if (addresses != null) {
                                while (addresses.hasMoreElements()) {
                                    try {
                                        InetAddress address = addresses.nextElement();
                                        if (isValidAddress(address)) {
                                            return address;
                                        }
                                    } catch (Throwable e) {
                                        logger.warn("Failed to retrieve ip address, " + e.getMessage(), e);
                                    }
                                }
                            }
                        } catch (Throwable e) {
                            logger.warn("Failed to retrieve ip address, " + e.getMessage(), e);
                        }
                    }
                }
            } catch (Throwable e) {
                logger.warn("Failed to retrieve ip address, " + e.getMessage(), e);
            }
            logger.error("Could not get local host ip address, will use 127.0.0.1 instead.");
            return localAddress;
        }

        private static boolean isValidAddress(InetAddress address) {
            if (address == null || address.isLoopbackAddress()) {
                return false;
            }
            String name = address.getHostAddress();
            return (name != null
                    && !ANYHOST.equals(name)
                    && !LOCALHOST.equals(name)
                    && IP_PATTERN.matcher(name).matches());
        }

    }

