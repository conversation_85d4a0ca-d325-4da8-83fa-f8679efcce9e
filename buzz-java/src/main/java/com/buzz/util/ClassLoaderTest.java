package com.buzz.util;

import java.io.File;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLClassLoader;
import java.util.ArrayList;
import java.util.List;

public class ClassLoaderTest {

	static void printClassLocation(URLClassLoader loader, String className) throws ClassNotFoundException {
		System.out.println(loader.loadClass(className).getProtectionDomain().getCodeSource().getLocation());
	}

	static List<URL> loadUrls(String dir) throws MalformedURLException {
		File dirFile = new File(dir);
		List<URL> urls = new ArrayList<>();
		for (File file : dirFile.listFiles()) {
			URL url = file.toURL();
			//System.out.println(url);
			urls.add(url);
		}
		return urls;
	}

	public static void main(String[] args) throws Throwable {

		List<URL> urls = new ArrayList<>();
		urls.addAll(loadUrls("/data/temp/MseAgent/lib"));
		urls.addAll(loadUrls("/data/temp/MseAgent/boot"));

		URLClassLoader loader = new URLClassLoader(urls.toArray(new URL[0]), null);
//		printClassLocation(loader,"com.alibaba.metrics.MetricManager");
//		printClassLocation(loader, "com.alibaba.middleware.common.thread.NamedThreadFactory");
//		printClassLocation(loader, "com.alibaba.middleware.tracing.TracingClient");
		printClassLocation(loader, "com.aliyuncs.mse.model.v20190531.ReportAgentInfoFormRequest");

	}
}
