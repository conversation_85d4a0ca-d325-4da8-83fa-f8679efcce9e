package com.buzz.util;

import java.util.ArrayList;
import java.util.List;
import java.util.Set;

import javax.management.MBeanAttributeInfo;
import javax.management.MBeanInfo;
import javax.management.MBeanServerConnection;
import javax.management.ObjectName;
import javax.management.remote.JMXConnector;
import javax.management.remote.JMXConnectorFactory;
import javax.management.remote.JMXServiceURL;

import com.alibaba.fastjson.JSON;
import org.junit.BeforeClass;
import org.junit.Test;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JmxTest {

	private static MBeanServerConnection server;

	@BeforeClass
	public static void openMBeanServer()throws Exception{
		String jmxURL = "service:jmx:rmi:///jndi/rmi://localhost:7001/jmxrmi";//tomcat jmx url

		JMXServiceURL serviceURL = new JMXServiceURL(jmxURL);
		JMXConnector conn = JMXConnectorFactory.connect(serviceURL);
		server = conn.getMBeanServerConnection();
		log.info("MBeanCount:"+server.getMBeanCount());

	}

	private static List<String> getAttributeNames(MBeanInfo mbeanInfo){
		List<String> attrNames = new ArrayList();
		for (MBeanAttributeInfo attrInfo : mbeanInfo.getAttributes()) {
			if (attrInfo.isReadable()) {
				attrNames.add(attrInfo.getName());
			}
		}
		return attrNames;
	}

	@Test
	public void fetchJvm() throws Exception {
		ObjectName memoryName  = new ObjectName("java.lang:type=Memory");
		MBeanInfo mbeanInfo = server.getMBeanInfo(memoryName);
		List<String> attrNames = getAttributeNames(mbeanInfo);
		for(String attrName :attrNames){
			Object value = server.getAttribute(memoryName,attrName);
			System.out.println(attrName+"\t | "+value.getClass());
		}
		System.out.println("==========");
		//直接返回GC
		ObjectName gc  = new ObjectName("java.lang:type=GarbageCollector,name=PS MarkSweep");
		System.out.println("GC次数 ==>"+server.getAttribute(gc,"CollectionCount"));

	}

	/**
	 * 远程获取
	 * @throws Exception
	 */
	@Test
	public void fetchSpringActuator() throws Exception {
		ObjectName memoryName  = new ObjectName("org.springframework.boot:type=Endpoint,name=healthEndpoint");
		MBeanInfo mBeanInfo = server.getMBeanInfo(memoryName);
		List<String> attrNames = getAttributeNames(mBeanInfo);
		for(String attrName :attrNames){
			Object value = server.getAttribute(memoryName,attrName);
			System.out.println(attrName+"\t | "+ JSON.toJSONString(value));
		}
	}

	@Test
	public void fetchAll() throws Exception {
		//数量比较大
		Set<ObjectName> objectNames = server.queryNames(null, null);
		for (ObjectName name : objectNames) {
			MBeanInfo info = null;
			try {
				info = server.getMBeanInfo(name);
			} catch (Exception e) {
				log.info(name + "==>error:" + e.getMessage());
			}
			log.info(name + "==>\t" + info);
		}
	}

	
}
