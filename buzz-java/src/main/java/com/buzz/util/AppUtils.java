package com.buzz.util;

import javax.management.AttributeNotFoundException;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import java.io.*;
import java.lang.management.ManagementFactory;
import java.net.URLDecoder;
import java.security.CodeSource;
import java.util.*;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

/**
 * 应用环境相关的工具方法
 *
 * <AUTHOR>
 */
public class AppUtils {
    private static final String SPRING_BOOT_MAIN_CLASS="org.springframework.boot.loader.Launcher";
    private static final int SPRING_BOOT_LIB_PREFIX_IX = "BOOT-INF/lib/".length();
    private static final String ARTIFACT_KEY = "ARTIFACT";
    public static final String FILE_ENCODING = System.getProperty("file.encoding");
    public static final String FILE_SEPARATOR = System.getProperty("file.separator");

    public static final String MAIN_CLASS_NAME;
    public static final boolean IS_SPRINGBOOT;
    public static final boolean IS_TOMCAT;
    private static final boolean IS_NEST;
    public static final String APP_ARTIFACT_ID;
    public static final Set<String> APP_JARS;

    static {
        MAIN_CLASS_NAME = getMainClassName();
        IS_SPRINGBOOT = isSpringBoot();
        IS_NEST = isNest();
        IS_TOMCAT = isTomcat();
        APP_ARTIFACT_ID = getArtifactId();
        APP_JARS = listJars();
    }



    /**
     * 列出应用打包时所有的jar
     *
     * @return
     */
    private static final Set<String> listJars() {
        Set<String> appDependencies = null;
        if (IS_SPRINGBOOT) {
            ClassLoader classLoader = ClassLoader.getSystemClassLoader();
            String path = null;
            try {
                path = classLoader.loadClass(SPRING_BOOT_MAIN_CLASS).getProtectionDomain().getCodeSource().getLocation().getPath();
            }catch (Throwable e){
            }
            if(path==null){
                return Collections.emptySet();
            }
            File appFile = new File(path);
            if (appFile.exists() && appFile.isFile()) {
                JarFile bigJar = null;
                try {
                    bigJar = new JarFile(appFile);
                    Enumeration<JarEntry> es = bigJar.entries();
                    appDependencies = new HashSet<String>();
                    while (es.hasMoreElements()) {
                        String filename = es.nextElement().getName();
                        if (filename.endsWith(".jar")) {
                            appDependencies.add(filename.substring(SPRING_BOOT_LIB_PREFIX_IX, filename.length()));
                        }
                    }
                } catch (IOException e) {
                } finally {
                    IOUtils.close(bigJar);
                }
            }
        } else if (IS_TOMCAT) {
            String path = AppUtils.class.getProtectionDomain().getCodeSource().getLocation().getPath();
            // tomcat应用，遍历当前jar所在目录
            File curJarFile = new java.io.File(path);
            if (curJarFile.exists() && curJarFile.isFile()) {
                File libDir = curJarFile.getParentFile();
                if (libDir.isDirectory()) {
                    appDependencies = new HashSet<String>();
                    for (File jar : IOUtils.listFiles(libDir, "jar")) {
                        appDependencies.add(jar.getName());
                    }
                }
            }
        }
        else {
            // 其他启动方式，从classpath获取
            appDependencies = ResourceUtils.listJarsInClassPath();
        }
        if (appDependencies != null)
            return appDependencies;
        return new HashSet<String>(0);
    }

    private static final String getArtifactId() {
        // 优先从 env 中获取
        String artifactId = System.getenv(ARTIFACT_KEY);
        if (artifactId != null) {
            return artifactId;
        }
        // 优先从 command line (“-D” option) 中获取
        artifactId = System.getProperty(ARTIFACT_KEY);
        if (artifactId != null) {
            return artifactId;
        }

        if (IS_SPRINGBOOT) {
            File appFile = getSpringbootAppJarFile();
            if (appFile != null && appFile.exists() && appFile.isFile()) {
                JarFile bigJar = null;
                try {
                    bigJar = new JarFile(appFile);
                    Enumeration<JarEntry> es = bigJar.entries();
                    while (es.hasMoreElements()) {
                        JarEntry e = es.nextElement();
                        String filename = e.getName();
                        // META-INF/maven/com.wacai.finance/finance-sangreal-service/pom.xml
                        if (filename.endsWith("pom.xml") && filename.startsWith("META-INF/maven/")) {
                            InputStream in = null;
                            try {
                                in = bigJar.getInputStream(e);
                                String aid = ResourceUtils.resolveArtifactIdFromPom(in);
                                return aid;
                            } catch (Throwable ex) {
                            } finally {
                                IOUtils.close(in);
                            }
                        }
                    }
                } catch (IOException e) {
                } finally {
                    IOUtils.close(bigJar);
                }
            }
        }

        if (IS_TOMCAT) {
            File pom = findPomInTomcat();
            if (pom != null && pom.exists() && pom.isFile()) {
                InputStream is = null;
                try {
                    is = new FileInputStream(pom);
                    return ResourceUtils.resolveArtifactIdFromPom(is);
                } catch (Throwable e) {
                } finally {
                    IOUtils.close(is);
                }
            }
        }

        if (IS_NEST) {
            return System.getProperty("Ark-Biz-Name");
        }

        // 再尝试main类所在的jar文件是否存在
        String cmd = System.getProperty("sun.java.command");
        if (MAIN_CLASS_NAME != null && cmd != null && (cmd.contains(".jar") || cmd.contains(".war"))) {
            try {
                Class<?> mainClz = Thread.currentThread().getContextClassLoader().loadClass(MAIN_CLASS_NAME);
                CodeSource codeSource = mainClz.getProtectionDomain().getCodeSource();
                if (codeSource != null) {
                    String fn = codeSource.getLocation().getFile();
                    if (fn != null && fn.endsWith(".jar")) {
                        JarFile jar = null;
                        try {
                            jar = new JarFile(new File(fn));
                            Enumeration<JarEntry> es = jar.entries();
                            while (es.hasMoreElements()) {
                                JarEntry e = es.nextElement();
                                String filename = e.getName();
                                // META-INF/maven/com.wacai/wacai-zookeeper/pom.xml
                                if (filename.endsWith("pom.xml") && filename.startsWith("META-INF/maven/")) {
                                    InputStream in = null;
                                    try {
                                        in = jar.getInputStream(e);
                                        return ResourceUtils.resolveArtifactIdFromPom(in);
                                    } catch (Throwable ex) {
                                    } finally {
                                        IOUtils.close(in);
                                    }
                                }
                            }
                        } finally {
                            IOUtils.close(jar);
                        }
                    }
                }
            } catch (Exception e) {
            }
        }

        // 找出IDE中启动，并识别子工程
        return ResourceUtils.findArtifactIdInClassPath();

    }

    private static final File getSpringbootAppJarFile() {
        ClassLoader classLoader = ClassLoader.getSystemClassLoader();
        try {
            String path = classLoader.loadClass(SPRING_BOOT_MAIN_CLASS).getProtectionDomain().getCodeSource().getLocation().getPath();
            File appFile = new File(path);
            return appFile;
        } catch (Exception e) {
            e.printStackTrace();
            //ignore
        }
        return null;
    }

    private static final File findPomInTomcat() {
        String path = AppUtils.class.getProtectionDomain().getCodeSource().getLocation().getPath();
        File curJarFile = new File(path);
        if (!curJarFile.exists() || !curJarFile.isFile())
            return null;
        File libDir = curJarFile.getParentFile();
        if (!libDir.exists() || !libDir.isDirectory())
            return null;
        File webInfDir = libDir.getParentFile();
        if (!webInfDir.exists() || !webInfDir.isDirectory())
            return null;
        File rootDir = webInfDir.getParentFile();
        if (!rootDir.exists() || !rootDir.isDirectory())
            return null;
        File mvnDir = new File(rootDir, "META-INF/maven");
        if (!mvnDir.exists() || !mvnDir.isDirectory())
            return null;
        Set<File> fs = IOUtils.findFiles(mvnDir, "pom.xml");
        if (fs == null || fs.isEmpty())
            return null;
        return fs.iterator().next();
    }

    private static final Map<String, String> parseManifest(InputStream in) throws Exception {
        Map<String, String> map = new HashMap<String, String>();
        BufferedReader br = new BufferedReader(new InputStreamReader(in));
        String line = null;
        while ((line = br.readLine()) != null) {
            // Manifest-Version: 1.0
            // Main-Class: org.springframework.boot.loader.JarLauncher
            if (!line.isEmpty() && line.indexOf(':') != -1) {
                String[] kv = line.split(":");
                map.put(kv[0].trim(), kv[1].trim());
            }
        }
        return map;
    }

    /**
     * 应用是否springboot项目
     *
     * @return
     */
    private static final boolean isSpringBoot() {
        ClassLoader classLoader = ClassLoader.getSystemClassLoader();
        return classLoader.getResource("org/springframework/boot/loader/Launcher.class") != null;
    }

    private static final boolean isTomcat() {
        return "org.apache.catalina.startup.Bootstrap".equals(MAIN_CLASS_NAME);
    }

    private static final boolean isNest() {
        return "com.alipay.sofa.ark.bootstrap.ArkLauncher".equals(MAIN_CLASS_NAME);

    }

    /**
     * 通过 mbean 判断是否嵌入式tomcat已启动. 注意，取决于嵌入式tomcat的启动是否在调用该方法之前
     *
     * @return
     */
    public static final boolean isEmbeddedTomcat() {
        try {
            MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
            ObjectName objName = new ObjectName("Tomcat:type=Server");
            return mbeanServer.isRegistered(objName);
        } catch (Exception ex) {
            return false;
        }
    }

    /**
     * 获取应用的启动类
     *
     * @return
     */
    private static final String getMainClassName() {
        String cmd = System.getProperty("sun.java.command");
        if (cmd != null && !cmd.isEmpty()) {
            if (cmd.contains(".jar") || cmd.contains(".war")) {
                // 根据 System.getenv 里的 JAVA_MAIN_CLASS_$pid 判断并不靠谱，OpenJDK就没有提供这个env
                for (Map.Entry<Thread, StackTraceElement[]> e : Thread.getAllStackTraces().entrySet()) {
                    Thread t = e.getKey();
                    if (!t.getName().equals("main"))
                        continue;
                    StackTraceElement[] arr = e.getValue();
                    StackTraceElement trace = arr[arr.length - 1];
                    if (trace.getMethodName().equals("main")) {
                        String str = trace.toString();
                        return str.substring(0, str.lastIndexOf(".main"));
                    }
                }

                // 找不到main线程, 从 META-INF/MANIFEST.MF 文件里解析 Main-Class
                File appFile = getSpringbootAppJarFile();
                if (appFile != null && appFile.exists() && appFile.isFile()) {
                    JarFile bigJar = null;
                    try {
                        bigJar = new JarFile(appFile);
                        Enumeration<JarEntry> es = bigJar.entries();
                        while (es.hasMoreElements()) {
                            JarEntry e = es.nextElement();
                            if (e.getName().equals("META-INF/MANIFEST.MF")) {
                                Map<String, String> map = parseManifest(bigJar.getInputStream(e));
                                String mc = map.get("Main-Class");
                                if (mc != null && !mc.isEmpty())
                                    return mc;
                            }
                        }
                    } catch (Exception e) {
                    } finally {
                        if (bigJar != null)
                            try {
                                bigJar.close();
                            } catch (IOException e1) {
                            }
                    }
                }
                return null;
            }

            // scala> System.getProperty("sun.java.command")
            // res4: String = scala.tools.nsc.MainGenericRunner -deprecation -feature
            // -Dscala.color
            String name = null;
            if (cmd.indexOf(' ') != -1) {
                name = cmd.substring(0, cmd.indexOf(' '));
            } else {
                name = cmd;
            }
            try {
                Class<?> clz = Thread.currentThread().getContextClassLoader().loadClass(name);
                return clz.getName();
            } catch (ClassNotFoundException e1) {
            }
        }
        return null;
    }

    public static final String getTomcatServerInfo() {
        String serverInfo = null;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName("Catalina:type=Server");
            Object attr = mbeanServer.getAttribute(accLogValve, "serverInfo");
            if (attr != null) {
                if (attr instanceof String)
                    serverInfo = (String) attr;
                else
                    serverInfo = attr.toString();
            }
        } catch (Exception e) {
        }
        return serverInfo;
    }

    public static final long getDispatcherServletMaxTime() {
        long maxTime = -1;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName(
                    "Catalina:j2eeType=Servlet,name=dispatcher,WebModule=//localhost/,J2EEApplication=none,J2EEServer=none");
            Object attr = mbeanServer.getAttribute(accLogValve, "maxTime");
            if (attr != null) {
                if (attr instanceof Long)
                    maxTime = (Long) attr;
                else
                    maxTime = Long.parseLong(attr.toString());
            }
        } catch (Exception e) {
        }
        return maxTime;
    }

    public static final long getDispatcherServletMinTime() {
        long minTime = -1;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName(
                    "Catalina:j2eeType=Servlet,name=dispatcher,WebModule=//localhost/,J2EEApplication=none,J2EEServer=none");
            Object attr = mbeanServer.getAttribute(accLogValve, "minTime");
            if (attr != null) {
                if (attr instanceof Long)
                    minTime = (Long) attr;
                else
                    minTime = Long.parseLong(attr.toString());
            }
        } catch (Exception e) {
        }
        return minTime;
    }

    public static final long getDispatcherServletProcessingTime() {
        long processingTime = -1;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName(
                    "Catalina:j2eeType=Servlet,name=dispatcher,WebModule=//localhost/,J2EEApplication=none,J2EEServer=none");
            Object attr = mbeanServer.getAttribute(accLogValve, "processingTime");
            if (attr != null) {
                if (attr instanceof Long)
                    processingTime = (Long) attr;
                else
                    processingTime = Long.parseLong(attr.toString());
            }
        } catch (Exception e) {
        }
        return processingTime;
    }

    public static final int getDispatcherServletRequestCount() {
        int requestCount = -1;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName(
                    "Catalina:j2eeType=Servlet,name=dispatcher,WebModule=//localhost/,J2EEApplication=none,J2EEServer=none");
            Object attr = mbeanServer.getAttribute(accLogValve, "requestCount");
            if (attr != null) {
                if (attr instanceof Integer)
                    requestCount = (Integer) attr;
                else
                    requestCount = Integer.parseInt(attr.toString());
            }
        } catch (Exception e) {
        }
        return requestCount;
    }

    public static final int getDispatcherServletErrorCount() {
        int errorCount = -1;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName(
                    "Catalina:j2eeType=Servlet,name=dispatcher,WebModule=//localhost/,J2EEApplication=none,J2EEServer=none");
            Object attr = mbeanServer.getAttribute(accLogValve, "errorCount");
            if (attr != null) {
                if (attr instanceof Integer)
                    errorCount = (Integer) attr;
                else
                    errorCount = Integer.parseInt(attr.toString());
            }
        } catch (Exception e) {
        }
        return errorCount;
    }

    public static final String getTomcatAccessLogFileDateFormat() {
        String fileDateFormat = null;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName("Catalina:type=Valve,host=localhost,name=AccessLogValve");
            Object attr = mbeanServer.getAttribute(accLogValve, "fileDateFormat");
            if (attr != null) {
                if (attr instanceof String)
                    fileDateFormat = (String) attr;
                else
                    fileDateFormat = attr.toString();
            }
        } catch (Exception e) {
        }
        return fileDateFormat;
    }

    public static final String getTomcatAccessLogPrefix() {
        String prefix = null;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName("Catalina:type=Valve,host=localhost,name=AccessLogValve");
            Object attr = mbeanServer.getAttribute(accLogValve, "prefix");
            if (attr != null) {
                if (attr instanceof String)
                    prefix = (String) attr;
                else
                    prefix = attr.toString();
            }
        } catch (Exception e) {
        }
        return prefix;
    }

    public static final String getTomcatAccessLogSuffix() {
        String suffix = null;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName("Catalina:type=Valve,host=localhost,name=AccessLogValve");
            Object attr = mbeanServer.getAttribute(accLogValve, "suffix");
            if (attr != null) {
                if (attr instanceof String)
                    suffix = (String) attr;
                else
                    suffix = attr.toString();
            }
        } catch (Exception e) {
        }
        return suffix;
    }

    public static final String getTomcatAccessLogDir() {
        String directory = null;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        try {
            ObjectName accLogValve = new ObjectName("Catalina:type=Valve,host=localhost,name=AccessLogValve");
            if (accLogValve != null) {
                Object attr = mbeanServer.getAttribute(accLogValve, "directory");
                if (attr != null) {
                    if (attr instanceof String)
                        directory = (String) attr;
                    else
                        directory = attr.toString();
                }
            }
        } catch (Exception e) {
        }

        if (directory == null) {
            // 再尝试 Catalina:type=Valve,host=localhost,name=ExtendedAccessLogValve
            try {
                ObjectName extAccLogValve = new ObjectName(
                        "Catalina:type=Valve,host=localhost,name=ExtendedAccessLogValve");
                if (extAccLogValve != null) {
                    Object attr = mbeanServer.getAttribute(extAccLogValve, "directory");
                    if (attr != null) {
                        if (attr instanceof String)
                            directory = (String) attr;
                        else
                            directory = attr.toString();
                    }
                }
            } catch (Exception e) {
            }
        }

        if (directory != null) {
            File logDir = new File(directory);
            // mbean 里暴露的日志目录是相对路径，可能不可访问
            if (logDir.exists() && logDir.isDirectory())
                return directory;
            // 再尝试绝对路径
            logDir = new File(getTomcatBaseDir(), directory);
            if (logDir.exists() && logDir.isDirectory())
                return logDir.getAbsolutePath();
        }

        return null;
    }

    public static final String getTomcatBaseDir() {
        String baseDir = null;
        MBeanServer mbeanServer = ManagementFactory.getPlatformMBeanServer();
        ObjectName engine = null;
        Object attr = null;
        try {
            engine = new ObjectName("Catalina:type=Engine");
            attr = mbeanServer.getAttribute(engine, "baseDir");
        } catch (AttributeNotFoundException anf) {
            // 在 8.5 版本里 baseDir 变成了 catalinaBase
            try {
                attr = mbeanServer.getAttribute(engine, "catalinaBase");
            } catch (Exception e) {
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        if (attr != null) {
            if (attr instanceof String)
                baseDir = (String) attr;
            else
                baseDir = attr.toString();
        }
        return baseDir;
    }

    public static final String parseJarVersion(Class<?> clz, String jarFilePrefix) {
        String path = clz.getProtectionDomain().getCodeSource().getLocation().getPath();
        if (path == null || path.isEmpty())
            return null;
        String version = null;
        try {
            // String prefix = "redis-client-all-in-";
            path = URLDecoder.decode(path, "utf-8");// 处理路径带中文空格问题
            File file = new java.io.File(path);
            if (file.exists() && file.isFile()) {
                String fn = file.getName();
                if (fn.startsWith(jarFilePrefix) && fn.endsWith(".jar")) {
                    version = fn.substring(jarFilePrefix.length(), fn.lastIndexOf("."));
                }
            } else {
                // eg:
                // file:/D:/app/target/xxx-service.jar!/lib/redis-client-all-in-2.9.0-SNAPSHOT.jar!/
                if (path.indexOf(jarFilePrefix) != -1 && path.indexOf(".jar!") != -1) {
                    String[] sa = path.split("[/]");
                    String fn = sa[sa.length - 1];
                    if (fn.startsWith(jarFilePrefix))
                        version = fn.substring(jarFilePrefix.length(), fn.lastIndexOf('.'));
                }
            }
        } catch (Throwable t) {
        }
        return version;
    }


}
