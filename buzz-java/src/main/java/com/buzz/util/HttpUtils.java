package com.buzz.util;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.util.Iterator;
import java.util.Map;


public class HttpUtils {

	static public HttpResult httpGet(String url, Map<String, String> headers, Map<String, String> paramValues,
			String encoding, long readTimeoutMs) throws IOException {
		String encodedContent = encodingParams(paramValues, encoding);
		url += (null == encodedContent) ? "" : ("?" + encodedContent);

		HttpURLConnection conn = null;
		try {
			conn = (HttpURLConnection) new URL(url).openConnection();
			conn.setRequestMethod("GET");
			conn.setConnectTimeout(3000);
			conn.setReadTimeout((int) readTimeoutMs);
			setHeaders(conn, headers, encoding);

			conn.connect();
			int respCode = conn.getResponseCode();
			String resp;

			if (HttpURLConnection.HTTP_OK == respCode) {
				resp = IOUtils.toString(conn.getInputStream(), encoding);
			} else {
				resp = IOUtils.toString(conn.getErrorStream(), encoding);
			}
			return new HttpResult(respCode, resp);
		} finally {
			if (conn != null) {
				conn.disconnect();
			}
		}
	}

	static public HttpResult httpPost(String url, Map<String, String> headers, Map<String, String> paramValues,
			String encoding, long readTimeoutMs) throws IOException {
		String encodedContent = encodingParams(paramValues, encoding);

		HttpURLConnection conn = null;
		try {
			conn = (HttpURLConnection) new URL(url).openConnection();
			conn.setRequestMethod("POST");
			conn.setConnectTimeout(3000);
			conn.setReadTimeout((int) readTimeoutMs);
			conn.setDoOutput(true);
			conn.setDoInput(true);
			setHeaders(conn, headers, encoding);

			conn.getOutputStream().write(encodedContent.getBytes());

			int respCode = conn.getResponseCode();
			String resp;

			if (HttpURLConnection.HTTP_OK == respCode) {
				resp = IOUtils.toString(conn.getInputStream(), encoding);
			} else {
				resp = IOUtils.toString(conn.getErrorStream(), encoding);
			}
			return new HttpResult(respCode, resp);
		} finally {
			if (null != conn) {
				conn.disconnect();
			}
		}
	}

	static private void setHeaders(HttpURLConnection conn, Map<String, String> headers, String encoding) {
		if (null != headers) {
			for (Iterator<String> iter = headers.keySet().iterator(); iter.hasNext();) {
				String key = iter.next();
				conn.addRequestProperty(key, headers.get(key));
			}
		}
		conn.addRequestProperty("Content-Type", "application/x-www-form-urlencoded;charset=" + encoding);
	}

	static private String encodingParams(Map<String, String> paramValues, String encoding)
			throws UnsupportedEncodingException {
		StringBuilder sb = new StringBuilder();
		if (null == paramValues) {
			return null;
		}

		for (Iterator<String> iter = paramValues.keySet().iterator(); iter.hasNext();) {
			String key = iter.next();
			sb.append(key).append("=");
			sb.append(URLEncoder.encode(paramValues.get(key), encoding));
			if (iter.hasNext()) {
				sb.append("&");
			}
		}
		return sb.toString();
	}

	public static class HttpResult {
		final public int code;
		final public String content;

		public HttpResult(int code, String content) {
			this.code = code;
			this.content = content;
		}
	}
}
