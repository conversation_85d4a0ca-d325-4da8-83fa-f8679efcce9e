package com.buzz.util;

import java.util.function.LongConsumer;

/**
 * note not thread safe
 *
 * <AUTHOR>
 * @description
 **/
public class StopWatch {

    private long startTimeMillis;
    private long elapsed;

    public static StopWatch create() {
        return new StopWatch();
    }

    public StopWatch start() {
        startTimeMillis = System.currentTimeMillis();
        return this;
    }

    public StopWatch stop() {
        elapsed = System.currentTimeMillis() - startTimeMillis;
        return this;
    }

    public long elapsed() {
        return elapsed;
    }

    /**
     * if elapsed more than the timeout the function will be invoked
     *
     * @param timeout
     * @param function
     * @return
     */
    public StopWatch trigger(int timeout, LongConsumer function) {
        if (timeout - elapsed < 0) {
            function.accept(elapsed);
        }
        return this;
    }

}
