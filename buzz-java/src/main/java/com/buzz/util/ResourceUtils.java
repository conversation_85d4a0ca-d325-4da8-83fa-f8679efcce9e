package com.buzz.util;

import org.w3c.dom.Document;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import javax.xml.parsers.DocumentBuilder;
import javax.xml.parsers.DocumentBuilderFactory;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URL;
import java.net.URLDecoder;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public class ResourceUtils {

    /**
     * <p>
     * find pom file in current class path, if can't find it will return null
     * </p>
     *
     * @return
     */
    public static String findPomFileInClassPath() {
        try {
            ClassLoader classLoader = ClassLoader.getSystemClassLoader();
            List<URL> urls = Collections.list(classLoader.getResources(""));
            for (URL url : urls) {
                String path = url.getPath();
                if (path.contains("/target/")) {
                    String pomPath = path.substring(0, path.indexOf("/target/")) + "/pom.xml";
                    try {
                        pomPath = URLDecoder.decode(pomPath, "utf-8");// 处理路径带中文问题
                    } catch (UnsupportedEncodingException e) {
                    }
                    return pomPath;
                }
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * <p>
     * find pom and resolve it's content to get artifact in current class path,  if can't find it will return null
     * </p>
     *
     * @return
     */
    public static String findArtifactIdInClassPath() {
        try {
            String pomPath = findPomFileInClassPath();
            if (pomPath == null) {
                return null;
            }
            File pomFile = new File(pomPath);
            if (pomFile.exists()) {
                return resolveArtifactIdFromPom(new FileInputStream(pomFile));
            }
        } catch (Throwable e) {
            e.printStackTrace();
        }
        return null;
    }

    /**
     * <p>
     * read pom's input stream and resolve artifact
     * </p>
     *
     * @param in
     * @return
     */
    public static String resolveArtifactIdFromPom(InputStream in) throws Exception {
        // 应用依赖了 org.apache.xerces的话，可能会使用xeres来解析而导致 Error
        // java.lang.AbstractMethodError:
        // org.apache.xerces.dom.DeferredElementNSImpl.getTextContent()Ljava/lang/String;
        // 强制使用 com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl
        // jdk9之后无法直接访问internal package，所以通过反射
        DocumentBuilderFactory dbf = (DocumentBuilderFactory) Class.forName("com.sun.org.apache.xerces.internal.jaxp.DocumentBuilderFactoryImpl").newInstance();
        dbf.setValidating(false);
        dbf.setNamespaceAware(true);

        DocumentBuilder db = dbf.newDocumentBuilder();
        Document doc = db.parse(in);
        doc.getDocumentElement().normalize();
        Node projectNode = doc.getFirstChild();

        NodeList list = projectNode.getChildNodes();
        for (int i = 0; i < list.getLength(); i++) {
            Node n = list.item(i);
            String nodeName = n.getNodeName();
            if (nodeName.equals("artifactId"))
                return n.getTextContent();
        }
        throw new RuntimeException("can't find artifactId from pom");
    }

    /**
     * list all dependency jars in current class path
     *
     * @return
     */
    public static Set<String> listJarsInClassPath() {
        String pathSeparator = System.getProperty("path.separator");
        String fileSeparator = System.getProperty("file.separator");
        String[] paths = System.getProperty("java.class.path").split(pathSeparator);
        String javaHome = System.getProperty("java.home");
        String jvmHome = javaHome.substring(0, javaHome.lastIndexOf(fileSeparator));
        Set<String> appDependencies = new HashSet<String>();

        for (String filePath : paths) {
            if (filePath.endsWith(".jar") && !filePath.startsWith(jvmHome)) {
                int i = filePath.lastIndexOf(pathSeparator);
                if (i != -1) {
                    appDependencies.add(filePath.substring(i + 1));
                } else {
                    appDependencies.add(filePath);
                }
            }
        }
        return appDependencies;
    }

}
