package com.buzz.util;

import java.io.*;
import java.nio.ByteBuffer;
import java.nio.charset.CharacterCodingException;
import java.nio.charset.Charset;
import java.nio.charset.CharsetDecoder;
import java.util.*;

/**
 * clone from dubbo IOUtils, apache.commons.io.IOUtils
 * 
 *
 */
public class IOUtils {

    public static List<String> readLines(String file) throws IOException {
        return readLines(new File(file));
    }
	/**
	 * read lines.
	 * 
	 * @param file file.
	 * @return lines.
	 * @throws IOException
	 */
	public static List<String> readLines(File file) throws IOException {
		if (file == null || !file.exists() || !file.canRead())
			return Collections.EMPTY_LIST;

		return readLines(new FileInputStream(file));
	}

	/**
	 * read lines.
	 * 
	 * @param is input stream.
	 * @return lines.
	 * @throws IOException
	 */
	public static List<String> readLines(InputStream is) throws IOException {
		List<String> lines = new ArrayList<String>();
		BufferedReader reader = new BufferedReader(new InputStreamReader(is));
		try {
			String line;
			while ((line = reader.readLine()) != null)
				lines.add(line);
			return lines;
		} finally {
			reader.close();
		}
	}

	public static String readFile(File file) throws IOException {
	    return readFile(file,"UTF-8");
    }
    public static String readFile(String file) throws IOException {
        return readFile(new File(file));
    }

	public static String readFile(File file, String encode) throws IOException {
        if (!file.exists() || !file.isFile()) {
            return null;
        }

        InputStream is = null;
        try {
            is = new FileInputStream(file);
            return toString(is, encode);
        } finally {
            try {
                if (null != is) {
                    is.close();
                }
            } catch (IOException ioe) {
            }
        }
    }

    public static void deleteFile(File file) throws IOException {
        if (file == null) {
            return;
        }

        if (file.isDirectory()) {
            cleanDirectory(file);
        }
        file.delete();
    }

    public static void writeFile(String file, String content) throws IOException {
        writeFile(new File(file),content,"utf-8");
    }

    public static void writeFile(File file, String content, String encode) throws IOException {
        if (content == null) {
            throw new RuntimeException("content can not be null");
        }
        OutputStream os = null;
        try {
            os = new FileOutputStream(file);
            os.write(content.getBytes(encode));
        } finally {
            if (null != os) {
                os.close();
            }
        }
    }

    static public String toString(InputStream input, String encoding) throws IOException {
        return (null == encoding) ? toString(new InputStreamReader(input))
                : toString(new InputStreamReader(input, encoding));
    }

    static public String toString(Reader reader) throws IOException {
        CharArrayWriter sw = new CharArrayWriter();
        copy(reader, sw);
        return sw.toString();
    }

    static public long copy(Reader input, Writer output) throws IOException {
        char[] buffer = new char[1 << 12];
        long count = 0;
        for (int n = 0; (n = input.read(buffer)) >= 0; ) {
            output.write(buffer, 0, n);
            count += n;
        }
        return count;
    }

    /**
     * 安全的关闭流，放到try-finally块中执行
     *
     * @param stream
     */
    public static void close(Closeable stream) {
        if (stream == null)
            return;
        try {
            stream.close();
        } catch (IOException ioe) {
            // ignore
        }
    }

    /**
     * 安全的关闭流，放到try-finally块中执行
     *
     * @param streams
     */
    public static void close(Closeable... streams) {
        if (streams == null || streams.length == 0)
            return;

        for (Closeable c : streams) {
            try {
                if (c != null) {
                    c.close();
                }
            } catch (IOException ioe) {
                // ignore
            }
        }
    }

    /**
     * 将数据写入文件
     *
     * @param data
     * @param file
     * @throws IOException
     */
    public static void writeToFile(byte[] data, File file) throws IOException {
        // if (data == null || data.length == 0 || file == null) return;
        if (data == null || file == null)
            return; // 当data长度为空时清空文件

        BufferedOutputStream bos = null;
        try {
            bos = new BufferedOutputStream(new FileOutputStream(file));
            bos.write(data);
        } finally {
            close(bos);
        }
    }

    /**
     * 将字符串列表写入文件
     *
     * @param strList
     * @param file
     * @throws IOException
     */
    public static void writeToFile(List<String> strList, File file) throws IOException {
        if (strList == null || file == null)
            return;

        BufferedWriter bw = null;
        try {
            bw = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file)));
            for (String line : strList) {
                bw.write(line);
                bw.newLine();
            }
        } finally {
            close(bw);
        }
    }

    /**
     * 查找指定目录中的所有子目录和文件(不会递归)
     *
     * @param dir
     *            要查找的目录
     * @return 如果要查找的目录不存在返回一个长度为0的File数组，否则所有子目录和文件(不会递归)。
     */
    public static File[] listAll(File dir) {
        File[] files = null;
        if (dir.exists()) {
            files = dir.listFiles();
        }

        if (files == null)
            return new File[0];
        else
            return files;
    }

    /**
     * 查找指定目录中某一类型的文件
     *
     * @param dir
     *            要查找的目录
     * @param suffix
     *            文件名后缀，通常是文件扩展名，如".jar"，必需小写。
     * @return 如果要查找的目录不存在返回一个长度为0的File数组，否则返回所有以suffix结尾的文件。
     */
    public static File[] listFiles(final File dir, final String suffix) {
        File[] files = null;
        if (dir.exists()) {
            files = dir.listFiles(new FileFilter() {
                public boolean accept(File pathName) {
                    if ((pathName.isFile()) && (pathName.getName().toLowerCase(Locale.ENGLISH).endsWith(suffix))) {
                        return true;
                    }
                    return false;
                }
            });

        }

        if (files == null)
            return new File[0];
        else
            return files;
    }

    /**
     * 查找指定目录中某一类型的子目录(不含递归子目录)
     *
     * @param dir
     *            要查找的目录
     * @param suffix
     *            子目录名后缀，必需小写。
     * @return 如果要查找的目录不存在返回一个长度为0的File数组，否则返回所有以suffix结尾的子目录。
     */
    public static File[] listDirs(final File dir, final String suffix) {
        File[] files = null;
        if (dir.exists()) {
            files = dir.listFiles(new FileFilter() {
                public boolean accept(File pathName) {
                    if ((pathName.isDirectory()) && (pathName.getName().toLowerCase(Locale.ENGLISH).endsWith(suffix))) {
                        return true;
                    }
                    return false;
                }
            });

        }

        if (files == null)
            return new File[0];
        else
            return files;
    }

    /**
     * 列出目录下所有符合该后缀的文件
     *
     * @param directory
     *            目标目录
     * @param suffix
     *            后缀
     * @param recursive
     *            是否递归
     * @return
     */
    public static Set<File> listFiles(File directory, String suffix, boolean recursive) {
        if (directory == null || !directory.exists() || !directory.isDirectory())
            return new HashSet<File>(0);

        Set<File> files = new HashSet<File>();
        for (File file : directory.listFiles()) {
            if (file.isFile() && file.getName().endsWith(suffix)) {
                files.add(file);
            } else if (file.isDirectory() && recursive) {
                files.addAll(listFiles(file, suffix, recursive));
            }
        }
        return files;
    }

    /**
     * 递归的查找某个目录及其子目录下所有符合该名字的文件
     *
     * @param directory
     * @param name
     * @return
     */
    public static Set<File> findFiles(File directory, String name) {
        if (directory == null || !directory.exists() || !directory.isDirectory())
            return new HashSet<File>(0);

        Set<File> files = new HashSet<File>();
        for (File file : directory.listFiles()) {
            if (file.isFile() && file.getName().equals(name)) {
                files.add(file);
            } else if (file.isDirectory()) {
                files.addAll(findFiles(file, name));
            }
        }
        return files;
    }

    /**
     * Cleans a directory without deleting it.
     *
     * @param directory
     *            directory to clean, must not be {@code null}
     * @throws NullPointerException
     *             if the directory is {@code null}
     * @throws IOException
     *             in case cleaning is unsuccessful
     */
    private static void cleanDirectoryOnExit(File directory) throws IOException {
        if (!directory.exists()) {
            String message = directory + " does not exist";
            throw new IllegalArgumentException(message);
        }

        if (!directory.isDirectory()) {
            String message = directory + " is not a directory";
            throw new IllegalArgumentException(message);
        }

        File[] files = directory.listFiles();
        if (files == null) { // null if security restricted
            throw new IOException("Failed to list contents of " + directory);
        }

        IOException exception = null;
        for (File file : files) {
            try {
                forceDeleteOnExit(file);
            } catch (IOException ioe) {
                exception = ioe;
            }
        }

        if (null != exception) {
            throw exception;
        }
    }

    /**
     * Cleans a directory without deleting it.
     *
     * @param directory
     *            directory to clean
     * @throws IOException
     *             in case cleaning is unsuccessful
     */
    public static void cleanDirectory(File directory) throws IOException {
        if (!directory.exists()) {
            String message = directory + " does not exist";
            throw new IllegalArgumentException(message);
        }

        if (!directory.isDirectory()) {
            String message = directory + " is not a directory";
            throw new IllegalArgumentException(message);
        }

        File[] files = directory.listFiles();
        if (files == null) { // null if security restricted
            throw new IOException("Failed to list contents of " + directory);
        }

        IOException exception = null;
        for (File file : files) {
            try {
                forceDelete(file);
            } catch (IOException ioe) {
                exception = ioe;
            }
        }

        if (null != exception) {
            throw exception;
        }
    }

    public static void forceDelete(File file) throws IOException {
        if (file.isDirectory()) {
            deleteDirectory(file);
        } else {
            boolean filePresent = file.exists();
            if (!file.delete()) {
                if (!filePresent) {
                    throw new FileNotFoundException("File does not exist: " + file);
                }
                String message = "Unable to delete file: " + file;
                throw new IOException(message);
            }
        }
    }

    /**
     * Deletes a directory recursively.
     *
     * @param directory
     *            directory to delete
     * @throws IOException
     *             in case deletion is unsuccessful
     */
    public static void deleteDirectory(File directory) throws IOException {
        if (!directory.exists()) {
            return;
        }

        if (!isSymlink(directory)) {
            cleanDirectory(directory);
        }

        if (!directory.delete()) {
            String message = "Unable to delete directory " + directory + ".";
            throw new IOException(message);
        }
    }

    /**
     * Schedules a file to be deleted when JVM exits.
     * If file is directory delete it and all sub-directories.
     *
     * @param file
     *            file or directory to delete, must not be {@code null}
     * @throws NullPointerException
     *             if the file is {@code null}
     * @throws IOException
     *             in case deletion is unsuccessful
     */
    public static void forceDeleteOnExit(File file) throws IOException {
        if (file.isDirectory()) {
            deleteDirectoryOnExit(file);
        } else {
            file.deleteOnExit();
        }
    }

    /**
     * Schedules a directory recursively for deletion on JVM exit.
     *
     * @param directory
     *            directory to delete, must not be {@code null}
     * @throws NullPointerException
     *             if the directory is {@code null}
     * @throws IOException
     *             in case deletion is unsuccessful
     */
    private static void deleteDirectoryOnExit(File directory) throws IOException {
        if (!directory.exists()) {
            return;
        }

        directory.deleteOnExit();
        if (!isSymlink(directory)) {
            cleanDirectoryOnExit(directory);
        }
    }

    /**
     * Determines whether the specified file is a Symbolic Link rather than an actual file.
     * <p>
     * Will not return true if there is a Symbolic Link anywhere in the path, only if the specific file is.
     * <p>
     * <b>Note:</b> the current implementation always returns {@code false} if the system is detected as Windows using
     *
     * @param file
     *            the file to check
     * @return true if the file is a Symbolic Link
     * @throws IOException
     *             if an IO error occurs while checking the file
     * @since 2.0
     */
    public static boolean isSymlink(File file) throws IOException {
        if (file == null) {
            throw new NullPointerException("File must not be null");
        }

        File fileInCanonicalDir = null;
        if (file.getParent() == null) {
            fileInCanonicalDir = file;
        } else {
            File canonicalDir = file.getParentFile().getCanonicalFile();
            fileInCanonicalDir = new File(canonicalDir, file.getName());
        }

        if (fileInCanonicalDir.getCanonicalFile().equals(fileInCanonicalDir.getAbsoluteFile())) {
            return false;
        } else {
            return true;
        }
    }

    /**
     * 按指定编码解析流
     *
     * @param is
     * @param charset
     * @return
     * @throws IOException
     */
    public static String parseString(InputStream is, String charset) throws IOException {
        if (is == null || charset == null)
            throw new IllegalArgumentException("param error");

        return tryParseString(is, charset);
    }

    /**
     * 解析字符流，按照后边指定的编码，先按照第一个编码尝试，不成功再按后边的编码尝试
     *
     * @param is
     * @param charsets
     * @return
     * @throws IOException
     */
    public static String tryParseString(InputStream is, String... charsets) throws IOException {
        if (is == null || charsets == null || charsets.length == 0) {
            throw new IllegalArgumentException("param error");
        }

        BufferedInputStream input = null;
        BufferedOutputStream output = null;
        byte[] buf = null;
        try {
            buf = new byte[128];
            input = new BufferedInputStream(is);
            ByteArrayOutputStream outBuf = new ByteArrayOutputStream(128);
            output = new BufferedOutputStream(outBuf);
            int len = 0;
            while ((len = input.read(buf)) != -1) {
                output.write(buf, 0, len);
            }
            output.flush();

            buf = outBuf.toByteArray();
        } finally {
            close(input, output);
        }

        IOException e = null;
        for (String charset : charsets) {
            try {
                // 按指定的编码解析
                CharsetDecoder decoder = Charset.forName(charset).newDecoder();
                // 若编码不匹配，会抛出 java.nio.charset.MalformedInputException
                // 或UnmappableCharacterException
                return decoder.decode(ByteBuffer.wrap(buf)).toString();
            } catch (CharacterCodingException e1) {
                e = e1;
            }
        }
        throw e;
    }
}
