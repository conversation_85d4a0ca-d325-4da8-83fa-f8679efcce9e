package com.buzz.http;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.InetSocketAddress;

import com.sun.net.httpserver.HttpExchange;
import com.sun.net.httpserver.HttpHandler;
import com.sun.net.httpserver.HttpServer;

/**
 * @see io.prometheus.client.exporter.HTTPServer
 * 
 * <AUTHOR>
 *
 */
@SuppressWarnings("restriction")
public class HttpServerDemo {

	private static class LocalByteArray extends ThreadLocal<ByteArrayOutputStream> {
		protected ByteArrayOutputStream initialValue() {
			return new ByteArrayOutputStream(1 << 20);
		}
	}

	private HttpServer server;

	public HttpServerDemo(int port) throws IOException {
		this(new InetSocketAddress(port));
	}

	public HttpServerDemo(InetSocketAddress addr) throws IOException {

		server = HttpServer.create(addr, 3);
		server.createContext("/", new BaseHttpHandler());
	}

	public void start() {
		server.start();

	}

	private static class BaseHttpHandler implements HttpHandler {

		private final LocalByteArray response = new LocalByteArray();

		@Override
		public void handle(HttpExchange t) throws IOException {
			ByteArrayOutputStream response = this.response.get();
			response.reset();
			OutputStreamWriter osw = new OutputStreamWriter(response);
			osw.write("hello world");
			osw.flush();
			osw.close();
			response.flush();
			response.close();
			t.getResponseHeaders().set("Content-Type", "text/plain; version=0.0.4; charset=utf-8");
			t.getResponseHeaders().set("Content-Length", String.valueOf(response.size()));
			t.sendResponseHeaders(HttpURLConnection.HTTP_OK, response.size());
			response.writeTo(t.getResponseBody());
		}

	}

	public static void main(String[] args) throws IOException {
		new HttpServerDemo(8888).start();
	}
}
