package com.buzz.javassist;

import java.io.IOException;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.PriorityBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 **/
public class TestQueueTest {
    public static void main(String[] args) throws IOException {
        BlockingQueue queue = new PriorityBlockingQueue();
        Task task1 = new Task(1);
        Task task2 = new Task(2);
        Task task3 = new Task(3);
        // 模拟任务提交速度大于线程处理速度
        queue.add(task3);
        queue.add(task2);
        queue.add(task1);

        ThreadPoolExecutor executor = new ThreadPoolExecutor(1, 1, 0L, TimeUnit.MILLISECONDS, queue);
        executor.execute(task3);
        System.in.read();
    }
    static class Task implements Runnable, Comparable<Task>{
        int priority;
        public Task(int priority) {
            this.priority = priority;
        }

        @Override
        public int compareTo(Task o) {
            return this.priority - o.priority;
        }

        @Override
        public String toString() {
            return "Task{" +
                    "priority=" + priority +
                    '}';
        }

        @Override
        public void run() {
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            System.out.println(this.toString());
        }
    }

}
