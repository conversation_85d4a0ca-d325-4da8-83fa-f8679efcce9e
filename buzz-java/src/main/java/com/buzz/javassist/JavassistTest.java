package com.buzz.javassist;

import javassist.*;
import org.junit.Test;

import java.util.Comparator;

/**
 * <AUTHOR>
 * @description
 **/
public class JavassistTest {
    private static final String PRIORITY_BLOCKING_QUEUE_CLASS_NAME = "java.util.concurrent.PriorityBlockingQueue";
    private static final String PRIORITY_QUEUE_CLASS_NAME = "java.util.PriorityQueue";
    private static final String COMPARATOR_FIELD_NAME = "comparator";
    /**
     * wrap comparator field in constructors
     */
    private static final String CODE = "this." + COMPARATOR_FIELD_NAME + " = "
            + JavassistTest.class.getName() +
            ".overwriteComparatorField$by$ttl(this." + COMPARATOR_FIELD_NAME + ");";

    @Test
    public void test() throws NotFoundException, CannotCompileException {
        ClassPool pool = ClassPool.getDefault();

        CtClass ctClass =pool.get("java.util.concurrent.PriorityBlockingQueue");
        if(ctClass.getDeclaredField("comparator")!=null){
            for (CtConstructor constructor : ctClass.getDeclaredConstructors()) {
                constructor.insertAfter(CODE);
            }
        }
    }


    public static Comparator<Runnable> overwriteComparatorField$by$ttl(Comparator<Runnable> comparator){
        if (comparator == null){

        }else{

        }
        return null;
    }


}
