package com.buzz.metric.prometheus;

import org.eclipse.jetty.server.Server;
import org.eclipse.jetty.servlet.ServletContextHandler;
import org.eclipse.jetty.servlet.ServletHolder;
import org.junit.Test;

import com.codahale.metrics.Counter;
import com.codahale.metrics.Histogram;
import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;

import io.prometheus.client.CollectorRegistry;
import io.prometheus.client.dropwizard.DropwizardExports;
import io.prometheus.client.exporter.MetricsServlet;

public class MetricExportTest {

	// Create registry for Dropwizard metrics.
	static final MetricRegistry metrics = new MetricRegistry();

	@Test
	public void test() throws Exception {
		// Hook the Dropwizard registry into the Prometheus registry
		// via the DropwizardExports collector.
		CollectorRegistry.defaultRegistry.register(new DropwizardExports(metrics));

		metrics();
		// Expose Prometheus metrics.
		Server server = new Server(8080);
		ServletContextHandler context = new ServletContextHandler();
		context.setContextPath("/");
		server.setHandler(context);
		context.addServlet(new ServletHolder(new MetricsServlet()), "/metrics");

		server.start();
		server.join();
	}

	protected void metrics() {

		Counter counter = metrics.counter("error.count");
		for (int i = 0; i < 10; ++i) {
			counter.inc();
		}

		Meter meter = metrics.meter("index.request");
		for (int i = 0; i < 10; ++i) {
			meter.mark();
		}

		Histogram histogram = metrics.histogram("get.request.rt");

		int a = 0;
		int b = 0;
		int c = 0;

		for (int i = 0; i < 100; ++i) {
			if (i < 80) {
				histogram.update(1);
				++a;
			} else if (i < 90) {
				histogram.update(10);
				++b;
			} else {
				histogram.update(50);
				++c;
			}
		}

		System.out.println(a + "," + b + "," + c);
		System.out.println(
				histogram.getSnapshot().getMedian() + "\t"
						+ histogram.getSnapshot().get75thPercentile() + "\t"
						+ histogram.getSnapshot().get95thPercentile() + "\t"
						+ histogram.getSnapshot().getMax());

	}
}
