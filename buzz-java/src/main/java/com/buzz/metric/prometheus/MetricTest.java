package com.buzz.metric.prometheus;

import com.alibaba.fastjson.JSON;
import io.prometheus.client.*;
import org.junit.Test;

/**
 * prometheus metric
 * <AUTHOR>
 * @description
 **/
public class MetricTest {

    @Test
    public void testCounter() {
        Counter requests = Counter.build()
                .name("requests_total")
                .help("Total requests.")
                .register();

        requests.inc();
        System.out.println(requests.get());
    }

    @Test
    public void testGauge() {
        Gauge requests = Gauge.build()
                .name("requests_total")
                .help("Total requests.")
                .register();

        requests.inc();
        requests.dec();
        System.out.println(requests.get());
    }


    @Test
    public void testHistogram() {
        Histogram histogram = Histogram.build()
                .buckets(10, 20, 30, 40, 50, 60, 70, 80, 90, 100)//定义bucket
                .name("latency")
                .help("latency")
                .register();


        for (int i = 0; i < 100; ++i) {
            histogram.observe(i);
        }

        for (int i = 0; i < 100; ++i) {
            histogram.observe(5);
        }

        for(Collector.MetricFamilySamples samples :histogram.collect()){
            System.out.println(JSON.toJSONString(samples));
        }

    }


    @Test
    public void testSummary() {
        Summary summary = Summary.build()
                .name("latency")
                .help("latency")
                .quantile(0.95, 0.01)
                .register();


        for (int i = 0; i < 100; ++i) {
            summary.observe(i);
        }

        for (int i = 0; i < 100; ++i) {
            summary.observe(5);
        }

        for(Collector.MetricFamilySamples samples :summary.collect()){
            System.out.println(JSON.toJSONString(samples));
        }

    }
}
