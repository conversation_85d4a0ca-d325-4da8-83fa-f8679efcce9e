package com.buzz.metric.zk.provider;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.PriorityQueue;
import java.util.Queue;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import com.buzz.metric.zk.Counter;
import com.buzz.metric.zk.MetricsContext;
import com.buzz.metric.zk.Trace;
import com.buzz.metric.zk.TraceItem;

import lombok.Data;

public class DefaultMetricsContext implements MetricsContext {

	private static final int DEFAULT_MAX_RECORD_SIZE = 50;

	private final Map<String, Counter> COUNTER_MAP = new ConcurrentHashMap<String, Counter>();

	private final Map<String, Trace> RECORD_MAP = new ConcurrentHashMap<String, Trace>();

	@Override
	public Counter getCounter(String metric) {
		Counter counter = COUNTER_MAP.get(metric);
		if (counter == null) {
			counter = new DefaultCounter();
			Counter existed = COUNTER_MAP.putIfAbsent(metric, counter);
			if (existed != null) {
				counter = existed;
			}
		}
		return counter;
	}

	@Override
	public Trace getTrace(String metric) {
		Trace record = RECORD_MAP.get(metric);
		if (record == null) {
			record = new DefaultTrace(DEFAULT_MAX_RECORD_SIZE);
			Trace existed = RECORD_MAP.putIfAbsent(metric, record);
			if (existed != null) {
				record = existed;
			}
		}
		return record;
	}

	private static class DefaultCounter implements Counter {

		private AtomicLong count = new AtomicLong();

		@Override
		public long inc() {
			return count.incrementAndGet();
		}

		@Override
		public long get() {
			return count.get();
		}

	}

	public static class DefaultTrace implements Trace {

		private final PriorityQueue<TraceItem> topRecords;

		private final Queue<TraceItem> history = new LinkedList<>();

		public DefaultTrace(int maxSize) {
			topRecords = new PriorityQueue<>(maxSize);
		}

		@Override
		public void addHistory(TraceItem item) {
			synchronized (this) {
				if (history.size() >= DEFAULT_MAX_RECORD_SIZE) {
					history.poll();
				}
			}
			history.offer(item);
		}

		@Override
		public void addTop(TraceItem item) {
			synchronized (this) {
				if (topRecords.size() >= DEFAULT_MAX_RECORD_SIZE) {
					if (item.getElapsedTotal() < topRecords.peek().getElapsedTotal()) {
						return;
					}
					// 最小的先出
					topRecords.poll();
				}
			}
			topRecords.offer(item);
		}

		@Override
		public List<TraceItem> getTop() {
			return new ArrayList<>(topRecords);
		}

		@Override
		public List<TraceItem> getHisotry() {
			return new ArrayList<>(history);
		}

		@Override
		public TraceItem createItem(String name) {
			return new DefaultTraceItem(name);
		}

	}

	@Data
	public static class DefaultTraceItem implements TraceItem, Comparable<TraceItem> {
		private String name;
		private long begin;
		private long end;
		private boolean error;
		private String errorMsg;
		private Map<String, Integer> timesMap = new HashMap<String, Integer>();

		public DefaultTraceItem(String name) {
			this.name = name;
			this.begin = System.currentTimeMillis();
		}

		@Override
		public void mark(String step) {
			timesMap.put(step, Long.valueOf(System.currentTimeMillis() - begin).intValue());
		}

		@Override
		public void finish() {
			this.end = System.currentTimeMillis();
		}

		@Override
		public void error(String msg) {
			this.error = true;
			this.errorMsg = msg;
			this.end = System.currentTimeMillis();
		}

		@Override
		public int getElapsed(String step) {
			Integer ret = timesMap.get(step);
			return (ret == null) ? 0 : ret;
		}

		@Override
		public int getElapsedTotal() {
			return (int) (end - begin);
		}

		@Override
		public String getName() {
			return name;
		}

		@Override
		public boolean isError() {
			return error;
		}

		@Override
		public String getErrorMsg() {
			return errorMsg;
		}

		@Override
		public int compareTo(TraceItem o) {
			return Long.compare(getElapsedTotal(), o.getElapsedTotal());
		}

		@Override
		public String toString() {
			return name + ",cost:" + (end - begin) + ",error:" + error;
		}
	}

}
