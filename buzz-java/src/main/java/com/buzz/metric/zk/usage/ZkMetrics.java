package com.buzz.metric.zk.usage;

import com.buzz.metric.zk.Counter;
import com.buzz.metric.zk.MetricsContext;
import com.buzz.metric.zk.Trace;
import com.buzz.metric.zk.provider.DefaultMetricsContextProvider;
import com.buzz.metric.zk.provider.MetricsContextProvider;

/**
 * zookeeper的指标集合，还可以创建其他指标集合，例如DubboMetrics
 * 
 * <AUTHOR>
 *
 */
public class ZkMetrics {

	private static ZkMetrics INSTANCE = new ZkMetrics();

	public final Counter SUCCESS_COUNTER; 

	public final Counter FAILED_COUNTER;

	public final Counter ERROR_COUNTER;

	public final Trace TRACE;

	private ZkMetrics() {
		MetricsContextProvider provider = new DefaultMetricsContextProvider();
		MetricsContext metricsContext = provider.getContext();
		SUCCESS_COUNTER = metricsContext.getCounter("DS_SUCCESS");
		FAILED_COUNTER = metricsContext.getCounter("DS_FAILED");
		ERROR_COUNTER = metricsContext.getCounter("DS_ERROR");
		TRACE = metricsContext.getTrace("DS_TRACE");
	}

	public static ZkMetrics getMetrics() {
		return INSTANCE;
	}

}
