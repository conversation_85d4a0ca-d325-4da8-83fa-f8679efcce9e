package com.buzz.metric.zk;

public interface TraceItem {

	/**
	 * 返回当前名称
	 * @return
	 */
	public String getName();

	/**
	 * 标记阶段
	 * @param step
	 */
	public void mark(String step);

	/**
	 * 结束
	 */
	public void finish();

	/**
	 * 失败
	 * @param msg
	 */
	public void error(String msg);
	
	/**
	 * 是否失败
	 * @return
	 */
	public boolean isError();
	
	/**
	 * 获取错误信息
	 * @return
	 */
	public String getErrorMsg();

	/**
	 * 获取阶段的耗时
	 * @param step
	 * @return
	 */
	public int getElapsed(String step);

	/**
	 * 获取总耗时
	 * @return
	 */
	public int getElapsedTotal();
}
