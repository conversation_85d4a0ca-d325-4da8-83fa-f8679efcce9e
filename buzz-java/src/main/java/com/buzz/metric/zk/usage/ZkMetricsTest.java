package com.buzz.metric.zk.usage;

import com.buzz.metric.zk.TraceItem;

public class ZkMetricsTest {

	public static void onLockedFail(TraceItem item, Exception e) throws InterruptedException {
		Thread.sleep(100);
		item.error(e == null ? null : e.getMessage());
		ZkMetrics.getMetrics().ERROR_COUNTER.inc();
		ZkMetrics.getMetrics().TRACE.addHistory(item);
		ZkMetrics.getMetrics().TRACE.addTop(item);
	}

	public static void onLocked(TraceItem item) throws InterruptedException {
		Thread.sleep(100);
		item.finish();
		ZkMetrics.getMetrics().SUCCESS_COUNTER.inc();
		ZkMetrics.getMetrics().TRACE.addHistory(item);
		ZkMetrics.getMetrics().TRACE.addTop(item);
	}

	public static void main(String[] args) throws InterruptedException {

		onLocked(ZkMetrics.getMetrics().TRACE.createItem("/test1"));
		onLockedFail(ZkMetrics.getMetrics().TRACE.createItem("/test2"), null);

		System.out.println(ZkMetrics.getMetrics().SUCCESS_COUNTER.get());
		System.out.println(ZkMetrics.getMetrics().ERROR_COUNTER.get());
		System.out.println(ZkMetrics.getMetrics().TRACE.getTop());

	}
}
