package com.buzz.metric.dropwizard;

import com.codahale.metrics.Counter;
import com.codahale.metrics.Histogram;
import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * Metric's best practices.
 *
 * <AUTHOR>
 * @description
 **/
public class EntryMetric {

    private static final Map<String, Counter> COUNTER_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Meter> METER_MAP = new ConcurrentHashMap<>();
    private static final Map<String, Histogram> HISTOGRAM_MAP = new ConcurrentHashMap<>();

    private static final MetricRegistry INNER_REGISTRY = new MetricRegistry();
    private static final Map<String, EntryStats> ENTRY_STAT_MAP = new ConcurrentHashMap<>();

    public static EntryStats entry(String entryName) {
        return ENTRY_STAT_MAP.computeIfAbsent(entryName, (k) -> {
            return new EntryStats(entryName);
        });
    }

    public static List<EntryStats> getAll(){
        return new ArrayList(ENTRY_STAT_MAP.values());
    }

    @Data
    public static class EntryStats {
        private String name;

        public EntryStats(String name) {
            this.name = name;
        }

        public Counter count(String name) {

            return COUNTER_MAP.computeIfAbsent(name, (k) -> {
                return INNER_REGISTRY.counter(name);
            });
        }

        public Meter meter(String name) {
            return METER_MAP.computeIfAbsent(name, (k) -> {
                return INNER_REGISTRY.meter(name);
            });
        }

        public Histogram histogram(String name) {
            return HISTOGRAM_MAP.computeIfAbsent(name, (k) -> {
                return INNER_REGISTRY.histogram(name);
            });
        }
    }
}
