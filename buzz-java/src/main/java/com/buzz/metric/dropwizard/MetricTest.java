package com.buzz.metric.dropwizard;

import com.buzz.util.FormatUtils;
import com.codahale.metrics.*;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import javax.servlet.Filter;
import java.util.LinkedList;
import java.util.Queue;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

import static java.lang.Math.exp;
import static org.junit.Assert.assertEquals;

/**
 * https://www.baeldung.com/dropwizard-metrics
 *
 * <AUTHOR>
 */
@Slf4j
public class MetricTest {

    MetricRegistry metricRegistry = new MetricRegistry();

    @Test
    public void testEntryMetric() {
        String entryName = "consumer_test1";
        //meter
        EntryMetric.entry(entryName).meter("request_count").mark();
        EntryMetric.entry(entryName).count("error_count").inc();
        //traffic
        int byteLength = 512;
        EntryMetric.entry(entryName).histogram("traffic").update(byteLength);
        EntryMetric.entry(entryName).count("traffic_sum").inc(byteLength);
        //latency
        int duration = 20;
        EntryMetric.entry(entryName).histogram("latency").update(duration);

        for (EntryMetric.EntryStats stats : EntryMetric.getAll()) {
            System.out.println(stats.getName() + "\t" + stats.count("error_count").getCount() + "\t" + stats.meter("request_count").getCount());
        }

    }

    @Test
    public void testNaming() throws InterruptedException {

        String name = MetricRegistry.name(Filter.class, "request", "count");
        System.out.println(name);
        String name2 = MetricRegistry.name("CustomFilter", "response", "count");
        System.out.println(name2);
    }

    @Test
    public void testGauge() throws InterruptedException {
        String name = MetricRegistry.name("MetricTest", "queue", "size");
        Queue<String> q = new LinkedList<String>();
        Gauge<Integer> gauge = metricRegistry.gauge(name, () -> new Gauge<Integer>() {
            @Override
            public Integer getValue() {
                return q.size();
            }
        });
        q.add("test1");
        q.add("test2");
        assertEquals(Integer.valueOf(2), gauge.getValue());
    }


    @Test
    public void testCounter() throws InterruptedException {
        String name = MetricRegistry.name("MetricTest", "queue", "size");
        Counter counter = metricRegistry.counter(name);
        counter.inc();
        counter.inc(2);
        assertEquals(3, counter.getCount());
    }

    /**
     * A Meter measures event occurrences count and rate:
     * 可以使用meter计算qps，请求量
     *
     * @throws InterruptedException
     */
    @Test
    public void testMeter() throws InterruptedException {

        Meter meter = metricRegistry.meter("site.request");
        long begin = System.currentTimeMillis();
        //前面2秒qps:10
        for (int i = 0; i < 20; ++i) {
            meter.mark();
            Thread.sleep(100);
            System.out.println(FormatUtils.toDateTimeMillisString(System.currentTimeMillis()));
        }
        System.out.println("sleep  ......");
        Thread.sleep(6000);
        meter.mark();
        meter.mark();
        meter.mark();
        long end = System.currentTimeMillis();
        System.out.println("");
        System.out.println("# Result");
        System.out.println("");
        System.out.println("* Duration:");
        print(" * %s - %s (%dms)", formatTime(begin), formatTime(end), (end - begin));
        System.out.println("");
        print(" * Summary:");

        System.out.println(" * Total:" + meter.getCount());
        double meanRate = meter.getMeanRate();
        double oneMinRate = meter.getOneMinuteRate();
        double fiveMinRate = meter.getFiveMinuteRate();
        double fifteenMinRate = meter.getFifteenMinuteRate();

        System.out.println("平均 qps:" + meanRate);
        System.out.println("1分钟 qps:" + oneMinRate);
        System.out.println("5分钟 qps:" + fiveMinRate);
        System.out.println("15分钟 qps:" + fifteenMinRate);

    }

    private void print(String format, Object... args) {
        print(String.format(format, args));
    }

    private void print(String line) {
        System.out.println(line);
    }

    private String formatTime(long ts) {
        return FormatUtils.toDateTimeMillisString(ts);
    }

    /**
     * Histogram 统计数据的 max, min, mean, median, standard deviation, 75th percentile
     * 可以使用Histogram记录耗时，流量等
     *
     * @throws InterruptedException
     */
    @Test
    public void testHistogram() {

        Histogram histogram = metricRegistry.histogram("redis-traffic-outgoing");
        for (int i = 0; i < 100; ++i) {
            histogram.update(i);
        }
        for (int i = 0; i < 100; ++i) {
            histogram.update(10);
        }

        Snapshot snapshot = histogram.getSnapshot();

        System.out.println(snapshot.getMedian());
        System.out.println(snapshot.get75thPercentile());
        System.out.println(snapshot.get95thPercentile());
        System.out.println(snapshot.get999thPercentile());
    }


    @Test
    public void testTimer() throws InterruptedException {
        Timer timer = metricRegistry.timer("api.get.latency");
        Timer.Context ctx = timer.time();
        TimeUnit.SECONDS.sleep(5);
        long elapsed1 = ctx.stop();
        assertEquals(5000000000L, elapsed1, 1000000);

        Snapshot snapshot = timer.getSnapshot();
        //返回中位数
        System.out.println(snapshot.getMedian());
        //返回max
        System.out.println(snapshot.getMax());

    }

    protected void addMeter(Meter meter) throws InterruptedException {
        for (int i = 0; i < 10; ++i) {
            meter.mark();
            Thread.sleep(1);
        }
    }

    private void addTrafficOutgoing(Histogram histogram) {

        for (int i = 1; i < 11; ++i) {

            histogram.update(i * 10);
            try {
                Thread.sleep(5);
            } catch (InterruptedException e) {
            }
        }
    }

    /**
     * ExponentiallyDecayingReservoir 是Histogram底层采样实现
     * 根据更新时间与开始时间的差值转化为权重值，权重越大数据被保留的几率越大
     */
    @Test
    public void test_ExponentiallyDecayingReservoir() {
        long duration = 60;//单位秒
        for (int i = 0; i < 10; ++i) {
            final double itemWeight = weight(duration);
            final double priority = itemWeight / ThreadLocalRandom.current().nextDouble();
            log.info("duration:{}, itemWeight:{} ,priority: {}", duration / 60, Math.round(itemWeight), Math.round(priority));
            duration = 5 * duration;
        }
    }

    //返回自然数底数e的参数次方,e为2.718
    private double weight(long t) {
        double DEFAULT_ALPHA = 0.015;
        return exp(DEFAULT_ALPHA * t);
    }

    public static void main(String[] args) throws InterruptedException {
        System.out.println(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
        Thread.sleep(1000);
        System.out.println(TimeUnit.MILLISECONDS.toSeconds(System.currentTimeMillis()));
    }

}
