package com.buzz.metric.dropwizard;

import com.codahale.metrics.ConsoleReporter;
import com.codahale.metrics.Meter;
import com.codahale.metrics.MetricRegistry;

import java.util.Random;
import java.util.concurrent.TimeUnit;

public class MeterTest {

	public static Random random = new Random();

	public static void request(Meter meter) {
		// System.out.println("request");
		meter.mark();
	}

	public static void request(Meter meter, int n) {
		while (n > 0) {
			request(meter);
			n--;
		}
	}

	public static void main(String[] args) throws InterruptedException {
		MetricRegistry registry = new MetricRegistry();
		ConsoleReporter reporter = ConsoleReporter.forRegistry(registry).build();
		reporter.start(1, TimeUnit.SECONDS);
		Meter tpsWrite = registry.meter(MetricRegistry.name(MeterTest.class, "request", "tps"));

		// 等待10秒
		for (int i = 0; i < 10; ++i) {
			request(tpsWrite, random.nextInt(5));
			Thread.sleep(1000);
		}

		Meter tpsRead = registry.getMeters().get(MetricRegistry.name(MeterTest.class, "request", "tps"));
		System.out.println(tpsRead.getCount() + "\t" + Math.round(tpsRead.getMeanRate()));
	}

}
