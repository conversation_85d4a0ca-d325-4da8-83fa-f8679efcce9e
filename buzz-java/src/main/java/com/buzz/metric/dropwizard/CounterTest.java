package com.buzz.metric.dropwizard;

import com.codahale.metrics.ConsoleReporter;
import com.codahale.metrics.Counter;
import com.codahale.metrics.MetricRegistry;

import java.util.Queue;
import java.util.Random;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * 统计队列堆积的数据大小
 * 
 * <AUTHOR>
 *
 */
public class CounterTest {

	public static Queue<String> q = new LinkedBlockingQueue<String>();

	public static Counter pendingJobCounter;

	public static Random random = new Random();

	public static void addJob(String job) {
		pendingJobCounter.inc();
		q.offer(job);
	}

	public static String takeJob() {
		String item = q.poll();
		if (item != null) {
			pendingJobCounter.dec();
		}
		return item;
	}

	public static void main(String[] args) throws InterruptedException {
		MetricRegistry registry = new MetricRegistry();
		ConsoleReporter reporter = ConsoleReporter.forRegistry(registry).build();
		reporter.start(1, TimeUnit.SECONDS);

		pendingJobCounter = registry.counter(MetricRegistry.name(Queue.class, "pending-jobs", "size"));

		for (int i = 0; i < 10; ++i) {
			Thread.sleep(200);
			if (random.nextDouble() > 0.7) {
				String job = takeJob();
				System.out.println(i+"\t take job : " + job);
			} else {
				String job = "Job-" + i;
				addJob(job);
				System.out.println(i+"\t add job : " + job);
			}
		}
	}
}