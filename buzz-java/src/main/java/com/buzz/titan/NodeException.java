package com.buzz.titan;

public class NodeException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	public NodeException() {
		super();
	}

	public NodeException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
		super(message, cause, enableSuppression, writableStackTrace);
	}

	public NodeException(String message, Throwable cause) {
		super(message, cause);
	}

	public NodeException(String message) {
		super(message);
	}

	public NodeException(Throwable cause) {
		super(cause);
	}

}
