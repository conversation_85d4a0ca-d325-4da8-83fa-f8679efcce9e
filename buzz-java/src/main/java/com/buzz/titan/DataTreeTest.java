package com.buzz.titan;

public class DataTreeTest {

    public static void main(String[] args) {
        DataTree tree = new DataTree();
        tree.createNode("/dubbo", "dubbo");
        tree.createNode("/dubbo/ucenter", "ucenter");
        tree.createNode("/dubbo/book", "book");
        tree.createNode("/http", "book");
        tree.createNode("/http/book", "book");


        tree.deleteNode("/dubbo");
        System.out.println(tree.getChildren("/"));
        System.out.println(tree.getChildren("/dubbo"));

    }
}
