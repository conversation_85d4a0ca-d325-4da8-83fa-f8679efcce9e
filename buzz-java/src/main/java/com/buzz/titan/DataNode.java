package com.buzz.titan;

import java.util.HashSet;
import java.util.Set;

public class DataNode {

	private String version;

	private String data;

	private Set<String> children;

	DataNode parent;

	public DataNode(DataNode parent, String data) {
		super();
		this.parent = parent;
		this.data = data;
	}

	public Set<String> getChildren() {
		return children;
	}

	public synchronized boolean addChild(String child) {
		if (children == null) {
			// let's be conservative on the typical number of children
			children = new HashSet<String>(8);
		}
		return children.add(child);
	}

	public synchronized boolean removeChild(String child) {
		if (children == null) {
			return false;
		}
		return children.remove(child);
	}
}
