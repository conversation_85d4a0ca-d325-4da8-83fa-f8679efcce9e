package com.buzz.titan;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

public class DataTree {

	private static final String root_path = "/";

	private Map<String, DataNode> nodes = new ConcurrentHashMap<String, DataNode>();

	private DataNode root = new DataNode(null, root_path);

	public DataTree() {

		/* Rather than fight it, let root have an alias */
		nodes.put("", root);
		nodes.put(root_path, root);
	}

	private void checkPath(String path) {
		if(path.lastIndexOf('/')==path.length()-1) {
			throw new NodeException("end of path must not be '/' ");
		}
	}

	public void createNode(String path, String data) {
		checkPath(path);

		int lastSlash = path.lastIndexOf('/');
		String parentName = path.substring(0, lastSlash);
		String childName = path.substring(lastSlash + 1);

		DataNode parent = nodes.get(parentName);
		if (parent == null) {
			throw new NodeException("parent node is null! parentName:" + parentName);
		}

		synchronized (parent) {

			Set<String> children = parent.getChildren();
			if (children != null) {
				if (children.contains(childName)) {
					throw new NodeException();
				}
			}

			DataNode child = new DataNode(parent, data);
			parent.addChild(childName);
			nodes.put(path, child);
		}

	}

	public DataNode createIfAbsent(String path, String data) {

		DataNode dataNode = getNode(path);

		while ((dataNode = getNode(path)) == null) {
			createNode(path, data);
		}

		return dataNode;
	}

	public DataNode getNode(String path) {
		return nodes.get(path);
	}



	public List<String> getChildren(String path) {
		DataNode n = nodes.get(path);
		if (n == null) {
			throw new NodeException("node not exist! path:"+path);//no node
		}

		synchronized (n) {
			ArrayList<String> children;
			Set<String> childs = n.getChildren();
			if (childs != null) {
				children = new ArrayList<String>(childs.size());
				children.addAll(childs);
			} else {
				children = new ArrayList<String>(0);
			}
			return children;
		}
	}

	public void deleteNode(String path) {
		int lastSlash = path.lastIndexOf('/');
		String parentName = path.substring(0, lastSlash);
		String childName = path.substring(lastSlash + 1);
		DataNode node = nodes.get(path);
		if (node == null) {
			throw new NodeException();//no node
		}

		nodes.remove(path);
		DataNode parent = nodes.get(parentName);
		if (parent == null) {
			throw new NodeException();//no node
		}
		synchronized (parent) {
			parent.removeChild(childName);
			node.parent = null;
		}
	}
}
