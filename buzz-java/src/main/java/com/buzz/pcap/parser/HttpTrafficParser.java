package com.buzz.pcap.parser;

import io.pkts.packet.IPPacket;
import io.pkts.packet.Packet;
import io.pkts.packet.TCPPacket;
import io.pkts.protocol.Protocol;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 **/
public class HttpTrafficParser implements TrafficParser {
    private int noCount = 0;//记录所有
    private int requestCount = 0;//只记录有数据传输的

    private boolean isRequest(TCPPacket tcpPacket) {
        String port = String.valueOf(tcpPacket.getDestinationPort());
        //8080说明是http
        return port.length() == 4 && port.startsWith("8080");
    }


    private void printPacket(TCPPacket tcpPacket) {
        IPPacket ipPacket = tcpPacket.getParentPacket();
        int dataSize = (tcpPacket.getPayload() == null) ? -1 : tcpPacket.getPayload().getArray().length;
        System.out.println(noCount + "\t" + flag(tcpPacket) +" "+ isRequest(tcpPacket) + "\t" + ipPacket.getSourceIP() + ":" + tcpPacket.getSourcePort() + "\t" + ipPacket.getDestinationIP() + ":" + tcpPacket.getDestinationPort() + "\t" + dataSize);

    }

    @Override
    public boolean process(Packet packet) throws IOException {
        if (!packet.hasProtocol(Protocol.TCP)) {
            return true;
        }
        ++noCount;
        TCPPacket tcpPacket = (TCPPacket) packet.getPacket(Protocol.TCP);

//        if(flag(tcpPacket).contains("fin")){
//        }
            printPacket(tcpPacket);


//        if(noCount==3554){
//            Buffer buffer = tcpPacket.getPayload();
//            System.out.println(buffer.toString());
//            System.exit(-1);
//        }



        if(noCount>=100){
            System.exit(-1);
        }
        return true;
    }

    @Override
    public void printResult() {
        System.out.println(noCount);
    }

    private String flag(TCPPacket tcpPacket) {
        StringBuilder sb = new StringBuilder();
        if (tcpPacket.isSYN()) {
            sb.append("sync");
        }
        if (tcpPacket.isACK()) {
            sb.append("ack");
        }
        if (tcpPacket.isFIN()) {
            sb.append("fin");
        }
        if (tcpPacket.isPSH()) {
            sb.append("push");
        }
        if (tcpPacket.isCWR()) {
            sb.append("cwr");
        }
        if (tcpPacket.isNS()) {
            sb.append("ns");
        }
        if (tcpPacket.isRST()) {
            sb.append("rst");
        }
        return sb.toString();
    }
}
