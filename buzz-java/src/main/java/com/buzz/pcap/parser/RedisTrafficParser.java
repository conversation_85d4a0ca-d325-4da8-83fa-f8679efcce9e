package com.buzz.pcap.parser;

import com.buzz.util.FormatUtils;
import com.buzz.util.TextTable;
import io.pkts.buffer.Buffer;
import io.pkts.packet.IPPacket;
import io.pkts.packet.Packet;
import io.pkts.packet.TCPPacket;
import io.pkts.protocol.Protocol;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.map.HashedMap;

import java.io.IOException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class RedisTrafficParser implements TrafficParser {

    private List<Command> commandList = new ArrayList<>();

    private AtomicLong beginTs = new AtomicLong(Long.MAX_VALUE);

    private AtomicLong endTs = new AtomicLong();

    private AtomicInteger requestCounter = new AtomicInteger();

    private AtomicInteger Counter = new AtomicInteger();

    

    public RedisTrafficParser() {

    }

    public boolean process(Packet packet) throws IOException {
        if (packet.hasProtocol(Protocol.TCP)) {
            TCPPacket tcp = (TCPPacket) packet.getPacket(Protocol.TCP);
            IPPacket ipPacket = tcp.getParentPacket();
            //说明是同一个网段
            if(ipPacket.getSourceIP().startsWith(ipPacket.getDestinationIP())){

            }
            //如果数据跳过
            if (tcp.getPayload() == null) {
                //System.out.println("null " +tcp.getSequenceNumber());
                return true;
            }
            if (tcp.getArrivalTime() < beginTs.get()) {
                beginTs.set(tcp.getArrivalTime());
            }
            if (tcp.getArrivalTime() > endTs.get()) {
                endTs.set(tcp.getArrivalTime());
            }
            //目标端口
            String port = String.valueOf(tcp.getDestinationPort());
            //63 开头，长度为 4 说明是redis端口
            if (port.length() == 4 && port.startsWith("63")) {
                processReq(tcp);
            } else {
                processRes(tcp);
            }
            requestCounter.incrementAndGet();
        }

        return true;
    }

    private void processReq(TCPPacket tcp) throws IOException {

        Buffer buffer = tcp.getPayload();
        byte b = buffer.readByte();
        if (b == '$') { //Bulk Strings
            //System.out.println("1==>" + buffer.getArray().length);
        } else if (b == '+') {// String
            //System.out.println("2==>" + buffer.getArray().length);
        } else if (b == '*') { //Arrays
            //System.out.println("3");
            String value = buffer.toString();
            String[] arrays = value.split("\r\n");
            if (arrays.length < 3) {
                //System.out.println("unexpected!==>" + buffer.getArray().length);
                return;
            }
            String opt = arrays[2];
            Command cmd = null;
            if (opt.equals("PING")) {
                cmd = new Command(arrays[2], null);
            } else if (opt.equals("QUIT")) {
                //ignore
                return;
            } else {
                if (arrays.length < 4) {
                    System.out.println("unexpected2 !==>" + buffer.getArray().length);
                } else {
                    cmd = new Command(arrays[2], arrays[4], buffer.getRawArray().length);
                }
            }
            commandList.add(cmd);
//            if(cmd.getCmd().equals("eval")){
//                System.out.println("eval "+tcp.getParentPacket().getSourceIP());
//            }
        } else {
            //这种情况是被拆包了
            //System.out.println("not support type " + b + "\t" + tcp.getSequenceNumber() + "\t" + requestCounter.get()+"\t"+tcp.get);
            Command last = commandList.get(commandList.size() - 1);
            last.getReqBytes().getAndAdd(buffer.getRawArray().length);
            //System.out.println(last +"\t"+ buffer.getArray().length);
            //commandList.add(new Command("TCP",null));
        }
    }

    private void processRes(TCPPacket tcp) throws IOException {
        Buffer buffer = tcp.getPayload();
        if (!commandList.isEmpty()) {
            //通过这种方式匹配Command并不准确
            Command last = commandList.get(commandList.size() - 1);
            last.getResBytes().getAndAdd(buffer.getRawArray().length);
        }

    }

    private String formatTime(long ts) {
        return FormatUtils.toDateTimeMillisString(ts / 1000);
    }

    private int costSec() {
        return Math.round((endTs.get() - beginTs.get()) / 1000000.0f);
    }

    private int requestQps() {
        return requestCounter.get() / costSec();
    }

    private long inTraffic() {

        return traffic(commandList, true);
    }

    private long outTraffic() {
        return traffic(commandList, false);
    }

    private long traffic(List<Command> commandList, boolean flag) {
        long inBytes = 0;
        long outBytes = 0;
        for (Command command : commandList) {
            inBytes += command.getReqBytes().get();
            outBytes += command.getResBytes().get();
        }
        return flag ? inBytes : outBytes;
    }

    public void printResult() {
        print("## Summary");
        print("* Duration:");
        print(" * %s - %s (%ds)", formatTime(beginTs.get()), formatTime(endTs.get()), costSec());
        print("* In Traffic:");
        long inTraffic = inTraffic();
        long outTraffic = outTraffic();
        print(" * %s bytes (%s bytes/sec)", FormatUtils.humanReadableByteSize(inTraffic), FormatUtils.humanReadableByteSize(inTraffic / costSec()));
        print("* Out Traffic:");
        print(" * %s bytes (%s bytes/sec)", FormatUtils.humanReadableByteSize(outTraffic), FormatUtils.humanReadableByteSize(outTraffic / costSec()));
        print("* Total Requests:");
        print(" * %d requests (Avg %d req/sec,Peak ? req/sec)", requestCounter.get(), requestQps());
        print("");
        print("## Command Detail (%d)", commandList.size());
        print("");

        Map<String, List<Command>> commandMap = commandList.stream().collect(Collectors.groupingBy(Command::getCmd));
        TextTable table = new TextTable();
        table.addRow("key", "count", "in", "out");
        for (Map.Entry<String, List<Command>> entry : commandMap.entrySet()) {
            table.addRow(entry.getKey(),
                    String.valueOf(entry.getValue().size()),
                    FormatUtils.humanReadableByteSize(traffic(entry.getValue(), true)),
                    FormatUtils.humanReadableByteSize(traffic(entry.getValue(), false)));
        }
        print(table.toString());

        cmdDetail(commandMap, "get");
        cmdDetail(commandMap, "hexists");
    }

    private void cmdDetail(Map<String, List<Command>> commandMap, String cmdName) {
        if(commandMap.get(cmdName)==null){
            return;
        }

        print("## %s Detail (%d)", cmdName.substring(0, 1).toUpperCase() + cmdName.substring(1), commandMap.get(cmdName).size());
        print("");
        Map<String, Integer> cmdStatMap = new HashedMap();
        Map<String, Command> cmdKeyMap = new HashedMap();
        for (Command cmd : commandMap.get(cmdName)) {
            Integer counter = cmdStatMap.computeIfAbsent(cmd.getKey(), (k) -> 0);
            ++counter;
            cmdStatMap.put(cmd.getKey(), counter);
            cmdKeyMap.put(cmd.getKey(), cmd);
        }
        cmdStatMap = cmdStatMap.entrySet()
                .stream()
                .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()))
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (e1, e2) -> e1, LinkedHashMap::new));

        int n = 0;
        TextTable table = new TextTable("key", "count");
        for (String key : cmdStatMap.keySet()) {
            if (n > 9) break;
            table.addRow(key, String.valueOf(cmdStatMap.get(key)));
            ++n;
        }
        System.out.println(table.toString());
    }

    private void print(String format, Object... args) {
        print(String.format(format, args));
    }

    private void print(String line) {
        System.out.println(line);

    }

    @Data
    @AllArgsConstructor
    private static class Command {
        private String cmd;
        private String key;
        private AtomicInteger reqBytes;
        private AtomicInteger resBytes = new AtomicInteger();

        public Command(String cmd, String key) {
            this.cmd = cmd.toLowerCase();
            this.key = key;
            this.reqBytes = new AtomicInteger(0);
        }

        public Command(String cmd, String key, int reqBytes) {
            this.cmd = cmd.toLowerCase();
            this.key = key;
            this.reqBytes = new AtomicInteger(reqBytes);
        }

        @Override
        public String toString() {
            return String.format("Command(cmd=%s, key=%s, reqBytes=%s, resBytes=%s)",
                    cmd,
                    key,
                    FormatUtils.humanReadableByteSize(reqBytes.get()),
                    FormatUtils.humanReadableByteSize(resBytes.get())
            );
        }
    }
}
