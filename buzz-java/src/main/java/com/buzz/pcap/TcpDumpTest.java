package com.buzz.pcap;

import com.buzz.pcap.parser.HttpTrafficParser;
import com.buzz.pcap.parser.RedisTrafficParser;
import com.buzz.pcap.parser.TrafficParser;
import io.pkts.PacketHandler;
import io.pkts.Pcap;
import io.pkts.packet.Packet;
import org.junit.Test;

import java.io.IOException;

/**
 * java parse pcap file
 * <p>
 * https://www.javahelps.com/2017/08/parse-pcap-files-in-java.html
 * https://github.com/aboutsip/pkts
 *
 * <dependency>
 * <groupId>io.pkts</groupId>
 * <artifactId>pkts-core</artifactId>
 * <version>3.0.0</version>
 * <type>jar</type>
 * </dependency>
 *
 * <dependency>
 * <groupId>io.pkts</groupId>
 * <artifactId>pkts-streams</artifactId>
 * <version>3.0.0</version>
 * <type>jar</type>
 * </dependency>
 *
 * <AUTHOR>
 * @description
 **/
public class TcpDumpTest {

    private void execute(Pcap pcap, TrafficParser parser) throws IOException {
        pcap.loop(new PacketHandler() {
            @Override
            public boolean nextPacket(Packet packet) throws IOException {
                parser.process(packet);
                return true;
            }
        });
        parser.printResult();
    }

    @Test
    public void testRedis() throws IOException {
        Pcap pcap = Pcap.openStream("/Users/<USER>/Downloads/redis.pcap");
        TrafficParser parser = new RedisTrafficParser();
        execute(pcap, parser);
    }

    @Test
    public void testHttp() throws IOException {
        Pcap pcap = Pcap.openStream("/Users/<USER>/Downloads/hermes-proxy.pcap");
        TrafficParser parser = new HttpTrafficParser();
        execute(pcap, parser);
    }
}
