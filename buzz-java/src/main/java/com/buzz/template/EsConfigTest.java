package com.buzz.template;


import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.junit.Test;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.util.ArrayList;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * 根据模版批量产生elasticsearch.yml
 * <p>
 * | r1   | 10.1.120.1，10.1.120.2                |          | 2t ，2.2t             |
 * | ---- | ------------------------------------- | -------- | --------------------- |
 * | r2   | 10.1.120.9，10.1.120.10               |          | 1.5t ，1.9t           |
 * | r3   | 10.1.120.19，10.1.120.20              |          | 653g ，636g           |
 * | r4   | 10.1.128.82，10.1.128.84              | nginx    | 1.8t，  1.8t          |
 * | r5   | 10.1.136.51                           |          | 1.3t                  |
 * | r6   | 10.1.128.82，10.1.128.84，10.1.128.91 | fund备份 | 567.5g， 677.8g，6.4t |
 * | r7   | 10.1.128.91                           | fund备份 |                       |
 */
public class EsConfigTest {
    private static final String TEMPLATE_PATH = "es-config/es-config.vm";

    private Map<String, List<NodeConfig>> initConfig() {
        Map<String, List<NodeConfig>> clusterConfig = new LinkedHashMap<>();
        List<NodeConfig> nodeConfig;

        nodeConfig = clusterConfig.computeIfAbsent("10.1.120.1", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-120-01-m", true, true, false, "r1", "/data01/master"));
        nodeConfig.add(new NodeConfig("node-120-01-d", false, false, true, "r1", "/data01/wacaiqshes,/data02"));

        nodeConfig = clusterConfig.computeIfAbsent("10.1.120.2", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-120-02-m", true, true, false, "r1", "/data01/master"));
        nodeConfig.add(new NodeConfig("node-120-02-d", false, false, true, "r1", "/data01/wacaiqshes,/data02"));

        nodeConfig = clusterConfig.computeIfAbsent("10.1.120.9", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-120-09-c", false, false, false, "r2", "/data01/client"));
        nodeConfig.add(new NodeConfig("node-120-09-d", false, false, true, "r2", "/data01/wacaiqshes,/data02"));

        nodeConfig = clusterConfig.computeIfAbsent("10.1.120.10", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-120-10-c", false, false, false, "r2", "/data01/client"));
        nodeConfig.add(new NodeConfig("node-120-10-d", false, false, true, "r2", "/data01/wacaiqshes,/data02/esdata"));


        nodeConfig = clusterConfig.computeIfAbsent("10.1.120.19", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-120-19-m", true, true, false, "r3", "/data01/master"));
        nodeConfig.add(new NodeConfig("node-120-19-d", false, false, true, "r3", "/data01/wacaiqshes"));

        nodeConfig = clusterConfig.computeIfAbsent("10.1.120.20", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-120.20-c", false, false, false, "r3", "/data01/client"));
        nodeConfig.add(new NodeConfig("node-120.20-d", false, false, true, "r3", "/data01/wacaiqshes"));


        nodeConfig = clusterConfig.computeIfAbsent("10.1.128.82", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-128-82-d", false, false, true, "r4", "/data01/wacaiqshes"));
        nodeConfig.add(new NodeConfig("node-128-82-d2", false, false, true, "r6", "/data03/wacaiqshes"));

        nodeConfig = clusterConfig.computeIfAbsent("10.1.128.84", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-128-84-d", false, false, true, "r4", "/data01/wacaiqshes"));
        nodeConfig.add(new NodeConfig("node-128-84-d2", false, false, true, "r6", "/data03/wacaiqshes"));

        nodeConfig = clusterConfig.computeIfAbsent("10.1.128.91", (k) -> new ArrayList<>());//*
        nodeConfig.add(new NodeConfig("node-128-91-d", false, false, true, "r6", "/data/es/data"));
        nodeConfig.add(new NodeConfig("node-128-91-d2", false, false, true, "r7", "/data/es/data2"));


        nodeConfig = clusterConfig.computeIfAbsent("10.1.136.51", (k) -> new ArrayList<>());
        nodeConfig.add(new NodeConfig("node-136-51-c", false, false, false, "r5", "/data01/client"));
        nodeConfig.add(new NodeConfig("node-136-51-d", false, false, true, "r5", "/data01"));


        return clusterConfig;
    }

    @Test
    public void testGenerate() throws FileNotFoundException {
        Map<String, List<NodeConfig>> clusterConfig = initConfig();
        VelocityEngine ve = new VelocityEngine();
        ve.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
        ve.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
        ve.init();
        // 获取模板文件
        Template t = ve.getTemplate(TEMPLATE_PATH, "utf-8");

        clusterConfig.entrySet().stream().forEach(e -> {
            for (NodeConfig config : e.getValue()) {
                try {
                    doGenerate(t, e.getKey(), config);
                } catch (Exception ex) {
                    ex.printStackTrace();
                    throw new RuntimeException(ex);
                }
            }
        });

    }

    private void doGenerate(Template t, String host, NodeConfig config) throws IOException {
        File outDir = new File("/tmp/es", host);
        if (!outDir.exists()) {
            outDir.mkdirs();
        }
        // 设置变量
        VelocityContext ctx = new VelocityContext();
        ctx.put("nodeName", config.getName());
        ctx.put("master", config.isMaster());
        ctx.put("data", config.isData());
        ctx.put("ingest", config.isIngest());
        ctx.put("rack", config.getRack());
        ctx.put("dataPath", config.getDataPath());
        ctx.put("host", host);

        //非data节点9201,data节点使用默认端口
        if (!config.isData()) {
            ctx.put("httpPort", "9201");
            ctx.put("tcpPort", "9301");
        }

        File outFile = new File(outDir, config.getName() + "-elasticsearch.yml");
        System.out.println("Write to " + outFile);
        Writer writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(outFile)));
        t.merge(ctx, writer);
        writer.close();
    }


    @Data
    @AllArgsConstructor
    private static class NodeConfig {
        private String name;
        private boolean master;
        private boolean ingest;
        private boolean data;
        private String rack;
        private String dataPath;

        private String getRole() {
            if (master) {
                return "master";
            } else if (ingest) {
                return "ingest";
            } else if (data) {
                return "data";
            }

            return "client";
        }

    }
}
