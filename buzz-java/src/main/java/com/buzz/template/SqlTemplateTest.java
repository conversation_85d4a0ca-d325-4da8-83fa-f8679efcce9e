package com.buzz.template;

import org.junit.Test;

import java.util.List;

/**
 * <AUTHOR>
 * @description
 * @date 2022-06-08 14:46
 **/
public class SqlTemplateTest {

    @Test
    public void testSqlParser() {
        SqlTemplate.SqlParser parser = new SqlTemplate.SqlParser(" values (187, '系统管理员', 'GOBLIN_ADMIN', 'ADMIN', 1, '', 'GOBLIN', 'tianji', 'wenshi', '2017-07-18 12:26:38.0', '2022-04-27 16:06:55.0', null), (188, '催收外部用户', 'GOBLIN_EXTERNAL', null, 1, null, 'GOBLIN', 'tianji', 'tianji', '2017-07-18 14:53:19.0', '2017-07-18 14:53:19.0', null), (189, '审批员查询', 'GOBLIN_ADANAS', null, 1, null, 'GOBLIN', 'tianji', 'tianji', '2017-07-18 14:53:19.0', '2017-07-18 14:53:19.0', null);");
        List<String> lines = parser.parse();
        for (String line : lines) {
            System.out.println(line);
        }
    }

    @Test
    public void testSqlTemplate() {
        String insertSqlFormat = "insert into `abstract_auth_role` (`id`, `name`, `code`, `sign`, `status`, `comment`, `app_code`, `creator`, `modifier`, `created_time`, `updated_time`, `group_id`) values (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s);";
        SqlTemplate template = new SqlTemplate("/Users/<USER>/Downloads/20220608103744752.sql", "0", insertSqlFormat);
        template.execute();
    }
}
