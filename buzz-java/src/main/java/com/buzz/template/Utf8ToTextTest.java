package com.buzz.template;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Properties;

public class Utf8ToTextTest {
    public static void main(String[] args) {
        // 原始 properties 文件路径
        String inputFilePath = "/tmp/messageBundle_zh.properties";
        // 转换后的 properties 文件路径
        String outputFilePath = "application_converted.properties";

        try {
            // 加载原始 properties 文件
            Properties properties = new Properties();
            try (Reader reader = new InputStreamReader(new FileInputStream(inputFilePath), "UTF-8")) {
                properties.load(reader);
            } catch (FileNotFoundException e) {
                throw new RuntimeException(e);
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            } catch (IOException e) {
                throw new RuntimeException(e);
            }

            // 转换内容
            StringBuilder convertedContent = new StringBuilder();
            properties.forEach((key, value) -> {
                String decodedValue = convertUnicodeToChinese(value.toString());
                convertedContent.append(key).append("=").append(decodedValue).append("\n");
            });

            // 保存到新文件
            Files.write(Paths.get(outputFilePath), convertedContent.toString().getBytes("UTF-8"));
            System.out.println("转换完成，结果已保存到：" + outputFilePath);
        } catch (IOException e) {
            System.err.println("文件处理失败：" + e.getMessage());
        }
    }

    /**
     * 将 Unicode 转义字符（如 \u64CD\u4F5C\u5931\u8D25）转换为中文。
     *
     * @param unicodeStr 原始字符串
     * @return 转换后的字符串
     */
    private static String convertUnicodeToChinese(String unicodeStr) {
        StringBuilder result = new StringBuilder();
        int length = unicodeStr.length();
        for (int i = 0; i < length; ) {
            char c = unicodeStr.charAt(i);
            if (c == '\\' && i + 1 < length && unicodeStr.charAt(i + 1) == 'u') {
                String unicode = unicodeStr.substring(i + 2, i + 6);
                try {
                    result.append((char) Integer.parseInt(unicode, 16));
                    i += 6;
                } catch (NumberFormatException e) {
                    // 如果解析失败，则原样保留
                    result.append(c);
                    i++;
                }
            } else {
                result.append(c);
                i++;
            }
        }
        return result.toString();
    }
}
