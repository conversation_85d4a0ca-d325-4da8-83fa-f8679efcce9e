package com.buzz.template;

import com.buzz.util.IOUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.velocity.Template;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.apache.velocity.runtime.RuntimeConstants;
import org.apache.velocity.runtime.resource.loader.ClasspathResourceLoader;
import org.docx4j.XmlUtils;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;

import java.io.File;
import java.io.IOException;
import java.io.StringWriter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 注意! 不要用空格作为分隔符
 * <p>
 * 事前处理：
 * 1. 把表格另存为cvs文件, cp ~/Downloads/23年5月待做调休单三云.csv /data/docs/user.txt
 * 2. cat /tmp/log2 |  awk -F ',' '$3~/^[1-9]/ {print $1 "," $3}' (这步操作目前可省略)
 * 事后处理：
 * 1. word文件字体需要人工改成：宋体，小四
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class YllTest {

    private static final String TEMPLATE_PATH = "docs/yll.vm";
    private static final String INPUT_USER_PATH = "/data/docs/user.txt";
    private static final String OUTPUT_TMP_PATH = "/data/docs/tmp.txt";
    private static final String OUTPUT_DOC_PATH = "/data/docs/%s年%s月调休单.docx";
    private static final String XML_SEPARATOR = "===";
    private static final String YEAR = "2023";
    private static final String MONTH = "06";

    public static void main(String[] args) throws IOException {
        //List<String> userPairList = Stream.of("马云 30").collect(Collectors.toList()); //测试
        //读取用户列表
        List<String> userPairList = IOUtils.readLines(INPUT_USER_PATH);
        userPairList = userPairList.stream().filter(str -> {
            boolean filter = !str.endsWith("天数");
            System.out.println("str:" + str + "\t filter:" + filter);
            return filter;
        }).collect(Collectors.toList());

        //通过模板产生xml
        OpenXMLGenerator generator = new OpenXMLGenerator();
        generator.setup(userPairList);
        String content = generator.generate();
        IOUtils.writeFile(OUTPUT_TMP_PATH, content);

        //从xml写docx
        String inputVM = IOUtils.readFile(OUTPUT_TMP_PATH);
        String[] openXMLs = inputVM.split(XML_SEPARATOR);
        String outputFile = String.format(OUTPUT_DOC_PATH, YEAR, MONTH);
        DocxWriter.write(outputFile, openXMLs);
        System.out.println("==================================");
        System.out.println("Write successfully!");
        System.out.println("Output: " + outputFile);
        System.out.println("==================================");
    }

    static class DocxWriter {
        public static void write(String outputFile, String... openXMLs) {
            try {
                WordprocessingMLPackage wordPackage = WordprocessingMLPackage.createPackage();
                MainDocumentPart mdp = wordPackage.getMainDocumentPart();
                for (String openXML : openXMLs) {
                    //最后一行
                    if (openXML.trim().length() < 2) {
                        continue;
                    }
                    try {
                        mdp.getContent().add(XmlUtils.unmarshalString(openXML));
                    } catch (Exception e) {
                        log.error("openXML=" + openXML, e);
                        System.exit(-1);
                    }
                }
                wordPackage.save(new File(outputFile));
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        }
    }

    static class OpenXMLGenerator {

        //预先标记了一些坐标
        private static int[][] coordinate = {
                {417, 543}, {6340, 530},
                {480, 5243}, {6340, 5243},
                {480, 9943}, {6340, 9943}
        };

        private List<UserItem> list = new ArrayList<UserItem>();

        public void setup(List<String> userPairList) throws IOException {
            for (String userPair : userPairList) {
                userPair = userPair.trim();
                String[] pair = userPair.split(",");
                String user = pair[0].trim();
                //按照0.5天一张单子，所以需要*2
                Integer size = Math.round(Float.parseFloat(pair[1]) * 2);
                if (size == 0)
                    continue;
                for (int i = 1; i <= size; ++i) {
                    UserItem userItem = new UserItem();
                    userItem.setName(user);
                    userItem.setNo(i);
                    addItem(userItem);
                }
            }
        }

        public void addItem(UserItem userItem) {
            int mod = list.size() % 6;
            int x = coordinate[mod][0];
            int y = coordinate[mod][1];
            userItem.setX(x);
            userItem.setY(y);
            list.add(userItem);
            if (list.size() % 6 == 0) {
                userItem.setBreakPage(true);
            }
        }

        public String generate() {
            VelocityEngine ve = new VelocityEngine();
            ve.setProperty(RuntimeConstants.RESOURCE_LOADER, "classpath");
            ve.setProperty("classpath.resource.loader.class", ClasspathResourceLoader.class.getName());
            ve.init();
            // 获取模板文件
            Template t = ve.getTemplate(TEMPLATE_PATH, "utf-8");
            // 设置变量
            VelocityContext ctx = new VelocityContext();
            ctx.put("year", YEAR);
            ctx.put("month", MONTH);
            ctx.put("list", list);
            for (UserItem item : list) {
                System.out.println(item);
            }
            StringWriter sw = new StringWriter();
            t.merge(ctx, sw);
            return sw.toString();
        }

        @Data
        public static class UserItem {
            private int x;
            private int y;
            private String name;
            private int no;
            boolean breakPage;
        }
    }
}
