package com.buzz.template;

import com.buzz.util.IOUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.apache.commons.collections.map.HashedMap;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;
import org.yaml.snakeyaml.Yaml;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description
 * @date 2022-06-14 15:26
 **/
public class EsFileTest {



    /**
     * 打印模板数据
     */
    @Test
    public void testPrintTemplate() throws IOException {

        EsTemplateVisitor visitor = new EsTemplateVisitor("/tmp/log");
        visitor.start();
    }

    /**
     * w-k2-template:
     *   order: 10
     *   index_patterns:
     *   - "w-*_*"
     *   settings:
     *     index:
     *       codec: "best_compression"
     *       routing:
     *         allocation:
     *           include:
     *             rack: "r3,r5"
     *       refresh_interval: "30s"
     *       number_of_shards: "3"
     */
    private static class EsTemplateVisitor {
        private static final String OUTPUT_HEADER = "| seq | template | rack | refresh_interval | shards | replicas | order | patterns |\n" +
                "| ---- | -------- | ---- | ---------------- | ------ | -------- | ----- |----- |";
        private Map<String, Object> root;

        public EsTemplateVisitor(String file) throws IOException {
            String str = IOUtils.readFile(new File(file));
            Yaml yaml = new Yaml();
            root = yaml.load(str);
        }

        public void start() {

            List<TemplateVO> list = new ArrayList<>();

            System.out.println(OUTPUT_HEADER);
            for (Map.Entry<String, Object> entry : root.entrySet()) {
                String key = entry.getKey();
                if (key.startsWith(".")) {
                    continue;
                }
                Map<String, Object> template = (Map<String, Object>) root.get(key);
                Map<String, String> settings = visitSettings(template);
                String rack = settings.get("rack");
                String interval = settings.get("refresh_interval");
                String shards = settings.get("number_of_shards");
                String replicas = settings.get("number_of_replicas");
                String order = safeToString(template.get("order"));
                String patterns = safeToString(template.get("index_patterns"));
                list.add(new TemplateVO(0, key, rack, interval, shards, replicas, order,patterns));
            }

            Collections.sort(list);
            for(int i = 0; i<list.size();++i){
                list.get(i).setSeq(i+1);
                System.out.println(list.get(i));
            }
        }

        private Map<String, String> visitSettings(Map<String, Object> template) {
            Map<String, String> result = new HashedMap();
            Map<String, Object> settingsMap = (Map<String, Object>) template.get("settings");
            String rack = MapIngester.ingest("index.routing.allocation.include.rack", settingsMap);
            String number_of_shards = MapIngester.ingest("index.number_of_shards", settingsMap);
            String number_of_replicas = MapIngester.ingest("index.number_of_replicas", settingsMap);
            String refresh_interval = MapIngester.ingest("index.refresh_interval", settingsMap);
            result.put("rack", rack);
            result.put("number_of_shards", number_of_shards);
            result.put("number_of_replicas", number_of_replicas);
            result.put("refresh_interval", refresh_interval);
            return result;
        }
    }

    private static String safeToString(Object obj) {
        return obj == null ? "null" : obj.toString();
    }

    @Data
    @AllArgsConstructor
    private static class TemplateVO implements Comparable<TemplateVO> {
        //seq., templateName, rack, refresh_interval,number_of_shards, number_of_replicas, order
        private static final String OUTPUT_FORMAT = "| %s | %s | %s | %s | %s | %s | %s | %s |";
        private int seq;
        String templateName;
        String rack;
        String interval;
        String shards;
        String replicas;
        String order;
        String patterns;

        @Override
        public int compareTo(@NotNull TemplateVO o) {
            if (rack == null && o.rack == null) {
                return 0;
            }
            if (rack == null && o.rack != null) {
                return -1;
            }
            if (rack != null && o.rack == null) {
                return 1;
            }
            return rack.compareTo(o.rack);
        }

        @Override
        public String toString() {
            return String.format(OUTPUT_FORMAT, seq, templateName, rack, interval, shards, replicas, order,patterns);
        }
    }

    private static class MapIngester {

        /**
         * {index={routing={allocation={include={rack=r1}}}
         * 通过 index.routing.allocation.include.rack 获取
         *
         * @param path
         * @param map
         * @return
         */
        public static String ingest(String path, Map<String, Object> map) {
            if (map == null) {
                return null;
            }
            int ix = path.indexOf(".");
            if (ix == -1) {
                return safeToString(map.get(path));
            }
            String prefix = path.substring(0, ix);
            String substring = path.substring(ix + 1, path.length());
            return ingest(substring, (Map<String, Object>) map.get(prefix));
        }
    }
}
