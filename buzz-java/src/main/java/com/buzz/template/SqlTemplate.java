package com.buzz.template;

import com.buzz.util.IOUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 * @date 2022-06-08 10:41
 **/
public class SqlTemplate {

    //sqlFile文件
    private File sqlFile;

    //需要处理的ID列的下标，从0开始
    private Map<Integer, Integer> idColumnIndexMap;

    //insertSql 格式
    private String insertSqlFormat;


    /**
     * @param sqlFilePath     sql文件路径
     * @param idColumnIndex   需要增加处理的ID列的下标，从0开始，多个列通过,分割
     * @param insertSqlFormat insertSql的格式
     */
    public SqlTemplate(String sqlFilePath, String idColumnIndex, String insertSqlFormat) {
        this.sqlFile = new File(sqlFilePath);
        this.insertSqlFormat = insertSqlFormat;
        this.idColumnIndexMap = Stream.of(idColumnIndex.split(","))
                .map(c -> Integer.parseInt(c))
                .collect(Collectors.toMap(Function.identity(), Function.identity()));
        if (idColumnIndexMap.isEmpty()) {
            throw new IllegalArgumentException("idColumnIndexMap is Empty! idColumnIndex=" + idColumnIndex);
        }
    }

    public void execute() {
        try {
            String sqlData = IOUtils.readFile(sqlFile, "utf-8");
            SqlParser parser = new SqlParser(sqlData);
            List<String> lines = parser.parse();
            int lastColumnLength = -1;
            for (String line : lines) {
                List<String> sqlParams = new ArrayList<>();
                String[] columns = line.split(",");

                //这里对columns的长度做一个检查，如果不一致中断
                if (lastColumnLength == -1) {
                    lastColumnLength = columns.length;
                } else if (lastColumnLength != columns.length) {
                    throw new IllegalStateException("columns length inconsistent! line:" + line);
                }

                //迭代每一列
                for (int i = 0; i < columns.length; ++i) {
                    Integer idColumnIndex;
                    //如果需要替换id
                    if ((idColumnIndex = idColumnIndexMap.get(i)) != null) {
                        int newId = operate(Integer.parseInt(columns[i]));
                        sqlParams.add(String.valueOf(newId));
                    } else {
                        sqlParams.add(columns[i]);
                    }
                }
                //输出
                write(insertSqlFormat, sqlParams);
            }

        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    //ID操作,默认+10000
    private int operate(int source) {
        return 10000 + source;
    }

    private void write(String insertSqlFormat, List<String> sqlParams) {
        //这里简单输出到控制台
        String out = String.format(insertSqlFormat, sqlParams.toArray());
        System.out.println(out);
    }


    /**
     * 解析 insert sql，得到values部分的值
     */
    static class SqlParser {
        private static final char LEFT = '(';
        private static final char RIGHT = ')';
        private String sqlData;
        private int highPointer = 0;
        private int lowPointer = 0;
        private List<String> lines = new ArrayList<>();

        public SqlParser(String sqlData) {
            this.sqlData = normalize(sqlData);
        }

        private String normalize(String sqlData) {
            if (sqlData == null) {
                throw new IllegalArgumentException("sqlData is null");
            }
            int ix = -1;
            if ((ix = sqlData.indexOf("values")) != -1) {
                return sqlData.substring(ix, sqlData.length());
            }
            return sqlData;
        }

        public List<String> parse() {
            while (highPointer < sqlData.length()) {
                char c = sqlData.charAt(highPointer);
                ++highPointer;
                read(c);
            }
            return lines;
        }

        private void read(char c) {
            if (c == LEFT) {
                lowPointer = highPointer;
            } else if (c == RIGHT) {
                push();
            }
        }

        private void push() {
            lines.add(sqlData.substring(lowPointer, highPointer - 1));
        }
    }
}
