package com.buzz.template;

import com.buzz.util.IOUtils;
import org.junit.Test;

import java.io.File;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 分析 hermes topic 业务线占比
 *
 * <AUTHOR>
 * @description
 **/
public class TopicFileTest {

    @Test
    public void test() throws Exception {
        String file = "/Users/<USER>/Downloads/hermes.csv";
        List<String> lines = IOUtils.readLines(new File(file));
        Map<String, Integer> staffMap = new HashMap<>();
        for (String line : lines) {
            String[] cols = line.split(",");
            if (cols.length < 2) {
                System.out.println("line=" + line);
                continue;
            }
            String topicName = cols[0];
            //System.out.println(topicName);
            for (int i = 1; i < cols.length; ++i) {
                String staff = cols[i].replace("\"", "");
                Integer count = staffMap.get(staff);
                count = (count == null) ? 1 : ++count;
                staffMap.put(staff, count);
            }
        }

        Stream<Map.Entry<String,Integer>> sorted =
                staffMap
                        .entrySet().stream()
                        .sorted(Map.Entry.comparingByValue(Comparator.reverseOrder()));

        for (Map.Entry<String, Integer> entry :   sorted.collect(Collectors.toList())) {
            System.out.println(entry.getKey() + "\t" + entry.getValue());
        }

    }
}
