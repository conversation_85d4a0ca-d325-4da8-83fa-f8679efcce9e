package com.buzz.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.buzz.util.FormatUtils;
import com.buzz.util.IOUtils;
import com.buzz.util.TextTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description
 **/
public class JsonFileTest {

    @Data
    public static class Span implements Comparable<Span> {
        private String appName;
        private String rpcId;
        private String endpoint;
        private String unitName;
        private String clientIp;
        private String serverIp;
        private String cost;

        @Override
        public int compareTo(@NotNull Span o) {
            String oRpcId = o.getRpcId();
            if (rpcId.equals("0") || oRpcId.equals("0")) {
                return rpcId.equals("0") ? -1 : 1;
            }else{
                String[] rpcIds = rpcId.split("\\.");
                String[] oRpcIds = oRpcId.split("\\.");
                int minSize = Math.min(rpcIds.length, oRpcIds.length);
                for(int i=1;i<minSize;++i){
                    if (Integer.parseInt(rpcIds[i]) < Integer.parseInt(oRpcIds[i])) {
                        return -1;
                    }else if(Integer.parseInt(rpcIds[i]) > Integer.parseInt(oRpcIds[i])){
                        return 1;
                    }else{
                        //0.1和0.1.1之间比较
                        if(rpcIds.length<oRpcIds.length){
                            return -1;
                        }else if(rpcIds.length>oRpcIds.length){
                            return 1;
                        }
                    }
                }
                //走到这里说明完全相等，比较endpoint，client比server优先
                if (endpoint.equals("client")) {
                    return -1;
                } else {
                    return 1;
                }
            }
        }
    }

    @Test
    public void test_BdsTrace() throws IOException {
        String file = "/data/program/logs/bds/t.log";
        String str = IOUtils.readFile(new File(file));
        List<JSONObject> jsonList = JSON.parseArray(str, JSONObject.class);
        List<Span> spanList = new ArrayList<>();
        for (JSONObject jsonObject : jsonList) {
            Span span = jsonObject.getObject("_source", Span.class);
            spanList.add(span);
        }
        Collections.sort(spanList);
        //System.out.println(spanList.size());
        TextTable table = new TextTable("id", "appName", "rpcId", "endpoint", "clientIp", "serverIp", "cost", "unitName");

        for (int i = 0; i < spanList.size(); ++i) {
            Span span = spanList.get(i);
            table.addRow(
                    String.valueOf(i),
                    span.getAppName(),
                    span.getRpcId(),
                    span.getEndpoint(),
                    span.getClientIp(),
                    span.getServerIp(),
                    span.getCost(),
                    span.getUnitName()
            );
        }

        System.out.println(table.toString());

    }

    @Test
    public void testQuantumJson() throws IOException {
        String file = "/tmp/log";
        String str = IOUtils.readFile(new File(file));
        JSONObject jsonObject = JSON.parseObject(str);
        JSONArray buckets = jsonObject.getJSONArray("buckets");
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buckets.size(); ++i) {
            sb.append(buckets.getJSONObject(i).getString("key"));
            sb.append(",");
        }
        System.out.println(sb.toString());
    }

    @Test
    public void testDominoJson() throws IOException {
        Map<String, String> bucketInfo = getBucketInfo();

        String file = "/tmp/log";
        String str = IOUtils.readFile(new File(file));
        JSONObject jsonObject = JSON.parseObject(str);
        //JSONArray buckets = jsonObject.getJSONObject("aggregations").getJSONObject("entity_id").getJSONArray("buckets");
        JSONArray buckets = jsonObject.getJSONArray("buckets");
        TextTable table = new TextTable("No.", "Bucket", "BizLine", "Size");
        List<Bucket> bucketList = new ArrayList();
        for (int i = 0; i < buckets.size(); ++i) {
            JSONObject bucket = buckets.getJSONObject(i);
            String name = bucket.getString("key");
            if (name.equals("test") || name.equals("domino-test")) {
                continue;
            }
            long bytes = bucket.getJSONObject("total").getLong("value");
            table.addRow(
                    String.valueOf(i),
                    name,
                    bucketInfo.get(name),
                    //String.valueOf(bytes),
                    FormatUtils.humanReadableByteSize(bytes)
            );
            bucketList.add(new Bucket(name, bucketInfo.get(name), bytes));
        }
        System.out.println(table.toString());
        Map<String, List<Bucket>> bucketMap = bucketList.stream().collect(Collectors.groupingBy(Bucket::getBizLine));
        Map<String, Double> summaryMap = bucketMap.entrySet().stream().collect(Collectors.toMap(e -> e.getKey(), e -> {
            return e.getValue().stream().mapToDouble(i -> i.getSize()).sum() / 1024 / 1024 / 1024 / 1024.0;
        }));

        summaryMap.entrySet().stream().forEach(e -> {
            System.out.println(e.getKey() + "\t" + FormatUtils.roundx4(e.getValue()));
        });
    }

    @Data
    @AllArgsConstructor
    private static class Bucket {
        private String name;
        private String bizLine;
        private long size;
    }

    private Map<String, String> getBucketInfo() throws IOException {
        Map<String, String> map = new HashMap<String, String>() {
            @Override
            public String get(Object key) {
                String ret = super.get(key);
                return ret == null ? "其它" : ret;
            }
        };
        for (String line : IOUtils.readLines(new File("/data/program/logs/bucket_map.log"))) {
            String[] pair = line.split(",");
            if (pair.length == 1) continue;
            map.put(pair[0].trim(), pair[1].trim());
        }

        return map;
    }


}
