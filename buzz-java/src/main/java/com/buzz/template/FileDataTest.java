package com.buzz.template;

import com.buzz.toolkit.RandomUtils;
import com.buzz.util.IOUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
public class FileDataTest {

    private static final char LF = '\n';

    private StringBuffer write(StringBuffer stringBuffer, String line) {
        stringBuffer.append(line);
        stringBuffer.append(LF);
        return stringBuffer;
    }

    @Test
    public void testTime() {
        Time time = new Time(2, 0);
        // data: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun']
        StringBuffer timesb = new StringBuffer("[");
        int size = 60;
        for (int i = 0; i < size; ++i) {
            time.increment();
            timesb.append("'" + time + "'");
            if (i != size - 1) {
                timesb.append(",");
            }
        }
        timesb.append("]");
        System.out.println(timesb.toString());

        StringBuffer v1sb = new StringBuffer("[");
        StringBuffer v2sb = new StringBuffer("[");
        StringBuffer v3sb = new StringBuffer("[");
        for (int i = 0; i < size; ++i) {
            int v1 = RandomUtils.getRangeRandom(500, 1000);
            int v2 = RandomUtils.getRangeRandom(200, 500);
            int v3 = RandomUtils.getRangeRandom(100, 200);

            v1sb.append(v1);
            v2sb.append(v2);
            v3sb.append(v3);

            if (i != size - 1) {
                v1sb.append(",");
                v2sb.append(",");
                v3sb.append(",");
            }
        }
        v1sb.append("]");
        v2sb.append("]");
        v3sb.append("]");
        System.out.println(v1sb.toString());
        System.out.println(v2sb.toString());
        System.out.println(v3sb.toString());
    }

    @Test
    public void testCVS() throws Exception {

        StringBuffer sb = new StringBuffer();
        write(sb, "series,x,y");
        List<Source> sources = Lists.newArrayList(
                Source.of("HTTP", Range.of(1000, 2000)),
                Source.of("Dubbo", Range.of(500, 1000)),
                Source.of("MQ", Range.of(50, 100))
        );
        final Time time = new Time(1, 0);
        for (int i = 0; i < 50; ++i) {
            time.increment();
            sources.forEach(s -> {
                write(sb, s.convert(time));
            });
        }
        IOUtils.writeFile(new File("/tmp/test.csv"), sb.toString(), "UTF-8");
        System.out.println(sb.toString());

//        String[] lines = IOUtils.readLines(new File("/Users/<USER>/Downloads/Line-20220413.csv"));
//        System.out.println(lines[0].charAt(lines[0].length() - 1));

    }

    @Test
    public void testSkywalkingMapping() throws IOException {
        List<String> lines = IOUtils.readLines(new File("/tmp/1.log"));
        Map<String, List<String>> map = new HashMap<>();
        for(String line:lines){
            String[] items = line.split("->");
            String item1 = items[0].trim();
            String item2 = items[1].trim();
            if(!map.containsKey(item2)){
                map.put(item2 ,new ArrayList<>());
            }
            map.get(item2).add(item1);

        }
        map.forEach((k,v)->{
            log.info("{} ->{}", k,v);
        });
        //System.out.println(map);
    }

    static class Source {
        private String name;
        private Range value;

        public Source(String name, Range value) {
            this.name = name;
            this.value = value;
        }

        public static Source of(String name, Range value) {
            return new Source(name, value);
        }

        public String convert(Time time) {
            return String.format("%s,%s,%s", name, time, RandomUtils.getRangeRandom(1000, 2000));
        }
    }

    static class Range {
        int begin;
        int end;

        public Range(int begin, int end) {
            this.begin = begin;
            this.end = end;
        }

        public static Range of(int begin, int end) {
            return new Range(begin, end);
        }
    }

    static class Time {
        int h;
        int m;

        public Time(int h, int m) {
            checkRange(h, m);
            this.h = h;
            this.m = m;

        }

        public void checkRange(int h, int m) {
            if (h > 24) {
                throw new RuntimeException("hour can't more than 24!");
            }
            if (m > 60) {
                throw new RuntimeException("minute can't more than 60!");
            }
        }

        public void increment() {
            if (m >= 60) {
                h++;
                m = 0;
            } else {
                ++m;
            }
        }

        public String toString() {
            return String.format("%02d:%02d", h, m);
        }
    }
}
