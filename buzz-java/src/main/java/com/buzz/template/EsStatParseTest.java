package com.buzz.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.buzz.util.FormatUtils;
import com.buzz.util.IOUtils;
import com.buzz.util.TextTable;
import lombok.AllArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;

/**
 * 解析/_nodes/stats
 */
public class EsStatParseTest {


    @Test
    public void test() throws IOException {
        JSONObject json = JSON.parseObject(IOUtils.readFile("/Users/<USER>/Downloads/stats.log"));
        JSONObject nodes = json.getJSONObject("nodes");
        TextTable table = new TextTable("id", "host", "rack", "store", "used", "used(%)", "total", "free", "load");
        int ix = 0;
        long totalStoreSize = 0;
        long totalFsSize = 0;
        long totalUsedFsSize = 0;

        ArrayList<Node> nodeList = new ArrayList<>();
        for (String key : nodes.keySet()) {
            JSONObject node = nodes.getJSONObject(key);
            if (!node.getJSONArray("roles").contains("data")) {
                continue;
            }
            String host = node.getString("ip");
            String rack = node.getJSONObject("attributes").getString("rack");
            nodeList.add(new Node(key, host, rack));
        }

        Collections.sort(nodeList);

        for (Node item : nodeList) {
            JSONObject node = nodes.getJSONObject(item.id);
            if (!node.getJSONArray("roles").contains("data")) {
                continue;
            }
            String host = node.getString("ip");
            JSONObject indices = node.getJSONObject("indices");
            //索引占用
            long storeSize = indices.getJSONObject("store").getLong("size_in_bytes");
            //文件
            JSONObject fs = node.getJSONObject("fs");
            long dataTotal = fs.getJSONArray("data").getJSONObject(0).getLong("total_in_bytes");
            long dataFree = fs.getJSONArray("data").getJSONObject(0).getLong("free_in_bytes");
            long dataUsed = dataTotal - dataFree;
            float dataUsedPct = dataUsed / (dataTotal * 1.0f); //Percentage

            JSONObject os = node.getJSONObject("os");
            String load1m = os.getJSONObject("cpu").getJSONObject("load_average").getString("1m");

            table.addRow(ix,
                    host,
                    item.rack,
                    FormatUtils.humanReadableByteSize(storeSize),
                    FormatUtils.humanReadableByteSize(dataUsed),
                    FormatUtils.percentx0(dataUsedPct),
                    FormatUtils.humanReadableByteSize(dataTotal),
                    FormatUtils.humanReadableByteSize(dataFree),
                    load1m
            );

            ++ix;
            totalStoreSize += storeSize;
            totalFsSize += dataTotal;
            totalUsedFsSize += dataUsed;
        }

        System.out.println(table.toString());
        System.out.println("total_datafs: " + FormatUtils.humanReadableByteSize(totalFsSize));
        System.out.println("indices_store: " + FormatUtils.humanReadableByteSize(totalStoreSize));
        System.out.println("total_used_datafs: " + FormatUtils.humanReadableByteSize(totalUsedFsSize));
    }

    @AllArgsConstructor
    static class Node implements Comparable<Node> {
        private String id;
        private String host;
        private String rack;

        @Override
        public int compareTo(@NotNull Node o) {
            if (rack.equals(o.rack)) {
                String ip = host.split(":")[0].split("\\.")[3];
                String ip2 = o.host.split(":")[0].split("\\.")[3];
                return Integer.valueOf(ip).compareTo(Integer.valueOf(ip2));
            } else {
                return rack.compareTo(o.rack);
            }
        }

        @Override
        public String toString() {
            return host;
        }
    }
}
