package com.buzz.template;

import java.io.BufferedReader;
import java.io.FileReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

import org.junit.Test;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;

public class KafkaConfigTest {


    private List<String>  readLogFile(String filePath) {
        // Implement file reading logic
        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(filePath))) {
            String line;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
       return lines;
    }
    
 
    
 
    
    //产生
    @Test
    public void testGenerateResignConfig(){
        // Read log file content
        List<String> lines = readLogFile("/tmp/all_topic.log");

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("topics", new JSONArray());
        jsonObject.put("version", "1");

        for(String line:lines){
            JSONArray topics = jsonObject.getJSONArray("topics");
            JSONObject topic = new JSONObject();
            topic.put("topic", line);
            topics.add(topic);
        }    
        System.out.println(jsonObject.toJSONString());
        


        
    }

}
