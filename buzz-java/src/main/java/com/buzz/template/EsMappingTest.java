package com.buzz.template;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.buzz.util.IOUtils;
import com.buzz.util.TextTable;
import lombok.AllArgsConstructor;
import lombok.Data;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;

import java.io.File;
import java.util.*;
import java.util.Map.Entry;
import java.util.stream.Collectors;

/**
 * 索引mapping字段对比
 *
 * <AUTHOR>
 * @description
 **/
public class EsMappingTest {

    private static File k2FundLogs = new File("/data/program/logs/w-fund-logs"); //w-fund
    private static File k2Logs = new File("/data/program/logs/k2-logs"); //w-middleware_hermes
    private static File k2MonitorLogs = new File("/data/program/logs/k2-monitor-logs"); //k2-monitor-logs
    private static File clientLogs = new File("/data/program/logs/client");


    @Test
    public void test_k2FundLogs() {
        check(k2FundLogs);
    }

    @Test
    public void test_K2Logs() {
        check(k2Logs);
    }

    @Test
    public void test_K2MonitorLogs() {
        check(k2MonitorLogs);
    }

    @Test
    public void test_ClientLogs() {
        check(clientLogs);
    }


    private void check(File fie) {
        List<Properties> list = new ArrayList<>();
        for (File file : fie.listFiles()) {
            try {
                JSONObject data = JSON.parseObject(IOUtils.readFile(file));
                list.add(parse(file.getName(), data.getJSONObject("properties")));
            } catch (Exception e) {
                throw new RuntimeException("parse " + file.getPath() + " error", e);
            }
        }

        Set<String> allFields = new TreeSet<>();
        TextTable table = new TextTable();
        table.addRow(list.stream().map(Properties::getName).collect(Collectors.toList()));
        int height = 0;
        for (Properties properties : list) {
            height = Math.max(properties.getSize(), height);
        }
        for (int k = 0; k < height; ++k) {
            List<String> row = new ArrayList<>(list.size());
            for (int i = 0; i < list.size(); ++i) {
                List<Filed> filedList = list.get(i).getFiledList();
                if (k < filedList.size()) {
                    String cell = filedList.get(k).getName() + ": " + filedList.get(k).getType();
                    row.add(cell);
                    allFields.add(cell);
                } else {
                    row.add("-");
                }
            }
            table.addRow(row);
        }
        System.out.println(table);
        //Collections.sort(new ArrayList());
        //allFields.stream().forEach(System.out::println);
        generate(allFields);
    }

    public static void generate(Set<String> allFields) {
        JSONObject jsonObject = new JSONObject();
        Map<String, List<String>> subProperties = new LinkedHashMap<>();
        for (String filed : allFields) {
            String[] pair = filed.split(": ");
            String name = pair[0];
            if (name.indexOf(".") != -1) { //属于子properties
                String[] subPair = name.split("\\.");
                List<String> subList = subProperties.computeIfAbsent(subPair[0], v -> {
                    return new ArrayList();
                });
                subList.add(subPair[1] + ":" + pair[1]);
            } else {
                JSONObject type = new JSONObject();
                type.put("type", pair[1]);
                jsonObject.put(name, type);
            }
        }
        subProperties.entrySet().forEach(e -> {
            String key = e.getKey();
            List<String> subList = e.getValue();
            JSONObject subJson = new JSONObject();
            jsonObject.put(key, subJson);
            JSONObject propertiesJson = new JSONObject();
            subJson.put("properties", propertiesJson);
            for (String sub : subList) {
                String[] pair = sub.split(":");
                JSONObject typeJson = new JSONObject();
                typeJson.put("type", pair[1]);
                propertiesJson.put(pair[0], typeJson);
            }
        });
        System.out.println(JSON.toJSONString(jsonObject));
    }

    private Properties parse(String name, JSONObject jsonObject) {
        List<Filed> filedList = new ArrayList<>();
        for (Entry<String, Object> entry : jsonObject.entrySet()) {
            String filedName = entry.getKey();
            JSONObject fieldValue = (JSONObject) entry.getValue();
            if (fieldValue.containsKey("properties")) {
                JSONObject subJsonObject = (JSONObject) fieldValue.get("properties");
                Properties subProperties = parse(name, subJsonObject);
                for (Filed field : subProperties.getFiledList()) {
                    filedList.add(new Filed(filedName + "." + field.getName(), field.getType()));
                }
            } else {
                String filedType = fieldValue.getString("type");
                filedList.add(new Filed(filedName, filedType));
            }
        }

        return new Properties(name, filedList);
    }

    @Data
    private static class Properties {
        private String name;
        private List<Filed> filedList;

        public Properties(String name, List<Filed> filedList) {
            this.name = name;
            this.filedList = filedList;
            Collections.sort(filedList);

        }

        public int getSize() {
            return filedList.size();
        }

    }

    @Data
    @AllArgsConstructor
    private static class Filed implements Comparable<Filed> {
        private String name;
        private String type;

        @Override
        public int compareTo(@NotNull Filed o) {
            return name.compareTo(o.getName());
        }
    }
}
