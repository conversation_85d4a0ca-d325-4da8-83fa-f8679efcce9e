package com.buzz.template;

import com.buzz.util.IOUtils;
import lombok.extern.slf4j.Slf4j;
import org.docx4j.Docx4J;
import org.docx4j.XmlUtils;
import org.docx4j.jaxb.Context;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.docx4j.openpackaging.parts.WordprocessingML.MainDocumentPart;
import org.docx4j.wml.*;
import org.junit.Test;

import javax.xml.bind.JAXBElement;
import javax.xml.bind.JAXBException;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.util.List;

/**
 * https://www.baeldung.com/docx4j
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class Docx4jTest {

    @Test
    public void test_HelloWorld() throws Exception {
        WordprocessingMLPackage wordPackage = WordprocessingMLPackage.createPackage();
        MainDocumentPart mdp = wordPackage.getMainDocumentPart();
        mdp.addStyledParagraphOfText("Title", "Hello World!");
        mdp.addParagraphOfText("Welcome To Baeldung");

        //File exportFile = new File("welcome.docx");
        //System.out.println(XmlUtils.marshaltoString(mdp.getContents(), true, true) );
        //wordPackage.save(exportFile);

        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Docx4J.save(wordPackage, baos, Docx4J.FLAG_SAVE_FLAT_XML);
        System.out.println(new String(baos.toByteArray()));
    }

    @Test
    public void test_HelloWorld2() throws Exception {
        WordprocessingMLPackage wordPackage = WordprocessingMLPackage.createPackage();
        MainDocumentPart mainDocumentPart = wordPackage.getMainDocumentPart();
        ObjectFactory factory = Context.getWmlObjectFactory();

        P p = factory.createP();
        R r = factory.createR();
        Text t = factory.createText();
        t.setValue("Welcome To Baeldung");
        r.getContent().add(t);
        p.getContent().add(r);

        RPr rpr = factory.createRPr();
        BooleanDefaultTrue b = new BooleanDefaultTrue();
        rpr.setB(b);
        rpr.setI(b);
        rpr.setCaps(b);

        Color red = factory.createColor();
        red.setVal("red");
        rpr.setColor(red);
        r.setRPr(rpr);
        mainDocumentPart.getContent().add(p);
        File exportFile = new File("welcome.docx");
        wordPackage.save(exportFile);

    }

    @Test
    public void test_Read() throws Exception {
        File doc = new File("/data/docs/template1.docx");
        WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.load(doc);
        MainDocumentPart mainDocumentPart = wordMLPackage.getMainDocumentPart();
        String textNodesXPath = "//w:t";
        List<Object> textNodes = mainDocumentPart.getJAXBNodesViaXPath(textNodesXPath, true);
        for (Object obj : textNodes) {
            JAXBElement node = (JAXBElement) obj;
            System.out.println(node.getDeclaredType() + "," + node.getName());
//            break;
//            Text text = (Text) ((JAXBElement) obj).getValue();
//            String textValue = text.getValue();
//            System.out.println(textValue);
        }
    }

    //google: docx4j add shape
    //https://stackoverflow.com/questions/22781843/inserting-any-shape-into-a-word-document-using-apache-poi
    @Test
    public void test_Pic() throws Exception {
        WordprocessingMLPackage wordPackage = WordprocessingMLPackage.createPackage();
        MainDocumentPart mdp = wordPackage.getMainDocumentPart();
        mdp.getContent().add(pRectangleViaXML());
        //mdp.getContent().add(pTextBox());
        //mdp.getContent().add(pOval());
        // mdp.getContent().add(pRectangle());
        //mdp.getContent().add(pRectangleViaXML());

        System.out.println(XmlUtils.marshaltoString(mdp.getContents(), true, true));

        File exportFile = new File("welcome.docx");
        wordPackage.save(exportFile);
    }


    public static P pRectangleViaXML() throws JAXBException {

        String openXML = "<w:p xmlns:v=\"urn:schemas-microsoft-com:vml\" xmlns:w=\"http://schemas.openxmlformats.org/wordprocessingml/2006/main\" xmlns:o=\"urn:schemas-microsoft-com:office:office\">"
                + "<w:pPr>"
                + "<w:rPr>"
                + "<w:lang w:val=\"en-AU\"/>"
                + "</w:rPr>"
                + "</w:pPr>"
                + "<w:r>"
                + "<w:rPr>"
                + "<w:noProof/>"
                + "</w:rPr>"
                + "<w:pict>"
                + "<v:rect fillcolor=\"#4f81bd [3204]\" id=\"Rectangle 8\"  o:spid=\"_x0000_s1026\" strokecolor=\"#243f60 [1604]\" strokeweight=\"2pt\" style=\"position:absolute;margin-left:48.75pt;margin-top:13.85pt;width:115.5pt;height:54pt;z-index:251661312;visibility:visible;mso-wrap-style:square;mso-wrap-distance-left:9pt;mso-wrap-distance-top:0;mso-wrap-distance-right:9pt;mso-wrap-distance-bottom:0;mso-position-horizontal:absolute;mso-position-horizontal-relative:text;mso-position-vertical:absolute;mso-position-vertical-relative:text;v-text-anchor:middle\"/>"
                + "</w:pict>"
                + "</w:r>"
                + "</w:p>";
        System.out.println(openXML);
        return (P) XmlUtils.unmarshalString(openXML);
    }


    @Test
    public void test_WriteTable() throws Exception {
        writeViaXmlFile("/docs/table.xml");
    }


    @Test
    public void test_WriteTableYll() throws Exception {
        String inputFile = getClass().getResource("/docs/table_yll_all.txt").getFile();
        String inputVM = IOUtils.readFile(inputFile);
        String[] openXMLs = inputVM.split("===");
        writeViaXml(openXMLs);
    }

    private void writeViaXmlFile(String... filePaths) throws Exception {
        String[] openXMLs = new String[filePaths.length];
        for (int i = 0; i < filePaths.length; ++i) {
            openXMLs[0] = IOUtils.readFile(this.getClass().getResource(filePaths[i]).getFile());
        }
        writeViaXml(openXMLs);
    }

    private void writeViaXml(String... openXMLs) throws Exception {
        WordprocessingMLPackage wordPackage = WordprocessingMLPackage.createPackage();
        MainDocumentPart mdp = wordPackage.getMainDocumentPart();

        for (String openXML : openXMLs) {
            if (openXML.isEmpty()) continue;
            try {
                mdp.getContent().add(XmlUtils.unmarshalString(openXML));
            } catch (Exception e) {
                log.error("openXML=" + openXML, e);
                System.exit(-1);
            }
        }

        File exportFile = new File("welcome.docx");
        wordPackage.save(exportFile);
    }

    @Test
    public void test_loadByXml() throws Exception {
        String xmlFilePath = "/docs/yll.vm";
        File xmlFile = new File(this.getClass().getResource(xmlFilePath).getFile());
        System.out.println(xmlFile);
        WordprocessingMLPackage pkg = Docx4J.load(xmlFile);
        File exportFile = new File("welcome.docx");
        Docx4J.save(pkg, exportFile);

    }
}


