package com.buzz.influx;

import org.influxdb.InfluxDB;
import org.influxdb.InfluxDBFactory;

public class InfluxdbFactory {

	private InfluxDB influxDB = null;

	public InfluxDB create() {
		if (influxDB != null) {
			return influxDB;
		}
		String url = "http://172.16.48.182:8086";
		this.influxDB = InfluxDBFactory.connect(url);
		this.influxDB.createDatabase("metrics_db");
		return influxDB;
	}

	private static InfluxdbFactory instance = new InfluxdbFactory();

	public static InfluxdbFactory get() {
		return instance;
	}
}
