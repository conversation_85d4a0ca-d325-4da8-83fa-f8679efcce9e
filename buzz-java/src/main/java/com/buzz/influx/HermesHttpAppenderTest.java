package com.buzz.influx;

import java.io.IOException;
import java.net.HttpURLConnection;
import java.net.URL;

public class HermesHttpAppenderTest {



	public static void main(String[] args) throws Throwable {

		URL url = new URL(
				"http://hermes-proxy-log.middleware.wse.test.wacai.info/hermes-proxy/sendSingle?topic=metric.bds.ratel&appName=middleware-test-app");

		HttpURLConnection conn = (HttpURLConnection) url.openConnection();
		conn.setDoOutput(true);
		conn.setRequestMethod("POST");
		conn.setReadTimeout(3000);

		conn.setRequestProperty("Client-Id", "metric_bds_sender_1");
		conn.setRequestProperty("Content-Type", "application/json;' charset=UTF-8");

		conn.getOutputStream().write("test".getBytes());

		System.out.println(conn.getResponseCode());
		System.out.println(conn.getContent());
		if (conn.getResponseCode() != 200) {
			throw new IOException(conn.getResponseMessage());
		}
	}
}
