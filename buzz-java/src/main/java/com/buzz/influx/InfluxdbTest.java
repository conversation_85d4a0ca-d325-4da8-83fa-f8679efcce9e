package com.buzz.influx;

import org.influxdb.InfluxDB;
import org.influxdb.dto.Query;
import org.influxdb.dto.QueryResult;
import org.junit.Test;

public class InfluxdbTest {

	private String template = "SELECT SUM(\"count\") FROM error_metrics " +
			"WHERE appname = '%s' AND type = 'error' AND time > %d AND time <= %d AND errorName = '%s' " +
			"GROUP BY errorName";

	@Test
	public void testBds() {
		InfluxDB influxDB = InfluxdbFactory.get().create();

//		String command = "SELECT sum(count) as count,sum(successCount) as success,sum(timeoutCount) as timeout,sum(rejectCount) as reject, sum(errorCount) as error,sum(sumRT) as "
//				+ "sumRt, max(maxRT) as maxRt FROM service_metrics_aggregate_server WHERE time >= 1630496760000000000 AND time < 1630496820000000000 "
//				+ "and appname = 'yo-cms' group by idc, serviceName, serviceType";

//		SELECT sum(count) as count,sum(successCount) as success,sum(timeoutCount) as timeout,sum(rejectCount) as reject, sum(errorCount) as error,sum(sumRT) as sumRt, max(maxRT 
//                ) as maxRt FROM service_metrics_aggregate_server WHERE time >= 1630496760000000000 AND time < 1630496820000000000 and appname = 'tesla-activity-provider' group by idc, serviceName, service 
//                Type
		
		
		String command = String.format("select * from error_metrics where appname ='middleware-test-app'");
		QueryResult queryResult = influxDB.query(new Query(command, "metrics_db"));
		System.out.println(	queryResult.getResults().size());
		
		queryResult.getResults().forEach(i->{
			System.out.println(i);
		});
	
	}
}
