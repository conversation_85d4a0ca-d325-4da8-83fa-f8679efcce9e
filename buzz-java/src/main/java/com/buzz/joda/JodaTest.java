package com.buzz.joda;

import org.joda.time.DateTime;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description
 **/
public class JodaTest {

    @Test
    public void testTime(){
        String dateFormat = "yyyy-MM-dd";
        int expireTime = 4;

        for (int i = 0; i < 3; i++) {
            DateTime dateTime = new DateTime();
            String expireDate = dateTime.minusDays(expireTime + i).toString(dateFormat);
            String indexName = "ratel_trace_index*".replaceFirst("\\*", "") + expireDate;
            System.out.println(indexName);
        }



    }
}
