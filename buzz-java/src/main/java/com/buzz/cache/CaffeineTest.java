package com.buzz.cache;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;

import java.io.IOException;
import java.util.concurrent.TimeUnit;

import org.checkerframework.checker.nullness.qual.NonNull;
import org.checkerframework.checker.nullness.qual.Nullable;
import org.junit.Test;

import com.buzz.cache.mock.DataObject;
import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.CacheLoader;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.LoadingCache;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class CaffeineTest {

	@Test
	public void testManual() {
		Cache<String, DataObject> cache = Caffeine.newBuilder()
				.expireAfterWrite(1, TimeUnit.MINUTES)
				.maximumSize(100)
				.build();
		String key = "A Data";
		DataObject dataObject = cache.getIfPresent(key);
		assertNull(dataObject);

		//手动设置value
		cache.put(key, DataObject.get(key));
		dataObject = cache.getIfPresent(key);
		assertNotNull(dataObject);

		dataObject = cache
				.get(key, k -> DataObject.get("A Data"));
		assertNotNull(dataObject);
		assertEquals("A Data", dataObject.getData());
	}

	@Test
	public void testEvictionBaseBySize() {
		
		//基于权重的淘汰算法，可以基于key或者value来动态淘汰
		LoadingCache<String, DataObject> cache = Caffeine.newBuilder()
				.maximumWeight(10)
				.weigher((k, v) -> 5)
				.build(k -> DataObject.get("Data for" + k));

		
		cache.get("A");
		assertEquals(1, cache.estimatedSize());

		cache.get("B");
		assertEquals(2, cache.estimatedSize());
		
		cache.get("C");
		
		cache.cleanUp();
		assertEquals(2, cache.estimatedSize());

	}
	@Test
	public void testEvictionBaseByTime() {
		LoadingCache<String,DataObject> cache =Caffeine.newBuilder()
		.expireAfterAccess(5, TimeUnit.MINUTES)
		.build(k->DataObject.get("Data for"+k));
	
	}
	
	@Test
	public void testRefersh() throws IOException, InterruptedException {
		LoadingCache<String, DataObject> cache = Caffeine.newBuilder()
				.refreshAfterWrite(1, TimeUnit.SECONDS)
				.build(k -> DataObject.get("Data for " + k));
		cache.get("B");
		
		while(true) {
			DataObject obj = cache.get("A");
			log.info("execute get {}",obj.getData());
			Thread.sleep(500);
		}
		
	}
	@Test
	public void testBuildByCacheLoader() {
		CacheLoader<String,DataObject> loader = key->DataObject.get("Data for" + key);		
		LoadingCache<String, DataObject> cache = Caffeine.newBuilder()
		.build(loader);
		
		loader  = new CacheLoader<String, DataObject>() {
			@Override
			public @Nullable DataObject load(@NonNull String key) throws Exception {
				return DataObject.get("Data for" + key);
			}
		};
	}

	
}
