package com.buzz.cache.tlive;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import com.google.common.cache.AbstractCache;

public class TairCache<K, V> extends AbstractCache<K, V> {

	private final V nullValue;

	private final ConcurrentHashMap<K, LocalCacheWrapper> cache = new ConcurrentHashMap<K, LocalCacheWrapper>();

	private int cacheExpiredInSeconds;

	private final ConcurrentMap<Object, Object> loadLatch = new ConcurrentHashMap<Object, Object>();

	private final DataLoader<K, V> dataLoader;

	public TairCache(DataLoader<K, V> dataLoader, V nullValue, int cacheExpiredInSeconds) {
		super();
		this.dataLoader = dataLoader;
		this.nullValue = nullValue;
		this.cacheExpiredInSeconds = cacheExpiredInSeconds;
	}

	@SuppressWarnings("unchecked")
	@Override
	public V getIfPresent(Object key) {
		LocalCacheWrapper wrapper = cache.get(key);
		V ret;
		if (wrapper != null && wrapper.getExpiredTime() < System.currentTimeMillis()) {
			ret = (V) wrapper.getValue();
			return ret == null ? nullValue : ret;
		} else {
			//说明过期
			cache.remove(key);
		}

		Object value = TairOps.get(key.toString());
		wrapper = wrapper(value);
		cache.put((K) key, wrapper);
		ret = (value == null) ? nullValue : (V) value;

		return ret;
	}

	public V get(K key) {
		V ret = getIfPresent(key);
		if (ret == nullValue) {
			return null;
		}
		if (ret == null) {
			CountDownLatch latch = new CountDownLatch(1);
			CountDownLatch previous = (CountDownLatch) loadLatch.putIfAbsent(key, latch);
			if (previous == null) {
				ret = getIfPresent(key);
				if (ret == null) {
					//加载数据
					ret = this.dataLoader.load(key);
					if (ret != null || nullValue != null) {
						this.put(key, ret);
					}
				}
			} else {
				//wait
				try {
					previous.await(2, TimeUnit.SECONDS);
				} catch (InterruptedException e) {
				}
				ret = getIfPresent(key);
				if (ret == null) {
					//加载数据
					ret = this.dataLoader.load(key);
					if (ret != null || nullValue != null) {
						this.put(key, ret);
					}
				}
			}
		}
		return ret;
	}

	public void put(K key, V value) {
		this.cache.put(key, wrapper(value));
	}

	private LocalCacheWrapper wrapper(Object value) {
		LocalCacheWrapper wrapper = new LocalCacheWrapper();
		wrapper.setValue(value);
		wrapper.setExpiredTime(System.currentTimeMillis() + TimeUnit.SECONDS.toMillis(cacheExpiredInSeconds));
		return wrapper;
	}
}