package com.buzz.cache;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import com.buzz.cache.mock.User;
import com.buzz.cache.mock.UserService;
import com.buzz.toolkit.NamedThreadFactory;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * 测试当 cache load value 返回null会导致缓存击穿问题
 * 2.8.2 修复了【race causing an incorrect removal cause】
 * 
 * @seehttps://github.com/ben-manes/caffeine/releases/tag/v2.8.2
 * <AUTHOR>
 *
 */
@Slf4j
public class CacheNullTest {

	private static String[] items = "lili,lili,lili,lili,lili,lili,lili,lili,lili".split(",");

	private static volatile boolean stop = false;

	public static void asyncInvoke(AsyncLoadingCache<String, User> cache) {
		List<CompletableFuture<User>> list = new ArrayList<>();
		for (String item : items) {
			CompletableFuture<User> f = CompletableFuture.supplyAsync(() -> {
				try {
					return cache.get(item).get(200, TimeUnit.MILLISECONDS);
				} catch (Exception e) {
					return null;
				}
			});

			list.add(f);
		}

		list.stream().forEach(f -> {
			try {
				log.info("{}", f.get());

			} catch (Exception e) {
				e.printStackTrace();
			}
		});

	}

	public static void invoke(AsyncLoadingCache<String, User> cache)
			throws InterruptedException, ExecutionException, TimeoutException {
		for (String item : items) {
			User u = cache.get(item).get(200, TimeUnit.MILLISECONDS);
			//log.info("get from cache {}", u);
		}
	}

	public static void main(String[] args) throws InterruptedException, ExecutionException, TimeoutException {
		UserService service = new UserService();

		AsyncLoadingCache<String, User> cache = Caffeine.newBuilder()
				.expireAfterWrite(3600, TimeUnit.SECONDS)
				.maximumSize(2000)
				.executor(Executors.newFixedThreadPool(10, new NamedThreadFactory("cache")))
				.removalListener(((key, value, cause) -> {
					log.info("removal key {} value {} cause {} ", key, value,cause);
					stop = true;

				}))
				.buildAsync(uname -> {
					log.info("load " + uname);
					Thread.sleep(30);
					return service.get(uname);
				});

		//使用 while循环模拟多次
		while (!stop) {
			invoke(cache);
			Thread.sleep(1000);
		}
	}
}
