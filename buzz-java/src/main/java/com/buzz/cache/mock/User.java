package com.buzz.cache.mock;

import java.util.Objects;

import lombok.Data;

@Data
public class User {

	private String name;
	private String nameCN;
	private String email;

	public User(String name) {
		super();
		this.name = name;
	}

	public User() {
		super();
	}

//	public static User newInstance(String name, String nameCN, String email) {
//		User instance = new User();
//		instance.setName(name);
//		instance.setNameCN(nameCN);
//		instance.setEmail(email);
//		return instance;
//	}
//
//	@Override
//	public boolean equals(Object o) {
//		if (this == o)
//			return true;
//		if (o == null || getClass() != o.getClass())
//			return false;
//		User user = (User) o;
//		return Objects.equals(name, user.name);
//	}
//
//	@Override
//	public int hashCode() {
//		return Objects.hash(name);
//	}

}
