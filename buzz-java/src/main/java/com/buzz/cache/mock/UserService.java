package com.buzz.cache.mock;

import java.util.HashMap;
import java.util.Map;

public class UserService {

	private Map<String, User> map = new HashMap<>();

	public UserService() {
		map.put("jack", new User("jack"));
		map.put("lili", new User("lili"));
		map.put("lucy", new User("lucy"));
	}

	public User get(String key) {
		if (key.contentEquals("lili")) {
			return null;
		}
		return map.get(key);
	}
	
	public User null_user = new User("null");
}
