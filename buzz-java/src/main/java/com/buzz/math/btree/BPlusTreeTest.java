package com.buzz.math.btree;

import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.FileChannel.MapMode;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;
import java.util.Arrays;
import java.util.stream.IntStream;

/**
 * 对 com.github.davidmoten.bplustree 的测试
 *
 * <AUTHOR>
 * @description
 **/
public class BPlusTreeTest {
    /**
     * btree key 中的二分查找
     *
     * @param keys
     * @param key
     * @return
     */
    private static int getLocation(int[] keys, int key) {
        System.out.println("find: " + Arrays.toString(keys) +" ==> "+key);
        int num = keys.length;
        int start = 0;
        int finish = num - 1;

        int result = -1;
        while (true) {
            int mid = (start + finish) / 2;
            System.out.println(String.format("find start:%s, finish:%s, mid:%s", start, finish, mid));
            if (key <= keys[mid]) {
                finish = mid;
                if (start == finish) {
                    result = start;
                    break;
                }
            } else {
                if (start == finish) {
                    result = mid + 1;
                    break;
                }
                start = mid + 1;
            }
        }
        System.out.println("find ret:" + result);
        return result;
    }



    @Test
    public void testVarInt(){
        int value = 60000;//对应的bin:10000010
        ByteBuffer buffer = ByteBuffer.allocate(3);
        while (true) {
            if ((value & ~0x7F) == 0) {
                buffer.put((byte) value);
                break;
            } else {
                buffer.put((byte) ((value & 0x7F) | 0x80));
                value >>>= 7;
            }
        }
        buffer.flip();

        System.out.println(buffer.get());
        System.out.println(buffer.get());
    }
    @Test
    public void testReadData() throws Exception {
        Tree tree = new Tree();
        tree.print();
    }

    @Test
    public void terGetLocation() {
        getLocation(new int[]{3, 7, 9, 11, 13, 15, 17, 20, 23}, 10);
        getLocation(IntStream.range(0, 3).toArray(), 4);
    }



    static class Tree {
        Node root;
        private IOManager ioManager;

        public Tree() throws IOException {
            ioManager = new IOManager();
            root = readRoot();
        }

        private Node readRoot() {
            //int position = (int) ioManager.ibb.getLong();
            return ioManager.readNode(0);
        }

        public void print() {
            travel(root);
        }

        private void travel(Node parent) {
            //如果是中间节点
            if (parent.type == 1) {
                System.out.println("leaf[" + parent.numKey + "]");
                for (int i = 0; i <= parent.numKey; ++i) {
                    Node node = parent.child(i);
                    travel(node);
                    //System.out.println(parent.key(i));
                }
            } else {
                //如果是叶子节点
                StringBuffer sb = new StringBuffer();
                sb.append("leafNode: ");
                for (int i = 0; i < parent.numKey; ++i) {
                    sb.append(parent.key(i) + "->" + parent.value(i) + ",");
                }
                System.out.println(sb.toString());
            }
        }
    }

    static class Node {
        int position;
        int type;
        int numKey;
        IOManager ioManager;

        public Node(int position, int type, int numKey, IOManager ioManager) {
            this.position = position;
            this.type = type;
            this.numKey = numKey;
            this.ioManager = ioManager;
        }

        private Node child(int i) {
            //NODE_TYPE_BYTES + NUM_KEYS_BYTES + i * (POSITION_BYTES + keySerializer.maxSize());
            int offset = relativePositionNonLeafEntry(i);
            int pos = position + offset;
            return ioManager.readNode(pos);
        }

        private int key(int i) {
            if (type == 0) {  //叶子节点
                int offset = relativeLeafKeyPosition(i);
                int pos = position + offset;
                ioManager.ibb.position(pos);
                return ioManager.ibb.getInt();
            } else {// 中间节点
                // NODE_TYPE_BYTES + NUM_KEYS_BYTES + i * (keySerializer.maxSize() + POSITION_BYTES)+POSITION_BYTES;
                int offset = relativePositionNonLeafEntry(i) + 8;
                int pos = position + offset;
                ioManager.ibb.position(pos);
                return ioManager.ibb.getInt();
            }
        }

        private int relativePositionNonLeafEntry(int i) {
            return 1 + 1 + i * (8 + 4);
        }

        private int relativeLeafKeyPosition(int i) {
            return 1 + 1 + i * (4 + 8);
        }

        private int value(int i) {
            //value只存在叶子节点
            //这里+4是跳过key的
            int offset = relativeLeafKeyPosition(i) + 4;
            int pos = position + offset;
            ioManager.ibb.position(pos);
            int valuePos = (int) ioManager.ibb.getLong();
            ioManager.values.position(valuePos);
            //valueSerializer
            return ioManager.values.getInt();
        }
    }


    static class IOManager<K,V> {
        MappedByteBufferWrapper ibb;
        MappedByteBufferWrapper values;

        private static MappedByteBufferWrapper createMappedByteBufferWrapper(String fileName) throws IOException {
            File file = new File("/tmp/btree-test", fileName);
            int segmentSizeBytes = 50 * 1024 * 1024;
            try (RandomAccessFile raf = new RandomAccessFile(file, "rw")) {
                raf.setLength(segmentSizeBytes);
            }

            FileChannel fileChannel = (FileChannel) Files.newByteChannel(file.toPath(),
                    StandardOpenOption.READ);

            // map the whole file
            MappedByteBuffer bb = fileChannel.map(MapMode.READ_ONLY, 0, segmentSizeBytes);
            MappedByteBufferWrapper mbb = new MappedByteBufferWrapper(bb, 0);
            return mbb;
        }
        public IOManager() throws IOException {
            ibb = createMappedByteBufferWrapper("index-0");
            values = createMappedByteBufferWrapper("value-0");
        }

        private int relativePositionNonLeafEntry(int i) {
            return 1 + 1 + i * (8 + 4);
        }

        private int relativeLeafKeyPosition(int i) {
            return 1 + 1 + i * (4 + 8);
        }

        public V leafValue(int position, int i) {
            //value只存在叶子节点
            //这里+4是跳过key的
            int offset = relativeLeafKeyPosition(i) + 4;
            int pos = position + offset;
            ibb.position(pos);
            int valuePos = (int) ibb.getLong();
            values.position(valuePos);
            //valueSerializer
            //return ioManager.vbb.getInt();
            return null;
        }

        public void leafSetValue(long position, int i, V value) {

        }


        private Node readNode(int position) {
            ibb.position(position);
            int pos = (int) ibb.getLong();
            ibb.position(pos);
            Node node = new Node(pos,
                    ibb.get(),
                    ibb.get(),
                    this);
            return node;
        }

    }

    static class MappedByteBufferWrapper {
        MappedByteBuffer bb;
        int position;

        public MappedByteBufferWrapper(MappedByteBuffer bb, int position) {
            this.bb = bb;
            this.position = position;
        }

        public void position(int newPosition) {
            this.position = newPosition;
        }

        public byte get() {
            bb.position(position++);
            return bb.get();
        }

        public long getLong() {
            int p = position;
            position += Long.BYTES;
            bb.position(p);
            return bb.getLong();
        }

        public int getInt() {
            int p = position;
            position += Integer.BYTES;
            bb.position(p);
            return bb.getInt();
        }
    }
}
