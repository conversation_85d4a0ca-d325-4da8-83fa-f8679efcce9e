package com.buzz.math;

import java.util.ArrayList;

/**
 * 
 * 一个简单的环形队列
 * <AUTHOR>
 */
public class CircularArray<E> {

	private int numBuckets;
	private int dataLength;
	private Object[] array;
	private int head = 0;
	private int tail = 0;
	private int size = 0;

	public CircularArray(int numBuckets) {
		this.numBuckets = numBuckets;
		this.array = new Object[numBuckets + 1];
		this.dataLength = this.array.length;
	}

	private int convert(int index) {
		return (head + index) % dataLength;
	}

	@SuppressWarnings("unchecked")
	public E[] getArray() {
		ArrayList<E> array = new ArrayList<E>();
		for (int i = 0; i < size; ++i) {
			array.add(get(i));
		}
		return (E[]) array.toArray();
	}

	@SuppressWarnings("unchecked")
	public E get(int i) {
		return (E) array[convert(i)];
	}

	public void add(E t) {
		this.array[tail] = t;
		incrementTail();
	}

	private void incrementTail() {
		//如果 size 已经到末尾，注意不是tail
		if (size == numBuckets) {
			head = (head + 1) % dataLength;
		}
		tail = (tail + 1) % dataLength;
		size = (tail - head + dataLength) % dataLength;
	}

	private String format(Object[] dataList) {
		StringBuffer sb = new StringBuffer();
		for (Object item : dataList) {
			sb.append(item.toString() + ",");
		}
		return sb.toString();
	}

	public static void main(String[] args) {

		CircularArray<Integer> array = new CircularArray<Integer>(4);
		for (int i = 0; i < 12; ++i) {
			array.add(i);
			System.out.println(i + "\t" + array.format(array.getArray()));
		}
	}
}
