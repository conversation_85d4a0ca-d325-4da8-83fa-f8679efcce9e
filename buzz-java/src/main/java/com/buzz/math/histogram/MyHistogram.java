package com.buzz.math.histogram;

import lombok.extern.slf4j.Slf4j;

import java.awt.geom.IllegalPathStateException;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于bucket的Histogram实现，类似
 * {
 * 0ms: 1,
 * 1ms: 1,
 * 3ms: 4,
 * ...
 * 400ms: 1
 * }
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class MyHistogram {
    long min, max;
    private AtomicLong numberValue = new AtomicLong();
    private Bucket[] buckets;

    public MyHistogram(long[] bucketLimits) {
        this.buckets = createBucket(bucketLimits);
    }

    private Bucket[] createBucket(long[] bucketLimits) {
        Bucket[] buckets = new Bucket[bucketLimits.length];
        long last = -1;
        for (int i = 0; i < bucketLimits.length; ++i) {
            long value = bucketLimits[i];
            buckets[i] = new Bucket(value);
            if (value < 0) {
                throw new IllegalPathStateException("value " + value + " must not be negative");
            }
            if (value < last) {
                throw new IllegalPathStateException("value " + value + " must greater than last " + last);
            }
            last = value;
        }
        return buckets;
    }

    public void add(long value) {
        getBucket(value).counter.incrementAndGet();
        numberValue.incrementAndGet();//记录value的条数

        if (numberValue.get() == 1) {
            min = value;
            max = value;
        } else if (value < min) {
            min = value;
        } else if (value > max) {
            max = value;
        }
    }

    private Bucket getBucket(long value) {
        for (Bucket bucket : buckets) {
            if (value < bucket.value) {
                return bucket;
            }
        }
        return buckets[buckets.length - 1];
    }

    public long getMedian() {
        return getPercentile(50);
    }

    /**
     * 核心就是找到百分位对应的bucket，本质还是找到一批数中的第N个, 不过这里我们没有保存原始的数字，而是通过bucket计数的方式实现。
     * 比如统计1万次延迟，数据分布是：1秒: 3000, 2秒:3000, 3秒:3000, 4秒:1000
     * 上述求p75的过程，1000*0.75=7500 ，然后找到7500对应在3秒所属 bucket
     *
     * @param percent
     * @return
     */
    public long getPercentile(int percent) {
        long percentSize = (long) Math.ceil(numberValue.get() * percent / 100.0);
        long valueSize = 0;
        for (Bucket bucket : buckets) {
            valueSize += bucket.counter.get();
            if (valueSize >= percentSize) {
                return bucket.value;
            }
        }
        return -1;
    }

    /**
     * 参考netflix的Histogram做了一个优化，解决上述bucket精度问题，比如插入了200条数求p99，对应的rank是200*0.99=198，对应的bucket是100
     * 按照我们前面的算法rank190~200都属于这个bucket，而netflix的版本会计算在bucket范围中占比
     *
     * @param percent
     * @return
     */
    public long getPercentile2(int percent) {
        //拿到rank,rank=总数*(percent/100)
        long n = (long) Math.ceil(numberValue.get() * percent / 100.0);
        if (n <= 0) {
            return this.min;
        } else if (n >= (this.numberValue.get() - 1)) {
            //如果计算出来的rank>=numberValue-1直接返回最大值
            return this.max;
        }

        double needed = n;
        int bucket = 0;
        int lastBucket = getNumBuckets() - 1;
        // Skip to the bucket that contains the desired index
        while (needed > 0 && bucket < lastBucket && needed >= getBucketCount(bucket)) {
            needed -= getBucketCount(bucket);
            bucket++;
        }
        //得到bucket的边界
        //注意bucket的边界可能比实际范围大，所以这里加一个判断
        double min = Math.max(getBucketMinimum(bucket), this.min);
        double max = Math.min(getBucketMaximum(bucket), this.max);
        long count = getBucketCount(bucket);
        double value;
        if (bucket <= lastBucket) {
            value = min + (max - min) * needed / count; //计算bucket中的占比
        } else if (count == 1) {
            // Final bucket, and only one entry (the max)
            value = max;
        } else {
            // Final bucket
            value = min + (max - min) * needed / (count - 1);
        }
        return (long) Math.floor(value + 0.5);
    }

    public double getBucketMinimum(int i) {
        if (i > 0) {
            return buckets[i - 1].value;
        } else if (getBucketCount(i) == 0) {
            // Edge case -- first bucket, but it is empty
            return Double.MIN_VALUE;
        } else {
            // First bucket is non-empty
            return buckets[0].value;
        }
    }

    public double getBucketMaximum(int i) {
        if (i < buckets.length) {
            return buckets[i].value;
        } else if (getBucketCount(i) == 0) {
            // last bucket, but empty
            return Double.MAX_VALUE;
        } else {
            return buckets[0].value;
        }
    }


    public int getNumBuckets() {
        return buckets.length;
    }

    public long getBucketCount(int i) {
        return buckets[i].counter.get();
    }

    @Override
    public String toString() {
        StringBuffer sb = new StringBuffer();
        sb.append("{");
        for (Bucket bucket : buckets) {
            if (bucket.counter.get() > 0) {
                sb.append(bucket.value + ":" + bucket.counter.get() + ",");
            }
        }
        sb.append("}");
        return sb.toString();
    }

    private static class Bucket {
        private long value;
        private AtomicLong counter;

        public Bucket(long value) {
            this.value = value;
            this.counter = new AtomicLong();
        }
    }


}
