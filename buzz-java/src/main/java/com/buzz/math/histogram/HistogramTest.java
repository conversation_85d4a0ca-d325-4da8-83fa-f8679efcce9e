package com.buzz.math.histogram;

import com.netflix.stats.distribution.Histogram;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description
 **/
public class HistogramTest {


    @Test
    public void testMyHistogram() {
        MyHistogram histogram = new MyHistogram(new long[]{10, 20, 30, 40, 50, 60, 70, 80, 90, 100});

        for (int i = 0; i < 100; ++i) {
            histogram.add(i);
        }

        for (int i = 0; i < 100; ++i) {
            histogram.add(5);
        }

        System.out.println(histogram);
        System.out.println(histogram.getMedian());
        System.out.println(histogram.getPercentile(75));
        System.out.println(histogram.getPercentile(95));
        System.out.println(histogram.getPercentile(99));
        System.out.println(histogram.getPercentile2(99));
    }


    @Test
    public void testMyHistogram2() {
        MyHistogram histogram = new MyHistogram(new long[]{10, 50, 75, 100, 200, 500, 700, 1000, 1500, 2000, 3000, 5000, 60000});
        histogram.add(1435);
        histogram.add(1535);
        histogram.add(1535);
        histogram.add(1535);
        histogram.add(2000);
        histogram.add(2200);
        histogram.add(2300);
        histogram.add(2400);
        histogram.add(2500);
        histogram.add(2600);
        System.out.println(histogram.getPercentile2(50));
        System.out.println(histogram.getPercentile2(75));
        System.out.println(histogram.getPercentile2(95));
        System.out.println(histogram.getPercentile2(99));
    }

    @Test
    public void testNetflixHistogram() {
        com.netflix.stats.distribution.Histogram histogram = new Histogram(new double[]{10, 50, 75, 100, 200, 500, 700, 1000, 1500, 2000, 3000, 5000, 60000});
        histogram.noteValue(1435);
        histogram.noteValue(1535);
        histogram.noteValue(1535);
        histogram.noteValue(1535);
        histogram.noteValue(2000);
        histogram.noteValue(2200);
        histogram.noteValue(2300);
        histogram.noteValue(2400);
        histogram.noteValue(2500);
        histogram.noteValue(2600);
        System.out.println(histogram.getPercentile(50));
        System.out.println(histogram.getPercentile(75));
        System.out.println(histogram.getPercentile(95));
        System.out.println(histogram.getPercentile(99));

//        for (int i = 0; i < 100; ++i) {
//            histogram.noteValue(i);
//        }
//
//        for (int i = 0; i < 100; ++i) {
//            histogram.noteValue(5);
//        }
//        System.out.println(histogram);
//        System.out.println(histogram.getMedian());
//        System.out.println(histogram.getPercentile(75));
//        System.out.println(histogram.getPercentile(95));
//        System.out.println(histogram.getPercentile(99));

    }
}
