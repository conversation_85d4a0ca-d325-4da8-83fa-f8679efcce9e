package com.buzz.math.bloomfilter;

public class MyBloomFilterTest {

    public static void main(String[] args) {
        MyBloomFilter filter = new MyBloomFilter();

        int size = 1000000;
        for (int i = 0; i < size; i++) {
            filter.add(String.valueOf(i));
        }
        
        for (int i = size + 1; i < size + 10000; i++) {

//            if (filter.exist(String.valueOf(i))) {
//                System.out.println("error!!!" + String.valueOf(i));
//            }else {
//                System.out.println("ok");
//            }
        }
    }

}
