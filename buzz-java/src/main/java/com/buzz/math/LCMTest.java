package com.buzz.math;

import org.junit.Test;

/**
 * 最小公倍数 least common multiple
 */
public class LCMTest {

    @Test
    public void test(){
        // 请替换这里的数字为你要计算的两个数
        int num1 = 640;
        int num2 = 8192;

        int result = LCMCalculator.lcm(num1, num2);

        System.out.println("最小公倍数是: " + result);
    }

    static class LCMCalculator {

        // 计算两个数的最大公约数，只x同时能被两数整除，12、16的最大公约数是4，4同时能被两数整除
        private static int gcd(int a, int b) {
            while (b != 0) {
                int temp = b;
                b = a % b;
                a = temp;
            }
            return a;
        }

        // 计算两个数的最小公倍数，指x同时是两数的倍数，12是4和6的最小公倍数，12同时是这两数的倍数
        private static int lcm(int a, int b) {
            return (a * b) / gcd(a, b);
        }


    }
}
