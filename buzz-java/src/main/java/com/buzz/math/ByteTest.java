package com.buzz.math;

import java.util.Date;


public class ByteTest {

	
	 static class Time {
		    /**
		     * Returns time in milliseconds as does System.currentTimeMillis(),
		     * but uses elapsed time from an arbitrary epoch more like System.nanoTime().
		     * The difference is that if somebody changes the system clock,
		     * Time.currentElapsedTime will change but nanoTime won't. On the other hand,
		     * all of ZK assumes that time is measured in milliseconds.
		     * @return  The time in milliseconds from some arbitrary point in time.
		     */
		    public static long currentElapsedTime() {
		        return System.nanoTime() / 1000000;
		    }

		    /**
		     * Explicitly returns system dependent current wall time.
		     * @return Current time in msec.
		     */
		    public static long currentWallTime() {
		        return System.currentTimeMillis();
		    }

		    /**
		     * This is to convert the elapsedTime to a Date.
		     * @return A date object indicated by the elapsedTime.
		     */
		    public static Date elapsedTimeToDate(long elapsedTime) {
		        long wallTime = currentWallTime() + elapsedTime - currentElapsedTime();
		        return new Date(wallTime);
		    }
		}
	 
	public static long initializeNextSession(long id) {
		long nextSid;
		nextSid = (Time.currentElapsedTime() << 24) >>> 8;
		nextSid = nextSid | (id << 56);
		return nextSid;
	}

	public static long test(long val, int offset) {
		val = val << offset;
		System.out.println(val + "\t" + "0x" + Long.toHexString(val) + "\t" + Long.toBinaryString(val));
		return val;
	}

	public static void main(String[] args) {
		System.out.println(0x1);
//		long id = 255;
//		long nextSid;
//		nextSid = (Time.currentElapsedTime() << 24) >>> 8;
//		nextSid = nextSid | (id << 56);
//		System.out.println(nextSid + "\t" + Long.toHexString(nextSid));
//		System.out.println(nextSid + "\t" + Long.toHexString(nextSid));

//		AtomicLong nextSessionId = new AtomicLong();
//		nextSessionId.set(initializeNextSession(id));
//		System.out.println("next=" + nextSessionId.getAndIncrement());
//
//		long val = 10000;
//		val = val << 40;
//		System.out.println(1 << 33);
//		System.out.println(System.currentTimeMillis()-val+"\t"+val);

//		long val = 311;
//		System.out.println(val + "\t" + "0x" + Long.toHexString(val) + "\t" + Long.toBinaryString(val));
//		val = test(val, 24);
//		long id = 11l << 56;
//		System.out.println("id->" + id + "\t" + "0x" + Long.toHexString(id) + "\t" + Long.toBinaryString(id));
//		val = val | id;
//		System.out.println(val + "\t" + "0x" + Long.toHexString(val) + "\t" + Long.toBinaryString(val));

		long zxid = Long.MAX_VALUE-1;
		boolean test = (zxid & 0xffffffffL) == 0xffffffffL;
		System.out.println(test);
		System.out.println(Long.toBinaryString(0xffffffffL));
	}
}
