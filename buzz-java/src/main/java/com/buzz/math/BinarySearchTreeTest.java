package com.buzz.math;

import lombok.Data;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

/**
 * (BST)Binary Search Tree
 *
 * <AUTHOR>
 * @description
 **/
public class BinarySearchTreeTest {

    @Test
    public void test(){
        BinaryTree binaryTree = new BinaryTree();
        binaryTree.insert(10);
        binaryTree.insert(5);
        binaryTree.insert(7);
        binaryTree.insert(4);
        binaryTree.insert(8);
        binaryTree.insert(3);
        binaryTree.insert(9);
        binaryTree.insert(2);
        binaryTree.insert(6);

        System.out.println(binaryTree.travel(TravelType.in));
        System.out.println(binaryTree.find(2));
        System.out.println(binaryTree.find(15));

    }
    private static class BinaryTree {

        private Node root;

        public void insert(Integer key) {
            Node node = new Node(key);
            if (root == null) {
                root = node;
                return;
            }
            Node parent = this.root;
            while (true) {
                if (key == parent.getKey()) {
                    throw new IllegalArgumentException("duplicate key" + key);
                } else if (key < parent.getKey()) {
                    if (parent.left == null) {
                        parent.left = node;
                        return;
                    } else {
                        parent = parent.left;
                    }
                } else {
                    if (parent.right == null) {
                        parent.right = node;
                        return;
                    } else {
                        parent = parent.right;
                    }
                }
            }
        }

        public int find(int key) {
            Node node = findNode(key);
            return node == null ? -1 : node.key;
        }

        private Node findNode(int key) {
            Node parent = this.root;
            while (true) {
                if (parent == null) {
                    return null;
                }
                if (key == parent.getKey()) {
                    return parent;
                } else if (key < parent.getKey()) {
                    parent = parent.left;
                } else {
                    parent = parent.right;
                }
            }

        }

        /**
         * consider 3 case:
         * 1. key don't exist in tree, return false;
         * 2. key is a leaf, insert directly
         * 3. key is a nonLeaf, if it have 2 children, use next node replace current node, if it only 1 child, directly
         * use child replace current node
         *
         * @param key
         * @return
         */
        public boolean remove(int key) {
            Node toDeletedNode = findNode(key);
            if (toDeletedNode == null) {
                return false;
            }
            //leaf node
            if (toDeletedNode.left == null && toDeletedNode.right == null) {
                //这里我们简单的把key改为-1
                toDeletedNode.key=-1;
            }else if (toDeletedNode.left != null && toDeletedNode.right != null) {
               //TODO
            } else {
                toDeletedNode = toDeletedNode.left != null ? toDeletedNode.left : toDeletedNode.right;
            }
            return true;
        }


        public List<Integer> travel(TravelType type) {
            List<Integer> list = new ArrayList<>();
            switch (type) {
                case in:
                    getTreeByInOrder(list, root);
                    break;
                case pre:
                    getTreeByPreOrder(list, root);
                    break;
                case post:
                    getTreeByPostOrder(list, root);
                    break;
            }
            return list;
        }

        /**
         * 中序遍历
         *
         * @param list
         * @param root
         */
        private void getTreeByInOrder(List<Integer> list, Node root) {
            if (root != null) {
                getTreeByInOrder(list, root.left);
                list.add(root.key);
                getTreeByInOrder(list, root.right);
            }
        }

        /**
         * 前序遍历
         *
         * @param list
         * @param root
         */
        private void getTreeByPreOrder(List<Integer> list, Node root) {
            if (root != null) {
                list.add(root.key);
                getTreeByInOrder(list, root.left);
                getTreeByInOrder(list, root.right);
            }
        }

        /**
         * 后续遍历
         *
         * @param list
         * @param root
         */
        private void getTreeByPostOrder(List<Integer> list, Node root) {
            if (root != null) {
                getTreeByInOrder(list, root.left);
                getTreeByInOrder(list, root.right);
                list.add(root.key);
            }
        }
    }

    private static enum TravelType {
        pre,
        in,
        post
    }

    @Data
    private static class Node {
        private Integer key;
        private Node left;
        private Node right;

        public Node(Integer key) {
            this.key = key;
        }
    }
}
