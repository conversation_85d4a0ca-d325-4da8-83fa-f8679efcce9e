package com.buzz.math;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.concurrent.atomic.AtomicReference;

/**
 * https://www.baeldung.com/java-concurrency-jc-tools
 *
 * stack: 插入过程
 *  _______     _______     _______
 * |   1  |     |   2  |    |   3  |     <-top
 * |      |     |   1  |    |   2  |
 * |      |     |      |    |   1  |
 */
@Slf4j
public class ConcurrentStackTest {


    @Test
    public void test() {
        ConcurrentStack<Integer> stack = new ConcurrentStack<>();
        stack.push(1);
        stack.push(2);
        stack.push(3);

        System.out.println(stack.pop());
    }


    public static class ConcurrentStack<E> {

        AtomicReference<Node<E>> top = new AtomicReference<Node<E>>();

        public void push(E item) {
            Node<E> newHead = new Node<E>(item);
            Node<E> oldHead;

            do {
                oldHead = top.get();
                newHead.next = oldHead;
            } while (!top.compareAndSet(oldHead, newHead));

            log.info("push top={} next={}", top.get(), top.get().next);
        }

        public E pop() {
            Node<E> oldHead;
            Node<E> newHead;
            do {
                oldHead = top.get();//从头开始找
                log.info("pop oldHead={}", oldHead);
                if (oldHead == null) {
                    return null;
                }
                newHead = oldHead.next; //下一个
            } while (!top.compareAndSet(oldHead, newHead));

            return oldHead.item;
        }

        private static class Node<E> {
            public E item;
            public Node<E> next;

            public Node(E item) {
                this.item = item;
            }

            @Override
            public String toString() {
                return item.toString();
            }
        }
    }
}
