package com.buzz.math;

import com.alibaba.fastjson.JSON;
import org.junit.Test;

/**
 * https://www.bilibili.com/video/BV1at411T75o
 */
public class QuickSortTest {

    public static void quickSort(int[] array, int low, int high) {
        int left = low;
        int right = high;
        if (left >= right) {
            return;
        }
        int pivot = array[left];
        while (left < right) {
            //移动右边
            while (left < right && array[right] >= pivot) {
                right--;
            }
            if (left < right) {
                array[left] = array[right];
            }
            while (left < right && array[left] <= pivot) {
                left++;
            }
            if (left < right) {
                array[right] = array[left];
            }
            if (left >= right) {
                array[left] = pivot;
            }
        }
        quickSort(array, low, right - 1);//左边部分递归
        quickSort(array, right + 1, high);//右边部分递归
    }

    @Test
    public void test() {
        int[] array = {19, 97, 9, 17, 1, 8};
        quickSort(array, 0, array.length - 1);
        System.out.println(JSON.toJSONString(array));
    }
}

