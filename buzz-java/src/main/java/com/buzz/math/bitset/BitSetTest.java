package com.buzz.math.bitset;

import org.junit.Test;

import java.io.IOException;
import java.util.ArrayList;
import java.util.BitSet;
import java.util.List;

/**
 * 
 * 用法
 * BitSet也是一种Set，所以具有HashSet类似的作用-去重。它的优势是不保存Value值，具有极低的内存消耗。
 * 因为BitSet内部存储是有序的，也可以利用这点进行排序。
 * 
 * 场景
 * 1、统计40亿个数据中没有出现的数据，将40亿个不同数据进行排序等。
 * 2、现在有1千万个随机数，随机数的范围在1到1亿之间。现在要求写出一种算法，将1到1亿之间没有在随机数中的数求出来
 * 
 * <AUTHOR>
 *
 */
public class BitSetTest {



	void printWord(long word){
		String bin = Long.toBinaryString(word);
		System.out.println(bin+"\t"+bin.length());
	}

	/**
	 * 通过 words[0] |= (1L << bitIndex) 设置bit位
	 */
	@Test
	public void testStoreBit() {
		long[] words = new long[1];
		words[0] |= (1L << 16);
		printWord(words[0]); //16 bit
		words[0] |= (1L << 32);
		printWord(words[0]); //32 bit
		words[0] |= (1L << 63);
		printWord(words[0]); //63 bit
		words[0] |= (1L << 97);
		printWord(words[0]); //97%64=33 bit
	}

	@Test
	public void testOOM() throws IOException {
		BitSet used = new BitSet(1000000000);//10 亿
		used.set(999999998);
		System.out.println(used.size());
		System.in.read();
	}

	/**
	 * 去掉字符串中重复的字符
	 */
	@Test
	public void testDuplicate() {
		//利用BitSet去重字符串
		String testStr = "aabbcc";
		BitSet used = new BitSet();
		for (int i = 0; i < testStr.length(); ++i) {
			System.out.println("设置:" + (int) testStr.charAt(i));
			used.set(testStr.charAt(i));
		}
		int size = used.size();
		System.out.println("已使用的size:" + size);
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < size; i++) {
			if (used.get(i)) {
				sb.append((char) i);
			}
		}
		System.out.println(sb.toString());
	}

	/**
	 * 排序
	 * @param asc
	 */
	public static void order() {
		int[] nums = new int[] { 19038290, 123, 1, 32, 12890, 100, 290, };
		BitSet used = new BitSet();
		for (int i = 0; i < nums.length; ++i) {
			used.set(nums[i]);
		}

		List<Integer> output = new ArrayList<Integer>();
		int size = used.size();
		System.out.println("已使用的size:" + size);
		for (int i = 0; i < size; ++i) {
			if (used.get(i)) {
				output.add(i);
			}
		}

		System.out.println("output:" + output);

	}

	public static void main(String[] args) throws IOException {
		
		BitSet used = new BitSet();
		used.set(1000000000);//10 亿
		used.set(999999998);
		System.out.println(used.size());
		System.in.read();

//		duplicate();
//		order();
//		long[] words = new long[32];
//		words[0] |= (1L << 0); 
//		System.out.println(Long.toBinaryString(words[0]));
//		words[0] |= (1L << 1); 
//		System.out.println(Long.toBinaryString(words[0]));
		
//		words[0] |= (1L << 65); 
//		System.out.println(Long.toBinaryString(words[15]));
//		words[0] |= (1L << 63); 
//		System.out.println(Long.toBinaryString(words[15]));
		
//		System.out.println("compare"); //19038290%64=18;
//		System.out.println(Long.toBinaryString(1<<18));
//		System.out.println(Long.toBinaryString(1<<19038290));
	}
}
