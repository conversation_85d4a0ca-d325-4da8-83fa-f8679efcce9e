package com.buzz.math.bitset;

import com.alibaba.fastjson.JSON;
import org.junit.Test;
import org.roaringbitmap.RoaringBitmap;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @description
 **/
public class RoaringBitmapTest {


    /**
     * 注意不需要修改该方法！
     * RoaringBitmap内部实现:采用char[] keys 保存高位bucket，有序但不需要像bitset那样提前分配空间,
     * 比如下面代码只需要分配4个bucket:[0,1,1525,15258]
     * 写入100000000，先计算出bucket为1525，存储keys数组中，低16位按key在keys中的offset保存在Container[]中
     * 读取100000000，先计算出bucket为1525，在keys数组中查询，如果找不到说明不存在，找到，通过其offset在Container[]中获取数据
     * base 65536
     */
    @Test
    public void test() {
        RoaringBitmap rb = new RoaringBitmap();
        rb.add(131075); // bucket: 2
        rb.add(655361); // bucket: 10
        rb.add(393216); // bucket: 6
        rb.add(327680); // bucket: 5
        rb.add(262144); // bucket: 4
    }

    /**
     * 测试 System.arraycopy
     * 1, 2, 4, 10 ->  [1],1,2, 4, 10
     */
    @Test
    public void testCopyArray() {
        int[] keys = {1, 2, 4, 10};
        int size = 4;
        keys = Arrays.copyOf(keys, 5);
        System.out.println(JSON.toJSONString(keys));
        int i = 3;
        System.arraycopy(keys, i, keys, i + 1, size - i);
        System.out.println(JSON.toJSONString(keys));

    }

    @Test
    public void test_temp() {
        char hb = highbits(70100);
        System.out.println((int) hb);
    }

    static char highbits(int x) {
        return (char) (x >>> 16);
    }
}
