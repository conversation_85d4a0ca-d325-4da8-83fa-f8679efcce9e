package com.buzz.math.bitset;

import java.util.Arrays;
import java.util.BitSet;
import java.util.Random;

/**
 * 对一千万数字进行排序对比
 * <AUTHOR>
 *
 */
public class BitSetSortCompare {

	public static void main(String[] args) {
		sortNums(generateNumber(10000000));
		sortNums1(generateNumber(10000000));
	}

	// 初始化一千万整数
	private static int[] generateNumber(int size) {
		long start = System.currentTimeMillis();
		//System.out.println("开始生成数据");
		int[] nums = new int[size];
		for (int i = 0; i < size; i++) {
			nums[i] = RandomUtils.nextInt(0, size);
		}
		System.out.println("生成数据完成,耗时:" + (System.currentTimeMillis() - start) + "毫秒");
		return nums;
	}

	// 使用BitSet进行排序
	private static String sortNums(int[] nums) {
		long start = System.currentTimeMillis();
		//System.out.println("BitSet 开始排序");
		int len = nums.length;
		StringBuilder sb = new StringBuilder();
		BitSet bitSet = new BitSet(len);
		bitSet.set(0, len, false);
		for (int i = 0; i < len; i++) {
			bitSet.set(nums[i], true);
		}
		for (int i = 0; i < len; i++) {
			if (bitSet.get(i)) {
				sb.append(i).append(",");
			}
		}
		System.out.println("BitSet 排序完成,耗时:" + (System.currentTimeMillis() - start) + "毫秒");
		return sb.toString();
	}

	// 使用Arrays工具类进行排序
	private static int[] sortNums1(int[] nums) {
		long start = System.currentTimeMillis();
		//System.out.println("Arrays工具类 开始排序");
		Arrays.sort(nums);
		System.out.println("Arrays工具类 排序完成,耗时:" + (System.currentTimeMillis() - start) + "毫秒");
		return nums;
	}

	private static class RandomUtils {
		private static final Random RANDOM = new Random();

		public static int nextInt(final int startInclusive, final int endExclusive) {
			return startInclusive + RANDOM.nextInt(endExclusive - startInclusive);
		}
	}
}
