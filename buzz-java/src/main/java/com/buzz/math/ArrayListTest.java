package com.buzz.math;

import org.junit.Assert;
import org.junit.Test;

import java.util.ArrayList;

public class ArrayListTest {


    @Test
    public void testArrayList() {
        ArrayList<String> list = new ArrayList<>();
        list.add("a");
        list.add("b");
        list.add("c");
        list.remove(1);
        Assert.assertEquals("c", list.get(1));

    }

    @Test
    public void testSyncList() {
        MyArrayList list = new MyArrayList(10);
        for (int i = 0; i < 6; ++i) {
            list.add(Integer.toString(i));
        }
        for(int i=0;i<list.length();++i){
            System.out.println(i+"="+list.get(i));
        }
        System.out.println("==================");
        Assert.assertEquals(6, list.length());
        System.out.println("length="+list.length());
        System.out.println("==================");
        list.remove(3);
        list.remove(2);
       for(int i=0;i<list.length();++i){
           System.out.println(i+"="+list.get(i));
       }
        System.out.println("length="+list.length());
        list.remove(4);
    }


    /**
     * 操作: add,remove,get
     */
    public static class MyArrayList {
        private String[] items;
        private int putIndex;

        public int length() {
            return putIndex;
        }

        private void checkIndex(int ix) {
            if (ix < 0) {
                throw new IllegalStateException("Index must > 0");
            }
            //这里容易错误写为items.length
            if (ix >= putIndex) {
                throw new IllegalStateException("Index out of array");
            }
        }

        public MyArrayList(int capacity) {
            items = new String[capacity];
        }

        public void add(String val) {
            if (putIndex >= items.length) {
                throw new IllegalStateException("More than array capacity");
            }
            items[putIndex] = val;
            ++putIndex;
        }

        public String get(int ix) {
            checkIndex(ix);
            return items[ix];
        }

        public boolean remove(String val) {
            boolean valIsNull = (val == null);
            for (int i = 0; i < items.length; ++i) {
                if (valIsNull) {
                    if (val == null) {
                        remove(i);
                        return true;
                    }
                } else {
                    if (val.equals(items[i])) {
                        remove(i);
                        return true;
                    }
                }
            }
            return false;
        }

        //如果我们需要将第 i 个位置的元素删除，那么这个位置就会产生空洞，从而造成数组内存空间不是连续的，所以要将第 i+1~n 这部元素顺序的前移一位。
        public void remove(int i) {
            checkIndex(i);
            //删除
            items[i] = null;
            --putIndex;
            //前移,这里容易错误写为items.length
            for (int k = i ; k < putIndex; ++k) {
                items[k] = items[k + 1];
            }
            items[putIndex] = null;
        }
    }
}
