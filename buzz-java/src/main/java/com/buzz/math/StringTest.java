package com.buzz.math;

import java.util.HashSet;
import java.util.Set;

public class StringTest {

	/**
	 * 
	 * 问题描述
	 *	<pre>	
	 * 	给出一串字符串，去掉重复的即可；
	 *  例：str = "abacdefabcde";
	 * 	去重后：str = "abcdef";
	 * </pre>
	 */
	static void trimString() {
		String str = "abacdefabcde";
		byte[] bucket = new byte[256];
		StringBuffer sb = new StringBuffer();
		for(int i=0;i<str.length();++i) {
			char c = str.charAt(i);
			byte ix = (byte) c;
			if(bucket[ix]==0) {
				sb.append(c);
				bucket[ix]=1;
			}
		}
		System.out.println(sb.toString());
		
		HashSet<Character> set = new HashSet<Character>();
		for(int i=0;i<str.length();++i) {
			set.add(str.charAt(i));
		}
		System.out.println(set);
	}
	/**
	 * 
	 * <pre>
	 * 找出一个没有重复的数
	 * 相同的数异或的结果是 0，一个数和 0 异或的结果是它本身
	 * 所以我们把这一组整型全部异或一下，
	 * 例如这组数据是：1，  2，  3，  4，  5，  1，  2，  3，  4。
	 * 其中 5 只出现了一次，其他都出现了两次，把他们全部异或一下，
	 * 结果如下：由于异或支持交换律和结合律，所以:1^2^3^4^5^1^2^3^4 = （1^1)^(2^2)^(3^3)^(4^4)^5= 0^0^0^0^5 = 5。
	 * 也就是说，那些出现了两次的数异或之后会变成0，那个出现一次的数，和 0 异或之后就等于它本身。
	 *
	 * </pre>
	 * http://www.voidcn.com/article/p-gycttqvr-zu.html
	 * https://www.zhihu.com/question/353155977/answer/878783236
	 * 
	 * @return
	 */
	static void findOnceNumer() {
		int[] arr = new int[] { 1, 2, 3, 4, 5, 6, 7, 3 };
		int tmp = arr[0];
		for (int i = 1; i < arr.length; i++) {
			tmp = tmp ^ arr[i];
		}

		System.out.println(tmp);

		int a = 123;
		int b = 222;
		System.out.println((a ^ b) ^ b);
		System.out.println((a ^ a) ^ a);
	}

	/**
	 * string 去重
	 */
	public void removeDuplicate() {
		String str = "aabbcc";

	}

	public int lengthOfLongestSubstring(String s) {
		int n = s.length();
		int ans = 0;
		for (int i = 0; i < n; i++)
			for (int j = i + 1; j <= n; j++)
				if (allUnique(s, i, j))
					ans = Math.max(ans, j - i);
		return ans;
	}

	public boolean allUnique(String s, int start, int end) {
		Set<Character> set = new HashSet<>();
		for (int i = start; i < end; i++) {
			Character ch = s.charAt(i);
			if (set.contains(ch))
				return false;
			set.add(ch);
		}
		return true;
	}

	public static void test() {
		String str = "abcd";
		int n = str.length();

		for (int i = 0; i < n; i++) {
			for (int j = i + 1; j <= n; j++) {
				System.out.print(i + "," + j + "|");
			}
			System.out.println();
			System.out.println("========");
		}
	}

	public static void main(String[] args) {

		trimString();

	}
}
