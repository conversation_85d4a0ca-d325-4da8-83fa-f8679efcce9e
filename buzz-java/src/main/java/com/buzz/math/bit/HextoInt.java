package com.buzz.math.bit;

import java.util.HashMap;

/**
 * 16 进制转 10 进制
 * <AUTHOR>
 * @description
 **/
public class HextoInt {

    private static HashMap<String, Integer> dict = new HashMap<>();

    static {
        dict.put("0", 0);
        dict.put("1", 1);
        dict.put("2", 2);
        dict.put("3", 3);
        dict.put("4", 4);
        dict.put("5", 5);
        dict.put("6", 6);
        dict.put("7", 7);
        dict.put("8", 8);
        dict.put("9", 9);
        dict.put("a", 10);
        dict.put("b", 11);
        dict.put("c", 12);
        dict.put("d", 13);
        dict.put("e", 14);
        dict.put("f", 15);
    }

    private static int hexToInt(String num) {
        if (num.length() == 1) {
            return dict.get(num);
        }
        int total = 0;
        //处理每一位
        for (int i = num.length() - 1, j = 0; i > 0; --i, j++) {
            int n = Integer.valueOf(String.valueOf(num.charAt(j)));
            total += n * Math.round(Math.pow(16, i));
        }
        //处理末尾一位
        total += dict.get(String.valueOf(num.charAt(num.length() - 1)));
        return total;
    }
    public static void main(String[] args) {
        System.out.println(hexToInt("65"));
        System.out.println(hexToInt("78"));
        System.out.println(hexToInt("69"));
        System.out.println(hexToInt("74"));
        System.out.println(hexToInt("d"));
        System.out.println(hexToInt("a"));
    }

}
