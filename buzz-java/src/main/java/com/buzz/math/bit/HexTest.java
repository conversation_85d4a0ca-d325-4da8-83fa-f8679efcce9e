package com.buzz.math.bit;

public class HexTest {

    public static String hexStr2Str(String hexStr) {
        String str = "0123456789ABCDEF";
        char[] hexs = hexStr.toCharArray();
        byte[] bytes = new byte[hexStr.length() / 2];
        int n;
        for (int i = 0; i < bytes.length; i++) {
            n = str.indexOf(hexs[2 * i]) * 16;
            n += str.indexOf(hexs[2 * i + 1]);
            bytes[i] = (byte)(n & 0xff);
        }
        return new String(bytes);
    }

    /**
     * <code>
     * 原理
     * 1. 先得到字符串的byte数组。
     * 2. 然后把每个byte切分为高位和低位两部分，每部分都是4位，4位的取值范围为0-15之间
     * 3. 得到高低位的索引之后再从chars中拿到具体值
     * </code>
     * @param str
     * @return
     */
    public static String str2HexStr(String str) {
        char[] chars = "0123456789ABCDEF".toCharArray();
        StringBuilder sb = new StringBuilder("");
        byte[] bs = str.getBytes();
        int bit;
        for (int i = 0; i < bs.length; i++) {
            //获取字符串中每个字节
            bit = (bs[i] & 0x0f0) >> 4; //截取字节的高位,即4到8位
            sb.append(chars[bit]);
            bit = bs[i] & 0x0f;//截取字节的低位,即0到4位
            sb.append(chars[bit]);
        }
        return sb.toString().trim();
    }

    public static void main(String[] args) {
        
        System.out.println((byte)('s'));
        Integer a = 0x0f;
        Integer b = 0x0f0;
        
        System.out.println(Integer.toBinaryString(115));
        System.out.println(115&a);
        System.out.println((115&b)>>4);
        
//        String str = str2HexStr("sanyun");
//        System.out.println("str2HexStr==>" + str);
//        System.out.println("hexStr2Str==>" + hexStr2Str(str));
    }
}
