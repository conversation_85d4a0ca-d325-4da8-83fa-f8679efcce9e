package com.buzz.math;

import org.junit.Test;

import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.zip.CRC32;

/**
 * md5简单实现
 * <AUTHOR>
 *
 */
public class Md5Test {




	@Test
	public void testMd5(){
		System.out.println(Codec.md5("chinese"));
		System.out.println(Codec.md5("chinese").substring(8, 24));
		System.out.println(Codec.getFixedLengthCRC("chinese"));

	}

	@Test
	public void testEncodeHex(){
		System.out.println(Codec.encodeHex("a".getBytes()));
		System.out.println(Codec.encodeHex("ab".getBytes()));
	}


	private static class Codec {

		//编码的数据，把byte转化为 这个数组中的元素
		private static char hexDigits[] = { '0', '1', '2', '3', '4', '5', '6', '7', '8', '9','a', 'b', 'c', 'd', 'e', 'f' };

		//md5加密数据128bit，16个byte
		public static String md5(String str) {
			byte[] data = str.getBytes();
			MessageDigest mdInst = null;
			try {
				mdInst = MessageDigest.getInstance("MD5");
			} catch (NoSuchAlgorithmException e) {
				e.printStackTrace();
			}

			//把byte数组编码成长度32的字符串
			return encodeHex(mdInst.digest(data));
		}


		public static String encodeHex(byte[] input )  {
			int len = input.length;
			final char[] out = new char[len*2];
			for (int i = 0, j = 0; i < len; i++) {
				byte byte0 = input[i];
				//把一个字节拆分为高4位和低4位，4位二进制的取值范围为0-15，所以可以通过数组下标表示
				out[j++] = hexDigits[(0xF0 & byte0) >>> 4];
				out[j++] = hexDigits[0x0F & byte0];
			}
			return new String(out);

		}

		public static String getFixedLengthCRC(Object obj) {
			if (obj == null || "".equals(obj)) {
				return "00000000";
			}
			CRC32 c = new CRC32();
			c.update(obj.toString().getBytes());
			String retVal = Long.toHexString(c.getValue());
			if (retVal.length() == 8) {
			} else if (retVal.length() > 8) {
				return retVal.substring(0, 8);
			} else {
				StringBuilder sb = new StringBuilder(8);
				for (int i = 0; i < 8 - retVal.length(); i++) {
					sb.append('0');
				}
				sb.append(retVal);
				return sb.toString();
			}
			return retVal;
		}
	}
}
