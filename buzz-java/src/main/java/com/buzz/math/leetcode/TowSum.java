package com.buzz.math.leetcode;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;

/**
 * <pre>
 * 给定一个整数数组 nums 和一个整数目标值 target，请你在该数组中找出 和为目标值 的那 两个 整数，并返回它们的数组下标。
 * </pre>
 * <code>
 * [2, 7, 11, 15]   22 
 * </code>
 * <AUTHOR>
 *
 */
public class TowSum {

	//暴力破解法
	public static int[] twoSum1(int[] nums, int target) {
		for (int i = 0; i < nums.length; ++i) {
			for (int j = i + 1; j < nums.length; ++j) {
				if (nums[i] + nums[j] == target) {
					return new int[] { i, j };
				}
			}
		}
		return new int[0];
	}

	//通过hash表
	public static int[] twoSum2(int[] nums, int target) {
		//以数字为key放入map,value是索引
		Map<Integer, Integer> hashtable = new HashMap<Integer, Integer>();
		for (int i = 0; i < nums.length; ++i) {
			if (hashtable.containsKey(target - nums[i])) {
				return new int[] { hashtable.get(target - nums[i]), i };
			}
			hashtable.put(nums[i], i);
			System.out.println(i + "\t" + hashtable);
		}
		return new int[0];
	}

	//排序+双指针
	public static int[] twoSum3(int[] nums, int target) {
		//sort
		Arrays.sort(nums);
		//double point
		for (int i = 0, j = nums.length - 1; j - i > 0;) {
			System.out.println(String.format("nums[%s] + nums[%s]=%s", i, j, nums[i] + nums[j]));
			//如果大于target，减少右边的指针
			if (nums[i] + nums[j] > target) {
				--j;
			//如果小于target，增加左边的指针
			} else if (nums[i] + nums[j] < target) {
				++i;
			} else {
				return new int[] { nums[i], nums[j] };

			}
		}
		return new int[0];
	}

	private static void sort(int[] nums) {
		for (int i = 0; i < nums.length - 1; ++i) {
			for (int j = 0; j < nums.length - 1 - i; ++j) {
				if (nums[j] > nums[j + 1]) {
					//switch it
					int temp = nums[j + 1];
					nums[j + 1] = nums[j];
					nums[j] = temp;
				}
			}
		}
	}

	public static void main(String[] args) {
		int[] ret = twoSum3(new int[] { 3, 2, 5, 4 }, 9);
		System.out.println(Arrays.toString(ret));
	}
}
