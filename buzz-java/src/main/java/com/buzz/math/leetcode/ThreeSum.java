package com.buzz.math.leetcode;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import com.google.common.collect.Lists;

/**
 * https://leetcode-cn.com/problems/3sum/solution/15-san-shu-zhi-he-ha-xi-fa-shuang-zhi-zhen-fa-xi-2/
 * 
 * <AUTHOR>
 *
 */
public class ThreeSum {

	public static List<List<Integer>> threeSum(int[] nums) {
		//必须先排序
		Arrays.sort(nums);
		System.out.println("nums=" + Arrays.toString(nums));
		List<List<Integer>> result = new ArrayList<List<Integer>>();
		for (int i = 0; i < nums.length; ++i) {
			// 排序之后如果第一个元素已经大于零，那么无论如何组合都不可能凑成三元组，直接返回结果就可以了
			if (nums[i] > 0) {
				return result;
			}
			//错误的去重，数字第一次出现就被去重了导致少执行了一次
//			if (i < nums.length-1 && nums[i] == nums[i + 1]) {
//				System.out.println(String.format("repeat [%s] [%s]", nums[i], nums[i + 1]));
//				continue;
//			}
			//正确的去重，去掉后面重复出现的值
			if (i > 0 && nums[i] == nums[i - 1]) {
				System.out.println(String.format("repeat i [%s] [%s]", nums[i], nums[i - 1]));
				continue;
			}

			int left = i + 1;
			int right = nums.length - 1;
			while (right > left) {
				System.out.println(
						String.format("i=%s,l=%s,r=%s ,ret=%s", i, left, right, nums[i] + nums[left] + nums[right]));
				if (nums[i] + nums[left] + nums[right] > 0) {
					right--;
				} else if (nums[i] + nums[left] + nums[right] < 0) {
					left++;
				} else {
					result.add(Lists.newArrayList(nums[i], nums[left], nums[right]));
					// 去除临近相同的元素
					while (right > left && nums[right] == nums[right - 1])
						right--;
					while (right > left && nums[left] == nums[left + 1])
						left++;
					// 找到答案时，双指针同时收缩
					right--;
					left++;
				}
			}
		}
		return result;
	}

	public static void main(String[] args) {
		//-1, 0, 1, 2, -1, -4
		//排序之后：[-4, -1, 0, 1, 2] 
		List<List<Integer>> ret = threeSum(new int[] { -1, 0, 1, 2, -1, -4 });
		System.out.println(ret);
	}
}
