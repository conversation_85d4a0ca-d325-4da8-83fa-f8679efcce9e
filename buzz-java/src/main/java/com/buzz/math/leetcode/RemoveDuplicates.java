package com.buzz.math.leetcode;

import java.util.Arrays;

import com.google.common.collect.Lists;

/**
 * Remove Duplicates
 * 
 * 时间复杂度：O(n) ，假设数组的长度是 n，那么 i 和 j 分别最多遍历 n 步。
 * 
 * <AUTHOR>
 *
 */
public class RemoveDuplicates {

	public static int removeDuplicates(Integer[] nums) {
		// 定义快慢j,i两个指针类型
		int slow = 0;
		for (int fast = 1; fast < nums.length; fast++) {
			//如果j不等于i，慢指针+1，慢指针对应的值赋值
			if (nums[fast] != nums[slow]) {
				slow++;
				nums[slow] = nums[fast];
				System.out.println(String.format("[%s]=[%s]", slow, fast));
			}
		}
		return slow + 1;
	}

	public static void main(String[] args) {
		//0, 1, 1, 1, 1, 2, 2, 3, 3, 4
		Integer[] nums = new Integer[] { 0, 0, 0, 1, 2, 3 };
		int ret = removeDuplicates(nums);
		System.out.println(Arrays.toString(Arrays.copyOfRange(nums, 0, ret)));
	}
}
