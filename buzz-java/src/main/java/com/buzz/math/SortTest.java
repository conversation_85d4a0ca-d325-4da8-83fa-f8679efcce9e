package com.buzz.math;

import org.junit.Test;

import java.util.Arrays;
import java.util.Random;

/**
 * <AUTHOR>
 * @description
 **/
public class SortTest {

    /**
     * https://www.geeksforgeeks.org/insertion-sort/
     * 插入排序
     */
    @Test
    public void testInsertionSort() {
        int[] nums = new int[]{3, 1, 4, 2, 10, 5};
        SortSolution.insertionSort(nums);
        System.out.println(Arrays.toString(nums));
    }

    /**
     * 来源RoaringBitmap
     */
    @Test
    public void testInsertionSort2() {
        OrderArray array = new OrderArray();
        Random random = new Random();
        for (int i = 0; i < 10; ++i) {
            int num = random.nextInt(1000);
            System.out.println(num);
            array.add(num);
        }
        System.out.println(Arrays.toString(array.get()));

    }

    @Test
    public void testBubbleSort() {
        int[] nums = new int[]{3, 1, 4, 2, 10, 5};
        SortSolution.bubbleSort(nums);
        System.out.println(Arrays.toString(nums));
    }

    @Test
    public void testTemp() {
        int[] nums = new int[]{ 5, 4, 1};
        for (int i = 0; i < nums.length; ++i) {
            for (int j = 0; j < nums.length - i - 1; ++j) {
                System.out.println(nums[j] + "," + nums[j + 1]);
            }
            System.out.println();
        }
    }

    private static class SortSolution {

        public static void insertionSort(int[] nums) {
            if (nums.length < 2)
                return;

            for (int i = 1; i < nums.length; ++i) {
                int value = nums[i];
                int j = i - 1;
                for (; j >= 0; --j) {
                    if (nums[j] > value) {
                        nums[j + 1] = nums[j];//交换位置
                    } else {
                        break; //找到位置可以跳出
                    }
                }
                nums[j + 1] = value;//插入数字
            }
        }

        //冒泡排序：大的数往右边移动
        //数组的末尾是有序的，所以嵌套的循环次数越来越少
        public static void bubbleSort(int[] nums) {
            if (nums.length < 2)
                return;

            for (int i = 0; i < nums.length; ++i) {
                for (int j = 0; j < nums.length - i - 1; ++j) {
                    if (nums[j] > nums[j + 1]) {
                        int temp = nums[j + 1];
                        nums[j + 1] = nums[j];
                        nums[j] = temp;
                    }
                }
            }
        }
    }

    //参考
    private class OrderArray {
        private static final int DEFAULT_SIZE = 4;
        public int[] items;
        private int size;

        public OrderArray() {
            this.items = new int[4];
        }

        /**
         * 该方法可以优化，拆分为两个字方法，一个方法负责查找待插入的offset，一个方法负责插入
         *
         * @param num
         */
        public void add(int num) {
            extendArray(1);
            if (size == 0) {
                items[size] = num;
            } else if (num > items[size - 1]) {//如果插入数据大于数组末尾则不需要比较
                items[size] = num;
            } else {
                for (int i = 0; i < size; ++i) {
                    if (num <= items[i]) {
                        System.arraycopy(items, i, items, i + 1, size - i);
                        items[i] = num;
                        break;
                    }
                }
            }
            ++size;
        }

        public int[] get() {
            return Arrays.copyOfRange(items, 0, size);
        }

        private void extendArray(int i) {
            if (items.length < size + i) {
                items = Arrays.copyOf(items, (size + i) * 2);
            }
        }
    }
}
