package com.buzz.math;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class MathTest {


    /**
     * 返回自然数底数e的参数次方
     * 自然常数，符号e,为数学中一个常数，是一个无限不循环小数，且为超越数，其值约为2.718281828459045。它是自然对数函数的底数。有时称它为欧拉数（Euler number）
     */
    @Test
    public void testEXP() {
        for (int i = 1; i < 10; ++i) {
            log.info("{} -> {}", i, Math.exp(i));
        }
    }


    @Test
    public void testLogN(){
        System.out.println(Math.log10(100));
        System.out.println(Math.log10(1_000));
        System.out.println(Math.log10(10_000));
        System.out.println();
        System.out.println(Math.log(100));
        System.out.println(Math.log(1_000));
        System.out.println(Math.log(10_000));
        System.out.println(Math.log(10_0000));

    }
}
