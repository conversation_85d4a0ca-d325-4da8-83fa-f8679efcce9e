package com.buzz.math;

import org.junit.Test;

import java.util.Random;

/**
 *
 * 控制范围的随机数，比如产生的50-100 的随机数
 * <AUTHOR>
 * @description
 **/
public class RangeRandomTest {


    @Test
    public void test(){
        //通过概率貌似并不靠谱
        for(int i=0;i<10;++i){
            System.out.println(RangeRandom.generateNumber());
        }
        //另外一种思路是采样，自增
    }

    /**
     * <pre>
     * 控制时间概率
     =======================
     50%,	30%,	  20%
     =======================
     0-50,	50-200, 200-1000
     * </pre>
     */
    private static class RangeRandom {

        private static Random rangeRandom = new Random();

        private static int geRangeRandom(int min, int max) {
            return min + rangeRandom.nextInt(max - min + 1);
        }

        private static long generateNumber() {
            double random = Math.random();
            //保留小数点 2 位
            random = (Math.round(random * 100) / 100.0);

            if (random < 0.5) {
                return geRangeRandom(0, 49);
            } else if (random < 0.8) {
                return geRangeRandom(50, 200);
            } else {
                return geRangeRandom(201, 1000);
            }
        }
    }
}
