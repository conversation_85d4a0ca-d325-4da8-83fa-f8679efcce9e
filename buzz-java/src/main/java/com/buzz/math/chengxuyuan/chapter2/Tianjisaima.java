package com.buzz.math.chengxuyuan.chapter2;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 田忌赛马
 * <AUTHOR>
 *
 */
public class Tianjisaima {

	List<String> qiwangHorses = Arrays.asList(new String[] { "q1", "q2", "q3" });
	Map<String, Float> qiwangHorsesTime = new HashMap<>();

	List<String> tianjiHorses = Arrays.asList(new String[] { "t1", "t2", "t3" });
	Map<String, Float> tianjiHorsesTime = new HashMap<>();

	public Tianjisaima() {
		qiwangHorsesTime.put("q1", 1.0f);
		qiwangHorsesTime.put("q2", 2.0f);
		qiwangHorsesTime.put("q3", 3.0f);

		tianjiHorsesTime.put("t1", 1.5f);
		tianjiHorsesTime.put("t2", 2.5f);
		tianjiHorsesTime.put("t3", 3.5f);
	}

	public void permutate(List<String> horses, List<String> selection) {
		if (horses.isEmpty()) {
			System.out.println(selection);
			compare(selection, qiwangHorses);
		}

		for (String each : horses) {
			List<String> newSelection = new ArrayList<String>(selection);
			newSelection.add(each);

			List<String> restHorses = new ArrayList<String>(horses);
			restHorses.remove(each);
			
			
			permutate(restHorses, newSelection);
		}

	}

	public void compare(List<String> t, List<String> q) {
		System.out.println("exeucte compare: " + t );
		//田忌获胜次数
		int t_won_cnt = 0;
		for (int i = 0; i < t.size(); ++i) {
			System.out.println(tianjiHorsesTime.get(t.get(i)) + " vs " + qiwangHorsesTime.get(q.get(i)));
			if (tianjiHorsesTime.get(t.get(i)) < qiwangHorsesTime.get(q.get(i))) {
				t_won_cnt++;
			}
		}

		if (t_won_cnt > t.size() / 2) {
			System.out.println("田忌获胜");
		} else {
			System.out.println("齐王获胜");
		}
	}

	public static void main(String[] args) {
		Tianjisaima p = new Tianjisaima();
		p.permutate(p.tianjiHorses, new ArrayList<String>());
	}
}
