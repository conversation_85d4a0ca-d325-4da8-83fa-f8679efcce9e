package com.buzz.math.chengxuyuan.chapter2;

import static org.junit.Assert.assertTrue;

import java.util.ArrayList;
import java.util.List;

import org.junit.Test;

import com.google.common.collect.Lists;

public class RecursionTest {

	/**
	 * 计算给定格子数的麦粒数
	 * @param gird
	 * @return
	 */
	public static int gridSum(int gird) {
		//边界条件
		if (gird == 1) {
			return 1;
		}
		//总数等于当前值 2^(n-1) + 前一格之和
		int sum = (1 << (gird - 1)) + gridSum(gird - 1);
		return sum;
	}
	/**
	 * 2的n次方
	 * @param n
	 * @return
	 */
	public int pow2n(int n) {
		if (n == 1) {
			return 2;
		}
		return 2 * pow2n(n - 1);
	}

	/**
	 * 全排列
	 */
	public void permutations(List<Integer> nums, List<Integer> selections) {
		//中止条件，nums无法再排列时，打印选择的selections
		if (nums.isEmpty()) {
			System.out.println(selections);
			return;
		}
		for (Integer each : nums) {
			List<Integer> copyNums = new ArrayList<Integer>(nums);
			copyNums.remove(each);
			List<Integer> copySelections = new ArrayList<Integer>(selections);
			copySelections.add(each);
			permutations(copyNums, copySelections);
		}
	}

	@Test
	public void testPow2n() {
		int ret = pow2n(10);
		assertTrue(ret == 1024);
	}

	@Test
	public void testPermutations() {
		permutations(Lists.newArrayList(1, 2, 3), new ArrayList<Integer>());
	}
}
