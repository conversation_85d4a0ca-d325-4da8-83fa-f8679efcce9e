package com.buzz.math.chengxuyuan.chapter2;

import java.util.ArrayList;
import java.util.List;

/**
 * 求给定数组的全排列
 * 
 * <AUTHOR>
 *
 */
public class Permutate {

	static List<List<Integer>> list = new ArrayList<List<Integer>>();

	public static List<List<Integer>> permute(int[] nums) {
		list.clear();
		List<Integer> sources = new ArrayList<>();
		for (int i = 0; i < nums.length; ++i) {
			sources.add(nums[i]);
		}
		doPermutate(sources, new ArrayList<Integer>());
		return list;
	}
	
	/**
	 * [2,3]   [1]
	 * [3]	   [1,2]
	 * ,	   [1,2,3] ok
	 * 2   	   [1,3]
	 * , 	   [1,3,2] ok
	 * @param sources
	 * @param selection
	 */
	public static void doPermutate(List<Integer> sources, List<Integer> selection) {

		if (sources.isEmpty()) {
			list.add(selection);
			//System.out.println(selection);
		}

		for (Integer each : sources) {
			// 从剩下的未出战马匹中，选择一匹，加入结果
			List<Integer> newSelection = new ArrayList<Integer>(selection);
			newSelection.add(each);
			//将已选择的马匹从未出战的列表中移出
			List<Integer> restSources = new ArrayList<Integer>(sources);
			restSources.remove(each);

			doPermutate(restSources, newSelection);
		}
	}

	public static void main(String[] args) {
		permute(new int[] { 0, 1 });
		System.out.println(Permutate.list);
	}
}
