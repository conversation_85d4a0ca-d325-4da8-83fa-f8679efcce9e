package com.buzz.math.chengxuyuan.chapter2;

import java.util.ArrayList;
import java.util.List;

import com.google.common.collect.Lists;

/**
 * 奖赏组合， 1,2,5,10 的面额，给定固定的金额，能组合出多少出方式
 * <AUTHOR>
 *
 */
public class RewardCombination {
	static int count;

	/**
	 * 
	 * @param rewards
	 * @param total_reward
	 * @param selection
	 */
	static void get_reward_combination(List<Integer> rewards, int total_reward, List<Integer> selection) {
		// 满足总奖赏额度的条件，输出选择的金额
		if (total_reward == 0) {
			System.out.println(selection);
			++count;
			return;
			//不满足总奖赏额度的条件，不输出	
		} else if (total_reward < 0) {
			return;
		} else {//尚未达到总奖赏额度
			for (Integer each : rewards) {
				List<Integer> newSelection = new ArrayList<Integer>(selection);
				newSelection.add(each);
				get_reward_combination(rewards, total_reward - each, newSelection);
			}
		}

	}

	public static void main(String[] args) {
		get_reward_combination(Lists.newArrayList(1, 2, 5, 10), 10, new ArrayList<Integer>());
		System.out.println(count);
	}
}
