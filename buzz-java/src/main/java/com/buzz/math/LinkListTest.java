package com.buzz.math;

import org.jetbrains.annotations.NotNull;
import org.junit.Test;

import java.util.Iterator;

/**
 * <AUTHOR>
 * @description
 **/
public class LinkListTest {


    @Test
    public void test() {
        MyLinkList<Integer> list = new MyLinkList<>();
        list.add(1);
        list.add(2);
        list.add(3);
        for (Integer i : list) {
            System.out.println(i);
        }
    }

    private static class MyLinkList<T> implements Iterable<T> {

        private Node<T> head;

        public void add(T value) {

            Node node = new Node(value);
            node.next = this.head;
            this.head = node;

        }


        @NotNull
        @Override
        public Iterator<T> iterator() {
            return new MyIterator(head);
        }

        private class MyIterator implements Iterator<T> {

            private Node<T> cursor;

            public MyIterator(Node<T> head) {
                cursor = new Node<>(null);
                cursor.next = head;
            }

            @Override
            public boolean hasNext() {
                return cursor.next != null;
            }

            @Override
            public T next() {
                Node<T> next = cursor.next;
                T value = next.value;
                cursor = next;
                return value;
            }
        }


        private static class Node<T> {
            T value;
            Node<T> next;

            public Node(T value) {
                this.value = value;
            }

            public Node(T value, Node<T> next) {
                this.value = value;
                this.next = next;
            }

            public Node copy() {
                return new Node<>(value, next);
            }
        }
    }
}
