package com.buzz.java.heap;

import com.buzz.util.TextTable;
import org.gridkit.jvmtool.heapdump.HeapHistogram;
import org.gridkit.jvmtool.heapdump.HeapHistogram.ClassRecord;
import org.gridkit.jvmtool.heapdump.StringCollector;
import org.junit.Before;
import org.junit.Test;
import org.netbeans.lib.profiler.heap.Heap;
import org.netbeans.lib.profiler.heap.HeapFactory;
import org.netbeans.lib.profiler.heap.JavaClass;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 代码分析 heap 文件 heap file analyzer
 * 参考：http://blog.ragozin.info/2015/02/programatic-heapdump-analysis.html
 * <AUTHOR>
 * @description
 **/
public class HeapFileTest {

    Heap heap;

    @Before
    public void initHeapDump() throws IOException {
        String dumppath = "/Users/<USER>/Downloads/heap.bin"; // path to dump file
        heap = HeapFactory.createFastHeap(new File(dumppath));
    }


    @Test
    public void getAllClass(){
//        HeapSummary heapSummary = heap.getSummary();
//        System.out.println(heapSummary);

        List<JavaClass>  javaClassList = heap.getAllClasses();
        System.out.println(javaClassList.size());


    }
    /** Reports retained size of string object in dump */
    @Test
    public void reportStrings() {
        StringCollector collector = new StringCollector();
        collector.collect(heap);
        System.out.println(collector);
    }

    /**
     * Create "jmap -histo" like class histogram from dump
     */
    @Test
    public void printHistogram() {
        StringCollector collector = new StringCollector();
        HeapHistogram histo = new HeapHistogram();
        collector.collect(heap, histo);
        List<ClassRecord> ht = new ArrayList<ClassRecord>(histo.getHisto());
        ht.add(collector.asClassRecord());
        Collections.sort(ht, HeapHistogram.BY_SIZE);
        TextTable tt = new TextTable();
        int n = 0;
        for(ClassRecord cr: ht.subList(0, 500)) {
            tt.addRow("" + (++n), " " + cr.getTotalSize(), " " + cr.getInstanceCount(), " " + cr.getClassName());
        }
        System.out.println(tt.formatTextTableUnbordered(1000));
    }
}
