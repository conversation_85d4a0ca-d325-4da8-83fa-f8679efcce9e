package com.buzz.java.security;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import javax.crypto.KeyGenerator;
import javax.crypto.Mac;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

import org.apache.commons.codec.binary.Base64;

import com.buzz.toolkit.RandomUtils;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MacTest {

	/**
	 * 加密
	 * @param plainText
	 * @param cipher
	 * @return
	 */
	public static String generateSign(String plainText, String cipher) {

		Mac mac;
		String algorithm = "hmacSha256";
		try {
			mac = Mac.getInstance(algorithm);
		} catch (NoSuchAlgorithmException e) {
			throw new RuntimeException(algorithm, e);
		}
		byte[] secretBytes = cipher.getBytes(StandardCharsets.UTF_8);
		try {
			mac.init(new SecretKeySpec(secretBytes, algorithm));
		} catch (InvalidKeyException e) {
			throw new RuntimeException("cipher : " + cipher, e);
		}
		byte[] signatureBytes = mac.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
		return Base64.encodeBase64URLSafeString(signatureBytes);
	}

	public static void main(String[] args) throws NoSuchAlgorithmException {
		String plainText = "helloworld";
		//盐可以使用简单随机字符串
		String appKey = RandomUtils.generateAppSecret();
		log.info("appKey={}", appKey);
		String sign = generateSign(plainText, appKey);
		log.info("sign={}", sign);

//		byte[] skey = key.getEncoded();
//		System.out.println(skey.length);
//		System.out.println(new String(Base64.encodeBase64(skey)));

//		System.out.println(new BigInteger(1, skey).toString(16));
	}
}
