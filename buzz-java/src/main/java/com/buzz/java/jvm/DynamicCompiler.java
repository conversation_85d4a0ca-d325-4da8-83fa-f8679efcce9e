package com.buzz.java.jvm;

import lombok.extern.slf4j.Slf4j;

import javax.tools.*;
import javax.tools.JavaCompiler.CompilationTask;
import javax.tools.JavaFileObject.Kind;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URI;
import java.util.*;


/**
 * 支持内存编译并执行
 * <p>
 * 参考：
 * - https://www.baeldung.com/java-string-compile-execute-code
 * - https://github.com/jOOQ/jOOR   参考它的封装方式
 * - https://commons.apache.org/proper/commons-jci
 */
@Slf4j
public class DynamicCompiler {

    private DiagnosticCollector<JavaFileObject> diagnosticCollector = new DiagnosticCollector<JavaFileObject>();

    private JavaCompiler compiler;

    private ExtendJavaFileManager extendJavaFileManager;

    private DynamicClassloader dynamicClassloader;

    public DynamicCompiler(ClassLoader classLoader) {
        compiler = ToolProvider.getSystemJavaCompiler();
        StandardJavaFileManager fileManager = compiler.getStandardFileManager(diagnosticCollector, null, null);
        dynamicClassloader = new DynamicClassloader(classLoader);
        extendJavaFileManager = new ExtendJavaFileManager(fileManager, dynamicClassloader);
    }

    public static DynamicCompiler create(ClassLoader classLoader) {
        return new DynamicCompiler(classLoader);
    }

    //编译
    public Class<?> compile(String className, String sourceCode) {

        JavaFileObject javaFileObject = new SourceCodeObject(className, sourceCode);

        List<String> options = Arrays.asList("-Xlint:unchecked");
        CompilationTask task = compiler.getTask(
                null,
                extendJavaFileManager,
                diagnosticCollector,
                options,
                null,
                Arrays.asList(javaFileObject)
        );

        if (task.call()) {
            System.out.println("compile ok");
        } else {
            System.out.println("compile failed");
            return null;
        }

        try {
            return dynamicClassloader.loadClass(className);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public static class ExtendJavaFileManager extends ForwardingJavaFileManager<JavaFileManager> {

        private DynamicClassloader classloader;

        protected ExtendJavaFileManager(JavaFileManager fileManager, DynamicClassloader classloader) {
            super(fileManager);
            this.classloader = classloader;
        }

        //覆写输入方法
        @Override
        public FileObject getFileForInput(Location location, String packageName, String relativeName) throws IOException {
            log.info("getFileForInput {} {} {}", location, packageName, relativeName);
            return super.getFileForInput(location, packageName, relativeName);
        }

        //覆写输出方法
        @Override
        public JavaFileObject getJavaFileForOutput(Location location,
                                                   String className,
                                                   Kind kind,
                                                   FileObject sibling) throws IOException {
            log.info("getJavaFileForOutput {} {} {}", location, className, kind);
            BytecodeObject bytecodeObject = new BytecodeObject(className);
            classloader.put(className, bytecodeObject);
            return bytecodeObject;
        }

        @Override
        public Iterable<JavaFileObject> list(Location location,
                                             String packageName,
                                             Set<Kind> kinds,
                                             boolean recurse) throws IOException {
            log.info("list {} {}", location, packageName);
            return fileManager.list(location, packageName, kinds, recurse);
        }
    }

    //对源文件的抽象
    public static class SourceCodeObject extends SimpleJavaFileObject {

        private String content;

        protected SourceCodeObject(String className, String content) {
            super(URI.create("string:///" + className.replace('.', '/') + Kind.SOURCE.extension), Kind.SOURCE);
            this.content = content;
        }

        @Override
        public CharSequence getCharContent(boolean ignoreEncodingErrors) throws IOException {
            return content;
        }
    }

    //对class文件的抽象
    public static class BytecodeObject extends SimpleJavaFileObject {

        private ByteArrayOutputStream bytecode;

        protected BytecodeObject(String className) {
            super(URI.create("string:///" + className.replace('.', '/') + Kind.CLASS.extension), Kind.CLASS);
        }

        @Override
        public OutputStream openOutputStream() throws IOException {
            bytecode = new ByteArrayOutputStream();
            return bytecode;
        }

        public byte[] getByteCode() {
            return bytecode.toByteArray();
        }
    }


    //自定义classloader, 从本地map中读取编译好的字节码
    private static class DynamicClassloader extends ClassLoader {

        private Map<String, BytecodeObject> bytecodeMap = new HashMap<>();

        private ClassLoader parent;

        public DynamicClassloader(ClassLoader parent) {
            super(null);
            this.parent = parent;
        }

        @Override
        public Class<?> loadClass(String name) throws ClassNotFoundException {
            BytecodeObject byteCodeObject = bytecodeMap.get(name);
            if (byteCodeObject != null) {
                return this.defineClass(name, byteCodeObject.getByteCode(), 0, byteCodeObject.getByteCode().length);
            }
            return parent.loadClass(name);
        }

        public void put(String name, BytecodeObject byteCodeObject) {
            bytecodeMap.put(name, byteCodeObject);
        }
    }
}
