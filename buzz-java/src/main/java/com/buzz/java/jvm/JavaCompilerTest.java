package com.buzz.java.jvm;

import com.buzz.util.IOUtils;
import org.junit.Test;

import javax.tools.*;
import javax.tools.JavaCompiler.CompilationTask;
import java.io.File;
import java.io.IOException;
import java.util.Arrays;
import java.util.Locale;

public class JavaCompilerTest {

    String sourceFilePath = "/work/dist/branch/wacai/middleware/my-boot/buzz-java/src/main/java/com/buzz/java/jvm/ObjectSizeTest.java";

    @Test
    public void test101() {
        // 获取Java编译器
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        if (compiler == null) {
            System.out.println("Java Compiler is not available. Make sure you are using JDK.");
            return;
        }

        int compilationResult = compiler.run(null, null, null, sourceFilePath);
        // 处理编译结果
        if (compilationResult == 0) {
            System.out.println("Compilation is successful");
        } else {
            System.out.println("Compilation failed");
        }
    }

    //配置禁止告警、自定义输出class文件地址
    @Test
    public void test102() throws IOException {
        String outputDirectory = "/tmp";
        JavaCompiler compiler = ToolProvider.getSystemJavaCompiler();
        //自定义告警信息输出
        DiagnosticListener diagnosticListener = new MyDiagnosticListener();
        StandardJavaFileManager fileManager = compiler.getStandardFileManager(null, null, null);

        // 设置输出目录
        Iterable<File> outputDirs = Arrays.asList(new File(outputDirectory));
        fileManager.setLocation(StandardLocation.CLASS_OUTPUT, outputDirs);

        // 编译选项
        Iterable<String> options = Arrays.asList("-Xlint:unchecked","-Xlint:deprecation");

        // 创建编译任务
        DiagnosticCollector<JavaFileObject> collector = new DiagnosticCollector<JavaFileObject>();
        CompilationTask compilationTask = compiler.getTask(null,
                fileManager,
                collector, //也可以通过diagnosticListener
                options,
                null,
                fileManager.getJavaFileObjectsFromStrings(Arrays.asList(sourceFilePath)));

        // 执行编译任务
        boolean compilationResult = compilationTask.call();

        // 处理编译结果
        if (compilationResult) {
            System.out.println("Compilation is successful");
        } else {
            System.out.println("Compilation failed");
        }
        System.out.println("====================");
        //查看告警/错误信息
        for (Diagnostic<? extends JavaFileObject> diagnostic : collector.getDiagnostics()) {
            System.out.println(diagnostic.getKind() + ": "+ diagnostic.getColumnNumber() + ", " + diagnostic.getMessage(Locale.ENGLISH));
        }

        // 关闭文件管理器
        try {
            fileManager.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    /**
     * 一个完善的DynamicCompiler需要考虑点：
     * 1. 自定义输入、输出，源文件可能是字符串代码
     * 2. 编译选项、告警错误提示
     * 3. 编译完成之后如何loadClass
     * 参考 dubbo JdkCompiler
     * https://www.baeldung.com/java-string-compile-execute-code
     */
    @Test
    public void test103() throws IOException, IllegalAccessException, InstantiationException {
        DynamicCompiler dynamicCompiler = DynamicCompiler.create(ClassLoader.getSystemClassLoader());
        Class<?> target = dynamicCompiler.compile("com.buzz.java.jvm.ObjectSizeTest", IOUtils.readFile(sourceFilePath));
        System.out.println(target.newInstance());
        System.out.println(target.getClassLoader());
    }

    private static class MyDiagnosticListener implements DiagnosticListener<JavaFileObject> {

        @Override
        public void report(Diagnostic diagnostic) {
            System.out.println(diagnostic.getKind() + "," + diagnostic.getColumnNumber() + "," + diagnostic.getMessage(Locale.ENGLISH));

        }
    }

}
