package com.buzz.java.collection;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;

import java.io.IOException;
import java.net.URL;
import java.util.Collections;
import java.util.Enumeration;
import java.util.List;

import org.junit.Test;

import lombok.extern.slf4j.Slf4j;

/**
 *
 * <AUTHOR>
 *
 */
@Slf4j
public class CollectionsTest {

	/**
	 *  对于 Enumeration 的使用要注意,Collections.list(urls)之后会改变Enumeration
	 *  
	 * @throws IOException
	 */
	@Test
	public void testEnumeration() throws IOException {
		log.info("测试 Enumeration");
		Enumeration<URL> urls = CollectionsTest.class.getClassLoader().getResources("test.vm");
		assertTrue(urls.hasMoreElements());
		urls.nextElement();
		assertFalse(urls.hasMoreElements());

		urls = CollectionsTest.class.getClassLoader().getResources("test.vm");
		List<URL> urlList = Collections.list(urls);
		assertTrue(!urlList.isEmpty());
		//urls被改变了
		assertFalse(urls.hasMoreElements());
	}
}
