package com.buzz.java;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class IntegerTest {

    private int newSize(int size) {
        //numberOfLeadingZeros返回二进制前面0的位数
        return 1 << (32 - Integer.numberOfLeadingZeros(size - 1));
    }

    @Test
    public void testNumberOfLeadingZeros() {
        //把size转换符合为2的n次方大小

        assertEquals(16, newSize(10));
        assertEquals(64, newSize(64));

        for (int i = 1; i < 50; i = i + 5) {
            int val = 32 - Integer.numberOfLeadingZeros(i);
            System.out.println(i + "\t" + Integer.toBinaryString(i) + "\t" + val + "\t" + (1 << val));
        }
    }


}
