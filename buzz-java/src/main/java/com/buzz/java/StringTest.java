package com.buzz.java;

public class StringTest {


    public static void main(String[] args) {
//        String a = "Data-" + new String("email");
//        a = a.intern();
//        String b = "Data-" + new String("email");
//        b = b.intern();
//
//        System.out.println(a==b);
//
//        String e = "Data-"+"email";
//        String f = "Data-"+"email";
//        System.out.println(e==f);


        String a1 = new String("a");
        String a2 = new String("a");
        System.out.println(a1==a2);//false

        String b1 = new String("b").intern();
        String b2 = new String("b").intern();
        System.out.println(b1==b2);//true
    }
}
