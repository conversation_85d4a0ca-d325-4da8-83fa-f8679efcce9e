package com.buzz.java.annotation;

import org.junit.Test;

import java.lang.annotation.Annotation;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;

public class AnnotationTest {

    @Test
    public void test() {
        System.out.println(Boo.class.getAnnotations().length);
        for (Annotation annotation : Boo.class.getAnnotations()) {
            System.out.println(annotation);
        }

    }

    @Retention(RetentionPolicy.CLASS)
    public @interface Metadata {
        String metadata();
    }

    @Metadata(metadata = "this is test")
    public class Boo {

    }
}
