package com.buzz.java.runtime;

/**
 * 用完即删的测试
 * 
 * <AUTHOR>
 *
 */

public class TmpTest {

	public static final int _1MB = 1 << 20;

	public static byte[] flags = new byte[512 * _1MB];

	/**
	 * https://blog.csdn.net/zdxiq000/article/details/57626464
	 * 
	 * @param num
	 */
	public static void setFlag(int num) {
		// 使用每个数的低三位作为byte内的映射
		// 例如: 255 = (11111111)
		// 低三位(也就是num & (0x07))为(111) = 7, 则byte的第7位为1, 表示255已存在
		flags[num >> 3] |= 1 << (num & (7));
	}

	public void testFlag() {
		int FLAG_A = 1; // 0001
		int FLAG_B = 1 << 1;// 0010
		int FLAG_C = 1 << 2;// 0100
		int FLAG_D = 1 << 3;// 1000

		int a = 11;
		System.out.println(a & FLAG_A);
		System.out.println(a & FLAG_B);
		System.out.println(a & FLAG_C);
		System.out.println(a & FLAG_D);

		int x = FLAG_A | FLAG_B;
		System.out.println(4 & x);
	}

	public static void test0xFF() {
	
		
//		byte[] a = new byte[10];
//		a[0] = -127;
//		a[1] = -128;
//		System.out.println(a[0]);
//		// 0xFF的二进制表示就是：1111 1111。 高24位补0：0000 0000 0000 0000 0000 0000 1111 1111;
//		int c = (a[0] & 0xFF);//10000001&11111111
//		System.out.println(c);
		System.out.println(Integer.toBinaryString(118));
		System.out.println(Integer.toBinaryString(-118));

		System.out.println("========");

		// int b2 = ((b1[0] & 0xff) << 24) | ((b1[1] & 0xff) << 16) | ((b1[2] & 0xff) <<
		// 8) | (b1[3] & 0xff);

		int a = 1234567890;
		byte[] b = new byte[4];
		b[0] = (byte) ((a >> 24) & 0xff);
		b[1] = (byte) ((a >> 16) & 0xff);
		b[2] = (byte) ((a >> 8) & 0xff);
		b[3] = (byte) (a & 0xff);

		for (byte x : b) {
			System.out.print(x + ",");
		}
		System.out.println("========");

		System.out.println((byte)(b[3]& 0xff));
		
		int ret = ((b[0]& 0xff) << 24) | ((b[1]& 0xff) << 16) | ((b[2]& 0xff) << 8) | (b[3]& 0xff);
		System.out.println(ret);
		
		
	}

	public static void main(String[] args) throws InterruptedException, InstantiationException {
		byte b = -40;
		int a = b & 0xFF;
		System.out.println(Integer.toBinaryString(216));
		System.out.println(Integer.toBinaryString(-40));
		
//		 System.out.println(Integer.toBinaryString((1<<4)-1));
//		System.out.println(Integer.toBinaryString(16-4));
//		System.out.println(Integer.toBinaryString(-4));
//		System.out.println(2*2*2*2);
//		System.out.println((1<<3)-1);
//		System.out.println(1l<<31);
//		System.out.println(Integer.MAX_VALUE);
//		System.out.println(Integer.toHexString(Integer.MAX_VALUE));
//		int a = 2;
//		int b = 3;
//		System.out.println(a^b);
//		a = a ^ b;
//		System.out.println(a);
//		b = b ^ a;
//		System.out.println(b);
//		System.out.println(a^b^b);
//		a = a^b;
//		b = a^b^b;
//		System.out.println(b);
//		a = b^a;
//		System.out.println(a);

//		int i = 4;
//		int a = (1 << i) + 1;
//		System.out.println(a);

//		System.out.println(Integer.MAX_VALUE);
//		System.out.println((1 << 31) - 1);

//		System.out.println(0x07);
//		System.out.println(Integer.toBinaryString(-15));
//		System.out.println(Integer.toBinaryString(-127));
//		
//		byte b = -8;
//		System.out.println(Byte.toUnsignedInt(b));
//		System.out.println(Integer.toBinaryString(248));
//	
//		int b = 0xb1;
//		System.out.println(b);
//		BitSet bs = new BitSet();
//		bs.set(1000);

//		ByteBuffer buffer = ByteBuffer.allocateDirect(4 * 1024 * 1024);
//		long addresses = ((DirectBuffer) buffer).address();
//		
//		Unsafe unsafe = Unsafe.getUnsafe();
//		TmpTest test = (TmpTest) unsafe.allocateInstance(TmpTest.class);
//		System.out.println(test.hashCode());
//		
//		List<Thread> threads = new ArrayList<Thread>();
//		for (int i = 0; i < 10; ++i) {
//			Thread t = new Thread(new Runnable() {
//				@Override
//				public void run() {
//					System.out.println(Thread.currentThread().getId() + "\t" + NativeThread.current());
//				}
//			});
//			t.start();
//		}
//
//		for (Thread t : threads) {
//			t.join();
//		}

	}
}
