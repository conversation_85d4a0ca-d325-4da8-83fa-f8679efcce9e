package com.buzz.java.runtime;

import java.io.IOException;

public class ShutdownTest {

	public static void shutdown() {
		System.out.println("shutdowning...");
		System.exit(-1);
	}

	public static void main(String[] args) throws IOException {

		Runtime.getRuntime().addShutdownHook(new Thread() {
			public void run() {
				Thread thread = Thread.currentThread();
				for (StackTraceElement element : thread.getStackTrace()) {
					StringBuffer sb = new StringBuffer();
					sb.append(element.getClassName());
					sb.append(".");
					sb.append(element.getMethodName());
					sb.append("()");
					// sb.append(element.getLineNumber());
					System.out.println("\t" + sb.toString());
				}
			}
		});
		System.out.println("ready");
		System.in.read();
		System.out.println("ok");
		new Thread(new Runnable() {

			@Override
			public void run() {
				for (int i = 0; i < 3; ++i) {
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
					}
					System.out.println("waiting..");
				}
				shutdown();
			}
		}).start();
	}
}
