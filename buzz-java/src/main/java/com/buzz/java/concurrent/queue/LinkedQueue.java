package com.buzz.java.concurrent.queue;

import lombok.extern.slf4j.Slf4j;

/**
 * 基于链表的队列
 * @param <E>
 */
@Slf4j
public class LinkedQueue<E> {

    // Head and last pointers
    transient Node<E> head;
    private transient Node<E> last;

    public LinkedQueue() {
        last = head = new Node<E>(null);
    }

    public boolean offer(E e) {
        Node<E> node = new Node<E>(e);
        enqueue(node);
        return true;
    }

    public E poll() {
        return dequeue();
    }

    private void enqueue(Node<E> node) {
        last = last.next = node;
        log.info("enqueue last={}, last.next={}", last, last.next);
    }

    private E dequeue() {
        Node<E> h = head; //head永远是null节点
        Node<E> first = h.next; //第一个节点
        h.next = h; // help GC
        head = first;       //把第一个节点设为head节点
        E x = first.item;   //第一个节点的值
        first.item = null;  //把head节点设为null
        return x;
    }

    static class Node<E> {
        E item;
        Node<E> next;

        Node(E x) {
            item = x;
        }

        @Override
        public String toString() {
            return item == null ? "null" : item.toString();
        }
    }
}
