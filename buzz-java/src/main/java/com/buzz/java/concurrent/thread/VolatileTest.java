package com.buzz.java.concurrent.thread;

import org.junit.Test;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicBoolean;

public class VolatileTest {


    /**
     * volatile 无法保证原子性，会打印多条init
     */
    @Test
    public void test1() {
        ExecutorService es = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 10; ++i) {
            es.submit(() -> {
                Task1.execute();
            });
        }
    }


    @Test
    public void test2() {
        ExecutorService es = Executors.newFixedThreadPool(10);
        for (int i = 0; i < 10; ++i) {
            es.submit(() -> {
                Task2.execute();
            });
        }
    }

    public static class Task1 {
        public volatile static boolean initFlag = false;

        public static void execute() {
            if (!initFlag) {
                System.out.println("init");
            }
            initFlag = true;
        }
    }

    public static class Task2 {

        public static AtomicBoolean initFlag = new AtomicBoolean();

        public static void execute() {
            if (initFlag.compareAndSet(false,true)) {
                System.out.println("init");
            }
        }
    }
}
