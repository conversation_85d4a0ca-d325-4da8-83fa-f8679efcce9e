package com.buzz.java.concurrent.delay;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

public class DelayedTaskExecutor {
    private DelayQueue<DelayedTask> delayQueue = new DelayQueue<>();
    private int numThreads;

    public DelayedTaskExecutor(int numThreads) {
        this.numThreads = numThreads;
    }

    public void executeDelayedTask(Runnable task, long delayTime) {
        DelayedTask delayedTask = new DelayedTask(task, delayTime);
        delayQueue.offer(delayedTask);
    }

    public void executeDelayedTask(DelayedTask delayedTask) {
        delayQueue.offer(delayedTask);
    }

    public void start() {
        for (int i = 0; i < numThreads; i++) {
            Thread workerThread = new Thread(() -> {
                while (true) {
                    try {
                        DelayedTask delayedTask = delayQueue.take();
                        delayedTask.execute();
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            });
            workerThread.start();
        }
    }

    public static class DelayedTask implements Delayed {
        private Runnable task;
        private long executionTime;

        public DelayedTask(Runnable task, long delayTime) {
            this.task = task;
            this.executionTime = System.currentTimeMillis() + delayTime;
        }

        /**
         *
         * 返回需要延迟的时间，时间需要通过unit进行格式化
         *
         * 该方法被 DelayQueue.take()方法调用
         * 如果返回 0 或者负数说明任务到期
         *
         * @param unit
         * @return
         */
        @Override
        public long getDelay(TimeUnit unit) {
            long delay = executionTime - System.currentTimeMillis();
            return unit.convert(delay, TimeUnit.MILLISECONDS);
        }

        /**
         * DelayQueue 需要通过expiration时间排序，确保最早过期的在队列的头部
         *
         * @param o
         * @return
         */
        @Override
        public int compareTo(Delayed o) {
            long diff = this.getDelay(TimeUnit.MILLISECONDS) - o.getDelay(TimeUnit.MILLISECONDS);
            return Long.compare(diff, 0);
        }

        public void execute() {
            task.run();
        }
    }
}
