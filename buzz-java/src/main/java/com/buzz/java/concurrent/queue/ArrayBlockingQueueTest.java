package com.buzz.java.concurrent.queue;

import org.junit.Test;

import java.util.concurrent.ArrayBlockingQueue;

/**
 * <AUTHOR>
 * @description
 **/
public class ArrayBlockingQueueTest {


    @Test
    public void test101() {
        ArrayQueue<Integer> queue = new ArrayQueue<>(3);
        queue.offer(1);
        queue.offer(2);
        queue.offer(3);

        System.out.println(queue.poll());
        System.out.println(queue.poll());
        System.out.println(queue.poll());

        queue.offer(4);
    }

    /**
     * 入队的时候，如果takeIndex达到数组长度会重置为0，详见enqueue()
     *
     * @throws InterruptedException
     */
    @Test
    public void test() throws InterruptedException {
        ArrayBlockingQueue queue = new ArrayBlockingQueue(2);
        queue.add("a");
        queue.add("b");

        queue.take();
        queue.take();
        queue.add(1);
    }


}
