package com.buzz.java.concurrent.delay;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;

@Slf4j
public class DelayedTaskTest {

    private static long elapsed(long time) {
        return System.currentTimeMillis() - time;
    }

    @Test
    public void test() throws InterruptedException, IOException {
        DelayedTaskExecutor executor = new DelayedTaskExecutor(1);
        executor.start();

        final long begin = System.currentTimeMillis();
        // 添加延迟任务
        executor.executeDelayedTask(() -> System.out.println("Task 1 executed " + (System.currentTimeMillis() - begin)),
                30000);
        executor.executeDelayedTask(() -> System.out.println("Task 2 executed " + (System.currentTimeMillis() - begin)),
                50000);
        executor.executeDelayedTask(() -> System.out.println("Task 3 executed " + (System.currentTimeMillis() - begin)),
                300_000);

        System.in.read();

    }

    @Test
    public void test2() throws InterruptedException, IOException {

        DelayedTaskExecutor executor = new DelayedTaskExecutor(1);
        executor.start();

        final long begin = System.currentTimeMillis();
        DelayedTaskExecutor.DelayedTask delayedTask = new DelayedTaskExecutor.DelayedTask(
                () -> System.out.println("Task 1 executed " +elapsed(begin)),
                25000);
        // 添加延迟任务
        executor.executeDelayedTask(delayedTask);
        Thread.sleep(500);
        System.out.println("try wakeup");
        Thread.sleep(3000);

        System.out.println("end");

    }


    @Test
    public void test3() throws InterruptedException {
        Thread.sleep(500);

        Long begin = System.currentTimeMillis();
        Timers.DelayedTask delayedTask = new Timers.DelayedTask(25000, () -> {
            log.info("Task 1 executed " + elapsed(begin));
        });
        Timers.newTimeout(delayedTask);

        Thread.sleep(500);
        log.info("try wakeup");
        delayedTask.wakeup();

        Thread.sleep(3000);

        System.out.println("end");
    }


}
