package com.buzz.java.concurrent;

import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

public class SemaphoreTest {

	private static Random rd = new Random();

	public static void doSomething() {
		System.out.println("doSomething");
		try {
			Thread.sleep(rd.nextInt(100));
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
	}

	public static void main(String[] args) throws InterruptedException {
		Semaphore semaphore = new Semaphore(10);
		ExecutorService executor = Executors.newFixedThreadPool(30);
		for (int i = 0; i < Integer.MAX_VALUE; i++) {
			executor.submit(new Runnable() {
				@Override
				public void run() {
					semaphore.acquireUninterruptibly(1);
					try {
						doSomething();
					} finally {
						semaphore.release();
					}
				}
			});
//			Thread.sleep(50);
		}
	}
}
