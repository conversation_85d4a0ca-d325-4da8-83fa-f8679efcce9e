package com.buzz.java.concurrent.executor;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.ReentrantLock;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Slf4j
public class ScheduledExecutorTest {

    @Test
    public void testOOM() throws InterruptedException {
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(10);
        AtomicInteger counter = new AtomicInteger();
        while (true) {
            executor.execute(() -> {
                try {
                    Thread.sleep(3000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                System.out
                        .println(executor.getQueue().size() + "\t" + counter.get() + "\t" + executor.getActiveCount());
            });
            Thread.sleep(100);
            counter.incrementAndGet();
        }

    }

    /**
     * hermes proxy 需要实现http long pooling ，服务端维护一个ScheduledThreadPoolExecutor来实现轮训，这里是一个原型测试
     *
     * @throws InterruptedException
     * @throws IOException
     */
    @Test
    public void testHermesFetcher() throws InterruptedException, IOException {
        ScheduledThreadPoolExecutor executor = (ScheduledThreadPoolExecutor) Executors.newScheduledThreadPool(10);
        CountDownLatch latch = new CountDownLatch(2);
        List<FetchTask> fetchTaskList = IntStream.range(0, 2).mapToObj(i -> {
            return new FetchTask(i, latch, executor);
        }).collect(Collectors.toList());

        for (FetchTask fetchTask : fetchTaskList) {
            System.out.println("提交任务：" + fetchTask);
            executor.submit(fetchTask);
        }
        latch.await();

        System.out.println("========summary============");
        fetchTaskList.forEach(System.out::println);

    }

    @Data
    public static class FetchTask implements Runnable {

        private String topic;
        private final long startTime;
        private long projectTime;
        private final int timeout = 5000;
        private int tryCount;
        private int latency;
        private boolean done;
        private CountDownLatch latch;
        private ScheduledThreadPoolExecutor executor;
        private ReentrantLock lock = new ReentrantLock();
        private ScheduledFuture future;

        public FetchTask(int i, CountDownLatch latch, ScheduledThreadPoolExecutor executor) {
            this.topic = "bairen.test." + i;
            this.startTime = System.currentTimeMillis();
            this.projectTime = startTime;
            this.latch = latch;
            this.executor = executor;
        }

        private void send(String type) {
            log.info("{} {}", type, this);
            done = true;
            latch.countDown();
        }

        @Override
        public void run() {
            int latency = (int) (System.currentTimeMillis() - projectTime) / 1000;
            if (latency > 0) {
                this.latency = Math.max(this.latency, latency);
                System.err.println(topic + " 延迟：" + (System.currentTimeMillis() - projectTime) / 1000 + "秒");
            }
            if (timeout()) {
                send("timeout");
                return;
            }

            if (fetch()) {
                //send("有数据，发送响应" + topic + ", 时间:" + (System.currentTimeMillis() - startTime) / 1000 + "秒前");
                send("response");
                return;
            }

            if (timeout()) {
                //send("已超时，发送响应" + topic + ", 时间:" + (System.currentTimeMillis() - startTime) / 1000 + "秒前");
                send("timeout");
                // lock.unlock();
                return;
            }

            projectTime = System.currentTimeMillis() + 200;

            future = executor.schedule(this, 500, TimeUnit.MILLISECONDS);
            // lock.unlock();
        }

        protected boolean fetch() {
            log.info("执行 fetch " + topic);
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            ++tryCount;
            return false;
        }

        public boolean timeout() {
            long elapsed = System.currentTimeMillis() - startTime;
            return elapsed > timeout;
        }

        @Override
        public String toString() {
            return "topic=" + topic + "\t tryCount=" + tryCount + "\t latency=" + latency;
        }
    }


    @Test
    public void testCancelTask() throws InterruptedException {
        ScheduledThreadPoolExecutor executor = (ScheduledThreadPoolExecutor) Executors.newScheduledThreadPool(10);
        CountDownLatch latch = new CountDownLatch(1);
        FetchTask fetchTask = new FetchTask(0, latch, executor);
        executor.submit(fetchTask);
        log.info("提交任务：" + fetchTask);
        Thread.sleep(1000);
        System.out.println("cancel " + fetchTask.getFuture().cancel(false));

        fetchTask = new FetchTask(1, latch, executor);
        log.info("提交任务：" + fetchTask);
        executor.submit(fetchTask);
        latch.await();
        System.out.println("========summary============");
        //fetchTaskList.forEach(System.out::println);
    }
}
