package com.buzz.java.concurrent;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @description
 * @date 2022-07-25 14:25
 **/
public class LocalCache {

    private ConcurrentHashMap<String, Object> map = new ConcurrentHashMap<String, Object>();

    public <R> R get(String key, Function<String, R> loader) {

        R data = (R) map.get(key);
        if (data != null) {
            return data;
        } else {
            //加锁避免缓存被击穿
            synchronized (key) {
                data = (R) map.get(key);
                if (data == null) {
                    data = loader.apply(key);
                    if (data != null) {
                        map.put(key, data);
                    }
                }
            }
        }
        return data;
    }

    @Slf4j
    @Data
    public static class DataObject {

        private final String data;

        public static int objectCounter = 0;

        public static DataObject get(String data) {
            log.info("get {}", data);
            objectCounter++;
            return new DataObject(data + new Date());
        }
    }

}

