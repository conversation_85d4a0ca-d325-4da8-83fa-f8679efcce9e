package com.buzz.java.concurrent;

import org.junit.Test;

import java.util.stream.IntStream;

public class FalseSharingTest {



    void run(ShareVo vo) throws InterruptedException {
        Thread thread1 = new Thread(()->{
            IntStream.range(0,50000000).forEach((i)->vo.a++);
        });

        Thread thread2 = new Thread(()->{
            IntStream.range(0,50000000).forEach((i)->vo.b++);
        });

        long start = System.currentTimeMillis();
        thread1.start();
        thread2.start();
        thread1.join();
        thread2.join();
        long end = System.currentTimeMillis();
        System.out.println(end-start);
    }
    @Test
    public void test() throws InterruptedException {
        ShareVo shareVo = new ShareVo();
        FalseShareVo falseShareVo = new FalseShareVo();
        run(shareVo);

        run(shareVo);
        run(falseShareVo);


    }

    static class ShareVo{
        volatile long a;
        volatile long b;
    }

    static class FalseShareVo extends ShareVo{
        volatile long a;
        long p1,p2,p3,p4,p5,p6,p7;
        volatile long b;
    }
}
