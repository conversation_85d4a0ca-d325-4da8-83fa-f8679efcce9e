package com.buzz.java.concurrent.juc;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

public class ConditionTest {

	private final ReentrantLock lock = new ReentrantLock();

	private final Condition available = lock.newCondition();

	private List<String> items = new ArrayList<String>();

	public void take() throws InterruptedException {
		final ReentrantLock lock = this.lock;
		lock.lockInterruptibly();
		try {
			System.out.println(Thread.currentThread().getId() + " take");
				available.await();
		} finally {
			lock.unlock();
		}
	}

	public static void main(String[] args) {
		ConditionTest test = new ConditionTest();
		new Thread(new Runnable() {

			@Override
			public void run() {
				try {
					test.take();
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}).start();

		new Thread(new Runnable() {

			@Override
			public void run() {
				try {
					test.take();
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}).start();
	}
}
