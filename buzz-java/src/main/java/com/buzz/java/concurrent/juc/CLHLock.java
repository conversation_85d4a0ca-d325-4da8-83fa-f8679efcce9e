package com.buzz.java.concurrent.juc;

import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.LockSupport;

public class CLHLock {

	private transient volatile Node head = new Node();// 哨兵节点,不与任何线程关联

	private final AtomicReference<Node> tail = new AtomicReference<Node>();

	/**
	 * 入队
	 * 
	 * @param node 
	 * @return 
	 */
	private Node enq(final Node node) {
		for (;;) {
			Node t = tail.get();
			if (t == null) {
				if(tail.compareAndSet(null, new Node())) {
					head = tail.get();
				}
			} else {
				node.prev = t;
				// 通过CAS保证并发更新不会覆盖
				if (tail.compareAndSet(t, node)) {
					t.next = node;
					return t;
				}
			}
		}
	}

	public void lock() {

		Node node = new Node();
		node.thread = Thread.currentThread();
		node.lock = true;
		Node t = enq(node);
		if (t.thread == null) {// 当前节点的prev等于head，说明是第一个节点
			head = node;
			System.out.println("im head " + "," + node);
			return;
		}
		while (t.lock) {
			LockSupport.park();
		}
	}

	public void unlock() {
		Node h = head;
		h.lock = false;
		h.thread = null;
		h.prev = null; // 销毁

		// 说明还有后续节点
		if (h.next != null) {
			head = h.next; // 更新head
			LockSupport.unpark(head.thread);// 唤醒新的head
			System.out.println("unlock " + h.next);
		}
	}

	private static class Node {
		private volatile boolean lock;
		private volatile Node prev;
		private volatile Node next;
		private volatile Thread thread;

		public String toString() {
			return thread == null ? "node:no_thread" : "node:" + thread.getName();
		}
	}
}
