package com.buzz.java.concurrent.queue;


public class ArrayQueue<E> {

    final Object[] items;
    private int putIndex;
    private int takeIndex;
    private int count;

    public ArrayQueue(int size) {
        this.items = new Object[size];
    }

    public boolean offer(E e) {
        final Object[] items = this.items;
        items[putIndex] = e;
        if (++putIndex == items.length)
            putIndex = 0;
        count++;
        return true;
    }


    public E poll() {
        final Object[] items = this.items;
        E x = (E) items[takeIndex];
        items[takeIndex] = null;
        if (++takeIndex == items.length)
            takeIndex = 0;
        count--;
        return x;
    }

    public int size() {
        return count;
    }
}
