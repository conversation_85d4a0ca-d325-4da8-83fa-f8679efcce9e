package com.buzz.java.concurrent.juc;

import java.util.concurrent.CountDownLatch;

public class CLHLockTest {

	private static CLHLock lock = new CLHLock();
 
	public static void test() {
		lock.lock();
		System.out.println("execute " + Thread.currentThread().getName());
		try {
			Thread.sleep(100);
		} catch (InterruptedException e) {
			e.printStackTrace();
		}
		lock.unlock();
	}

	public static void main(String[] args) throws InterruptedException {

		CountDownLatch latch = new CountDownLatch(3);
		for (int i = 0; i < 3; ++i) {

			Thread t = new Thread(new Runnable() {

				@Override
				public void run() {

					test();
					latch.countDown();
				}
			});
			t.setName("thread-" + i);
			t.start();
		}
//		latch.await();
//		System.out.println("=======finally========");
//		test();
	}
}
