package com.buzz.java.concurrent.delay;

import lombok.extern.slf4j.Slf4j;

import java.util.concurrent.Delayed;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class Timers {

    private static Timer delayQueueTimer = new DelayQueueTimer(1);

    public static TimeoutFuture newTimeout(TimeUnit timeUnit, int timeout, Runnable callback) {
        return delayQueueTimer.newTimeout(timeUnit, timeout, callback);
    }

    public static TimeoutFuture newTimeout(DelayedTask delayedTask) {
        return delayQueueTimer.newTimeout(delayedTask);
    }

    interface Timer {
        TimeoutFuture newTimeout(TimeUnit timeUnit, int timeout, Runnable callback);

        TimeoutFuture newTimeout(DelayedTask delayedTask);
    }

    interface TimeoutFuture {
        void cancel();
    }

    /**
     * Base DelayQueue Timer
     */
    static class DelayQueueTimer implements Timer, Runnable {

        private DelayQueue<DelayedTask> delayQueue = new DelayQueue<>();

        public DelayQueueTimer(int workerSize) {
            for (int i = 0; i < workerSize; ++i) {
                Thread thread = new Thread(this, "timeout-thread-" + i);
                thread.setDaemon(true);
                thread.start();

            }
        }

        @Override
        public TimeoutFuture newTimeout(TimeUnit timeUnit, int timeout, Runnable callback) {
            DelayedTask delayedTask = new DelayedTask(timeUnit.toMillis(timeout), callback);
            return newTimeout(delayedTask);
        }

        @Override
        public TimeoutFuture newTimeout(DelayedTask delayedTask) {
            delayQueue.add(delayedTask);
            return new DelayQueueTimeoutFuture(this, delayedTask);
        }

        void cancel(DelayedTask delayedTask) {
            delayQueue.remove(delayedTask);
        }

        @Override
        public void run() {
            while (true) {
                try {
                    DelayedTask delayedTask = delayQueue.take();
                    delayedTask.run();
                } catch (InterruptedException e) {
                    break;
                } catch (Exception e) {
                    log.error("execute delayedTask error", e);
                }
            }
        }
    }


    static class DelayQueueTimeoutFuture implements TimeoutFuture {

        private DelayQueueTimer delayQueueTimer;
        private DelayedTask delayedTask;

        public DelayQueueTimeoutFuture(DelayQueueTimer delayQueueTimer, DelayedTask delayedTask) {
            this.delayQueueTimer = delayQueueTimer;
            this.delayedTask = delayedTask;
        }

        @Override
        public void cancel() {
            delayQueueTimer.cancel(delayedTask);
        }
    }

    //参考ScheduledThreadPoolExecutor中的ScheduledFutureTask
    static class DelayedTask implements Delayed, Runnable {

        private long expireTime;
        private Runnable callback;

        public DelayedTask(long delay, Runnable callback) {
            this.expireTime = System.nanoTime() + TimeUnit.MILLISECONDS.toNanos(delay);
            this.callback = callback;
        }

        public void wakeup() {
            this.expireTime = System.nanoTime();
        }

        @Override
        public int compareTo(Delayed other) {
            if (other == this) {
                return 0;
            }
            long diff = getDelay(TimeUnit.NANOSECONDS) - other.getDelay(TimeUnit.NANOSECONDS);
            return (diff < 0) ? -1 : ((diff > 0) ? 1 : 0);
        }

        @Override
        public long getDelay(TimeUnit unit) {
            long delay = unit.convert(expireTime - System.nanoTime(), TimeUnit.NANOSECONDS);
            log.info("getDelay {}", delay);
            return delay;
        }

        @Override
        public void run() {
            callback.run();
        }
    }

}