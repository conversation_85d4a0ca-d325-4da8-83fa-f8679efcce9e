package com.buzz.java.concurrent.executor;

import com.buzz.toolkit.NamedThreadFactory;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.util.concurrent.*;

/**
 * <AUTHOR>
 */
@Slf4j
public class ExecutorTest {


    /**
     * 如何正确关闭 ScheduledThreadPoolExecutor
     */
    @Test
    public void test_CloseScheduledThreadPool() throws InterruptedException {
        ScheduledThreadPoolExecutor executor = new ScheduledThreadPoolExecutor(1, new NamedThreadFactory("test"));

        executor.scheduleAtFixedRate(() -> {
            log.info("ok");
        }, 0, 1, TimeUnit.SECONDS);

        Thread.sleep(1000);

        log.info("removing....");
        executor.shutdown();
        log.info("removed....");

        Thread.sleep(3000);
    }

    /**
     * 测试任务执行抛出异常
     *
     * @throws TimeoutException
     * @throws ExecutionException
     * @throws InterruptedException
     */
    @Test
    public void testTaskException() {
        ThreadPoolExecutor poll = (ThreadPoolExecutor) Executors.newFixedThreadPool(1);
        Future<Object> future = poll.submit(() -> {
            log.info("execute");
            throw new RuntimeException("error");
        });

        try {
            future.get(1000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            //会报错此异常
            e.printStackTrace();
        } catch (TimeoutException e) {
            e.printStackTrace();
        }

    }
//    private static final int COUNT_BITS = Integer.SIZE - 3;
//    private static final int CAPACITY = (1 << COUNT_BITS) - 1;
//    private ThreadPoolExecutor executor = (ThreadPoolExecutor)Executors.newFixedThreadPool(1);
//
//    private static int workerCountOf(int c) {
//        return c & CAPACITY;
//    }
//
//    public int getWorkerCount() {
//        try {
//            Field f = ThreadPoolExecutor.class.getDeclaredField("ctl");
//            f.setAccessible(true);
//            AtomicInteger ctl = (AtomicInteger)f.get(executor);
//            return workerCountOf(ctl.get());
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        return 0;
//    }
//
//    public void test() throws InterruptedException {
//
//        System.out.println("prepre \t" + getWorkerCount());
//
//        Runnable r = () -> {
//            System.out.println("running  \t " + getWorkerCount());
//            System.out.println(1 / 0);
//        };
//
//        try {
//            executor.submit(r);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//
//        Thread.sleep(1000);
//        System.out.println("after execute \t " + getWorkerCount());
//
//        // executor.shutdown();
//    }
//
//    public static void main(String[] args) throws InterruptedException {
//        // new ExecutorTest().test();
//
//        Thread t = new Thread(() -> {
//            while(true) {
//                SynchronousQueue queue = new SynchronousQueue();
//                Object task =null;
//                try {
//                    task = queue.take();
//                } catch (InterruptedException e) {
//                    task =null;
//                }
//                System.out.println("shutdown");
//                break;
//            }
//        });
//        t.start();
//        t.interrupt();
//    }

}
