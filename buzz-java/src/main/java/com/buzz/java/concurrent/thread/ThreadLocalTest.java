package com.buzz.java.concurrent.thread;

import org.junit.Test;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

public class ThreadLocalTest {


    @Test
    public void test_basic() {
        ThreadLocal<Integer> uid = ThreadLocal.withInitial(() -> 1);
        ThreadLocal<Integer> tid = ThreadLocal.withInitial(() -> 1000);
        uid.set(10);
        System.out.println(uid.get());
        System.out.println(tid.get());
    }

    /**
     * 测试 InheritableThreadLocal 无法解决 ExecutorService 中获取的问题
     */
    @Test
    public void test_InheritableThreadLocal() {
        // 1、让ExecutorService先启动ThreadPoool
        ExecutorService es = Executors.newFixedThreadPool(1);
        es.execute(() -> {
            System.out.println("es started");
        });

        // 2、设置value
        InheritableThreadLocal<String> ctx = new InheritableThreadLocal<String>();
        ctx.set("bairen");
        //通过线程可以获取到
        new Thread(() -> {
            System.out.println(Thread.currentThread().getName() + " ==> " + ctx.get());
        }).start();

        //通过es执行无法获取
        es.execute(() -> {
            System.out.println(Thread.currentThread().getName() + " ==> " + ctx.get());
        });

        es.shutdownNow();
        System.out.println("=============");
    }

    /**
     * 测试 通过 InheritableTask解决
     *
     * @throws Exception
     */
    @Test
    public void test_InheritableTask() throws Exception {
        // 1、让ExecutorService先启动ThreadPoool
        ExecutorService es = Executors.newFixedThreadPool(1);
        es.execute(() -> {
            System.out.println("es started");
        });

        // 2、设置value
        InheritableThreadLocal<String> ctx = new InheritableThreadLocal<String>();
        ctx.set("bairen");

        //通过es执行无法获取
        es.execute(() -> {
            System.out.println(Thread.currentThread().getName() + " ==> " + ctx.get());
        });

        //3、通过es执行InheritableTask成功获取
        es.submit(new InheritableTask() {

            public void runTask() {
                System.out.println(Thread.currentThread().getName() + " ==> " + ctx.get());
            }

        });
        es.shutdownNow();
    }


    private abstract static class InheritableTask implements Runnable {

        private Object inheritableThreadLocalsObj;

        private Field inheritableThreadLocalsField;

        public InheritableTask() throws Exception {
            // 获取业务线程的中的inheritableThreadLocals属性值
            Thread currentThread = Thread.currentThread();
            Object inheritableThreadLocals = getInheritableThreadLocals(currentThread);

            if (inheritableThreadLocals != null) {
                //相当于调用 ThreadLocal.createInheritedMap(parentMap);
                //业务线程的ThreaLocal已经被保存下来来
                Method method = ThreadLocal.class.getDeclaredMethod("createInheritedMap",
                        inheritableThreadLocalsField.getType());
                method.setAccessible(true);
                inheritableThreadLocalsObj = method.invoke(ThreadLocal.class, inheritableThreadLocals);
            }
        }

        public abstract void runTask();

        private Object getInheritableThreadLocals(Thread thread) {
            try {
                inheritableThreadLocalsField = Thread.class.getDeclaredField("inheritableThreadLocals");
                inheritableThreadLocalsField.setAccessible(true);
                return inheritableThreadLocalsField.get(thread);
            } catch (Exception e) {
                throw new IllegalStateException(e);
            }
        }

        public void run() {
            // 此处得到的是当前处理该业务的线程，也就是线程池中的线程
            Thread currentThread = Thread.currentThread();
            Field field = null;
            try {
                field = Thread.class.getDeclaredField("inheritableThreadLocals");
                field.setAccessible(true);
                // 将暂存的值，赋值给currentThread
                if (inheritableThreadLocalsObj != null && field != null) {
                    field.set(currentThread, inheritableThreadLocalsObj);
                    inheritableThreadLocalsObj = null;
                }
                // 执行任务
                runTask();
            } catch (Exception e) {
                throw new IllegalStateException(e);
            } finally {
                // 最后将线程中的InheritableThreadLocals设置为null
                try {
                    field.set(currentThread, null);
                } catch (Exception e) {
                    throw new IllegalStateException(e);
                }
            }
        }
    }
}
