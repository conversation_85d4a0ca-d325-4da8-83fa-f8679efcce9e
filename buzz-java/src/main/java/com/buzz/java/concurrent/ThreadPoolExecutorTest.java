package com.buzz.java.concurrent;

import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;

public class ThreadPoolExecutorTest {

	public static void main(String[] args) throws InterruptedException {
		ThreadPoolExecutor service = (ThreadPoolExecutor) Executors.newFixedThreadPool(1);
		Runnable r = () -> {
			int i = 0;
			while (!Thread.interrupted() && i < 10000000) {
				System.out.println("execute " + i);
				++i;
			}

		};
		
		service.submit(r);
		Thread.sleep(1000);
		service.shutdown();
		service.shutdownNow();
		
	}
}
