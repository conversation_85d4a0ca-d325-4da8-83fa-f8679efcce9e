package com.buzz.java.concurrent.queue;

import org.junit.Test;

import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.TimeUnit;

public class SynchronousQueueTest {

    @Test
    public void test101() throws InterruptedException {

        SynchronousQueue<Integer> queue = new SynchronousQueue();


        Thread thread = new Thread(() -> {
            while (true) {
                try {
                    Integer item = queue.poll(1, TimeUnit.SECONDS);
                    if (item != null) {
                        System.out.println("poll="+item);
                    }
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        });
        thread.start();
        Thread.sleep(1000);
        System.out.println(queue.offer(1));
        System.out.println(queue.offer(2));
    }
}
