package com.buzz.java.concurrent;

import java.io.IOException;
import java.util.concurrent.locks.ReentrantLock;

public class ReentrantLockTest {

	public static void main(String[] args) throws IOException  {
		
		ReentrantLock lock = new ReentrantLock();
		lock.lock();
		System.out.println("ok");
		
		
		new Thread(new Runnable() {
			
			@Override
			public void run() {
				lock.lock();
			}
		}).start();
	}
}
