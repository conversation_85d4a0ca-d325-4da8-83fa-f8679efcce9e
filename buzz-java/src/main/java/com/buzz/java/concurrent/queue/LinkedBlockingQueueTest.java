package com.buzz.java.concurrent.queue;

import org.junit.Test;

import java.util.concurrent.LinkedBlockingQueue;

public class LinkedBlockingQueueTest {


	@Test
	public void test101(){
		LinkedQueue<Integer> q = new LinkedQueue<>();
		q.offer(1);
		q.offer(2);
		q.offer(3);

		System.out.println(q.poll());
		System.out.println(q.poll());
	}

	@Test
	public void test102(){
		LinkedBlockingQueue<Integer> q = new LinkedBlockingQueue<>();
		q.offer(1);
		q.offer(2);
		q.offer(3);

		System.out.println(q.poll());
		System.out.println(q.poll());
	}


}
