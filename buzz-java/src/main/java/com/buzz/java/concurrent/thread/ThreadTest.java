package com.buzz.java.concurrent.thread;

import org.junit.Test;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;

/**
 * <AUTHOR>
 * @description
 * @date 2022-07-04 20:33
 **/
public class ThreadTest {


    /**
     * 停止线程
     *
     * @throws Exception
     */
    @Test
    public void testStopThread() throws Exception {

        Thread thread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                System.out.println("status:" + Thread.currentThread().isInterrupted());
            }
        });
        thread.start();
        Thread.sleep(5);
        thread.interrupt();
        System.out.println("notify interrupt");
        Thread.sleep(1000);
    }
    /**
     * 停止带有sleep的线程
     *
     * @throws Exception
     */
    @Test
    public void testStopSleepThread() throws Exception {

        Thread thread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                System.out.println("status:" + Thread.currentThread().isInterrupted());
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    System.out.println("receive InterruptedException");
                    Thread.currentThread().interrupt(); //注意需要把线程状态设置为已中断
                }
            }
        });
        thread.start();
        Thread.sleep(5);
        thread.interrupt();
        System.out.println("notify interrupt");
        Thread.sleep(1000);
    }

    /**
     * 停止被block的Thread
     *
     * @throws Exception
     */
    @Test
    public void testStopBlockingQueueThread()throws Exception{
        final BlockingQueue storage = new ArrayBlockingQueue(1);
        Thread thread = new Thread(() -> {
            while (!Thread.currentThread().isInterrupted()) {
                System.out.println("status:" + Thread.currentThread().isInterrupted());
                try {
                    storage.put("test");
                } catch (InterruptedException e) {
                    System.out.println("put queue InterruptedException");
                    Thread.currentThread().interrupt(); //注意需要把线程状态设置为已中断
                }
            }
        });
        thread.start();
        Thread.sleep(5);
        thread.interrupt();
        System.out.println("notify interrupt");
        Thread.sleep(1000);
    }

}
