package com.buzz.java.concurrent;

import com.buzz.cache.mock.DataObject;
import org.apache.commons.collections.map.HashedMap;
import org.junit.Test;

import java.util.Date;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.CyclicBarrier;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import static org.junit.Assert.assertEquals;

public class ConcurrentCacheTest {

    private LocalCache localCache = new LocalCache();

    @Test
    public void testConcurrentGetSet() throws InterruptedException {
        int threadCount = 10;
        ExecutorService es = Executors.newFixedThreadPool(threadCount);
        CyclicBarrier barrier = new CyclicBarrier(threadCount);
        CountDownLatch latch = new CountDownLatch(threadCount);
        for (int i = 0; i < threadCount; ++i) {
            es.submit(() -> {
                try {
                    barrier.await();
                } catch (Exception e) {
                    e.printStackTrace();
                }
                //同一个key并发方法，期望不能执行多次
                String key = "A";
                localCache.get(key, l -> DataObject.get(key));
                latch.countDown();
            });
        }
        latch.await();
        assertEquals(1, DataObject.objectCounter);
    }



}
