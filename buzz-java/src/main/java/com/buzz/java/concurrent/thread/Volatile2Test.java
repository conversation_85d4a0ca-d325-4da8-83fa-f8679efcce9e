package com.buzz.java.concurrent.thread;

public class Volatile2Test {

	private static boolean initFlag = false;

	public static void main(String[] args) throws Exception {

		new Thread(new Runnable() {

			@Override
			public void run() {
				System.out.println("waiting data...");
				while (!initFlag) {

				}
				System.out.println("success...");
			}
		}).start();

		Thread.sleep(500);

//		new Thread(new Runnable() {
//
//			@Override
//			public void run() {
//				prepareData();
//			}
//		}).start();
		
		prepareData();
	}

	static void prepareData() {
		System.out.println("prepare data...");
		initFlag = true;
		System.out.println("prepare data end ...");
	}
}
