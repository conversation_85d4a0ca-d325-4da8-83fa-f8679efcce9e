package com.buzz.java.concurrent.executor;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import com.google.common.base.Preconditions;
import org.junit.Test;

import com.buzz.util.HttpUtils;
import com.buzz.util.HttpUtils.HttpResult;

import lombok.extern.slf4j.Slf4j;

/**
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class BadThreadPollExecutor {

	private CountDownLatch latch;
	private ArrayBlockingQueue<AsyncRequest> requestQueue = new ArrayBlockingQueue<AsyncRequest>(5000);
	private ThreadPoolExecutor threadPoll;

	public BadThreadPollExecutor() {
		threadPoll = new ThreadPoolExecutor(5, 5, 5L, TimeUnit.SECONDS, new LinkedBlockingQueue<>());

	}

	public void prepare() {
		for (int i = 0; i < 5; ++i) {
			threadPoll.submit(new AsyncRequestReceiver());
		}
	}

	public void add(AsyncRequest request) {
		requestQueue.add(request);
	}

	@Test
	public void testByNativeThreadPollExecutor() throws InterruptedException {
		BadThreadPollExecutor executor = new BadThreadPollExecutor();
		for (int i = 0; i < 5; ++i) {
			doNativeThreadPollExecutor(executor);
		}
	}

	@Test
	public void testByWrapperThreadPollExecutor() throws InterruptedException {
		BadThreadPollExecutor executor = new BadThreadPollExecutor();
		executor.prepare();
		for (int i = 0; i < 5; ++i) {
			doByWrapperThreadPollExecutor(executor);
		}
	}

	public void doNativeThreadPollExecutor(BadThreadPollExecutor executor) throws InterruptedException {
		long begin = System.currentTimeMillis();
		CountDownLatch latch = new CountDownLatch(1000);
		for (int i = 0; i < 1000; ++i) {
			executor.threadPoll.submit(new AsyncRequestTask(new AsyncRequest(), latch));
		}
		latch.await();
		long end = System.currentTimeMillis();
		log.info("native test cost:" + (end - begin));
	}

	public void doByWrapperThreadPollExecutor(BadThreadPollExecutor executor) throws InterruptedException {
		long begin = System.currentTimeMillis();
		executor.latch = new CountDownLatch(1000);
		for (int i = 0; i < 1000; ++i) {
			executor.add(new AsyncRequest());
		}
		executor.latch.await();
		long end = System.currentTimeMillis();
		log.info("cost:" + (end - begin));
	}

	private static class AsyncRequestTask implements Runnable {
		private AsyncRequest asyncRequest;
		private CountDownLatch latch;

		public AsyncRequestTask(AsyncRequest asyncRequest, CountDownLatch latch) {
			this.asyncRequest = asyncRequest;
			this.latch = latch;
		}

		@Override
		public void run() {
			HttpResult rest;
			try {
				rest = HttpUtils.httpGet(asyncRequest.url, null, null, "utf-8", 3000);
				Preconditions.checkArgument(rest.code == 200);
				latch.countDown();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	}

	private class AsyncRequestReceiver implements Runnable {

		@Override
		public void run() {
			while (true) {
				try {
					AsyncRequest request = requestQueue.take();
					HttpResult rest = HttpUtils.httpGet(request.url, null, null, "utf-8", 3000);
					Preconditions.checkArgument(rest.code == 200);
					latch.countDown();
					// System.out.println("execute");
				} catch (InterruptedException e) {
					// ignore
				} catch (Exception e) {
					log.error("process error", e);
				}
			}

		}
	}

	private static class AsyncRequest {
		public String url = "https://www.baidu.com";
	}

}
