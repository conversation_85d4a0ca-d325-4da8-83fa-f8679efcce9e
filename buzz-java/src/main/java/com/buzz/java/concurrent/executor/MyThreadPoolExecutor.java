package com.buzz.java.concurrent.executor;

import java.lang.reflect.Field;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.Future;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

public class MyThreadPoolExecutor extends ThreadPoolExecutor {

	public MyThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
			BlockingQueue<Runnable> workQueue) {
		super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
	}

	@Override
	public Future<?> submit(Runnable task) {
		Task t = (Task) task;
		System.out.println("submit task-" + t.id + ", active_size=" + super.getActiveCount() + "\t ctl_size="
				+ this.getWorkerCount());
		Future<?> ret = super.submit(task);
		return ret;
	}

	private static final int COUNT_BITS = Integer.SIZE - 3;
	private static final int CAPACITY = (1 << COUNT_BITS) - 1;

	private static int workerCountOf(int c) {
		return c & CAPACITY;
	}

	private String getWorkerCount() {
		String value = "";
		try {
			Field ctlField = ThreadPoolExecutor.class.getDeclaredField("ctl");
			ctlField.setAccessible(true);
			value = String.valueOf(workerCountOf(Integer.valueOf(ctlField.get(this).toString())));
		} catch (Exception e) {
			e.printStackTrace();
		}

		return value;
	}

	public static void main(String[] args) throws InterruptedException {
		BlockingQueue<Runnable> workQueue = new LogableArrayBlockingQueue(1);
		MyThreadPoolExecutor executor = new MyThreadPoolExecutor(3, 5, 1, TimeUnit.MILLISECONDS, workQueue);
		for (int i = 0; i < 10; ++i) {
			try {
				executor.submit(new Task(i));
			} catch (Exception e) {
				System.err.println("submit failed in " + i + ", msg is " + e.getMessage() + "");
//				break;
				Thread.sleep(1000);
				System.err.println(
						"wakeup active_size=" + executor.getActiveCount() + "\t ctl_size=" + executor.getWorkerCount());
			}
		}

		executor.shutdown();
	}

	private static class Task implements Runnable {

		private long id;

		public Task(long id) {
			super();
			this.id = id;
		}

		@Override
		public void run() {
			// System.out.println("execute task " + id);
			try {
				TimeUnit.MILLISECONDS.sleep(10);
			} catch (InterruptedException e) {
				System.err.println("task-" + id + " " + e.getMessage());
			}
		}

	}

	public static class LogableArrayBlockingQueue extends ArrayBlockingQueue<Runnable> {

		private static final long serialVersionUID = 1L;

		public LogableArrayBlockingQueue(int capacity) {
			super(capacity);
		}

		@Override
		public boolean offer(Runnable e) {
			boolean ret = super.offer(e);
			System.out.println(
					Thread.currentThread().getName() + " offer [" + e + "] result " + ret + ",size=" + this.size());
			return ret;
		}

		@Override
		public Runnable take() throws InterruptedException {
			Runnable e = super.take();
//			System.out.println("take " + e);
			return e;
		}
	}
}
