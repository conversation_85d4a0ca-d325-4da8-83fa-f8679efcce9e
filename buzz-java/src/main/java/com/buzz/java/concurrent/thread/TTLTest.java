package com.buzz.java.concurrent.thread;

import com.alibaba.ttl.TransmittableThreadLocal;
import com.alibaba.ttl.TtlRunnable;
import org.jetbrains.annotations.NotNull;
import org.junit.Test;

import java.util.Comparator;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;

/**
 * TransmittableThreadLocal
 *
 * https://blog.csdn.net/vivo_tech/article/details/113500269
 * https://github.com/alibaba/transmittable-thread-local/issues/330
 * https://github.com/alibaba/transmittable-thread-local/issues/340
 * https://github.com/alibaba/transmittable-thread-local/issues/293
 * https://github.com/alibaba/transmittable-thread-local/issues/135
 *
 * <AUTHOR>
 * @description
 **/
public class TTLTest {

    /**
     * 注意，ttl只是透传数据，所以在runnable中修改ttl不影响root
     * @throws InterruptedException
     */
    @Test
    public void test_basic() throws InterruptedException {
        TTLHolder.threadLocal.set("tx=root");
        ThreadPoolExecutor executor = new CustomThreadPoolExecutor(1, 1, 30, TimeUnit.SECONDS, new LinkedBlockingDeque());
        CountDownLatch latch = new CountDownLatch(3);
        for (int i = 0; i < 3; ++i) {
            executor.execute(TtlRunnable.get(new Task(latch, i)));
        }
        latch.await();
        System.out.println("finish"+TTLHolder.threadLocal.get());
    }

    public static class TTLHolder {
        public static TransmittableThreadLocal threadLocal = new TransmittableThreadLocal();
    }

    private static class Task implements Runnable {
        private CountDownLatch latch;
        private Integer order;

        public Task(CountDownLatch latch, Integer order) {
            this.latch = latch;
            this.order = order;
        }

        @Override
        public String toString() {
            return "task-" + order;
        }

        @Override
        public void run() {
            System.out.println("before execute " + this + "\t ctx:" + TTLHolder.threadLocal.get());
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            TTLHolder.threadLocal.set("tx=" + toString());
            System.out.println("after execute " + this + "\t ctx:" + TTLHolder.threadLocal.get());
            System.out.println();
            latch.countDown();
        }
    }

    @Test
    public void test_issue330() throws InterruptedException {

        TTLHolder.threadLocal.set("tx=root");
        Comparator<Object> comparator = new Comparator<Object>() {
            @Override
            public int compare(Object o1, Object o2) {
                TtlRunnable r1 = (TtlRunnable) o1;
                TtlRunnable r2 = (TtlRunnable) o2;
                Comparable c1 = (Comparable) r1.unwrap();
                Comparable c2 = (Comparable) r2.unwrap();
                return c1.compareTo(c2);
            }
        };
        ThreadPoolExecutor executor = new CustomThreadPoolExecutor(1, 1, 30, TimeUnit.SECONDS, new PriorityBlockingQueue(10, comparator));
        CountDownLatch latch = new CountDownLatch(10);
        for (int i = 0; i < 10; ++i) {
            executor.execute(TtlRunnable.get(new PriorityTask(latch, i)));

        }
        latch.await();
    }

    public static class PriorityTask implements Runnable, Comparable<PriorityTask> {
        private CountDownLatch latch;
        private Integer order;

        public PriorityTask(CountDownLatch latch, Integer order) {
            this.latch = latch;
            this.order = order;
        }

        @Override
        public int compareTo(@NotNull PriorityTask o) {
            return order.compareTo(o.order);
        }

        @Override
        public String toString() {
            return "task-" + order;
        }

        @Override
        public void run() {
            System.out.println("execute " + this + "\t ctx:" + TTLHolder.threadLocal.get());
            try {
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            TTLHolder.threadLocal.set("tx=" + toString());
            latch.countDown();
        }
    }




    @Test
    public void test_ScheduledThreadPoolExecutor() throws InterruptedException {
        ScheduledThreadPoolExecutor schedule = new ScheduledThreadPoolExecutor(1);
        CountDownLatch latch = new CountDownLatch(10);
        for (int i = 0; i < 10; ++i) {
            schedule.schedule(TtlRunnable.get(new PriorityTask(latch, i)), i, TimeUnit.SECONDS);
        }
        latch.await();
    }


    @Test
    public void test_TracePass() throws InterruptedException {
        MockBiz.root();
        System.out.println(Context.getContext().get("traceId"));
    }

    private static class Context<K, V> {
        private static TransmittableThreadLocal threadLocal = new TransmittableThreadLocal() {
            protected Context initialValue() {
                return new Context();
            }
        };

        private Map<K, V> innerMap = new HashMap();

        public static <K, V> Context<K, V> getContext() {
            return (Context) threadLocal.get();
        }

        public V get(K key) {
            return innerMap.get(key);
        }

        private V computeIfAbsent(K key,
                                  Function<? super K, ? extends V> mappingFunction) {
            return innerMap.computeIfAbsent(key, mappingFunction);
        }

    }

    private static void mark() {
        Context<String, AtomicInteger> ctx = Context.getContext();
        AtomicInteger counter = ctx.computeIfAbsent("traceId", (key) -> {
            return new AtomicInteger();
        });
        counter.incrementAndGet();
    }

    private static class MockBiz {

        public static void root() throws InterruptedException {
            mark();
            action0_1();
            action0_2();
        }

        public static void action0_1() throws InterruptedException {
            mark();
            ThreadPoolExecutor executor = new CustomThreadPoolExecutor(8, 8, 30, TimeUnit.SECONDS, new LinkedBlockingDeque<>());

            CountDownLatch latch = new CountDownLatch(2);
            executor.submit(TtlRunnable.get(new Runnable() {
                @Override
                public void run() {
                    action0_1_1();
                    latch.countDown();
                }
            }));
            executor.submit(TtlRunnable.get(new Runnable() {
                @Override
                public void run() {
                    action0_1_2();
                    latch.countDown();
                }
            }));

            latch.await();

        }

        public static void action0_1_1() {
            mark();
        }

        public static void action0_1_2() {
            mark();
        }

        public static void action0_2() {
            mark();
        }
    }

    private static class CustomThreadPoolExecutor extends ThreadPoolExecutor {

        public CustomThreadPoolExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit, BlockingQueue<Runnable> workQueue) {
            super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
        }

    }
}
