package com.buzz.java.concurrent.juc;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

public class ConcurentMapUseage {

	static class HostListenerRegistry {

		private ConcurrentMap<String, List<HostListener>> observerMap = new ConcurrentHashMap<String, List<HostListener>>();

		public void add(String key, HostListener listener) {
			//大部分场景不为null
			List<HostListener> observers = observerMap.get(key);
			if (observers == null) {
				observers = Collections.synchronizedList(new ArrayList<HostListener>());
				observers.add(listener);
				observers = observerMap.putIfAbsent(key, observers);
			}
			observers.add(listener);

		}
	}

	interface HostListener {

	}
}
