package com.buzz.java.concurrent.juc;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 
 * 通过double-check保证创建出来的Bucket对象个数刚好符合指定的大小
 * 
 * <AUTHOR>
 *
 */
public class DoubleCheckTest {

	public static void test(BucketContainer container) {
		for (int i = 0; i < 10; ++i) {
			container.getBucket(String.valueOf(i));
		}
	}

	public static void main(String[] args) {
		final BucketContainer container = new BucketContainer();
		AtomicInteger countDown = new AtomicInteger(5);

		for (int i = 0; i < 5; ++i) {
			Thread t = new Thread(new Runnable() {

				@Override
				public void run() {
					test(container);
					if (countDown.decrementAndGet() == 0) {
						synchronized (countDown) {
							countDown.notify();
						}
					}
				}
			});
			t.start();
		}

		synchronized (countDown) {
			try {
				countDown.wait();
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}

		System.out.println("done " + Bucket.count + "\t" + container.bucketCache);

	}

	private static class BucketContainer {

		public Map<String, Bucket> bucketCache = new HashMap<String, Bucket>();

		public Bucket getBucket(String name) {
			Bucket bucket = bucketCache.get(name);
			if (bucket != null) {
				return bucket;
			}
			synchronized (bucketCache) {
				bucket = bucketCache.get(name);// double check
				if (bucket == null) {
					bucket = new Bucket(name);
					bucketCache.put(name, bucket);
				}
				return bucket;
			}
		}
	}

	private static class Bucket {

		public static final AtomicInteger count = new AtomicInteger();

		private String name;
		private int ix;

		public Bucket(String name) {
			this.name = name;
			this.ix = count.incrementAndGet();
		}

		@Override
		public String toString() {
			return name + ":" + ix;
		}
	}

}
