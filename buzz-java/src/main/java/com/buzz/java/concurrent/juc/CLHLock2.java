package com.buzz.java.concurrent.juc;

import java.util.concurrent.atomic.AtomicReference;

public class CLHLock2 {
	private final ThreadLocal<Node> node; // 保存当前线程的节点
	private final ThreadLocal<Node> prev; // 保存当前线程的前继节点
	// 类在初始化时会初始化tail节点，相当于head节点(注意对隐式队列的理解)
	private final AtomicReference<Node> tail = new AtomicReference<Node>(new Node());

	public CLHLock2() {

		this.node = new ThreadLocal<Node>() {
			@Override
			protected Node initialValue() {
				return new Node();
			}
		};

		this.prev = new ThreadLocal<Node>() {
			@Override
			protected Node initialValue() {
				return null;
			}
		};

	}

	public void lock() {
		Node node = this.node.get(); // 获取Threadlocal变量，每个线程私有
		node.thread = Thread.currentThread();
		node.lock = true;
		Node tail = this.tail.getAndSet(node); // CAS方式设置tail节点，设置成功，相当于加入队列

		System.out.println("lock " + node.thread.getName() + ",tail=" + tail);
		// 把之前的tail节点，存入自己的prev节点，并循环访问其lock标志位，等待释放
		this.prev.set(tail);
		while (tail.lock) {
			try {
				Thread.sleep(10);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}

	}

	public void unlock() {
		Node node = this.node.get();
		node.lock = false;
		System.out.println("unlock " + node.thread.getName() + ",pred=" + prev.get());
	}

	private class Node {
		// 默认初始化值为false
		private volatile boolean lock;
		private Thread thread;

		public String toString() {
			return thread == null ? "null" : thread.getName();
		}
	}
}
