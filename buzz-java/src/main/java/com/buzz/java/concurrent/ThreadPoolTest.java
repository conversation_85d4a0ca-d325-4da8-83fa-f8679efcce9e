package com.buzz.java.concurrent;

import java.util.concurrent.Executors;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

public class ThreadPoolTest {

	private static long statCount;

	public static void printStat(ThreadPoolExecutor executor) {

		System.out.println("========" + (statCount++) + "========");
		StringBuffer sb = new StringBuffer();
		sb.append(String.format("ActiveCount:%s ", executor.getActiveCount()));
		sb.append(String.format(",CorePoolSize:%s ", executor.getCorePoolSize()));
		sb.append(String.format(",QueueSize:%s ", executor.getQueue().size()));
		sb.append(String.format(",TaskCount:%s ", executor.getTaskCount()));
		sb.append(String.format(",ActiveCount:%s ", executor.getActiveCount()));
		sb.append(String.format(",PoolSize:%s ", executor.getPoolSize()));
		System.out.println(sb.toString());
		System.out.println("=================");
	}

	static class Task implements Runnable {

		private int id;

		private ThreadPoolExecutor executor;

		public Task(int id, ThreadPoolExecutor executor) {
			this.id = id;
			this.executor = executor;
		}

		@Override
		public void run() {
			printStat(executor);
			try {
				Thread.sleep(500);
			} catch (InterruptedException e) {
				e.printStackTrace();
				throw new RuntimeException(e);
			}
			
			System.out.println(Thread.currentThread().getName() + " finished task " + id);
		}

	}

	public static void main(String[] args) throws InterruptedException {

		ThreadPoolExecutor executor = (ThreadPoolExecutor) Executors.newFixedThreadPool(2);
		printStat(executor);

		Object lock = new Object();
		final AtomicBoolean notifyed = new AtomicBoolean();
		Thread mainThread = new Thread(new Runnable() {

			@Override
			public void run() {
				for (int i = 0; i < 50; ++i) {
					if (i > 19 && !notifyed.get()) {
						notifyed.set(true);
						synchronized (lock) {
							lock.notify();
						}
					}
					try {
						Thread.sleep(10);
					} catch (InterruptedException e) {
						e.printStackTrace();
					}
					Task task = new Task(i, executor);
					executor.execute(task);

				}
			}
		});
		mainThread.start();
		synchronized (lock) {
			lock.wait();
		}
		executor.shutdown();
		while(!executor.awaitTermination(1000, TimeUnit.MILLISECONDS)) {
			System.out.println("wait termination...");
		}
		System.out.println("terminated");
	}
}
