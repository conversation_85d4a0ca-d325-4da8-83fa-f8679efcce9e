package com.buzz.java.concurrent;


import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @description
 **/
public class HashMapTest {

    public static void main(String[] args) throws InterruptedException {
        final DateCache cache = new DateCache();
        int workCount = 50;
        ExecutorService executorService = Executors.newFixedThreadPool(workCount);
        final CountDownLatch latch = new CountDownLatch(workCount);
        for (int i = 0; i < workCount; ++i) {
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    for (int k = 0; k < 5000; ++k) {
                        try {
                            cache.get(String.valueOf(k));
                        } catch (Exception e) {
                            e.printStackTrace();
                        }
                        Thread.currentThread().yield();
                    }
                    latch.countDown();
                }
            });
        }
        latch.await();
        for(String v: cache.getMap().values()){
            System.out.println(v);
        }
        executorService.shutdown();
    }

    private static class DateCache {
        public Map<String, String> map = new HashMap<String, String>();

        public Map<String, String> getMap() {
            return map;
        }

        public String get(String key) {
            String date = map.get(key);
            if (date == null) {
                synchronized (map) {
                    date = Thread.currentThread().getName() + "\t" + new Date().toString();
                    map.put(key, date);
                }
            }
            return date;
        }
    }
}
