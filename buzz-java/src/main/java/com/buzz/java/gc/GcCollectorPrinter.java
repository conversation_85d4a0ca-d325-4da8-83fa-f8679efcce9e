package com.buzz.java.gc;

import java.io.IOException;
import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.util.List;

public class GcCollectorPrinter {

	public static void main(String[] args) throws IOException {
		List<GarbageCollectorMXBean> beans = ManagementFactory.getGarbageCollectorMXBeans();
		for (GarbageCollectorMXBean bean : beans) {
			System.out.println(bean.getName());
		}
		
		//System.in.read();
	}
}
