package com.buzz.java.nio.server;

import java.io.IOException;
import java.nio.channels.SelectionKey;
import java.nio.channels.ServerSocketChannel;
import java.nio.channels.SocketChannel;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 处理 Connection
 * 
 * <AUTHOR>
 *
 */
public class AcceptThread extends AbstractThread {

	private static final Logger log = LoggerFactory.getLogger(AcceptThread.class);

	private final SelectionKey acceptKey;

	private volatile boolean stopped = false;

	private final ServerSocketChannel acceptSocket;

	private final Collection<SelectorThread> selectorThreads;

	private Iterator<SelectorThread> selectorIterator;

	public AcceptThread(ServerSocketChannel ss, List<SelectorThread> selectorThreads, String name) throws IOException {
		super(name);
		this.acceptSocket = ss;
		this.acceptKey = acceptSocket.register(selector, SelectionKey.OP_ACCEPT);
		this.selectorThreads = Collections.unmodifiableList(new ArrayList<SelectorThread>(selectorThreads));
		selectorIterator = this.selectorThreads.iterator();
	}

	@Override
	public void run() {
		log.info(Thread.currentThread().getName() + " starting...");

		while (!stopped && !acceptSocket.socket().isClosed()) {
			try {
				acceptSelect();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
		log.info("accept thread exitted run method");
	}

	private boolean doAccept() throws IOException {
		SocketChannel sc = acceptSocket.accept();
		sc.configureBlocking(false);

		// Round-robin assign this connection to a selector thread
		if (!selectorIterator.hasNext()) {
			selectorIterator = selectorThreads.iterator();
		}

		SelectorThread selectorThread = selectorIterator.next();
		selectorThread.addAcceptedConnection(sc);
		return true;
	}

	public void acceptSelect() throws IOException {
		selector.select();
		Iterator<SelectionKey> selectedKeys = selector.selectedKeys().iterator();

		while (!stopped && selectedKeys.hasNext()) {

			SelectionKey key = selectedKeys.next();
			selectedKeys.remove();

			if (!key.isValid()) {
				continue;
			}
			if (key.isAcceptable()) {
				if (!doAccept()) {
					// If unable to pull a new connection off the accept
					// queue, pause accepting to give us time to free
					// up file descriptors and so the accept thread
					// doesn't spin in a tight loop.
					pauseAccept(10);
				}
			} else {
				log.warn("Unexpected ops in accept select " + key.readyOps());
			}

		}
	}

	private void pauseAccept(long millisecs) {
		log.info("pauseAccept");
		acceptKey.interestOps(0);
		try {
			selector.select(millisecs);
		} catch (IOException e) {
			// ignore
		} finally {
			acceptKey.interestOps(SelectionKey.OP_ACCEPT);
		}
	}
}
