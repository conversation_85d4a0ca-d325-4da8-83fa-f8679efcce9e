package com.buzz.java.nio.scalable.io;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.ServerSocketChannel;
import java.nio.channels.SocketChannel;
import java.util.Iterator;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class Reactor implements Runnable {

	Selector selector;

	ServerSocketChannel serverSocket;

	public Reactor(int port) throws IOException {
		selector = Selector.open();
		serverSocket = ServerSocketChannel.open();
		serverSocket.socket().bind(new InetSocketAddress(port));
		serverSocket.configureBlocking(false);

		SelectionKey sk = serverSocket.register(selector, SelectionKey.OP_ACCEPT);
		sk.attach(new Acceptor());
	}
	public static void main(String[] args) throws IOException {
		new Reactor(8864).run();
	}
	@Override
	public void run() {
		try {
			while (!Thread.interrupted()) {
				selector.select();
				Set<SelectionKey> selected = selector.selectedKeys();
				Iterator<SelectionKey> it = selected.iterator();
				while (it.hasNext()) {
					dispatch(it.next()); //dispatch分发事件
				}
				selected.clear();
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	void dispatch(SelectionKey k) {
		Acceptor r = (Acceptor) (k.attachment()); //调用SelectionKey绑定的调用对象
		if (r != null)
			r.run();
	}

	class Acceptor implements Runnable {

		public Acceptor() {
			log.info("Acceptor init");
		}

		@Override
		public void run() {
			try {
				SocketChannel c = serverSocket.accept();
				if (c != null) {
					log.info("handle {}", c);
					//new Handler(selector, c);
				}
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	}
}
