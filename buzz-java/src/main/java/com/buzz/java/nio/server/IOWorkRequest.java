package com.buzz.java.nio.server;

import java.io.IOException;
import java.nio.channels.SelectionKey;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 请求任务
 * <AUTHOR>
 *
 */
public class IOWorkRequest implements Runnable {

	private static final Logger log = LoggerFactory.getLogger(IOWorkRequest.class);

	private SelectorThread selectorThread;

	private final SelectionKey key;

	private final Channel channel;

	public IOWorkRequest(SelectorThread selectorThread, SelectionKey key) {
		super();
		this.selectorThread = selectorThread;
		this.key = key;
		channel = (Channel) key.attachment();
	}

	@Override
	public void run() {
		log.info("exeucte io request key.isValid " + key.isValid());
		if (!key.isValid()) {
			// selectorThread.cleanupSelectionKey(key);
			key.cancel();
			return;
		}

		if (key.isReadable() || key.isWritable()) {
			try {
				channel.doIO(key);
			} catch (Exception e) {
				e.printStackTrace();
			}

			if (!key.isValid()) {
				key.cancel();
				return;
			}
		}

		// Mark this connection as once again ready for selection
		channel.enableSelectable();
		// Push an update request on the queue to resume selecting
		// on the current set of interest ops, which may have changed
		// as a result of the I/O operations we just performed.
		if (!selectorThread.addInterestOpsUpdateRequest(key)) {
			try {
				channel.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

}
