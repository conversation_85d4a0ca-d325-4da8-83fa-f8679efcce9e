package com.buzz.java.nio.client;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.SocketChannel;
import java.util.Iterator;
import java.util.Set;

import lombok.extern.slf4j.Slf4j;

/**
 * https://blog.csdn.net/baiye_xing/article/details/73136119
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class NioClient {

	public static void main(String[] args) throws IOException, InterruptedException {
		NioClientHandler h = new NioClientHandler("localhost", 8080);
		h.start();
		
		Thread.sleep(1000);
		while(true) {
			h.sendPing();
			Thread.sleep(1000);
		}
	}

	private static class NioClientHandler implements Runnable {

		private volatile boolean stop;

		private final Selector selector;

		private InetSocketAddress addr;

		private SelectionKey sockKey;

		private Thread thread;
		
		private SocketChannel sock; 

		public NioClientHandler(String host, int port) throws IOException {
			this.addr = new InetSocketAddress(host, port);
			selector = Selector.open();
		}

		public void start() {
			thread = new Thread(this);
			thread.start();
		}

		protected void doConnectAndRegister(SocketChannel sock, InetSocketAddress addr) throws IOException {
			//通过ip和端口号连接到服务器
			if (sock.connect(addr)) {
				//向多路复用器注册可读事件
				sock.register(selector, SelectionKey.OP_READ);
			} else {
				sockKey = sock.register(selector, SelectionKey.OP_CONNECT);
			}

		}

		public void sendPing() throws IOException {
			//要写的内容 
			byte[] req = "ping".getBytes();
			//为字节缓冲区分配指定字节大小的容量
			ByteBuffer writeBuffer = ByteBuffer.allocate(req.length);
			//将内容写入缓冲区
			writeBuffer.put(req);
			//反转缓冲区
			writeBuffer.flip();
			int dataLength = writeBuffer.remaining();

			//将内容写入管道中
			sock.write(writeBuffer);
			if (!writeBuffer.hasRemaining()) {
				//若缓冲区中无可读字节，则说明成功发送给服务器消息
				log.info("Send to server {} byte succeed.", dataLength);
			}
		}

		protected void handleInput(SelectionKey key) throws IOException {
			//若该selectorkey可用
			if (key.isValid()) {
				//将key转型为SocketChannel
				SocketChannel sc = (SocketChannel) key.channel();
				//判断是否连接成功
				if (key.isConnectable()) {
					//若已经建立连接
					if (sc.finishConnect()) {
						//向多路复用器注册可读事件
						sc.register(selector, SelectionKey.OP_READ);
						//向管道写数据
						//sendPing(sc);
					} else {
						//连接失败 进程退出
						System.exit(1);
					}
				}
				//若是可读的事件
				if (key.isReadable()) {
					ByteBuffer readBuffer = ByteBuffer.allocate(512);
					//从管道中读取数据然后写入缓冲区中
					int readBytes = sc.read(readBuffer);
					//若有数据
					if (readBytes > 0) {
						//反转缓冲区
						readBuffer.flip();

						byte[] bytes = new byte[readBuffer.remaining()];
						//获取缓冲区并写入字节数组中
						readBuffer.get(bytes);
						//将字节数组转换为String类型
						String body = new String(bytes);
						log.info("receive: " + body + "!");
					} else if (readBytes < 0) {
						key.cancel();
						sc.close();
					} else {
						sc.register(selector, SelectionKey.OP_READ);
					}
				}

			}
			//disableWrite();
		}

		protected SocketChannel createSocketChannel() throws IOException {
			SocketChannel sock = SocketChannel.open();
			sock.configureBlocking(false);
			sock.socket().setSoLinger(false, -1);
			sock.socket().setTcpNoDelay(true);
			return sock;
		}

		protected void close(SelectionKey key) throws IOException {
			//取消selectionkey
			key.cancel();
			if (key.channel() != null) {
				key.channel().close();
			}
		}

		@Override
		public void run() {
			try {
				sock = createSocketChannel();
				doConnectAndRegister(sock, addr);
			} catch (Exception e) {
				e.printStackTrace();
			}
			while (!stop) {
				//阻塞等待1s，若超时则返回
				try {
					selector.select(1000);
					//获取所有selectionkey
					Set<SelectionKey> selected = selector.selectedKeys();
					Iterator<SelectionKey> it = selected.iterator();
					SelectionKey key = null;
					while (it.hasNext()) {
						key = it.next();
						//获取之后删除
						it.remove();
						try {
							handleInput(key);
						} catch (Exception e) {
							if (key != null) {
								close(key);
							}
						}
					}
					selected.clear();
					//Thread.sleep(1000);

				} catch (Exception e) {
					e.printStackTrace();
					System.exit(1);
				}
			}
		}

//		private synchronized void disableWrite() {
//			int i = sockKey.interestOps();
//			if ((i & SelectionKey.OP_WRITE) != 0) {
//				sockKey.interestOps(i & (~SelectionKey.OP_WRITE));
//			}
//		}

	}

}
