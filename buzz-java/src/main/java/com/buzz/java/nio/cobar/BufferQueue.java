package com.buzz.java.nio.cobar;

import java.nio.ByteBuffer;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 
 * 自己实现的 BlockBufferQueue，基于数组，通过维护take,put两个index实现。
 *
 */
public class BufferQueue {

	private int takeIndex;
	private int putIndex;
	private int count;

	private final ByteBuffer[] items;
	private final ReentrantLock lock;
	private final Condition notFull;
	private ByteBuffer attachment;

	public BufferQueue(int capacity) {
		items = new ByteBuffer[capacity];
		lock = new ReentrantLock();
		notFull = lock.newCondition();
	}

	public ByteBuffer attachment() {
		return attachment;
	}

	public void attach(ByteBuffer buffer) {
		this.attachment = buffer;
	}

	public int size() {
		final ReentrantLock lock = this.lock;
		lock.lock();
		try {
			return count;
		} finally {
			lock.unlock();
		}
	}

	public void put(ByteBuffer buffer) throws InterruptedException {
		final ByteBuffer[] items = this.items;
		final ReentrantLock lock = this.lock;
		lock.lockInterruptibly();

		try {
			try {
				while (count == items.length) {
					notFull.await();
				}
			} catch (InterruptedException e) {
				notFull.signal();
				throw e;
			}
			insert(buffer);
		} finally {
			lock.unlock();
		}
	}

	public ByteBuffer poll() {
		final ReentrantLock lock = this.lock;
		lock.lock();
		try {
			if (count == 0) {
				return null;
			}
			return extract();
		} finally {
			lock.unlock();
		}
	}

	private ByteBuffer extract() {
		ByteBuffer buffer = items[takeIndex];
		items[takeIndex] = null;
		takeIndex = inc(takeIndex);
		--count;
		notFull.signal();
		return buffer;
	}

	private void insert(ByteBuffer buffer) {
		items[putIndex] = buffer;
		putIndex = inc(putIndex);
		++count;
	}

	private int inc(int i) {
		return (++i == items.length) ? 0 : i;
	}

	public static void main(String[] args) throws InterruptedException {
		final BufferQueue bq = new BufferQueue(4);

		new Thread(() -> {
			ByteBuffer d = null;
			while((d=bq.poll())!=null) {
				d.flip();
				System.out.println(d.getInt());
				d.clear();
			}
		}).start();
		for (int i = 0; i < 10; ++i) {
			ByteBuffer data = ByteBuffer.allocate(4);
			data.putInt(i);
			bq.put(data);
		}
	}
}
