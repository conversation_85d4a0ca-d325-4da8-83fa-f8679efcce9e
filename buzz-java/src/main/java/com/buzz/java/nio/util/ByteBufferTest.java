package com.buzz.java.nio.util;

import org.junit.Test;

import java.nio.ByteBuffer;

public class ByteBufferTest {

    @Test
    public void testBase() {
        ByteBuffer buffer = ByteBuffer.allocate(10);// 分配10个byte
        System.out.println(
                String.format("pos=%s,limit=%s,remain=%s", buffer.position(), buffer.limit(), buffer.remaining()));// pos=0，limit=10，remaining=10

        buffer.put((byte) 1);
        buffer.put((byte) 1);
        buffer.put((byte) 1);

        System.out.println(
                String.format("pos=%s,limit=%s,remain=%s", buffer.position(), buffer.limit(), buffer.remaining())); //pos=3，limit=10，remaining=7

        System.out.println(buffer.get());//无法读到之前写入的数据

        System.out.println(
                String.format("pos=%s,limit=%s,remain=%s", buffer.position(), buffer.limit(), buffer.remaining())); //pos=4，limit=10，remaining=7

        buffer.flip();
        System.out.println(
                String.format("pos=%s,limit=%s,remain=%s", buffer.position(), buffer.limit(), buffer.remaining()));//pos=0，limit=4，remaining=4

        System.out.println(buffer.get());
        System.out.println(buffer.get());
        System.out.println(buffer.get());
        System.out.println(buffer.get());
        System.out.println(buffer.get());//抛出异常
    }

    @Test
    public void testWriteAndRead() {

        ByteBuffer bb = ByteBuffer.allocate(4);
        bb.putChar('h');
        bb.putChar('e');
        System.out.println(String.format("pos=%s,limit=%s,remain=%s", bb.position(), bb.limit(), bb.remaining()));
        //调用flip才能read
        bb.flip();

        System.out.println(bb.getChar());
        System.out.println(bb.getChar());

    }

    @Test
    public void testReadString() {

        ByteBuffer bb = ByteBuffer.wrap("he".getBytes());
        System.out.println(String.format("pos=%s,limit=%s,remain=%s", bb.position(), bb.limit(), bb.remaining()));
        System.out.println(bb.remaining());
        System.out.println(String.format("pos=%s,limit=%s,remain=%s", bb.position(), bb.limit(), bb.remaining()));
        byte[] dist = new byte[bb.remaining()];
        bb.get(dist);
        System.out.println(new String(dist));

        // 注意，不能用ByteBuffer.getChar()读
    }

    public static void main(String[] args) {

        ByteBuffer bb = ByteBuffer.allocate(8);
        bb.putChar(4, 'h');// 写入2字节
        System.out.println(String.format("pos=%s,limit=%s,remain=%s", bb.position(), bb.limit(), bb.remaining()));

        System.out.println("=====wrap=========");
        bb = ByteBuffer.wrap(bb.array());
        System.out.println(String.format("pos=%s,limit=%s,remain=%s", bb.position(), bb.limit(), bb.remaining()));
        bb.putInt(10);// 覆盖数据

        bb.rewind();// 读取数据
        System.out.println(bb.getInt());
        System.out.println(bb.getChar());

    }
}
