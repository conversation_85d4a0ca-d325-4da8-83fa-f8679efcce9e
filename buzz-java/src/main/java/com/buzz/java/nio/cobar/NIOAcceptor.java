package com.buzz.java.nio.cobar;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.ServerSocketChannel;

public final class NIOAcceptor extends Thread {

	private final int port;
	private final Selector selector;
	private final ServerSocketChannel serverChannel;
	
	
	 public NIOAcceptor(String name, int port) throws IOException {
		  super.setName(name);
		  this.port = port;
		  this.selector = Selector.open();
		  this.serverChannel = ServerSocketChannel.open();
		  this.serverChannel.bind(new InetSocketAddress(port));
		  this.serverChannel.configureBlocking(false);
		  this.serverChannel.register(selector, SelectionKey.OP_ACCEPT);
	 }
}
