package com.buzz.java.nio.server;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.SocketChannel;
import java.util.Queue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.atomic.AtomicBoolean;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 通道，封装了读写操作
 * 
 * <AUTHOR>
 *
 */
public class Channel {

	private static final Logger log = LoggerFactory.getLogger(IOWorkRequest.class);

	private ByteBuffer buffer = ByteBuffer.allocate(4 << 10);

	private final AtomicBoolean selectable = new AtomicBoolean(true);

	private final Queue<ByteBuffer> outgoingBuffers = new LinkedBlockingQueue<ByteBuffer>();

	private final SocketChannel sock;

	private final SelectionKey sk;

	private final SelectorThread selectorThread;

	public Channel(SocketChannel sock, SelectionKey sk, SelectorThread selectorThread) {
		super();
		this.sock = sock;
		this.sk = sk;
		this.selectorThread = selectorThread;
	}

	public boolean isSelectable() {
		return sk.isValid() && selectable.get();
	}

	public void disableSelectable() {
		selectable.set(false);
	}

	public void enableSelectable() {
		selectable.set(true);
	}

	private void requestInterestOpsUpdate() {
		if (isSelectable()) {
			selectorThread.addInterestOpsUpdateRequest(sk);
		}
	}

	private final AtomicBoolean throttled = new AtomicBoolean(false);

	// Throttle acceptance of new requests. If this entailed a state change,
	// register an interest op update request with the selector.
	public void disableRecv() {
		if (throttled.compareAndSet(false, true)) {
			requestInterestOpsUpdate();
		}
	}

	// Disable throttling and resume acceptance of new requests. If this
	// entailed a state change, register an interest op update request with
	// the selector.
	public void enableRecv() {
		if (throttled.compareAndSet(true, false)) {
			requestInterestOpsUpdate();
		}
	}

	// returns whether we are interested in taking new requests, which is
	// determined by whether we are currently throttled or not
	private boolean getReadInterest() {
		return !throttled.get();
	}

	// returns whether we are interested in writing, which is determined
	// by whether we have any pending buffers on the output queue or not
	private boolean getWriteInterest() {
		return !outgoingBuffers.isEmpty();
	}

	public int getInterestOps() {
		if (!isSelectable()) {
			return 0;
		}
		int interestOps = 0;
		if (getReadInterest()) {
			interestOps |= SelectionKey.OP_READ;
		}
		if (getWriteInterest()) {
			interestOps |= SelectionKey.OP_WRITE;
		}
		return interestOps;
	}

	public SelectionKey getSk() {
		return sk;
	}

	public SocketChannel getSock() {
		return sock;
	}

	public void close() throws IOException {
		sk.cancel();
		sock.close();
	}

	private void readPayload() throws IOException, InterruptedException {

		int rc = sock.read(buffer);
		if (rc < 0) {
			throw new RuntimeException("Unable to read additional data from client likely client has closed socket");
		}
		log.info(String.format("pos=%s,limit=%s,remain=%s", buffer.position(), buffer.limit(), buffer.remaining()));// pos=0，limit=10，remaining=10
		buffer.flip();
		log.info(String.format("pos=%s,limit=%s,remain=%s", buffer.position(), buffer.limit(), buffer.remaining()));
		String str = new String(buffer.array(), buffer.position(), buffer.limit());
		log.info("readPayload " + str);
		// System.in.read();

		buffer.rewind();
		while (buffer.hasRemaining()) {
			System.out.print(buffer.get() + "_");
		}
		System.out.println();
		buffer.clear();
	}

	public void doIO(SelectionKey k) throws InterruptedException, IOException {

		if (k.isReadable()) {

			readPayload();

		} else if (k.isWritable()) {
			handleWrite(k);
		}
	}

	private void handleWrite(SelectionKey k) throws IOException{
		if (outgoingBuffers.isEmpty()) {
			return;
		}
	}

	public void sendResponse() {
		byte[] array = "HTTP/1.1 200 OK".getBytes();
		ByteBuffer buffer = ByteBuffer.wrap(array);
		outgoingBuffers.add(buffer);
		requestInterestOpsUpdate();
	}
}
