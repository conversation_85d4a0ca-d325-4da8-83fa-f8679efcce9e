package com.buzz.java.nio.client;

import org.junit.Test;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.nio.channels.SocketChannel;

public class SocketChannelTest {

	/**
	 * 测试 SocketChannel.connect() timeout
	 * @throws IOException 
	 */
	@Test
	public void testConnectTimeout() throws IOException {
		SocketChannel socketChannel = SocketChannel.open();
		socketChannel.configureBlocking(false);
		Socket socket = socketChannel.socket();
        socket.setKeepAlive(true);
        socket.setTcpNoDelay(true);
		boolean ret = socketChannel.connect(new InetSocketAddress("*************",443));
		System.out.println(ret);
	}


}
