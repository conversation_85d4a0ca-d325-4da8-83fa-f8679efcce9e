package com.buzz.java.nio.reactor;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.SocketChannel;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ReadEventHandler implements EventHandler {

	@Override
	public void handleEvent(Selection<PERSON>ey key) throws IOException {
		log.info("ReadEventHandler execute " + key);

		ByteBuffer buffer = ByteBuffer.allocate(64);
		SocketChannel client = (SocketChannel) key.channel();
		client.read(buffer);

		if (new String(buffer.array()).trim().equals("bye")) {
			 client.close();
			 System.out.println("Not accepting client messages anymore");
		}else {
			buffer.flip();
            client.write(buffer);
            buffer.clear();
		}

	}

}
