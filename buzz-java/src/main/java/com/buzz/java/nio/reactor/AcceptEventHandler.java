package com.buzz.java.nio.reactor;

import java.io.IOException;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.ServerSocketChannel;
import java.nio.channels.SocketChannel;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class AcceptEventHandler implements EventHandler {

	private Selector selector;
	private ServerSocketChannel serverSocket;

	public AcceptEventHandler(Selector selector, ServerSocketChannel serverSocket) {
		super();
		this.selector = selector;
		this.serverSocket = serverSocket;
	}

	@Override
	public void handleEvent(SelectionKey key) throws IOException {
		log.info("AcceptEventHandler execute");
		SocketChannel client = serverSocket.accept();
		client.configureBlocking(false);
		client.register(selector, SelectionKey.OP_READ);
	}

}
