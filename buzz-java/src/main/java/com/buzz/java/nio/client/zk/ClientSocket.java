package com.buzz.java.nio.client.zk;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.SocketChannel;
import java.util.Set;
import java.util.concurrent.LinkedBlockingDeque;

import com.buzz.java.nio.client.zk.NioClientZk.Packet;
import com.buzz.java.nio.client.zk.NioClientZk.SendThread;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class ClientSocket {

	protected final ByteBuffer lenBuffer = ByteBuffer.allocateDirect(4);

	/**
	 * After the length is read, a new incomingBuffer is allocated in
	 * readLength() to receive the full message.
	 */
	protected ByteBuffer incomingBuffer = lenBuffer;

	protected long now;

	protected long lastSend;

	protected long lastHeard;

	protected boolean initialized;

	private SelectionKey sockKey;

	private Selector selector;

	private SendThread sendThread;

	protected LinkedBlockingDeque<Packet> outgoingQueue;

	public ClientSocket() throws IOException {
		selector = Selector.open();
	}

	public void attach(SendThread sendThread, LinkedBlockingDeque<Packet> outgoingQueue) {
		this.sendThread = sendThread;
		this.outgoingQueue = outgoingQueue;
	}

	private Packet findSendablePacket(LinkedBlockingDeque<Packet> outgoingQueue) {
		if (outgoingQueue.isEmpty()) {
			return null;
		}
		return outgoingQueue.getFirst();
	}

	private synchronized void disableWrite() {
		int i = sockKey.interestOps();
		if ((i & SelectionKey.OP_WRITE) != 0) {
			sockKey.interestOps(i & (~SelectionKey.OP_WRITE));
		}
	}

	private synchronized void enableWrite() {
		int i = sockKey.interestOps();
		if ((i & SelectionKey.OP_WRITE) == 0) {
			sockKey.interestOps(i | SelectionKey.OP_WRITE);
		}
	}

	void doIO() throws IOException {
		SocketChannel sock = (SocketChannel) sockKey.channel();
		if (sock == null) {
			throw new IOException("Socket is null!");
		}
		if (sockKey.isReadable()) {
			int rc = sock.read(incomingBuffer);
			if (rc < 0) {
				throw new IOException(
						"Unable to read additional data from server "
								+ ", likely server has closed socket");
			}

			if (!incomingBuffer.hasRemaining()) {
				incomingBuffer.flip();
				byte[] bytes = new byte[incomingBuffer.remaining()];
				//获取缓冲区并写入字节数组中
				incomingBuffer.get(bytes);
				//将字节数组转换为String类型
				String body = new String(bytes);
				System.out.println("receive: " + body + "!");
				updateLastHeard();
				incomingBuffer.clear();
			}

		}
		if (sockKey.isWritable()) {
			updateLastSend();
			Packet p = findSendablePacket(outgoingQueue);
			if (p != null) {
				updateLastSend();
				byte[] req = "ping".getBytes();
				ByteBuffer writeBuffer = ByteBuffer.allocate(req.length);
				//将内容写入缓冲区
				writeBuffer.put(req);
				//反转缓冲区
				writeBuffer.flip();
				int dataLength = writeBuffer.remaining();

				sock.write(writeBuffer);
				if (!writeBuffer.hasRemaining()) {
					//若缓冲区中无可读字节，则说明成功发送给服务器消息
					log.info("Send to server {} byte succeed.", dataLength);
					outgoingQueue.removeFirstOccurrence(p);
				}
			}
			if (outgoingQueue.isEmpty()) {
				disableWrite();
			} else {
				enableWrite();
			}

		}

	}

	void doTransport(int waitTimeOut) throws IOException {
		if (sendThread.getState().isConnected()) {
			if (!outgoingQueue.isEmpty()) {
				enableWrite();
			}
		}
		selector.select(waitTimeOut);
		Set<SelectionKey> selected = selector.selectedKeys();
		updateNow();

		for (SelectionKey k : selected) {
			SocketChannel sc = ((SocketChannel) k.channel());

			//判断是否连接成功
			if (k.isConnectable()) {
				//若已经建立连接
				if (sc.finishConnect()) {
					//注册读写事件
					sockKey.interestOps(SelectionKey.OP_READ | SelectionKey.OP_WRITE);
					updateLastSendAndHeard();
					sendThread.primeConnection();
				}

			} else if ((k.readyOps() & (SelectionKey.OP_READ | SelectionKey.OP_WRITE)) != 0) {
				doIO();
			}
		}

		

		selected.clear();
	}

	boolean isConnected() {
		return sockKey != null;
	}

	private SocketChannel createSocketChannel() throws IOException {
		SocketChannel sock = SocketChannel.open();
		sock.configureBlocking(false);
		sock.socket().setKeepAlive(true);
		//设置linger为false，以便在socket关闭时不会阻塞
		sock.socket().setSoLinger(false, -1);
		sock.socket().setTcpNoDelay(true);
		return sock;
	}

	void registerAndConnect(SocketChannel sock, InetSocketAddress addr) throws IOException {
		sockKey = sock.register(selector, SelectionKey.OP_CONNECT);
		boolean immediateConnect = sock.connect(addr);
		if (immediateConnect) {
			sendThread.primeConnection();
		}
	}

	void connect(InetSocketAddress addr) throws IOException {
		SocketChannel sock = createSocketChannel();
		try {
			registerAndConnect(sock, addr);
		} catch (Exception e) {
			log.error("Unable to open socket to " + addr);
			sock.close();
			throw e;
		}
		initialized = true;

	}

	int getIdleSend() {
		return (int) (now - lastSend);
	}

	int getIdleRecv() {
		return (int) (now - lastHeard);
	}

	void updateNow() {
		now = System.currentTimeMillis();
	}

	void updateLastSend() {
		lastSend = now;
	}

	void updateLastSendAndHeard() {
		this.lastSend = now;
		this.lastHeard = now;
	}

	void updateLastHeard() {
		this.lastHeard = now;
	}
}
