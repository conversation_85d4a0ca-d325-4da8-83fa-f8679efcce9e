package com.buzz.java.nio.cobar;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.SocketChannel;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

public abstract class AbstractConnection implements NIOConnection {

	private static final int OP_NOT_READ = ~SelectionKey.OP_READ;
	private static final int OP_NOT_WRITE = ~SelectionKey.OP_WRITE;

	protected final ReentrantLock keyLock;
	protected final ReentrantLock writeLock;

	//状态
	//是否已关闭
	protected final AtomicBoolean isClosed;
	//是否已注册
	protected volatile boolean isRegistered;

	protected final SocketChannel channel;
	protected SelectionKey processKey;
	protected BufferQueue writeQueue;

	protected long startupTime;
	protected long lastReadTime;
	protected long lastWriteTime;

	//发送的byte数
	protected long netOutBytes;

	protected int writeAttempts;

	//处理器
	protected NIOProcessor processor;

	public AbstractConnection(SocketChannel channel) {
		this.channel = channel;
		this.keyLock = new ReentrantLock();
		this.writeLock = new ReentrantLock();

		this.isClosed = new AtomicBoolean(false);
		this.startupTime = System.currentTimeMillis();
		this.lastReadTime = startupTime;
		this.lastWriteTime = startupTime;
	}

	public BufferQueue getWriteQueue() {
		return writeQueue;
	}

	public void setWriteQueue(BufferQueue writeQueue) {
		this.writeQueue = writeQueue;
	}

	@Override
	public void register(Selector selector) throws IOException {
		try {
			processKey = channel.register(selector, SelectionKey.OP_READ, this);
			isRegistered = true;
		} finally {
			if (isClosed.get()) {
				clearSelectionKey();
			}
		}
	}

	/**
	 * 打开读事件
	 */
	public void enableRead() {
		final Lock lock = this.keyLock;
		lock.lock();
		try {
			SelectionKey key = this.processKey;
			key.interestOps(key.interestOps() | SelectionKey.OP_READ);
		} finally {
			lock.unlock();
		}
		processKey.selector().wakeup();
	}

	/**
	 * 打开写事件
	 */
	private void enableWrite() {
		final Lock lock = this.keyLock;
		lock.lock();
		try {
			SelectionKey key = this.processKey;
			key.interestOps(key.interestOps() | SelectionKey.OP_WRITE);
		} finally {
			lock.unlock();
		}
		processKey.selector().wakeup();
	}

	/**
	 * 关闭写事件
	 */
	private void disableWrite() {
		final Lock lock = this.keyLock;
		lock.lock();
		try {
			SelectionKey key = this.processKey;
			key.interestOps(key.interestOps() & OP_NOT_WRITE);
		} finally {
			lock.unlock();
		}
		processKey.selector().wakeup();
	}

	/**
	* 关闭读事件
	*/
	public void disableRead() {
		final Lock lock = this.keyLock;
		lock.lock();
		try {
			SelectionKey key = this.processKey;
			key.interestOps(key.interestOps() & OP_NOT_READ);
		} finally {
			lock.unlock();
		}
	}

	@Override
	public void writeByQueue() throws IOException {
		if (isClosed.get()) {
			return;
		}

		final ReentrantLock lock = this.writeLock;
		lock.lock();

		try {
			// 满足以下两个条件时，切换到基于事件的写操作。
			// 1.当前key对写事件不该兴趣。
			// 2.write0()返回false。
			if ((processKey.interestOps() & SelectionKey.OP_WRITE) == 0 && !write0()) {
				enableWrite();
			}
		} finally {
			lock.unlock();
		}
	}

	@Override
	public void writeByEvent() throws IOException {
		if (isClosed.get()) {
			return;
		}
		final ReentrantLock lock = this.writeLock;
		lock.lock();
		try {
			// 满足以下两个条件时，切换到基于队列的写操作。
			// 1.write0()返回true。
			// 2.发送队列的buffer为空。
			if (write0() && writeQueue.size() == 0) {
				disableWrite();
			}
		} finally {
			lock.unlock();
		}
	}

	@Override
	public void write(ByteBuffer buffer) {
		if (isClosed.get()) {
			processor.getBufferPool().recycle(buffer);
			return;
		}

		if (isRegistered) {
			try {
				writeQueue.put(buffer);
			} catch (Exception e) {
				error(ErrorCode.ERR_PUT_WRITE_QUEUE, e);
				return;
			}
			processor.postWrite(this);
		} else {
			processor.getBufferPool().recycle(buffer);
			close();
		}
	}

	private boolean write0() throws IOException {
		// 检查是否有遗留数据未写出
		ByteBuffer buffer = writeQueue.attachment();
		if (buffer != null) {
			int written = channel.write(buffer);
			if (written > 0) {
				netOutBytes += written;
				processor.addNetOutBytes(written);
			}
			lastWriteTime = System.currentTimeMillis();
			if (buffer.hasRemaining()) {
				writeAttempts++;
				return false;
			} else {
				writeQueue.attach(null);
				processor.getBufferPool().recycle(buffer);
			}
		}
		// 写出发送队列中的数据块
		if ((buffer = writeQueue.poll()) != null) {
			// 如果是一块未使用过的buffer，则执行关闭连接。
			if (buffer.position() == 0) {
				processor.getBufferPool().recycle(buffer);
				close();
				return true;
			}
			buffer.flip();
			//写入到channel
			int written = channel.write(buffer);
			if (written > 0) {
				netOutBytes += written;
				processor.addNetOutBytes(written);
			}
			lastWriteTime = System.currentTimeMillis();
			if (buffer.hasRemaining()) {
				writeQueue.attach(buffer);
				writeAttempts++;
				return false;
			} else {
				processor.getBufferPool().recycle(buffer);
			}
		}
		return true;
	}

	private void clearSelectionKey() {
		final Lock lock = this.keyLock;
		lock.lock();
		try {
			SelectionKey key = this.processKey;
			if (key != null && key.isValid()) {
				key.attach(null);
				key.cancel();
			}
		} finally {
			lock.unlock();
		}
	}
}
