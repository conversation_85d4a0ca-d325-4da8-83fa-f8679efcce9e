package com.buzz.java.nio.channel;

import org.junit.Test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.FileChannel.MapMode;
import java.nio.file.Files;
import java.nio.file.StandardOpenOption;

/**
 * <AUTHOR>
 * @description
 **/
public class MappedByteBufferTest {

    @Test
    public void testTemp() throws IOException {
        File file = new File("/tmp/mmap");
        file.createNewFile();

        FileChannel fileChannel = new RandomAccessFile(file, "rw").getChannel();
        MappedByteBuffer buffer = fileChannel.map(MapMode.READ_WRITE,0,1024*100);
        System.out.println(buffer.limit());
    }

    @Test
    public void testRead() throws Exception {
        //Leaf:0 NoneLeaf:1

        File file = new File("/tmp/btree-test", "index-0");
        int segmentSizeBytes = 50 * 1024 * 1024;
        try (RandomAccessFile raf = new RandomAccessFile(file, "rw")) {
            raf.setLength(segmentSizeBytes);
        }

        FileChannel fileChannel = (FileChannel) Files.newByteChannel(file.toPath(),
                StandardOpenOption.READ);

        // map the whole file
        MappedByteBuffer bb = fileChannel.map(MapMode.READ_ONLY, 0, segmentSizeBytes);
        MappedByteBufferWrapper mbb = new MappedByteBufferWrapper(bb,0);

        int position = (int) (mbb.getLong());//read root position,397
        System.out.println("position=" + position);
        mbb.position(position);
        System.out.println(mbb.get()); //read type
        System.out.println(mbb.get()); //read key num
        position = (int)mbb.getLong(); //read key position
        System.out.println("key ="+mbb.getInt()); //read key
        System.out.println("value position="+mbb.getLong()); //read value position
        System.out.println("child key position=" + position);

        mbb.position(position);
        System.out.println(mbb.get()); //read child type
        System.out.println(mbb.get()); //read child key num
        position = (int)mbb.getLong(); //read child key position
        System.out.println("child key value="+mbb.getInt()); //read child key value

        System.out.println("child child position=" + position);



    }

    static class MappedByteBufferWrapper {
        MappedByteBuffer bb;
        int position;

        public MappedByteBufferWrapper(MappedByteBuffer bb, int position) {
            this.bb = bb;
            this.position = position;
        }

        public void position(int newPosition) {
            this.position = newPosition;
        }

        public byte get() {
            bb.position(position++);
            return bb.get();
        }

        public long getLong() {
            int p = position;
            position += Long.BYTES;
            bb.position(p);
            return bb.getLong();
        }

        public int getInt() {
            int p = position;
            position += Integer.BYTES;
            bb.position(p);
            return bb.getInt();
        }
    }
}
