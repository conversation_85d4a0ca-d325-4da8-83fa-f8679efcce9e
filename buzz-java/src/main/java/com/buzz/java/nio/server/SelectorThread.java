package com.buzz.java.nio.server;

import java.io.IOException;
import java.nio.channels.SelectionKey;
import java.nio.channels.SocketChannel;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Iterator;
import java.util.Queue;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 处理IO事件线程
 * <AUTHOR>
 *
 */
public class SelectorThread extends AbstractThread {

	private static final Logger log = LoggerFactory.getLogger(SelectorThread.class);

	private final Queue<SocketChannel> acceptedQueue;

	private final Queue<SelectionKey> updateQueue;

	private NioServer nioServer;

	public SelectorThread(int id, NioServer nioServer) throws IOException {
		super("selector-thread-" + id);
		this.nioServer = nioServer;
		acceptedQueue = new LinkedBlockingQueue<SocketChannel>();
		updateQueue = new LinkedBlockingQueue<SelectionKey>();
	}

	public boolean addAcceptedConnection(SocketChannel accepted) {
		log.info("new client accepted " + accepted);
		if (nioServer.stopped || !acceptedQueue.offer(accepted)) {
			return false;
		}
		selector.wakeup();
		return true;
	}

	@Override
	public void run() {
		log.info(Thread.currentThread().getName() + " starting...");
		while (!nioServer.stopped) {
			try {
				select();
				processAcceptedConnections();
				processInterestOpsUpdateRequests();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	private Channel createChannel(SocketChannel accepted, SelectionKey key) {
		Channel channel = new Channel(accepted, key, this);
		return channel;
	}

	private void processAcceptedConnections() {
		SocketChannel accepted;
		while (!nioServer.stopped && (accepted = acceptedQueue.poll()) != null) {
			SelectionKey key = null;
			try {
				key = accepted.register(selector, SelectionKey.OP_READ); // 注册
				Channel cnxn = createChannel(accepted, key);
				key.attach(cnxn);
				log.info("new client register OP_READ event, key=" + key);
			} catch (IOException e) {
				e.printStackTrace();
				// register, createConnection
				// cleanupSelectionKey(key);
				// fastCloseSock(accepted);
			}
		}
	}

	private void handleIO(SelectionKey key) {
		log.info("handleIO key=" + key + ", interestOps=" + key.interestOps());
		Channel channel = (Channel) key.attachment();
		key.interestOps(0);
		channel.disableSelectable();
		IOWorkRequest request = new IOWorkRequest(this, key);
		nioServer.workerpool.schedule(request);
	}

	private void select() throws IOException {
		selector.select();
		Set<SelectionKey> selected = selector.selectedKeys();
		ArrayList<SelectionKey> selectedList = new ArrayList<SelectionKey>(selected);
		Collections.shuffle(selectedList);
		Iterator<SelectionKey> selectedKeys = selectedList.iterator();

		while (!nioServer.stopped && selectedKeys.hasNext()) {
			SelectionKey key = selectedKeys.next();
			selected.remove(key);

			if (!key.isValid()) {
				key.cancel();
				continue;
			}
			if (key.isReadable() || key.isWritable()) {
				handleIO(key);
			} else {
				log.warn("Unexpected ops in select " + key.readyOps());
			}
		}
	}

	private void processInterestOpsUpdateRequests() {
		SelectionKey key;
		while (!nioServer.stopped && (key = updateQueue.poll()) != null) {
			if (!key.isValid()) {
				key.cancel();
			}
			Channel channel = (Channel) key.attachment();
			if (channel.isSelectable()) {
				int ops = channel.getInterestOps();
				log.info("re-interestOps,ops=" + ops);
				key.interestOps(ops);
			}
		}
	}

	/**
	 * Place interest op update requests onto a queue so that only the selector
	 * thread modifies interest ops, because interest ops reads/sets are potentially
	 * blocking operations if other select operations are happening.
	 */
	public boolean addInterestOpsUpdateRequest(SelectionKey sk) {
		log.info("addInterestOpsUpdateRequest key=" + sk.interestOps());
		if (nioServer.stopped || !updateQueue.offer(sk)) {
			return false;
		}
		selector.wakeup();
		return true;
	}

}
