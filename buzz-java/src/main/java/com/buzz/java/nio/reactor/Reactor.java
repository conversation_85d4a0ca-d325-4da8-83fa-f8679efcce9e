package com.buzz.java.nio.reactor;

import java.io.IOException;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.ServerSocketChannel;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;


/**
 * http://afghl.github.io/2016/12/17/java-nio-02-reactor-and-nio.html
 * 
 * 一个简单的Reactor实现
 * 
 * <AUTHOR>
 *
 */
public class Reactor {

	private Map<Integer, EventHandler> registeredHandlers = new ConcurrentHashMap<>();

	private Selector selector;//多路分配器

	public Reactor(Selector selector) throws IOException {
		this.selector = selector;
	}

	public void registerEventHandler(
			int eventType, EventHandler eventHandler) {
		registeredHandlers.put(eventType, eventHandler);
	}

	public void registerChannel(int eventType, ServerSocketChannel serverChannel) throws Exception {
		serverChannel.register(selector, eventType);
	}

	public void run() {
		try {
			while (true) { // Loop indefinitely
				int readyChannels = selector.selectNow();
				if (readyChannels == 0)
					continue;

				Set<SelectionKey> selectedKeys = selector.selectedKeys();
				Iterator<SelectionKey> keyIterator = selectedKeys.iterator();

				while (keyIterator.hasNext()) {

					SelectionKey key = keyIterator.next();

					if (key.isAcceptable()) {
						EventHandler handler = registeredHandlers.get(SelectionKey.OP_ACCEPT);
						handler.handleEvent(key);
						
					} else if (key.isReadable()) {
						EventHandler handler = registeredHandlers.get(SelectionKey.OP_READ);
						handler.handleEvent(key);
						
					} else if (key.isWritable()) {
						EventHandler handler = registeredHandlers.get(SelectionKey.OP_WRITE);
						handler.handleEvent(key);
						
					}
					keyIterator.remove();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

}
