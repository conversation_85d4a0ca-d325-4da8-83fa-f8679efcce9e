package com.buzz.java.nio.channel;

import org.junit.Test;

import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.FileChannel.MapMode;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <p>
 *  测试FileChannel，用法：
 *  FileChannel fileChannel = new RandomAccessFile(file, "rw").getChannel();
 *</p>
 *
 * 性能测试看耗时意义不大，最佳实践还是通过 iostat 1 看磁盘监控
 * Mac Pro这块盘使用Disk Speed Test测试过顺序写峰值是12XXmb/s上下浮动
 * buf_size设置不同对性能影响比较大：
 * buf_size为4k写入610mb~800mb/s
 * buf_size为16k可以达到峰值，1204mb/s
 *
 *
 */
public class FileChannelTest {
    private static int buf_size = 16 * 1024;
    private static long _GB = 1 * 1024 * 1024 * 1024l;

    // 方法一: 4kb 刷盘 耗时2~3秒
    @Test
    public void testWrite4k() throws Exception {
        long begin = System.currentTimeMillis();
        FileChannel fileChannel = new RandomAccessFile("/data/tmp/buzz/1.log", "rw").getChannel();
        ByteBuffer byteBuffer = ByteBuffer.allocateDirect(buf_size);
        for (int i = 0; i < buf_size; i++) {
            byteBuffer.put((byte) 0);
        }
        for (int i = 0; i < _GB; i += buf_size) {
            byteBuffer.position(0);
            byteBuffer.limit(buf_size);
            fileChannel.write(byteBuffer);
        }
        long end = System.currentTimeMillis();
        System.out.println("cost:" + (end - begin));


    }


    //方法二: 单字节刷盘，耗时>5分钟
    @Test
    public void testWrite1B() throws Exception {
        long begin = System.currentTimeMillis();
        FileChannel fileChannel = new RandomAccessFile("/data/tmp/buzz/2.log", "rw").getChannel();
        ByteBuffer byteBuffer = ByteBuffer.allocateDirect(1);
        byteBuffer.put((byte) 0);
        for (int i = 0; i < _GB; i++) {
            byteBuffer.position(0);
            byteBuffer.limit(1);
            fileChannel.write(byteBuffer);
        }
        long end = System.currentTimeMillis();
        System.out.println("cost:" + (end - begin));
    }

    //方法三: 单字节刷盘，通过mmap，耗时3~4秒
    @Test
    public void testWrite1BWithMMP() throws Exception {
        long begin = System.currentTimeMillis();
        FileChannel fileChannel = new RandomAccessFile("/data/tmp/buzz/3.log", "rw").getChannel();
        MappedByteBuffer map = fileChannel.map(MapMode.READ_WRITE, 0, _GB);
        for (int i = 0; i < _GB; i++) {
            map.put((byte) 0);
        }
        long end = System.currentTimeMillis();
        System.out.println("cost:" + (end - begin));
    }


    //方法四：4kb 刷盘，但每次都force，耗时36秒
    @Test
    public void testWrite4kWithForce() throws Exception {
        long begin = System.currentTimeMillis();
        FileChannel fileChannel = new RandomAccessFile("/data/tmp/buzz/1.log", "rw").getChannel();
        ByteBuffer byteBuffer = ByteBuffer.allocateDirect(buf_size);
        for (int i = 0; i < buf_size; i++) {
            byteBuffer.put((byte) 0);
        }
        for (int i = 0; i < _GB; i += buf_size) {
            byteBuffer.position(0);
            byteBuffer.limit(buf_size);
            fileChannel.write(byteBuffer);
            fileChannel.force(true);
        }
        long end = System.currentTimeMillis();
        System.out.println("cost:" + (end - begin));
    }

    // 方法五: 多文件并行刷盘，小于2秒
    @Test
    public void testWrite4kConcurrent() throws Exception {
        int threadCount = 4;
        CountDownLatch latch = new CountDownLatch(threadCount);
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);

        long begin = System.currentTimeMillis();
        for (int i = 0; i < threadCount; ++i) {
            final int threadId = i;
            executor.submit(() -> {
                try {
                    FileChannel fileChannel = new RandomAccessFile("/data/tmp/buzz/bucket/" + threadId + ".log", "rw").getChannel();

                    ByteBuffer byteBuffer = ByteBuffer.allocateDirect(buf_size);
                    for (int j = 0; j < buf_size; j++) {
                        byteBuffer.put((byte) 0);
                    }

                    for (int k = 0; k < _GB / threadCount; k += buf_size) {
                        byteBuffer.position(0);
                        byteBuffer.limit(buf_size);
                        fileChannel.write(byteBuffer);
                    }

                    latch.countDown();
                } catch (Exception e) {
                    e.printStackTrace();
                }

            });
        }
        latch.await();
        long end = System.currentTimeMillis();
        System.out.println("cost:" + (end - begin));
    }


}
