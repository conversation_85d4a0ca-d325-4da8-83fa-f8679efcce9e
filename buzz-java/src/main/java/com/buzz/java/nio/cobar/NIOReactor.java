package com.buzz.java.nio.cobar;

import java.io.IOException;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.util.Set;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

import org.eclipse.jetty.util.BlockingArrayQueue;

import lombok.extern.slf4j.Slf4j;

/**
 * 网络事件反应器
 *
 */
@Slf4j
public class NIOReactor {

	private final String name;
	private final R reactorR;
	private final W reactorW;

	public NIOReactor(String name) throws IOException {
		this.name = name;
		this.reactorR = new R();
		this.reactorW = new W();
	}

	final void startup() {
		new Thread(reactorR, name + "-R").start();
		new Thread(reactorW, name + "-W").start();
	}

	final void postWrite(NIOConnection c) {
        reactorW.writeQueue.offer(c);
    }
	
	private final class R implements Runnable {
		private final Selector selector;
		private long reactCount;
		private final BlockingQueue<NIOConnection> registerQueue;

		public R() throws IOException {
			this.selector = Selector.open();
			this.registerQueue = new LinkedBlockingQueue<NIOConnection>();
		}

		@Override
		public void run() {
			final Selector selector = this.selector;

			for (;;) {
				++reactCount;
				try {
					selector.select(1000L);
					//把con注册到 selector
					register(selector);
					Set<SelectionKey> keys = selector.selectedKeys();
					for (SelectionKey key : keys) {
						Object att = key.attachment();
						System.out.println("attachment:" + att);
						if (key.isValid()) {
							int readyOps = key.readyOps();
							if ((readyOps & SelectionKey.OP_READ) != 0) {
								read((NIOConnection) att);
							} else if ((readyOps & SelectionKey.OP_WRITE) != 0) {
								write((NIOConnection) att);
							} else {
								key.cancel();
							}
						} else {
							key.cancel();
						}
					}

				} catch (Exception e) {
				}
			}
		}

		private void register(Selector selector) {
			NIOConnection c = null;
			while ((c = registerQueue.poll()) != null) {
				try {
					c.register(selector);
				} catch (Exception e) {
					c.error(ErrorCode.ERR_REGISTER, e);
				}
			}
		}

		private void read(NIOConnection c) {
			try {
				c.read();
			} catch (Throwable e) {
				c.error(ErrorCode.ERR_READ, e);
			}
		}

		private void write(NIOConnection c) {
			try {
				c.writeByEvent();
			} catch (Throwable e) {
				c.error(ErrorCode.ERR_WRITE_BY_EVENT, e);
			}
		}

	}

	private final class W implements Runnable {

		private final BlockingQueue<NIOConnection> writeQueue;

		public W() {
			writeQueue = new BlockingArrayQueue<NIOConnection>();
		}

		@Override
		public void run() {
			NIOConnection c = null;
			for (;;) {
				try {
					if ((c = writeQueue.take()) != null) {
						write(c);
					}
				} catch (Exception e) {
					log.error(name, e);
				}
			}
		}

		public void write(NIOConnection c) {
			try {
				c.writeByQueue();
			} catch (Throwable e) {
				c.error(ErrorCode.ERR_WRITE_BY_QUEUE, e);
			}
		}

	}
}
