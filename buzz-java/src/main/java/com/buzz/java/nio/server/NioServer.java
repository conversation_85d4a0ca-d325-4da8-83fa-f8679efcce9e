package com.buzz.java.nio.server;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.nio.channels.ServerSocketChannel;
import java.util.ArrayList;
import java.util.List;

public class NioServer {

	public volatile boolean stopped = true;

	public WorkerPool workerpool = new WorkerPool();

	public void start() throws IOException {
		stopped = false;
		ServerSocketChannel ss = ServerSocketChannel.open();
		ss.socket().bind(new InetSocketAddress("localhost", 8080));
		ss.configureBlocking(false);
		int size = 2;
		
		List<SelectorThread> selectorThreads = new ArrayList<SelectorThread>();
		for (int i = 0; i < size; ++i) {
			SelectorThread st = new SelectorThread(i + 1, this);
			selectorThreads.add(st);
		}
		AcceptThread accpect = new AcceptThread(ss, selectorThreads, "AccpectThread");

		for (SelectorThread thread : selectorThreads) {
			if (thread.getState() == Thread.State.NEW) {
				thread.start();
			}
		}

		accpect.start();
	}

	public static void main(String[] args) throws IOException {

		NioServer server = new NioServer();
		server.start();
	}
}
