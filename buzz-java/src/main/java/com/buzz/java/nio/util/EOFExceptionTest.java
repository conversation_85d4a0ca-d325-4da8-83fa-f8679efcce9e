package com.buzz.java.nio.util;

import java.io.DataInputStream;
import java.io.IOException;
import java.net.InetSocketAddress;
import java.net.ServerSocket;
import java.net.Socket;

/*
 * 重现 EOFException
 */
public class EOFExceptionTest {

	public static void main(String[] args) throws IOException {
		new DemoServer().start();
		Socket socket = new Socket();
		socket.setTcpNoDelay(true);
		socket.setSoTimeout(3000);
		socket.connect(new InetSocketAddress("localhost", 8080), 3000);

		try {
			DataInputStream input = new DataInputStream(socket.getInputStream());
			System.out.println(input.read());
			System.out.println(input.readInt());
			socket.close();
		} catch (Exception e) {
			System.out.println(socket.isClosed());
			e.printStackTrace();
		}
	}

	static class DemoServer extends Thread {

		@Override
		public void run() {
			try {
				ServerSocket ss = new ServerSocket(8080);
				System.out.println("DemoServer bind 8080");
				while (true) {
					Socket s = ss.accept();
					System.out.println("accpect :" + s + " close it");
					s.close();
					break;
				}
				ss.close();

			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
}
