package com.buzz.java.nio.client.zk;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.Random;
import java.util.concurrent.LinkedBlockingDeque;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class NioClientZk {

	private volatile boolean closing = false;

	private int readTimeout = 2000;

	private final LinkedBlockingDeque<Packet> outgoingQueue = new LinkedBlockingDeque<Packet>();

	public void start() throws IOException {
		ClientSocket sock = new ClientSocket();
		SendThread sendThread = new SendThread(new InetSocketAddress("localhost", 8080), sock);
		sendThread.start();
	}

	public static void main(String[] args) throws IOException {
		new NioClientZk().start();
	}

	class SendThread implements Runnable {

		//状态
		private States state = States.NOT_CONNECTED;

		private Random r = new Random(System.nanoTime());

		private boolean isFirstConnect = true;

		private volatile boolean stop;

		private ClientSocket clientSocket;

		private InetSocketAddress addr;

		public SendThread(InetSocketAddress addr, ClientSocket clientCnxn) {
			this.addr = addr;
			this.clientSocket = clientCnxn;

		}

		public States getState() {
			return state;
		}

		public void primeConnection() {
			log.info("socket 连接成功");
			isFirstConnect = false;
			onConnected();
			outgoingQueue.addFirst(new Packet());
		}

		private void onConnected() {
			state = States.CONNECTED;
		}

		private void startConnect() throws IOException {
			state = States.CONNECTING;
			//避免大量客户端同时连接
			if (!isFirstConnect) {
				try {
					Thread.sleep(r.nextInt(1000));
				} catch (InterruptedException e) {
					log.warn("Unexpected exception", e);
				}
			}

			clientSocket.connect(addr);
		}

		private void sendPing() {
			outgoingQueue.addFirst(new Packet());
		}

		public void start() {
			Thread t = new Thread(this);
			t.start();
		}

		@Override
		public void run() {
			clientSocket.attach(this, outgoingQueue);
			clientSocket.updateNow();
			clientSocket.updateLastSendAndHeard();

			int to = 0;
			int timeToNextPing = 0;
			while (!stop) {
				log.info("IdleSend:{}, IdleRecv:{}, to:{}, timeToNextPing:{}", clientSocket.getIdleSend(),
						clientSocket.getIdleRecv(), to, timeToNextPing);
				try {
					//如果clientSocket还没有连接，开始连接
					if (!clientSocket.isConnected()) {
						// don't re-establish connection if we are closing
						if (closing) {
							break;
						}
						startConnect();
						//clientCnxnSocket.updateLastSendAndHeard();

					}
					if (state.isConnected()) {
						to = readTimeout - clientSocket.getIdleRecv();

						timeToNextPing = readTimeout / 2 - clientSocket.getIdleSend();
						
						if (timeToNextPing <= 0) {
							log.info("timeToNextPing is {}, to is {}, [ready sendPing] ", timeToNextPing, to);
							sendPing();
							clientSocket.updateLastSend();
						} else {
							if (timeToNextPing < to) {
								to = timeToNextPing;
							}
							if (to < 0) {
								log.info("error! ConnectTimeout to is {}", to);
								System.exit(-1);
							}
						}
					}

					clientSocket.doTransport(to);

				} catch (Exception e) {
					e.printStackTrace();
				}

			}
		}
	}

	static enum States {
		NOT_CONNECTED, CONNECTING, CONNECTED, CLOSED;

		public boolean isConnected() {
			return this == CONNECTED;
		}
	}

	static class Packet {
		int code;
	}
}
