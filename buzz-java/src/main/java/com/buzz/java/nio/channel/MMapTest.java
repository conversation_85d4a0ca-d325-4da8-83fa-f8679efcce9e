package com.buzz.java.nio.channel;

import org.junit.Test;

import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.FileChannel.MapMode;
import java.util.Random;

public class MMapTest {

    private static int _GB = 1024 * 1024 * 1024;

    /**
     * This is a test for write and read the long value
     * @throws IOException
     */
    @Test
    public void testReadAndWriteLong() throws IOException {
        //write 1000 numbers
        FileChannel fileChannel = new RandomAccessFile("/data/tmp/buzz/test.dvd", "rw").getChannel();
        MappedByteBuffer mp = fileChannel.map(MapMode.READ_WRITE, 0, _GB);
        long base = 10000000;
        for (int i = 0; i < 1000; ++i) {
            mp.putLong(base + i);
        }

        // read  0-999 value
        Random random = new Random();
        int index = random.nextInt(1000);
        int position = index * Long.BYTES;
        mp.position(position);
        long value = mp.getLong();
        System.out.println(String.format("read long[%s] from mmp position:%s. value is %s", index, position, value));
    }
}
