package com.buzz.java.nio.reactor;

import java.net.InetSocketAddress;
import java.nio.channels.SelectionKey;
import java.nio.channels.Selector;
import java.nio.channels.ServerSocketChannel;

/**
 * 基于Reactor实现一个简单的 EchoServer
 * 
 * https://www.baeldung.com/java-nio-selector
 * 
 * <AUTHOR>
 *
 */
public class ReactorTest {

	public static void main(String[] args) throws Exception {
		ServerSocketChannel server = ServerSocketChannel.open();
		server.socket().bind(new InetSocketAddress(8080));
		server.configureBlocking(false);

		Selector selector = Selector.open();
		Reactor reactor = new Reactor(selector);
		reactor.registerChannel(SelectionKey.OP_ACCEPT, server);

		reactor.registerEventHandler(
				SelectionKey.OP_ACCEPT, new AcceptEventHandler(selector, server));

		reactor.registerEventHandler(
				SelectionKey.OP_READ, new ReadEventHandler());

		reactor.registerEventHandler(
				SelectionKey.OP_WRITE, new WriteEventHandler());

		reactor.run();
	}
}
