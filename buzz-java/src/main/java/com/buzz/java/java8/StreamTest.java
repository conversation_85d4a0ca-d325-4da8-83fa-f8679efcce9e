package com.buzz.java.java8;

import java.util.List;

import com.google.common.collect.Lists;

public class StreamTest {

	public static void main(String[] args) {
		List<String> list = Lists.newArrayList();
		list.add("a");
		list.add("b");
		list.add("c");

		boolean ret = list.stream().anyMatch(s -> {
			System.out.println(s);
			return s.equals("b");
		});

		System.out.println("ret=" + ret);
		
		
		

	}
}
