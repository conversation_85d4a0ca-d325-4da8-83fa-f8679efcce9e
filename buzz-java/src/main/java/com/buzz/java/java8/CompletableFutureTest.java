package com.buzz.java.java8;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.persistence.jaxb.compiler.facets.MinFacet;
import org.junit.Test;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

/**
 * CompletableFuture 顾名思义，表示可以完成的Future。 CompletableFuture 即是java的Promise
 * https://www.baeldung.com/java-completablefuture
 */
@Slf4j
public class CompletableFutureTest {


    static class MockAsyncHttp {
        public static CompletableFuture<String> invoke() {
            CompletableFuture<String> cf = new CompletableFuture<>();
            cf.complete("{\"code\":1}");
            return cf;
        }
    }

    @Test
    public void test101() {
        MockAsyncHttp.invoke()
                .thenApply((s) -> {
                    log.info("parse string {}", s);
                    return JSON.parseObject(s);
                })
                .thenAccept((json) -> {
                    log.info("receive {}", json);
                });
    }

    /**
     * thenRun: 不关心上一个阶段的结果，也不返回新的结果，只是执行一个 Runnable。
     * thenApply: 处理上一个阶段的结果，并返回一个新的结果。
     * thenAccept: 处理上一个阶段的结果，但不返回新的结果。
     */
    @Test
    public void testChain() {
        // origin->first->second
        CompletableFuture<String> origin = new CompletableFuture();
        CompletableFuture<String> first = origin.thenApply((s) -> {
            log.info("first accept {}", s);
            return s.toUpperCase();
        });

        first.thenAccept((s) -> {
            log.info("second accept {}", s);
        });

        origin.complete("response");//通知必须是原始的
        //可以简写为
        //origin.thenApply().thenAccept();
    }

    private Future<String> calculateAsync(boolean error) {
        CompletableFuture<String> cf = new CompletableFuture();
        cf.whenComplete((v, t) -> {
            log.info("we receive response:" + v);
            log.info("we receive error:" + t);
        });
        cf.whenComplete((v, t) -> {
            log.info("2 we receive response:" + v);
            log.info("2 we receive error:" + t);
        });
        new Thread(() -> {
            try {
                Thread.sleep(500);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            if (error) {
                cf.completeExceptionally(new IllegalStateException("fault error!"));
            } else {
                cf.complete("Hello ok");
            }
        }).start();
        return cf;
    }


    /**
     * 可以通过 cf.whenComplete() 添加监听器
     */
    @Test
    public void testWatchResult() throws InterruptedException, ExecutionException {
        calculateAsync(false).get();
        calculateAsync(true).get();
    }

    @Test
    public void testSupplyAsync() throws InterruptedException {
        //创建异步执行任务:
        CompletableFuture<Double> cf = CompletableFuture.supplyAsync(CompletableFutureTest::fetchPrice);

        // 如果执行成功:
        cf.thenAccept((result) -> {
            System.out.println("price: " + result);
        });
        // 如果执行异常:
        cf.exceptionally((e) -> {
            e.printStackTrace();
            return null;
        });
        CompletableFuture.allOf(cf).join();
        System.out.println("finish");
        // 主线程不要立刻结束，否则CompletableFuture默认使用的线程池会立刻关闭:
        Thread.sleep(1000);
    }

    static Double fetchPrice() {
        try {
            Thread.sleep(100);
        } catch (InterruptedException e) {
        }
        if (Math.random() < 0.01) {
            throw new RuntimeException("fetch price failed!");
        }
        System.out.println("fetchPrice end");
        return 5 + Math.random() * 20;
    }
}
