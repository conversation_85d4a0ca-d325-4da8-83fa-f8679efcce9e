package com.buzz.java.generic;

import java.lang.reflect.Method;
import java.util.Arrays;

public class GenericTypeTest {
    public static void main(String[] args) {
        GenericTypeTest.P p = new GenericTypeTest.S();
//		p.test(new Object());

        Class<?> clazz = GenericTypeTest.S.class;
        Method[] methods = clazz.getMethods();
        for (Method method : methods) {
            if (method.getName().startsWith("test")) {
                System.out.println(
                        method.getName() + ":" + Arrays.toString(method.getParameterTypes()) + method.isBridge());
            }
        }
    }

    static class P<T> {
        public T test(T t) {
            return t;
        }
    }

    static class S extends GenericTypeTest.P<String> {
        @Override
        public String test(String t) {
            return t;
        }
    }
}
