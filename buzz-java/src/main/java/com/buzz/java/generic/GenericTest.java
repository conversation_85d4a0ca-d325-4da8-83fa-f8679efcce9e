package com.buzz.java.generic;

import org.junit.Test;

/**
 *
 */
public class GenericTest {


    @Test
    public void testGenericInherit() {
        StringToNumberConverterFactory factory = null;
        factory.getConvert(Integer.class).convert("100");
        factory.getConvert(Float.class).convert("100.01");
    }


    static class BaseModule {

        public <T1 extends T4, T2, T3, T4> void test(Converter<T1, T2> converter, Class<T4> type) {

        }
    }

    interface Converter<S, R> {
        public R convert(S source);
    }


    interface ConverterFactory<S, R> {

        //结果返回参数的子类
        public <T extends R> Converter<S, R> getConvert(Class<T> type);
    }


    class StringToNumberConverterFactory implements ConverterFactory<String, Number> {

        @Override
        public <T extends Number> Converter<String, Number> getConvert(Class<T> type) {

            return null;
        }
    }

    class StringToNumberConverter implements Converter<String, Number> {

        private Class<?> targetType;

        @Override
        public Number convert(String source) {
            //根据 targetType 不同做类型转化
            return null;
        }
    }
}
