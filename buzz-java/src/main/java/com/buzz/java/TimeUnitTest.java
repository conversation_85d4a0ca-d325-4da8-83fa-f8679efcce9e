package com.buzz.java;

import org.junit.Test;

import java.util.concurrent.TimeUnit;

public class TimeUnitTest {

    @Test
    public void test() {
        //MILLISECONDS 转 NANOSECONDS
        System.out.println(TimeUnit.MILLISECONDS.toNanos(24978));
        System.out.println(TimeUnit.NANOSECONDS.convert(24978, TimeUnit.MILLISECONDS));

        System.out.println("==========");
        System.out.println(secondToAnyTime(TimeUnit.MILLISECONDS, 1));//1000毫秒
        System.out.println(secondToAnyTime(TimeUnit.MINUTES, 120));//2分钟
        System.out.println(secondToAnyTime(TimeUnit.HOURS, 3000));//不到一小时
        System.out.println(secondToAnyTime(TimeUnit.HOURS, 3600));//一小时




    }

    @Test
    public void test2() {
        //SECONDS -> MILLISECONDS
        System.out.println(TimeUnit.SECONDS.toMillis(1));
        System.out.println(TimeUnit.MILLISECONDS.convert(1, TimeUnit.SECONDS));
    }

    /**
     * 秒转化为任意时间单位
     *
     * @param timeUnit
     * @param duration
     * @return
     */
    public long secondToAnyTime(TimeUnit timeUnit, long duration) {
        return timeUnit.convert(duration, TimeUnit.SECONDS);
    }
}
