package com.buzz.java.regex;

import org.junit.Test;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Stream;

import static org.junit.Assert.*;

public class RegexTest {


    /**
     * 匹配以xxx开头的字符串
     */
    @Test
    public void testWordBegin() {
        String[] words = {"middle-","middle-abc","middle123","middleware","middle","mid","mmiddle"};
        Stream.of(words).forEach(word -> {
            boolean ret = match("^middle.*", word);
            System.out.println(word+" "+ret);
        });

    }

    public static int runTest(String regex, String text) {
        Pattern pattern = Pattern.compile(regex);
        Matcher matcher = pattern.matcher(text);
        System.out.println(matcher.matches());
        int matches = 0;
        while (matcher.find()) {
            matches++;
        }
        return matches;
    }

    @Test
    public void testMatches() {
        int matches = runTest("foo", "foofoo");
        assertEquals(matches, 2);
        matches = runTest(".", "foofoo");
        assertEquals(matches, 6);
    }

    @Test
    public void testORSet() {
        int matches = runTest("[abc]", "b");
        assertEquals(matches, 1);

        matches = runTest("[abc]", "cab");
        assertEquals(matches, 3);

        matches = runTest("[bcr]at", "bat cat rat");
        assertEquals(matches, 3);
    }

    @Test
    public void givenNORSet_whenMatchesNon_thenCorrect() {
        int matches = runTest("[^abc]", "g");
        assertEquals(matches, 1);

        matches = runTest("[^bcr]at", "sat mat eat");
        assertTrue(matches > 0);
    }

    @Test
    public void givenTwoSets_whenMatchesIntersection_thenCorrect() {
        // && 取交集intersection
        int matches = runTest("[1-6&&[3-9]]", "123456789");
        //3,4,5,6
        assertEquals(matches, 4);
    }

    @Test
    public void testWacaiDomain() {
//		String pattern = "(http|imaps|https)://\\S+.(wacai|caimi-inc)\\S+";
        String pattern = "^(https|imaps|http)://.*(wacai|caimi).*";

        match(pattern, "https://uav.wacai.info/");
        match(pattern, "https://uav.wacai.info/dashboard");
        match(pattern, "https://uav.test.wacai.info/dashboard");
        match(pattern, "http://twork.wacai.info/#category3");
        match(pattern, "https://mail.wacai.com/?cus=1");
        match(pattern, "https://git.caimi-inc.com/?cus=1");
        match(pattern, "http://waes.staging.wacai.info/serverless-runtime/fn/conf-web-faas/staging?ticket=ST-38388-XDWc5KgaP5TsA-YOWmr6wkP3vhc-web-4-108-hzifc");

        unMatch(pattern, "https://abc.baidu.com");
        unMatch(pattern, "https://mail.taobao.com");
        unMatch(pattern, "https://xxx.com");
    }

    private boolean match(String regex, String text) {
        Pattern pattern = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        return pattern.matcher(text).matches();
        //assertEquals(result, true);
    }

    private void unMatch(String pattern, String text) {
        boolean ret = Pattern.matches(pattern, text);
        assertEquals(ret, false);
    }

    @Test
    public void whenStudyMethodsWork_thenCorrect() {
        Pattern pattern = Pattern.compile("dog");
        Matcher matcher = pattern.matcher("dogs are friendly");

        assertTrue(matcher.lookingAt());
        assertFalse(matcher.matches());
    }
}
