package com.buzz.java.regex;

import org.junit.Test;

import java.util.regex.Pattern;

public class PatternTest {

    private static final String IP_PATTERN = "\\d{1,3}(\\.\\d{1,3}){3,5}$";

    private static final String TEST_PATTERN = "\"^[0-9]+(.[0-9]{2})?$";

    @Test
    public void testPath() {

        String pattern = "/hermes-proxy/send\\w*";
        matches(pattern, "/hermes-proxy/sendSingle");
        matches(pattern, "/hermes-proxy2/sendSingle");
        matches(pattern, "/hermes-proxy/publish");

        String pattern2 = "/hermes-proxy/fetch";
    }

    private boolean matches(String regex, String input) {
        Pattern pattern = Pattern.compile(regex);
        boolean ret = pattern.matcher(input).matches();
        System.out.println(regex + " " + input + " " + ret);
        return ret;
    }

    public static void main(String[] args) {

        //System.out.println(Pattern.matches("\\w+-*.*/*\\w+", "jack.com/jack"));
        //System.out.println(IP_PATTERN);
//		System.out.println(Pattern.matches(TEST_PATTERN, "1"));
//		System.out.println(Pattern.matches(TEST_PATTERN, "1.01"));

        String pattern = "[a-z0-9A-Z/\\.\\-]+(:){1}[a-z0-9A-Z\\-\\.]+$";
        System.out.println(Pattern.matches(pattern, "docker.io/istio/examples-bookinfo-ratings-v1:1.15.1"));
        System.out.println(Pattern.matches(pattern, "a:1.15.1"));
        System.out.println(Pattern.matches(pattern, "b:1"));
        System.out.println(Pattern.matches(pattern, "b:laset"));
        System.out.println(Pattern.matches(pattern, "test"));
        System.out.println(Pattern.matches(pattern, "123423"));

//		Matcher m = Pattern.compile(pattern).matcher(input);
//		int count = 0;
//		while (m.find()) {
//			count++;
//			System.out.println("Match number " + count);
//			System.out.println("start(): " + m.start());
//			System.out.println("end(): " + m.end());
//		}
    }


}
