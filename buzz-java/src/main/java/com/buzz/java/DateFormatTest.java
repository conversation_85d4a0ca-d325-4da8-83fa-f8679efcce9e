package com.buzz.java;

import org.junit.Test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;

public class DateFormatTest {


    private static SimpleDateFormat getDateFormat(String pattern) {
        return new SimpleDateFormat(pattern, Locale.CHINA);
    }

    /**
     * 测试带时区的格式
     */
    @Test
    public void testWithTimezone() throws ParseException {

        Date now = new Date();
        SimpleDateFormat format1 = getDateFormat("yyyy-MM-dd'T'HH:mm.SSSXXX");//ISO 8601 timezone
        SimpleDateFormat format2 = getDateFormat("yyyy-MM-dd'T'HH:mm.SSSZ");//RFC 822 timezone
        System.out.println(format1.format(now));
        System.out.println(format2.format(now));

        String str = "2023-09-13T16:44.836+08:00";
        System.out.println( format1.parse(str));

    }
}
