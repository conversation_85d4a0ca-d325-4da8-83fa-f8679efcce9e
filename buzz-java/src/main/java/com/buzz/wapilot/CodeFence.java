package com.buzz.wapilot;


import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Data
public class CodeFence {
    private final String language;
    private final String text;
    private boolean isComplete;

    private static final Pattern devinStartRegex = Pattern.compile("<devin>");
    private static final Pattern devinEndRegex = Pattern.compile("</devin>");
    private static final Pattern languageRegex = Pattern.compile("\\s*```([\\w#+ ]*)");
    private static final Pattern devinRegexBlock = Pattern.compile("(?<=^|\\n)```devin\\n([\\s\\S]*?)\\n```\\n");
    private static final Pattern normalCodeBlock = Pattern.compile("\\s*```([\\w#+ ]*)\\n");

    public CodeFence(String language, String text, boolean isComplete) {
        this.language = language;
        this.text = text;
        this.isComplete = isComplete;
    }


    public static CodeFence parse(String content) {
        Matcher startMatch = devinStartRegex.matcher(content);
        if (startMatch.find()) {
            Matcher endMatch = devinEndRegex.matcher(content);
            boolean isComplete = endMatch.find();
            String devinContent = isComplete ?
                    content.substring(startMatch.end(), endMatch.start()).trim() :
                    content.substring(startMatch.end()).trim();

            return new CodeFence("devin", devinContent, isComplete);
        }

        boolean codeStarted = false;
        boolean codeClosed = false;
        String languageId = null;
        StringBuilder codeBuilder = new StringBuilder();

        String[] lines = content.split("\n");
        for (String line : lines) {
            if (!codeStarted) {
                String trimmedLine = line.trim();
                Matcher matchResult = languageRegex.matcher(trimmedLine);
                if (matchResult.find()) {//code开始
                    languageId = matchResult.group(1).trim();
                    codeStarted = true;
                }
            } else {
                String trimmedLine = line.trim();
                if ("```".equals(trimmedLine)) {
                    codeClosed = true;
                    break;
                } else {
                    codeBuilder.append(line).append("\n");
                }
            }
        }

        String trimmedCode = codeBuilder.toString().trim();
        return new CodeFence(languageId, trimmedCode.isEmpty() ? "" : trimmedCode, codeClosed);
    }


    public static List<CodeFence> parseAll(String content) {
        List<CodeFence> codeFences = new ArrayList<>();
        int currentIndex = 0;

        Matcher startMatcher = devinStartRegex.matcher(content);
        while (startMatcher.find()) {
            int startIndex = startMatcher.start();
            if (startIndex > currentIndex) {//前面有text
                String beforeText = content.substring(currentIndex, startIndex);
                if (!beforeText.isEmpty()) {
                    parseMarkdownContent(beforeText, codeFences);
                }
            }

            String searchRegion = content.substring(startIndex);
            Matcher endMatch = devinEndRegex.matcher(searchRegion);
            boolean isComplete = endMatch.find();

            String devinContent = isComplete ?
                    searchRegion.substring(startMatcher.group().length(), endMatch.start()).trim() :
                    searchRegion.substring(startMatcher.group().length()).trim();

            codeFences.add(new CodeFence("devin", devinContent, isComplete));
            currentIndex = isComplete ? startIndex + endMatch.end() : content.length();
        }

        if (currentIndex < content.length()) {
            String remainingContent = content.substring(currentIndex);
            parseMarkdownContent(remainingContent, codeFences);
        }

        return codeFences.stream()
                .filter(it -> "devin".equals(it.language) || !it.text.isEmpty())
                .collect(Collectors.toList());
    }


    private static void parseMarkdownContent(String content, List<CodeFence> codeFences) {

        String[] lines = content.split("\n");

        boolean codeStarted = false;
        String languageId = null;
        StringBuilder codeBuilder = new StringBuilder();
        StringBuilder textBuilder = new StringBuilder();

        for (String line : lines) {
            String trimmedLine = line.replaceAll("^\\s+", ""); //去除前导空格
            if (!codeStarted) {
                // Check for code block start with any indentation
                Matcher matchResult = languageRegex.matcher(trimmedLine);
                if (matchResult.find()) {
                    if (textBuilder.length() > 0) {//匹配到了code，并且之前textBuilder有数据
                        CodeFence textBlock = new CodeFence(
                                "markdown", textBuilder.toString().trim(), true
                        );
                        codeFences.add(textBlock);
                        textBuilder.setLength(0); // 清空textBuilder
                    }
                    languageId = matchResult.group(1).trim();
                    codeStarted = true;
                } else { // 说明就是普通文本，先暂存
                    textBuilder.append(line).append("\n");
                }
            } else {
                if (trimmedLine.equals("```")) {//code结束标记
                    String codeContent = codeBuilder.toString().trim();
                    CodeFence codeFence = new CodeFence(
                            languageId,
                            codeContent,
                            true
                    );
                    codeFences.add(codeFence);
                    codeBuilder.setLength(0); // 清空codeBuilder
                    codeStarted = false;
                    languageId = null;
                } else {//先暂存
                    codeBuilder.append(line).append("\n");
                }
            }
        }

        if (textBuilder.length() > 0) { // 剩余的文本
            CodeFence textBlock = new CodeFence("markdown", textBuilder.toString().trim(), true);
            codeFences.add(textBlock);
        }

        if (codeStarted && codeBuilder.length() > 0) {//剩余的code
            String code = codeBuilder.toString().trim();
            CodeFence codeFence = new CodeFence(languageId, code, false);
            codeFences.add(codeFence);
        }
    }
}