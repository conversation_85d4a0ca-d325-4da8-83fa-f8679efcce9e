package com.buzz.wapilot;

import org.junit.Test;

import java.util.List;

public class CodeFenceTest {

    @Test
    public void test101() {
        String markdown = """
根据您提供的代码库上下文信息，我将帮助您为Blog新增删除API。以下是计划的步骤：

```plan
1. 在 `BlogService.java` 中添加 `deleteBlog` 方法
   - [ ] 添加 `deleteBlog` 方法，调用 `blogRepository.deleteById(id)` 来删除指定的博客文章
2. 在 `BlogController.java` 中添加 `deleteBlog` 的 API 接口
   - [ ] 添加 `@DeleteMapping` 注解的方法 `deleteBlog`，调用 `blogService.deleteBlog(id)` 来执行删除操作
3. 在 `BlogServiceTest.java` 中添加 `deleteBlog` 的测试用例
   - [ ] 添加测试用例，验证 `deleteBlog` 方法的正确性
```

### 第一步. 在 `BlogService.java` 中添加 `deleteBlog` 方法
我将添加 `deleteBlog` 方法，该方法接受一个 `Long id` 参数，并调用 `blogRepository.deleteById(id)` 来删除指定的博客文章。

<devin>
/patch:src/main/java/com/phodal/shire/demo/service/BlogService.java
```patch
Index: src/main/java/com/phodal/shire/demo/service/BlogService.java
--- src/main/java/com/phodal/shire/demo/service/BlogService.java
+++ src/main/java/com/phodal/shire/demo/service/BlogService.java
@@ -10,6 +10,12 @@
 }

 public BlogPost updatePost(Long id, BlogPost blogDto) {
+    public void deleteBlog(Long id) {
+        blogRepository.deleteById(id);
+    }
+
     public BlogPost createBlog(BlogPost blogDto) {
```
</devin>

### 第二步. 在 `BlogController.java` 中添加 `deleteBlog` 的 API 接口
我将添加 `@DeleteMapping` 注解的方法 `deleteBlog`，该方法接受一个路径变量 `id`，并调用 `blogService.deleteBlog(id)` 来执行删除操作。

<devin>
/patch:src/main/java/com/phodal/shire/demo/controller/BlogController.java
```patch
Index: src/main/java/com/phodal/shire/demo/controller/BlogController.java
--- src/main/java/com/phodal/shire/demo/controller/BlogController.java
+++ src/main/java/com/phodal/shire/demo/controller/BlogController.java
@@ -24,6 +24,12 @@
     }

     /**
+    @DeleteMapping("/{id}")
+    public void deleteBlog(@PathVariable Long id) {
+        blogService.deleteBlog(id);
+    }
+
     @PostMapping
     public CreateBlogResponse createBlog(@RequestBody CreateBlogRequest request) {
```
</devin>

### 第三步. 在 `BlogServiceTest.java` 中添加 `deleteBlog` 的测试用例
我将添加测试用例，验证 `deleteBlog` 方法的正确性。

<devin>
/patch:src/test/java/com/phodal/shire/demo/service/BlogServiceTest.java
```patch
Index: src/test/java/com/phodal/shire/demo/service/BlogServiceTest.java
--- src/test/java/com/phodal/shire/demo/service/BlogServiceTest.java
+++ src/test/java/com/phodal/shire/demo/service/BlogServiceTest.java
@@ -27,6 +27,16 @@
     private BlogService blogService;

     @Test
+    void shouldDeleteBlog() {
+        // Given
+        Long blogId = 1L;
+        willDoNothing().given(blogRepository).deleteById(blogId);
+
+        // When
+        blogService.deleteBlog(blogId);
+
+        // Then
+        verify(blogRepository, times(1)).deleteById(blogId);
+    }
+
+    @Test
     void shouldCreateBlog() {
```
</devin>

完成以上步骤后，您可以通过以下命令启动应用程序并测试新的删除API：

```bash
./gradlew :bootRun
```                        
                """;
        List<CodeFence> codes = CodeFence.parseAll(markdown);
        System.out.println("========结果=============");
        codes.stream().forEach(i -> {
            System.out.println(i.getLanguage());
            System.out.println(i.getText());
            System.out.println("=====================");

        });
    }
}
