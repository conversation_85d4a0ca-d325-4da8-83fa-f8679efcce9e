package com.buzz.jmh;

import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.concurrent.TimeUnit;

/**
 * Java Microbenchmark Harness Test
 *
 * <AUTHOR>
 * @description
 **/
@BenchmarkMode(Mode.Throughput)
@Warmup(iterations = 3)
@Measurement(iterations = 3, time = 5, timeUnit = TimeUnit.SECONDS)
@Threads(2)
@OutputTimeUnit(TimeUnit.MILLISECONDS)
public class JMHTest {


    @Benchmark
    public  void testStringFormat() {
        long intPart = 110293249333l;
        long ymd = intPart >> 17;
        long ym = ymd >> 5;
        long hms = intPart % (1 << 17);

        String second = String.format("%04d-%02d-%02d %02d:%02d:%02d",
                (int) (ym / 13),
                (int) (ym % 13),
                (int) (ymd % (1 << 5)),
                (int) (hms >> 12),
                (int) ((hms >> 6) % (1 << 6)),
                (int) (hms % (1 << 6)));
       // System.out.println(second);
    }

    @Benchmark
    public  void testStringBuffer() {
        long intPart = 110293249333l;
        long ymd = intPart >> 17;
        long ym = ymd >> 5;
        long hms = intPart % (1 << 17);
        StringBuilder builder = new StringBuilder(26);
        builder.append(ym / 13);
        builder.append('-');
        builder.append(ym % 13);
        builder.append('-');
        builder.append(ymd % 32);
        builder.append(' ');
        builder.append(hms >> 12);
        builder.append(':');
        builder.append((hms >> 6) % (1 << 6));
        builder.append(':');
        builder.append(hms % (1 << 6));
        String second = builder.toString();
        //System.out.println(second);
    }

    public static void main(String[] args) throws RunnerException {
//        testStringFormat();
//        testStringBuffer();
        Options opt = new OptionsBuilder()
                .include(JMHTest.class.getSimpleName())
                .output("/tmp/Benchmark.log")
                .forks(1)
                .warmupIterations(5)
                .measurementIterations(5)
                .build();

        new Runner(opt).run();
        System.out.println("end");
    }
}
