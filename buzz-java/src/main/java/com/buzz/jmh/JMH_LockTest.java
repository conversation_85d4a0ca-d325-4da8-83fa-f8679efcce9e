package com.buzz.jmh;

import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

@BenchmarkMode({Mode.SampleTime})
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@Warmup(iterations=3, time = 5, timeUnit = TimeUnit.MILLISECONDS)
@Measurement(iterations=1,batchSize = 100000000)
@Threads(2)
@Fork(1)
@State(Scope.Benchmark)
public class JMH_LockTest {

    Lock lock = new ReentrantLock();
    long i = 0;
    AtomicLong atomicLong = new AtomicLong(0);

    @Benchmark
    public void measureLock() {
        lock.lock();
        i++;
        lock.unlock();
    }

    @Benchmark
    public void measureCAS() {
        atomicLong.incrementAndGet();
    }
    @Benchmark
    public void measureNoLock() {
        i++;
    }

    /**
     * 结果：
     * JMH_LockTest.measureLock                          sample    7   4250.628 ± 7272.045  ms/op
     * JMH_LockTest.measureCAS                           sample    8   2693.005 ±  684.133  ms/op
     * JMH_LockTest.measureNoLock                        sample  102    196.436 ±    7.640  ms/op
     * @param args
     * @throws RunnerException
     */
    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
                .include(JMH_LockTest.class.getSimpleName())
                .output("/tmp/Benchmark.log")
                .mode(Mode.SampleTime)
                .timeUnit(TimeUnit.MILLISECONDS)
                .forks(1)
                .warmupIterations(5)
                .measurementIterations(1)
                .measurementBatchSize(100000000)
                .build();

        new Runner(opt).run();
    }
}
