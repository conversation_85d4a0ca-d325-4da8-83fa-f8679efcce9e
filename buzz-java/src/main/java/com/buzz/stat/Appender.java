package com.buzz.stat;

import org.apache.commons.lang.time.DateFormatUtils;

public class Appender {

	public static void append(StatEntry entry, Integer count) {

		String line = DateFormatUtils.format(entry.getStatLogger().getRollingData().getTimeSlot(),
				"yyyy-MM-dd HH:mm:ss") + "|"
				+ entry.getStatLogger().name + "|1|";
		line += String.join(",", entry.getKeys());
		line += "|" + count;
		System.out.println(line);
	}

	public static void main(String[] args) {
		System.out.println(DateFormatUtils.format(System.currentTimeMillis(), "yyyy-MM-dd HH:mm:ss"));
	}
}
