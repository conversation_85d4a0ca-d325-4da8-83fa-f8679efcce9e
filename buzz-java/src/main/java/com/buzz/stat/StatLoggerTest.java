package com.buzz.stat;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

@Slf4j
public class StatLoggerTest {


    @Test
    public void test() throws InterruptedException {
        StatLogger statLogger = StatLoggerFactory.createStatLogger("tlog-stage", 1000);

        for (int i = 0; i < 10; ++i) {
            statLogger.stat("127.0.0." + i % 2).count();
            //间隔睡眠，注意睡眠不能太短
            if (i % 5 == 0) {
                Thread.sleep(1000);
            }
        }
        Thread.sleep(2 * 1000);
        System.out.println("==========");
    }

    @Test
    public void testKafka() throws InterruptedException {
        StatLogger statLogger = StatLoggerFactory.createStatLogger("message_write_request", 1000);

        for (int i = 0; i < 10; ++i) {
            int p = i % 2;
            //System.out.println(p);
            statLogger.stat("bairen.test", String.valueOf(p)).count();
            if (i % 5 == 0) {
                Thread.sleep(1000);
            }
        }
        Thread.sleep(2 * 1000);
        System.out.println("==========");

    }

}
