package com.buzz.stat;

import java.util.Arrays;

public class StatEntry {

	private StatLogger statLogger;

	private String[] keys;

	//避免重复计算hash
	private transient int hash;

	public StatEntry(StatLogger statLogger, String... keys) {
		this.statLogger = statLogger;
		this.keys = keys;
	}

	public void count() {
		statLogger.getRollingData().count(this);
	}

	public StatLogger getStatLogger() {
		return statLogger;
	}

	public void setStatLogger(StatLogger statLogger) {
		this.statLogger = statLogger;
	}

	public String[] getKeys() {
		return keys;
	}

	public void setKeys(String[] keys) {
		this.keys = keys;
	}

	@Override
	public int hashCode() {
		if (hash == 0) {
			int result = 1;
			result = 31 * result + Arrays.hashCode(keys);
			hash = result;
		}
		return hash;

	}

	@Override
	public boolean equals(Object o) {
		StatEntry other = (StatEntry) o;
		if (!this.statLogger.name.equals(other.statLogger.name)) {
			return false;
		}
		boolean ret = Arrays.equals(keys, other.keys);
		return ret;
	}
}
