package com.buzz.stat;

import java.util.Iterator;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

import com.buzz.toolkit.NamedThreadFactory;

/**
 * 
 * <AUTHOR>
 *
 */
public class StatLoggerFactory {

	private static final Map<String, StatLogger> statLoggers = new ConcurrentHashMap<String, StatLogger>();

	private static ScheduledThreadPoolExecutor schedue;

	static {
		schedue = new ScheduledThreadPoolExecutor(1, new NamedThreadFactory("stat-rolling"));
	}

	public static StatLogger createStatLogger(String loggerName, int intervalMillis) {
		StatLogger statLogger = statLoggers.get(loggerName);
		if (statLogger == null) {
			synchronized (StatLoggerFactory.class) {
				//double check
				if ((statLogger = statLoggers.get(loggerName)) == null) {
					statLogger = new StatLogger(loggerName, intervalMillis);
					addStatRollingTask(statLogger);
				}
			}
		}
		return statLogger;
	}

	private static void addStatRollingTask(StatLogger statLogger) {
		scheduleNextRollingTask(statLogger);
		schedue.setMaximumPoolSize(Math.max(1, statLoggers.size()));
	}

	private static void writeRollingData(StatRollingData data) {
		if (data.statEntrymap != null && !data.statEntrymap.isEmpty()) {
			Iterator<Entry<StatEntry, AtomicInteger>> it = data.statEntrymap.entrySet().iterator();
			while (it.hasNext()) {
				Entry<StatEntry, AtomicInteger> entry = it.next();
				Appender.append(entry.getKey(), entry.getValue().get());
			}
		}
	}

	private static void scheduleNextRollingTask(StatLogger statLogger) {

		StatLogRollingTask rollingTask = new StatLogRollingTask(statLogger);
		long now = System.currentTimeMillis();
		long rollingTs = statLogger.getRollingData().getRollingTs();
		long delayMillis = (rollingTs - now);
		if (delayMillis < 0) {
			schedue.submit(rollingTask);
		} else {
			schedue.schedule(rollingTask, delayMillis, TimeUnit.MILLISECONDS);
		}
	}

	private static class StatLogRollingTask implements Runnable {

		private StatLogger statLogger;

		public StatLogRollingTask(StatLogger statLogger) {
			super();
			this.statLogger = statLogger;
		}

		@Override
		public void run() {
			writeRollingData(statLogger.rolling());
			scheduleNextRollingTask(statLogger);

		}
	}
}
