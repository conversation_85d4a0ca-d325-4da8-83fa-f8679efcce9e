package com.buzz.stat;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicInteger;

public class StatRollingData {

	private long timeSlot;

	private long rollingTs;

	Map<StatEntry, AtomicInteger> statEntrymap = new ConcurrentHashMap<>();//这里做了简化，实际是应该是StatEntryFunc

	public StatRollingData(long timeSlot, long rollingTs) {
		super();
		this.timeSlot = timeSlot;
		this.rollingTs = rollingTs;
	}

	public void count(StatEntry entry) {
		AtomicInteger counter = statEntrymap.putIfAbsent(entry, new AtomicInteger(1));
		if (counter != null) {
			counter.incrementAndGet();
		}
	}

	public long getRollingTs() {
		return rollingTs;
	}

	public long getTimeSlot() {
		return timeSlot;
	}

}
