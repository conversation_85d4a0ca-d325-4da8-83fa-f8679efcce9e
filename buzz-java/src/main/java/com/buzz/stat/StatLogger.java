package com.buzz.stat;

import java.util.concurrent.atomic.AtomicReference;

public class StatLogger {

	String name;

	int intervalMillis;

	AtomicReference<StatRollingData> ref = new AtomicReference<StatRollingData>();

	StatLogger(String name, int intervalMillis) {
		this.name = name;
		this.intervalMillis = intervalMillis;
		rolling();
	}

	public StatEntry stat(String key) {

		return new StatEntry(this, key);
	}

	public StatEntry stat(String key1, String key2) {

		return new StatEntry(this, key1, key2);
	}

	public StatEntry stat(String key1, String key2, String key3) {

		return new StatEntry(this, key1, key2, key3);
	}

	public StatRollingData getRollingData() {
		return ref.get();
	}

	public StatRollingData rolling() {
//		姬总原来的代码：
//		do {
//			long now = System.currentTimeMillis();
//			//获取当前时间的起点
//			long timeSlot = now - now % intervalMillis;
//			//获取下一次滚动时间
//			long rollingTs = timeSlot + intervalMillis;
//			StatRollingData pre = ref.get();
//			StatRollingData next = new StatRollingData(timeSlot, rollingTs);
//			if(ref.compareAndSet(pre, next)) {
//				return pre;
//			}
//		} while (true);

		//上述代码可以优化为getAndSet()，因为CAS比较对象永远是从ref.get()得到的
		long now = System.currentTimeMillis();
		long timeSlot = now - now % intervalMillis;
		long rollingTs = timeSlot + intervalMillis;
		StatRollingData next = new StatRollingData(timeSlot, rollingTs);
		return ref.getAndSet(next);

	}
	
}
