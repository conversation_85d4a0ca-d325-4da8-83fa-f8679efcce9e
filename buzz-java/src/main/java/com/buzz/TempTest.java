package com.buzz;

import lombok.extern.slf4j.Slf4j;
import org.eclipse.jetty.util.BlockingArrayQueue;
import org.junit.Test;

import java.util.concurrent.CountDownLatch;
import java.util.concurrent.atomic.AtomicReference;

@Slf4j
public class TempTest {

    @Test
    public void test(){
        String line = """
                ## file: hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java\n```Java\npackage com.wacai.hermes.proxy.controller;\n\nimport com.google.inject.Inject;\nimport com.wacai.hermes.async.ActionListener;\nimport com.wacai.hermes.core.rest.RestController;\nimport com.wacai.hermes.core.rest.handler.BaseRestHandler;\nimport com.wacai.hermes.core.util.StopWatch;\nimport com.wacai.hermes.errors.Errors;\nimport com.wacai.hermes.message.BatchMessageMeta;\nimport com.wacai.hermes.message.HermesMessage;\nimport com.wacai.hermes.message.MessageMeta;\nimport com.wacai.hermes.proxy.client.Client;\nimport com.wacai.hermes.rest.RequestMapping;\nimport com.wacai.hermes.rest.RestChannel;\nimport com.wacai.hermes.rest.RestRequest;\nimport com.wacai.hermes.rest.RestResponse;\nimport com.wacai.hermes.runtime.ISettings;\nimport com.wacai.hermes.util.Assert;\nimport lombok.extern.slf4j.Slf4j;\nimport org.apache.commons.lang3.StringUtils;\n\nimport java.io.UnsupportedEncodingException;\nimport java.util.HashMap;\nimport java.util.List;\nimport java.util.Map;\n\nimport static com.wacai.hermes.rest.RestRequest.Method.POST;\n\n\n/**\n * <AUTHOR> * @description\n **/\n@Slf4j\npublic class ProducerController extends BaseRestHandler {\n\n private final long messageMaxSize;\n\n @Inject\n private Client client;\n\n @Inject\n public ProducerController(RestController controller, ISettings settings) {\n super(controller);\n //消息限制默认3MB\n messageMaxSize = settings.getLong(ISettings.PROXY_MESSAGE_MAX_SIZE, 3145728l);\n }\n\n /**\n * @param restRequest\n * @param restChannel\n */\n @RequestMapping(value = \"/hermes-proxy/sendSingle\", method = POST)\n public void sendSingle(RestRequest restRequest, RestChannel restChannel) {\n String topic = restRequest.param(\"topic\");\n String ack = restRequest.param(\"ack\", \"1\");\n long timeout = restRequest.paramAsLong(\"timeout\", 5000l);\n HermesMessage hermesMessage = restRequest.contentAsObject(HermesMessage.class);\n validateSingleMessage(hermesMessage, topic);\n StopWatch watch = StopWatch.create().start();\n ActionListener<MessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {\n log.info(\"sendSingle, topic {} client {} offset {} cost(ms) {}\",\n topic, restChannel.getClientIp(), r.getOffset(), watch.stop().elapsed());\n });\n client.send(ack, hermesMessage, timeout, actionListener);\n }\n\n private void validateSingleMessage(HermesMessage hermesMessage, String topic) {\n Assert.notNull(hermesMessage, Errors.ILLEGAL_PARAMETER, \"发送的消息体不能为空\");\n long payLoad = hermesMessage.getPayLoad();\n Assert.isTrue(payLoad > 0, Errors.ILLEGAL_PARAMETER, \"消息的key和value不能同时为空\");\n Assert.isTrue(payLoad <= messageMaxSize, Errors.ILLEGAL_PARAMETER, \"消息长度不能大于\" + messageMaxSize + \"(key+value),topic:\" + hermesMessage.getTopic());\n Assert.isTrue(StringUtils.equals(topic, hermesMessage.getTopic()), Errors.ILLEGAL_PARAMETER, \"body中的消息的topic和请求参数中topic不一致\");\n }\n\n /**\n * @param restRequest\n * @param restChannel\n */\n @RequestMapping(value = \"/hermes-proxy/sendBatch\", method = POST)\n public void sendBatch(RestRequest restRequest, RestChannel restChannel) {\n String topic = restRequest.param(\"topic\");\n String ack = restRequest.param(\"ack\", \"1\");\n long timeout = restRequest.paramAsLong(\"timeout\", 5000l);\n String trace = restRequest.param(\"trace\", \"off\");\n List<HermesMessage> messages = restRequest.contentAsList(HermesMessage.class);\n\n validateBatchMessages(messages, topic);\n StopWatch watch = StopWatch.create().start();\n ActionListener<BatchMessageMeta> actionListener = ActionListener.wrap(getActionListener(restChannel), (r) -> {\n log.info(\"sendBatch topic {} client {} offset {} cost(ms) {}\",\n topic, restChannel.getClientIp(), r.getMessageMetaList().get(0).getOffset(), watch.stop().elapsed());\n });\n if (StringUtils.equalsIgnoreCase(\"on\", trace)) {\n client.sendBatch(ack, messages, timeout, actionListener);\n } else {\n client.sendBatchByOneFlush(ack, messages, timeout, actionListener);\n }\n }\n\n\n private void validateBatchMessages(List<HermesMessage> hermesMessages, String topic) {\n Assert.isTrue(hermesMessages.size() > 0, Errors.ILLEGAL_PARAMETER, \"批量发送至少包含一条消息\");\n long batchPayLoad = 0;\n Map topicMap = new HashMap();\n for (HermesMessage hermesMessage : hermesMessages) {\n Assert.isTrue(hermesMessage.getPayLoad() > 0, Errors.ILLEGAL_PARAMETER, \"批量消息中每条消息的key和value不能同时为空\");\n Assert.isTrue(hermesMessage.getPayLoad() <= messageMaxSize, Errors.ILLEGAL_PARAMETER, \"单条消息长度不能大于\" + messageMaxSize + \"(key+value),topic:\" + topic);\n batchPayLoad += hermesMessage.getPayLoad();\n topicMap.put(hermesMessage.getTopic(), hermesMessage.getTopic());\n }\n\n Assert.isTrue(topicMap.size() == 1, Errors.ILLEGAL_PARAMETER, \"批量消息发送只能是相同topic\");\n Assert.isTrue(StringUtils.equals(topic, hermesMessages.get(0).getTopic()), Errors.ILLEGAL_PARAMETER, \"body中的消息的topic和请求参数中topic不一致\");\n\n }\n\n @RequestMapping(value = {\"/kafka/publish\", \"/kafka-proxy/kafka/publish\"}, method = POST)\n public void publish(RestRequest restRequest, RestChannel restChannel) throws UnsupportedEncodingException {\n\n String topic = parseTopic(restRequest);\n final String msgKey = restRequest.param(\"message_key\");\n final String msgVal = restRequest.param(\"message\");\n Assert.isTrue(StringUtils.isNotEmpty(msgVal), Errors.ILLEGAL_PARAMETER, \"message cannot be empty\");\n HermesMessage hermesMessage = new HermesMessage();\n hermesMessage.setTopic(topic);\n if (StringUtils.isNotBlank(msgKey)) {\n hermesMessage.setKey(msgKey.getBytes(\"UTF-8\"));\n }\n hermesMessage.setData(msgVal.getBytes(\"UTF-8\"));\n StopWatch watch = StopWatch.create().start();\n ActionListener<MessageMeta> actionListener = ActionListener.wrap((ret, e) -> {\n if (ret != null) {\n log.info(\"publish, topic {} client {} offset {} cost(ms) {}\",\n topic, restChannel.getClientIp(), ret.getOffset(), watch.stop().elapsed());\n }else{\n log.error(\"publish error\",e);\n }\n //兼容老版本kafka-http-client，这里只返回ok\n restChannel.sendResponse(RestResponse.text(\"ok\"));\n });\n client.send(\"1\", hermesMessage, 20000, actionListener);\n }\n\n private String parseTopic(RestRequest request) {\n String topic = request.param(\"topic\");\n if (topic == null || topic.isEmpty()) {\n StringBuilder errMsg = new StringBuilder(\"topic not specified. from \");\n String xff = request.header(\"X-Forwarded-For\");\n if (xff != null && !xff.isEmpty()) {\n errMsg.append(\" X-Forwarded-For: \").append(xff);\n }\n String clientId = request.header(\"Client-Id\");\n if (clientId != null && !clientId.isEmpty()) {\n errMsg.append(\" Client-Id: \").append(clientId);\n }\n log.error(errMsg.toString());\n }\n return topic;\n }\n}\n\n```
                """;
        System.out.println(line);
    }

    @Test
    public void testPositive() {
        Integer slot = -1;
        System.out.println(Integer.toBinaryString(slot) + "," + slot);
        slot &= 0x7FFFFFFF;
        System.out.println(Integer.toBinaryString(slot) + "," + slot);
        slot = Integer.MAX_VALUE;
        System.out.println(Integer.toBinaryString(slot) + "," + slot);
    }

    @Test
    public void testRound() {
        for (int i = 0; i < 30; ++i) {
            System.out.println(i + "\t" + (i & 8 - 1));
        }
    }

    @Test
    public void testMessageSize() {

        String msg = "{\"logNo\":null,\"logDate\":1692777904064,\"source\":\"OPRT\",\"tableCode\":\"daq_plv_ld_fund_executive_achment\",\"recordId\":59963,\"oprtType\":\"A\"}";

        System.out.println(msg.getBytes().length);
    }

    public static void main(String[] args) {

        final AtomicReference<CountDownLatch> ackLatchRef = new AtomicReference();
        BlockingArrayQueue<Integer> queue = new BlockingArrayQueue(10);

        Thread thread1 = new Thread(() -> {
            while (true) {
                CountDownLatch latch = ackLatchRef.get();
                if (latch != null) {
                    try {
                        latch.await();
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                }

                latch = ackLatchRef.updateAndGet((pre) -> {
                    log.info("new CountDownLatch ,pre={}", pre);
                    return new CountDownLatch(10);
                });

                //latch = ackLatchRef.updateAndGet((pre) -> new CountDownLatch(10));

                log.info("current latch: {}", latch.getCount());
                for (int i = 0; i < 10; ++i) {
                    queue.add(i);
                }

//                try {
//                    Thread.sleep(1000l);
//                } catch (InterruptedException e) {
//                    e.printStackTrace();
//                }
            }
        });
        thread1.start();

        Thread thread2 = new Thread(() -> {
            while (true) {
                Integer id = null;
                try {
                    id = queue.take();
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                CountDownLatch latch = ackLatchRef.get();
                if (latch == null) {
                    log.info("latch is null!");
                    try {
                        Thread.sleep(100);
                    } catch (InterruptedException e) {
                        e.printStackTrace();
                    }
                } else {
                    latch.countDown();
                    log.info("countDown:{} id:{}", latch.getCount(), id);
                }
            }
        });

        thread2.start();


//        SecurityManager var5 = System.getSecurityManager();
//        if (var5 != null) {
//            var5.checkListen(8563);
//        }
//        System.out.println("ok");

//        List list = new ArrayList();
//        for(int i = 0;i<100;++i){
//            list.add(i);
//        }
//
//        ListIterator it = list.listIterator();
//        it.next();
//
//        while(it.hasNext()){
//            System.out.println(it.next()+"\t"+it.previousIndex());
//        }

//        System.out.println(Long.parseLong("7FFFFFFF", 16));
//        System.out.println(Long.toBinaryString(2147483647).length());

//        System.out.println(Math.pow(16, 1));
//        System.out.println(Math.pow(16, 0));
//        System.out.println(Math.round(Math.pow(16, 2)));
//        System.out.println(Math.sqrt(4096));
//        byte[] data = new byte[6];
//        data[0] = 65;
//        data[1] = 78;
//        data[2] = 69;
//        data[3] = 74;
//        data[3] = 74;


    }
}
