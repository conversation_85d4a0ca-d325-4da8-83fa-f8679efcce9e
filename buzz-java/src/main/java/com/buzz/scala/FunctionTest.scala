package com.buzz.scala

//函数编程
object FunctionTest extends App {
  // 定义一个加法函数
  // 定义函数语法：def functionName(param1: Type1, param2: Type2, ...): ReturnType = {
  def add (a: Int, b:Int) :Int = {
    a+b
  }
  //使用匿名函数定义法函数
  //匿名函数语法：val functionName: (Type1, Type2, ...) => ReturnType = (param1, param2, ...) => {
  val multiply: (Int, Int) => Int = (x, y) => x * y


  //声明一个函数对象作为参数执行
  def operateOnNumbers(a:Int,b:Int, f:(Int,Int) => Int):Int={
    f(a,b)
  }

  val result1 = operateOnNumbers(3,3,add)
  println("Result of addition: " + result1)

  val result2 = operateOnNumbers(3,3,multiply)
  println("Result of multiplication: " + result2)
}
