package com.buzz.scala

import scala.collection.mutable

// set 和 map 的使用
object CollectionTest extends App {

  testSet()
  testMap()
  testSeq()
  testMapAndCase

  def testSet(): Unit = {
    //定义set
    val set1 = Set(1, 2, 3)
    val set2 = set1 + 4 + 5 + 6

    //set拆分为奇偶数
    val (even, odd) = set2.partition(_ % 2 == 0)
    println("Even numbers: " + even)
    println("Odd numbers: " + odd)
  }

  def testMap(): Unit = {
    //定义map
    val map = Map("a" -> 1, "b" -> 2, "c" -> 3, "d" -> 4, "e" -> 5)

    //通过匿名函数
    val (small, large) = map.partition { case (key, value) =>
      value <= 3 && key != "a"
    }
    println("small numbers: " + small)
    println("large numbers: " + large)
  }

  def testSeq(): Unit = {
    //Seq是一个不可变序列，因此无法直接在原始Seq上添加元素
    val topics = Seq("tp1", "tp2", "tp3")

    //通过map() 把seq[String] 转化为 seq[ElectionResult]
    val electionResults = topics.map(name => ElectionResult(name, 1))
    println(s"electionResults1=${electionResults}")

  }

  //测试map()和case操作
  def testMapAndCase(): Unit = {

    val partitionsAndLeader = mutable.Buffer.empty[(String, Int)] //声明一个可变序列
    partitionsAndLeader += "tp1" -> 1
    partitionsAndLeader += "tp2" -> 2
    partitionsAndLeader += "tp3" -> 3

    println(s"partitionsAndLeader=${partitionsAndLeader}")

    //case 关键字+map
    //当一个方法只有一个参数时，你可以选择不使用括号来调用它。这是 Scala 中的一种常见惯例，被称为“接近自然语言的调用风格”。
    val electionResults = partitionsAndLeader.map {
      //case 关键字用于解构元组
      case (partition, leader) => elect(partition, leader)
    }

    println(s"electionResults2=${electionResults}")

  }

  def elect(topic: String, leader: Int): ElectionResult = {
    ElectionResult(topic, leader)
  }
}


case class PartitionAndLeader(topic: String, leader: Int)

case class ElectionResult(topic: String, leader: Int)