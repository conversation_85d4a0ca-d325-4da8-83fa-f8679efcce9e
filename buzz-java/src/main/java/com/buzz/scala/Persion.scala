package com.buzz.scala

// 定义一个case class，可以理解为java中POJO
// 1. case class 表示不可变的数据结构，而普通的class则更适合表示具有复杂行为和状态的对象
// 2. Scala编译器会自动生成一些方法，包括toString、equals、hashCode和copy方法，这些方法可以直接使用而不需要显式地编写
// 3. 具有更简洁的语法，它们的定义通常比普通的class更简短。
case class Person(name: String, age: Int)

//自定义的apply方法需要定义伴生对象
object Person{
  val defaultAge: Int = 30

  //自定义的apply方法
  def apply(name: String): Person = {
    new Person(name, defaultAge)
  }
}


//通过 extends App 作为应用程序的入口点
object Main extends App {

  // 创建两个Person对象
  val person1 = Person("Alice", 25)
  val person2 = Person("Bob") // 使用自定义的apply方法

  // 打印Person对象的属性
  println(s"Person 1: Name=${person1.name}, Age=${person1.age}")

  println(s"Person 2: Name=${person2.name}, Age=${person2.age}")

  // 创建一个修改年龄的新Person对象
  val modifiedPerson = person1.copy(age = 35)
  println(s"Modified person: Name=${modifiedPerson.name}, Age=${modifiedPerson.age}")


}