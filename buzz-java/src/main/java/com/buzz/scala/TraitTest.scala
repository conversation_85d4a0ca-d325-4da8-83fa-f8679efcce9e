package com.buzz.scala

//trait 用法
trait Greeter {
  def greet(): Unit // 抽象方法
  def defaultGreeting(): Unit = println("Hello, world!") // 具体方法
}

class Programmer extends Greeter {
  override def greet(): Unit = {
    println("<b>Hello, world!</b>")
  }
}

object TraitTest extends App {
  test1();
  test2();

  // Scala 中的 trait 是一种用于定义对象的特征或行为的机制，类似于 Java 中的接口（interface）
  def test1(): Unit = {
    val programmer = new Programmer()
    programmer.greet()
  }

  //参考kafka中 PartitionLeaderElectionStrategy
  // sealed trait 是一种定义密封特质（sealed trait）的方式，当我们使用 match 表达式对密封特质进行模式匹配时，如果没有覆盖所有可能的情况，
  // 编译器会发出警告。这样可以帮助我们更好地捕获代码中的错误和遗漏，确保程序的正确性。
  def test2(): Unit = {
    val result = describeAnimal(Dog("xiao de"))
    println(result)
  }

  def describeAnimal(animal: Animal): String = animal match {
    case Dog(name) => s"$name is a dog"
    case Cat(name) => s"$name is a cat"
  }
}

//sealed trait 用法
sealed trait Animal

case class Dog(name: String) extends Animal

case class Cat(name: String) extends Animal

