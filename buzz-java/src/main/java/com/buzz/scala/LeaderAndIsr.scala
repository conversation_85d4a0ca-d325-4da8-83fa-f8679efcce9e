
// case class用法
case class LeaderAndIsr(leader: Int,
                        leaderEpoch: Int,
                        isr: List[Int]) {


  def newLeaderAndIsr(leader: Int, isr: List[Int]) = LeaderAndIsr(leader, leaderEpoch + 1, isr)


}

//伴生对应
object LeaderAndIsr {
  val initialLeaderEpoch: Int = 0
  val NoLeader: Int = -1
  val LeaderDuringDelete: Int = -2

  //自定义apply
  def apply(leader: Int, isr: List[Int]): LeaderAndIsr = LeaderAndIsr(leader, initialLeaderEpoch, isr)

  //静态方法
  def duringDelete(isr: List[Int]): LeaderAndIsr = LeaderAndIsr(LeaderDuringDelete, isr)
}

object Main extends App {

  val isr: List[Int] = List(1, 2, 3, 4, 5)
  val leader1 = LeaderAndIsr(1,1001, isr) // 默认构造方式
  val leader2 = LeaderAndIsr(1,isr) // 自定义apply
  var leader3 = LeaderAndIsr.duringDelete(isr)


  println(s"leader 1: leader=${leader1.leader}")
  println(s"leader 2: leader=${leader2.leader}")
  println(s"leader 3: leader=${leader3.leader}")

}
