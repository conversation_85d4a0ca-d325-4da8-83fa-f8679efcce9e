package com.buzz.guava;

import java.io.FileInputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.apache.commons.io.IOUtils;


public class ExcelTest {

	private static String SPLIT_FLAG = String.valueOf((char) 9);

	public static List<Integer> toIntList(List<String> strs) {
		List<Integer> items = new ArrayList<Integer>();
		for (String str : strs) {
			items.add(Integer.valueOf(str.trim()));
		}
		return items;
	}

	public static void formatMap(Map<Integer, Integer> map){
		for(Entry<Integer,Integer> entry:map.entrySet()) {
			System.out.println(entry.getValue()+"个"+entry.getKey());
		}
		System.out.println("-----------------");
	}
	public static void main(String[] args) throws IOException {

		ArrayList<List<Integer>> matrix = new ArrayList<List<Integer>>();
		List<String> lines = IOUtils.readLines(new FileInputStream("/tmp/vi"));
		for (String line : lines) {
			List<String> strs = Arrays.asList(line.split(SPLIT_FLAG));
			matrix.add(toIntList(strs));
		}
		
		
		int columeSize = matrix.get(0).size();
//		for(List<Integer> items:matrix) {
//			System.out.println(items);
//		}
		System.out.println("========"+columeSize+"=========");
		System.out.println();
		
		for (int y = 0; y < columeSize; ++y) {
			Map<Integer, Integer> map = new HashMap<Integer, Integer>();
			for (int x = 0; x < 10; ++x) {
				int key = matrix.get(x).get(y);
				//System.out.println(x+":"+y+"==>"+key);
				int size;
				if (map.containsKey(key)) {
					size = map.get(key) + 1;
				} else {
					size = 1;
				}
				map.put(key, size);
			}
			System.out.println("第"+(y+1)+"个:");
			formatMap(map);
//			break;
		}
	}
}
