package com.buzz.guava;

import com.google.common.util.concurrent.RateLimiter;

public class RateLimiterTest {

	public static void main(String[] args) {

		RateLimiter r = RateLimiter.create(5);
		while (true) {
			long begin = System.currentTimeMillis();
			System.out.print("get 1 tokens: " + r.acquire() + "s");
			long end = System.currentTimeMillis();
			System.out.println(" cost " + (end - begin) + " ms");
		}
	}
}
