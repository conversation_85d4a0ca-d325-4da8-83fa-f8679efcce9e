package com.buzz.guava;

import java.util.ArrayList;

import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;

public class BloomFilterTest {

	public static void main(String[] args) {

		// Guava的布隆过滤器通过调用BloomFilter类的静态函数创建，传递一个Funnel对象以及一个代表预期插入数量的整数。
//		BloomFilter<Long> filter = BloomFilter.create(
//				Funnels.longFunnel(),
//				10000000,
//				0.01);
//		long[] nums = new long[] { 3, 4, 7, 7, 8, 8, 1, 2, 3 };
//		ArrayList<Long> out = new ArrayList<Long>();
//		for (int i = 0; i < nums.length; ++i) {
//			if (!filter.mightContain(nums[i])) {
//				out.add(nums[i]);
//			}
//			filter.put(nums[i]);
//		}
//		System.out.println(out);

		for (int i = 1; i < 64; ++i) {
			long b = 0;
			b |= (1 << i);
			System.out.println(i+"\t"+Long.toBinaryString(b)+"\t"+b);
		}
	}
}
