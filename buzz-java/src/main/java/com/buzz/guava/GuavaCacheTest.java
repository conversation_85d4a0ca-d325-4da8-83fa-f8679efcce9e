package com.buzz.guava;

import java.util.List;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;

import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.google.common.cache.CacheLoader;
import com.google.common.cache.LoadingCache;

public class GuavaCacheTest {

	private static List<String> getScenarioKeysFromRemote(String key) {
		return null;

	}
	
	
	public static void testBase() throws ExecutionException {
		Cache<String, List<String>> guavaCache = CacheBuilder.
	            newBuilder().
	            maximumSize(10).
	            expireAfterWrite(24, TimeUnit.HOURS).build();
		
		guavaCache.put("test", null);
		
		String appName="nest";
		guavaCache.get(appName, new Callable<List<String>>() {

			@Override
			public List<String> call() throws Exception {
				// load from appName
				return null;
			}
			
		});
	}

	public static void testLoadingCache() throws ExecutionException {
		LoadingCache<String, List<String>> keysCache = CacheBuilder
				.newBuilder()
				.maximumSize(1000)
				.expireAfterWrite(24, TimeUnit.HOURS)
				.build(
						new CacheLoader<String, List<String>>() {
							public List<String> load(String key) throws Exception {
								return getScenarioKeysFromRemote(key);
							}
						});

		keysCache.get("test");
	}
}
