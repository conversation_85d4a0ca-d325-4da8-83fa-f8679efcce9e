package com.buzz.toolkit;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class DistributeSequenceIdGenerator {

	private static final Logger logger = LoggerFactory.getLogger(DistributeSequenceIdGenerator.class);
	
	private RedisProxy redisProxy;
	
	String appName;
	//时间起始标记点，作为基准，一般取系统的最近时间，默认2017-01-01
	private static final long epoch = 1420041600000L;
	
	//机器id所占的位数（5位）
	private static final long workerIdBits = 5L;
	//数据中心所占的位数（5位）
	private static final long dataCenterIdBits = 5L;
	//机器ID最大值:31
	private static final long maxWorkerId = 31L;
	//数据中心最大值:31
	private static final long maxDataCenterId = 31L;
	//序列在id中占的位数
	private static final long sequenceBits = 12L;
	//机器ID向左移12位
	private static final long workerIdShift = sequenceBits;
	//数据中心左移12+5位
	private static final long dataCenterIdShift = workerIdShift+dataCenterIdBits;
	//时间戳向左移22位(12+5+5)
	private static final long timestampLeftShift = workerIdShift+dataCenterIdBits+workerIdBits;
	//生成序列的掩码，这里为4095 (0b111111111111=0xfff=4095)，12位
	private static final long sequenceMask = 4095L;//(1<<12)-1
	
	private long workerId;
	private long dataCenterId;
	private long sequence = 0L;
	private long lastTimestamp = -1L;

	public String nextID() {
		return String.valueOf(this.nextId());
	}

	public synchronized long nextId() {
		long timestamp = timeGen();

		// 如果上一个timestamp与新产生的相等，则sequence加一(0-4095循环);
		if (this.lastTimestamp == timestamp) {
			// 对新的timestamp，sequence从0开始
			this.sequence = this.sequence + 1 & sequenceMask;
			// 毫秒内序列溢出
			if (this.sequence == 0) {
				// 阻塞到下一个毫秒,获得新的时间戳
				timestamp = this.tilNextMillis(this.lastTimestamp);
			}
		} else {
			// 时间戳改变，毫秒内序列重置
			this.sequence = 0;
		}

		// 如果当前时间小于上一次ID生成的时间戳，说明系统时钟回退过这个时候应当抛出异常
		if (timestamp < this.lastTimestamp) {
			String message = String.format("Clock moved backwards. Refusing to generate id for %d milliseconds.", (this.lastTimestamp - timestamp));
			logger.error(message);
			throw new RuntimeException(message);
		}

		this.lastTimestamp = timestamp;
		// 移位并通过或运算拼到一起组成64位的ID
		//return timestamp - epoch << timestampLeftShift | this.workerId << workerIdShift | this.sequence;
		return timestamp - epoch << timestampLeftShift | dataCenterId << dataCenterIdShift | workerId << workerIdShift | sequence;
	}

	private long tilNextMillis(long lastTimestamp) {
		long timestamp;
		for (timestamp = this.timeGen(); timestamp <= lastTimestamp; timestamp = this.timeGen()) {
			;
		}

		return timestamp;
	}

	private long timeGen() {
		return System.currentTimeMillis();
	}
	
	

	public void afterPropertiesSet() throws Exception {
		this.dataCenterId = 0L;
		this.workerId = -1L;
		
		//这里通过redis获取一个唯一的workerId,也可以从一个 Zookeeper 集群获取 
		for (int i = 0; (long) i <= maxDataCenterId; ++i) {
			if (this.redisProxy.setnx(this.appName + i, this.appName) == 1L) {
				this.redisProxy.expire(this.appName + i, 31536000);
				this.workerId = (long) i;
				break;
			}
		}

		if (this.workerId <= maxWorkerId && this.workerId >= 0L) {
			if (this.dataCenterId <= maxDataCenterId && this.dataCenterId >= 0L) {
				logger.info("{} 初始化成功, dataCenterId={}, workerId={}",
						new Object[]{this.getClass().getSimpleName(), this.dataCenterId, this.workerId});
			} else {
				throw new IllegalArgumentException(
						String.format("dataCenter Id can't be greater than %d or less than 0", 31L));
			}
		} else {
			throw new IllegalArgumentException(String.format("worker Id can't be greater than %d or less than 0", 31L));
		}
	}

	public void destroy() {
		try {
			this.redisProxy.del(new String[]{this.appName + this.workerId});
			logger.info("workerId={} redis释放", this.workerId);
		} catch (Exception var2) {
			logger.error("workerId={} redis释放异常", this.workerId);
		}

	}
	
	//mock
	static class RedisProxy{
		Long setnx(String... keys){
			return 0l;
		}
		Long del(String... keys) {
			return -1l;
		}
		void expire(String key,int seconds) {
		}
	}
}
