package com.buzz.toolkit.message;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Locale;
import java.util.Map;
import java.util.ResourceBundle;

/**
 * 
 * <AUTHOR>
 *
 */
public class MessageManager {

	private static Map<String, Map<Locale, MessageManager>> managers = new HashMap<>();

	private ResourceBundle bundle;

	private Locale locale;

	private MessageManager(String baseName, Locale locale) {
		this.locale = locale;
		bundle = ResourceBundle.getBundle(baseName);
	}

	public static MessageManager getManager(Class<?> klass) {
		return getManager(klass.getName(), Locale.getDefault());
	}

	/**
	 * Get the StringManager for a particular baseName and Locale.
	 * 
	 * @param baseName
	 * @param locale
	 * @return
	 */
	public static synchronized MessageManager getManager(String baseName, Locale locale) {

		Map<Locale, MessageManager> map = managers.get(baseName);

		if (map == null) {
			map = new HashMap<Locale, MessageManager>();
			managers.put(baseName, map);
		}

		MessageManager mgr = map.get(locale);
		if (mgr == null) {
			mgr = new MessageManager(baseName, locale);
			map.put(locale, mgr);
		}

		return mgr;

	}

	/**
	 * Get a string from the underlying resource bundle or return null if the String is not found.
	 * 
	 * @param key
	 * @return
	 */
	public String getString(String key) {
		return bundle.getString(key);
	}

	/**
	 * Get a string from the underlying resource bundle or return null if the String is not found.
	 * 
	 * @param key
	 * @param args
	 * @return
	 */
	public String getString(String key, Object... args) {
		String value = this.getString(key);
		if (value == null) {
			value = key;
		}

		MessageFormat mf = new MessageFormat(value);
		mf.setLocale(locale);
		return mf.format(args, new StringBuffer(), null).toString();
	}
}
