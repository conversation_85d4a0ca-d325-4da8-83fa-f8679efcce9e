package com.buzz.toolkit;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;


public class NamedThreadFactory implements ThreadFactory {

	private final AtomicInteger mThreadNum = new AtomicInteger(1);
	private final boolean mDaemon;
	private final String mPrefix;
	private final ThreadGroup mGroup;

	public NamedThreadFactory(String prefix) {
		this(prefix, true);
	}

	public NamedThreadFactory(String prefix, boolean daemon) {
		mPrefix = prefix + "-thread-";
		mDaemon = daemon;
		SecurityManager s = System.getSecurityManager();
		mGroup = (s == null) ? Thread.currentThread().getThreadGroup() : s.getThreadGroup();
	}

	@Override
	public Thread newThread(Runnable runnable) {
		String name = mPrefix + mThreadNum.getAndIncrement();
		Thread thread = new Thread(mGroup, runnable, name, 0);
		thread.setDaemon(mDaemon);
		return thread;
	}

}