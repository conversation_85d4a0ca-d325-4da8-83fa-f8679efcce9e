package com.buzz.toolkit;

import java.security.SecureRandom;
import java.util.Random;
import java.util.UUID;


public class RandomUtils {
	/**
	 * 去除容易混淆的字母
	 */
	private static final String SIMPLE_LETTER_NUMBER_SYMBOL = "abcdefhkmnpqrstuvwxy3456789";
	
	/**
	 * 仅仅包括数字和字母，去掉易混淆字母
	 * @param length 指定长度的字符串
	 * @return 随即字符串
	 */
	public static String getSimpleLetterNumber(int length) {
		if(length <= 0) {
			return "";
		}
		Random random = new SecureRandom();
		StringBuffer sb = new StringBuffer();
		for(int i = 0; i < length; i++){
			sb.append(SIMPLE_LETTER_NUMBER_SYMBOL.charAt(random.nextInt(SIMPLE_LETTER_NUMBER_SYMBOL.length())));
		}
		return sb.toString();
	}
	
	public static String generateAppSecret() {
		return UUID.randomUUID().toString().replace("-", "");
	}



	public static int getRangeRandom(int begin, int end) {
		return begin + rangeRandom.nextInt(end - begin + 1);
	}

	private static Random rangeRandom = new Random();

	/**
	 *
	 * <pre>
	 * 控制时间概率
	 =======================
	 50%,	30%,	  20%
	 =======================
	 0-50,	50-200, 200-1000
	 * </pre>
	 */
	private static class Simulator {

		private static Random rangeRandom = new Random();

		private static int getrangeRandom(int min, int max) {
			return min + rangeRandom.nextInt(max - min + 1);
		}
		private static long getTime() {
			double random = Math.random();
			random = (Math.round(random * 100) / 100.0);
			if (random < 0.5) {
				return getrangeRandom(0, 49);
			} else if (random < 0.8) {
				return getrangeRandom(50, 200);
			} else {
				return getrangeRandom(201, 1000);
			}
		}

		public static void execute() {
			try {
				Thread.sleep(getTime());
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		}
	}


}
