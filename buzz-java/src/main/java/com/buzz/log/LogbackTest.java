package com.buzz.log;


import ch.qos.logback.classic.Level;
import ch.qos.logback.classic.Logger;
import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.classic.encoder.PatternLayoutEncoder;
import ch.qos.logback.classic.spi.ILoggingEvent;
import ch.qos.logback.core.joran.spi.JoranException;
import ch.qos.logback.core.rolling.FixedWindowRollingPolicy;
import ch.qos.logback.core.rolling.RollingFileAppender;
import ch.qos.logback.core.rolling.SizeBasedTriggeringPolicy;
import ch.qos.logback.core.util.FileSize;

import java.io.File;
import java.net.URL;

public class LogbackTest {

    public static void main(String[] args) throws JoranException {
        URL url = Thread.currentThread().getContextClassLoader().getResource("logback1.xml");
        System.out.println(url);
    }

    static class LogConfig {

        private static String LOG_FILE_SIZE = "10MB";

        private static Logger ROOT;

        private static LoggerContext CONTEXT = new LoggerContext();

        public void init() {
            String logPath = "";

            System.out.println("jasmine client log path : " + logPath);

            ROOT = CONTEXT.getLogger("ROOT");

            // appender
            RollingFileAppender<ILoggingEvent> appender = new RollingFileAppender<ILoggingEvent>();
            appender.setContext(CONTEXT);
            appender.setAppend(true);
            appender.setFile(logPath);

            // encoder
            PatternLayoutEncoder encoder = new PatternLayoutEncoder();
            encoder.setContext(CONTEXT);
            encoder.setPattern("%date{ISO8601} %-5level [%thread] %logger{32} - %message%n");
            encoder.start();

            // rolling
            FixedWindowRollingPolicy rolling = new FixedWindowRollingPolicy();
            rolling.setContext(CONTEXT);
            rolling.setParent(appender);
            rolling.setMaxIndex(2);
            rolling.setFileNamePattern(logPath + File.separator + "jasmine-client.%i.log");
            rolling.start();

            // triggering
            SizeBasedTriggeringPolicy<ILoggingEvent> triggering = new SizeBasedTriggeringPolicy<ILoggingEvent>();
            triggering.setContext(CONTEXT);
            triggering.setMaxFileSize(FileSize.valueOf(LOG_FILE_SIZE));
            triggering.start();

            // appender init
            appender.setRollingPolicy(rolling);
            appender.setTriggeringPolicy(triggering);
            appender.setEncoder(encoder);
            appender.start();

            // logger init
            ROOT.setLevel(Level.INFO);
            ROOT.setAdditive(false);
            ROOT.addAppender(appender);
        }
    }

}
