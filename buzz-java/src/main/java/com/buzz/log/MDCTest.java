package com.buzz.log;

import java.util.Map;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import org.junit.Test;
import org.slf4j.MDC;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class MDCTest {

	@Test
	public void testInThreadPool() {
		//线程池中丢失MDC会丢失
		MDC.put("traceId", "123");
		MDC.put("uid", "xxx");
		ExecutorService es = Executors.newFixedThreadPool(1);
		es.submit(() -> {
			log.info("xxxx");
		});
	}

	@Test
	public void testInInheritableTask() {
		//通过 InheritableTask 复制
		MDC.put("traceId", "123");
		MDC.put("uid", "xxx");
		MdcThreadPoolTaskExecutor es = new MdcThreadPoolTaskExecutor(1, 1, 5, TimeUnit.SECONDS,
				new LinkedBlockingDeque<Runnable>());

		es.submit(() -> {
			log.info("xxxx");
		});
	}

	@Test
	public void testNewThread() throws InterruptedException {
		MDC.put("traceId", "123");
		final Map<String, String> map = MDC.getCopyOfContextMap();

		Thread t = new Thread(
				() -> {
					MDC.setContextMap(map);
					log.info("xxxx");
				}

		);
		t.start();
		t.join();
	}

	public static class MdcThreadPoolTaskExecutor extends ThreadPoolExecutor {

		public MdcThreadPoolTaskExecutor(int corePoolSize, int maximumPoolSize, long keepAliveTime, TimeUnit unit,
				BlockingQueue<Runnable> workQueue) {
			super(corePoolSize, maximumPoolSize, keepAliveTime, unit, workQueue);
		}

		@Override
		public void execute(Runnable command) {
			super.execute(wrapExecute(command, MDC.getCopyOfContextMap()));
		}

		private Runnable wrapExecute(final Runnable runnable, final Map<String, String> context) {
			return () -> {
				Map<String, String> previous = MDC.getCopyOfContextMap();
				if (context == null) {
					MDC.clear();
				} else {
					MDC.setContextMap(context);
				}
				try {
					runnable.run();
				} finally {
					if (previous == null) {
						MDC.clear();
					} else {
						MDC.setContextMap(previous);
					}
				}
			};
		}
	}
}