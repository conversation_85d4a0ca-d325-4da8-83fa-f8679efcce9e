package com.buzz.redis;

import com.buzz.redis.JedisBuilder.Env;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.junit.Test;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.Tuple;
import redis.clients.jedis.params.SetParams;
import redis.clients.jedis.util.JedisClusterCRC16;

import java.io.IOException;
import java.util.Set;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * JedisCluster 是集群模式
 */
@Slf4j
public class JedisClusterTest {


    private String errorType(int errorCode) {
        if (errorCode == 1) {
            return "write";
        } else if (errorCode == 2) {
            return "read";
        } else if (errorCode == 3) {
            return "all";
        }
        return "except";
    }

    @Test
    public void testZrangeByScore() {
        JedisCluster cluster = JedisBuilder.builder().env(Env.test).buildJedisCluster();
        Set<String> set = cluster.zrangeByScore("myzset", "3", "5");
        System.out.println("set=" + set);

        set = cluster.zrangeByScore("myzset", "(3", "(5");
        System.out.println("set=" + set);

    }



    @Test
    public void testClusterFault() throws InterruptedException {
        JedisCluster cluster = JedisBuilder.builder().env(Env.local).buildJedisCluster();
        while (true) {
            for (int i = 0; i < 10; i++) {
                String key = "test" + i;
                int errorCode = 0;
                StringBuffer errorMsg = new StringBuffer();
                try {
                    cluster.set(key, String.valueOf(i), new SetParams().ex(3600));
                } catch (Exception e) {
                    errorCode += 1;
                    errorMsg.append(e.getMessage() + "\r\n");
                }
                try {
                    System.out.println(key + "\t" + cluster.get(key) + "\t" + JedisClusterCRC16.getSlot(key));
                } catch (Exception e) {
                    errorCode += 2;
                    errorMsg.append(e.getMessage() + "\r\n");
                }
                if (errorCode > 0) {
                    log.info("ops " + errorType(errorCode) + " error slot:" + JedisClusterCRC16.getSlot(key)
                            + ",key:" + key + ", error:" + errorMsg.toString());
                }

            }
            Thread.sleep(2000);
            System.out.println("==================");
        }
    }

    @Test
    public void testCluster() throws InterruptedException {
        JedisCluster cluster = JedisBuilder.builder().env(Env.test).buildJedisCluster();
        String key = "test";
        cluster.set(key, "hello world");
        System.out.println(key + "\t" + cluster.get(key));
    }

    @Test
    public void testKvrocks() {
        JedisCluster cluster = JedisBuilder.builder().env(Env.kvrocks).buildJedisCluster();
        String key = "test";
        cluster.set(key, "hello world");
        System.out.println(key + "\t" + cluster.get(key));

        cluster.hset("test_hset", "name", "jack");
        cluster.hset("test_hset", "age", "90");
        System.out.println(cluster.hgetAll("test_hset"));
    }

    /**
     * 测试zset的用法，可用于延迟订单关闭场景
     */
    @Test
    public void testZSet() {
        JedisCluster cluster = JedisBuilder.builder().env(Env.test).buildJedisCluster();
        long startTime = System.currentTimeMillis() - 5000;
        String key = "test.delay.order";

        for (int i = 0; i < 10; ++i) {
            Long ret = cluster.zadd(key, startTime, "1000" + String.valueOf(i));
            System.out.println("add " + startTime + " " + ret);
            startTime += 1000;
        }

        while (true) {
            Set<Tuple> order = cluster.zrangeByScoreWithScores(key, 0, System.currentTimeMillis());
            if (order == null || order.isEmpty()) {
                System.out.println("当前没有等待的任务");
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
                continue;
            }
            System.out.println("order.size " + order.size());
            for (Tuple tuple : order.toArray(new Tuple[0])) {
                double score = tuple.getScore();
                if (System.currentTimeMillis() >= score) {
                    String element = tuple.getElement();
                    Long orderId = cluster.zrem("orderId", element);
                    System.out.println("获取 orderId " + orderId + " " + element);
                }
            }
            break;
        }
    }

    /**
     * 执行lua脚本
     */
    @Test
    public void testExecuteLua() {
        JedisCluster cluster = JedisBuilder.builder().env(Env.test).buildJedisCluster();
        String script = "if redis.call('get',ARGV[1]) == ARGV[2] then" + "\r\n" +
                "  return redis.call('get',ARGV[1])" + "\r\n" +
                "else" + "\r\n" +
                "  return 99" + "\r\n" +
                "end";
        String key = "bkk.maizi.test";
        String value = "111";
        System.out.println(cluster.set(key, value));
        System.out.println(cluster.eval(script, 1, key, key, value));

    }


    /**
     * 验证redis cluster迁移是否会影响客户端
     */
    @Test
    public void testClusterLongtime() throws InterruptedException, IOException {
        ExecutorService es = Executors.newFixedThreadPool(50);
        for (int x = 0; x < 50; ++x) {
            es.submit(() -> {
                try {
                    JedisCluster cluster = JedisBuilder.builder()
                            .env(Env.test).buildJedisCluster();
                    for (int i = 0; i < 10000; i++) {
                        String key = "test" + i;
                        cluster.setex(key, 60, String.valueOf(i));
                        if (StringUtils.isEmpty(cluster.get(key))) {
                            throw new IllegalArgumentException(key + " is null");
                        }
                        if (i % 1000 == 0) {
                            System.out.println(Thread.currentThread().getName() + " execute " + i);
                        }
                        if (i == 9999) {
                            i = 0;
                            System.out.println("ok");
                            try {
                                Thread.sleep(1000);
                            } catch (InterruptedException e) {
                                e.printStackTrace();
                            }
                        }
                    }
                } catch (Exception e) {
                    e.printStackTrace();
                    System.exit(-1);
                }

            });
        }
        System.in.read();
    }


    /**
     * 测试cluster多key
     */
    @Test
    public void testMultiKey() {
        JedisCluster cluster = JedisBuilder.builder().env(Env.test2).buildJedisCluster();
        System.out.println(cluster.sunion("test1", "test5"));

        // System.out.println(cluster.mget("test1", "test2"));
    }
}
