package com.buzz.redis;

import lombok.Getter;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.*;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @description
 **/
public class JedisBuilder {

    private Env env = Env.local;

    public static JedisBuilder builder() {
        return new JedisBuilder();
    }

    public JedisBuilder env(Env env) {
        this.env = env;
        return this;
    }

    //ShardedJedisPool 是一个分片的 jedis pool
    public ShardedJedisPool buildShardedJedisPool() {
        String hosts = env.getHostAddress();
        List<JedisShardInfo> shardInfoList = Stream.of(hosts.split(","))
                .map(str -> {
                    String[] hostPort = str.split(":");
                    return new JedisShardInfo(hostPort[0], Integer.parseInt(hostPort[1]));
                })
                .collect(Collectors.toList());

        ShardedJedisPool pool = new ShardedJedisPool(new GenericObjectPoolConfig(), shardInfoList);
        return pool;
    }

    public JedisPool buildJedisPool() {
        JedisPool pool = new JedisPool(new GenericObjectPoolConfig(), env.getHost(), env.getPort());
        return pool;
    }

    public JedisCluster buildJedisCluster() {
        String hosts = env.getHostAddress();
        Set<HostAndPort> nodes = Stream.of(hosts.split(","))
                .map(str -> {
                    String[] hostPort = str.split(":");
                    return new HostAndPort(hostPort[0], Integer.parseInt(hostPort[1]));
                })
                .collect(Collectors.toSet());
        JedisCluster cluster = new JedisCluster(nodes, new GenericObjectPoolConfig());
        return cluster;
    }

    @Getter
    public enum Env {
        local("127.0.0.1:6379,127.0.0.1:6380,127.0.0.1:6382"),
        test("************:6379,************:6380,************:6380"),
        test2("************:6379,************:6380,************:6380"),
        kvrocks("************:7381,************:7381,************:7381"),
        standalone("127.0.0.1:6379");

        String hostAddress;

        Env(String hostAddress) {
            this.hostAddress = hostAddress;
        }

        public String getHost() {
            return hostAddress.split(":")[0];
        }

        public int getPort() {
            return Integer.parseInt(hostAddress.split(":")[1]);
        }
    }


}
