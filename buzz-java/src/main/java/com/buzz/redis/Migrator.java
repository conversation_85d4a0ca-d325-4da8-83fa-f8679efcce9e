package com.buzz.redis;

import lombok.Builder;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.params.MigrateParams;

import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * <AUTHOR>
 * @description
 **/
@Builder
public class Migrator {
    private Jedis source;
    private String sourceNodeId;
    private Jedis target;
    private String targetNodeId;
    private int slot;
    private String password;

    public void migrate(){
        //① CLUSTER SETSLOT <slot> IMPORTING <node_id> 把 sourceNodeId 节点中导入 slot 到target节点。
        String importing = target.clusterSetSlotImporting(slot, sourceNodeId);
        System.out.println(importing);
        //② CLUSTER SETSLOT <slot> MIGRATING <node_id> 将源节点的 slot 迁移到 node_id 指定的节点中。
        String migrating =source.clusterSetSlotMigrating(slot, targetNodeId);
        System.out.println(migrating);

        Set<String> keys = new HashSet<String>();
        int migrateBatch = 5;
        while (true) {
            //③ CLUSTER GETKEYSINSLOT <slot> <count> 返回 slot 槽中的key
            List<String> perKeys = source.clusterGetKeysInSlot(0, migrateBatch);
            keys.addAll(perKeys);

            //说明当前slot下没有key了，推出
            if (keys.isEmpty()) {
                break;
            }
            String[] ks = keys.toArray(new String[keys.size()]);
            //④ MIGRATE host port key|"" destination-db timeout [COPY] [REPLACE] [KEYS key [key ...]] 迁移key
            String response = source.migrate(
                    target.getClient().getHost(),
                    target.getClient().getPort(),
                    0,
                    3000,
                    migrateParams(),
                    ks);

            System.out.println(response);
            keys.clear();
        }
        target.clusterSetSlotNode(slot, targetNodeId);
        source.clusterSetSlotNode(slot, targetNodeId);
    }

    private MigrateParams migrateParams(){
        MigrateParams migrateParams = MigrateParams.migrateParams();
        if(password!=null){
            migrateParams.auth(password);
        }
        return migrateParams.replace();
    }
}
