package com.buzz.redis;

import org.junit.Test;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.JedisPool;
import redis.clients.jedis.ShardedJedisPool;
import redis.clients.jedis.params.SetParams;

/**
 * 使用jedis正确的姿势是通过 JedisPool 使用
 *
 * <AUTHOR>
 * @description
 **/
public class JedisTest {


    @Test
    public void testJedisPool() {
        JedisPool pool = new JedisPool();
        pool.getResource().get("test");
    }

    /**
     * ShardedJedisPool 是一个分片的 jedis pool
     */
    @Test
    public void testShardedJedisPool() {
        ShardedJedisPool pool = JedisBuilder.builder().buildShardedJedisPool();
        pool.getResource().get("test");
    }


    @Test
    public void test_Set() throws InterruptedException {
        JedisCluster cluster = JedisBuilder.builder().buildJedisCluster();
        //set可以带options
        SetParams params = new SetParams();
        params.nx().px(3000);
        cluster.set("test.lock", "1", params);
    }




}
