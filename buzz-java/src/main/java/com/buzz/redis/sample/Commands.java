package com.buzz.redis.sample;

/**
 * <AUTHOR>
 * @description
 * @see redis.clients.jedis.commands.Commands
 **/
public interface Commands {

    void set(String key, String value);

    void setEX(String key, int seconds, String value);

    String get(String key);

    void incr(String key);

    void decr(String key);

    void del(String key);

    //支持的命令，参考：https://redis.io/commands/
    enum Command{

        /* 字符串相关操作 */
        SET,SETEX,GET,

        /* 计数相关操作 */
        INCR,DECR,

        /* Hash结构相关操作 */
        HSET, HGET,HGETALL,

        /* 通用 */
        DEL;

        public final byte[] raw;

        Command() {
            this.raw = encode(this.name());
        }

        private byte[] encode(String name){
            return name.getBytes();
        }
    }
}
