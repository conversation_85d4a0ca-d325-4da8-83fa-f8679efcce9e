package com.buzz.redis.sample;

import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @description
 * com.wacai.common.redis.provider.SimpleRedisCli
 **/
@Slf4j
public class SampleRedis implements Commands {

    // For Bulk Strings the first byte of the reply is "$"
    private static final byte DOLLAR_BYTE = '$';
    // For Arrays the first byte of the reply is "*"
    private static final byte ASTERISK_BYTE = '*';
    // For Simple Strings the first byte of the reply is "+"
    private static final byte PLUS_BYTE = '+';
    // For Errors the first byte of the reply is "-"
    private static final byte MINUS_BYTE = '-';
    // For Integers the first byte of the reply is ":"
    private static final byte COLON_BYTE = ':';

    private RedisConnection redisConnection;

    public SampleRedis(String host, int port) {
        this.redisConnection = new RedisConnection(host, port);
    }

    /**
     * 包含 Bulk Strings 的 RESP 数组
     *
     * @param cmd
     * @param args
     */
    public void sendCommand(Command cmd, byte[]... args) {
        try {
            byte[] command = cmd.raw;
            int size = args.length + 1;
            //RESP数组
            redisConnection.write(ASTERISK_BYTE);
            redisConnection.writeCrlf(size);
            //命令长度
            redisConnection.write(DOLLAR_BYTE);
            redisConnection.writeCrlf(command.length);
            //命令内容
            redisConnection.write(command);
            redisConnection.writeCrlf();

            for (byte[] arg : args) {
                //参数长度
                redisConnection.write(DOLLAR_BYTE);
                redisConnection.writeCrlf(arg.length);
                //参数内容
                redisConnection.write(arg);
                redisConnection.writeCrlf();
            }
        } catch (IOException e) {
            throw new IllegalStateException(e);
        } finally {
            redisConnection.flush();
        }
    }

    private ProcessResult read() {
        try {
            ProcessResult result = process();
            log.info(result.result.toString());
            return result;
        } catch (IOException e) {
            throw new IllegalStateException(e);
        }
    }

    /**
     * 这里没有处理null
     *
     * @return
     */
    private ProcessResult process() throws IOException {
        byte b = redisConnection.readByte();
        switch (b) {
            case PLUS_BYTE: {   //以+字符开头，返回类型是一个字符串
                byte[] data = redisConnection.readLine();
                return new ProcessResult(data.length, new String(data));
            }
            case DOLLAR_BYTE: { //以$字符开头，返回类型是一批字符串(bulk strings)
                //$5\r\nhello\r\n
                int len = (int) redisConnection.readLongCrLf();
                if(len==-1){
                    return new ProcessResult(len, "null");
                }else{
                    byte[] data = redisConnection.readLine();
                    return new ProcessResult(len, new String(data));
                }
            }
            case ASTERISK_BYTE: {//以*字符开头，返回类型是数组
                int num = (int) redisConnection.readLongCrLf();
                if (num == -1) {
                    return null;
                }
                List<ProcessResult> list = new ArrayList();
                for (int i = 0; i < num; ++i) {
                    list.add(read());
                }
                return new ProcessResult(num, list);
            }
            case COLON_BYTE: {//以:字符开头，返回类型是整数
                return new ProcessResult(-1, redisConnection.readLongCrLf());
            }
            case MINUS_BYTE: {//以-字符开头，返回类型是错误信息
                throw new IllegalStateException(new String(redisConnection.readLine()));
            }
        }
        return null;
    }

    public void sendCommand(Command cmd, String... args) {
        sendCommand(cmd, Codec.encodeStrArray(args));
    }

    @Override
    public void set(String key, String value) {
        sendCommand(Command.SET, key, value);
        read();
    }

    @Override
    public void setEX(String key, int seconds, String value) {
        sendCommand(Command.SET, Codec.encode(key), Codec.toByteArray(seconds), Codec.encode(value));
        read();
    }

    @Override
    public String get(String key) {
        sendCommand(Command.GET, key);
        ProcessResult result = read();
        return (String)result.result;
    }

    @Override
    public void incr(String key) {
        sendCommand(Command.INCR, key);
        read();
    }

    @Override
    public void decr(String key) {
        sendCommand(Command.DECR, key);
        read();
    }

    @Override
    public void del(String key) {
        sendCommand(Command.DEL, key);
        read();
    }

    private static class ProcessResult {
        public int byteSize;
        public Object result;

        public ProcessResult(int byteSize, Object result) {
            this.byteSize = byteSize;
            this.result = result;
        }
    }
}
