package com.buzz.redis.sample;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @description
 **/
public class Codec {

    public static final byte[] toByteArray(final int value) {
        return encode(String.valueOf(value));
    }


    public static byte[][] encodeStrArray(final String... strs) {
        byte[][] bs = new byte[strs.length][];
        for (int i = 0; i < strs.length; i++) {
            bs[i] = encode(strs[i]);
        }
        return bs;
    }


    public static byte[] encode(String str) {
        if (str == null) {
            throw new IllegalArgumentException("str can't be null");
        }

        try {
            return str.getBytes("UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new IllegalArgumentException(e);
        }
    }

    public static void main(String[] args) {
        System.out.println(toByteArray(9999).length);
    }
}
