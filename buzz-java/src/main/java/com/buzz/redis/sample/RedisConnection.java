package com.buzz.redis.sample;

import java.io.*;
import java.net.InetSocketAddress;
import java.net.Socket;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 * @description
 **/
public class RedisConnection {

    private static final byte CR = '\r';

    private static final byte LF = '\n';

    public static final int BUFFER_SIZE = 2 * 1024;

    private static final int timeout = 3000;

    private AtomicBoolean connected = new AtomicBoolean(false);

    private String host;

    private int port;

    private Socket socket;

    private RedisOutputStream redisOutputStream;

    private RedisInputStream redisInputStream;

    public RedisConnection(String host, int port) {
        this.host = host;
        this.port = port;
    }

    public void connect() throws IOException {
        if (connected.compareAndSet(false, true)) {
            socket = new Socket();
            socket.setTcpNoDelay(true);
            socket.connect(new InetSocketAddress(host, port), timeout);
            redisOutputStream = new RedisOutputStream(socket.getOutputStream());
            redisInputStream = new RedisInputStream(socket.getInputStream());
        }
    }

    public void write(byte b) throws IOException {
        connect();
        redisOutputStream.write(b);
    }

    public void write(byte[] b) throws IOException {
        connect();
        redisOutputStream.write(b);
    }

    public void writeCrlf() throws IOException {
        connect();
        redisOutputStream.write(CR);
        redisOutputStream.write(LF);
    }

    public void writeCrlf(int i) throws IOException {
        write(Codec.toByteArray(i));
        writeCrlf();
    }

    public byte readByte() throws IOException {
        return redisInputStream.read();
    }

    public byte[] readLine() throws IOException {
        return redisInputStream.readLine();
    }

    public long readLongCrLf() throws IOException {
        return redisInputStream.readLongCrLf();
    }

    public void flush() {
        if (connected.get()) {
            redisOutputStream.flush();
        }
    }

    public void close() {
        if (connected.compareAndSet(true, false)) {
            try {
                redisOutputStream.close();
                redisInputStream.close();
                socket.close();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    private static class RedisOutputStream {

        private BufferedOutputStream outputStream;

        public RedisOutputStream(OutputStream outputStream) throws IOException {
            this.outputStream = new BufferedOutputStream(outputStream, BUFFER_SIZE);
        }

        public void write(byte[] b) throws IOException {
            outputStream.write(b);
        }

        public void write(byte b) throws IOException {
            outputStream.write(b);
        }

        public void close() {
            try {
                outputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        public void flush() {
            try {
                outputStream.flush();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    private static class RedisInputStream {
        private BufferedInputStream inputStream;

        protected int count;//已读的数量

        protected int limit;//允许读的数量

        protected byte[] buf = new byte[128];

        public RedisInputStream(InputStream inputStream) {
            this.inputStream = new BufferedInputStream(inputStream);
        }

        public void close() {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

        public void ensureFill() throws IOException {
            //如果达到上限，重新开始
            if (count >= limit) {
                limit = inputStream.read(buf);
                count = 0;
                if(limit==-1){
                    throw new IllegalArgumentException("EOF");
                }
            }
        }

        public byte read() throws IOException {
            ensureFill();
            return buf[count++];
        }

        public byte[] readLine() throws IOException {
            ensureFill();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            while (true) {
                byte b = read();
                if (b == CR) {
                    byte c = read();
                    if (c == LF) {
                        break;
                    }
                    out.write(b);
                    out.write(c);
                } else {
                    out.write(b);
                }
            }
            return out.toByteArray();
        }

        public long readLongCrLf() throws IOException {
            ensureFill();
            ByteArrayOutputStream out = new ByteArrayOutputStream();
            while(true){
                byte b = read();
                if (b == CR) {
                    byte c = read();
                    if (c == LF) {
                        break;
                    }
                } else {
                    out.write(b);
                }
            }
            return Long.valueOf(new String(out.toByteArray()));
        }
    }


}
