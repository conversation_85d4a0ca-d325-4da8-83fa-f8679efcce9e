package com.buzz.redis.sample;

import org.junit.Test;

/**
 * 基于socket实现了RedisClient
 * <AUTHOR>
 * @description
 **/
public class SampleRedisTest {

    SampleRedis redis = new SampleRedis("*************", 6379);

    @Test
    public void testSet() {

        redis.set("test", "abc");
        redis.get("test");
        redis.del("test");
    }

    @Test
    public void testInc() {
        redis.set("test", "10");
        for(int i=0;i<10;++i){
            redis.incr("test");
        }
        redis.get("test");
        redis.del("test");
        redis.get("test");
    }
}
