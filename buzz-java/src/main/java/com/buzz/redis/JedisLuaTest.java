package com.buzz.redis;

import org.junit.Test;
import redis.clients.jedis.JedisCluster;

/**
 * <AUTHOR>
 * @description
 **/
public class JedisLuaTest {
    /**
     * 2 个参数：
     * 1、redis-key
     * 2、时间窗口，单位毫秒
     *
     * 注意下标从1开始，并且有KEY和ARGV
     */
    public static String TPS_COUNTER_LUA_SCRIPT = ""

            //-- 资源唯一标识
            + "local key = KEYS[1]" + "\n"
            //-- 窗口的间隔时间
            + "local interval_milliseconds = tonumber(ARGV[1]) " + "\n"

            + "local current_permits = tonumber(redis.call('get', key) or 0) " + "\n"
            + "local current_ttl = tonumber(redis.call('ttl', key) or -1) " + "\n"
            //-- 增加并发计数
            + "redis.call('incr', key)" + "\n"

            //-- 如果key中保存的并发计数为0，说明当前是一个新的时间窗口，它的过期时间设置为窗口的过期时间
            + "if (current_permits == 0 or current_ttl < 0 or current_ttl > interval_milliseconds) then" + "\n"
            + "   redis.call('pexpire', key, interval_milliseconds)" + "\n"
            + "end" + "\n"

            + "return current_permits + 1";

    @Test
    public void test_tpsCounter() throws InterruptedException {
        JedisCluster cluster = JedisBuilder.builder().buildJedisCluster();
        cluster.del("test");
        System.out.println(cluster.get("test"));
        System.out.println(cluster.incr("test"));
        for(int i=0;i<10;++i){
            Object ret =cluster.eval(TPS_COUNTER_LUA_SCRIPT, 1,"test","3000");
            System.out.println("result="+ret+"\t ttl="+cluster.ttl("test"));
            Thread.sleep(500);

        }

    }

}

