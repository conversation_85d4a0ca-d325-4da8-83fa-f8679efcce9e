package com.buzz.redis;

import lombok.Data;
import org.junit.Test;
import redis.clients.jedis.HostAndPort;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.JedisCluster;
import redis.clients.jedis.ScanParams;
import redis.clients.jedis.ScanResult;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * jedis 单节点测试
 * <p>
 * https://github.com/redis/jedis
 *
 * <AUTHOR>
 */
public class JedisStandaloneTest {




    @Data
    static class RedisSlotInfo {
        private String host;
        private int port;
        private String nodeId;
        private Jedis jedis;

        public RedisSlotInfo(String host, int port, String nodeId) {
            this.host = host;
            this.port = port;
            this.nodeId = nodeId;
            this.jedis = new Jedis("************", 6385);
        }
    }

    /**
     * 删除 cluster节点需要两步：1. cluster Forget 2. shutdown
     */
    @Test
    public void testCluster_delNode() {
        //定义集群
        List<Jedis> redisList = Stream.of(
                new Jedis("************", 6385 ),
                new Jedis("************", 6385),
                new Jedis("************", 6385)
        ).collect(Collectors.toList());
        //定义待删除的节点
        RedisSlotInfo toDeleteNode = new RedisSlotInfo("************", 7002, "166acec82f8dc562e64efe4a88e308ecf39e6843");

        //1. 执行cluster Forget
        for (Jedis jedis : redisList) {
            try {
                jedis.clusterForget(toDeleteNode.getNodeId());
            } catch (Exception e) {
                System.out.println("delNode failed:" + e.getMessage());
            }
        }
        //2. 执行 shutdown
        toDeleteNode.getJedis().shutdown();
    }



    /**
     * 测试slot
     * <p>
     * https://cloud.tencent.com/developer/section/1374007
     */
    @Test
    public void testCluster_clearSlot() {
        Jedis target = new Jedis("172.16.49.112", 6383);
        //清除slot的状态
        System.out.println(target.clusterSetSlotStable(0));

        Jedis source = new Jedis("************", 6379);
        //清除slot的状态
        System.out.println(source.clusterSetSlotStable(0));
    }

    @Test
    public void testCluster_getSlot() {
        Jedis jedis = new Jedis("172.16.49.112", 6383);
        //拿到slot下的key
        for (int i = 533; i < 1000; ++i) {
            Long ret = jedis.clusterCountKeysInSlot(i);
            if (ret > 0) {
                System.out.println(i + "\t" + jedis.clusterGetKeysInSlot(i, 10));
                break;
            }
        }

        //CLUSTER DELSLOTS slot
        //System.out.println(jedis.clusterDelSlots(532,533));

        System.out.println(jedis.clusterGetKeysInSlot(532, 10));
    }

    /**
     * target: ************:6389
     * source: 172.16.49.101:6383
     */
    @Test
    public void testCluster_migrateWithoutAuth() {
        Jedis source = new Jedis("************", 6379);
        Jedis target = new Jedis("172.16.49.112", 6383);
        Migrator.builder()
                .source(source)
                .sourceNodeId("823a0b467d2a72528eb7b8ec30ddee33c105f655")
                .target(target)
                .targetNodeId("a6bb7857d2797a53657767268cb27ca07f5da686")
                .slot(0)
                .build()
                .migrate();

        //通过builder避免方法参数列表过长
        //migrate(source,"823a0b467d2a72528eb7b8ec30ddee33c105f655",target,"a6bb7857d2797a53657767268cb27ca07f5da686",0);
    }

    @Test
    public void testCluster_migrateWithAuth() {
        String pwd = "abcd";
        Jedis source = new Jedis("172.16.49.101", 6383);
        source.auth(pwd);
        Jedis target = new Jedis("************", 6389);
        target.auth(pwd);

        Migrator.builder()
                .source(source)
                .sourceNodeId("681fcd3a82a6f56312dd73ccbf3edaea8ace40b5")
                .target(target)
                .targetNodeId("910ed8256ddce90e4910904ac846a544e26b406c")
                .slot(0)
                .password(pwd)
                .build()
                .migrate();
    }


    @Test
    public void testStandalone() {

        Jedis jedis = new Jedis("*************", 6379);
        String value = jedis.get("wac-base:wups:wups_station_all");
        System.out.println(value);
        jedis.close();

        Set<HostAndPort> jedisClusterNodes = new HashSet<HostAndPort>();
        //Jedis Cluster will attempt to discover cluster nodes automatically
        jedisClusterNodes.add(new HostAndPort("*************", 6379));
        JedisCluster cluster = new JedisCluster(jedisClusterNodes);

    }

    @Test
    public void testScan(){
        Jedis jedis = new Jedis("************", 6382);

        String cursor = ScanParams.SCAN_POINTER_START;
        String key = "test*";
        ScanParams scanParams = new ScanParams();
        scanParams.match(key); // 设置匹配模式
        scanParams.count(1000); // 设置每次返回的元素数量

        while (true) {
            ScanResult<String> scanResult = jedis.scan(cursor, scanParams);
            cursor = scanResult.getCursor();
            List<String> list = scanResult.getResult();

            if ("0".equals(cursor)) {
                break; // 迭代结束
            }

            // 处理返回的key列表
            for (String mapentry : list) {
                System.out.println(mapentry);
                // 可以执行其他操作，例如删除key
            }
        }
        System.out.println("====end====");
        jedis.close();
    }

}
