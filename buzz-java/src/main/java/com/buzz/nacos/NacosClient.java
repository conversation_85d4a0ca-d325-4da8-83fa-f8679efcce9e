//package com.buzz.nacos;
//
//import com.alibaba.nacos.api.NacosFactory;
//import com.alibaba.nacos.api.exception.NacosException;
//import com.alibaba.nacos.api.naming.NamingMaintainService;
//import com.alibaba.nacos.api.naming.NamingService;
//
//public class NacosClient {
//
//	//private static final String SERVER_ADDR = "172.16.49.70:8848";
//	private static final String SERVER_ADDR = "172.16.145.39:8848";
//
//	private static NamingService namingService;
//
//	private static NamingMaintainService namingMaintainService;
//
//	public static NamingService getNamingService() throws NacosException {
//		if (namingService == null) {
//			namingService = NacosFactory.createNamingService(SERVER_ADDR);
//		}
//
//		return namingService;
//	}
//
//	public static NamingMaintainService getNamingMaintainService() throws NacosException {
//		if (namingMaintainService == null) {
//			namingMaintainService = NacosFactory.createMaintainService(SERVER_ADDR);
//		}
//
//		return namingMaintainService;
//	}
//}
