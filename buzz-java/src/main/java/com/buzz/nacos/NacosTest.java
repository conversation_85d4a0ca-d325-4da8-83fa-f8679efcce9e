//package com.buzz.nacos;
//
//import com.alibaba.nacos.api.exception.NacosException;
//import com.alibaba.nacos.api.naming.NamingMaintainService;
//import com.alibaba.nacos.api.naming.NamingService;
//import com.alibaba.nacos.api.naming.pojo.Instance;
//import com.alibaba.nacos.api.naming.pojo.Service;
//import org.junit.Test;
//
//import java.io.IOException;
//import java.util.HashMap;
//import java.util.List;
//import java.util.Map;
//
//public class NacosTest {
//
//	@Test
//	public void testCreateService() throws NacosException, IOException {
//		NamingMaintainService namingMaintainService = NacosClient.getNamingMaintainService();
//
//		Service service = null;
//		namingMaintainService.createService(service, null);
//	}
//
//	private Instance addInstance(String ip, String site) {
//		Instance instance = new Instance();
//		instance.setIp(ip);
//		instance.setPort(8080);
//		instance.setHealthy(true);
//		Map<String, String> metadata = new HashMap<String, String>();
//		metadata.put("site", site);
//		instance.setMetadata(metadata);
//		return instance;
//	}
//	@Test
//	public void testRegisterApplication() throws NacosException, IOException {
//		NamingService namingService = NacosClient.getNamingService();
//		namingService.registerInstance("rating", addInstance("**************", "sh"));
//		namingService.registerInstance("rating", addInstance("**************", "hz"));
////		namingService.registerInstance("rating.k2.test.wacai.com", addInstance("**************", "cd"));
//
//
//		System.in.read();
//
//	}
//
//	@Test
//	public void testGetInstance() throws NacosException, IOException {
//		NamingService naming = NacosClient.getNamingService();
//		String appName = "rating";
//		//naming.getServicesOfServer(pageNo, pageSize)
////		ExpressionSelector selector = new ExpressionSelector();
////		selector.setExpression("site.");
////		naming.getServicesOfServer(0, 100, selector);
//
//		List<Instance> allInstanceList = naming.getAllInstances(appName);
//		for (Instance instance : allInstanceList) {
//			System.out.println(instance);
//		}
//	}
//}
