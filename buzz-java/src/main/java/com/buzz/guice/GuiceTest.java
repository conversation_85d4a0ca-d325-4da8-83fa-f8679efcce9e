package com.buzz.guice;

import com.google.inject.*;
import com.google.inject.name.Named;
import lombok.Data;
import org.junit.Test;

import static com.google.inject.name.Names.named;

/**
 * https://www.baeldung.com/guice
 *
 * <AUTHOR>
 * @description
 **/
public class GuiceTest {

    @Test
    public void testBasic() {
        Injector injector = Guice.createInjector(new TaskModule());
        TaskManager taskManager = injector.getInstance(TaskManager.class);
        taskManager.start();
        taskManager.start2();
    }

    private static class TaskModule extends AbstractModule {
        @Override
        protected void configure() {

            Task task = new Task();
            task.setName("test");
            bind(Task.class).toInstance(task);//绑定到实例

            bind(TaskExecutor.class).
                    annotatedWith(named("lazyTaskExecutor"))
                    .to(LazyTaskExecutor.class);
            bind(TaskExecutor.class).
                    annotatedWith(named("concurrentTaskExecutor"))
                    .to(ConcurrentTaskExecutor.class);

            bind(TaskManager.class).asEagerSingleton(); //单例

        }
    }


    private static class TaskManager {

        private Task task;

        @Inject
        @Named("concurrentTaskExecutor")
        private TaskExecutor executor;

        private Injector injector;

        @Inject
        public void setTask(Task task) {
            this.task = task;
        }

        @Inject
        public void setInjector(Injector injector) {
            this.injector = injector;
        }

        public void start() {
            executor.execute(task);
        }

        public void start2() {
            TaskExecutor executor = injector.getInstance(Key.get(TaskExecutor.class, named("lazyTaskExecutor")));
            executor.execute(task);
        }
    }

    interface TaskExecutor {
        void execute(Task task);
    }

    static class ConcurrentTaskExecutor implements TaskExecutor {

        @Override
        public void execute(Task task) {
            System.out.println("concurrent execute " + task);
        }
    }

    static class LazyTaskExecutor implements TaskExecutor {

        @Override
        public void execute(Task task) {
            System.out.println("lazy execute " + task);
        }
    }

    @Data
    static class Task {
        private String name;
    }
}
