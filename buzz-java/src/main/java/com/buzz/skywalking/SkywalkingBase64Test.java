package com.buzz.skywalking;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.buzz.util.IOUtils;
import org.junit.Test;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.function.Consumer;

/**
 * <AUTHOR>
 * @description
 **/
public class SkywalkingBase64Test {



    private String serviceId(String serviceId) {
        String[] strings = serviceId.split("\\.");
        String base64text = strings[0];
        return new String(Base64.getDecoder().decode(base64text), StandardCharsets.UTF_8);
    }

    private void loadFormFile(String filePath, boolean jsonType) throws IOException {
        Consumer<String> stringConsumer = (s) -> {
            System.out.println(s + "\t" + serviceId(s));
        };

        if (jsonType) {
            String json = IOUtils.readFile(filePath);
            for (JSONObject item : JSON.parseArray(json, JSONObject.class)) {
                String serviceId = item.getString("key");
                stringConsumer.accept(serviceId);
            }
        } else {
            for (String serviceId : IOUtils.readLines(filePath)) {
                stringConsumer.accept(serviceId);
            }
        }
    }

    @Test
    public void testServiceID() throws IOException {
        System.out.println(serviceId("aGVybWVzLWNlbnRlcg==.1"));
    }

    @Test
    public void testServiceIDFromJsonFile() throws IOException {
        loadFormFile("/tmp/log", true);
    }

    @Test
    public void testServiceIDFromTextFile() throws IOException {
        loadFormFile("/tmp/log2", false);
    }
}
