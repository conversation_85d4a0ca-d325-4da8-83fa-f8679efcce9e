package com.buzz.skywalking;

import org.junit.Test;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

public class Base64Test {


    public String decode(String base64text){
        return new String(Base64.getDecoder().decode(base64text), StandardCharsets.UTF_8);
    }

    @Test
    public void testDecode(){
       String test = "1_ZGNmNzA3MTM1Y2JhNDJhMWIxNjZkNmMwOWU2NjczOGRAMTcyLjE2LjE0NC4xOTg=";
       System.out.println(decode(test));
    }
}
