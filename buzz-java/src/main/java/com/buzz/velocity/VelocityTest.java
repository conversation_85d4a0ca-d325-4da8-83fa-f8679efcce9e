package com.buzz.velocity;

import java.lang.reflect.Type;
import java.net.URL;

import org.apache.commons.io.IOUtils;
import org.apache.velocity.context.InternalContextAdapter;
import org.apache.velocity.context.InternalContextAdapterImpl;
import org.apache.velocity.runtime.RuntimeInstance;
import org.apache.velocity.runtime.parser.ParseException;
import org.apache.velocity.runtime.parser.node.ASTExpression;
import org.apache.velocity.runtime.parser.node.ASTReference;
import org.apache.velocity.runtime.parser.node.ASTSetDirective;
import org.apache.velocity.runtime.parser.node.Node;
import org.apache.velocity.runtime.parser.node.SimpleNode;


public class VelocityTest {

	
	 public void check(Node node, Type claz, InternalContextAdapter ica) {
//	        ASTSetDirective ast = (ASTSetDirective) node;
//	        Node right = ast.jjtGetChild(1);
//	        ASTReference left = (ASTReference) ast.jjtGetChild(0);
//	        if (this.childCheck(right, claz, ica)) {
//	            Type rightType = this.result.getType();
//	            if (left.jjtGetNumChildren() == 0) {
//	            	
//	            	//checkSetOperation(ica, left, rightType);
//	            }
//	        }
    }
	public static void main(String[] args) throws ParseException, Exception {

		String name = "test.vm";

		RuntimeInstance ri = new RuntimeInstance();
		ri.init();

		URL url = Thread.currentThread().getContextClassLoader().getResource(name);
		String content = IOUtils.toString(url.openStream());
		System.out.println(content);
		
		System.out.println("========================");
		
		//得到AST node
		SimpleNode node = ri.parse(content, name);
		
		//推入我们的context
		CheckerContext checkerContext = new CheckerContext();
		InternalContextAdapterImpl ica = new InternalContextAdapterImpl(checkerContext);
		ica.pushCurrentTemplateName(name);
		node.init(ica, ri);

		
		
		int i, k = node.jjtGetNumChildren();
        for (i = 0; i < k; i++) {
            Node child = node.jjtGetChild(i);
            if(child instanceof  ASTSetDirective) {
            	ASTSetDirective  ast =  (ASTSetDirective) child;
            	ASTReference left = (ASTReference) ast.jjtGetChild(0);
            	ASTExpression right = (ASTExpression) ast.jjtGetChild(1);
            	//获取引用名
//            	Type type = (Type) left.getVariableValue(ica, left.getRootString());
//            	System.out.println("child.left="+left+"\t right="+right);
            	System.out.println(right.jjtGetChild(0));
            }
        }
        
		//System.out.println(data);
	}
}
