package com.buzz.json;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import lombok.Data;
import org.junit.Test;

public class FastJsonTest {



    @Test
    public void test101(){
        String json = """
                {
                    "file_list": [
                        11,  // CreateBlogRequest.java - 可能需要修改以支持删除操作
                        13,  // BlogRepository.java - 需要添加删除方法
                        22,  // CreateBlogResponse.java - 可能需要修改以支持删除操作的响应
                        26,  // BlogService.java - 需要添加删除博客的业务逻辑
                        27,  // BlogPost.java - 可能需要修改以支持删除操作
                        29,  // BlogController.java - 需要添加删除博客的API
                        33   // BlogServiceTest.java - 需要添加删除博客的测试用例
                    ]
                }                
                """;
        JSONObject jsonObject = JSON.parseObject(json);
        System.out.println(jsonObject);
    }

    @Test
    public void test() {

        Person  person = new Person();
        person.setId(100010001l);
        person.setName("jack ma");
        person.setAge(48);
        person.setMale(true);
        person.setCity("hangzhou");

        JSON.toJSONString(person);
    }

    @Data
    public static class Person {
        long id;
        int age;
        String name;
        boolean male;
        String city;
    }
}
