spring:
  application:
    name: buzz-spring-ai-mcp
  
  ai:
    openai:
      # DeepSeek API configuration (compatible with OpenAI API)
      base-url: https://api.deepseek.com
      api-key: ${DEEPSEEK_API_KEY:your-deepseek-api-key-here}
      chat:
        options:
          model: deepseek-chat
          temperature: 0.7
          max-tokens: 2000
    
    # MCP Client Configuration
    mcp:
      client:
        stdio:
          connections:
            # Brave Search MCP Server
            brave-search:
              command: npx
              args:
                - "-y"
                - "@modelcontextprotocol/server-brave-search"
              env:
                BRAVE_API_KEY: ${BRAVE_API_KEY:your-brave-api-key-here}
            
            # Filesystem MCP Server
            filesystem:
              command: npx
              args:
                - "-y"
                - "@modelcontextprotocol/server-filesystem"
                - "./"
        
        sse:
          connections:
            # Custom Author Tools MCP Server
            author-tools-server:
              url: http://localhost:8081

server:
  port: 8080

logging:
  level:
    org.springframework.ai: DEBUG
    com.buzz: DEBUG
