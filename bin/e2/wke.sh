#!/usr/bin/expect -f
# 初始化个性化数据信息
set username "luwu"
set pwd "******"


# 选择空间环境，可自行配置
send_user "登录的环境空间：\n"
send_user " 1. wac-amc\n"
send_user " 2. hainan-amc\n"
send_user " 3. wr-amc\n"
send_user "请选择: "
expect_user -re "(.*)\n"
set num $expect_out(1,string)
switch $num {
    "2" {
        set space "hainan-amc"
        set cluster "xamc"
    }
    "3" {
        set space "wr-amc"
        set cluster "wramc"
    }
    default {
        set space "wac-amc"
        set cluster "xamc"
    }
}


# 选择应用，可自行配置
send_user "应用列表: \n"
send_user "1. black-shark\n"
send_user "2. snowstorm\n"
send_user "请选择: "
expect_user -re "(.*)\n"
set num $expect_out(1,string)
switch $num {
    "2" {
        set app "snowstorm"
    }
    default {
        set app "black-shark"
    }
}
send_user "\n\n已选择，空间：$space 应用：$app\n"
send_user "（按回车继续，Ctrl+C 返回.）\n"
expect_user -re "(.*)\n"


# 登录跳板机
spawn ssh $<EMAIL>
expect "Password:" { send "$pwd\n" }

# 选择机房，当前选择的是 3 qingshanhu
expect "Select group:" { send "3\n" }
expect -re ".*jumper-160-105-hzqsh.*" {
    sleep 0.2
    # 登录容器
    send "pod=`wkectl get pods -n $space -c $cluster | grep $app | awk '{print \$1}'` && wkectl exec \$pod bash -n $space -c $cluster\n"
}
expect -re ".*root.black-shark.*" {
    sleep 0.2
  # 在容器内预执行命令
    # send "date\n"
}
interact