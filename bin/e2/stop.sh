$ cat bin/stop.sh
[ ! -f "./config/application.properties" ] && echo "[ERROR] please run this script in the project root path!" && exit 1

# source "./bin/setenv.sh"
# SCRIPT_DIR
# PID_FILE
# JOLOKIA_FILE
# PROC_ID not PID!!!
# JOLOKIA_PORT
# SERVER_PORT inaccuracy!!!
function init_and_check() {
  echo "[INFO] prepare to close service"

  source "./bin/setenv.sh"
  SCRIPT_DIR=`pwd`

  PID_FILE="$SCRIPT_DIR/../.service_pid"
  [ ! -f "$PID_FILE" ] && PID_FILE="$SCRIPT_DIR/.service_pid"

  JOLOKIA_FILE="$SCRIPT_DIR/../.jolokia_port"
  [ ! -f "$JOLOKIA_FILE" ] && JOLOKIA_FILE="$SCRIPT_DIR/.jolokia_port"

  [ ! -f "$PID_FILE" ] && echo "[ERROR] no pid file" && exit 0
  [ ! -f "$JOLOKIA_FILE" ] && echo "[ERROR] no jolokia file" && exit 0

  PROC_ID=$(cat $PID_FILE)
  JOLOKIA_PORT=$(cat $JOLOKIA_FILE)

  SERVER_PORT=`grep 'server.port' "$SCRIPT_DIR"/config/application.properties | awk -F '=' '{print $2}' | awk 'NR==1' | sed 's/\\r//g'`
  if [ -z "$SERVER_PORT" ]; then
    SERVER_PORT=8080
  fi
  echo "[INFO] PID: $PROC_ID"
  echo "[INFO] JOLOKIA_PORT: $JOLOKIA_PORT"
}

function get_default_ip() {
  local ip=$( /sbin/ifconfig | grep "inet " | awk '{print $2}' | sed 's/addr://' | grep "$NETWORK" | head -1 )
  [ ! -z "$ip" ] && echo $ip && return
  /sbin/ifconfig | grep "inet " | awk '{print $2}' | sed 's/addr://' | grep -v "127.0.0.1" | head -1
}

function service_flow_switch() {
  echo "[INFO] elegent restart processing..."
  local health_check_result=$(curl -s -m 5 http://127.0.0.1:"$SERVER_PORT"/check_backend_active.html?status=false)

  echo "[INFO] health check status: $health_check_result"
  if [ "$health_check_result" == "imok" ]; then
    echo "[INFO] waiting for flow switching..."
    for i in {1..3}
    do
      sleep 5
      echo "[INFO] waiting..."
    done
    sleep 5
  fi

  echo "[INFO] flow switch completed"
}

function jolokia_shutdown() {
  echo "[INFO] use jolokia shutdown to stop the service, jolokia_port: ${JOLOKIA_PORT}"
  local i=0
  local result=$(curl -s --connect-timeout 5 -m 5 http://$(get_default_ip):${JOLOKIA_PORT}/jolokia/search/com.wacai.lifecycles:name=ShutdownLatch)

  while ! echo "$result" |grep -q "\"status\":200"
  do
    sleep 1
    result=$(curl -s --connect-timeout 5 -m 5 http://$(get_default_ip):${JOLOKIA_PORT}/jolokia/search/com.wacai.lifecycles:name=ShutdownLatch)
    ((i++))
    if (($i>4)); then
      break
    fi
  done

  if ! echo "$result" |grep -q "\"value\":\[\]"
  then
    result=$(curl -s --connect-timeout 5 -m 5 http://$(get_default_ip):${JOLOKIA_PORT}/jolokia/exec/com.wacai.lifecycles:name=ShutdownLatch/shutdown)
  else
    result=$(curl -s --connect-timeout 5 -m 5 http://$(get_default_ip):${JOLOKIA_PORT}/jolokia/exec/org.springframework.boot:type=Endpoint,name=shutdownEndpoint/shutdown)
  fi
}

function stop_service() {
  jolokia_shutdown

  sleep 2
  if ps -p $PROC_ID
  then
    echo "[INFO] use kill to kill the service: ${PROC_ID}"
    kill $PROC_ID

    local kill_wait_time=10
    if [[ "$ARTIFACT" == "fund-web-api" || "$ARTIFACT" == "cn.fudata" || "$ARTIFACT" == "cn.fudata.spider" ]]; then
      # 10 minutes
      kill_wait_time=600
    fi

    if [ `echo ${ENV_STOP_WAIT} | grep -E "^[0-9]{1,}$"` ]; then
      kill_wait_time=${ENV_STOP_WAIT}
      echo "[INFO] custom wait stop service timeout [${kill_wait_time}]"
    fi

    kill_service_check $kill_wait_time
    kill_service_9
  fi
}

# parameter wait_time(seconds)
function kill_service_check() {
  local wait_time=${1}
  if [ $wait_time -gt 30 ]; then
    local step_time_span=30
  else
    local step_time_span=2
  fi

  local i=0
  while ps -p $PROC_ID >/dev/null 2>&1
  do
    sleep $step_time_span
    echo "[INFO] wait stop service..."
    ((i=$i+$step_time_span))
    if [ $i -gt $wait_time ]; then
      echo "[INFO] wait stop service timeout [$wait_time]"
      break
    fi
  done
}

# kill -9
function kill_service_9() {
  if ps -p $PROC_ID
  then
    echo "[WARN] use kill -9 to kill the service: ${PROC_ID}"
    kill -9 $PROC_ID
    sleep 3
  fi
}

function services_deregister() {
  # etcd deregister
  curl -s -X DELETE http://etcd.ops.service.wacai.info:2379/v2/keys/obelisk3/register/${LOCAL_IP} || echo "[WARN] request etcd error"
}

init_and_check
service_flow_switch
services_deregister
stop_service

echo ""
echo "[INFO] stop service success"
