#!/bin/sh
# ===================================================================================
# Generic startup script for running arbitrary Java applications with
# being optimized for running in containers
#
# Usage:
#    # Execute a Java app:
#    ./run-java.sh <args given to Java code>
#
#    # Get options which can be used for invoking Java apps like Maven or Tomcat
#    ./run-java.sh options [....]
#
#
# This script will pick up either a 'fat' jar which can be run with "-jar"
# or you can sepcify a JAVA_MAIN_CLASS.
#
# Source and Documentation can be found
# at https://github.com/fabric8io-images/run-java-sh
#
# Env-variables evaluated in this script:
#
# JAVA_OPTIONS: Checked for already set options
# JAVA_MAX_MEM_RATIO: Ratio use to calculate a default maximum Memory, in percent.
#                     E.g. the "50" value implies that 50% of the Memory
#                     given to the container is used as the maximum heap memory with
#                     '-Xmx'.
#                     It defaults to "25" when the maximum amount of memory available
#                     to the container is below 300M, otherwise defaults to "50".
#                     It is a heuristic and should be better backed up with real
#                     experiments and measurements.
#                     For a good overviews what tuning options are available -->
#                             https://youtu.be/Vt4G-pHXfs4
#                             https://www.youtube.com/watch?v=w1rZOY5gbvk
#                             https://vimeo.com/album/4133413/video/181900266
# Also note that heap is only a small portion of the memory used by a JVM. There are lot
# of other memory areas (metadata, thread, code cache, ...) which addes to the overall
# size. When your container gets killed because of an OOM, then you should tune
# the absolute values.
# JAVA_INIT_MEM_RATIO: Ratio use to calculate a default intial heap memory, in percent.
#                      By default this value is not set.
#
# The following variables are exposed to your Java application:
#
# CONTAINER_MAX_MEMORY: Max memory for the container (if running within a container)
# MAX_CORE_LIMIT: Number of cores available for the container (if running within a container)

# ==========================================================
# 1. Init: limit env workspace configfile strongbox
# 2. Java options: run_java_options debug_options java_default_options
# 3. Java agent: bds secure test
# 4. Java args: exec_args java_options classpath
# 5. CMD: options run
# 6. Fire up

# ==========================================================
# 1. Init: limit env workspace configfile strongbox

# Fail on a single failed command in a pipeline (if supported)
(set -o | grep -q pipefail) && set -o pipefail


# Save global script args
ARGS="$@"

# ksh is different for defining local vars
if [ -n "${KSH_VERSION:-}" ]; then
  alias local=typeset
fi

# Error is indicated with a prefix in the return value
check_error() {
  local error_msg="$1"
  if echo "${error_msg}" | grep -E -q "^ERROR:|\[ERROR\]"; then
    echo "${error_msg}"
    exit 1
  fi
}

# The full qualified directory where this script is located in
script_dir() {
  # Default is current directory
  local dir=$(dirname "$0")
  local full_dir=$(cd "${dir}" && pwd)
  echo ${full_dir}
}

# Try hard to find a sane default jar-file
auto_detect_jar_file() {
  local dir="$1"

  # Filter out temporary jars from the shade plugin which start with 'original-'
  local old_dir="$(pwd)"
  cd ${dir}
  if [ $? = 0 ]; then
    # NB: Find both (single) JAR *or* WAR <https://github.com/fabric8io-images/run-java-sh/issues/79>
    local nr_jars="$(ls 2>/dev/null | grep -e '.*\.jar$' -e '.*\.war$' | grep -v '^original-' | wc -l | awk '{print $1}')"
    if [ "${nr_jars}" = 1 ]; then
      ls 2>/dev/null | grep -e '.*\.jar$' -e '.*\.war$' | grep -v '^original-'
      exit 0
    fi
    cd "${old_dir}"
    echo "ERROR: Neither JAVA_MAIN_CLASS nor JAVA_APP_JAR is set and ${nr_jars} found in ${dir} (1 expected)"
  else
    echo "ERROR: No directory ${dir} found for auto detection"
  fi
}

# Check directories (arg 2...n) for a jar file (arg 1)
find_jar_file() {
  local jar="$1"
  shift;

  # Absolute path check if jar specifies an absolute path
  if [ "${jar}" != ${jar#/} ]; then
    if [ -f "${jar}" ]; then
      echo "${jar}"
    else
      echo "ERROR: No such file ${jar}"
    fi
  else
    for dir in $*; do
      if [ -f "${dir}/$jar" ]; then
        echo "${dir}/$jar"
        return
      fi
    done
    echo "ERROR: No ${jar} found in $*"
  fi
}

# Generic formula evaluation based on awk
calc() {
  local formula="$1"
  shift
  echo "$@" | awk '
    function ceil(x) {
      return x % 1 ? int(x) + 1 : x
    }
    function log2(x) {
      return log(x)/log(2)
    }
    function max2(x, y) {
      return x > y ? x : y
    }
    function round(x) {
      return int(x + 0.5)
    }
    {print '"int(${formula})"'}
  '
}

# Based on the cgroup limits, figure out the max number of core we should utilize
core_limit() {
  local cpu_period_file="/sys/fs/cgroup/cpu/cpu.cfs_period_us"
  local cpu_quota_file="/sys/fs/cgroup/cpu/cpu.cfs_quota_us"
  if [ -r "${cpu_period_file}" ]; then
    local cpu_period="$(cat ${cpu_period_file})"

    if [ -r "${cpu_quota_file}" ]; then
      local cpu_quota="$(cat ${cpu_quota_file})"
      # cfs_quota_us == -1 --> no restrictions
      if [ ${cpu_quota:-0} -ne -1 ]; then
        echo $(calc 'ceil($1/$2)' "${cpu_quota}" "${cpu_period}")
      fi
    fi
  fi
}

max_memory() {
  # High number which is the max limit until which memory is supposed to be
  # unbounded.
  local mem_file="/sys/fs/cgroup/memory/memory.limit_in_bytes"
  if [ -r "${mem_file}" ]; then
    local max_mem_cgroup="$(cat ${mem_file})"
    local max_mem_meminfo_kb="$(cat /proc/meminfo | awk '/MemTotal/ {print $2}')"
    local max_mem_meminfo="$(expr $max_mem_meminfo_kb \* 1024)"
    if [ ${max_mem_cgroup:-0} != -1 ] && [ ${max_mem_cgroup:-0} -lt ${max_mem_meminfo:-0} ]
    then
      echo "${max_mem_cgroup}"
    fi
  fi
}

init_limit_env_vars() {
  # Read in container limits and export the as environment variables
  local core_limit="$(core_limit)"
  if [ -n "${core_limit}" ]; then
    export CONTAINER_CORE_LIMIT="${core_limit}"
  fi

  local mem_limit="$(max_memory)"
  if [ -n "${mem_limit}" ]; then
    export CONTAINER_MAX_MEMORY="${mem_limit}"
  fi
}

function get_default_ip() {
  local ip=$( /sbin/ifconfig | grep "inet " | awk '{print $2}' | sed 's/addr://' | grep "$NETWORK" | head -1 )
  [ ! -z "$ip" ] && echo $ip && return
  /sbin/ifconfig | grep "inet " | awk '{print $2}' | sed 's/addr://' | grep -v "127.0.0.1" | head -1
}

load_config() {
  if [ "$PLUGIN_ENABLE_OB_FE_CONFIG" != "0" ]; then
    echo "[INFO] HOST_ASSETS=${HOST_ASSETS}"
    wget -q ${HOST_ASSETS}/config -O /tmp/.obeliskx_config || exit 1
    if [ "$PLUGIN_ENABLE_OB_FE_GRAY" != "0" ]; then
      original_obelisk_config=$(cat /tmp/.obeliskx_config)
      local gray_obelisk_config=$(set_gray_obelisk_config "$original_obelisk_config")
      echo "$gray_obelisk_config" > /tmp/.obeliskx_config
    fi
    source /tmp/.obeliskx_config
    rm -rf /tmp/.obeliskx_config
  fi
}

function set_gray_obelisk_config() {
  local obelisk_config="$*"
  local gray_obelisk_config="$obelisk_config"
  gray_obelisk_config=$(apply_gray "OBELISK_CONFIG_MODULE" "$obelisk_config")
  gray_result=$?
  if [[ "$gray_result" == "0" ]];then
    echo "$gray_obelisk_config"
  else
    echo "$obelisk_config"
  fi
}

function apply_gray() {
  local gray_module="$1"
  local config=$(echo "$2" | sed ':label;N;s/\n/\\n/;b label')
  local env_level=$(echo "$APP_ENV" | tr 'a-z' 'A-Z')
  local GRAY_RULE_URL="${HOST_ASSETS}/grayRule/apply"
  local env_cluster=$(echo "$K2_SERVER_NAME" | tr 'a-z' 'A-Z')
  local GRAY_RULE_DATA="{\"val\":\"$config\",\"grayModule\":\"$gray_module\",\"projectType\":\"springboot\",\"envLevel\":\"${env_level}\",\"artifactId\":\"${ARTIFACT}\",\"envCluster\":\"${env_cluster}\"}"
  local result
  result=$(curl -s -w "\\n%{http_code}" -X POST -H "Content-Type:application/json" --data "${GRAY_RULE_DATA}" "$GRAY_RULE_URL")

  local http_code
  http_code=$(echo "$result" | tail -1)
  [[ "$http_code" != 200 ]] && echo "$config" && exit 1

  local code
  code=$(echo "$result" | sed '$d' | sed "s/'//g" | python -c 'import sys, json; print json.load(sys.stdin)["code"]' 2>/dev/null | sed 's/\"//g')
  if [[ "$code" == "0" ]]; then
    echo "$result" | sed '$d' | sed "s/'//g" | python -c 'import sys, json; print json.load(sys.stdin)["data"]' 2>/dev/null | sed 's/\\n/\n/g'
  else
    echo "$config"
  fi
}

load_env() {
  loload_envcal script_dir="$1"

  # Configuration stuff is read from this file
  local run_env_sh="setenv.sh"
  local run_plugin_sh="plugin.sh"

  # Load default default config
  if [ -f "${script_dir}/${run_env_sh}" ]; then
    . "${script_dir}/${run_env_sh}"
  fi

  if [ -f "${script_dir}/${run_plugin_sh}" ]; then
    . "${script_dir}/${run_plugin_sh}"
  fi

  load_config
  load_app_env

  # Check also $JAVA_APP_DIR. Overrides other defaults
  # It's valid to set the app dir in the default script
  JAVA_APP_DIR="${JAVA_APP_DIR:-${script_dir}}"
  if [ -f "${JAVA_APP_DIR}/${run_env_sh}" ]; then
    . "${JAVA_APP_DIR}/${run_env_sh}"
  fi
  export JAVA_APP_DIR

  # JAVA_LIB_DIR defaults to JAVA_APP_DIR
  export JAVA_LIB_DIR="${JAVA_LIB_DIR:-${JAVA_APP_DIR}}"
  if [ -z "${JAVA_MAIN_CLASS:-}" ] && [ -z "${JAVA_APP_JAR:-}" ]; then
    JAVA_APP_JAR="$(auto_detect_jar_file ${JAVA_APP_DIR})"
    check_error "${JAVA_APP_JAR}"
  fi

  if [ -n "${JAVA_APP_JAR:-}" ]; then
    local jar="$(find_jar_file ${JAVA_APP_JAR} ${JAVA_APP_DIR} ${JAVA_LIB_DIR})"
    check_error "${jar}"
    export JAVA_APP_JAR="${jar}"
  else
    export JAVA_MAIN_CLASS
  fi
}

load_app_env() {
  # TRUE_ENV online or offline
  TRUE_ENV="offline"
  if [ "x$APP_ENV" == "xstaging" ] || [ "x$APP_ENV" == "xproduction" ]; then
    TRUE_ENV="online"
  fi

  # cluster
  if [[ "x$K2_SERVER_NAME" != "x" ]]; then
    CLUSTER=${K2_SERVER_NAME^^}
    _CLUSTER="_${K2_SERVER_NAME^^}"
  else
    CLUSTER=""
    _CLUSTER=""
  fi
  eval HOST_STRONGBOX=\$HOST_STRONGBOX${_CLUSTER}
  eval HOST_BDS_WEB=\$HOST_BDS_WEB${_CLUSTER}
  eval HOST_KEY_ESCROW=\$HOST_KEY_ESCROW${_CLUSTER}

  local program_path="/data/program"
  local app_root_path="${program_path}/${GROUP}/${ARTIFACT}/${VERSION}"
  local app_config_path="${app_root_path}/config"
  local app_log_path="${program_path}/logs/${GROUP}/${ARTIFACT}"

  # global env
  JAVA_APP_DIR=${app_root_path}
  APP_ROOT_PATH=${app_root_path}
  APP_CONFIG_PATH=${app_config_path}
  APP_LOG_PATH=${app_log_path}
  APP_DUMP_PATH=${APP_LOG_PATH}"/coredump"

  echo "[INFO] APP_ROOT_PATH=${app_root_path}"
  echo "[INFO] APP_LOG_PATH=${app_log_path}"
  echo "[INFO] HOST_STRONGBOX=${HOST_STRONGBOX}|${HOST_STRONGBOX_TEST}"
}

init_structure() {
  local k2_log_path="/data/k2/log/"
  local k2_instance_id=$(echo ${HOSTNAME} | awk -F- '{printf("%s-%s\n",$(NF-1),$NF)}')
  local program_path="/data/program"

  # APP dir
  mkdir -p ${APP_CONFIG_PATH} ${APP_ROOT_PATH}/lib ${APP_ROOT_PATH}/config ${APP_ROOT_PATH}/agent

  # APP Log dir
  mkdir -p ${APP_LOG_PATH} ${k2_log_path}/${k2_instance_id}
  rm -rf ${program_path}/logs
  ln -sf ${k2_log_path}/${k2_instance_id} ${program_path}/logs
  mkdir -p ${APP_LOG_PATH}/access_log
  ln -sf ${APP_LOG_PATH}/access_log ${APP_ROOT_PATH}/logs
  mkdir -p ${APP_LOG_PATH}/app_log
  ln -sf ${APP_LOG_PATH}/app_log ${APP_ROOT_PATH}/app_log
  mkdir -p ${APP_LOG_PATH}/coredump
  mkdir -p ${APP_LOG_PATH}/gc_log
  ln -sf ${APP_LOG_PATH}/gc_log ${APP_ROOT_PATH}/gc_log

  # APP config setting
  # TODO cp -r /config/* ${APP_CONFIG_PATH}
  ln -sf /config/* ${APP_CONFIG_PATH}
  ln -sf /logback/* ${APP_CONFIG_PATH}
}

load_config_file() {
  if [ ! -f "${APP_CONFIG_PATH}/application.properties" ]; then
    if [ "x$APP_ENV" == "xtest" ] && [ -f "${APP_CONFIG_PATH}/application-test.properties" ]; then
      mv ${APP_CONFIG_PATH}/application-test.properties ${APP_CONFIG_PATH}/application.properties
    else
      wget -q ${HOST_CONFIG_FILE}/project/config-file/${ARTIFACT}/${CONFIG_FILE}/${APP_ENV}/${CONFIG_QUALIFIER} -O ${APP_CONFIG_PATH}/application.properties || exit 1
    fi
  fi

  # automatically generated configuration items
  if ! grep -q "obeliskx automatically" ${APP_CONFIG_PATH}/application.properties; then
      echo -e "\n\n## obeliskx automatically generated configuration items" >> ${APP_CONFIG_PATH}/application.properties

      if ! grep -q "server.tomcat.accesslog.pattern" ${APP_CONFIG_PATH}/application.properties; then
        echo "server.tomcat.accesslog.pattern=%{X-Forwarded-For}i %h %l %u %t \"%r\" %s %b" >> ${APP_CONFIG_PATH}/application.properties
        echo "# hack for spring 1.2.x" >> ${APP_CONFIG_PATH}/application.properties
        echo "server.tomcat.access-log-pattern=%{X-Forwarded-For}i %h %l %u %t \"%r\" %s %b" >> ${APP_CONFIG_PATH}/application.properties
      fi

      echo "management.health.mail.enabled=false" >> ${APP_CONFIG_PATH}/application.properties
  fi
}

load_strongbox() {
  if [[ $TRUE_ENV == "online" ]]; then
    export DRUID_DECRYPT_KEY=`curl -s -k ${HOST_STRONGBOX}/key/${GROUP}/${ARTIFACT}?token=${TOKEN_STRONGBOX}` JDBC_PROPS_URL=${HOST_STRONGBOX}/props/${GROUP}/${ARTIFACT}
  else
    export DRUID_DECRYPT_KEY=`curl -s -k ${HOST_STRONGBOX_TEST}/key/${GROUP}/${ARTIFACT}?token=${TOKEN_STRONGBOX}` JDBC_PROPS_URL=${HOST_STRONGBOX_TEST}/props/${GROUP}/${ARTIFACT}
  fi
}

load_key_escrow() {
  local ESCROW_URL="${HOST_KEY_ESCROW}/server/obtain/key"
  local ESCROW_DATA="{\"groupId\": \"${GROUP}\",\"artifactId\": \"${ARTIFACT}\",\"env\": \"${APP_ENV}\"}"
  local ESCROW_SECRET=`curl -s -X POST -H "Escrow-Authorized-Access-Key:${TOKEN_KEY_ESCROW}" -H "Content-Type:application/json" --data "${ESCROW_DATA}" ${ESCROW_URL} | python -c 'import sys, json; print json.load(sys.stdin)["data"]' 2>/dev/null` || ESCROW_SECRET=''
  if [[ "x${ESCROW_SECRET}" != "x" && "x${ESCROW_SECRET}" != "xnull" ]]; then
      export KEY_ESCROW_APP_SECRET=${ESCROW_SECRET}
  fi
}

# Read in a classpath either from a file with a single line, colon separated
# or given line-by-line in separate lines
# Arg 1: path to claspath (must exist), optional arg2: application jar, which is stripped from the classpath in
# multi line arrangements
format_classpath() {
  local cp_file="$1"
  local app_jar="${2:-}"

  local wc_out="$(wc -l $1 2>&1)"
  if [ $? -ne 0 ]; then
    echo "Cannot read lines in ${cp_file}: $wc_out"
    exit 1
  fi

  local nr_lines=$(echo $wc_out | awk '{ print $1 }')
  if [ ${nr_lines} -gt 1 ]; then
    local sep=""
    local classpath=""
    while read file; do
      local full_path="${JAVA_LIB_DIR}/${file}"
      # Don't include app jar if include in list
      if [ "${app_jar}" != "${full_path}" ]; then
        classpath="${classpath}${sep}${full_path}"
      fi
      sep=":"
    done < "${cp_file}"
    echo "${classpath}"
  else
    # Supposed to be a single line, colon separated classpath file
    cat "${cp_file}"
  fi
}

# ==========================================================================
# 2. Java options: run_java_options debug_options java_default_options

# Check for standard /opt/run-java-options first, fallback to run-java-options in the path if not existing
run_java_options() {
  if [ -f "/opt/run-java-options" ]; then
    echo "$(. /opt/run-java-options)"
  else
    which run-java-options >/dev/null 2>&1
    if [ $? = 0 ]; then
      echo "$(run-java-options)"
    fi
  fi
}

debug_options() {
  if [ -n "${JAVA_ENABLE_DEBUG:-}" ] || [ -n "${JAVA_DEBUG_ENABLE:-}" ] ||  [ -n "${JAVA_DEBUG:-}" ]; then
    local debug_port="${JAVA_DEBUG_PORT:-8000}"
    local suspend_mode="n"
    if [ -n "${JAVA_DEBUG_SUSPEND:-}" ]; then
      if ! echo "${JAVA_DEBUG_SUSPEND}" | grep -q -e '^\(false\|n\|no\|0\)$'; then
        suspend_mode="y"
      fi
    fi
    echo "-agentlib:jdwp=transport=dt_socket,server=y,suspend=${suspend_mode},address=${debug_port}"
  fi
}

memory_options() {
  local opts="$(calc_init_memory) $(calc_max_memory)"

  # metaspace
  if ! echo "${JAVA_OPTIONS:-} ${RUN_OPTIONS:-}" | grep -q -- "MetaspaceSize"; then
    opts="${opts} -XX:MetaspaceSize=38m -XX:MaxMetaspaceSize=380m"
  fi

  # dump
  opts="${opts} -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=${APP_DUMP_PATH}/java.hprof"

  echo $opts
}

# Check for memory options and set max heap size if needed
calc_max_memory() {
  # Check whether -Xmx is already given in JAVA_OPTIONS
  if echo "${JAVA_OPTIONS:-}" | grep -q -- "-Xmx"; then
    return
  fi

  if [ -z "${CONTAINER_MAX_MEMORY:-}" ]; then
    return
  fi

  # Check for the 'real memory size' and calculate Xmx from the ratio
  if [ -n "${JAVA_MAX_MEM_RATIO:-}" ]; then
    if [ "${JAVA_MAX_MEM_RATIO}" -eq 0 ]; then
      # Explicitely switched off
      return
    fi
    calc_mem_opt "${CONTAINER_MAX_MEMORY}" "${JAVA_MAX_MEM_RATIO}" "mx"
    calc_mem_opt "${CONTAINER_MAX_MEMORY}" "20" "X:MaxNewSize="
  # When JAVA_MAX_MEM_RATIO not set and JVM >= 10 no max_memory
  elif [ "${JAVA_MAJOR_VERSION:-0}" -ge "10" ]; then
    return
  elif [ "${CONTAINER_MAX_MEMORY}" -le 314572800 ]; then
    # Restore the one-fourth default heap size instead of the one-half below 300MB threshold
    # See https://docs.oracle.com/javase/8/docs/technotes/guides/vm/gctuning/parallel.html#default_heap_size
    calc_mem_opt "${CONTAINER_MAX_MEMORY}" "25" "mx"
    calc_mem_opt "${CONTAINER_MAX_MEMORY}" "10" "X:MaxNewSize="
  else
    calc_mem_opt "${CONTAINER_MAX_MEMORY}" "50" "mx"
    calc_mem_opt "${CONTAINER_MAX_MEMORY}" "20" "X:MaxNewSize="
  fi
}

# Check for memory options and set initial heap size if requested
calc_init_memory() {
  # Check whether -Xms is already given in JAVA_OPTIONS.
  if echo "${JAVA_OPTIONS:-}" | grep -q -- "-Xms"; then
    return
  fi

  if [ -z "${CONTAINER_MAX_MEMORY:-}" ]; then
    return
  fi

  if [ -n "${JAVA_INIT_MEM_RATIO:-}" ]; then
    if [ "${JAVA_INIT_MEM_RATIO}" -eq 0 ]; then
      # Explicitely switched off
      return
    fi
    # Calculate Xms from the ratio given
    calc_mem_opt "${CONTAINER_MAX_MEMORY}" "${JAVA_INIT_MEM_RATIO}" "ms"
  else
    if [ "${CONTAINER_MAX_MEMORY}" -le 314572800 ]; then
      # Restore the one-fourth default heap size instead of the one-half below 300MB threshold
      # See https://docs.oracle.com/javase/8/docs/technotes/guides/vm/gctuning/parallel.html#default_heap_size
      calc_mem_opt "${CONTAINER_MAX_MEMORY}" "25" "ms"
    else
      calc_mem_opt "${CONTAINER_MAX_MEMORY}" "50" "ms"
    fi
  fi
}

calc_mem_opt() {
  local max_mem="$1"
  local fraction="$2"
  local mem_opt="$3"

  local val=$(calc 'round($1*$2/100/1048576)' "${max_mem}" "${fraction}")
  echo "-X${mem_opt}${val}m"
}

c2_disabled() {
  if [ -n "${CONTAINER_MAX_MEMORY:-}" ]; then
    # Disable C2 compiler when container memory <=300MB
    if [ "${CONTAINER_MAX_MEMORY}" -le 314572800 ]; then
      echo true
      return
    fi
  fi
  echo false
}

jit_options() {
  if [ "${JAVA_MAJOR_VERSION:-0}" -ge "10" ]; then
    return
  fi
  # Check whether -XX:TieredStopAtLevel is already given in JAVA_OPTIONS
  if echo "${JAVA_OPTIONS:-}" | grep -q -- "-XX:TieredStopAtLevel"; then
    return
  fi
  if [ $(c2_disabled) = true ]; then
    echo "-XX:TieredStopAtLevel=1"
  fi
}

# Switch on diagnostics except when switched off
diagnostics_options() {
  if [ -n "${JAVA_DIAGNOSTICS:-}" ]; then
    if [ "${JAVA_MAJOR_VERSION:-0}" -ge "11" ]; then
      echo "-XX:NativeMemoryTracking=summary -Xlog:gc*:stdout:time -XX:+UnlockDiagnosticVMOptions"
    else
      echo "-XX:NativeMemoryTracking=summary -XX:+PrintGC -XX:+PrintGCDateStamps -XX:+PrintGCTimeStamps -XX:+UnlockDiagnosticVMOptions"
    fi
  fi
}

# Replicate thread ergonomics for tiered compilation.
# This could ideally be skipped when tiered compilation is disabled.
# The algorithm is taken from:
# OpenJDK / jdk8u / jdk8u / hotspot
# src/share/vm/runtime/advancedThresholdPolicy.cpp
ci_compiler_count() {
  local core_limit="$1"
  local log_cpu=$(calc 'log2($1)' "$core_limit")
  local loglog_cpu=$(calc 'log2(max2($1,1))' "$log_cpu")
  local count=$(calc 'max2($1*$2,1)*3/2' "$log_cpu" "$loglog_cpu")
  local c1_count=$(calc 'max2($1/3,1)' "$count")
  local c2_count=$(calc 'max2($1-$2,1)' "$count" "$c1_count")
  [ $(c2_disabled) = true ] && echo "$c1_count" || echo $(calc '$1+$2' "$c1_count" "$c2_count")
}

cpu_options() {
  # JVMs >= 10 know about CPU limits
  if [ "${JAVA_MAJOR_VERSION:-0}" -ge "10" ]; then
    return
  fi

  local core_limit="${JAVA_CORE_LIMIT:-}"
  if [ "$core_limit" = "0" ]; then
    return
  fi

  if [ -n "${CONTAINER_CORE_LIMIT:-}" ]; then
    if [ -z ${core_limit} ]; then
      core_limit="${CONTAINER_CORE_LIMIT}"
    fi
    echo "-XX:ParallelGCThreads=${core_limit} " \
         "-XX:ConcGCThreads=${core_limit} " \
         "-Djava.util.concurrent.ForkJoinPool.common.parallelism=${core_limit} " \
         "-XX:CICompilerCount=$(ci_compiler_count $core_limit)"
  fi
}

#-XX:MinHeapFreeRatio=20  These parameters tell the heap to shrink aggressively and to grow conservatively.
#-XX:MaxHeapFreeRatio=40  Thereby optimizing the amount of memory available to the operating system.
heap_ratio() {
  echo "-XX:MinHeapFreeRatio=20 -XX:MaxHeapFreeRatio=40"
}

# These parameters are necessary when running parallel GC if you want to use the Min and Max Heap Free ratios.
# Skip setting gc_options if any other GC is set in JAVA_OPTIONS.
# -XX:GCTimeRatio=4
# -XX:AdaptiveSizePolicyWeight=90
gc_options() {
  local opts=""
  if echo "${JAVA_OPTIONS:-}" | grep -q -- "-XX:.*Use.*GC"; then
    opts="${opts}"
  else
    # for JVMs < 10 set GC settings
    if [ -z "${JAVA_MAJOR_VERSION:-}" ] || [ "${JAVA_MAJOR_VERSION:-0}" -lt "10" ]; then
      opts="${opts} -XX:+UseParallelGC -XX:GCTimeRatio=4 -XX:AdaptiveSizePolicyWeight=90 $(heap_ratio)"
    fi
    if [ -z "${JAVA_MAJOR_VERSION:-}" ] || [ "${JAVA_MAJOR_VERSION:-}" != "7" ]; then
      opts="${opts} -XX:+ExitOnOutOfMemoryError"
    fi
  fi

  # gc_log
  opts="${opts} -verbose:gc -Xloggc:${APP_LOG_PATH}/gc_log/gc.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=20 -XX:GCLogFileSize=50m"
  echo $opts
}

java_mixed_options() {
  local opts="-Djava.awt.headless=true"
  opts="${opts} -Dmanagement.port=-1 -Dshell.telnet.enabled=false"
  opts="${opts} -Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false"

  # security
  opts="${opts} -Djava.security.egd=file:/dev/./urandom"

  # log
  opts="${opts} -DLOG_HOME=${APP_LOG_PATH}/app_log -Dapp.log.dir=${APP_LOG_PATH}/app_log -XX:ErrorFile=${APP_LOG_PATH}/java_error.log"

  # tomcat and access log
  opts="${opts} -Dserver.tomcat.basedir=${APP_ROOT_PATH} -Dserver.tomcat.access-log-enabled=true -Dserver.tomcat.accesslog.enabled=true"
  echo $opts
}

# env RUN_OPTIONS
run_options() {
  if [ "x$RUN_OPTIONS" != "x" ]; then
    RUN_OPTIONS="$(echo $RUN_OPTIONS | sed s#./config/logback.xml#${APP_ROOT_PATH}/config/logback.xml#g)"
    echo "$RUN_OPTIONS"
  fi
}

java_default_options() {
  # Echo options, trimming trailing and multiple spaces
  echo "$(memory_options) $(jit_options) $(diagnostics_options) $(cpu_options) $(gc_options) $(java_mixed_options) $(run_options)" | awk '$1=$1'

}

# ==============================================================================
# 3. Java agent: bds secure test

java_agent_options() {
  echo "$(agent_quantum)" | awk '$1=$1'
}

agent_quantum() {
  if [ "$TRUE_ENV" = "offline" ] && [ "X$VERSION_QUANTUM_OFFLINE" != "X" ]; then
    wget -q -O ./agent/apm-dist-${VERSION_QUANTUM_OFFLINE}.tar.gz ${HOST_REPO_MVN}/nexus/content/repositories/releases/com/wacai/middleware/apm-dist/${VERSION_QUANTUM_OFFLINE}/apm-dist-${VERSION_QUANTUM_OFFLINE}.tar.gz
    rm -rf ./agent/quantum-agent && test -f ./agent/apm-dist-${VERSION_QUANTUM_OFFLINE}.tar.gz && tar -xzf ./agent/apm-dist-${VERSION_QUANTUM_OFFLINE}.tar.gz -C ./agent/
    if [ $? -eq 0 ]; then
      echo "-javaagent:./agent/quantum-agent/quantum-agent-${VERSION_QUANTUM_OFFLINE}.jar"
    else
      echo "[ERROR] init agent_quantum failed" && exit 1
    fi
  fi

  if [ "$TRUE_ENV" = "online" ] && [ "X$VERSION_QUANTUM" != "X" ]; then
    wget -q -O ./agent/apm-dist-${VERSION_QUANTUM}.tar.gz ${HOST_REPO_MVN}/nexus/content/repositories/releases/com/wacai/middleware/apm-dist/${VERSION_QUANTUM}/apm-dist-${VERSION_QUANTUM}.tar.gz
    rm -rf ./agent/quantum-agent && test -f ./agent/apm-dist-${VERSION_QUANTUM}.tar.gz && tar -xzf ./agent/apm-dist-${VERSION_QUANTUM}.tar.gz -C ./agent/
    if [ $? -eq 0 ]; then
      echo "-javaagent:./agent/quantum-agent/quantum-agent-${VERSION_QUANTUM}.jar"
    else
      echo "[ERROR] init agent_quantum failed" && exit 1
    fi
  fi
}


# ==============================================================================
# 4. Java args: exec_args java_options classpath

# Set process name if possible
exec_args() {
  EXEC_ARGS=""
  if [ -n "${JAVA_APP_NAME:-}" ]; then
    # Not all shells support the 'exec -a newname' syntax..
    if $(exec -a test true 2>/dev/null); then
      echo "-a '${JAVA_APP_NAME}'"
    fi
  fi
}

# Combine all java options
java_options() {
  # Normalize spaces with awk (i.e. trim and elimate double spaces)
  # See e.g. https://www.physicsforums.com/threads/awk-1-1-1-file-txt.658865/ for an explanation
  # of this awk idiom
  echo "${JAVA_OPTIONS:-} $(run_java_options) $(debug_options) $(java_default_options) $(java_agent_options)" | awk '$1=$1'


}

# Fetch classpath from env or from a local "run-classpath" file
classpath() {
  local cp_path="."
  if [ "${JAVA_LIB_DIR}" != "${JAVA_APP_DIR}" ]; then
    cp_path="${cp_path}:${JAVA_LIB_DIR}"
  fi
  if [ -z "${JAVA_CLASSPATH:-}" ] && [ -n "${JAVA_MAIN_CLASS:-}" ]; then
    if [ -n "${JAVA_APP_JAR:-}" ]; then
      cp_path="${cp_path}:${JAVA_APP_JAR}"
    fi
    if [ -f "${JAVA_LIB_DIR}/classpath" ]; then
      # Classpath is pre-created and stored in a 'run-classpath' file
      cp_path="${cp_path}:$(format_classpath ${JAVA_LIB_DIR}/classpath ${JAVA_APP_JAR:-})"
    else
      # No order implied
      cp_path="${cp_path}:${JAVA_APP_DIR}/*"
    fi
  elif [ -n "${JAVA_CLASSPATH:-}" ]; then
    # Given from the outside
    cp_path="${JAVA_CLASSPATH}"
  fi
  echo "${cp_path}"
}

# Checks if a flag is present in the arguments.
hasflag() {
    local filters="$@"
    for var in $ARGS; do
        for filter in $filters; do
          if [ "$var" = "$filter" ]; then
              echo 'true'
              return
          fi
        done
    done
}

# ==============================================================================
# 5. CMD: options run

options() {
    if [ -z ${1:-} ]; then
      java_options
      return
    fi

    local ret=""
    if [ $(hasflag --debug) ]; then
      ret="$ret $(debug_options)"
    fi
    if [ $(hasflag --java-agent) ]; then
      ret="$ret $(java_agent_options)"
    fi
    if [ $(hasflag --java-default) ]; then
      ret="$ret $(java_default_options)"
    fi
    if [ $(hasflag --memory) ]; then
      ret="$ret $(memory_options)"
    fi
    if [ $(hasflag --jit) ]; then
      ret="$ret $(jit_options)"
    fi
    if [ $(hasflag --diagnostics) ]; then
      ret="$ret $(diagnostics_options)"
    fi
    if [ $(hasflag --cpu) ]; then
      ret="$ret $(cpu_options)"
    fi
    if [ $(hasflag --gc) ]; then
      ret="$ret $(gc_options)"
    fi
    if [ $(hasflag --run) ]; then
      ret="$ret $(run_options)"
    fi
    if [ $(hasflag --mixed) ]; then
      ret="$ret $(java_mixed_options)"
    fi

    echo $ret | awk '$1=$1'
}

# Start JVM
run() {
  # Initialize project structure
  init_structure

  load_config_file
  load_strongbox
  load_key_escrow

  local args
  cd ${JAVA_APP_DIR}
  if [ -n "${JAVA_MAIN_CLASS:-}" ] ; then
     args="${JAVA_MAIN_CLASS}"
  elif [ -n "${JAVA_APP_JAR:-}" ]; then
     args="-jar ${JAVA_APP_JAR}"
  else
     echo "Either JAVA_MAIN_CLASS or JAVA_APP_JAR needs to be given"
     exit 1
  fi

  # Don't put ${args} in quotes, otherwise it would be interpreted as a single arg.
  # However it could be two args (see above). zsh doesn't like this btw, but zsh is not
  # supported anyway.
  local exec_args=$(exec_args)
  local java_options=$(java_options)
  local classpath=$(classpath)
  check_error "$java_options"

  echo exec $exec_args java $java_options -cp "$classpath" ${args} "$@"
  exec $exec_args java $java_options -cp "$classpath" ${args} "$@"
}

# =============================================================================
# 6. Fire up

# Set env vars reflecting limits
init_limit_env_vars
# Initialize environment
 $(script_dir)


first_arg=${1:-}
if [ "${first_arg}" = "options" ]; then
  # Print out options only
  shift
  options $@
  exit 0
elif [ "${first_arg}" = "run" ]; then
  # Run is the default command, but can be given to allow "options"
  # as first argument to your
  shift
fi
run "$@"