function pre_check() {
  [ ! -d "./bin" ] && [ ! -d "./config" ] && echo "[ERROR] please run this script in the project root path!" && exit 1
  [ ! -f "./config/application.properties" ] && echo "[ERROR] config file(application.properties) not found!" && exit 1

  local script_dir=`pwd`
  local pid_file="$script_dir/../.service_pid"
  [ ! -f "$pid_file" ] && local pid_file="$script_dir/.service_pid"
  if [ -f "$pid_file" ]; then
    local proc_id=$(cat $pid_file)
    if ps -p $proc_id >/dev/null 2>&1; then
      echo "[ERROR] please run stop.sh first [PID: ${proc_id}]" && exit 1
    fi
  fi
}

function get_default_ip() {
  local ip=$( /sbin/ifconfig | grep "inet " | awk '{print $2}' | sed 's/addr://' | grep "$NETWORK" | head -1 )
  [ ! -z "$ip" ] && echo $ip && return
  /sbin/ifconfig | grep "inet " | awk '{print $2}' | sed 's/addr://' | grep -v "127.0.0.1" | head -1
}

function get_jmx_port() {
  if [ $(is_available_port $JMX_PORT) = "yes" ];then
    echo $JMX_PORT && return
  fi

  for port in `seq 9999 10000 49999`;do
    if [ $(is_available_port $port) = "yes" ];then
      echo $port && return
    fi
  done

  for port in `seq 50999 500 59999`;do
    if [ $(is_available_port $port) = "yes" ];then
      echo $port && return
    fi
  done

  echo "[ERROR] cannot found available jmx port" && exit 1
}

function get_jolokia_port() {
  if [ $(is_available_port $JOLOKIA_PORT) = "yes" ];then
    echo $JOLOKIA_PORT && return
  fi

  for port in `seq 7777 10000 47777`;do
    if [ $(is_available_port $port) = "yes" ];then
      echo $port && return
    fi
  done

  for port in `seq 50777 500 57777`;do
    if [ $(is_available_port $port) = "yes" ];then
      echo $port && return
    fi
  done

  echo "[ERROR] cannot found available jolokia port" && exit 1
}

function is_available_port() {
  local port=$1
  if [[ "$OSTYPE" = *linux* ]];then
    r=$( netstat -ant | awk '$4~":'$port'$"' )
  elif [[ "$OSTYPE" = *darwin* ]];then
    r=$( netstat -ant | awk '$6=="LISTEN"' | grep "\.$port " )
  else
    echo "[ERROR] unknown system" && exit 1
  fi

  if [ -z "$r" ];then
    echo "yes" # available
  else
    echo "no" # port has been used
  fi
}

function init_and_set_env() {
  echo "[INFO] prepare to start service"

  source "./bin/setenv.sh"

  APP_ENV=${ENV}
  JAVA_APP_ENV=${ENV}

  [ -z $JAVA_HOME ] && export JAVA_HOME=/data/program/java8
  JAVA_CMD="${JAVA_HOME}/bin/java"
  SCRIPT_DIR=`pwd`
  SERVICE_HOME="$( dirname $SCRIPT_DIR )"
  PID_FILE="$SCRIPT_DIR/.service_pid"
  PID_FILE2="$SCRIPT_DIR/../.service_pid"
  JOLOKIA_FILE="$SCRIPT_DIR/.jolokia_port"
  JOLOKIA_FILE2="$SCRIPT_DIR/../.jolokia_port"

  SERVER_PORT=`cat ./config/application.properties | grep '^server.port=' | cut -b 13-`
  [ ! -n "$SERVER_PORT" ] && SERVER_PORT=8080
  SERVER_PORT=`echo ${SERVER_PORT} | sed 's/\r//g'`

  # LOG
  # LOG_HOME /data/program to /data/program/logs
  LOG_DIR="${SCRIPT_DIR}"
  LOG="$LOG_DIR/stdout.log"
  LOG_DIR_TMP=${LOG_DIR%/*}
  LOG_DIR_TMP2=/data/program/logs${LOG_DIR_TMP:13}
  LOG_ACCESS=${LOG_DIR_TMP2}/access_log
  LOG_HOME=${LOG_DIR_TMP2}/app_log
  set_access_log
  set_stdout_log
  set_gc_log
  echo "[INFO] STDOUT: $LOG"
  echo "[INFO] LOG HOME: $LOG_HOME"
  echo "[INFO] ACCESS_LOG HOME: $LOG_ACCESS"

  # TRUE_ENV online or offline
  TRUE_ENV="offline"
  if [[ $ENV == production || $ENV == staging || $LOCAL_IP == 192.168.4.* ]]; then
    TRUE_ENV="online"
  fi
  echo "[INFO] TRUE_ENV : $TRUE_ENV"

  # springboot
  init_springboot

  # strongbox
  set_strongbox

  # export env
  export ARTIFACT GROUP APP_IDC APP_ENV JAVA_APP_ENV

  # export KEY_ESCROW env
  export KEY_ESCROW_APP_SECRET
}

function set_access_log() {
  rm -rf $LOG_DIR_TMP2/access_log
  ln -s ${LOG_DIR_TMP}/logs $LOG_DIR_TMP2/access_log
}

function set_stdout_log() {
  if [ -f $LOG ]; then
    mv $LOG ${LOG}.bak
  fi

  mkdir -p ${LOG_DIR_TMP2}
  rm -rf ${LOG_DIR_TMP2}/stdout.log
  ln -s $LOG ${LOG_DIR_TMP2}/stdout.log
}

function set_gc_log() {
  mkdir -p $LOG_DIR/gc_log

  rm -rf $LOG_DIR_TMP2/gc_log
  ln -sf $LOG_DIR/gc_log ${LOG_DIR_TMP2}/gc_log
}

function init_springboot() {
  echo -e "\n\n# obelisk automatically generated configuration items" >> ./config/application.properties
  grep "server.tomcat.access-log-pattern" ./config/application.properties || echo "server.tomcat.access-log-pattern=%h %l %u %t \"%r\" %s %b %D" >> ./config/application.properties
  echo "management.health.mail.enabled=false" >> ./config/application.properties
}

function set_strongbox() {
  if [[ $TRUE_ENV == "online" ]]; then
    export DRUID_DECRYPT_KEY=`curl -s -k https://strongbox.wacai.info/key/${GROUP}/${ARTIFACT}?token=6ac2764a8d8745219e73a9700cf6da9d` JDBC_PROPS_URL=https://strongbox.wacai.info/props/${GROUP}/${ARTIFACT}
  else
    export DRUID_DECRYPT_KEY=`curl -s -k https://strongbox.test.wacai.info/key/${GROUP}/${ARTIFACT}?token=6ac2764a8d8745219e73a9700cf6da9d` JDBC_PROPS_URL=https://strongbox.test.wacai.info/props/${GROUP}/${ARTIFACT}
  fi

  echo "[INFO] strongbox: $JDBC_PROPS_URL"
}

function init_java_opts() {
  echo "[INFO] init java options..."
  
  CUSTOM_JAVA_OPTS="$JAVA_OPTS"
  JAVA_OPTS=""
  set_java_opts
  set_javaagent_opts
  JAVA_OPTS="$JAVA_OPTS $CUSTOM_JAVA_OPTS"
  
  echo "[INFO] java options: $JAVA_OPTS"
}

function set_java_opts() {
  JAVA_OPTS="$JAVA_OPTS -verbose:gc -Xloggc:$LOG_DIR/gc_log/gc.log -XX:+PrintGCDetails -XX:+PrintGCDateStamps -XX:+UseGCLogFileRotation -XX:NumberOfGCLogFiles=20 -XX:GCLogFileSize=50m"
  JAVA_OPTS="$JAVA_OPTS -XX:+HeapDumpOnOutOfMemoryError -XX:HeapDumpPath=$LOG_DIR/java.hprof"
  JAVA_OPTS="$JAVA_OPTS -XX:ErrorFile=$LOG_DIR/java_error.log"
  JAVA_OPTS="$JAVA_OPTS -Djava.awt.headless=true"
  JAVA_OPTS="$JAVA_OPTS -Djava.security.egd=file:/dev/./urandom"

  # log and tomcat
  JAVA_OPTS="$JAVA_OPTS -DLOG_HOME=$LOG_HOME"
  JAVA_OPTS="$JAVA_OPTS -Dapp.log.dir=$LOG_HOME"
  JAVA_OPTS="$JAVA_OPTS -Dserver.tomcat.access-log-enabled=true"
  JAVA_OPTS="$JAVA_OPTS -Dserver.tomcat.basedir=$LOG_DIR_TMP"

  # spring boot management and jmx
  JAVA_OPTS="$JAVA_OPTS -Dmanagement.port=-1"
  JAVA_OPTS="$JAVA_OPTS -Dendpoints.shutdown.enabled=true"
  JAVA_OPTS="$JAVA_OPTS -Dshell.telnet.enabled=false"
  set_jmx_java_opts
  
  # -XX
  xms_cust=$(echo $CUSTOM_JAVA_OPTS | grep "\-Xms")
  xmx_cust=$(echo $CUSTOM_JAVA_OPTS | grep "\-Xmx")
  meta_size_cust=$(echo $CUSTOM_JAVA_OPTS | grep "\-XX:MetaspaceSize")
  max_meta_size_cust=$(echo $CUSTOM_JAVA_OPTS | grep "\-XX:MaxMetaspaceSize")

  if [[ $TRUE_ENV == "online" ]]; then
    # JAVA_OPTS="$JAVA_OPTS -Xms2g -Xmx2g -XX:MetaspaceSize=38m -XX:MaxMetaspaceSize=380m"
    [[ $xms_cust == "" ]] && JAVA_OPTS="$JAVA_OPTS -Xms2g"
    [[ $xmx_cust == "" ]] && JAVA_OPTS="$JAVA_OPTS -Xmx2g"
    [[ $meta_size_cust == "" ]] && JAVA_OPTS="$JAVA_OPTS -XX:MetaspaceSize=38m"
    [[ $max_meta_size_cust == "" ]] && JAVA_OPTS="$JAVA_OPTS -XX:MaxMetaspaceSize=380m"
  else
    # JAVA_OPTS="$JAVA_OPTS -Xms1500m -Xmx1500m -XX:MetaspaceSize=25m -XX:MaxMetaspaceSize=250m"
    [[ $xms_cust == "" ]] && JAVA_OPTS="$JAVA_OPTS -Xms1500m"
    [[ $xmx_cust == "" ]] && JAVA_OPTS="$JAVA_OPTS -Xmx1500m"
    [[ $meta_size_cust == "" ]] && JAVA_OPTS="$JAVA_OPTS -XX:MetaspaceSize=25m"
    [[ $max_meta_size_cust == "" ]] && JAVA_OPTS="$JAVA_OPTS -XX:MaxMetaspaceSize=250m"
    JAVA_OPTS="$JAVA_OPTS -XX:NewSize=400m -XX:MaxNewSize=500m"
  fi

  # use custom gc？
  needsizedef=$(echo $CUSTOM_JAVA_OPTS | grep "UseG1GC")
  if [[ "$needsizedef" == "" ]]; then
    JAVA_OPTS="$JAVA_OPTS -XX:NewSize=600m -XX:MaxNewSize=750m"
  fi
  result=$(echo $CUSTOM_JAVA_OPTS | grep "UseSerialGC")$(echo $CUSTOM_JAVA_OPTS | grep "UseG1GC")$(echo $CUSTOM_JAVA_OPTS | grep "UseParallelGC")$(echo $CUSTOM_JAVA_OPTS | grep "UseParallelOldGC")  
  if [[ "$result" == "" ]]; then
    JAVA_OPTS="$JAVA_OPTS -XX:+UseConcMarkSweepGC -XX:CMSMaxAbortablePrecleanTime=5000 -XX:+CMSClassUnloadingEnabled -XX:CMSInitiatingOccupancyFraction=80 -XX:+UseCMSInitiatingOccupancyOnly"
  fi
}

function set_jmx_java_opts() {
  JMX_OPTS="-Dcom.sun.management.jmxremote.authenticate=false -Dcom.sun.management.jmxremote.ssl=false"
  [[ $TRUE_ENV == "online" ]] && JAVA_OPTS="$JAVA_OPTS $JMX_OPTS"

  JMX_OPTS="$JMX_OPTS -Djava.rmi.server.hostname=$LOCAL_IP"
  JMX_PORT=${JMX_PORT:-9999}
  local jmx_port=$(get_jmx_port)
  JMX_OPTS="$JMX_OPTS -Dcom.sun.management.jmxremote.port=$jmx_port"
  JMX_OPTS="$JMX_OPTS -Dcom.sun.management.jmxremote.rmi.port=$jmx_port"
  [[ $TRUE_ENV == "offline" ]] && JAVA_OPTS="$JAVA_OPTS $JMX_OPTS"
}

function set_javaagent_opts() {
  set_bds_cmd
  set_sccure_agent

  JAVA_AGENT_OPTS="$BDS_CMD $SECURE_AGENT"
  JAVA_OPTS="$JAVA_OPTS $JAVA_AGENT_OPTS"
}

function set_bds_cmd() {
  JOLOKIA_PORT=${JOLOKIA_PORT:-7777}
  JOLOKIA_PORT=$(get_jolokia_port)
  echo $JOLOKIA_PORT > "$JOLOKIA_FILE"
  echo $JOLOKIA_PORT > "$JOLOKIA_FILE2"

  # bds-agent
  if [ $TRUE_ENV == "offline" ]; then
    curl -s -o ./agent/bds-axb-1.1.15.jar http://repo.caimi-inc.com/nexus/content/repositories/releases/com/wacai/metric/bds-axb/1.1.15/bds-axb-1.1.15.jar
    if [ $? -eq 0 ]; then
      BDS_CMD="-javaagent:./agent/bds-axb-1.1.15.jar=config=http://bds-web.metric-test.k2.test.wacai.info/metric/config,port=${JOLOKIA_PORT},host=${LOCAL_IP}"
    fi
  fi

  if [ $TRUE_ENV == "online" ]; then
    curl -s -o ./agent/bds-axb-1.1.15.jar http://repo.caimi-inc.com/nexus/content/repositories/releases/com/wacai/metric/bds-axb/1.1.15/bds-axb-1.1.15.jar
    if [ $? -eq 0 ]; then
      BDS_CMD="-javaagent:./agent/bds-axb-1.1.15.jar=config=http://bds.wacai.info/metric/config,port=${JOLOKIA_PORT},host=${LOCAL_IP}"
    fi
  fi

}

function set_sccure_agent() {
  if [ "x$ENV_SECURE_AGENT" == "xtrue" ]; then
    curl -s -o ./agent/wac-rasp-1.1.1.jar http://repo.caimi-inc.com/nexus/service/local/repositories/releases/content/com/wacai/sec/wac-rasp/1.1.1/wac-rasp-1.1.1.jar
    if [ $? -eq 0 ]; then  
      SECURE_AGENT="-javaagent:./agent/wac-rasp-1.1.1.jar"
    fi
  fi
}

function start_service() {
  echo "[INFO] start service ..." 
  START_TIME=$(date +%s)
  nohup "$JAVA_CMD" $JAVA_OPTS -jar ./lib/$PROJECT_NAME >$LOG 2>$LOG &
  PROC_ID="$!"
  sleep 3
  if kill -0 "$PROC_ID" >/dev/null 2>&1; then
	  echo $PROC_ID > "$PID_FILE"
	  echo $PROC_ID > "$PID_FILE2"
    echo "[INFO] process started, pid: $PROC_ID"
    # default 3 minutes, interval 5 seconds
    if [ "x$ENV_START_WAITE_TIME" == "x" ]; then
      wait_service_start 180 5
    else
      wait_service_start $ENV_START_WAITE_TIME 5
    fi
  else
    echo "[ERROR] service start failed"
    analyze_fail_reason 
    exit 1
  fi
}

# parameter wait_time(seconds)
function wait_service_start() {
  local wait_time=${1}
  local step_time_span=${2}

  local i=0
  echo "[INFO] wait for service start ..."
  sleep $step_time_span
  while service_start_check
  do
    # timeout check
    ((i=$i+$step_time_span))
    if [ $i -gt $wait_time ]; then
      echo "[INFO] process info: " && ps -p $PROC_ID
      echo "[ERROR] wait start service timeout [$wait_time]"
      echo "[ERROR] start service failure cause: "
      echo "-- The keyword [JVM running for] that sprinboot started successfully flag was not detected in the app log"
      echo "-- Please check the app log"
      echo "-- This may be because the service is starting too slow or failing to start."
      exit 1
    fi

    echo "[INFO] wait for service start ..."
    sleep $step_time_span
  done
  
  echo "[INFO] service started"
}

function service_start_check() {
  if [ "x$ENV_NO_CHECK_HEALTH" == "xtrue" ]; then
    return 1
  fi
  # ps check
  if ! ps -p $PROC_ID >/dev/null 2>&1; then
    echo "[ERROR] wait start service failed"
    tail_app_log 50
    exit 1
  fi

  # log check
  local app_log_file=$(ls -t $LOG_HOME | head -1 | awk '{print i$0}' i=$LOG_HOME'/')
  echo "[INFO] Checking keyword [JVM running for] in the ${app_log_file}"
  if [ "x$app_log_file" != "x" ]; then
    local started_time=$(grep "seconds (JVM running for" $app_log_file | tail -1 | awk '{print $1i$2}' i=' ')
    if [[ "x$started_time" != "x" && $(date +%s -d "$started_time") -gt $START_TIME ]]; then
      local line=$(grep -n "seconds (JVM running for" $app_log_file | tail -1 | awk -F ':' '{print $1}')
      local line_start=$(($line-10))
      if [ $line_start -lt 0 ]; then
        line_start=0
      fi
      local line_end=$(($line+10))

      echo "[INFO] tail app log:" &&  sed -n "${line_start},${line_end}p" $app_log_file
      echo "[INFO] process info: " && ps -p $PROC_ID
      return 1
    else
      return 0
    fi
  else
    return 0
  fi
}

function tail_app_log() {
  local line=${1}
  local app_log_file=$(ls -t $LOG_HOME | head -1 | awk '{print i$0}' i=$LOG_HOME'/')
  if [ "x$app_log_file" != "x" ]; then
    echo "[INFO] tail app log:" && tail -$line $app_log_file
  fi
}

function analyze_fail_reason() {
  echo "[INFO] analyze the reasons of fail ..."
  echo "[INFO] print filesystem info:" && df -h 
  echo "[INFO] print memory info:" && free
  echo "[INFO] tail -100 stdout log:" && tail -100 $LOG
}

function services_register() {
  echo "[INFO] register ops services ..." 
  consul_register
}

function consul_register() {
  local i=0
  local result=$(curl -s --connect-timeout 5 -m 5 http://${LOCAL_IP}:${JOLOKIA_PORT}/jolokia/search/com.wacai.lifecycles:name=ShutdownLatch)
  while ! echo "$result" | grep -q "\"status\":200"
  do
    sleep 1
    result=$(curl -s --connect-timeout 5 -m 5 http://${LOCAL_IP}:${JOLOKIA_PORT}/jolokia/search/com.wacai.lifecycles:name=ShutdownLatch)
    ((i++))
    if (($i>2)); then
      break
    fi
  done
  
  if ! echo "$result" | grep -q "\"value\":\[\]"
  then
    # curl -s -l --connect-timeout 5 -m 5 -H "Content-type: application/json" -X POST -d "{\"id\": \"${ARTIFACT}\",\"name\": \"${ARTIFACT}\",\"tags\": [\"DUBBO\",\"${ARTIFACT}\",\"${GROUP}\",\"${GIT_REPO_URL}\",\"${JOLOKIA_PORT}\",\"${BRANCH_SPECIFIER}\"],\"address\": \"$(get_default_ip)\",\"port\": 80}" http://127.0.0.1:8500/v1/agent/service/register || echo "[WARN] consul agent error!"
    curl -s -l --connect-timeout 5 -m 5 -X PUT -d value="{\"id\": \"${ARTIFACT}\",\"name\": \"${ARTIFACT}\",\"tags\": [\"DUBBO\",\"${ARTIFACT}\",\"${GROUP}\",\"${GIT_REPO_URL}\",\"${JOLOKIA_PORT}\",\"${BRANCH_SPECIFIER}\"],\"address\": \"${LOCAL_IP}\",\"port\": 80}" http://etcd.ops.service.wacai.info:2379/v2/keys/obelisk3/register/${LOCAL_IP} || echo "[WARN] request etcd error!"
  else
    # curl -s -l --connect-timeout 5 -m 5 -H "Content-type: application/json" -X POST -d "{\"id\": \"${ARTIFACT}\",\"name\": \"${ARTIFACT}\",\"tags\": [\"REST\",\"${ARTIFACT}\",\"${GROUP}\",\"${GIT_REPO_URL}\",\"${JOLOKIA_PORT}\",\"${BRANCH_SPECIFIER}\"], \"Check\": {\"Interval\": \"5s\",\"TCP\": \"$(get_default_ip):${SERVER_PORT}\"},  \"port\": ${SERVER_PORT}, \"address\": \"$(get_default_ip)\"}" http://127.0.0.1:8500/v1/agent/service/register || echo "[WARN] consul agent error!"
    curl -s -l --connect-timeout 5 -m 5 -X PUT -d value="{\"id\": \"${ARTIFACT}\",\"name\": \"${ARTIFACT}\",\"tags\": [\"REST\",\"${ARTIFACT}\",\"${GROUP}\",\"${GIT_REPO_URL}\",\"${JOLOKIA_PORT}\",\"${BRANCH_SPECIFIER}\"], \"Check\": {\"Interval\": \"5s\",\"TCP\": \"${LOCAL_IP}:${SERVER_PORT}\"},  \"port\": ${SERVER_PORT}, \"address\": \"${LOCAL_IP}\"}" http://etcd.ops.service.wacai.info:2379/v2/keys/obelisk3/register/${LOCAL_IP} || echo "[WARN] request etcd error!"
  fi
}

pre_check
init_and_set_env
init_java_opts
start_service
services_register

echo ""
echo "[INFO] start service success"
