#!/bin/sh

script_dir() {
  # Default is current directory
  local dir=$(dirname "$0")
  local full_dir=$(cd "${dir}" && pwd)
  echo ${full_dir}
}

load_env() {
  local script_dir="$1"

  # Configuration stuff is read from this file
  local run_env_sh="setenv.sh"
  local run_plugin_sh="plugin.sh"

  # Load default default config
  if [ -f "${script_dir}/${run_env_sh}" ]; then
    . "${script_dir}/${run_env_sh}"
  fi

  if [ -f "${script_dir}/${run_plugin_sh}" ]; then
    . "${script_dir}/${run_plugin_sh}"
  fi

  load_config

 echo "hello"
 echo ${VERSION_QUANTUM_OFFLINE}

  #load_app_env

}

load_config() {
  if [ "$PLUGIN_ENABLE_OB_FE_CONFIG" != "0" ]; then
    echo "[INFO] HOST_ASSETS=${HOST_ASSETS}"
    wget -q ${HOST_ASSETS}/config -O /tmp/.obeliskx_config || exit 1
    if [ "$PLUGIN_ENABLE_OB_FE_GRAY" != "0" ]; then
      original_obelisk_config=$(cat /tmp/.obeliskx_config)
      local gray_obelisk_config=$(set_gray_obelisk_config "$original_obelisk_config")
      echo "$gray_obelisk_config" > /tmp/.obeliskx_config
    fi
    source /tmp/.obeliskx_config
    rm -rf /tmp/.obeliskx_config
  fi
}

function set_gray_obelisk_config() {
  local obelisk_config="$*"
  local gray_obelisk_config="$obelisk_config"
  gray_obelisk_config=$(apply_gray "OBELISK_CONFIG_MODULE" "$obelisk_config")
  gray_result=$?
  if [[ "$gray_result" == "0" ]];then
    echo "$gray_obelisk_config"
  else
    echo "$obelisk_config"
  fi
}

function apply_gray() {
  local gray_module="$1"
  local config=$(echo "$2" | sed ':label;N;s/\n/\\n/;b label')
  local env_level=$(echo "$APP_ENV" | tr 'a-z' 'A-Z')
  local GRAY_RULE_URL="${HOST_ASSETS}/grayRule/apply"
  local env_cluster=$(echo "$K2_SERVER_NAME" | tr 'a-z' 'A-Z')
  local GRAY_RULE_DATA="{\"val\":\"$config\",\"grayModule\":\"$gray_module\",\"projectType\":\"springboot\",\"envLevel\":\"${env_level}\",\"artifactId\":\"${ARTIFACT}\",\"envCluster\":\"${env_cluster}\"}"
  local result
  result=$(curl -s -w "\\n%{http_code}" -X POST -H "Content-Type:application/json" --data "${GRAY_RULE_DATA}" "$GRAY_RULE_URL")

  local http_code
  http_code=$(echo "$result" | tail -1)
  [[ "$http_code" != 200 ]] && echo "$config" && exit 1

  local code
  code=$(echo "$result" | sed '$d' | sed "s/'//g" | python -c 'import sys, json; print json.load(sys.stdin)["code"]' 2>/dev/null | sed 's/\"//g')
  if [[ "$code" == "0" ]]; then
    echo "$result" | sed '$d' | sed "s/'//g" | python -c 'import sys, json; print json.load(sys.stdin)["data"]' 2>/dev/null | sed 's/\\n/\n/g'
  else
    echo "$config"
  fi
}


load_env $(script_dir)