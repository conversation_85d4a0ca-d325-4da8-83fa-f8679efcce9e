#!/bin/bash

############################################################################
# @Usage
# sh kvrocks-start.sh port config_file
#
###########################################################################


port=$1
config_file=$2
kvrocks_dir=/data/program/sailfish/kvrocks/work/$port
kvrocks_log_dir=$kvrocks_dir/logs

check_param(){
  if [ ! "$port" ]; then
      echo '[ERROR] need input port!' && exit 1
  fi

  if [ ! "$config_file" ]; then
      echo '[ERROR] need input config_file!' && exit 1
  fi
}


create_dir(){
  if [ ! -e $1 ];then
    mkdir $1
    echo "mkdir $1 success"
  fi
}

start_kvrocks(){
  check_param

  create_dir $kvrocks_dir
  create_dir $kvrocks_log_dir

  if [[ $"`netstat -apn | awk '{print $4}' | awk -F ':' '{print $2}' | grep -w $port | uniq`" =~ $port ]]; then
    echo 'running';
  else
    echo "kvrocks -c $config_file";
  fi
}

start_kvrocks