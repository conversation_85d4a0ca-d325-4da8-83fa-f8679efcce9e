#!/bin/bash

############################################################################
# @Usage
# ./hotcode.sh $ip $java_file
#
###########################################################################



server_url="http://$1:8563/api"
java_file=$2
temp_file='/tmp/1.java'

function send() {

  if [ ! "$server_url" ]; then
      echo 'Need input server_url!' && exit 1
  fi

  if [ ! "$java_file" ]; then
    echo "Need input java file path, please." && return 1
  fi

  cat $java_file | jq -sRr @uri >$temp_file
  # read file to variable
  code=$(<$temp_file)
  rm $temp_file

  result=$(curl -s --location $server_url -X POST --header 'Content-Type: application/json' --data "{\"command\":\"hotcode $code\",\"action\":\"exec\",\"requestId\":\"123\",\"execTimeout\": \"60000\"}" )

  state=$(echo "$result" | awk -F 'state' '{print $2}'| awk -F '"' '{print $3}')

  echo $state
  echo "rowCount:"
  echo $result | jq -r '.body.results[0].rowCount'

}

send