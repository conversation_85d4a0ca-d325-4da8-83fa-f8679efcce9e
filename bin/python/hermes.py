# -*- coding:utf-8 -*-
#!/bin/python

import sys
import json
import urllib2
import threading
import time
import requests
import socket
import random
from threading import Event

#建议HEARTBEAT_API、APP_NAME放在配置文件
HEARTBEAT_API='http://hermes-center.middleware.wse.test.wacai.info/hermes-center/hb/client'
APP_NAME='python-biz-app'

FETCH_API='http://{}:{}/hermes-proxy/fetch?consumerId={}&appName={}&groupId={}&topic={}&fetchMax={}&reset={}'
ACK_API='http://{}:{}/hermes-proxy/ack?consumerId={}&appName={}&groupId={}&topic={}&partition={}&offset={}&clusterId={}'


#负责多个consumer的心跳，单例
class HermesController:
    def __init__(self):
        self.is_running=False
        self.consumers={}#通过一个map保存,key为groupId:topic
        self.appName=APP_NAME
        # 获取本机IP
        self.ip=socket.gethostbyname(socket.gethostname())
        self.exit = Event()

    def addConsumer(self,consumer):
        self.consumers[consumer.groupId+":"+consumer.topic]=consumer

    def startHeartbeat(self):
        #心跳线程，只启动一次，注意并发问题
        if not self.is_running:
            self.is_running = True
            self.heartbeat_thread = threading.Thread(target=self.heartbeat)
            self.heartbeat_thread.start()

    def heartbeat(self):
        while not self.exit.is_set():
            self.sendHeartbeat()
            #间隔10秒
            self.exit.wait(10)

    def sendHeartbeat(self):
        req={}
        req["appName"]=self.appName
        req["ip"]= self.ip
        req["port"]=8864 #可以写死

        # 当前时间
        req["timestamp"]=int(round(time.time()*1000))
        req["clientType"]="hermes-consumer"
        req["clientLanguage"]="python"
        req["appVersion"]="1.0"

        #消费者信息
        req["consumerInfos"] = []
        for key,consumer in self.consumers.items():
            consumerInfo={}
            consumerInfo["topic"]= consumer.topic
            consumerInfo["groupId"]= consumer.groupId
            consumerInfo["status"]= "ONLINE"
            consumerInfo["clusterId"]= consumer.clusterId
            consumerInfo["reset"]=consumer.reset
            consumerInfo["lastReceiveTime"]= consumer.lastReceiveTime
            req["consumerInfos"].append(consumerInfo)

        json_data = json.dumps(req)
        print("heartbeat..."+HEARTBEAT_API)
        print(json_data)

        #发送POST请求
        response = requests.post(HEARTBEAT_API, data=json_data, headers={"Content-Type": "application/json"})
        # 检查响应
        if response.status_code == 200:
            #print(response.text)
            response_json = response.json()
            code = response_json.get('code')
            if code==0:
                response_data = response_json.get('data')
                #返回的是list
                for consumerInfoResp in response_data.get('consumerInfoResps'):
                    #通过key定位到对应的consumer
                    consumer = self.consumers[consumerInfoResp.get('groupTopic')]
                    #更新consumer本地信息
                    #proxy地址列表[ {ip:"",port:""} ]
                    consumer.proxyServerList = consumerInfoResp.get('endPoints')
                    consumer.clusterId =consumerInfoResp.get('clusterId')
                    consumer.fetchMax =consumerInfoResp.get('batchSize')
                    consumer.reset = consumerInfoResp.get('reset')
        else:
            print("error:", response.status_code)

    def stop(self):
        self.exit.set() # 中断

hermesController = HermesController()

#负责消费
class HermesConsumer:

    def __init__(self, topic, groupId, callback):
        self.appName= APP_NAME
        self.topic=topic
        self.groupId=groupId
        self.callback=callback
        self.is_running=False
        self.clusterId=''#默认为空，通过心跳线程更新

        ##设置每次最多抓取的条数
        self.fetchMax = 200
        self.reset = 1
        # 获取本机IP
        self.ip=socket.gethostbyname(socket.gethostname())
        self.consumerId = self.ip
        # 上一次收到消息的时间
        self.lastReceiveTime=0

        #添加到hermesController
        hermesController.addConsumer(self)

    def start(self):
        if not self.is_running:
            self.is_running = True
            #先执行一次心跳，需要获取相关信息
            hermesController.sendHeartbeat()
            #启动心跳线程
            hermesController.startHeartbeat()

            #启动fetch线程
            self.fetch_thread = threading.Thread(target=self.fetch)
            self.fetch_thread.start()
            print("Hermes consumer started.")
        else:
          print("Hermes consumer is already running.")

    def getProxyNode(self):
        #如果地址列表只有一个节点直接返回
        size = len(self.proxyServerList)
        if size==1:
            return self.proxyServerList[0]
        else:
            #否则随机选择一个
            return self.proxyServerList.index(random.randint(0,size-1))

    def fetch(self):
        while self.is_running:
            #获取proxy节点
            node = self.getProxyNode()

            #格式化API
            fetch_api = FETCH_API.format(
                node.get('ip'),
                node.get('port'),
                self.consumerId,
                self.appName,
                self.groupId,
                self.topic,
                self.fetchMax,
                self.reset
                )

            print("fetch..."+fetch_api)

            # 设置请求头
            headers = {'fetch-type': 'long-fetch','client-type':'hermes-python-client-1.0'}
            response = requests.get(fetch_api,headers=headers)
            #处理返回的数据
            if response.status_code == 200:
                response_json = response.json()
                code = response_json.get('code')
                if code == 0:
                    self.process(response_json.get('data'))
                else:#说明有错，打印日志重试即可
                    print("fetch error "+str(response_json))
            else:
                print("Failed fetch")


    def process(self,data):
        print("Processing data: {} , {} , {} ".format(data.get('orignTotal'),data.get('startOffset'),data.get('endOffset')))
        #记录最后收到的时间
        self.lastReceiveTime = int(round(time.time()*1000))

        total =data.get('orignTotal')
        if total==0: ##没有数据
            return
        messageList = data.get('messageList')
        lastMessage = None
        #批量处理message
        for message in messageList:
            try:
                self.processMessage(message)
                lastMessage=message
            except Exception as e:
                #需要重试10次
                print("error")
        if lastMessage:
            self.ack(lastMessage)

    def processMessage(self,message):
        #调用回调函数
        self.callback(message)

    def ack(self,message):
        #获取节点
        node = self.getProxyNode()

        #格式化API
        ack_api = ACK_API.format(
            node.get('ip'),
            node.get('port'),
            self.consumerId,
            self.appName,
            self.groupId,
            self.topic,
            message.get('partition'),
            int(message.get('offset'))+1, #注意ack是最后一条消息offset+1
            self.clusterId)

        print("ack..."+ack_api)
        response = requests.get(ack_api)
        #处理返回的数据
        if response.status_code == 200:
            response_json = response.json()
            code = response_json.get('code')
            if code == 0:
                print("ack ok "+str(response_json))
            else:
                print("ack error "+str(response_json))
        else:
            print("Failed ack")

    def stop(self):
        if self.is_running:
            self.is_running = False
            self.fetch_thread.join()  # 等待线程结束
            print("Hermes consume stopped.")
        else:
            print("Hermes consume is not running.")

# 示例回调方法
def example_callback(message):
    print("receive message offset: "+str(message.get("offset")))


if __name__ == "__main__":

    consumer1 = HermesConsumer("client.photon.crawler","python.biz.group3",example_callback)
    consumer1.start()

    # 当需要停止时，按Ctrl+C来停止线程
    # 需要kill执行： ps aux | grep python  | grep -v grep  | awk '{print $2}' |xargs kill
    try:
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        consumer1.stop()
        hermesController.stop()