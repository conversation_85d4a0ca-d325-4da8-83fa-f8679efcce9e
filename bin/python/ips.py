# -*- coding:utf-8 -*-
#!/bin/python
import sys
import json
import urllib2

API_URL='http://xconsole.devops.k2.wacai.info/api/v1/search/ips?ip=%s'

class Table:

    def __init__(self):
        self.rows = []
        self.colCount = 0

    def addRow(self,row):
        if len(self.rows)==0:
            self.colCount=len(row)
        self.rows.append(row)

    def setHeader(self,header):
        self.addRow(header)

    def write(self):
        width = [None]*self.colCount
        for row in self.rows:
            for i,col in enumerate(row):
                width[i] = max(width[i],len(col))

        sb=''
        header=True

        for row in self.rows:
            sb+="|"
            for i,col in enumerate(row):
                sb+=col
                #padding
                for j in xrange(width[i]-len(col)):
                        sb+=" "
                sb+="|"
            sb+='\n'
            if header:
                header=False
                sb+="|"
                for n in width:
                    for j in xrange(n):
                        sb+='-'
                    sb+='|'
                sb+='\n'
        print sb

def fetch():
    file = open(sys.argv[1], 'r')
    lines = file.readlines()
    ips = []
    for line in lines:
        ips.append(line.strip())

    table  = Table()
    table.setHeader(["ip","app","owner"])

    for ip in ips:
        request=API_URL%(ip)

        response = json.loads(urllib2.urlopen(request).read())
        if(response['data']):
            data = response['data'][0]
            if(data['container']['env']!=""):
                app=data['container']['application']['url']
                owner=data['container']['owners'][0]
                table.addRow([ip,app,owner])
            else:
                app=data['no_container']['nodegroup']
                owner=data['no_container']['owners'][0]
                table.addRow([ip,app,owner])
        else:
            print 'ip %s no data'%(ip)
    table.write()

def main():
   fetch()

def test():
    table  = Table()
    table.setHeader(['id','first name'])
    table.addRow(['101','jack'])
    table.addRow(['102','jack ma'])
    table.write()

if __name__ == "__main__":
    main()