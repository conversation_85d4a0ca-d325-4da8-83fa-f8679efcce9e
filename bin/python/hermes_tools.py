# -*- coding:utf-8 -*-
#!/bin/python

#########################################
# a tool for find un-produced topic
#########################################

import sys
import json
import urllib2

QSH="http://proxy.hermes.middleware.k2.wacai.info/hermes-proxy/offset/range?topic=%s"
TEST="http://hermes-proxy.wse-test-middleware.wke-office.test.wacai.info/hermes-proxy/offset/range?topic=%s"


API_URL=TEST

def find():
    file = open(sys.argv[1], 'r')
    lines = file.readlines()
    topics = []
    for line in lines:
        line=line.strip()
        if(line==""):
            continue
        topics.append(line)

    for topic in topics:
        request=API_URL%(topic)
        response = json.loads(urllib2.urlopen(request).read())
        if(response['code']==0):
            beginOffset = response['data']['beiginOffset']
            endOffset = response['data']['endOffset']
            unProduced = beginOffset==endOffset
            print("%s \t %s \t %s \t %s"%(topic,beginOffset,endOffset,unProduced))


def main():
   find()

if __name__ == "__main__":
    main()
