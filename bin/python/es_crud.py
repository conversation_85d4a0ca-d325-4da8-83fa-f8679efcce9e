# -*- coding:utf-8 -*-
#!/bin/python
import requests
import json

# Elasticsearch服务器的地址
elasticsearch_url = "http://*************:9200"
#elasticsearch_url = "http://localhost:9200"
index_name = "bairen_test"

# 生成5000 条测试数据
bulk_data = []
for i in range(1, 1000):
    document = {
        "index": {
            "_index": index_name,
            "_type":"_doc",
            "_id": str(i)
        }
    }
    data = {
        "name": "jack-{}".format(i),
        "city":"hangzhou",
        "age": i,
        "@timestamp":"2023-10-07T10:20:16.000+08:00"
    }
    bulk_data.append(document)
    bulk_data.append(data)



# 转换数据为NDJSON格式
ndjson_data = "\n".join([json.dumps(action) for action in bulk_data]) + "\n"


# 执行批量操作
headers = {'Content-Type': 'application/json'}
response = requests.post("{}/_bulk?refresh=true".format(elasticsearch_url), data=ndjson_data, headers=headers)

# 检查响应
if response.status_code == 200:
    result = response.json()
    successful = result['items']
    print("成功操作数: {}".format(len(successful)))
else:
    print("批量操作失败")
    print(response.text)
