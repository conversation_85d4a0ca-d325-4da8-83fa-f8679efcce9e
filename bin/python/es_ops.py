import requests
import sys
import commands
import urllib2
import urllib


es_host='***********'
es_port='9201'

#es_host='***********'
#es_port='9200'

es_url='http://'+es_host+':'+es_port

def get_indices(index_prefix):
    if len(index_prefix)==0:
        print "index_prefix is empty"
        return []
    url = es_url+'/_cat/indices/'+index_prefix+'*'
    print 'url=%s'%(url)
    f = urllib2.urlopen(url)
    content = f.read()
    indexList =  content.split('\n')
    indicesList = []
    for index_var in indexList :
        index_nameList =  index_var.split()
        if(len(index_nameList)>0):
            index = index_nameList[2]
            indicesList.append(index)
    print indicesList
    return indicesList

def delete_index(lines):
    for line in lines:
        line = line.strip().replace('\n', '').replace('\r', '')
        index_nameList = get_indices(line)
        for index_name in index_nameList:
            url = es_url+'/'+index_name
            #print url
            requests.delete(url)


if __name__ == '__main__':
    file = open(sys.argv[1], 'r')
    lines = file.readlines()
    delete_index(lines)