# -*- coding:utf-8 -*-
# this is a chat code
import matplotlib.pyplot as plt
import numpy as np

# 数据
commands = ['GET', 'SET', 'INCR', 'LPUSH', 'RPUSH', 'MSET_10', 'LPOP', 'SPOP', 'SADD', 'HSET', 'ZADD']
redis_data = [99502, 99009, 99206, 79302,99108, 79113, 99304, 99403, 99403, 79617, 99304]
kvrock_data = [66401, 19916, 18089, 13736,13271,10763,11055,33189,16581,15323,14751]

# 设置y轴的刻度和标签
y_pos = np.arange(len(commands))
plt.yticks(y_pos, commands)

# 绘制两个柱状图
plt.barh(y_pos, redis_data, align='center', alpha=0.3, color='red', label='Redis')
plt.barh(y_pos, kvrock_data, align='center', alpha=0.5, color='blue', label='KVrock')

# 设置标签和标题
plt.xlabel('QPS On Command')
plt.ylabel('Commands')
plt.title('Redis vs KVrock performance comparison')

# 显示图例
plt.legend()

# 显示图像
plt.show()