创建一个python脚本，这个脚本会根据一个app_list数组，比如
```
app_list = [
    "sxb-position-provider1",
    "sxb-position-provider2",
    "sxb-position-provider3",
    "sxb-position-provider4",
    "sxb-position-provider5",
]
```
然后批量POST请求一个http接口：
```
http://quantum-observe-console.quantum.wke-office.test.wacai.info//metrics/summary/all
```

请求参数：
```
{"appName":"sxb-position-provider1","endTime":1728552688454,"startTime":1728549088453}
```
注意参数中的appName是app_list中的一个，需要进行base64处理一下，然后加上字符串常量.1，startTime是昨天0点的时间戳，endTime是今天0点的时间戳。返回json数据，你可以假设这个http接口返回的json数据格式如下：
```
{
    "code": 200,
    "data": {
        "appName": "sxb-position-provider",
        "count": "484487",
        "error": 18
    }
}
```
这样你就得到每个app对应的count和error，然后你需要把这个list的数据批量写入elasticsearch，地址是http://es.test.wacai.info/，写入的索引是fund_app_state，写入的数据格式：
```
{
    "timestamp": "2024-06-10 00:00:00",
    "appName": "sxb-position-provider",
    "count": "484487",
    "error": 18
}
```
