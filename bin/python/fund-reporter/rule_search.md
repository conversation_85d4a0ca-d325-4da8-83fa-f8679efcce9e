从elasticsearch中查询数据, elasticsearch地址是http://10.1.136.51:9200,  查询的索引是fund_app_state, 查询条件 @timestamp 开始于今天0点，结束于今天12点，
按照count排序，只返回前25条数据，返回的数据格式如下：
```
{
    "@timestamp" : "2024-10-14 11:36:05",
    "appName" : "fund-pd-provider",
    "count" : 35374089,
    "error" : "0"
}
```

通过这批数据创建一个markdown文件，文件名为：2024-Q3应用软件运行状报告.md，文件内容为：
```
# 2024-Q3应用软件运行状报告
然后创建表格，格式如下：
| 应用名称 | 每日请求量 | 每日错误数 | 故障事件|
| -------- | -------- | -------- | -------- |
| ${appName} | ${count} | ${error} | 无 |

**总结**
- 合格线：错误占比小于1%
- 合格占比：100%
- 不合格占比：0%
```

