# 应用指标收集器

## 项目背景

这个项目旨在收集多个应用的指标数据，处理这些数据，并将其存储到 Elasticsearch 中。它主要用于监控各种基金相关应用的性能和错误率。

该脚本从指定的 API 获取应用列表的指标数据，然后将这些数据索引到 Elasticsearch 中，以便进行进一步的分析和可视化。

## 使用技术

- Python 3.x
- Requests 库用于 HTTP 请求
- Elasticsearch Python 客户端
- Base64 编码用于应用名称处理

## 安装方式

1. 克隆此仓库：
   ```
   git clone https://github.com/your-username/app-metrics-collector.git
   cd app-metrics-collector
   ```

2. 安装所需的 Python 包：
   ```
   pip install requests
   pip install elasticsearch==7.11.0
   ```

3. 在 `app_metrics_to_es.py` 文件中配置 `app_list`，列出您要监控的应用。

4. 确保您能访问脚本中指定的 API 端点和 Elasticsearch 实例。

## 使用方式

使用 Python 运行脚本：

```
python app_metrics_to_es.py
```

脚本将执行以下操作：
1. 遍历应用列表
2. 从指定的 API 获取每个应用的指标数据
3. 处理接收到的数据
4. 将处理后的数据索引到 Elasticsearch

## 配置

- API_URL：获取应用指标的端点
- Elasticsearch 连接：如有必要，更新 Elasticsearch 主机地址
- app_list：要监控的应用列表

## 数据格式

脚本为每个应用收集以下指标：
- 应用名称
- 请求计数
- 错误计数

这些数据随后会与时间戳一起存储在 Elasticsearch 中，便于查询和可视化。

## 注意事项

在运行脚本之前，请确保您有必要的权限来访问 API 和 Elasticsearch 实例。

