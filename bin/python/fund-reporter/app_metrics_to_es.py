# -*- coding:utf-8 -*-

import requests
from datetime import datetime, timedelta
from elasticsearch import Elasticsearch
import time
import base64
import re

# 环境准备：
# 安装python3 ，进入biz-app，执行 yum install python3
# pip3 install requests
# pip3 install elasticsearch==7.11.0


# 定义app列表
app_list = [
"fund-settle-provider",
"fund-account-provider",
"fund-agrt-provider",
"fund-aip-provider",
"fund-app",
"fund-business-provider",
"fund-daq-provider",
"fund-data-service",
"fund-ths-service",
"fund-fs-provider",
"fund-market-app",
"fund-ocs",
"fund-pacific-account-provider",
"fund-pacific-acquirer-provider",
"fund-pacific-antimoney-provider",
"fund-pacific-assets-provider",
"fund-pacific-audit-provider",
"fund-pacific-flowcontrol-provider",
"fund-pacific-gateway-provider",
"fund-pacific-instruct-provider",
"fund-pacific-ordermanage-provider",
"fund-pacific-params-provider",
"fund-pacific-recon-provider",
"fund-pacific-settle-provider",
"fund-pacific-tadataexchange-provider",
"fund-pacific-trade-provider",
"fund-pd-provider",
"fund-position-provider",
"fundsell-etl-task",
"fundsell-service-provider",
"fund-settle-provider",
"fund-analyse-provider",
"fund-sms-provider",
"fund-tradegateway-provider",
"fund-trade-provider",
"market-business-provider",
"market-mall-provider",
"sxb-app",
"sxb-bridge-provider",
"sxb-business-provider",
"sxb-position-provider",
"sxb-settle-provider",
"sxb-trade-provider",
"welfare-task-provider",
"fund-factor-app",
"fund-factor-service",
"fund-derived-service",
"fintech-market-app",
"fintech-market-service",
]

# 设置API URL
#API_URL = "http://quantum-observe-console.quantum.wke-office.test.wacai.info/metrics/summary/all"
API_URL = "http://quantum-observe-console.middleware.k2.wacai.info/metrics/summary/all"

# 设置Elasticsearch连接
#es = Elasticsearch(["http://172.16.49.101:9200/"]) 
es = Elasticsearch(["http://10.1.136.51:9200"])


# 在文件顶部添加这个新函数
def encode_app_name(app_name):
    return base64.b64encode(app_name.encode()).decode() + ".1"

def get_timestamps():
    today = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
    yesterday = today - timedelta(days=3)
    return int(yesterday.timestamp() * 1000), int(today.timestamp() * 1000)

def convert_chinese_number(chinese_number):
    if not chinese_number or chinese_number == '0':
        return 0
    
    # 处理包含"万"的情况
    if '万' in chinese_number:
        parts = chinese_number.split('万')
        if len(parts) == 2:
            wan = float(parts[0]) * 10000
            rest = float(parts[1]) if parts[1] else 0
            return int(wan + rest)
        else:
            return int(float(parts[0]) * 10000)
    
    # 如果没有"万"，直接转换为整数
    return int(chinese_number)

def fetch_app_metrics(app_name, start_time, end_time):
    encoded_app_name = encode_app_name(app_name)
    
    payload = {
        "appName": encoded_app_name,
        "startTime": start_time,
        "endTime": end_time
    }
    response = requests.post(API_URL, json=payload)
    return response.json()

def index_to_elasticsearch(data):
    # 获取当前时间，并设置为东八区（北京时间）
    current_time = datetime.now()
    
    # 格式化时间戳
    formatted_timestamp = current_time.strftime("%Y-%m-%dT%H:%M:%S.%f")[:-3] + current_time.strftime("%z")
    formatted_timestamp = formatted_timestamp+"+08:00"   

    # 更新数据中的时间戳
    data['@timestamp'] = formatted_timestamp

    print(f"Indexed data {data}")
    es.index(index="m-fund-app-state", body=data)

def main():
    start_time, end_time = get_timestamps()

    for app in app_list:
        try:
            print(f"Fetching data for {app}")

            response_data = fetch_app_metrics(app, start_time, end_time)
            
            print(response_data)

            if response_data["code"] == 0:
                count = convert_chinese_number(response_data["data"]["count"])
                es_data = { 
                    "appName": app,
                    "count": count,
                    "error": response_data["data"]["error"]
                }
                index_to_elasticsearch(es_data)
            else:
                print(f"Error fetching data for {app}: {response_data}")
        except Exception as e:
            print(f"Exception occurred while processing {app}: {str(e)}")
        
       

if __name__ == "__main__":
    main()
