# -*- coding:utf-8 -*-

from elasticsearch import Elasticsearch
from datetime import datetime, time


# 连接到 Elasticsearch
es = Elasticsearch(["http://10.1.136.51:9200"])

# 获取今天的日期范围
now = datetime.now()
today = now.date()
start_time = datetime.combine(today, time.min)
end_time = datetime.combine(today, time(23, 59))

# 转换为毫秒时间戳
start_timestamp = int(start_time.timestamp() * 1000)
end_timestamp = int(end_time.timestamp() * 1000)

# 构建查询
query = {
    "query": {
        "range": {
            "@timestamp": {
                "gte": start_timestamp,
                "lte": end_timestamp
            }
        }
    },
    "sort": [
        {"count": "desc"}
    ],
    "size": 25
}

# 执行查询
result = es.search(index="m-fund-app-state", body=query)
print(f"search data {result}")

# 生成 Markdown 报告
report = f"# {today.year}-Q3应用软件运行状报告\n\n"
report += "| 应用名称 | 每日请求量 | 每日错误数 | 故障事件 |\n"
report += "| -------- | -------- | -------- | -------- |\n"

for hit in result['hits']['hits']:
    source = hit['_source']
    report += f"| {source['appName']} | {source['count']} | {source['error']} | 无 |\n"

report += "\n**总结**\n"
report += "- 合格线：错误占比小于1%\n"
report += "- 合格占比：100%\n"
report += "- 不合格占比：0%\n"

# 将报告写入文件
filename = f"{today.year}-Q3应用软件运行状报告.md"
with open(filename, 'w', encoding='utf-8') as f:
    f.write(report)

print(f"报告已生成：{filename}")
