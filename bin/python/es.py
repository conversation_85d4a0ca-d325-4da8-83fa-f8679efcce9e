import requests
import sys
import commands
import urllib2
import urllib

es_host='***********'
es_port='9201'
es_url='http://'+es_host+':'+es_port
def getIndices(option):
    print 'get indices :' + option
    url = es_url+'/_cat/indices/*'+option
    f = urllib2.urlopen(url)

    content = f.read()

    indexList =  content.split('\n')

    indicesList = []
    for index_var in indexList :
        index_nameList =  index_var.split()
        if(len(index_nameList)>0):
            indices = index_nameList[2]
            #print indices
            if(option in indices):
                print indices
                indicesList.append(indices)
    #print indicesList
    return indicesList

def backup_index(index_name):
    print 'backup index :'
    print index_name
    payload = '{"indices": "'+index_name + '", "ignore_unavailable": "true", "include_global_state": "false"}'
    #print payload
    comm="curl -XPUT "+es_url+"/_snapshot/backup/"+ index_name + "?wait_for_completion=true -d '"+payload +"'"
    #print comm
    (status, output) = commands.getstatusoutput('%s ' % (comm))
    print 'success backup index:'+ index_name
    #delete_index(index_name)

import datetime

def day_backup_index():
    yesterday = datetime.date.today() - datetime.timedelta(days=3)
    #delete_index('w-user-service-provider-'+ str(yesterday))
    #delete_index('w-user-auth-service-'+ str(yesterday))
    delete_index('w-stock-web-tmp-'+ str(yesterday))
    delete_index('w-creditguard-'+ str(yesterday))
    delete_index('w-buffer-vm-'+ str(yesterday))
    #yesterday = '2017-06-01'
    print 'day backup index :' + str(yesterday)

    payload = '{"indices": "w-*-'+ str(yesterday) + ',c-*-' + str(yesterday) + ',wodlee-*-' + str(yesterday) + '", "ignore_unavailable": "true", "include_global_state": "false"}'
    print payload
    comm="curl -XPUT "+es_url+"/_snapshot/backup/es-"+ str(yesterday) + "?wait_for_completion=true -d '"+payload +"'"
    print comm
    #(status, output) = commands.getstatusoutput('%s ' % (comm))
    print 'success backup index:'+ str(yesterday)

def day_delete_index():

    w_exceptions = [
        "w-finance-api-gateway",
        "w-finance-app",
        "w-finance-position-service",
        "w-finance-sangreal-service",
        "w-wcb-market-provider",
        "w-fund-*",
        "w-cas-server",
        "w-fund_fund*",
        "w-fund_sxb*",
        "w-trident_sms-backendapplication-*"
    ]

    m_exceptions = [
        "m-finance-koa-react",
        "m-fund-*",
        "m-fund_wax*",
        "m-node-fund-node-payment"
    ]


    date_7_days_ago = datetime.date.today() - datetime.timedelta(days=7)
    date_3_days_ago = datetime.date.today() - datetime.timedelta(days=3)
    date_5_days_ago = datetime.date.today() - datetime.timedelta(days=5)

    #date_7_days_ago = '2017-06-24'
    date_month_ago = datetime.date.today() - datetime.timedelta(days=30)
    date_60_days_ago = datetime.date.today() - datetime.timedelta(days=60)
    date_180_days_ago = datetime.date.today() - datetime.timedelta(days=180)
    print date_month_ago
    #indices_list = getIndices(str(date_7_days_ago))

    print  'delete index :'+ str(date_7_days_ago)
    #delete index
    url=es_url+'/w-*'+ str(date_7_days_ago) + ',' + ','.join([ "-{i}*".format(i=i) for i in w_exceptions ])
    print url
    r = requests.delete(url)

    url=es_url+'/m-*'+ str(date_7_days_ago) + ',' + ','.join([ "-{i}*".format(i=i) for i in m_exceptions ])
    print url
    r = requests.delete(url)

    url=es_url+'/c-*-'+ str(date_7_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/t-*'+ str(date_7_days_ago) + '*'
    print url
    r = requests.delete(url)

    url=es_url+'/wodlee-*'+ str(date_month_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/c*-nginx-'+ str(date_7_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/w-de-*'+ str(date_3_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/w-variable-center*'+ str(date_5_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/w-fund*'+ str(date_180_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/m-*-fund*'+ str(date_180_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/w-cas-server-'+ str(date_180_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/w-finance-*'+ str(date_180_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/m-finance-koa-react-'+ str(date_180_days_ago)
    print url
    r = requests.delete(url)

    url=es_url+'/w-trident_sms-backendapplication-*'+ str(date_60_days_ago)
    print url
    r = requests.delete(url)

    #for index_name in indices_list:
#print index_name
#delete_index(index_name)


def day_close_index():
    date_7_days_ago = datetime.date.today() - datetime.timedelta(days=7)
    #date_7_days_ago = '2017-06-24'
    print date_7_days_ago

    print  'close index :'+ str(date_7_days_ago)
    #delete index
    url=es_url+'/w-*'+ str(date_7_days_ago) + '/_close'
    print url
    r = requests.post(url)

    url=es_url+'/m-*'+ str(date_7_days_ago) + '/_close'
    print url
    r = requests.post(url)

def delete_index(index_name):
    url=es_url+ '/'+str(index_name)
    print url
    r = requests.delete(url)


import time
def pre_create_index():
    date_tomorrow = datetime.date.today() - datetime.timedelta(days=-1)
    print date_tomorrow

    url=es_url+'/c-nginx-'+ str(date_tomorrow)
    print url
    r = requests.put(url)

    print datetime.date.today()
    indices_list = getIndices(str(datetime.date.today()))
    for index_name in indices_list:
        index_name = index_name.replace(str(datetime.date.today()),str(date_tomorrow))
        print index_name
        url=es_url+'/'+index_name
        print url
        r = requests.put(url)

if __name__ == '__main__':
    if sys.argv[1] == 'status':
        get_status()
    elif sys.argv[1] == 'day_backup_index':
        day_backup_index()
    elif sys.argv[1] == 'day_delete_index':
        day_delete_index()
    elif sys.argv[1] == 'day_close_index':
        day_close_index()
    elif sys.argv[1] == 'pre_create_index':
        pre_create_index()
    elif sys.argv[1] == 'day_delete_snapshot':
        day_delete_snapshot()
    elif sys.argv[1] == 'backup_index':
        backup_index(sys.argv[1])
    elif sys.argv[1] == 'get_bk_status':
        get_bk_status(sys.argv[2])
    elif sys.argv[1] == 'delete_snapshot':
        delete_snapshot(sys.argv[2])