## 10分钟shell
可以参考的脚本：
- 2024： redis/start_redis.sh

1、字符串中包含$变量，不能用单引号
```shell
    echo "$(date +"%Y-%m-%d %H:%M:%S") running" #正确
    echo '$(date +"%Y-%m-%d %H:%M:%S") running' #错误
```
2、函数中传递参数，使用$1,$2,$3...

3、IF 判断
```shell
check_port(){
   if [[ "$(ps aux | grep redis-server | grep $1 | awk '{print $2}')" =~ $1 ]]; then
    echo "$(date +"%Y-%m-%d %H:%M:%S") $1 already running"  >>/data/program/sailfish/reboot.log
    return 1
  else
    return 0
  fi
}
```
- [[ ... =~ $1 ]]：这是一个条件语句，用于匹配左边的字符串是否与右边的正则表达式 $1 匹配。如果匹配成功，则条件为真。
- "$()"：这个语法是命令替换，它会执行括号中的命令，并将结果作为一个单一的字符串返回。

