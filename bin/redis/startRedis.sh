#!/bin/bash

check_port(){
   if [[ -n "$(ps aux | grep redis-server | grep $1 | awk '{print $2}')" ]]; then
    echo "$(date +"%Y-%m-%d %H:%M:%S") $1 already running"  >>/data/program/sailfish/reboot.log
    return 1
  else
    return 0
  fi
}

wait_for_port(){
  local port=$1
  local max_attempts=10
  local attempt=0
  while [ $attempt -lt $max_attempts ]; do
    if netstat -tuln | grep ":$port " >/dev/null; then
      return 0
    else
      sleep 1
      attempt=$((attempt + 1))
    fi
  done
  return 1
}

start_redis_cluster(){
    check_port $1
    local result=$?
    if [ $result -eq 0 ]; then
      cd /data/program/sailfish
      echo "$(date +"%Y-%m-%d %H:%M:%S") start redis cluster $1" >> /data/program/sailfish/reboot.log
      /usr/local/bin/redis-server conf/redis-cluster-$1.conf >> logs/redis-$1.log &
      wait_for_port $1
      if [ $? -eq 0 ]; then
        echo "$(date +"%Y-%m-%d %H:%M:%S") redis cluster $1 started successfully" >> /data/program/sailfish/reboot.log
      else
        echo "$(date +"%Y-%m-%d %H:%M:%S") failed to start redis cluster $1: port not available" >> /data/program/sailfish/reboot.log
      fi
    fi
}

start_redis(){
  check_port $1
  local result=$?
  if [ $result -eq 0 ]; then
    cd /data/program/sailfish
    echo "$(date +"%Y-%m-%d %H:%M:%S") start redis $1" >> /data/program/sailfish/reboot.log
    /usr/local/bin/redis-server conf/redis-$1.conf >> logs/redis-$1.log &
    wait_for_port $1
    if [ $? -eq 0 ]; then
      echo "$(date +"%Y-%m-%d %H:%M:%S") redis $1 started successfully" >> /data/program/sailfish/reboot.log
    else
      echo "$(date +"%Y-%m-%d %H:%M:%S") failed to start redis $1: port not available" >> /data/program/sailfish/reboot.log
    fi
  fi
}

echo "============================================================" >> /data/program/sailfish/reboot.log
echo "$(date +"%Y-%m-%d %H:%M:%S") prepare start redis" >> /data/program/sailfish/reboot.log
start_redis_cluster 6379
start_redis_cluster 6380
#start_redis 6381
echo "============================================================" >> /data/program/sailfish/reboot.log
echo "exit" >> /data/program/sailfish/reboot.log
