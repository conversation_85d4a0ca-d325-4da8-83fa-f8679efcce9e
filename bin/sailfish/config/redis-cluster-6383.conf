bind 0.0.0.0
protected-mode no
port 6383
tcp-backlog 511
timeout 300
tcp-keepalive 300
daemonize no
supervised no
loglevel notice
databases 16
stop-writes-on-bgsave-error no
rdbcompression yes
rdbchecksum yes
dbfilename "dump-6383.rdb"
dir "/data/program/sailfish/data"
slave-serve-stale-data yes
slave-read-only yes
repl-diskless-sync no
repl-diskless-sync-delay 5
repl-ping-slave-period 10
repl-timeout 60
repl-disable-tcp-nodelay no
repl-backlog-size 10000000
repl-backlog-ttl 7200
slave-priority 100
min-slaves-to-write 0
min-slaves-max-lag 10
maxclients 10000
maxmemory 100mb
maxmemory-policy volatile-lru
maxmemory-samples 5
appendfilename "appendonly-6383.aof"
no-appendfsync-on-rewrite yes
auto-aof-rewrite-min-size 62500kb
auto-aof-rewrite-percentage 98
aof-load-truncated yes
lua-time-limit 5000
slowlog-log-slower-than 10000
slowlog-max-len 128
latency-monitor-threshold 0
notify-keyspace-events ""
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64
hll-sparse-max-bytes 3000
activerehashing yes
client-output-buffer-limit normal 0 0 0
client-output-buffer-limit slave 512mb 128mb 60
client-output-buffer-limit pubsub 32mb 8mb 60
hz 10
aof-rewrite-incremental-fsync yes
rename-command FLUSHALL rds_flushall
rename-command FLUSHDB rds_flushdb
rename-command KEYS rds_keys
rename-command MONITOR rds_monitor
appendonly no
appendfsync everysec
save 86400 100
save 86400 1000
save 43200 1000000
cluster-enabled yes
cluster-config-file "nodes-6383.conf"
cluster-node-timeout 15000
cluster-slave-validity-factor 10
cluster-migration-barrier 1
cluster-require-full-coverage no
