#!/bin/bash

#####################################
#see:http://git.caimi-inc.com/middleware/sailfish
#
#####################################


set -e -o pipefail
basedir=$(cd $(dirname ${BASH_SOURCE:-$0});pwd)
# init ssh login 注意不同环境修改 pub_key
pub_key='ssh-rsa AAAAB3NzaC1yc2EAAAABIwAAAQEA28PGGgY7uBPy+FBz03aeBaYCGMybDhM9HIdsA6eHO3rw472Kx/DVZAsPdNrAwdrQSZH4PoRJic1usxELDILFrvkU+ONwFTfmKM1ZGwd5CgQ05c9Bi3Wyq+s0q58F3uILWRtm0cA8GmNwuYw8Lv7Be8i8K6k4dslGGjHCxlqaYavoYYdat9C4WbvMR5od16PTScDHqXdwiK9N3M8l1Br8bDQAktBHvVD7+uTG+wzgKD9dRfR2vth3bCs2czFQVnHN8wwzZ7lPWr6891Umh5ArU0lKTQczuxvoOwIHcseoUKW9vGxHp5O8wQDCNC85edew6i2Qli8b4a/EnUiO4E3ECQ== <EMAIL>'
ssh_dir='/home/<USER>/.ssh'
auth_key='/home/<USER>/.ssh/authorized_keys'

if [ ! -e ${ssh_dir} ];then
	mkdir ${ssh_dir}
	echo mkdir ${ssh_dir} success
else
	echo "file ${ssh_dir} already exist, skip"
fi

if [ ! -e ${auth_key} ];then
	echo ${pub_key} > ${auth_key}
	echo add authorized_keys success
else
	echo file ${auth_key} already exist, please check
fi

chmod 700 ${ssh_dir}
chmod 600 ${auth_key}
chown -R redis:redis /home/<USER>/.ssh
echo add ssh pubKey done success

# modify sys env
cp /etc/sysctl.conf /etc/sysctl.conf.backup
sysctl vm.overcommit_memory=1
overcommit_memory=$(perl -lne 'print $1 if /^\s*vm.overcommit_memory\s*=\s*(\w+)\s*$/' /etc/sysctl.conf)
if [ -z "${overcommit_memory}" ];then
	echo vm.overcommit_memory = 1 >> /etc/sysctl.conf
elif [ "x${overcommit_memory}x" = "x0x" ];then
    perl -i.bak -lpe 's/^\s*vm.overcommit_memory\s*=\s*(\w+)\s*$/vm.overcommit_memory=1/' /etc/sysctl.conf
fi
echo 'set vm.overcommit_memory=1 done'
#
echo never > /sys/kernel/mm/transparent_hugepage/enabled
echo 'echo never > /sys/kernel/mm/transparent_hugepage/enabled' >> /etc/rc.local

#start check
echo ''
echo '--------------------- start check config -----------------------------------------'
echo ''
echo "------authorized_keys info : `ls -l /home/<USER>/.ssh/authorized_keys`"
echo ''
echo "------vm.overcommit_memory : `grep vm.overcommit_memory /etc/sysctl.conf`"
echo ''
echo "------/sys/kernel/mm/transparent_hugepage/enabled: `cat /sys/kernel/mm/transparent_hugepage/enabled`"
echo ''
echo "------/etc/rc.local : `grep /sys/kernel/mm/transparent_hugepage/enabled /etc/rc.local`"
echo ''
echo "------ulimit -n : `ulimit -n`"
echo ''
echo "------sysctl net.core.somaxconn: `sysctl net.core.somaxconn`"
echo ''
echo 'check done'