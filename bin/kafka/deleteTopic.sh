#!/bin/bash
set -e -o pipefail
BASE_DIR=$( cd "$(dirname "${BASH_SOURCE[0]}")" && pwd);
TOPIC_FILE=$1

KAFKA_BASE_DIR=/data/program/kafka
KAFKA_HOME=$KAFKA_BASE_DIR/kafka_2.13-2.8.0

delete_topic(){
  if [ ! "$TOPIC_FILE" ]; then
    echo "Error：Need topic file"
    return
  fi

  while IFS= read -r line; do
    echo $line
    $KAFKA_HOME/bin/kafka-topics.sh --zookeeper *************:2181,*************:2181,*************:2181/kfk1  --delete --topic $line
  done < "$TOPIC_FILE"

}

delete_topic