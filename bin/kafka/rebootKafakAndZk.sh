#!/bin/bash

check_zk_running(){
   if [[ -n "$(ps aux | grep java | grep zookeeper.properties | awk '{print $2}')" ]]; then
    echo "$(date +"%Y-%m-%d %H:%M:%S") zookeeper already running"  >>/data/program/kafka/reboot.log
    exit 0
  fi
}

check_kafka_running(){
   if [[ -n "$(ps aux | grep java | grep server.properties | awk '{print $2}')" ]]; then
    echo "$(date +"%Y-%m-%d %H:%M:%S") kafka already running"  >>/data/program/kafka/reboot.log
    exit 0
  fi
}


wait_for_port(){
  local port=$1
  local max_attempts=20
  local attempt=0
  while [ $attempt -lt $max_attempts ]; do
    if netstat -tuln | grep ":$port " >/dev/null; then
      return 0
    else
      sleep 1
      attempt=$((attempt + 1))
    fi
  done
  return 1
}

start_kafka(){
  check_zk_running
  check_kafka_running

  #启动zk
  cd /data/program/kafka
  echo "$(date +"%Y-%m-%d %H:%M:%S") start zookeeper" >> /data/program/kafka/reboot.log
  sh startZookeeper.sh >> /data/program/kafka/reboot.log
  wait_for_port 2181
  if [ $? -eq 0 ]; then
    echo "$(date +"%Y-%m-%d %H:%M:%S") zk started successfully" >> /data/program/kafka/reboot.log
  else
    echo "$(date +"%Y-%m-%d %H:%M:%S") failed to start zk 2181 port not available" >> /data/program/kafka/reboot.log
  fi

  #等待20秒清理session
  sleep 20

  # 启动kafka
  echo "$(date +"%Y-%m-%d %H:%M:%S") start kafka" >> /data/program/kafka/reboot.log
  sh startKafka.sh  >> /data/program/kafka/reboot.log
  wait_for_port 9092
  if [ $? -eq 0 ]; then
    echo "$(date +"%Y-%m-%d %H:%M:%S") kafka started successfully" >> /data/program/kafka/reboot.log
  else
    echo "$(date +"%Y-%m-%d %H:%M:%S") failed to start kafka 9092 port not available" >> /data/program/kafka/reboot.log
  fi
}

echo "============================================================" >> /data/program/kafka/reboot.log
echo "$(date +"%Y-%m-%d %H:%M:%S") prepare start zookeeper and kafka" >> /data/program/kafka/reboot.log
start_kafka
echo "============================================================" >> /data/program/kafka/reboot.log
echo "exit" >> /data/program/kafka/reboot.log