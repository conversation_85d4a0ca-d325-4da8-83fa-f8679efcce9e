#!/bin/bash
set -e -o pipefail
BASE_DIR=$( cd "$(dirname "${BASH_SOURCE[0]}")" && pwd);
TOPIC_FILE=$1

KAFKA_BASE_DIR=/data/program/kafka
KAFKA_HOME=$KAFKA_BASE_DIR/kafka_2.13-2.8.0
BORKER_LIST="*************:9081,*************:9081,*************:9081"

get_offset(){
  if [ ! "$TOPIC_FILE" ]; then
    echo "Error：Need topic file"
    return
  fi
  while IFS= read -r line; do
    $KAFKA_HOME/bin/kafka-run-class.sh kafka.tools.GetOffsetShell  --broker-list $BORKER_LIST --topic $line
  done < "$TOPIC_FILE"
}

get_offset