#!/bin/bash
set -e -o pipefail
yesterday=$(date -d '-1 day' '+%Y-%m-%d')

daily_report(){
  curl "http://***********:9200/_cat/shards?bytes=mb&v&h=index,state,ip,store" > /tmp/shards.log
  cat /tmp/shards.log | grep $yesterday | awk '{total+=$4} END{print total/1024}' > /tmp/rep.txt
  store_value=`cat /tmp/rep.txt`
  data="{ \"store\":\"$store_value\",\"@timestamp\":\"${yesterday}T00:20:16.000+08:00\"}"
  curl -X POST -H 'Content-Type: application/json' http://***********:9201/es-daily-report/_doc -d "$data"
  echo 'report success'
}

daily_report
