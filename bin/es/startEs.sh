#!/bin/bash
#需要定义java_home，cron中无法获取
JAVA_HOME=/data/program/java8
export JAVA_HOME

check_running(){
   if [[ -n "$(ps aux | grep java | grep elasticsearch-6.8.23 | awk '{print $2}')" ]]; then
    echo "$(date +"%Y-%m-%d %H:%M:%S") elasticsearch already running"  >>/data/program/es/reboot.log
    return 1
  else
    return 0
  fi
}

wait_for_port(){
  local port=$1
  local max_attempts=20
  local attempt=0
  while [ $attempt -lt $max_attempts ]; do
    if netstat -tuln | grep ":$port " >/dev/null; then
      return 0
    else
      sleep 1
      attempt=$((attempt + 1))
    fi
  done
  return 1
}

start_es(){
  check_running
  local result=$?
  if [ $result -eq 0 ]; then
    cd /data/program/es/elasticsearch-6.8.23
    echo "$(date +"%Y-%m-%d %H:%M:%S") start elasticsearch" >> /data/program/es/reboot.log
    sh ./bin/elasticsearch -d >> /data/program/es/reboot.log
    wait_for_port $1
    if [ $? -eq 0 ]; then
      echo "$(date +"%Y-%m-%d %H:%M:%S") elasticsearch $1 started successfully" >> /data/program/es/reboot.log
    else
      echo "$(date +"%Y-%m-%d %H:%M:%S") failed to start elasticsearch $1: port not available" >> /data/program/es/reboot.log
    fi
  fi
}

echo "============================================================" >> /data/program/es/reboot.log
echo "$(date +"%Y-%m-%d %H:%M:%S") prepare start elasticsearch" >> /data/program/es/reboot.log
start_es 9300
echo "============================================================" >> /data/program/es/reboot.log
echo "exit" >> /data/program/es/reboot.log