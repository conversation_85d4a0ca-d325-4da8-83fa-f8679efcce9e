package com.buzz.mybatis.sqlsession;

import javax.sql.DataSource;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;

public class SqlSessionFactoryBeanTest {

    public static void main(String[] args) throws Exception {

        DataSource dataSource = null;
        SqlSessionFactoryBean sessionFactory = new SqlSessionFactoryBean();
        sessionFactory.setDataSource(dataSource);
        SqlSessionFactory sqlSessionFactory = sessionFactory.getObject();
    }
}
