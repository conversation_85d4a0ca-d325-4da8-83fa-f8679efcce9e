package com.buzz.mybatis.mapper;

import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.SqlSessionTemplate;
import org.mybatis.spring.mapper.MapperFactoryBean;
import org.springframework.boot.autoconfigure.security.SecurityProperties.User;
import org.springframework.jdbc.datasource.DriverManagerDataSource;

import javax.sql.DataSource;

public class MapperFactoryBeanTest {

    public static void main(String[] args) throws Exception {

        DataSource dataSource = new DriverManagerDataSource("****************************************","root","1234");
        SqlSessionFactoryBean sqlSessionFactory = new SqlSessionFactoryBean();
        sqlSessionFactory.setDataSource(dataSource);
        sqlSessionFactory.afterPropertiesSet();
        
        MapperFactoryBean<UserDao> mapperFactory = new MapperFactoryBean<UserDao>();
        mapperFactory.setMapperInterface(UserDao.class);
        mapperFactory.setSqlSessionFactory(sqlSessionFactory.getObject());
        
        mapperFactory.afterPropertiesSet();
        UserDao userDAO = mapperFactory.getObject();
        userDAO.get(1001l);
        
        SqlSessionTemplate template = new SqlSessionTemplate(sqlSessionFactory.getObject());
        User user = template.selectOne("test");
        
    }
}
