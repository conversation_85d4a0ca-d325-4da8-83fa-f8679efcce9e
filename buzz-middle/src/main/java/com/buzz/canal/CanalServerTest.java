package com.buzz.canal;

import com.alibaba.otter.canal.protocol.CanalEntry.*;
import com.alibaba.otter.canal.protocol.ClientIdentity;
import com.alibaba.otter.canal.protocol.Message;
import com.alibaba.otter.canal.server.embedded.CanalServerWithEmbedded;
import com.buzz.canal.CanalBuilder.Env;
import com.google.protobuf.ByteString;
import com.google.protobuf.InvalidProtocolBufferException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.locks.LockSupport;

/**
 * 基于 canal-1.0.24
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class CanalServerTest {

    //protected static final String DESTINATION = "drc--test-daily-************:3311-************:3312";
    //这个1002会在 /storage/drc/destinations/DESTINATION/1002 记录点位
    //private ClientIdentity clientIdentity = new ClientIdentity(DESTINATION, (short) 1002);

    @Test
    public void testStandalone_getWithoutAck() {
        Env env = Env.TEST1;
        String destination = env.getDestination();
        ClientIdentity clientIdentity = new ClientIdentity(env.getDestination(), (short) 1002);
        CanalServerWithEmbedded server = CanalBuilder.builder()
                .env(env)
                .filterBlackRegex(env.getBlacklist())
                .destination(destination)
                .buildCanalServer();
        server.start();
        server.start(env.getDestination());
        server.subscribe(clientIdentity);

        int count = 0;
        while (count < 3) {
            Message message = server.getWithoutAck(clientIdentity, 128);
            if (message.getEntries().isEmpty()) {
                log.info("message is empty ! try again");
                LockSupport.parkNanos(2 * 1000L * 1000 * 1000);
                continue;
            }
            log.info("receive data!  id:{}, entry_list_size:{}", message.getId(), message.getEntries().size());
            EntryBag entryBag = splitEntriesBySchema(message.getEntries());
            if(entryBag.entryMap.isEmpty()){
                log.info("data is TRANSACTION  ! try again");
                LockSupport.parkNanos(2 * 1000L * 1000 * 1000);
                continue;
            }
            for (List<Entry> entries : entryBag.entryMap.values()) {
                extractEntries(entries);
            }
            //ack
            server.ack(clientIdentity, message.getId());
            LockSupport.parkNanos(2 * 1000L * 1000 * 1000);
            ++count;
        }

        server.stop();
        server.stop(destination);
    }


    private DRCMessageList extractEntries(List<Entry> entries) {
        String db = null;
        int totalSize = 0;
        DRCMessageList drcMessageDOList = new DRCMessageList();

        for (Entry entry : entries) {
            // 跳过事务的开头和结尾
            if (EntryType.TRANSACTIONBEGIN == entry.getEntryType() || EntryType.TRANSACTIONEND == entry.getEntryType()) {
                continue;
            }
            Header header = entry.getHeader();
            if (null == db) {
                db = entry.getHeader().getSchemaName();
            }
            //storeValue
            ByteString storeValue = entry.getStoreValue();
            RowChange rowChange;
            try {
                rowChange = RowChange.parseFrom(storeValue);
            } catch (InvalidProtocolBufferException e) {
                e.printStackTrace();
                continue;
            }
            //事件类型：INSERT、UPDATE、DELETE、CREATE
            EventType eventType = rowChange.getEventType();
            log.info("eventType:{} , db:{} , table:{}", eventType, db, header.getTableName());
            List<RowData> rowDataList = rowChange.getRowDatasList();
            int storeValueSize = storeValue.size();
            totalSize += storeValueSize;
            drcMessageDOList.addDRCMessage(new DRCMessageDO(header, eventType, rowDataList));
        }

        sendMessage(db, drcMessageDOList);

        return drcMessageDOList;
    }

    private void sendMessage(String db, DRCMessageList drcMessageList) {
        //这里需要对 drcMessageList 序列化
        try {
            byte[] value = drcMessageList.toByteArray();
            //drcProducer.send(AppConfig.topic, db, value);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    //把事务按schema级别进行合并, 并且过滤掉事务的开始和结束
    private EntryBag splitEntriesBySchema(List<Entry> entries) {
        Map<String, List<Entry>> entryMap = new HashMap<>();
        int totalSize = 0;
        for (Entry entry : entries) {
            // 跳过事务的开头和结尾
            if (EntryType.TRANSACTIONBEGIN == entry.getEntryType() || EntryType.TRANSACTIONEND == entry.getEntryType()) {
                continue;
            }
            // 获取当前entry的schem name
            String schemaName = entry.getHeader().getSchemaName();
            List<Entry> transactions = entryMap.get(schemaName);
            if (transactions == null) {
                entryMap.put(schemaName, transactions = new ArrayList<>(1));
            }
            transactions.add(entry);
            totalSize++;
        }

        return new EntryBag(totalSize, entryMap);
    }

    private static class EntryBag {
        private int totalSize;
        private Map<String, List<Entry>> entryMap;

        public EntryBag(int totalSize, Map<String, List<Entry>> entryMap) {
            this.totalSize = totalSize;
            this.entryMap = entryMap;
        }
    }

    @Data
    private static class DRCMessageList {
        public static final int BUFFER_SIZE = 80 * 1024;
        private final List<DRCMessageDO> messageDOs = new ArrayList<>();

        private long drcRecordTimeStamp;

        public byte[] toByteArray() throws IOException {
            //记录drc-producer消息发送的时间, 可用于计算消息到drc-consumer, drc-streams的延迟
            long timestamp = System.currentTimeMillis();
            int size = messageDOs.size();
            ByteArrayOutputStream baos = new ByteArrayOutputStream(BUFFER_SIZE * size);
            DataOutputStream dos = new DataOutputStream(baos);
            for (DRCMessageDO drcMessageDO : messageDOs) {
                byte[] data = drcMessageDO.toByteArray();
                dos.writeInt(data.length);
                dos.write(data);
            }
            return baos.toByteArray();
        }

        public void addDRCMessage(DRCMessageDO messageDO) {
            messageDOs.add(messageDO);
        }
    }

    @Data
    private static class DRCMessageDO {
        public static final int BUFFER_SIZE = 80 * 1024;

        private String db;
        private String table;
        // 即binlog中记录的mysql表中数据变更的时间
        private long executeTime;
        private EventType eventType;
        private List<RowData> rowDataList;

        // don't serialize this filed
        private transient Header header;

        public DRCMessageDO(Header header, EventType eventType, List<RowData> rowDataList) {
            this(header.getSchemaName(), header.getTableName(), header.getExecuteTime(), eventType, rowDataList);
            this.header = header;
        }

        public DRCMessageDO(String db, String table, long executeTime, EventType eventType, List<RowData> rowDataList) {
            this.db = db;
            this.table = table;
            this.executeTime = executeTime;
            this.eventType = eventType;
            this.rowDataList = rowDataList;
        }

        public byte[] toByteArray() throws IOException {
            ByteArrayOutputStream baos = new ByteArrayOutputStream(BUFFER_SIZE);
            DataOutputStream dos = new DataOutputStream(baos);
            dos.writeInt(db.length());
            dos.write(db.getBytes());
            dos.writeInt(table.length());
            dos.write(table.getBytes());
            dos.writeLong(executeTime);
            dos.writeInt(eventType.getNumber());
            dos.writeInt(rowDataList.size());
            for (RowData rowData : rowDataList) {

                byte[] rowDataByte = rowData.toByteArray();
                dos.writeInt(rowDataByte.length);
                dos.write(rowDataByte);
            }
            return baos.toByteArray();
        }
    }


}
