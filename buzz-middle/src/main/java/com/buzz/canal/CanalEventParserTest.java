package com.buzz.canal;

import com.alibaba.otter.canal.common.AbstractCanalLifeCycle;
import com.alibaba.otter.canal.parse.exception.CanalParseException;
import com.alibaba.otter.canal.parse.inbound.mysql.MysqlEventParser;
import com.alibaba.otter.canal.parse.index.CanalLogPositionManager;
import com.alibaba.otter.canal.parse.support.AuthenticationInfo;
import com.alibaba.otter.canal.protocol.CanalEntry;
import com.alibaba.otter.canal.protocol.CanalEntry.*;
import com.alibaba.otter.canal.protocol.position.LogPosition;
import com.alibaba.otter.canal.sink.AbstractCanalEventSink;
import com.alibaba.otter.canal.sink.exception.CanalSinkException;
import com.google.protobuf.InvalidProtocolBufferException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;
import java.net.InetSocketAddress;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;

/**
 * +------------------+     +--------------------------+     +---------------------+     +------------------+
 * | MysqlEventParser | --> | AbstractMysqlEventParser | --> | AbstractEventParser | --> | CanalEventParser |
 * +------------------+     +--------------------------+     +---------------------+     +------------------+
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class CanalEventParserTest {

    private static final String MYSQL_ADDRESS = "************:3342";
    private static final String USERNAME = "drc";
    private static final String PASSWORD = "d77edef36384cf38f8e67ca8c91afffa";

    @Test
    public void test_position() throws IOException {
        final AtomicLong entryCount = new AtomicLong(0);

        MysqlEventParser eventParser = new MysqlEventParser();
        eventParser.setSlaveId(3344l);
        eventParser.setDetectingEnable(false);
        eventParser.setMasterInfo(buildAuthentication());
        eventParser.setEventSink(new AbstractCanalEventSink<List<CanalEntry.Entry>>() {

            @Override
            public boolean sink(List<Entry> entrys, InetSocketAddress remoteAddress, String destination) throws CanalSinkException, InterruptedException {
                for (Entry entry : entrys) {
                    if (entry.getEntryType() != EntryType.HEARTBEAT) {

                        try {
                            String db = entry.getHeader().getSchemaName();
                            String tableName = entry.getHeader().getTableName();
                            String logfile = entry.getHeader().getLogfileName();
                            long offset = entry.getHeader().getLogfileOffset();

                            RowChange rowChange = RowChange.parseFrom(entry.getStoreValue());
                            EventType eventType = rowChange.getEventType();
                            log.info("entryType:{}, logfile:{}, offset:{}, eventType:{} , db:{} , table:{}", entry.getEntryType(), logfile, offset, eventType, db, tableName);
                            List<RowData> rowDataList = rowChange.getRowDatasList();
                            for (RowData rowData : rowDataList) {
                                log.info(rowData.toString());
                            }
                        } catch (InvalidProtocolBufferException e) {
                            e.printStackTrace();
                        }
                        entryCount.incrementAndGet();
                    }
                }
                return true;
            }
        });

        eventParser.setLogPositionManager(new AbstractCanalLogPositionManager() {


            @Override
            public LogPosition getLatestIndexBy(String destination) {
                return null;
            }

            @Override
            public void persistLogPosition(String destination, LogPosition logPosition) throws CanalParseException {
                System.out.println("destination:" + destination + "\t logPosition:" + logPosition);
            }
        });
        eventParser.start();
        startThread(entryCount,eventParser);
        System.in.read();
    }

    private void startThread(AtomicLong entryCount,MysqlEventParser eventParser) {
        new Thread(() -> {
            while (true) {
                if (entryCount.get() > 5) {
                    log.info("stopping");
                    eventParser.stop();
                    System.exit(-1);
                }
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }).start();
    }

    private AuthenticationInfo buildAuthentication() {
        String[] hostAndPort = MYSQL_ADDRESS.split(":");
        return new AuthenticationInfo(new InetSocketAddress(hostAndPort[0], Integer.parseInt(hostAndPort[1])), USERNAME, PASSWORD);
    }

    private static abstract class AbstractCanalLogPositionManager extends AbstractCanalLifeCycle implements CanalLogPositionManager {

    }
}
