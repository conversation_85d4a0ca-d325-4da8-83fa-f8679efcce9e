package com.buzz.canal;

import com.alibaba.otter.canal.instance.core.CanalInstance;
import com.alibaba.otter.canal.instance.core.CanalInstanceGenerator;
import com.alibaba.otter.canal.instance.manager.model.Canal;
import com.alibaba.otter.canal.instance.manager.model.CanalParameter;

import java.util.Arrays;

/**
 *
 * 参考 com.wacai.middleware.producer.service.CanalInstanceService
 * <AUTHOR>
 * @description
 **/
public class DRCCanalInstanceGenerator implements CanalInstanceGenerator {
    @Override
    public CanalInstance generate(String destination) {

        Canal canal = new Canal();
        canal.setId(1L);
        canal.setName(destination);

        CanalParameter parameter = new CanalParameter();
        parameter.setZkClusters(Arrays.asList(AppConfig.zkAddress));
        parameter.setHaMode(CanalParameter.HAMode.HEARTBEAT);
        return null;
    }
}
