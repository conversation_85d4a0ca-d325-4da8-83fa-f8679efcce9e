package com.buzz.canal;

import com.alibaba.otter.canal.instance.core.CanalInstance;
import com.alibaba.otter.canal.instance.core.CanalInstanceGenerator;
import com.alibaba.otter.canal.instance.manager.CanalInstanceWithManager;
import com.alibaba.otter.canal.instance.manager.model.Canal;
import com.alibaba.otter.canal.instance.manager.model.CanalParameter;
import com.alibaba.otter.canal.server.embedded.CanalServerWithEmbedded;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.net.InetSocketAddress;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @description
 **/
public class CanalBuilder {

    public static CanalBuilder builder() {
        return new CanalBuilder();
    }

    private Env env = Env.DEV;
    private String filterRegex = ".*";
    private String filterBlackRegex = "heartbeat\\..*";
    private String destination;
    private String zkHosts = "127.0.0.1:2181";

    public CanalBuilder env(Env env) {
        this.env = env;
        return this;
    }

    public CanalBuilder destination(String destination) {
        this.destination = destination;
        return this;
    }

    public CanalBuilder filterBlackRegex(String filterBlackRegex) {
        this.filterBlackRegex = filterBlackRegex;
        return this;
    }

    public Canal buildCanal(String destination) {
        Canal canal = new Canal();
        canal.setId(2L);
        canal.setName(destination);

        CanalParameter parameter = new CanalParameter();
        parameter.setZkClusters(Arrays.asList(zkHosts));
        parameter.setHaMode(CanalParameter.HAMode.HEARTBEAT);
        parameter.setMetaMode(CanalParameter.MetaMode.MIXED);
        parameter.setIndexMode(CanalParameter.IndexMode.MEMORY_META_FAILBACK);
        parameter.setStorageMode(CanalParameter.StorageMode.MEMORY);
        parameter.setStorageBatchMode(CanalParameter.BatchMode.ITEMSIZE);

        parameter.setMemoryStorageBufferSize(32 * 1024);
        parameter.setSourcingType(CanalParameter.SourcingType.MYSQL);
        parameter.setBlackFilter(filterBlackRegex);

        //mysql信息
        String[] master = env.getMaster().split(":");
        String[] slave = env.getSlave().split(":");

        parameter.setMasterAddress(new InetSocketAddress(master[0], Integer.parseInt(master[1])));
        parameter.setStandbyAddress(new InetSocketAddress(slave[0], Integer.parseInt(slave[1])));
        parameter.setDbUsername(env.getUsername());
        parameter.setDbPassword(env.getPassword());

        parameter.setSlaveId(1L);
        parameter.setDefaultConnectionTimeoutInSeconds(30);
        parameter.setConnectionCharset("utf-8");
        parameter.setConnectionCharsetNumber((byte) 33);
        parameter.setReceiveBufferSize(8 * 1024);
        parameter.setSendBufferSize(8 * 1024);
        parameter.setDetectingEnable(false);
        parameter.setDetectingIntervalInSeconds(10);
        parameter.setDetectingRetryTimes(3);

        canal.setCanalParameter(parameter);

        return canal;
    }

    public CanalInstanceGenerator buildCanalInstanceGenerator() {
        return new CanalInstanceGenerator() {

            public CanalInstance generate(String destination) {
                Canal canal = buildCanal(destination);
                CanalInstanceWithManager canalInstance = new CanalInstanceWithManager(canal, filterRegex);
                canalInstance.setAlarmHandler(new DRCCanalAlarmHandler());
                return canalInstance;
            }
        };
    }

    public CanalServerWithEmbedded buildCanalServer() {
        CanalServerWithEmbedded server = CanalServerWithEmbedded.instance();
        server.setCanalInstanceGenerator(buildCanalInstanceGenerator());
        return server;

    }

    @AllArgsConstructor
    @Getter
    public static enum Env {
        DEV("127.0.0.1:3306", "127.0.0.1:3306", "canal", "canal", "heartbeat\\..*"),
        TEST1("192.168.5.14:3310", "192.168.5.15:3310", "drc", "d77edef36384cf38f8e67ca8c91afffa", "heartbeat\\..*"),
        TEST2("192.168.5.14:3311", "192.168.5.15:3311", "drc", "d77edef36384cf38f8e67ca8c91afffa", "heartbeat\\..*"),
        TEST3("192.168.5.14:3342", "192.168.5.15:3342", "drc", "d77edef36384cf38f8e67ca8c91afffa", "heartbeat\\..*,dbmonitor\\..*,wac_iast_test\\..*,wac_iast\\..*");
        private String master;
        private String slave;
        private String username;
        private String password;
        private String blacklist;

        public String getDestination() {
            return master + "-" + slave;
        }
    }
}
