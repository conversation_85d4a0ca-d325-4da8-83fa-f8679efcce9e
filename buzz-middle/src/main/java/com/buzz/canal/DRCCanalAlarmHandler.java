package com.buzz.canal;

import com.alibaba.otter.canal.common.AbstractCanalLifeCycle;
import com.alibaba.otter.canal.common.alarm.CanalAlarmHandler;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class DRCCanalAlarmHandler extends AbstractCanalLifeCycle implements CanalAlarmHandler {
    @Override
    public void sendAlarm(String destination, String msg) {
     log.error("error {}, {}",destination,msg);
    }
}
