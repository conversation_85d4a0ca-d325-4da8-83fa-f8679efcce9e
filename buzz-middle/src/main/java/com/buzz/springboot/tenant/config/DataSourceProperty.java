package com.buzz.springboot.tenant.config;

import java.util.ArrayList;
import java.util.List;

import lombok.Data;

@Data
public class DataSourceProperty {

	private String dataSourceName;
	private String databaseName;
	private String propsUrl;
	private String decryptKey;
	private String url;
	private String username;
	private String password;
	//可能存在多个，例如：wups-gsy.ucenter.k2.xihuyun.info,gongshu.caimitech.com
	private List<String> tenants = new ArrayList<>();
}
