package com.buzz.springboot.tenant.config;

import java.util.ArrayList;
import java.util.List;

public class DataSourceConfigContext {

	private static List<DataSourceProperty> dataSourceProperties = new ArrayList<>();

	public static List<DataSourceProperty> getDataSourceProperties() {
		return dataSourceProperties;
	}

	public static void setDataSourceProperties(List<DataSourceProperty> dataSourceProperties) {
		DataSourceConfigContext.dataSourceProperties = dataSourceProperties;
	}

}
