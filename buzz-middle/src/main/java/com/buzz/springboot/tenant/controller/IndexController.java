package com.buzz.springboot.tenant.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSON;
import com.buzz.springboot.tenant.config.DataSourceRoutingKeyContext;
import com.buzz.springboot.tenant.dao.AppMapper;

import lombok.extern.slf4j.Slf4j;

@RestController
@Slf4j
public class IndexController {

	private static long count;
	@Autowired
	
	private AppMapper appMapper;

	private String getTenant() {
		++count;
		if (count % 2 == 0) {
			return "jgyDataSource";
		} else {
			return "defaultDataSource";
		}
	}

	@RequestMapping("/")
	public String index() {
		//模拟切换数据源
		String tenant = getTenant();
		log.info(tenant);
		DataSourceRoutingKeyContext.setDataSourceRoutingKey(tenant);
		return JSON.toJSONString(appMapper.queryAll());
	}
}
