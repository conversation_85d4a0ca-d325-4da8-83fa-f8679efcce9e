package com.buzz.springboot.tenant.config;

import java.util.HashMap;
import java.util.Map;

import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.SqlSessionFactoryBean;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Import;

@Configuration
@Import(DataSourceRegistrar.class)
@MapperScan("com.buzz.tenant.dao")
public class AppConfig {

	@Bean
	public DynamicRoutingDataSource dynamicRoutingDataSource(ApplicationContext applicationContext) {
		DynamicRoutingDataSource dynamicRoutingDataSource = new DynamicRoutingDataSource();
		Map<Object, Object> dataSourceMap = new HashMap<>();
		//获取所有动态数据源
		for (DataSourceProperty ds : DataSourceConfigContext.getDataSourceProperties()) {
			Object dataSourceInstance = applicationContext.getBean(ds.getDataSourceName());
			dataSourceMap.put(ds.getDataSourceName(), dataSourceInstance);
		}
		dynamicRoutingDataSource.setTargetDataSources(dataSourceMap);
		return dynamicRoutingDataSource;
	}

	@Bean
	public SqlSessionFactory sqlSessionFactory(@Autowired DynamicRoutingDataSource dynamicRoutingDataSource) {
		SqlSessionFactoryBean sqlSessionFactoryBean = new SqlSessionFactoryBean();

		sqlSessionFactoryBean.setDataSource(dynamicRoutingDataSource);
//		ResourcePatternResolver resolver = new PathMatchingResourcePatternResolver();
//		try {
//			sqlSessionFactoryBean.setMapperLocations(resolver.getResources("classpath:mybatis/mappers/*.xml"));
//			return sqlSessionFactoryBean.getObject();
//		} catch (Exception e) {
//			log.error("MyBatis SqlSessionFactory 初始化发生异常：", e);
//			throw new RuntimeException(e);
//		}
		try {
			return sqlSessionFactoryBean.getObject();
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}
}
