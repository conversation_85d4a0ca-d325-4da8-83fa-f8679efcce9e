package com.buzz.springboot.tenant.config;

public class DataSourceRoutingKeyContext {

	private static final String DEFAULT_DATASOURCE = "defaultDataSource";

	private final static ThreadLocal<String> CTX = ThreadLocal.withInitial(() -> DEFAULT_DATASOURCE);

	private DataSourceRoutingKeyContext() {
	}

	public static void setDataSourceRoutingKey(String dataSourceRoutingKey) {
		CTX.set(dataSourceRoutingKey);
	}

	public static String getDataSourceRoutingKey() {
		return CTX.get();
	}

	public static String removeDataSourceRoutingKey() {
		String routingKey = CTX.get();
		CTX.remove();
		return routingKey;
	}

}
