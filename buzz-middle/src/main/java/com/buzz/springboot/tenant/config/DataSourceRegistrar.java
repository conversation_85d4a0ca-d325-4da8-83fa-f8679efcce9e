package com.buzz.springboot.tenant.config;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.env.Environment;
import org.springframework.core.type.AnnotationMetadata;

import com.alibaba.druid.pool.DruidDataSource;
import com.zaxxer.hikari.HikariDataSource;

public class DataSourceRegistrar implements ImportBeanDefinitionRegistrar, EnvironmentAware {

	private static int max_retry = 10;
	private Environment environment;
	private volatile boolean primaryBeanAlreadyRegister = false;

	@Override
	public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
		//默认从配置文件获取，也可以从数据库获取
		List<DataSourceProperty> dataSourceProperties = getDataSourcesConfig();
		for (DataSourceProperty ds : dataSourceProperties) {
			if (!primaryBeanAlreadyRegister) {
				registerLocalDataSourceBean(true, ds, registry);
				primaryBeanAlreadyRegister = true;
			}else {
				registerLocalDataSourceBean(false, ds, registry);
			}
		}
		DataSourceConfigContext.setDataSourceProperties(dataSourceProperties);
	}

	private List<DataSourceProperty> getDataSourcesConfig() {
		List<DataSourceProperty> dataSourceProperties = new ArrayList<>();
		for (int i = 0; i < max_retry; i++) {
			String name = "ds" + i + ".name";
			// 数据源名称name必须指定，否则该项配置不会处理
			if (StringUtils.isBlank(environment.getProperty(name))) {
				continue;
			}
			String jdbcUrl = "ds" + i + ".jdbcUrl";
			if (StringUtils.isBlank(environment.getProperty(jdbcUrl))) {
				continue;
			}
			String username = "ds" + i + ".username";
			String password = "ds" + i + ".password";
			String tenants = "ds" + i + ".tenants";
			if (StringUtils.isAnyBlank(environment.getProperty(username), environment.getProperty(password))) {
				continue;
			}
			DataSourceProperty ds = new DataSourceProperty();
			ds.setDataSourceName(environment.getProperty(name));
			ds.setUrl(environment.getProperty(jdbcUrl));
			ds.setUsername(environment.getProperty(username));
			ds.setPassword(environment.getProperty(password));
			if (StringUtils.isNotBlank(environment.getProperty(tenants))) {
				ds.setTenants(Arrays.asList(environment.getProperty(tenants).split(",")));
			}
			dataSourceProperties.add(ds);
		}
		return dataSourceProperties;
	}

	private void registerLocalDataSourceBean(boolean primary, DataSourceProperty ds, BeanDefinitionRegistry registry) {
		BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(HikariDataSource.class);
		builder.addPropertyValue("jdbcUrl", ds.getUrl());
		builder.addPropertyValue("username", ds.getUsername());
		builder.addPropertyValue("password", ds.getPassword());
		// 选择配置文件中第一个数据源作为Primary Bean，MyBatis需要指定一个Primary Bean，否则无法正常初始化MyBatis
		builder.getBeanDefinition().setPrimary(primary);
		registry.registerBeanDefinition(ds.getDataSourceName(), builder.getBeanDefinition());
	}

	@Override
	public void setEnvironment(Environment environment) {
		this.environment = environment;
	}

}
