package com.buzz.springboot.autowire.config;

import java.beans.PropertyDescriptor;
import java.lang.annotation.Annotation;
import java.lang.reflect.AnnotatedElement;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

import org.springframework.beans.BeansException;
import org.springframework.beans.PropertyValues;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.annotation.AutowiredAnnotationBeanPostProcessor;
import org.springframework.beans.factory.annotation.InjectionMetadata;
import org.springframework.beans.factory.annotation.InjectionMetadata.InjectedElement;
import org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessorAdapter;
import org.springframework.beans.factory.support.MergedBeanDefinitionPostProcessor;
import org.springframework.beans.factory.support.RootBeanDefinition;
import org.springframework.core.PriorityOrdered;
import org.springframework.core.annotation.AnnotatedElementUtils;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import com.buzz.springboot.autowire.annotation.JasmineValue;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class JasmineValueAnnotationBeanPostProcessor extends InstantiationAwareBeanPostProcessorAdapter
		implements MergedBeanDefinitionPostProcessor, PriorityOrdered, BeanFactoryAware {

	private static int order = AutowiredAnnotationBeanPostProcessor.LOWEST_PRECEDENCE - 3;

	private final List<Class<? extends Annotation>> annotationTypes = new ArrayList<>();

	private final Map<String, InjectionMetadata> injectionMetadataCache = new ConcurrentHashMap<>(256);

	public JasmineValueAnnotationBeanPostProcessor() {
		super();
		annotationTypes.add(JasmineValue.class);
	}

	@Override
	public int getOrder() {
		return order;
	}

	@Override
	public void setBeanFactory(BeanFactory beanFactory) throws BeansException {

	}
	@Override
	public Object postProcessBeforeInitialization(Object bean, final String beanName)
			throws BeansException {

		//doWithFields(bean, beanName);

		//doWithMethods(bean, beanName);

		return super.postProcessBeforeInitialization(bean, beanName);
	}
	
	


	@Override
	public void postProcessMergedBeanDefinition(RootBeanDefinition beanDefinition, Class<?> beanType, String beanName) {

		InjectionMetadata metadata = findAutowiringMetadata(beanName, beanType, null);
		metadata.checkConfigMembers(beanDefinition);
	}

	@Override
	public PropertyValues postProcessProperties(PropertyValues pvs, Object bean, String beanName) {
		InjectionMetadata metadata = findAutowiringMetadata(beanName, bean.getClass(), pvs);
		try {
			metadata.inject(bean, beanName, pvs);
		} catch (BeanCreationException ex) {
			throw ex;
		} catch (Throwable ex) {
			throw new BeanCreationException(beanName, "Injection of autowired dependencies failed", ex);
		}
		return pvs;

	}

	private InjectionMetadata findAutowiringMetadata(String beanName, Class<?> clazz, PropertyValues pvs) {
		// Fall back to class name as cache key, for backwards compatibility with custom callers.
		String cacheKey = (StringUtils.hasLength(beanName) ? beanName : clazz.getName());

		InjectionMetadata metadata = this.injectionMetadataCache.get(cacheKey);
		if (InjectionMetadata.needsRefresh(metadata, clazz)) {
			synchronized (this.injectionMetadataCache) {
				metadata = this.injectionMetadataCache.get(cacheKey);
				if (InjectionMetadata.needsRefresh(metadata, clazz)) {
					if (metadata != null) {
						metadata.clear(pvs);
					}
					metadata = buildAutowiringMetadata(clazz);
					this.injectionMetadataCache.put(cacheKey, metadata);
				}
			}
		}
		return metadata;
	}

	protected AnnotationAttributes doGetAnnotationAttributes(AnnotatedElement element,
			Class<? extends Annotation> annotationType) {
		AnnotationAttributes attributes = AnnotatedElementUtils.getMergedAnnotationAttributes(element, annotationType);
		if (attributes != null) {
			return attributes;
		}
		return null;
	}

	protected final List<Class<? extends Annotation>> getAnnotationTypes() {
		return annotationTypes;
	}

	private InjectionMetadata buildAutowiringMetadata(final Class<?> beanClass) {
		List<InjectionMetadata.InjectedElement> elements = new ArrayList<>();

		ReflectionUtils.doWithFields(beanClass, new ReflectionUtils.FieldCallback() {
			@Override
			public void doWith(Field field) throws IllegalArgumentException, IllegalAccessException {

				for (Class<? extends Annotation> annotationType : getAnnotationTypes()) {

					AnnotationAttributes attributes = doGetAnnotationAttributes(field, annotationType);

					if (attributes != null) {

						if (Modifier.isStatic(field.getModifiers())) {
							if (log.isWarnEnabled()) {
								log.warn("@" + annotationType.getName() + " is not supported on static fields: "
										+ field);
							}
							return;
						}

						elements.add(new AnnotatedFieldElement(field, attributes));
					}
				}
			}
		});

		return new InjectionMetadata(beanClass, elements);

	}

	public class AnnotatedFieldElement extends InjectedElement {

		private AnnotationAttributes attributes;

		protected AnnotatedFieldElement(Field field, AnnotationAttributes attributes) {
			super(field, null);
			this.attributes = attributes;
		}

		@Override
		protected void inject(Object target, String requestingBeanName, PropertyValues pvs) throws Throwable {

			Field field = (Field) this.member;
			ReflectionUtils.makeAccessible(field);
			Object value = getValue(target, requestingBeanName);
			field.set(target, value);

		}

		private Object getValue(Object target, String requestingBeanName) {
			//get value from jasmine
			String value = attributes.getString("value");
			return "hello " + value;
		}
	}

}
