package com.buzz.springboot.autowire;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;

import com.buzz.springboot.autowire.annotation.EnableJasmine;
import com.buzz.springboot.autowire.annotation.JasmineValue;

@EnableJasmine
@Configuration
public class AutowireTest {

	@JasmineValue("jackma")
	private String usrname;

	@Autowired
	private Environment environment;

	@Value("${age:18}")
	private Long age;

	public static void main(String[] args) {
		AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext(
				AutowireTest.class);

		AutowireTest test = applicationContext.getBean(AutowireTest.class);
		System.out.println(test.usrname);
		System.out.println(test.environment);
		System.out.println(test.age);
	}
}
