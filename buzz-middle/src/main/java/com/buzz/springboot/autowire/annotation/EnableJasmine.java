package com.buzz.springboot.autowire.annotation;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

import com.buzz.springboot.autowire.config.JasmineValueAnnotationBeanPostProcessor;

@Target(ElementType.TYPE_USE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Import(JasmineValueAnnotationBeanPostProcessor.class)
public @interface EnableJasmine {

	
}
