package com.buzz.springboot.mock;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.apache.coyote.http2.Http2Protocol;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.web.embedded.tomcat.TomcatServletWebServerFactory;
import org.springframework.boot.web.servlet.server.ConfigurableServletWebServerFactory;
import org.springframework.context.annotation.Bean;
import org.springframework.web.HttpRequestHandler;

import com.alibaba.fastjson.JSON;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@SpringBootApplication()
public class MockServer {

	public static void main(String[] args) {
		SpringApplication.run(MockServer.class, args);
	}

	@Bean(name = "/*")
	public HttpRequestHandler mock() {
		return new MockHandler();
	}
	
	@Bean
	public ConfigurableServletWebServerFactory tomcatCustomizer() {
	    TomcatServletWebServerFactory factory = new TomcatServletWebServerFactory();
	    factory.addConnectorCustomizers(connector -> connector.addUpgradeProtocol(new Http2Protocol()));
	    return factory;
	}

	private static class MockHandler implements HttpRequestHandler {

		@Override
		public void handleRequest(HttpServletRequest request, HttpServletResponse response)
				throws ServletException, IOException {

			String path = request.getRequestURI();
			BufferedReader reader = new BufferedReader(new InputStreamReader(request.getInputStream()));
			String body = IOUtils.read(reader);
			String method = request.getMethod();
			
			log.info("receive Transfer-Encoding:{}",request.getHeader("Transfer-Encoding"));
			log.info("receive method:{}, path:{}", method, path);
			log.info("receive x-login-user:{}", request.getHeader("x-login-user"));
			log.info("receive body:{}", body);
			
			try {
				Thread.sleep(2);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			Map<String, Object> result = new HashMap<String, Object>();
			result.put("code", 0);
			result.put("data", String.format("%s success", method));

			if (method.equals("GET")) {
				result.put("data", buildResult(path));
			}
			
			if(request.getRequestURI().contains("ssh")) {
				log.info("301");
				response.setStatus(301);
				response.setHeader("Location","http://open.wacai.com");
				//response.sendRedirect("/");
			}else {
				response.setContentType("application/json");
				response.getWriter().write(JSON.toJSONString(result));
				response.getWriter().flush();
			}
			
		}
		
		public Map<String,Object> buildResult(String path) {
			Map<String, Object> data = new HashMap<String, Object>();
			if(path.endsWith("pods")) {
				data.put("host_ip", "***********");
				data.put("pod_ip", "***********");
			}else {
				data.put("name", "mock");
			}
			
			return data;
		}

	}
}
