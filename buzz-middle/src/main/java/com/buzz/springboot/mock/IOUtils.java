package com.buzz.springboot.mock;

import java.io.IOException;
import java.io.Reader;
import java.io.StringWriter;
import java.io.Writer;

public class IOUtils {

	private static final int BUFFER_SIZE = 1024 * 8;
	
	public static String read(Reader reader) throws IOException {
		StringWriter writer = new StringWriter();
		try {
			write(reader, writer);
			return writer.getBuffer().toString();
		} finally {
			writer.close();
		}
	}

	public static long write(Reader reader, Writer writer) throws IOException
	{
		int read;
		long total = 0;
		char[] buf = new char[BUFFER_SIZE];
		while( ( read = reader.read(buf) ) != -1 )
		{
			writer.write(buf, 0, read);
			total += read;
		}
		return total;
	}
}
