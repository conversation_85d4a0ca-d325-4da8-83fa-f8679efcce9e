package com.buzz.springboot.boot2.controller;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.buzz.springboot.boot2.config.properties.AppGlobalProperties;

@RestController
public class SimpleController {

	@Autowired
	private AppGlobalProperties appGlobalProperties;

	@RequestMapping("/")
	public User view() {
		User user = new User();
		user.setId(1001l);
		user.setName("jackma");
		
		throw new RuntimeException("error,code=1");

	}

	@RequestMapping("/test")
	public AppGlobalProperties test() {
		return appGlobalProperties;
	}
}
