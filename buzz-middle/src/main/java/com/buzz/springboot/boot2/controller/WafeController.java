package com.buzz.springboot.boot2.controller;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

//import com.wacai.jasmine.autowire.annotation.JasmineValue;

@RestController
public class WafeController {

	//@JasmineValue(module = "JASMINE_DEMO", subModule = "quick_start", key = "test")
	private String key;

	@Value("${middleware.dslock.zkconnectstring:}")
	private String zkconnectstring;

	@RequestMapping("/wafe")
	public String wafe() {

		return key;
	}

	@RequestMapping("/wafe/zk")
	public String zk() {

		return zkconnectstring;
	}
}
