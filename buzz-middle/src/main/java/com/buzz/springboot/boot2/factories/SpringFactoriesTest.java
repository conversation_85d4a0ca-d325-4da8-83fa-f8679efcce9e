package com.buzz.springboot.boot2.factories;

import java.util.List;

import org.springframework.beans.factory.config.BeanFactoryPostProcessor;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.core.io.support.SpringFactoriesLoader;

public class SpringFactoriesTest {

	public static void main(String[] args) {

		List<SpringPrintProcessor> springPrintProcessors = SpringFactoriesLoader
				.loadFactories(SpringPrintProcessor.class, Thread.currentThread().getContextClassLoader());

		springPrintProcessors.stream().forEach(i -> i.print());

		List<String> list = SpringFactoriesLoader.loadFactoryNames(ApplicationContextInitializer.class,
				Thread.currentThread().getContextClassLoader());
		System.out.println(list.size());
		list.stream().forEach(System.out::println);
	}
}
