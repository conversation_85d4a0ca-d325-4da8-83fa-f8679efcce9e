package com.buzz.springboot.boot2.container;

import java.util.Map;

import javax.annotation.PostConstruct;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;

public class AppFilter {

	@Autowired
	private AppResolver appResolver;

	@Value("#{{'Thriller': 100, 'Comedy': 300}}")
	private Map<String, Integer> countOfMoviesPerCatalog;

	@Value("${jdbc.url:}")
	private String url;

	@PostConstruct
	public void init() {
		System.out.println("init!");
		System.out.println("appResolver=" + appResolver + ",url=" + url);
		System.out.println("countOfMoviesPerCatalog=" + countOfMoviesPerCatalog);
	}

	public void execute() {
		appResolver.resolve();
	}

}
