package com.buzz.springboot.boot2.config.properties;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.context.config.ConfigFileApplicationListener;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.boot.env.EnvironmentPostProcessor;
import org.springframework.context.ApplicationContextInitializer;
import org.springframework.context.ApplicationListener;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.Ordered;
import org.springframework.core.env.ConfigurableEnvironment;

public class JasmineConfiguration {

	//执行顺序: 2
	public static class JasmineInitializer implements ApplicationContextInitializer<ConfigurableApplicationContext> {

		@Override
		public void initialize(ConfigurableApplicationContext applicationContext) {
			System.out.println("JasmineInitializer process ");
			System.out.println(applicationContext.getEnvironment().getClass());
			System.out.println(applicationContext.getEnvironment().getProperty("jasmine.endpoint"));
		}

	}

	//执行顺序: 3
	public static class JasmineApplicaitonListener implements ApplicationListener<ContextRefreshedEvent> {

		@Override
		public void onApplicationEvent(ContextRefreshedEvent event) {

			System.out.println("JasmineApplicaitonListener process ");
			System.out.println(event.getApplicationContext().getEnvironment().getProperty("jasmine.endpoint"));

		}

	}

	//执行顺序: 1
	public static class JasmineEnvironmentPostProcessor implements EnvironmentPostProcessor, Ordered {

		@Override
		public void postProcessEnvironment(ConfigurableEnvironment environment, SpringApplication application) {
			System.out.println("JasmineEnvironmentPostProcessor process ");
			System.out.println(environment.getProperty("jasmine.endpoint"));

		}

		@Override
		public int getOrder() {
			return Ordered.HIGHEST_PRECEDENCE;
		}

	}

	//执行顺序: 4
	@Configuration
	@EnableConfigurationProperties({ AppGlobalProperties.class })
	public static class JasmineAutoConfiguration implements ApplicationListener<ApplicationReadyEvent> {

		@Override
		public void onApplicationEvent(ApplicationReadyEvent event) {
			String n = PropertySourcesPlaceholderConfigurer.ENVIRONMENT_PROPERTIES_PROPERTY_SOURCE_NAME;
			int o = ConfigFileApplicationListener.DEFAULT_ORDER;

			System.out.println("JasmineAutoConfiguration load ");
			System.out.println(event.getApplicationContext().getEnvironment().getProperty("jasmine.endpoint"));
		}

	}
}