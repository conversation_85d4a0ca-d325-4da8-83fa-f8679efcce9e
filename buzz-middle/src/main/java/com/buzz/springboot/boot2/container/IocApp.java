package com.buzz.springboot.boot2.container;

import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

public class IocApp {

	public static void main(String[] args) {
		ApplicationContext ctx = new AnnotationConfigApplicationContext(AppConfig.class);
		AppFilter myService = ctx.getBean(AppFilter.class);
		myService.execute();
		
		
	}	
}
