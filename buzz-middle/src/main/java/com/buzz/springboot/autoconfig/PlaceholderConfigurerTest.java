package com.buzz.springboot.autoconfig;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;

import com.buzz.springboot.autoconfig.config.AppProperties2;

import lombok.Data;

@Configuration
@PropertySource(value = "/application.properties")
@EnableConfigurationProperties({ AppProperties2.class })
public class PlaceholderConfigurerTest {

	@Value("${ds0.name}")
	private String name;
	
	@Bean
	public AppProperties appProperties() {
		return new AppProperties();
	}

	public static void main(String[] args) {

		AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext(
				PlaceholderConfigurerTest.class);

//		applicationContext.getBean(PropertySourcesPlaceholderConfigurer.class);
		
		AppProperties properties = applicationContext
				.getBean(AppProperties.class);

		System.out.println(properties.getName());

		AppProperties2 properties2 = applicationContext
				.getBean(AppProperties2.class);

		System.out.println(properties2.getName());
	}

	@Data
	public static class AppProperties {

		@Value("${ds0.name}")
		private String name;
	}

}
