package com.buzz.springboot.autoconfig.config;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.context.annotation.Import;

@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.TYPE)
@Documented
@Import(HttpProxyRegistor.class)
public @interface EnableHttpProxy {

	public Class<?>[] value() default {};

	String[] basePackages() default {};
}
