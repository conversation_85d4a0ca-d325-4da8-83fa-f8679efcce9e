package com.buzz.springboot.autoconfig;

import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Configuration;

import com.buzz.springboot.autoconfig.config.EnableHttpProxy;
import com.buzz.springboot.autoconfig.service.RequestDemo;

@EnableHttpProxy()
@Configuration
public class HttpProxyTest {

	@SuppressWarnings("resource")
	public static void main(String[] args) {

		AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext(
				HttpProxyTest.class);
		RequestDemo demo = applicationContext.getBean(RequestDemo.class);
		System.out.println(demo.test1());
	}

}
