package com.buzz.springboot.autoconfig;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;

import com.buzz.springboot.sdk.Mesh;
import com.buzz.springboot.sdk.MeshConfiguration;

@Import(MeshConfiguration.class)
@SpringBootApplication
public class CommandLineRunnerTest implements CommandLineRunner {

	@Autowired
	private Mesh mesh;

	@Override
	public void run(String... args) throws Exception {
		mesh.start();
	}

	public static void main(String[] args) {

		SpringApplication boot = new SpringApplication(CommandLineRunnerTest.class);
		boot.run(args);
	}

}
