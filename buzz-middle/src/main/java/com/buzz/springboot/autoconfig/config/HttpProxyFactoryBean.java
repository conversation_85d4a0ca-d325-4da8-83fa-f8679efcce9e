package com.buzz.springboot.autoconfig.config;

import java.lang.reflect.Method;

import org.springframework.beans.factory.FactoryBean;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.cglib.proxy.InvocationHandler;
import org.springframework.cglib.proxy.Proxy;

import lombok.Data;

/**
 * HTTP代理
 * <AUTHOR>
 *
 */
@Data
public class HttpProxyFactoryBean implements FactoryBean<Object>, InitializingBean {

	private Class<?> type;

	private String url;

	@Override
	public void afterPropertiesSet() throws Exception {

	}

	@Override
	public Object getObject() throws Exception {
		if (!this.url.startsWith("http")) {
			this.url = "http://" + this.url;
		}
		return createProxy(this.url);
	}

	/**
	* 创建代理类，在代理类中执行真正的操作
	*/
	private Object createProxy(String url) {
		InvocationHandler invocationHandler = createInvocationHandler(url);
		Object proxy = Proxy.newProxyInstance(Thread.currentThread().getContextClassLoader(), new Class[] { type },
				invocationHandler);
		return proxy;
	}

	private InvocationHandler createInvocationHandler(String url) {
		return new InvocationHandler() {

			private HttpHandler httpHandler = new HttpHandler(url);

			@Override
			public Object invoke(Object proxy, Method method, Object[] args) throws Throwable {

				return httpHandler.handle(method, args);
			}
		};
	}

	@Override
	public Class<?> getObjectType() {
		return type;
	}

	private static class HttpHandler {

		String url;

		public HttpHandler(String url) {
			super();
			this.url = url;
		}

		public String handle(Method method, Object[] args) {
			HttpRequest httpRequest = method.getAnnotation(HttpRequest.class);
			String path = httpRequest.path();
			String httpMethod = httpRequest.method();
			return httpMethod + " " + url + path;
		}
	}
}
