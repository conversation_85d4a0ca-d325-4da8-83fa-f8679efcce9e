package com.buzz.springboot.autoconfig;

import java.lang.reflect.Field;
import java.util.LinkedHashSet;
import java.util.Set;

import org.springframework.beans.TypeConverter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.beans.factory.config.DependencyDescriptor;
import org.springframework.beans.factory.config.PropertyResourceConfigurer;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.PropertySource;
import org.springframework.context.support.PropertySourcesPlaceholderConfigurer;
import org.springframework.core.env.Environment;

@Configuration
@PropertySource(value = "/application.properties")
public class ResolveDependencyTest {

	@Value("${ARTIFACT}")
	private String name;
	
	private Environment environment;

	@Autowired
	private DefaultListableBeanFactory beanFactory;

	private static Object doGetDependency(ResolveDependencyTest bean, DefaultListableBeanFactory beanFactory,
			Field field) {

		DependencyDescriptor desc = new DependencyDescriptor(field, false);
		desc.setContainingClass(bean.getClass());
		Set<String> autowiredBeanNames = new LinkedHashSet<>(1);
		TypeConverter typeConverter = beanFactory.getTypeConverter();

		Object value = beanFactory.resolveDependency(desc, "propertiesSourceTest", autowiredBeanNames, typeConverter);
		return value;
	}

	public static void testResolveDependency(ResolveDependencyTest bean, DefaultListableBeanFactory beanFactory)
			throws Exception {

		Field field = ResolveDependencyTest.class.getDeclaredField("name");
		Object value = doGetDependency(bean, beanFactory, field);
		System.out.println(value);

		field = ResolveDependencyTest.class.getDeclaredField("environment");
		value = doGetDependency(bean, beanFactory, field);
		System.out.println(value.getClass());
	}

	public static void testResolveEmbeddedValue(ResolveDependencyTest bean, DefaultListableBeanFactory beanFactory) {

		String value = beanFactory.resolveEmbeddedValue("${ARTIFACT}");
		System.out.println(value);
	}

	public static void main(String[] args) throws Exception {

		AnnotationConfigApplicationContext applicationContext = new AnnotationConfigApplicationContext(
				ResolveDependencyTest.class);

		ResolveDependencyTest bean = applicationContext.getBean(ResolveDependencyTest.class);

		testResolveDependency(bean, bean.beanFactory);
		testResolveEmbeddedValue(bean, bean.beanFactory);
		//EnableAutoConfigurationImportSelector.class
		//AutoConfigurationImportSelector.class
		//PropertySourcesPlaceholderConfigurer.class
	}
	

}
