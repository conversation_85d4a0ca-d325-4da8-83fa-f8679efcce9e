package com.buzz.springboot.basic;

import javax.servlet.http.HttpServletRequest;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.web.servlet.error.ErrorAttributes;
import org.springframework.boot.web.servlet.error.ErrorController;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@RestController
public class WebApiErrorController implements ErrorController {

	protected Logger logger = LoggerFactory.getLogger(WebApiErrorController.class);

	@Autowired
	public ErrorAttributes errorAttributes;

	@RequestMapping(value = "/error")
	public ResponseEntity<WebApiResponse<String>> error(HttpServletRequest request) {
//		RequestAttributes requestAttributes = new ServletRequestAttributes(request);
//
//		// 1. log the detailed error message
//		logger.warn("error in processing web request:", errorAttributes.getErrorAttributes(requestAttributes, true));
//
//		// 2. provide minimal error information to api consumer
//		Map<String, Object> errorInfo = errorAttributes.getErrorAttributes(requestAttributes, false);
//		String error = (String) errorInfo.get("error");

		return new ResponseEntity<>(WebApiResponse.error("error",1), getStatus(request));
	}

	@Override
	public String getErrorPath() {
		return "/error";
	}

	/**
	 * copied from BasicErrorController
	 */
	protected HttpStatus getStatus(HttpServletRequest request) {
		Integer statusCode = (Integer) request.getAttribute("javax.servlet.error.status_code");
		if (statusCode != null) {
			try {
				return HttpStatus.valueOf(statusCode);
			} catch (Exception ex) {
			}
		}
		return HttpStatus.INTERNAL_SERVER_ERROR;
	}

	public static class WebApiResponse<T> {
		private int code;
		private String error;
		T data;

		public int getCode() {
			return code;
		}

		public void setCode(int code) {
			this.code = code;
		}

		public String getError() {
			return error;
		}

		public void setError(String error) {
			this.error = error;
		}

		public T getData() {
			return data;
		}

		public void setData(T data) {
			this.data = data;
		}

		public static <T> WebApiResponse<T> error(String errorMessage, int errorCode) {
			WebApiResponse<T> response = new WebApiResponse<>();
			response.setCode(errorCode);
			response.setError(errorMessage);
			return response;
		}
	}
}