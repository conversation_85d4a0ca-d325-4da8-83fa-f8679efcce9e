package com.buzz.race.openmessaging.model;

import java.nio.ByteBuffer;
import java.util.List;

import com.buzz.race.openmessaging.Const;
import com.buzz.race.openmessaging.Message;
import com.buzz.race.openmessaging.codec.TDecoder;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2019-07-28
 */
public class GetMsgItem {
    public ByteBuffer readBuf = ByteBuffer.allocateDirect(Const.MAX_GET_AT_SIZE * Const.MSG_BYTES);
    public final long[] as = new long[Const.MAX_GET_AT_SIZE];
    public final long[] ts = new long[Const.MAX_GET_AT_SIZE];
    public List<Message> messages;
    public TDecoder tDecoder = new TDecoder();
}
