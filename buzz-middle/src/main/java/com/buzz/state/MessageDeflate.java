package com.buzz.state;

public class MessageDeflate implements Transformation {

    private Transformation next;

    private static int id = 0;

    @Override
    public void setNext(Transformation t) {

        if (next == null) {
            next = t;
        } else {
            next.setNext(t);
        }
    }

    @Override
    public String sendMessagePart(String data) {
        ++id;
        if (next == null) {
            return id + "|" + data;
        } else {
            return next.sendMessagePart(id + "|" + data);
        }
    }

    public static void main(String[] args) {
        MessageDeflate base = new MessageDeflate();
        for (int i = 0; i < 3; ++i) {
            base.setNext(new MessageDeflate());
        }

        System.out.println(base.sendMessagePart("ok"));
    }

}
