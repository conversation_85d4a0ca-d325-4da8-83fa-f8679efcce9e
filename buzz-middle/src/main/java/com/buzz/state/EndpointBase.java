package com.buzz.state;

public class EndpointBase {

    public void send(String data, SendHandler handler) {

        PrepareSendHandler psh = new PrepareSendHandler(handler, this, new <PERSON>Buffer(data, handler));
        psh.write();
    }

    public void startSend(DataBuffer buffer, SendHandler sendHandler) {
        System.out.println("start Send");
        buffer.setData(decode(buffer, sendHandler));

        System.out.println("send ");
        finishSend(buffer, new EndSendHandler(sendHandler));
    }

    public String decode(DataBuffer buffer, SendHandler sendHandler) {
        return buffer.getData() + "|version:10001";
    }

    public void finishSend(DataBuffer buffer, SendHandler sendHandler) {

      System.out.println("finishSend-->"+buffer.getData());
      
      sendHandler.onResult(new SendResult("ok"));
    }
    
    public static void main(String[] args) {
        
        EndpointBase test = new EndpointBase();
        test.send("hello",  (i)->{
            System.out.println(i.getData());
            
        });
    }

    static class Prepare<PERSON><PERSON><PERSON>and<PERSON> implements SendHandler {

        private SendHandler sendHandler;

        private EndpointBase endpointBase;

        private boolean isDone;

        private int count;

        private DataBuffer buffer;

        public PrepareSendHandler(SendHandler sendHandler, EndpointBase endpointBase, DataBuffer buffer) {
            super();
            this.sendHandler = sendHandler;
            this.endpointBase = endpointBase;
            this.buffer = buffer;
        }

        @Override
        public void onResult(SendResult result) {
            if (isDone) {
                result.setData(result.getData()+"#PrepareSendHandler");
                sendHandler.onResult(result);
            } else {
                write();
            }
        }

        public void write() {
            System.out.println("write "+count);
            ++count;
            if (count > 5) {
                isDone = true;
            }
            endpointBase.startSend(buffer, this);
        }

    }

    static class EndSendHandler implements SendHandler {

        private SendHandler sendHandler;

        public EndSendHandler(SendHandler sendHandler) {
            super();
            this.sendHandler = sendHandler;
        }

        @Override
        public void onResult(SendResult result) {
            result.setData(result.getData()+"#EndSendHandler");
            sendHandler.onResult(result);
        }

    }

    public static class DataBuffer {
        private String data;
        private SendHandler sendHandler;

        public DataBuffer(String data, SendHandler sendHandler) {
            super();
            this.data = data;
            this.sendHandler = sendHandler;
        }

        public String getData() {
            return data;
        }

        public void setData(String data) {
            this.data = data;
        }

        public SendHandler getSendHandler() {
            return sendHandler;
        }

        public void setSendHandler(SendHandler sendHandler) {
            this.sendHandler = sendHandler;
        }

    }
}
