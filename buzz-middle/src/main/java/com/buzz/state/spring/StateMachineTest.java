package com.buzz.state.spring;

import java.util.EnumSet;

import org.springframework.statemachine.StateMachine;
import org.springframework.statemachine.action.Action;
import org.springframework.statemachine.config.StateMachineBuilder;
import org.springframework.statemachine.config.StateMachineBuilder.Builder;

public class StateMachineTest {

    public static enum States {
        STATE1, STATE2
    }

    public static enum Events {
        EVENT1, EVENT2
    }

    public StateMachine<States, Events> buildMachine() throws Exception {

        Builder<States, Events> builder = StateMachineBuilder.builder();

        builder.configureStates().withStates().initial(States.STATE1).states(EnumSet.allOf(States.class));

        builder.configureTransitions()
                .withExternal()
                .source(States.STATE1).target(States.STATE2)
                .event(Events.EVENT1).action(printEvent1())
                .and()
                .withExternal()
                .source(States.STATE2).target(States.STATE1)
                .event(Events.EVENT2);

        return builder.build();
    }

    
    public Action<States,Events> printEvent1(){
        return ctx->System.out.println("解锁旋转门，以便游客能够通过");
    }
    
    public static void main(String[] args) throws Exception {
        StateMachine<States, Events> stateMachine = new StateMachineTest().buildMachine();
        System.out.println(stateMachine.getClass());
        stateMachine.start();
        
        System.out.println(stateMachine.getState());
        stateMachine.sendEvent(Events.EVENT1);
        System.out.println(stateMachine.getState());
    }

}
