package com.buzz.flow.hystrix.circuitbreaker;

import com.netflix.hystrix.HystrixCommand;
import com.netflix.hystrix.HystrixCommand.Setter;
import com.netflix.hystrix.HystrixCommandGroupKey;
import com.netflix.hystrix.HystrixCommandProperties;

/**
 * 断路器样例代码
 * 
 * <AUTHOR>
 *
 */
public class CircuitbreakerTest {

	public static void main(String[] args) throws InterruptedException {
		Setter config = HystrixCommand.Setter
				.withGroupKey(HystrixCommandGroupKey.Factory.asKey("HelloWorld"));

		//设置 command 配置
		HystrixCommandProperties.Setter properties = HystrixCommandProperties.Setter();
		properties.withCircuitBreakerEnabled(true);

		//熔断器中断工作的时间；
		//熔断器中断请求N秒后会进入半打开状态，放部分流量过去重试，如果发现请求通过，那么再尝试放更多流量进入
		properties.withCircuitBreakerSleepWindowInMilliseconds(1000);

		//当错误率超过该值后，熔断自动启动
		properties.withCircuitBreakerErrorThresholdPercentage(10);

		//熔断器在整个统计时间内是否开启的阀值
		//默认20，也就是在metricsRollingStatisticalWindow（默认10s）内至少请求20次，熔断器才发挥起作用
		properties.withCircuitBreakerRequestVolumeThreshold(1);

		// 可简单看做hystrix的系统采样时间
		// 与circuitBreakerRequestVolumeThreshold配合使用，具体使用方法见circuitBreakerRequestVolumeThreshold的注解
		properties.withMetricsRollingStatisticalWindowInMilliseconds(1000);

		config.andCommandPropertiesDefaults(properties);

		for (int i = 0; i < 10; ++i) {
			System.out.println(new CommandHelloWorld(config, "World+").execute());
			Thread.sleep(100);
		}
		System.out.println(CommandHelloWorld.count);
		//睡眠1秒钟等待 CircuitBreakerSleepWindowInMilliseconds
		Thread.sleep(1000);
		CommandHelloWorld.count = 0;
		System.out.println("======================");
		for (int i = 0; i < 10; ++i) {
			System.out.println(new CommandHelloWorld(config, "World+").execute());
			Thread.sleep(100);
		}
		System.out.println(CommandHelloWorld.count);
	}

	static class CommandHelloWorld extends HystrixCommand<String> {

		private final String name;
		private static volatile int count;

		public CommandHelloWorld(Setter config, String name) {
			super(config);
			this.name = name;
		}

		@Override
		protected String run() throws Exception { //完成业务逻辑
			System.out.println("executionTimeoutInMilliseconds="
					+ super.getProperties().executionIsolationSemaphoreMaxConcurrentRequests().get());

			++count;
			if (count % 2 == 0) {
				throw new RuntimeException("erro");
			}
			return "Hello " + name + "!";
		}

		@Override
		protected String getFallback() { // run方法抛出异常的时候返回备用结果
			return "Hello Failure " + name + "!";
		}

	}

}
