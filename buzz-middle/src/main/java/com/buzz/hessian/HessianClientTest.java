package com.buzz.hessian;

import java.net.MalformedURLException;

import org.springframework.remoting.caucho.HessianProxyFactoryBean;

import com.buzz.hessian.HelloService;
import com.caucho.hessian.client.HessianProxyFactory;

public class HessianClientTest {

	public static void test1() {

		HessianProxyFactoryBean factory = new HessianProxyFactoryBean();
		factory.setServiceUrl("http://localhost:8080/helloService");
		factory.setServiceInterface(HelloService.class);
		factory.setOverloadEnabled(true);
		factory.setHessian2(true);
		factory.afterPropertiesSet();
		HelloService helloServcie = (HelloService) factory.getObject();
		System.out.println(helloServcie.getClass());
		Long id = 1l;
		System.out.println(helloServcie.hello("test", id, null));
	}

	public static void main(String[] args) throws MalformedURLException {
		test1();
	}

	public static void test2() throws MalformedURLException {
		HessianProxyFactory factory = new HessianProxyFactory();
		HelloService helloService = (HelloService) factory.create(HelloService.class,
				"http://localhost:8080/helloService", Thread.currentThread().getContextClassLoader());

		System.out.println(helloService.hello());
	}
}
