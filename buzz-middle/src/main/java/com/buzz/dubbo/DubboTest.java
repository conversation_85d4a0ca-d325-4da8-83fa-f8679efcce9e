package com.buzz.dubbo;

import com.alibaba.dubbo.config.ProtocolConfig;
import com.alibaba.dubbo.config.RegistryConfig;

public class DubboTest {

	public static RegistryConfig registry_config = getRegistryConfig(Env.TEST);

	private static RegistryConfig getRegistryConfig(Env env) {

		RegistryConfig registry = new RegistryConfig();
		registry.setProtocol("zookeeper");
		registry.setAddress(env.host);
		registry.setGroup("dubbo_test");

		return registry;
	}

	public ProtocolConfig protocolConfig = getProtocolConfig();

	private static ProtocolConfig getProtocolConfig() {
		ProtocolConfig protocol = new ProtocolConfig();
		protocol.setName("dubbo");
		protocol.setPort(20880);
		protocol.setThreads(1);

		return protocol;
	}

	
	public static enum Env {
		LOCAL("localhost:2181"),
		TEST("zktestserver1.wacai.info:22181");
		
		private String host;

		private Env(String host) {
			this.host = host;
		}
		
		
	}
}
