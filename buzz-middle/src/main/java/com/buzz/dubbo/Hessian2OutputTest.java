package com.buzz.dubbo;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;

import com.alibaba.com.caucho.hessian.io.Hessian2Output;

public class Hessian2OutputTest {

	public static void main(String[] args) throws IOException {

		OutputStream os = new ByteArrayOutputStream();
		Hessian2Output out = new Hessian2Output(os);
		Item item = new Item();
		item.setId(1001l);
		item.setName("test");
		out.writeObject(item);
		out.flush();

	}

	private static class Item implements java.io.Serializable {

		private static final long serialVersionUID = 1L;

		private Long id;

		private String name;

		public Long getId() {
			return id;
		}

		public void setId(Long id) {
			this.id = id;
		}

		public String getName() {
			return name;
		}

		public void setName(String name) {
			this.name = name;
		}

	}
}
