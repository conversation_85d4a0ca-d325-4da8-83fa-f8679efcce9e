package com.buzz.dubbo.spi;

import com.alibaba.dubbo.common.Constants;
import com.alibaba.dubbo.common.URL;
import com.alibaba.dubbo.common.extension.ExtensionLoader;
import com.alibaba.dubbo.rpc.Filter;
import org.junit.Test;

import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class ExtensionLoaderTest {


    @Test
    public void test() throws Exception {

        String type = Filter.class.getName();
        String file = "META-INF/dubbo/" + type;
        System.out.println("file=" + file);
        ClassLoader cl = Thread.currentThread().getContextClassLoader();

        Enumeration<java.net.URL> urls = cl.getResources(file);
        while (urls.hasMoreElements()) {
            java.net.URL url = urls.nextElement();
            System.out.println("url=" + url);
        }
        ExtensionLoader<Filter> extensionLoader = ExtensionLoader.getExtensionLoader(Filter.class);
        System.out.println("log=" + extensionLoader.getExtension("log").getClass().getName());

    }

    /**
     * dubbo://*************:20880/com.wacai.quntum.example.common.DubboTestService?application=quantum-example-consumer
     * &check=false&default.check=false&dubbo=2.0.2&interface=com.wacai.quntum.example.common.DubboTestService&logger=slf4j
     * &methods=heartbeat,echo,getLatestHeartbeat&pid=39729&reference.filter=log&register.ip=*************&side=consumer&timestamp=1656602032801
     */
    @Test
    public void testLoadFilters() {
        Map<String, String> parameters = new HashMap();
        parameters.put("accesslog", "true");
        parameters.put("anyhost", "true");
        parameters.put("check", "false");
        parameters.put("dubbo", "2.0.2");
        parameters.put("generic", "false");
        parameters.put("interface", "com.wacai.quntum.example.common.DubboTestService");
        parameters.put("logger", "slf4j");
        parameters.put("side", "consumer");
        parameters.put("reference.filter","log");

        URL url = new URL("dubbo", "*************", 20800, "com.wacai.quntum.example.common.DubboTestService", parameters);

        //返回 consumer 所有filter
        List<Filter> consumerFilter = ExtensionLoader.getExtensionLoader(Filter.class)
                .getActivateExtension(url, Constants.REFERENCE_FILTER_KEY, Constants.CONSUMER);
        consumerFilter.stream().forEach(i -> System.out.println(i));

        System.out.println("=========");
        //返回 provider 所有filter
        List<Filter> providerFilter = ExtensionLoader.getExtensionLoader(Filter.class)
                .getActivateExtension(url, "service.filter", "provider");
        providerFilter.stream().forEach(i -> System.out.println(i));


    }
}
