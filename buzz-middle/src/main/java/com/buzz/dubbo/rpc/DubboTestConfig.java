package com.buzz.dubbo.rpc;

import com.alibaba.dubbo.config.RegistryConfig;

/**
 * <AUTHOR>
 * @description
 * @date 2022-07-20 16:50
 **/
public class DubboTestConfig {

    public static RegistryConfig getRegistryConfig(String type) {
        RegistryConfig registry = new RegistryConfig();

        if("zk".equals(type)){
            registry.setProtocol("zookeeper");
            registry.setAddress("*************:2181");
            registry.setGroup("dubbo");
        }else{
            registry.setProtocol("nacos");
            registry.setAddress("*************:8848");
            //registry.setGroup("dubbo");
        }
        return registry;
    }
}
