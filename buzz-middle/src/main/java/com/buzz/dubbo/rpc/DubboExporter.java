package com.buzz.dubbo.rpc;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ServiceConfig;

/**
 * <AUTHOR>
 * @description
 * @date 2022-07-20 16:43
 **/
public class DubboExporter {

    private ApplicationConfig application;

    private String registryProtocol;

    private String version;

    DubboExporter(String appName) {
        application = new ApplicationConfig();
        application.setName(appName);
    }

    public void export(Object service) {
        ServiceConfig<Object> serviceConfig = new ServiceConfig<>();
        serviceConfig.setApplication(application);
        // 多个注册中心可以用setRegistries()
        serviceConfig.setRegistry(DubboTestConfig.getRegistryConfig(registryProtocol));
        serviceConfig.setInterface(service.getClass().getInterfaces()[0]);
        serviceConfig.setRef(service);
        serviceConfig.setVersion(version);
        serviceConfig.export();
        System.out.println("export success!" + serviceConfig);

    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private String appName = "service-app-provider";
        private String registry;
        private String version;

        public Builder registry(String registry){
            this.registry = registry;
            return this;
        }

        public Builder version(String version){
            this.version = version;
            return this;
        }

        public DubboExporter build() {
            DubboExporter exporter = new DubboExporter(appName);
            exporter.registryProtocol = registry;
            exporter.version = version;
            return exporter;
        }
    }
}
