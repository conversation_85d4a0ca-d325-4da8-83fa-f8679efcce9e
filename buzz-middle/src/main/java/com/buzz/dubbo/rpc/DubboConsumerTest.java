package com.buzz.dubbo.rpc;

import com.alibaba.dubbo.rpc.service.GenericService;
import org.junit.Test;

import java.io.IOException;

/**
 * <AUTHOR>
 * @description
 * @date 2022-07-20 17:00
 **/
public class DubboConsumerTest {

    @Test
    public void test() throws IOException {
//        DubboExporter exporter = DubboExporter.builder().registry("nacos").build();
//        exporter.export(new BizServiceImpl());

        GenericService service = DubboConsumerBuilder.builder().registry("nacos").version("1.1").build("com.buzz.springboot.app.BizService");

        System.out.println("waiting...");
        System.in.read();

    }
}
