package com.buzz.dubbo;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.alibaba.dubbo.common.URL;
import com.alibaba.dubbo.common.extension.ExtensionLoader;
import com.alibaba.dubbo.rpc.Exporter;
import com.alibaba.dubbo.rpc.Invoker;
import com.alibaba.dubbo.rpc.Protocol;
import com.alibaba.dubbo.rpc.ProxyFactory;
import com.alibaba.dubbo.rpc.RpcException;
import com.alibaba.dubbo.rpc.RpcInvocation;
import com.buzz.dubbo.mock.BizService;
import com.buzz.dubbo.mock.BizServiceImpl;

public class LocalProtocol implements Protocol {

	private static Map<String, Invoker<?>> serviceRegister = new HashMap<String, Invoker<?>>();

	private final List<Exporter<?>> exporters = new ArrayList<Exporter<?>>();

	@Override
	public int getDefaultPort() {
		return 0;
	}

	@Override
	public <T> Exporter<T> export(Invoker<T> invoker) throws RpcException {
		String key = invoker.getUrl().toString();
		System.out.println("export invoker \t" + key);
		serviceRegister.put(key, invoker);

		Exporter<T> exporter = new Exporter<T>() {

			@Override
			public Invoker<T> getInvoker() {
				return invoker;
			}

			@Override
			public void unexport() {
				String key = invoker.getUrl().toString();
				System.out.println("unexport invoker \t" + key);
				serviceRegister.remove(key);
				getInvoker().destroy();
			}

		};

		exporters.add(exporter);
		return exporter;
	}

	@Override
	public <T> Invoker<T> refer(Class<T> type, URL url) throws RpcException {
		return (Invoker<T>) serviceRegister.get(url.toString());
	}

	@Override
	public void destroy() {
		//服务销毁
		for (Exporter<?> exporter : exporters) {
			exporter.unexport();
		}
	}

	public static void main(String[] args) {
		ProxyFactory pf = ExtensionLoader.getExtensionLoader(ProxyFactory.class).getAdaptiveExtension();
		System.out.println(pf);

		BizService service = new BizServiceImpl();
		URL url = new URL("local", "localhost", 3306);
		Invoker<BizService> invoker = pf.getInvoker(service, BizService.class, url);
		System.err.println(invoker);
		LocalProtocol localProtocol = new LocalProtocol();
		localProtocol.export(invoker);

		RpcInvocation invocation = new RpcInvocation();
		invocation.setMethodName("sayHelloToWafe");
		invocation.setParameterTypes(new Class[] { Long.class });
		invoker = localProtocol.refer(BizService.class, url);
		invocation.setArguments(new Long[] {1l});
		invoker.invoke(invocation);
	}

}
