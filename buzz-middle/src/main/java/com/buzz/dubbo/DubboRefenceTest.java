package com.buzz.dubbo;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ReferenceConfig;
import com.alibaba.dubbo.config.RegistryConfig;
import com.buzz.dubbo.mock.BizService;

import java.io.IOException;
import java.util.concurrent.atomic.AtomicBoolean;

//@see DubboConsumerTest
@Deprecated
public class DubboRefenceTest {

	public static void main(String[] args) throws IOException {

        // 当前应用配置
        ApplicationConfig application = new ApplicationConfig();
        application.setName("consumer");

        // 连接注册中心配置
        RegistryConfig registry = new RegistryConfig();
        registry.setAddress("localhost:2181");
        registry.setProtocol("zookeeper");
        registry.setGroup("dubbo_test");

        // 注意：ReferenceConfig为重对象，内部封装了与注册中心的连接，以及与服务提供方的连接

        // 引用远程服务
        ReferenceConfig<BizService> reference = new ReferenceConfig<BizService>(); // 此实例很重，封装了与注册中心的连接以及与提供者的连接，请自行缓存，否则可能造成内存和连接泄漏
        reference.setApplication(application);
        reference.setRegistry(registry); // 多个注册中心可以用setRegistries()
        reference.setInterface(BizService.class);
        reference.setVersion("1.0.0");
        reference.setTimeout(5000);
        //reference.setUrl("dubbo://127.0.0.1:20880"); 
        System.out.println(reference.getUrl());
        // 和本地bean一样使用xxxService
        BizService dubboService = reference.get(); // 注意：此代理对象内部封装了所有通讯细节，对象较重，请缓存复用
        
        AtomicBoolean stop = new AtomicBoolean();
        Thread thread = new Thread(new Runnable() {
			
			@Override
			public void run() {
				while(!stop.get()) {
					dubboService.sayHelloToWafe((int)Math.random());
					System.out.println("invoke");
					try {
						Thread.sleep(1000);
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
				}
			}
		});
        thread.setDaemon(true);
        thread.start();
        System.in.read();
        stop.set(false);

    }
}