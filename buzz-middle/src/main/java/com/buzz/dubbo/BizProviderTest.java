package com.buzz.dubbo;

import com.alibaba.dubbo.config.ApplicationConfig;
import com.alibaba.dubbo.config.ServiceConfig;
import com.buzz.dubbo.mock.BizService;
import com.buzz.dubbo.mock.BizServiceImpl;

import java.io.IOException;

//@see DubboProviderTest
@Deprecated
public class BizProviderTest {

	private ServiceConfig<BizService> serviceConfig;
	private ApplicationConfig application;

	public BizProviderTest() {
		application = new ApplicationConfig();
		application.setName("service-provider");
	}

	public void export(BizService target) {
		serviceConfig = new ServiceConfig<>();
		serviceConfig.setApplication(application);
		// 多个注册中心可以用setRegistries()
		serviceConfig.setRegistry(DubboTest.registry_config);
		serviceConfig.setInterface(BizService.class);
		serviceConfig.setRef(target);
		serviceConfig.setVersion("1.0.0");
		serviceConfig.export();

	}

	public static void main(String[] args) throws IOException {

		BizProviderTest test = new BizProviderTest();
		test.export(new BizServiceImpl());
		System.out.println("waiting....");
		System.in.read();

	}
}