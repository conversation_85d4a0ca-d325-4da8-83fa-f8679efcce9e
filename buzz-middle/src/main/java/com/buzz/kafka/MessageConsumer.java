package com.buzz.kafka;

import lombok.Data;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndMetadata;
import org.apache.kafka.common.TopicPartition;

import java.time.Duration;
import java.util.*;
import java.util.concurrent.CountDownLatch;

/**
 * kafka consumer的简单封装
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public abstract class MessageConsumer {

    private KafkaConsumer<String, byte[]> kafkaConsumer;
    @Getter
    private String consumerId;

    private CountDownLatch latch;

    public MessageConsumer(KafkaConsumer<String, byte[]> kafkaConsumer) {
        this.kafkaConsumer = kafkaConsumer;
        this.consumerId = UUID.randomUUID().toString();
        this.latch = new CountDownLatch(1);
    }


    public void start() {

        Thread thread = new Thread(new MessageConsumerThread());
        thread.setDaemon(false);
        thread.start();
    }

    public void await() {
        try {
            latch.await();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    public abstract void processMessage(Message message);

    private class MessageConsumerThread implements Runnable {

        public void run() {
            while (true) {
                ConsumerRecords<String, byte[]> records = kafkaConsumer.poll(Duration.ofMillis(500));
                List<Message> messageList = convertToMessage(records).getMessageList();
                for (Message message : messageList) {
                    try {
                        processMessage(message);
                        Map<TopicPartition, OffsetAndMetadata> map = new HashMap<>();
                        map.put(new TopicPartition(message.getTopic(), message.getPartition()), new OffsetAndMetadata(message.getOffset()));
                        log.info("ack topic={}, partition={}, offset={}", message.getTopic(), message.getPartition(), message.getOffset());
                        kafkaConsumer.commitSync(map);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }
    }

    public static BatchMessage convertToMessage(ConsumerRecords records) {
        BatchMessage batchMessage = new BatchMessage();
        List<Message> messageList = new ArrayList();
        Set<TopicPartition> partitions = records.partitions();
        if (partitions.isEmpty()) {
            batchMessage.setTotal(partitions.size());
            return batchMessage;
        }

        for (TopicPartition partition : partitions) {
            List<ConsumerRecord<String, byte[]>> recordList = records.records(partition);
            for (ConsumerRecord<String, byte[]> record : recordList) {
                Message message = new Message();
                message.setTopic(record.topic());
                message.setPartition(record.partition());
                message.setValue(record.value());
                message.setOffset(record.offset());
                messageList.add(message);
                batchMessage.setTopic(record.topic());
            }
        }
        batchMessage.setMessageList(messageList);
        return batchMessage;

    }

    public static class MessageConsumerConfig {

    }

    @Data
    public static class BatchMessage {
        private String topic;
        private int partition;

        private long startOffset = -1;
        private long endOffset = -1;
        private int total = 0;
        private List<Message> messageList = new ArrayList<>();
    }

    @Data
    public static class Message {
        private String topic;
        private int partition;
        private byte[] value;
        private long offset;
    }
}
