package com.buzz.kafka;

import ch.qos.logback.classic.Level;
import com.buzz.kafka.KafkaClientBuilder.Env;
import com.buzz.kafka.MessageConsumer.BatchMessage;
import com.buzz.kafka.MessageConsumer.Message;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.Test;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.*;
import java.util.Map.Entry;

/**
 * <AUTHOR>
 * @description
 **/
public class KafkaBatchConsumeTest {

    static {
        ch.qos.logback.classic.Logger logger = (ch.qos.logback.classic.Logger) LoggerFactory.getLogger("org.apache.kafka");
        logger.setLevel(Level.WARN);
    }


    private void batchConsumer(Object[][] dataSource) {
        BatchConsumerBuilder batchConsumerBuilder = BatchConsumerBuilder.builder();
        for (Object[] item : dataSource) {
            String topic = (String) item[0];
            if (topic == null) continue;

            int partition = (int) item[1];
            long offset = (long) item[2];
            System.out.printf("assign %s %s \r\n", topic, offset);
            batchConsumerBuilder.assign(topic, partition);
            batchConsumerBuilder.offset(topic, partition, offset);
        }
        Fetcher fetcher = new Fetcher(batchConsumerBuilder);
        fetcher.start();
        try {
            System.in.read();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void testBatchConsume1() throws IOException {
        Object[][] dataSource = new Object[][]{
                {"bairen.test.373", 0, 0l},
        };
        batchConsumer(dataSource);
    }

    @Test
    public void testBatchConsume2() throws IOException {
        Object[][] dataSource = new Object[100][3];
        for (int i = 0; i < 5; ++i) {
            String topic = "bairen.test." + i;
            dataSource[i][0] = topic;
            dataSource[i][1] = 0;
            dataSource[i][2] = 1l;
        }
        batchConsumer(dataSource);
    }

    @Test
    public void testGetBeginOffset() {
        KafkaConsumer<String, byte[]> kafkaConsumer = KafkaClientBuilder.builder()
                .env(Env.LOG)
                .buildKafkaConsumer();

        List<TopicPartition> partitions = new ArrayList<>();
        for (int i = 0; i < 10; ++i) {
            String topic = "bairen.test." + i;
            partitions.add(new TopicPartition(topic, 0));
        }
        Map<TopicPartition, Long> beginningOffsetMap = kafkaConsumer.beginningOffsets(partitions);
        Map<TopicPartition, Long> endOffsetMap = kafkaConsumer.endOffsets(partitions);
        for (Entry<TopicPartition, Long> entry : beginningOffsetMap.entrySet()) {
            TopicPartition partition = entry.getKey();
            long beginOffset = entry.getValue();
            long endOffset = endOffsetMap.get(partition);
            System.out.println(partition.topic() + "\t beginOffset=" + beginOffset + "\t endOffset=" + endOffset);
        }
    }


    private static class BatchConsumerBuilder {

        public static BatchConsumerBuilder builder() {
            return new BatchConsumerBuilder();
        }

        private List<TopicPartition> partitionInfoList = new ArrayList<>();
        private Map<TopicPartition, Long> offsetMap = new HashMap<>();

        public BatchConsumerBuilder assign(String topic, int partition) {
            partitionInfoList.add(new TopicPartition(topic, partition));
            return this;
        }

        public void offset(String topic, int partition, Long offset) {
            offsetMap.put(new TopicPartition(topic, partition), offset);
        }

        public KafkaConsumer<String, byte[]> build() {
            Properties props = new Properties();
            props.put("bootstrap.servers", Env.LOG.getServers());
            props.put("client.id", IPUtil.localIpCached());
            props.put("key.deserializer", StringDeserializer.class);
            props.put("value.deserializer", ByteArrayDeserializer.class);
            props.put("key.serializer", StringSerializer.class);
            props.put("value.serializer", ByteArraySerializer.class);
            props.put("max.poll.records", 3);//单次消费者拉取的最大数据条数
            props.put("enable.auto.commit", "false");
            props.put("auto.offset.reset", "none");//offset范围超限报错
            return new KafkaConsumer<String, byte[]>(props);
        }

    }

    @Slf4j
    public static class Fetcher implements Runnable {

        private BatchConsumerBuilder batchConsumerBuilder;
        private KafkaConsumer<String, byte[]> kafkaConsumer;

        public Fetcher(BatchConsumerBuilder batchConsumerBuilder) {
            this.batchConsumerBuilder = batchConsumerBuilder;
            this.kafkaConsumer = batchConsumerBuilder.build();
        }

        public void start() {
            new Thread(this).start();
        }

        public void processMessage(Message message) {
            String value = new String(message.getValue(), StandardCharsets.UTF_8);
            log.info("onMessageReceived offset={},partition={},value={}", message.getOffset(),
                    message.getPartition(), value);
        }

        public BatchMessage fetch() {
            try {
                kafkaConsumer.assign(batchConsumerBuilder.partitionInfoList);
                StringBuffer sb = new StringBuffer();
                for (Entry<TopicPartition, Long> entry : batchConsumerBuilder.offsetMap.entrySet()) {
                    TopicPartition topicPartition = entry.getKey();
                    Long offset = entry.getValue();
                    kafkaConsumer.seek(topicPartition, offset);
                    sb.append(topicPartition + ":" + offset);
                    sb.append(" ,");
                }
                //log.info("seek {}", sb.toString());
                ConsumerRecords<String, byte[]> records = kafkaConsumer.poll(Duration.ofMillis(1000));
                return MessageConsumer.convertToMessage(records);
            } catch (Exception e) {
                e.printStackTrace();
                System.exit(-1);
                return null;
            }
        }

        @Override
        public void run() {
            while (true) {
                BatchMessage batchMessage = fetch();
                List<Message> messageList = batchMessage.getMessageList();

                if (!messageList.isEmpty()) {
                    System.out.println("rec" + batchMessage);
                }
                for (Message message : messageList) {
                    try {
                        //processMessage(message);
                        //log.info("ack topic={}, partition={}, offset={}", message.getTopic(), message.getPartition(), message.getOffset());
                        batchConsumerBuilder.offsetMap.put(new TopicPartition(message.getTopic(), message.getPartition()), message.getOffset() + 1);
                        //kafkaConsumer.commitSync(map);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
            }
        }


    }
}
