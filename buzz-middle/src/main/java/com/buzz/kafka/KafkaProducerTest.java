package com.buzz.kafka;

import com.buzz.kafka.KafkaClientBuilder.Env;
import com.buzz.util.StopWatch;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.Callback;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.junit.Test;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.Future;

import static com.alibaba.fastjson.JSON.toJSONString;

/**
 * https://kafka.apache.org/28/javadoc/org/apache/kafka/clients/producer/KafkaProducer.html
 * com.wacai.hermes.proxy.producer.kafka.KafkaProducerWrapper
 * <p>
 * KafkaProducer --> Sender --> NetworkClient
 */
@Slf4j
public class KafkaProducerTest {


    @Test
    public void testWarmUp() {
        KafkaProducer<String, byte[]> producer = KafkaClientBuilder.builder()
                .env(Env.LOG)
                .buildKafkaProducer();
        StopWatch watch = StopWatch.create().start();
        producer.partitionsFor("bairen.test.101");
        watch.trigger(0, (cost) -> {
            System.out.println("cost " + cost);
        });
    }

    //what is kafka message key
    //attaching a key to messages will ensure messages with the same key always go to the same partition in a topic
    //https://stackoverflow.com/questions/29511521/is-key-required-as-part-of-sending-messages-to-kafka
    public void asyncSend(String topic, Integer partition, String key, String data) throws Throwable {

        KafkaProducer<String, byte[]> topicProduce = KafkaClientBuilder.builder().buildKafkaProducer();
        ProducerRecord<String, byte[]> record = new ProducerRecord<String, byte[]>(topic, partition, key,
                data.getBytes());

        log.info("prepare send message");
        Future<RecordMetadata> future = topicProduce.send(record, (metadata, exception) -> {
            if (exception == null) {
                log.info("send ok,offset:" + metadata.offset() + ",partition:" + metadata.partition());
            } else {
                log.error("send faild,topic:" + topic, exception);
            }
        });
        future.get();
    }

    /**
     * 同步发送
     *
     * @param topic
     * @param partition
     * @param key
     * @param data
     * @throws InterruptedException
     * @throws ExecutionException
     */
    private void doSyncSend(KafkaProducer<String, byte[]> producer,
                            String topic,
                            Integer partition,
                            String data,
                            String key) {

        ProducerRecord<String, byte[]> record = new ProducerRecord<String, byte[]>(topic, partition, key,
                data.getBytes());
        try {
            RecordMetadata metadata = producer.send(record).get();
            log.info("send ok,offset:" + metadata.offset());
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
    }

    /**
     * 测试发送
     *
     * @throws Throwable
     */
    @Test
    public void testSyncSend() throws Throwable {
        KafkaProducer<String, byte[]> producer = KafkaClientBuilder.builder()
                .env(Env.TEST)
                .buildKafkaProducer();
        doSyncSend(producer, "manyou.test", 0, "hello", null);
    }

    public static void main(String[] args) throws Throwable {
        new KafkaProducerTest().testSyncSend();
    }

    /**
     * 测试发送一个不存在的partition
     * 会等10秒抛出TimeoutException
     */
    @Test
    public void testSendAbsentPartition() {
        KafkaProducer<String, byte[]> producer = KafkaClientBuilder.builder()
                .env(Env.LOG)
                .buildKafkaProducer();
        doSyncSend(producer, "bairen.test.101", 1, "hello", null);
    }

    @Test
    public void test_SendLegionLog() throws Exception {
        KafkaProducer<String, byte[]> topicProduce = KafkaClientBuilder
                .builder()
                .env(KafkaClientBuilder.Env.LOG)
                .buildKafkaProducer();

        List<ObjectDTO> list = getData();
        byte[] data = toJSONString(list).getBytes();
        System.out.println("send data " + data.length);
        ProducerRecord<String, byte[]> record = new ProducerRecord<String, byte[]>("legion.sinker.test", 0, null, data);
        topicProduce.send(record);
        System.in.read();
//        RecordMetadata metadata = topicProduce.send(record).get();
//        log.info("send ok,offset:" + metadata.offset());
    }

    private List<ObjectDTO> getData() {
        List<ObjectDTO> list = new ArrayList();
        Map<String, Object> data = new HashMap<>();
        data.put("@timestamp", "2022-10-24T19:52:52.000+08:00");
        data.put("input_type", "log");
        data.put("message", "common.wacai.com:443 | ************ | \"-\" | POST /prism/metrics/meta/2 HTTP/1.1");
        data.put("type", "nginx_log");
        data.put("client_ip", "************");
        list.add(new ObjectDTO("index", data));
        return list;
    }

    @Data
    @AllArgsConstructor
    public class ObjectDTO {

        private String operate;
        private Map<String, Object> data;
    }
}
