package com.buzz.kafka.server.controller;

import org.junit.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Map;

//测试副本分配算法
public class ReplicaAssignmentTest {

    @Test
    public void test() {

        int nPartitions = 20;
        int replicationFactor = 3;
        List<Integer> brokerList = Arrays.asList(0,1,2,3,4);
        int fixedStartIndex = 0;
        int startPartitionId = -1;

        Map<Integer, List<Integer>> result = ReplicaAssignment.assignReplicasToBrokersRackUnaware(
                nPartitions, replicationFactor, brokerList, fixedStartIndex, startPartitionId);

        for (Map.Entry<Integer, List<Integer>> entry : result.entrySet()) {
            System.out.println("Partition: " + entry.getKey() + ", Replicas: " + entry.getValue());
        }
    }
}
