package com.buzz.kafka.server.controller;

import lombok.extern.slf4j.Slf4j;

import java.util.*;

@Slf4j
public class ReplicaAssignment {

    public static Map<Integer, List<Integer>> assignReplicasToBrokersRackUnaware(int nPartitions,
                                                                                 int replicationFactor,
                                                                                 List<Integer> brokerList,
                                                                                 int fixedStartIndex,
                                                                                 int startPartitionId) {
        Map<Integer, List<Integer>> ret = new HashMap<>();
        List<Integer> brokerArray = new ArrayList<>(brokerList);
        Random rand = new Random();
        //startIndex是一个常量，表示起始坐标，不会被修改
        final int startIndex = fixedStartIndex >= 0 ? fixedStartIndex : rand.nextInt(brokerArray.size());
        int currentPartitionId = Math.max(0, startPartitionId);
        int nextReplicaShift = fixedStartIndex >= 0 ? fixedStartIndex : rand.nextInt(brokerArray.size());

        for (int i = 0; i < nPartitions; i++) {
            if (currentPartitionId > 0 && (currentPartitionId % brokerArray.size() == 0))
                nextReplicaShift += 1;//当分区Id轮训完一轮brokerNum自增

            int firstReplicaIndex = (currentPartitionId + startIndex) % brokerArray.size();
            List<Integer> replicaBuffer = new ArrayList<>();
            replicaBuffer.add(brokerArray.get(firstReplicaIndex)); //分配分区第一个副本，采用round-robin

            for (int j = 0; j < replicationFactor - 1; j++) {//分配分区后续副本
                int replicaIndex = replicaIndex(firstReplicaIndex, nextReplicaShift, j, brokerArray.size());
                replicaBuffer.add(brokerArray.get(replicaIndex));
            }
            log.info("currentPartitionId: {} , firstReplicaIndex:{}, nextReplicaShift:{}, replica: {}", currentPartitionId, firstReplicaIndex, nextReplicaShift, replicaBuffer);
            ret.put(currentPartitionId, replicaBuffer);
            currentPartitionId += 1;
        }
        return ret;
    }

    /**
     * @param firstReplicaIndex=  分区id % brokerNum
     * @param secondReplicaShift= 当分区Id轮训完一轮自增
     * @param replicaIndex=       副本索引
     * @param nBrokers=           broker数量
     * @return
     */
    private static int replicaIndex(int firstReplicaIndex, int secondReplicaShift, int replicaIndex, int nBrokers) {
        int shift = 1 + (secondReplicaShift + replicaIndex) % (nBrokers - 1);
        return (firstReplicaIndex + shift) % nBrokers;

        //和下面这行代码类似
        //return (firstReplicaIndex+1+secondReplicaShift+replicaIndex)%nBrokers;
    }


    private Map<Integer, List<Integer>> assignReplicasToBrokersRackAware(int nPartitions,
                                                                         int replicationFactor,
                                                                         List<BrokerMetadata> brokerMetadatas,
                                                                         int fixedStartIndex,
                                                                         int startPartitionId) {
        Map<Integer, String> brokerRackMap = new HashMap<>();
        for (BrokerMetadata metadata : brokerMetadatas) {
            if (metadata.getRack().isPresent()) {
                brokerRackMap.put(metadata.getId(), metadata.getRack().get());
            }
        }

        Set<String> uniqueRacks = new HashSet<>(brokerRackMap.values());
        int numRacks = uniqueRacks.size();

        List<Integer> arrangedBrokerList = getRackAlternatedBrokerList(brokerRackMap);

        int numBrokers = arrangedBrokerList.size();
        Map<Integer, List<Integer>> ret = new HashMap<>();

        Random rand = new Random();
        int startIndex = (fixedStartIndex >= 0) ? fixedStartIndex : rand.nextInt(arrangedBrokerList.size());
        int currentPartitionId = Math.max(0, startPartitionId);
        int nextReplicaShift = (fixedStartIndex >= 0) ? fixedStartIndex : rand.nextInt(arrangedBrokerList.size());

        for (int i = 0; i < nPartitions; i++) {
            if (currentPartitionId > 0 && (currentPartitionId % arrangedBrokerList.size() == 0)) {
                nextReplicaShift += 1;
            }

            int firstReplicaIndex = (currentPartitionId + startIndex) % arrangedBrokerList.size();
            int leader = arrangedBrokerList.get(firstReplicaIndex);

            List<Integer> replicaBuffer = new ArrayList<>();
            replicaBuffer.add(leader);

            Set<String> racksWithReplicas = new HashSet<>();
            racksWithReplicas.add(brokerRackMap.get(leader));

            Set<Integer> brokersWithReplicas = new HashSet<>();
            brokersWithReplicas.add(leader);

            int k = 0;
            for (int j = 0; j < replicationFactor - 1; j++) {
                boolean done = false;
                while (!done) {
                    int broker = arrangedBrokerList.get(replicaIndex(firstReplicaIndex, nextReplicaShift * numRacks, k, arrangedBrokerList.size()));
                    String rack = brokerRackMap.get(broker);

                    if ((!racksWithReplicas.contains(rack) || racksWithReplicas.size() == numRacks)
                            && (!brokersWithReplicas.contains(broker) || brokersWithReplicas.size() == numBrokers)) {
                        replicaBuffer.add(broker);
                        racksWithReplicas.add(rack);
                        brokersWithReplicas.add(broker);
                        done = true;
                    }
                    k += 1;
                }
            }
            ret.put(currentPartitionId, replicaBuffer);
            currentPartitionId += 1;
        }
        return ret;
    }


    /**
     * Given broker and rack information, returns a list of brokers alternated by the rack. Assume
     * this is the rack and its brokers:
     * <p>
     * rack1: 0, 1, 2
     * rack2: 3, 4, 5
     * rack3: 6, 7, 8
     * <p>
     * This API would return the list of 0, 3, 6, 1, 4, 7, 2, 5, 8
     */
    private List<Integer> getRackAlternatedBrokerList(Map<Integer, String> brokerRackMap) {
        Map<String, List<Integer>> brokersByRack = getInverseMap(brokerRackMap);
        List<String> racks = new ArrayList<>(brokersByRack.keySet());
        Collections.sort(racks);

        List<Integer> result = new ArrayList<>();
        int rackIndex = 0;

        while (result.size() < brokerRackMap.size()) {
            List<Integer> rackBrokers = brokersByRack.get(racks.get(rackIndex));
            if (!rackBrokers.isEmpty()) {
                result.add(rackBrokers.remove(0));
            }
            rackIndex = (rackIndex + 1) % racks.size();
        }
        return result;
    }

    private Map<String, List<Integer>> getInverseMap(Map<Integer, String> brokerRackMap) {
        Map<String, List<Integer>> inverseMap = new HashMap<>();
        for (Map.Entry<Integer, String> entry : brokerRackMap.entrySet()) {
            String rack = entry.getValue();
            int broker = entry.getKey();
            if (!inverseMap.containsKey(rack)) {
                inverseMap.put(rack, new ArrayList<>());
            }
            inverseMap.get(rack).add(broker);
        }
        return inverseMap;
    }

    private static class BrokerMetadata {
        private int id;
        private Optional<String> rack;

        public BrokerMetadata(int id, Optional<String> rack) {
            this.id = id;
            this.rack = rack;
        }

        public int getId() {
            return id;
        }

        public Optional<String> getRack() {
            return rack;
        }
    }

}
