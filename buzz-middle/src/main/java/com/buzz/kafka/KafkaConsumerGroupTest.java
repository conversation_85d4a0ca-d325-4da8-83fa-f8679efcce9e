package com.buzz.kafka;

import org.apache.kafka.clients.admin.*;
import org.apache.kafka.common.TopicPartition;
import org.junit.Test;

import java.util.*;

/**
 * 如何查询消费组信息？ 虽然消费group信息是存在__consumer topic中，kafka broker其实有缓存的
 *
 * https://github.com/hiddenzzh/kafka_book_demo/blob/master/src/main/java/chapter10/KafkaConsumerGroupService.java
 *
 * <AUTHOR>
 * @description
 **/
public class KafkaConsumerGroupTest {

    KafkaConsumerGroupService service = new KafkaConsumerGroupService(KafkaClientBuilder.builder().buildAdminClient());

    @Test
    public void testListConsumerGroup() throws Exception {

        service.listGroup().stream().forEach(System.out::println);
    }

    @Test
    public void testGetConsumerGroupDetail() throws Exception {
        service.listGroup().stream().forEach((group) -> {
            try {
                service.describeGroup(group);
            } catch (Exception e) {
                e.printStackTrace();
            }
            System.out.println("==================");
        });
    }

    static class KafkaConsumerGroupService {
        private AdminClient client;

        public KafkaConsumerGroupService(AdminClient client) {
            this.client = client;
        }

        public List<String> listGroup() throws Exception {
            List<String> groups = new ArrayList();
            ListConsumerGroupsResult result = client.listConsumerGroups();
            for (ConsumerGroupListing listing : result.all().get()) {
                groups.add(listing.groupId());
            }
            return groups;
        }

        public void describeGroup(String group) throws Exception {
            DescribeConsumerGroupsResult groupResult = client.describeConsumerGroups(Collections.singleton(group));
            ConsumerGroupDescription description = groupResult.all().get().get(group);

            Collection<MemberDescription> members = description.members();
            if (members != null) {
                for (MemberDescription member : members) {
                    MemberAssignment assignment = member.assignment();
                    if (assignment == null) {
                        continue;
                    }
                    Set<TopicPartition> tpSet = assignment.topicPartitions();
                    System.out.println(group + " tpSet=" + tpSet);
                }
            }

        }
    }
}
