package com.buzz.kafka.consumer;

import com.buzz.kafka.CountDown;
import com.buzz.kafka.SourceLogger;
import org.apache.kafka.clients.consumer.ConsumerRebalanceListener;
import org.apache.kafka.common.TopicPartition;
import org.junit.Test;

import java.util.Collection;
import java.util.concurrent.atomic.AtomicLong;

/**
 * Author: bairen
 * Date:2024/4/25 23:51
 */
public class SubscribeTest {

    @Test
    public void testRebalance1() throws InterruptedException {
        CountDown countDown = new CountDown(Integer.MAX_VALUE);
        String topic = "manyou.test";
        BuzzConsumer consumer = new BuzzConsumer("manyou.test.group") {
            public void onProcessedMessage() {
                countDown.countDown();
            }
        };
        consumer.subscribe(topic, new ConsumerRebalanceListener() {
            @Override
            public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
                SourceLogger.info("onPartitionsRevoked {}",partitions);
            }

            @Override
            public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
                SourceLogger.info("onPartitionsAssigned {}",partitions);
            }
        });
        consumer.start();
        countDown.await();
    }

    @Test
    public void testRebalance2() throws InterruptedException {
        AtomicLong counter = new AtomicLong();
        CountDown countDown = new CountDown(Integer.MAX_VALUE);
        String topic = "manyou.test";
        BuzzConsumer consumer = new BuzzConsumer("manyou.test.group") {
            public void onProcessedMessage() {
                countDown.countDown();
            }
        };
        consumer.subscribe(topic, new ConsumerRebalanceListener() {
            @Override
            public void onPartitionsRevoked(Collection<TopicPartition> partitions) {
                SourceLogger.info("onPartitionsRevoked {}",partitions);
//                if(counter.getAndIncrement()==1){
//                }
                throw new IllegalArgumentException("i can't exit c2");
            }

            @Override
            public void onPartitionsAssigned(Collection<TopicPartition> partitions) {
                SourceLogger.info("onPartitionsAssigned {}",partitions);
            }
        });
        consumer.start();
        countDown.await();
    }
}
