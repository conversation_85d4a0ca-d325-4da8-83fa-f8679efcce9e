package com.buzz.kafka;

import java.lang.management.ManagementFactory;
import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.net.UnknownHostException;
import java.util.Collections;
import java.util.Enumeration;


public class IPUtil {
	 private static String ip = null;

	    public static String localIp() {
	        StringBuilder sb = new StringBuilder();
	        try {
	            // 获取第一个非回环 ipv4 地址
	            Enumeration<NetworkInterface> nets = NetworkInterface.getNetworkInterfaces();
	            label:
	            for (NetworkInterface netint : Collections.list(nets)) {
	                for (InetAddress addr : Collections.list(netint.getInetAddresses())) {
	                    if (!addr.isLoopbackAddress() && addr instanceof Inet4Address) {
	                        sb.append(addr.getHostAddress());
	                        break label;
	                    }
	                }
	            }
	            // 没有ipv4地址
	            if (sb.length() == 0) {
	                sb.append(InetAddress.getLocalHost().getHostAddress());
	            }
	        } catch (SocketException e) {
	        } catch (UnknownHostException e) {
	        }
	        return sb.toString();
	    }

	    public static String localIpCached() {
	        if (ip == null) {
	            synchronized (IPUtil.class) {
	                if (ip == null) {
	                    ip = localIp();
	                }
	            }
	        }
	        return ip;
	    }

	    public static long getPID() {
	        String processName = ManagementFactory.getRuntimeMXBean().getName();
	        if (processName != null && processName.length() > 0) {
	            try {
	                return Long.parseLong(processName.split("@")[0]);
	            } catch (Exception e) {
	                return 0;
	            }
	        }
	        return 0;
	    }

	    public static void main(String[] args) {
	        System.out.println(localIp());
	    }
}
