package com.buzz.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.common.TopicPartition;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;

import java.nio.charset.StandardCharsets;
import java.util.Collections;
import java.util.Properties;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 配置详见：https://kafka.apache.org/documentation/#adminclientconfigs
 *
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class KafkaClientBuilder {
    private static AtomicInteger id = new AtomicInteger(1);

    public static KafkaClientBuilder builder() {
        return new KafkaClientBuilder();
    }

    private String topic;
    private int partition = 0;
    private int offset = -1;
    private Env env = Env.TEST;
    private boolean autoCommit = false;
    private String groupId = "zion-neo";
    private int maxPoll = 20;

    public KafkaClientBuilder topic(String topic) {
        this.topic = topic;
        return this;
    }

    public KafkaClientBuilder partition(int partition) {
        this.partition = partition;
        return this;
    }

    public KafkaClientBuilder offset(int offset) {
        this.offset = offset;
        return this;
    }

    public KafkaClientBuilder env(Env env) {
        this.env = env;
        return this;
    }

    public KafkaClientBuilder groupId(String groupId) {
        this.groupId = groupId;
        return this;
    }

    public KafkaClientBuilder autoCommit(boolean autoCommit) {
        this.autoCommit = autoCommit;
        return this;
    }

    public KafkaClientBuilder maxPoll(int maxPoll) {
        this.maxPoll = maxPoll;
        return this;
    }


    //============================ build ==========================================

    /**
     * build KafkaConsumer
     * @return
     */
    public KafkaConsumer<String, byte[]> buildKafkaConsumer() {
        return new KafkaConsumer<String, byte[]>(getConsumerProperties());
    }


    /**
     * build MessageConsumer
     *
     * @return
     */
    public MessageConsumer buildMessageConsumer() {
        AtomicInteger counter = new AtomicInteger(1);
        KafkaConsumer<String, byte[]>  consumer = buildKafkaConsumer();
        if (offset >= 0) {
            TopicPartition topicPartition = new TopicPartition(topic, partition);
            consumer.assign(Collections.singletonList(topicPartition));
            consumer.seek(topicPartition, offset);

        } else {
            consumer.subscribe(Collections.singletonList(topic));
        }

        return new MessageConsumer(consumer) {

            @Override
            public void processMessage(Message message) {
                String value = new String(message.getValue(), StandardCharsets.UTF_8);
                log.info("onMessageReceived consumerId={},offset={},partition={},value={}", getConsumerId(), message.getOffset(),
                        message.getPartition(), value);
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }

                if (counter.getAndIncrement() % 2 == 0) {
                    throw new RuntimeException("Murphy error! offset="+message.getOffset());
                }
            }
        };
    }

    public KafkaProducer<String, byte[]> buildKafkaProducer() {
        return new KafkaProducer<String, byte[]>(getProduceProperties());
    }

    public AdminClient buildAdminClient() {
        return AdminClient.create(getDefaultProperties());
    }

    private Properties getConsumerProperties() {
        Properties props = new Properties();
        props.putAll(getDefaultProperties());

        if(autoCommit){
            props.setProperty("enable.auto.commit", "true");
            props.setProperty(ConsumerConfig.GROUP_ID_CONFIG, groupId);
            props.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest"); //latest或者earliest
        }else if(offset>=0){ //seek模式
            //group.id 参数不设置这样的consumer就是纯proxy模式
            props.setProperty("enable.auto.commit", "false");
            //kafka不会自动管理offset，同时enable.auto.commit必须设置为false
            props.put("auto.offset.reset", "none");//offset范围超限报错
        }else{
            props.setProperty("enable.auto.commit", "false");
            props.setProperty(ConsumerConfig.GROUP_ID_CONFIG, groupId);
            props.setProperty(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest"); //latest或者earliest
        }

        return props;
    }

    public Properties getProduceProperties() {
        Properties props = new Properties();
        props.putAll(getDefaultProperties());
        props.put("max.block.ms", 10 * 1000);
        props.put("acks", "-1");
        //还有一些配置是可选的
//      linger.ms，这个是批量发送的等待时间，如果设置为500毫秒，表示等待500ms这一批数据将会被提交
//		props.put("linger.ms", 50000);
//		props.put("request.timeout.ms", 500);
//		props.put("delivery.timeout.ms", 1000);
        return props;
    }

    public Properties getDefaultProperties() {
        Properties props = new Properties();
        props.put("bootstrap.servers", env.getServers());
        props.put("client.id", IPUtil.localIpCached() +"-"+ id.getAndIncrement());
        props.put("key.deserializer", StringDeserializer.class);
        props.put("value.deserializer", ByteArrayDeserializer.class);
        props.put("key.serializer", StringSerializer.class);
        props.put("value.serializer", ByteArraySerializer.class);
        props.put("max.poll.records", String.valueOf(maxPoll));//单次消费者拉取的最大数据条数
        props.put("enable.auto.commit", "false");
        props.put("fetch.min.bytes", "1048576");//每次拉取更多的数据，设置为 1 MB,参数的默认值是 1 字节。
        props.put(" fetch.max.wait.ms", "1000");//消费者在无法立即拉取到满足 fetch.min.bytes 要求的数据时，等待的最长时间，默认500ms
        return props;
    }

    public enum Env {
        TEST("*************:9081,*************:9081,*************:9081"),
        DRC("*************:9011,*************:9012,*************:9011"),
        LOG("***********:9093,************:9093,172.16.49.10:9093");
        private String servers;

        Env(String servers) {
            this.servers = servers;
        }

        public String getServers() {
            return servers;
        }
    }
}
