package com.buzz.kafka;

import java.util.concurrent.atomic.AtomicInteger;

import org.apache.kafka.common.metrics.Metrics;
import org.apache.kafka.common.utils.AppInfoParser;
import org.junit.Test;

import lombok.extern.slf4j.Slf4j;

/**
 * 验证 AppInfoParser性能问题
 * 
 * <AUTHOR>
 *
 */
@Slf4j
public class AppInfoParserTest {

	private static AtomicInteger counter = new AtomicInteger();
	
	public void registerAppInfo() {
		long begin = System.currentTimeMillis();
		AppInfoParser.registerAppInfo("consumer-client", String.valueOf(System.currentTimeMillis()), new Metrics(),
				System.currentTimeMillis());
		long end = System.currentTimeMillis();
		log.info("registerAppInfo cost=" + (end - begin));
	}
	
	@Test
	public void testRegisterAppInfo() throws InterruptedException {
		for (int i = 0; i < 120; ++i) {
			registerAppInfo();
			Thread.sleep(10);
		}
	}
}
