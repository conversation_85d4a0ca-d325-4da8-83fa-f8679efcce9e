package com.buzz.kafka;

import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.UUID;


@RestController
public class KafkaApplication implements ApplicationRunner{

	String TOPIC_NAME = "qianniao-test";
	KafkaProducerTest produce = new KafkaProducerTest();
	KafkaConsumerTest consumer = new KafkaConsumerTest();
	
	public static void main(String[] args) {
		SpringApplication.run(KafkaApplication.class, args);
	}
	
	@GetMapping("/push")
	public String push() throws Throwable {
		for(int i=0;i<20;++i) {
			produce.asyncSend(TOPIC_NAME,  null, UUID.randomUUID().toString(), "{name:test,op:1}");
		}
		return "ok";
	}

	@Override
	public void run(ApplicationArguments args) throws Exception {
		
		//consumer.start();
	}
}
