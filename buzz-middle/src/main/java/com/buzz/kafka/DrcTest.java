package com.buzz.kafka;

import com.alibaba.fastjson.JSON;
import com.buzz.util.FormatUtils;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.junit.Test;

import java.time.Duration;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

/**
 *
 * 订阅binlog详见CanalServerTest
 * <AUTHOR>
 * @description
 **/
@Slf4j
public class DrcTest {

    @Test
    public void testConsume_DRC() {
        String topic = "db.wac_platform.config_entry";
        KafkaConsumer<String, byte[]> autoKafkaConsumer = KafkaClientBuilder.builder()
                .autoCommit(false)
                .topic(topic)
                .partition(0)
                .offset(343270)
                .groupId("zion-neo.1")
                .maxPoll(2)
                .env(KafkaClientBuilder.Env.DRC)
                .buildKafkaConsumer();
        while (true) {
            ConsumerRecords<String, byte[]> records = autoKafkaConsumer.poll(Duration.ofMillis(2000));
            log.info("poll records size:" + records.count());

            Iterator<ConsumerRecord<String, byte[]>> iterator = records.iterator();
            while (iterator.hasNext()) {

                ConsumerRecord<String, byte[]> record = iterator.next();
                extractBinlog(record.offset(), record.key(), new String(record.value()));


            }

        }
    }

    private void extractBinlog(long offset, String key, String value) {
        //keys: binlogTime，currentTime，event_type, binlog_time, tbl_idx, 列名
        //values: event_type，binlogTime，数据
        String[] keys = key.split(COL_SEPARATOR);
        //切分行
        String[] rows = value.split(ROW_SEPARATOR);
        log.info("================================offset:{}, keys:{}",offset,keys[2]);
        for (int i = 0; i < rows.length; i++) {
            //切分列
            String[] cols = rows[i].split(COL_SEPARATOR);
            Row row = new Row();
            row.binlogType = cols[0];
            row.binlogTime = FormatUtils.toDateTimeString(Long.parseLong(cols[1])) ;
            row.columnValues = convertMap(keys, cols);
            log.info(row.binlogType+"\t" + JSON.toJSONString(row.columnValues));
        }
        log.info("========================================================");
    }

    private Map<String, String> convertMap(String[] keys, String[] cols) {
        Map<String, String> map = new HashMap<>();
        //跳过前 4 列，因为是binlogTime，currentTime，event_type, binlog_time, tbl_idx,
        for (int i = 4; i < keys.length; ++i) {
            String key = keys[i];
            String value = cols[i - 2];
            if ("N/A".equals(value)) {
                value = null;
            }
            map.put(key, value);
        }
        return map;
    }

    private String ROW_SEPARATOR = "\u0002";
    private String COL_SEPARATOR = "\u0001";

    @Data
    public static class Row {
        private String binlogTime;
        private String binlogType;
        private Map<String, String> columnValues;
    }
}
