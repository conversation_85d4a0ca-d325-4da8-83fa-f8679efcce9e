package com.buzz.kafka;

import com.buzz.kafka.KafkaClientBuilder.Env;
import com.buzz.util.FormatUtils;
import com.buzz.util.StopWatch;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.consumer.OffsetAndTimestamp;
import org.apache.kafka.common.TopicPartition;
import org.junit.Test;

import java.io.IOException;
import java.time.Duration;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * [Optimizing Kafka
 * consumers](https://strimzi.io/blog/2021/01/07/consumer-tuning/)
 * <p>
 * [Kafka: The Definitive Guide-Kafka Consumers: Reading Data from
 * Kafka](https://www.oreilly.com/library/view/kafka-the-definitive/9781491936153/ch04.html)
 * <p>
 * 用法可以参考：https://kafka.apache.org/28/javadoc/org/apache/kafka/clients/consumer/KafkaConsumer.html
 */
@Slf4j
public class KafkaConsumerTest {

    private void printResult(ConsumerRecords<String, byte[]> records, String topic) {
        log.info("poll records size:" + records.count());
        Iterator<ConsumerRecord<String, byte[]>> iterator = records.records(topic).iterator();
        while (iterator.hasNext()) {
            // maxPollRecords
            ConsumerRecord<String, byte[]> record = iterator.next();
            long latency = System.currentTimeMillis() - record.timestamp();
            log.info(record.topic() + "\t partition:"
                    + record.partition() + "\t offset:" + record.offset() + "\t latency: " + latency + "ms");
        }
    }

    private void printResult2(ConsumerRecords<String, byte[]> records, String topic) {
        Iterator<ConsumerRecord<String, byte[]>> iterator = records.records(topic).iterator();
        while (iterator.hasNext()) {
            // maxPollRecords
            ConsumerRecord<String, byte[]> record = iterator.next();
            log.info(record.topic() + "\t partition:"
                    + record.partition() + "\t offset:" + record.offset() + "\t time: " + record.timestamp());
        }
    }

    /**
     * 测试 subscribe 模式
     */
    @Test
    public void testSubscribe() throws InterruptedException {
        String topic = "bairen.test.2000";
        KafkaConsumer<String, byte[]> consumer = KafkaClientBuilder.builder()
                .env(Env.TEST)
                .groupId("bairen.test")
                .topic(topic)
                .buildKafkaConsumer();
        consumer.subscribe(Collections.singletonList(topic));

        while (true) {
            ConsumerRecords<String, byte[]> records = consumer.poll(Duration.ofMillis(500));
            Thread.sleep(3000);
            if (records.count() == 0) {
                continue;
            }
            Iterator<ConsumerRecord<String, byte[]>> iterator = records.records(topic).iterator();
            log.info("fetch {} data! offset={}",records.count(),iterator.next().offset());
            consumer.commitSync(); //如果不提交下次还会从重复消费
            log.info("commited");

            //printResult(records, topic);
        }
    }

    /**
     * 测试 assign 模式
     * 如果没有配置 groupId，则KafkaConsumer内部不会初始化coordinator
     */
    @Test
    public void testAssign() {
        String topic = "manyou.test";
        int offset = 1277807;
        KafkaConsumer<String, byte[]> consumer = KafkaClientBuilder.builder()
                .offset(offset)
                .env(Env.TEST)
                .maxPoll(50)
                .buildKafkaConsumer();
        TopicPartition topicPartition = new TopicPartition(topic, 0);
        consumer.assign(Collections.singletonList(topicPartition));
        consumer.seek(topicPartition, offset);

        while (true) {
            ConsumerRecords<String, byte[]> records = consumer.poll(Duration.ofMillis(6000));
            printResult(records, topic);
        }
    }

    /**
     * 单个offset
     */
    @Test
    public void testAssignOffset() {
        String topic = "wc.fund.cfk.msg";
        int offset = 4320;
        KafkaConsumer<String, byte[]> consumer = KafkaClientBuilder.builder()
                .offset(offset)
                .env(Env.TEST)
                .maxPoll(2)
                .buildKafkaConsumer();
        TopicPartition topicPartition = new TopicPartition(topic, 0);
        consumer.assign(Collections.singletonList(topicPartition));
        consumer.seek(topicPartition, offset);

        while (true) {
            ConsumerRecords<String, byte[]> records = consumer.poll(Duration.ofMillis(200));
            printResult2(records, topic);
            if (records.count() > 0) {
                break;
            }
        }
    }

    /**
     *测试offsetsForTimes()
     *
     */
    @Test
    public void testOffsetsForTimestamp() {
        String topic = "wc.fund.cfk.msg";
        KafkaConsumer<String, byte[]> consumer = KafkaClientBuilder.builder()
                .offset(0)
                .env(Env.TEST)
                .buildKafkaConsumer();

        long time = FormatUtils.parseDateTime("2023-06-15 17:18:48").getTime();
        Map<TopicPartition, Long> timestampsToSearch = new HashMap();
        timestampsToSearch.put(new TopicPartition(topic, 0), time);
        Map<TopicPartition, OffsetAndTimestamp> result = consumer.offsetsForTimes(timestampsToSearch);
        System.out.println(time + " \t " + result.size());
        result.entrySet().stream().forEach((entry) -> {
            System.out.println(entry.getKey() + "\t" + entry.getValue());
        });
    }

    /**
     * 查询topic offset区间
     */
    @Test
    public void testGetBeginningOffset() {
        String topic = "bairen.test.101";
        KafkaConsumer<String, byte[]> consumer = KafkaClientBuilder.builder()
                .offset(0)
                .env(Env.LOG)
                .buildKafkaConsumer();


        TopicPartition topicPartition = new TopicPartition(topic, 0);
        Map<TopicPartition, Long> map = consumer.beginningOffsets(Collections.singletonList(topicPartition));
        System.out.println("beginningOffsets:" + map);

        map = consumer.endOffsets(Collections.singletonList(topicPartition));
        System.out.println("endOffsets:" + map);

    }

    /**
     * 查询topic offset区间
     */
    @Test
    public void testGetEndOffset() {
        String topic = "manyou.test";
        KafkaConsumer<String, byte[]> consumer = KafkaClientBuilder.builder()
                .offset(0)
                .env(Env.TEST)
                .buildKafkaConsumer();
        TopicPartition tp = new TopicPartition(topic, 0);
        for (int i = 0; i < 100; ++i) {
            StopWatch watch = StopWatch.create().start();
            long end = consumer.endOffsets(Collections.singletonList(tp)).get(tp);
            watch.stop().trigger(0, (cost) -> {
                log.info("offset:{} cost:{}", end, cost);
            });
        }

    }

    /**
     * 封装 MessageConsumer
     */
    @Test
    public void testMessageConsumer() {

        String topic = "bairen.test.1";
        String group = "bairen-test-1";
        MessageConsumer messageConsumer = KafkaClientBuilder.builder()
                .env(Env.LOG)
                .topic(topic)
                .groupId(group)
                .buildMessageConsumer();
        messageConsumer.start();
        messageConsumer.await();
    }


    /**
     * thread pool
     */
    @Test
    public void testMessageConsumerInThreadPool() throws IOException {
        int taskNum = 10;
        ExecutorService executorService = Executors.newFixedThreadPool(taskNum);
        List<String> list = IntStream.range(0, 500)
                .mapToObj(i -> "bairen.test." + i)
                .collect(Collectors.toList());
        Topics topics = new Topics(list);
        for (int i = 0; i < taskNum; ++i) {
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    KafkaConsumer<String, byte[]> consumer = KafkaClientBuilder.builder()
                            .env(Env.LOG)
                            .buildKafkaConsumer();
                    while (true) {
                        String topic = topics.getTopic();
                        log.info("pull {}", topic);
                        TopicPartition topicPartition = new TopicPartition(topic, 0);
                        consumer.assign(Collections.singletonList(topicPartition));
                        consumer.seek(topicPartition, 0);
                        ConsumerRecords<String, byte[]> records = consumer.poll(Duration.ofMillis(200));
                        if (records.count() > 0) {
                            printResult(records, topic);
                        }
                    }
                }
            });
        }
        System.in.read();
    }


    public static class Topics {

        private AtomicInteger counter = new AtomicInteger();
        private List<String> topics;
        private int size;

        public Topics(List<String> topics) {
            this.topics = topics;
            this.size = topics.size();
        }

        public String getTopic() {
            int index = Math.abs(counter.incrementAndGet()) % size;
            return topics.get(index);
        }

    }


    /**
     * 测试拉取抛出 java.nio.channels.ClosedByInterruptException:
     *
     * @throws IOException
     */
    @Test
    public void testTimeout() throws IOException {
        ExecutorService es = Executors.newFixedThreadPool(1);
        Future<Object> future = es.submit(() -> {
            String topic = "bairen.test.1";
            KafkaConsumer<String, byte[]> kafkaConsumer = KafkaClientBuilder.builder().buildKafkaConsumer();
            kafkaConsumer.subscribe(Collections.singletonList(topic));
            printResult(kafkaConsumer.poll(Duration.ofMillis(1000)), topic);
            return null;
        });

        try {
            future.get(300, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        } catch (TimeoutException e) {
            log.error("timeout", e);
            future.cancel(true);
        }

        System.in.read();
    }


}
