package com.buzz.kafka;

import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.CreateTopicsResult;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.admin.NewTopic;
import org.junit.Test;

import java.util.Arrays;
import java.util.Set;

/**
 * https://kafka.apache.org/28/javadoc/index.html?org/apache/kafka/clients/admin/Admin.html
 *
 * <AUTHOR>
 */
public class KafkaAdminTest {
    AdminClient client = KafkaClientBuilder.builder().buildAdminClient();
    String TOPIC_NAME = "qianniao-test";


    @Test
    public void testCreate() throws Exception {
        String topic = TOPIC_NAME;
        CreateTopicsResult topicsResult = client.createTopics(Arrays.asList(new NewTopic(topic, 1, (short) 1)));
        topicsResult.all().get();
    }

    @Test
    public void testList() throws Exception {
        ListTopicsResult topicsResult = client.listTopics();
        Set<String> s = topicsResult.names().get();
        System.out.println("size:" + s.size());
        System.out.println(s);

     
    }


}
