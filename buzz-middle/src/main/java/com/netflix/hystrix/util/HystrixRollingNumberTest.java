package com.netflix.hystrix.util;

import java.util.Random;

import com.netflix.hystrix.util.HystrixRollingNumber;
import com.netflix.hystrix.util.HystrixRollingNumberEvent;

/*
 * We could in theory have 2 threads addBucket concurrently and this compound operation would interleave.
 * <p>
 * This should NOT happen since getCurrentBucket is supposed to be executed by a single thread.
 * <p>
 * If it does happen, it's not a huge deal as incrementTail() will be protected by compareAndSet and one of the two addBucket calls will succeed with one of the Buckets.
 * <p>
 * In either case, a single Bucket will be returned as "last" and data loss should not occur and everything keeps in sync for head/tail.
 * <p>
 * Also, it's fine to set it before incrementTail because nothing else should be referencing that index position until incrementTail occurs.
 */
public class HystrixRollingNumberTest {

	public static void main(String[] args) throws InterruptedException {

		HystrixRollingNumber counter = new HystrixRollingNumber(8 * 1000, 8);

		int i = 0;
		Random rd = new Random();
		while (true) {
			counter.increment(HystrixRollingNumberEvent.SUCCESS);// 事件+增量
			if (i % 2 == 0) {
				counter.increment(HystrixRollingNumberEvent.TIMEOUT);
			}
			if (i++ > 1000) {
				System.out.println(counter.buckets.size()
						+ "\t" + counter.getRollingSum(HystrixRollingNumberEvent.SUCCESS)
						+ " : " + counter.getRollingSum(HystrixRollingNumberEvent.SUCCESS) / counter.buckets.size()
						+ "(qps)");
				i = 0;
				Thread.sleep(rd.nextInt(1000));
			}

		}
	}
}
