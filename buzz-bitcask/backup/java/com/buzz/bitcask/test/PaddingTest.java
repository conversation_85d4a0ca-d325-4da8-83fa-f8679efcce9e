package com.buzz.bitcask.test;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;

public class PaddingTest {

	private static final ByteBuffer fill = ByteBuffer.allocateDirect(1);
	
	private long currentSize;
	
	private static long preAllocSize = 1024*1024;

	public long padFile(FileChannel fileChannel) throws IOException {
		long newFileSize = calculateFileSizeWithPadding(fileChannel.position(), currentSize, preAllocSize);
		if (currentSize != newFileSize) {
            fileChannel.write((ByteBuffer) fill.position(0), newFileSize - fill.remaining());
            currentSize = newFileSize;
        }
        return currentSize;
	}

	/**
	 * zk写入日志会每次append都会检测是否需要 
	 * 
	 * 使用填充计算新文件大小。 
	 * 如果当前文件位置足够接近（小于 4K）文件末尾并且 preAllocSize > 0，才会返回一个新的大小。
	 * 填充当前文件以将其大小增加到大于当前大小和位置的 preAllocSize 的下一个倍数，假设当前位置是100，preAllocSize=60，则下次增加到160
	 * @param position
	 * @param fileSize
	 * @param preAllocSize
	 * @return
	 */
	public long calculateFileSizeWithPadding(long position, long fileSize, long preAllocSize) {
		//如果 position 和fileSize 之间的距离小于4k，则需要padding
		if (preAllocSize > 0 & position + 4096 >= fileSize) {
			//如果位置已经超过文件大小
			if (position > fileSize) {
				fileSize = position + preAllocSize;
				fileSize = fileSize - (fileSize % preAllocSize);
			} else {
				fileSize += preAllocSize;
			}

		}
		return fileSize;
	}

	public static void main(String[] args) throws Exception {
		PaddingTest test = new PaddingTest();
		System.out.println(test.calculateFileSizeWithPadding(10, 100, 60));
		System.out.println(test.calculateFileSizeWithPadding(110,100, 60));

		FileChannel fileChannel = new FileOutputStream(new File("/tmp/1.log")).getChannel();
		test.padFile(fileChannel);
		
//		ByteBuffer fill = ByteBuffer.allocate(1);
//		ByteBuffer buffer = (ByteBuffer)fill.position(0);
//		System.out.println(fill.remaining());
//		fileChannel.write(buffer, 1*1024*1024L);
		
//		byte[] buf = new byte[128];
//		int len = 0;
//		while(len!=-1) {
//			len=in.read(buf);
//			System.out.println(n+","+buf[0]+","+buf[1]);
//		};
		
	}
}
