package com.buzz.bitcask.test;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

import com.buzz.bitcask.DataEntry;
import com.buzz.bitcask.Util;
import com.buzz.bitcask.codec.BinaryInputArchive;
import com.buzz.bitcask.codec.BinaryOutputArchive;
import com.buzz.bitcask.codec.ByteBufferOutputArchive;
import com.buzz.bitcask.codec.InputArchive;
import com.buzz.bitcask.codec.OutputArchive;

public class FileChannelTest {

	public void seekRead(int pos) throws Exception {
		File file = new File("/tmp/test2.txt");
		RandomAccessFile raf = new RandomAccessFile(file, "rw");

		raf.seek(pos);

		byte[] buf = new byte[4];
		raf.readFully(buf);

	}

	void randomWrite() throws IOException {

		File file = new File("/tmp/test2.txt");
		RandomAccessFile raf = new RandomAccessFile(file, "rw");
//		raf.seek(file.length());

		System.out.println(raf.getFilePointer());
		FileChannel channel = raf.getChannel();
		channel.position(file.length());

		System.out.println("begin:" + channel.position() + "\t" + channel.size() + "\t" + file.length());
		for (int i = 0; i < 10; ++i) {
			String data = "No description, website, or topics provided.";
			ByteBuffer bf = ByteBuffer.allocate(128);
			bf.putLong(System.currentTimeMillis());
			bf.put(data.getBytes());
			bf.flip();
			channel.write(bf);
			System.out.println(channel.position());
		}

		raf.close();

	}

	void seqWrite() throws IOException {
		FileChannel channel = FileChannel.open(Paths.get("/tmp/test2.txt"), StandardOpenOption.APPEND);
		System.out.println("begin:" + channel.position());
		for (int i = 0; i < 10; ++i) {
			String data = "No description, website, or topics provided.";
			ByteBuffer bf = ByteBuffer.allocate(128);
			bf.putLong(System.currentTimeMillis());
			bf.put(data.getBytes());
			bf.flip();
			channel.write(bf);
			System.out.println(channel.position());
		}
	}

	static byte[] test_data_1k = get1kValue();
	
	static byte[] test_data_4k = get4kValue();

	private static byte[] get1kValue() {
		String str =
				"abcedfghigkligklimlomlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjaklkldsjafdds1sfklabc"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl"
				+"abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl";
		return str.getBytes();
	}
	
	private static byte[] get4kValue() {
		byte[] ret = new byte[test_data_1k.length*4];
		for(int i=0;i<4;++i) {
			System.arraycopy(test_data_1k, 0, ret, test_data_1k.length*i, test_data_1k.length);
		}
		return ret;
	}

	static void writeDataEntry() throws IOException {
		String key = "wup-abc-key";

//		byte[] value = "abcedfghigklimlokoadtkldsjafdds1sfklabcedfghigklimlokoadtkldsjakl".getBytes();
		byte[] value = test_data_1k;
		String path = "/data/tmp/test2.txt";
		File file = new File(path);
		if (!file.exists()) {
			file.createNewFile();
		}
		FileChannel channel = FileChannel.open(Paths.get(path), StandardOpenOption.APPEND);
		
		long begin = System.currentTimeMillis();
		for (int i = 0; i < 100000; ++i) {
			DataEntry dataEntry = new DataEntry();
			dataEntry.setKey(key);
			dataEntry.setKeySize(key.getBytes().length);
			dataEntry.setValue(value);
			dataEntry.setValueSize(value.length);
			dataEntry.setTimestamp(Util.getCurrentTimestamp());
			dataEntry.setCrc32(1L);//todo
			ByteBuffer buffer = ByteBuffer.allocate(dataEntry.getObjectSize());
			OutputArchive out = ByteBufferOutputArchive.getArchive(buffer);
			dataEntry.marshal(out);
			buffer.flip();
			channel.write(buffer);
		}
//		long end = System.currentTimeMillis();
//		System.out.println("cost:" + (end - begin) + "ms");
		channel.force(true);
		long end2 = System.currentTimeMillis();
		System.out.println("force cost:" + (end2 - begin) + "ms");
		channel.close();

	}

	static void readDataEntry(int pos) throws IOException {
		File file = new File("/tmp/test2.txt");
		RandomAccessFile raf = new RandomAccessFile(file, "rw");
		raf.seek(pos);

		InputArchive in = BinaryInputArchive.getFileArchive(raf);
		DataEntry dataEntry = new DataEntry();
		dataEntry.unmarshal(in);

		System.out.println(dataEntry.getKey() + "\t" + new String(dataEntry.getValue()));
	}

	public static void main(String[] args) throws IOException {
		for(int i=0;i<1;++i) {
			writeDataEntry();
		}
//		writeDataEntry();
//		writeDataEntry();
//		writeDataEntry();
//		writeDataEntry();
//		readDataEntry(277);
	}
}
