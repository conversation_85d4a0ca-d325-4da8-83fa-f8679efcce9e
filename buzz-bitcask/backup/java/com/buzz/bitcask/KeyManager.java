package com.buzz.bitcask;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class KeyManager {

	private Map<String, KeyEntry> cache = new ConcurrentHashMap<String, KeyEntry>();

	public void put(String key, int fileId, DataEntry dataEntry, int pos) {
		KeyEntry keyEntry = new KeyEntry();
		keyEntry.setFileId(fileId);
		keyEntry.setValSize(dataEntry.getValueSize());
		keyEntry.setValPos(pos);
		keyEntry.setTimestamp(dataEntry.getTimestamp());
		cache.put(key, keyEntry);
	}

	public KeyEntry get(String key) {
		return cache.get(key);
	}

	public void del(String key) {
		cache.remove(key);
	}

}
