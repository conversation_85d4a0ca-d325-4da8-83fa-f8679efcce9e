package com.buzz.bitcask;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

public class FileChannelTest {

//	File file = new File("/tmp/test2.txt");
//	RandomAccessFile raf = new RandomAccessFile(file, "rw");
//	FileChannel	fileChannel = FileChannel.open(Paths.get("/tmp/test2.txt"), StandardOpenOption.APPEND);
//	fileChannel.position()
//	
//	FileChannel channel = raf.getChannel();
//	for (int i = 0; i < 10; ++i) {
//		System.out.println(channel.position());
//		String data = "No description, website, or topics provided.";
//		ByteBuffer bf = ByteBuffer.allocate(data.getBytes().length);
//		bf.put(data.getBytes());
//		bf.flip();
////			ByteBuffer bf = ByteBuffer.wrap(data.getBytes());
//		channel.write(bf);
////			System.out.println(len);
//	}
//	channel.close();
//	raf.close();
	
	
	public static void randomAccess() throws IOException {
		
		File file = new File("/tmp/test2.txt");
		RandomAccessFile raf = new RandomAccessFile(file, "rw");
//		raf.seek(file.length());
		
		System.out.println(raf.getFilePointer());
		FileChannel channel =raf.getChannel();
		channel.position(file.length());
		
		System.out.println("begin:" + channel.position()+"\t"+channel.size()+"\t"+file.length());
		for (int i = 0; i < 10; ++i) {
			String data = "No description, website, or topics provided.";
			ByteBuffer bf = ByteBuffer.allocate(128);
			bf.putLong(System.currentTimeMillis());
			bf.put(data.getBytes());
			bf.flip();
			channel.write(bf);
			System.out.println(channel.position());
		}
		
		raf.close();
		
	}
	
	static void write() throws IOException {
		FileChannel channel = FileChannel.open(Paths.get("/tmp/test2.txt"), StandardOpenOption.APPEND);
		System.out.println("begin:" + channel.position());
		for (int i = 0; i < 10; ++i) {
			String data = "No description, website, or topics provided.";
			ByteBuffer bf = ByteBuffer.allocate(128);
			bf.putLong(System.currentTimeMillis());
			bf.put(data.getBytes());
			bf.flip();
			channel.write(bf);
			System.out.println(channel.position());
		}
	}
	public static void main(String[] args) throws IOException {
		
		randomAccess();
		

		
	}
}
