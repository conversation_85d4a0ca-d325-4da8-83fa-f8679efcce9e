package com.buzz.bitcask;

import java.io.IOException;
import java.util.zip.CRC32;

import com.buzz.bitcask.codec.InputArchive;
import com.buzz.bitcask.codec.OutputArchive;
import com.buzz.bitcask.codec.Recordable;

import lombok.Data;

/**
 * <p>
 * Data Format:
 * </p>
 * 
 * <pre>
 * |crc32|ts|key_sz|val_size|key|value
 * <pre>
 * 
 * <AUTHOR>
 *
 */
@Data
public class DataEntry implements Recordable {

	private long crc32;
	private long timestamp;
	private int keySize;
	private int valueSize;
	private String key;
	private byte[] value;

	
	@Override
	public void marshal(OutputArchive out) throws IOException {
		out.writeLong(crc32); //8
		out.writeLong(timestamp);//8
		out.writeInt(keySize);//4
		out.writeInt(valueSize);//4
		out.writeBuffer(key.getBytes());
		out.writeBuffer(value);
	}

	@Override
	public void unmarshal(InputArchive in) throws IOException {
		crc32 = in.readLong();
		timestamp = in.readLong();
		keySize = in.readInt();
		valueSize = in.readInt();
		key = new String(in.readBuffer(keySize));
		value = in.readBuffer(valueSize);
	}

	@Override
	public int getObjectSize() {
		int size =  24+keySize+valueSize;
		return size;
	}

	public static void main(String[] args) {
		CRC32 crc32 = new CRC32();
		crc32.update("hello world".getBytes());
		long val = crc32.getValue();
		System.out.println(val);
	}
}
