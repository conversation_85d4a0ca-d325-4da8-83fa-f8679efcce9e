package com.buzz.bitcask;

import java.io.IOException;
import java.util.Map;

public class DataManager {
	
	private int lastFileId;
	
	private String dataDir; 
	
	private DataFile activeDataFile;

	private Map<Integer, DataFile> readOnlyDataFiles;

	public int put(String key, byte[] value) throws IOException {

		DataEntry dataEntry = new DataEntry();
		dataEntry.setKeySize(key.getBytes().length);
		dataEntry.setKey(key);
		dataEntry.setValue(value);
		dataEntry.setKeySize(value.length);
		dataEntry.setTimestamp(Util.getCurrentTimestamp());
		dataEntry.setCrc32(1L);//todo
		int pos = activeDataFile.append(dataEntry);

		if (pos >= Util.MAX_FILE_BYTE_SIZE) {
			activeDataFile.close();
			activeDataFile = createNewDataFile();
		}
		return pos;
	}

	public DataFile createNewDataFile() throws IOException {
		int fileId = this.lastFileId;
		++fileId;
		DataFile dataFile = new DataFile(dataDir,fileId);
		dataFile.open();
		this.lastFileId = fileId;
		return dataFile;
	}

	public DataEntry get(KeyEntry keyEntry) {
		int fileId = keyEntry.getFileId();
		DataFile dataFile = readOnlyDataFiles.get(fileId);
		if (dataFile == null) {
			dataFile = (activeDataFile.getFileId() == fileId) ? activeDataFile : null;
		}
		if (dataFile == null) {
			return null;
		}
		//TODO 未实现，先编译通过
		return null;

	}

	public void del(String key) {

	}
}
