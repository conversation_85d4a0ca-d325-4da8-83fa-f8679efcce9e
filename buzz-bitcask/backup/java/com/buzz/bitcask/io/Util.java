package com.buzz.bitcask.io;

import java.io.ByteArrayOutputStream;

/**
 * clone from org.apache.zookeeper.server.persistence.Util
 * 
 * <AUTHOR>
 *
 */
public class Util {

//	public static byte[] marshallTxn(Record record) {
//
//		ByteArrayOutputStream baos = new ByteArrayOutputStream();
//		OutputArchive boa = BinaryOutputArchive.getArchive(baos);
//		record.serialize(boa);
//		return baos.toByteArray();
//	};
}
