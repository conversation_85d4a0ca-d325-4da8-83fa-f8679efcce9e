package com.buzz.bitcask.codec;

import java.io.IOException;
import java.nio.ByteBuffer;

public class ByteBufferOutputArchive implements OutputArchive{

	ByteBuffer buffer;
	
    public static OutputArchive getArchive(ByteBuffer buffer) {
        return new ByteBufferOutputArchive(buffer);
    }
	
	ByteBufferOutputArchive(ByteBuffer buffer) {
		super();
		this.buffer = buffer;
	}

	@Override
	public void writeInt(int i) throws IOException {
		buffer.putInt(i);
	}

	@Override
	public void writeLong(long l) throws IOException {
		buffer.putLong(l);
	}

	@Override
	public void writeBuffer(byte[] buf) throws IOException {
		buffer.put(buf);
	}

}
