package com.buzz.bitcask.codec;

import java.io.DataInput;
import java.io.IOException;
import java.io.RandomAccessFile;

public class BinaryInputArchive implements InputArchive{

	private DataInput input;
	
	public BinaryInputArchive(DataInput input) {
		super();
		this.input = input;
	}

	public static InputArchive getFileArchive(RandomAccessFile file) {
		return new BinaryInputArchive(file);
	}
	
	@Override
	public int readInt() throws IOException {
		return input.readInt();
	}

	@Override
	public long readLong() throws IOException {
		return input.readLong();
	}

	@Override
	public byte[] readBuffer(int len) throws IOException {
		byte[] buf = new byte[len];
		input.readFully(buf);
		return buf;
	}
	
	

}
