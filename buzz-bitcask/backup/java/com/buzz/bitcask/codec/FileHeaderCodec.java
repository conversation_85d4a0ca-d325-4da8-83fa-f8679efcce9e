package com.buzz.bitcask.codec;

import java.nio.ByteBuffer;

import com.buzz.bitcask.FileHeader;

/**
 * |magic|version|fileId
 *
 */
public class FileHeaderCodec  {

	 
	public byte[] encode(FileHeader obj) {
		ByteBuffer bf = ByteBuffer.allocate(FileHeader.OBJECT_SIZE);
		bf.putInt(obj.getMagic());
		bf.putInt(obj.getVersion());
		bf.putInt(obj.getFileId());
		return bf.array();
	}

	 
	public FileHeader decode(byte[] bytes) {
		ByteBuffer bf = ByteBuffer.wrap(bytes);
		System.out.println(bf.remaining());
		FileHeader header = new FileHeader();
		header.setMagic(bf.getInt());
		header.setVersion(bf.getInt());
		header.setFileId(bf.getInt());
		return header;
	}

	

	public static void main(String[] args) throws Exception {
		
		FileHeader header = new FileHeader();
		header.setFileId(10);
		
		FileHeaderCodec codec = new FileHeaderCodec();
		byte[] bytes = codec.encode(header);
		FileHeader header2 = codec.decode(bytes);
		System.out.println(header2.getFileId()+","+header.getMagic()+","+header.getVersion());
		
	
	}

}
