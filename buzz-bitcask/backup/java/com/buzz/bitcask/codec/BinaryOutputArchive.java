package com.buzz.bitcask.codec;

import java.io.ByteArrayOutputStream;
import java.io.DataOutput;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.RandomAccessFile;

public class BinaryOutputArchive implements OutputArchive {

	private DataOutput output;

	public BinaryOutputArchive(DataOutput output) {
		super();
		this.output = output;
	}

	public OutputArchive getFileArchive(RandomAccessFile file) {
		return new BinaryOutputArchive(file);
	}

	public static OutputArchive getOutputArchive(OutputStream out) {
		return new BinaryOutputArchive(new DataOutputStream(out));
	}

	@Override
	public void writeInt(int i) throws IOException {
		output.writeInt(i);
	}

	@Override
	public void writeLong(long l) throws IOException {
		output.writeLong(l);
	}

	@Override
	public void writeBuffer(byte[] buf) throws IOException {
		output.write(buf);
	}

	public static void main(String[] args) throws IOException {
		ByteArrayOutputStream bos = new ByteArrayOutputStream();
		OutputArchive out = BinaryOutputArchive.getOutputArchive(bos); //new BinaryOutputArchive(bos);
		out.writeInt(999);
		out.writeLong(1024);
		out.writeBuffer("this.is str".getBytes());

	}
}
