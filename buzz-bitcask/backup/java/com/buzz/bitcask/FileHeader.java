package com.buzz.bitcask;

import java.io.IOException;

import com.buzz.bitcask.codec.InputArchive;
import com.buzz.bitcask.codec.OutputArchive;
import com.buzz.bitcask.codec.Recordable;

import lombok.Data;

/**
 * 
 * magic,version,fileId	
 */
@Data
public class FileHeader implements Recordable {

	private static final int DEFAULT_MAGIC = 2048;

	private static final int DEFAULT_VERSION = 1;

	public static final int OBJECT_SIZE = Integer.BYTES * 3;

	private int magic = DEFAULT_MAGIC;

	private int version = DEFAULT_VERSION;

	private int fileId;

	public FileHeader() {
		super();
	}

	public FileHeader(int fileId) {
		super();
		this.fileId = fileId;
	}

	@Override
	public int getObjectSize() {
		return OBJECT_SIZE;
	}

	@Override
	public void marshal(OutputArchive out) throws IOException {
		out.writeInt(magic);
		out.writeInt(version);
		out.writeInt(fileId);
	}

	@Override
	public void unmarshal(InputArchive in) throws IOException {
		this.magic = in.readInt();
		this.version = in.readInt();
		this.fileId = in.readInt();
	}

}
