package com.buzz.bitcask;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

public class DataFile {

	private RandomAccessFile originFile;

	private FileChannel fileWriteChannel;

	private String dataDir;

	private int fileId;

	public DataFile(String dataDir, int fileId) {
		this.dataDir = dataDir;
		this.fileId = fileId;
	}

	public void open() throws IOException {
		String path = dataDir + Util.toHexString(fileId) + ".data";
		File file = new File(path);
		if(!file.exists()) {
			file.createNewFile();
			fileWriteChannel = FileChannel.open(Paths.get(path), StandardOpenOption.APPEND);
			FileHeader header = new FileHeader();
			//TODO 还未实现！为了编译通过
			//fileWriteChannel.write();
		}
		originFile = new RandomAccessFile(new File(path), "rw");
		fileWriteChannel = originFile.getChannel();
	}

	public DataEntry read(int pos, int valSize) {
		return null;
	}

	public synchronized int append(DataEntry data) throws IOException {
		
		//collateRolling
		ByteBuffer bb = ByteBuffer.allocate(24 + data.getKeySize() + data.getValueSize());
		bb.putLong(data.getCrc32());//8
		bb.putLong(data.getTimestamp());//8
		bb.putInt(data.getKeySize());//4
		bb.putInt(data.getValueSize());//4
		bb.put(data.getKey().getBytes());
		bb.put(data.getValue());
		bb.flip();
		int pos = fileWriteChannel.write(bb);
		return pos;
	}

	public void close() throws IOException {
		fileWriteChannel.close();
		originFile.close();
	}

	public int getFileId() {
		return fileId;
	}

	public static void main(String[] args) throws Throwable {

		File file = new File("/tmp/test.txt");
		FileOutputStream out = new FileOutputStream(file, true);

		long ts = System.currentTimeMillis();
		String data = "No description, website, or topics provided.";
		ByteBuffer buffer = ByteBuffer.allocate(1024);
		buffer.putLong(ts);
		buffer.put(data.getBytes());
		out.write(buffer.array());
		out.close();
		// 5120,6144,7168,8192
		System.out.println(file.length());

		RandomAccessFile in = new RandomAccessFile(file, "r");
		in.seek(5120);
		ByteBuffer dest = ByteBuffer.allocate(1024);
		FileChannel fileChannel = in.getChannel();
		fileChannel.read(dest);
		dest.flip();
		System.out.println(dest.getLong());
		byte[] dist = new byte[dest.remaining()];
		dest.get(dist);
		System.out.println(new String(dist));

	}
}
