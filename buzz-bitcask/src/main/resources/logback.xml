<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
        <encoder class="ch.qos.logback.classic.encoder.PatternLayoutEncoder">
            <!--格式化输出：%d表示日期，%thread表示线程名，%-5level：级别从左显示5个字符宽度%msg：日志消息，%n是换行符-->
            <pattern>%date{ISO8601} %-5level [%thread] %logger{32} - %message%n</pattern>
        </encoder>
    </appender>
    <root>
        <level value="INFO"/>
        <appender-ref ref="CONSOLE"/>
    </root>
</configuration>