package com.buzz.bitcask.util;

public class Constant {

    public static String BASE_DIR = "/data/tmp/bitcask";

    public static int BUCKET_SIZE = 4;

    /**
     * 数据文件大小 1g
     */
    public static long ENTRY_FILE_SIZE = 1 << 30;

    /**
     * hitBlock大小 1k
     */
    public static int HIT_BLOCK_SIZE = 1 << 10;

    /**
     * 1KB
     */
    public static int HIT_ACCUMULATE_SIZE = 1 << 10;

    /**
     * 32KB 性能最好，200万条数据耗时5290ms
     */
    public static int ENTRY_ACCUMULATE_SIZE = 32 << 10;

    /**
     * 累加器暂存时间
     */
    public static int ACCUMULATOR_LINGER_MS = 3000;

}
