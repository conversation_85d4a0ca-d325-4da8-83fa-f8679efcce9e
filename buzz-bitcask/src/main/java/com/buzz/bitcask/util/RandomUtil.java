package com.buzz.bitcask.util;

import java.util.Random;

public class RandomUtil {

    private static final char[] letters = {
            'a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k',
            'l', 'm', 'n', 'o', 'p', 'q', 'r', 's', 't', 'u', 'v', 'w', 'x', 'y', 'z',
            '0', '1', '2', '3', '4', '5', '6', '7', '8', '9'};

    private static final Random innerRandom = new Random();

    public static String generate(int len) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < len; ++i) {
            sb.append(letters[innerRandom.nextInt(36)]);
        }
        return sb.toString();
    }

    public static int get(int bound) {
        return innerRandom.nextInt(bound);
    }
    public static void main(String[] args) {
        System.out.println(RandomUtil.generate(8));
        System.out.println(RandomUtil.generate(8));
    }

}
