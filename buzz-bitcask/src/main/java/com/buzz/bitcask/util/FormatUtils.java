package com.buzz.bitcask.util;

import java.text.SimpleDateFormat;
import java.util.Date;

public class FormatUtils {

    /*
     * yyyy-MM-dd HH:mm:ss
     */
    private static final ThreadLocal<SimpleDateFormat> dateTimeFormatter = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
            format.setLenient(false);
            return format;
        }
    };
    public static String toDateTimeString(long millis) throws IllegalArgumentException {
        return dateTimeFormatter.get().format(new Date(millis));
    }


    public static void main(String[] args) {
        System.out.println( FormatUtils.toDateTimeString(System.currentTimeMillis()));
    }


}
