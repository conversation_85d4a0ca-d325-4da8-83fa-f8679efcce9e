package com.buzz.bitcask;

import com.buzz.bitcask.util.Constant;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.BufferUnderflowException;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.HashMap;
import java.util.Map;

/**
 * header+key_size+version+ts+key_value+data_fp
 */
@Slf4j
public class EntryHitFile {

    private String baseDir;
    private String bucket;
    private File hitFile;
    private FileChannel hitFileChannel;

    public EntryHitFile(String baseDir, String bucket) {
        this.baseDir = baseDir;
        this.bucket = bucket;
    }

    public void init() throws IOException {
        File bucketDir = new File(baseDir, bucket);
        if (!bucketDir.exists()) {
            bucketDir.mkdirs();
        }
        hitFile = new File(bucketDir, "entry.hit");
        if (!hitFile.exists()) {
            hitFile.createNewFile();
        }
        hitFileChannel = new RandomAccessFile(hitFile, "rw").getChannel();
    }

    public Map<String, EntryHit> load() throws IOException {
        Map<String, EntryHit> entryHitMap = new HashMap<>();
        FileChannel fileChannel;
        long fileLength;
        File backup = new File(hitFile.getPath() + ".backup");
        if (backup.exists()) {//优先从backup文件中恢复
            fileChannel = new RandomAccessFile(backup, "rw").getChannel();
            fileLength = backup.length();
        } else {
            fileChannel = hitFileChannel;
            fileLength = hitFile.length();
        }
        if (fileLength < 1) {//如果没有数据
            return entryHitMap;
        }

        ByteBuffer blockBuffer = ByteBuffer.allocate(Constant.HIT_BLOCK_SIZE);
        int totalReadBytes = 0;//总的读取字节数量
        int readBytes = 0;//单次读取的字节数量
        boolean needDump = false;
        while (totalReadBytes <= fileLength && (readBytes = fileChannel.read(blockBuffer, totalReadBytes)) > 0) {
            blockBuffer.flip();
            while (blockBuffer.hasRemaining()) {
                try {
                    EntryHit entryHit = new EntryHit();
                    long ts = blockBuffer.getLong();//ts
                    int keySize = blockBuffer.getInt();//KeySize;
                    byte[] keyBytes = new byte[keySize];
                    blockBuffer.get(keyBytes);//keyValue
                    long entryFP = blockBuffer.getLong();//entryFP
                    if (entryFP == -1) {//已删除标识
                        needDump = true;
                        continue;
                    }
                    String key = new String(keyBytes);
                    entryHit.setTimestamp(ts);
                    entryHit.setBlockFp(entryFP);
                    totalReadBytes += bufferSize(keySize);
                    if (entryHitMap.put(key, entryHit) != null) {//说明修改过
                        needDump = true;
                    }
                    //log.info("load key={} totalReadBytes={}", key, totalReadBytes);
                } catch (BufferUnderflowException e) {
                    //blockBuffer已经到尾了
                    blockBuffer.clear();
                    break;
                }
            }
        }

        if (needDump) {
            dump(entryHitMap);
        }
        log.info("load keys {} on bucket {}", entryHitMap.size(), bucket);
        return entryHitMap;
    }


    public void dump(Map<String, EntryHit> entryHitMap) throws IOException {
        String path = hitFile.getPath();
        File backup = new File(path + ".backup");
        hitFile.renameTo(backup);
        init();
        for (Map.Entry<String, EntryHit> entry : entryHitMap.entrySet()) {
            write(entry.getKey(), entry.getValue());
        }
        flush();
        backup.delete();
    }

    private int bufferSize(int keySize) {
        return Long.BYTES + Integer.BYTES + keySize + Long.BYTES;
    }

    public ByteBuffer encode(EntryHit entryHit) {
        byte[] keyBytes = entryHit.getKey().getBytes();
        int keySize = keyBytes.length;
        ByteBuffer buffer = ByteBuffer.allocate(bufferSize(keySize));
        buffer.putLong(entryHit.getTimestamp());
        buffer.putInt(keySize);
        buffer.put(keyBytes);
        buffer.putLong(entryHit.getBlockFp());
        return buffer;
    }

    public void write(String key, EntryHit entryHit) throws IOException {
        entryHit.setKey(key);
        ByteBuffer buffer = encode(entryHit);
        buffer.flip();
        hitFileChannel.write(buffer);
    }

    public void flush() throws IOException {
        hitFileChannel.force(true);
    }

    public void close() throws IOException {
        flush();
        hitFileChannel.close();
    }

    public void delete() throws IOException {
        hitFileChannel.close();
        hitFile.delete();
    }

    public void write(ByteBuffer buffer) {
        try {
            buffer.flip();
            hitFileChannel.write(buffer);
        } catch (IOException e) {
            log.error("write file error", e);
        } finally {
            buffer.clear();
        }
    }
}
