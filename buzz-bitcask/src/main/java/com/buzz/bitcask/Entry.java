package com.buzz.bitcask;

import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class Entry {
    private String key;
    private byte[] data;
    private long timestamp;

    public static Entry create(String key, byte[] data) {
        return new Entry(key, data, 0l);
    }

    public static Entry create(String key, byte[] data, long timestamp) {
        return new Entry(key, data, timestamp);
    }

    @Override
    public String toString() {
        return "Entry(key=" + key + ",data=" + new String(data) + ",timestamp=" + timestamp + ")";
    }
}
