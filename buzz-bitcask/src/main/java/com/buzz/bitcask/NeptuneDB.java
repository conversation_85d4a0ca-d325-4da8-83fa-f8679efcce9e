package com.buzz.bitcask;

import com.buzz.bitcask.util.CRC16;
import com.buzz.bitcask.util.Constant;

import java.io.IOException;

public class NeptuneDB {

    private NeptuneWorker[] workers;

    public NeptuneDB() {
        workers = new NeptuneWorker[Constant.BUCKET_SIZE];
    }

    public void init(boolean loadHit) throws IOException {
        for (int i = 0; i < Constant.BUCKET_SIZE; ++i) {
            workers[i] = new NeptuneWorker(Constant.BASE_DIR, i);
            workers[i].start(loadHit);
        }
    }

    public void put(String key, byte[] value) {
        int bucket = hash(key);
        if (!workers[bucket].put(key, value)) {
            throw new IllegalArgumentException("write " + key + " error! worker-" + bucket + "is full!");
        }

    }

    public void delete(String key) {
        int bucket = hash(key);
        workers[bucket].delete(key);
    }

    public Entry get(String key) throws IOException {
        int bucket = hash(key);
        return workers[bucket].get(key);
    }

    public void stop() {
        for (NeptuneWorker worker : workers) {
            worker.stop();
        }
    }

    public void flush() {
        for (NeptuneWorker worker : workers) {
            worker.flush();
        }
    }

    private int hash(String key) {
        return CRC16.getCRC16(key) % Constant.BUCKET_SIZE;
    }


}
