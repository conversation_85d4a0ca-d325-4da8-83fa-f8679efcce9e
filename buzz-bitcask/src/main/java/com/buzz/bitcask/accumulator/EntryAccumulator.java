package com.buzz.bitcask.accumulator;

import com.buzz.bitcask.Entry;
import com.buzz.bitcask.EntryFile;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.function.Consumer;

/**
 * Entry累加器，当commit时会通知HitAccumulator
 */
@Slf4j
public class EntryAccumulator implements Consumer<CommitInfo> {

    private EntryFile entryFile;
    private HitAccumulator hitAccumulator;
    private ByteBufferAccumulator byteBufferAccumulator;

    public EntryAccumulator(int capacity, int lingerMs, EntryFile entryFile, HitAccumulator hitAccumulator) {
        this.entryFile = entryFile;
        this.hitAccumulator = hitAccumulator;
        this.byteBufferAccumulator = new ByteBufferAccumulator("entry", capacity, lingerMs, this);
    }

    /**
     * entry 可以为空，用于触发累加器中过期的数据commit
     *
     * @param entry
     */
    public void append(Entry entry) {
        ByteBuffer buf = entryFile.encode(entry);
        byteBufferAccumulator.append(buf, entry.getKey());
    }

    public void tryCommit(){
        byteBufferAccumulator.tryCommit(false);
    }

    public void flush(){
        byteBufferAccumulator.tryCommit(true);
    }

    public void stop()  {
        flush();
        try {
            entryFile.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    @Override
    public void accept(CommitInfo commitInfo) {
        try {
            long fp = entryFile.write(commitInfo.getAccumulateByteBuffer());
            List<String> keyList = commitInfo.getKeysList();
            //log.info("notify pendingKeyList={} fp={}", commitInfo.getKeysList(), fp);
            hitAccumulator.notify(keyList, fp);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}
