package com.buzz.bitcask.accumulator;

import com.buzz.bitcask.util.SourceLogger;
import lombok.extern.slf4j.Slf4j;

import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.function.Consumer;

/**
 * ByteBuffer累加器
 * 基于文件大小和时间，默认3秒
 * - hitFile:1kb
 * - entryFile:8kb
 */
@Slf4j
public class ByteBufferAccumulator {
    private String name;//用于排查问题
    private Consumer<CommitInfo> persistConsumer;
    private ByteBuffer accumulateByteBuffer;
    private int capacity;
    private int lingerMs;
    private long lastCommitTimestamp;
    private volatile AtomicLong appendCounter = new AtomicLong();
    private volatile List<String> commitObjList = new ArrayList<>();


    public ByteBufferAccumulator(String name, int capacity, int lingerMs, Consumer<CommitInfo> persistConsumer) {
        this.name = name;
        this.capacity = capacity;
        this.lingerMs = lingerMs;
        this.accumulateByteBuffer = ByteBuffer.allocateDirect(capacity);
        this.persistConsumer = persistConsumer;
    }

    public void append(ByteBuffer buffer, String key) {
        tryCommit(false);
        //如果accumulateByteBuffer的容量不足
        if (accumulateByteBuffer.remaining() < buffer.capacity()) {
            commit(System.currentTimeMillis(), "remain=" + accumulateByteBuffer.remaining());
        }
        buffer.flip();
        accumulateByteBuffer.put(buffer);
        appendCounter.incrementAndGet();
        commitObjList.add(key);

        if (name.equals("entry") && Thread.currentThread().getName().equals("neptune-worker-2")) {
            SourceLogger.info(log, "append entry key={}, appendCounter={}, commitObjList-{}", key, appendCounter.get(), commitObjList);
        }
    }

    public void tryCommit(boolean force) {
        long now = System.currentTimeMillis();
        if (lastCommitTimestamp == 0) {
            lastCommitTimestamp = now;
        }
        //超过时间或者force
        if ((now - lastCommitTimestamp) > lingerMs || force) {
            commit(now, "timeout");
        }
    }

    public void commit(long now, String cause) {
        if (appendCounter.get() == 0) {
            return;
        }
        if (this.name.equals("entry") && Thread.currentThread().getName().equals("neptune-worker-2")) {
            SourceLogger.info(log, "commit entry by {} commitObjList={} ,appendCount={}", this.hashCode(), commitObjList, appendCounter.get());
        }
        persistConsumer.accept(new CommitInfo(accumulateByteBuffer, commitObjList));
        lastCommitTimestamp = now;
        appendCounter.set(0);
        commitObjList.clear();
    }


}
