package com.buzz.bitcask.accumulator;

import com.buzz.bitcask.EntryHit;
import com.buzz.bitcask.EntryHitFile;
import com.buzz.bitcask.NeptuneWorker;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Consumer;

/**
 * hit累加器
 */
@Slf4j
public class HitAccumulator implements Consumer<CommitInfo> {

    private Map<String, EntryHit> entryHitPendingMap = new ConcurrentHashMap<>();
    private ByteBufferAccumulator byteBufferAccumulator;
    private EntryHitFile entryHitFile;
    private NeptuneWorker neptuneWorker;

    public HitAccumulator(int capacity, int lingerMs, EntryHitFile entryHitFile, NeptuneWorker neptuneWorker) {
        this.entryHitFile = entryHitFile;
        this.byteBufferAccumulator = new ByteBufferAccumulator("hit", capacity, lingerMs, this);
        this.neptuneWorker = neptuneWorker;
    }

    /**
     * 先暂存在map中，因为这时候EntryHit数据还不完整
     *
     * @param key
     * @param entryHit
     */
    public void addToPendingMap(String key, EntryHit entryHit) {
        entryHitPendingMap.put(key, entryHit);
    }

    /**
     * append，比如delete不需要entry触发
     *
     * @param entryHit
     */
    public void append(EntryHit entryHit) {
        ByteBuffer buffer = entryHitFile.encode(entryHit);
        byteBufferAccumulator.append(buffer, entryHit.getKey());
    }

    public void tryCommit() {
        byteBufferAccumulator.tryCommit(false);
    }

    public void flush(){
        byteBufferAccumulator.tryCommit(true);
    }

    public void stop(){
        flush();
        try {
            entryHitFile.close();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
    /**
     * 当entry被commit时收到通知
     *
     * @param keyList
     * @param fp
     */
    public void notify(List<String> keyList, long fp) {
        for (String key : keyList) {
            EntryHit entryHit = entryHitPendingMap.remove(key);
            if (entryHit == null) {
                log.error("notify entryHit is null,key={}", key);
            }
            entryHit.setBlockFp(fp);
            neptuneWorker.updateHitFp(key, fp);//更新内存中的fp
            byteBufferAccumulator.append(entryHitFile.encode(entryHit), key);//写入到累加器
        }
    }

    @Override
    public void accept(CommitInfo buffer) {
        entryHitFile.write(buffer.getAccumulateByteBuffer());
    }
}
