package com.buzz.bitcask;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class Event {
    private EventType type;
    private Object payload;

    public enum EventType {
        PUT("put"),
        STOP("stop"),
        FLUSH("flush"),
        DELETE("delete");
        private String value;

        EventType(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }
    }
}
