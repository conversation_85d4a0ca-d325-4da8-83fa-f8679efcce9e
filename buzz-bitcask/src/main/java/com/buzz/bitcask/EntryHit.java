package com.buzz.bitcask;

import lombok.Data;

/**
 * key_size+ts+key_value+data_FP
 */
@Data
public class EntryHit {
    private String key;
    private long blockFp = -1;//entry的FilePointer，实际是blockFP
    private long timestamp;

    public EntryHit() {
    }

    public EntryHit(String key,long timestamp) {
        this.key = key;
        this.timestamp = timestamp;
    }

    public EntryHit(long timestamp, long blockFp) {
        this.timestamp = timestamp;
        this.blockFp = blockFp;
    }

    public EntryHit blockFp(long blockFp) {
        this.blockFp = blockFp;
        return this;
    }
}
