package com.buzz.bitcask;

import com.buzz.bitcask.Event.EventType;
import com.buzz.bitcask.accumulator.EntryAccumulator;
import com.buzz.bitcask.accumulator.HitAccumulator;
import com.buzz.bitcask.util.Constant;
import com.buzz.bitcask.util.FastException;
import com.buzz.bitcask.util.SourceLogger;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicBoolean;

import static com.buzz.bitcask.Event.EventType.DELETE;
import static com.buzz.bitcask.Event.EventType.PUT;

@Slf4j
public class NeptuneWorker implements Runnable {

    private static final FastException closeEvent = new FastException("Close NeptuneWorker");

    private int bucket;

    private BlockingQueue<Event> queue = new ArrayBlockingQueue<>(1000);

    private Map<String, EntryHit> entryHitMap = new ConcurrentHashMap<>();

    private EntryHitFile entryHitFile;

    private EntryFile entryFile;

    private AtomicBoolean running = new AtomicBoolean(false);

    private HitAccumulator hitAccumulator;

    private EntryAccumulator entryAccumulator;

    private CountDownLatch stopCountDownLatch = new CountDownLatch(1);

    public NeptuneWorker(String baseDir, int bucket) {
        this.bucket = bucket;
        entryHitFile = new EntryHitFile(baseDir, Integer.toHexString(bucket));
        entryFile = new EntryFile(baseDir, Integer.toHexString(bucket));
        hitAccumulator = new HitAccumulator(Constant.HIT_ACCUMULATE_SIZE, Constant.ACCUMULATOR_LINGER_MS, entryHitFile, this);
        entryAccumulator = new EntryAccumulator(Constant.ENTRY_ACCUMULATE_SIZE, Constant.ACCUMULATOR_LINGER_MS, entryFile, hitAccumulator);
    }

    public void start(boolean loadHit) throws IOException {
        if (running.compareAndSet(false, true)) {
            //初始化entryHitFile和entryFile
            entryHitFile.init();
            entryFile.init();

            //是否loadHit
            if (loadHit) {
                entryHitMap.putAll(entryHitFile.load());
            }
            Thread thread = new Thread(this);
            thread.setName("neptune-worker-" + bucket);
            thread.start();
            //log.info("start neptune-worker-{}", bucket);
        }
    }

    public boolean put(String key, byte[] value) {
        Event event = new Event();
        event.setType(PUT);
        event.setPayload(Entry.create(key, value));
        return publishEvent(event);
    }

    public boolean delete(String key) {
        Event event = new Event();
        event.setType(DELETE);
        event.setPayload(key);
        return publishEvent(event);
    }

    public Entry get(String key) throws IOException {
        EntryHit entryHit = entryHitMap.get(key);
        if (entryHit == null || entryHit.getBlockFp() == -1) {
            return null;
        }
        byte[] data = entryFile.read(key, entryHit.getBlockFp());
        if (data == null) {
            return null;
        }
        return Entry.create(key, data, entryHit.getTimestamp());
    }


    public void updateHitFp(String key, long fp) {
        entryHitMap.computeIfPresent(key, (k, v) -> v.blockFp(fp));
    }

    @Override
    public void run() {
        List<Event> sinkList = new ArrayList();
        while (running.get()) {
            Event event = null;
            try {
                event = queue.poll(1, TimeUnit.SECONDS);
                if (event == null) {
                    flushAccumulator();
                } else {
                    sinkList.add(event);
                    queue.drainTo(sinkList);
                    handleEvent(sinkList);
                    sinkList.clear();
                }
            } catch (InterruptedException e) {
                break;
            } catch (FastException e) {
                break;
            } catch (Throwable e) {
                log.error("NeptuneWorker process event error", e);
            }
        }
        running.set(true);
        log.info("neptune-worker-{} is exited goodbye!", bucket);
        stopCountDownLatch.countDown();
    }

    protected void handleEvent(List<Event> eventList) {
        for (Event event : eventList) {
            switch (event.getType()) {
                case PUT: {
                    Entry entry = (Entry) event.getPayload();
                    EntryHit entryHit = new EntryHit(entry.getKey(), entry.getTimestamp());
                    SourceLogger.info(log, "receive put event key={}", entry.getKey());
                    entryHitMap.put(entry.getKey(), entryHit);
                    hitAccumulator.addToPendingMap(entry.getKey(), entryHit);
                    entryAccumulator.append(entry);
                    break;
                }
                case DELETE: {
                    String key = (String) event.getPayload();
                    EntryHit entryHit = entryHitMap.remove(key);
                    if (entryHit != null) {
                        entryHit.setBlockFp(-1);
                        hitAccumulator.append(entryHit);
                    }
                    break;
                }
                case FLUSH: {
                    entryAccumulator.flush();//强制刷新
                    hitAccumulator.flush();
                    break;
                }
                case STOP: {
                    entryAccumulator.stop();
                    hitAccumulator.stop();
                    throw closeEvent;
                }
            }
        }
    }

    private void flushAccumulator() {
        entryAccumulator.tryCommit();
        hitAccumulator.tryCommit();
    }

    private boolean publishEvent(Event event) {
        try {
            return queue.offer(event, 1, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
            return false;
        }
    }

    public void flush() {
        publishEvent(new Event(EventType.FLUSH, null));
    }

    public void stop() {
        publishEvent(new Event(EventType.STOP, null));
        boolean stopSuccess = false;
        try {
            stopSuccess = stopCountDownLatch.await(1, TimeUnit.MINUTES);
        } catch (InterruptedException e) {
        }
        if (!stopSuccess) {
            log.info("wait stop timeout");
        }
    }
}
