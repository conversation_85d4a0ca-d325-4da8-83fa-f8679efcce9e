package com.buzz.bitcask;

import com.buzz.bitcask.util.Constant;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.RandomAccessFile;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Data file format: header+data+data
 * name: 20231101173415.data
 * <p>
 * 1、Data file format本来是size+data，但在read()发现，FileChannel没有readLong()这样的API，导致要读两次，所以决定把size放在hit文件中保存
 * 2、采用block写入方式又需要改成: key_size+value_size+key+data
 */
@Slf4j
public class EntryFile {

    private String baseDir;
    private String bucket;
    private File bucketDir;
    private FileChannel entryFileChannel;
    private AtomicBoolean initialized = new AtomicBoolean();
    private long writtenBytes;

    public EntryFile(String baseDir, String bucket) {
        this.baseDir = baseDir;
        this.bucket = bucket;
        this.bucketDir = new File(baseDir, bucket);
    }

    protected void newFile() throws FileNotFoundException {
        //entryFileChannel = new RandomAccessFile(new File(bucketDir, FormatUtils.toDateTimeString(System.currentTimeMillis())), "rw").getChannel();
        File entryFile = new File(bucketDir, "entry.data");
        entryFileChannel = new RandomAccessFile(entryFile, "rw").getChannel();
        writtenBytes = 0;
    }

    public void init() throws FileNotFoundException {
        if (!initialized.compareAndSet(false, true)) {
            return;
        }

        List<String> fileNameList = Stream.of(bucketDir.listFiles((dir, name) -> name.endsWith("data")))
                .map(File::getName)
                .sorted(Comparator.comparing(Integer::valueOf))
                .collect(Collectors.toList());

        if (fileNameList.isEmpty()) {
            newFile();
        } else {
            File lastFile = new File(bucketDir, fileNameList.get(0));
            if (lastFile.length() < Constant.ENTRY_FILE_SIZE) {
                entryFileChannel = new RandomAccessFile(lastFile, "rw").getChannel();
                writtenBytes = lastFile.length();
            } else {
                newFile();
            }
        }


    }

    protected void check() {
        if (!initialized.get()) {
            throw new IllegalArgumentException("not initialized");
        }
    }

    public ByteBuffer encode(Entry entry) {
        byte[] keyBytes = entry.getKey().getBytes();
        int dataSize = entry.getData().length;
        int keySize = keyBytes.length;
        ByteBuffer buffer = ByteBuffer.allocate(Integer.BYTES + Integer.BYTES + keySize + dataSize);
        buffer.putInt(keySize);
        buffer.put(keyBytes);
        buffer.putInt(dataSize);
        buffer.put(entry.getData());
        return buffer;
    }

    public long write(ByteBuffer buffer) throws IOException {
        try {
            rolling();
            buffer.flip();
            long fp = entryFileChannel.position();
            entryFileChannel.write(buffer);
            writtenBytes += buffer.capacity();
            return fp;
        } finally {
            buffer.clear();
        }
    }

    protected synchronized void rolling() throws FileNotFoundException {
        //暂时先不实现rolling,因为hit需要记录fileName
        if (writtenBytes >= Constant.ENTRY_FILE_SIZE) {
            //newFile();
        }
    }
    public byte[] read(String key, long blockFp) throws IOException {
        ByteBuffer buffer = ByteBuffer.allocate(Constant.ENTRY_ACCUMULATE_SIZE);
        entryFileChannel.read(buffer, blockFp);
        buffer.flip();
        while (buffer.hasRemaining()) {
            int keySize = buffer.getInt();
            byte[] keyBuf = new byte[keySize];
            buffer.get(keyBuf);
            int dataSize = buffer.getInt();
            if (key.equals(new String(keyBuf))) { //如果匹配到了对应的key
                byte[] dataBuf = new byte[dataSize];
                buffer.get(dataBuf);
                return dataBuf;
            } else {
                int base = buffer.position();
                buffer.position(base + dataSize);//跳过data部分
            }
        }
        return null;
    }

    public void flush() throws IOException {
        entryFileChannel.force(true);
    }

    public void close() throws IOException {
        flush();
        entryFileChannel.close();
    }

}
