package com.buzz.bitcask;

import com.buzz.bitcask.util.Constant;
import com.buzz.bitcask.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.IOException;
import java.util.stream.Stream;

@Slf4j
public class NeptuneDBPerformanceTest {

    static String testStr = RandomUtil.generate(1000);
    static byte[] testData = testStr.getBytes();

    static {
        log.info("testStr={}", testStr);
    }

    private static void clean() {
        File baseDir = new File(Constant.BASE_DIR);
        for (File bucket : baseDir.listFiles()) {
            Stream.of(bucket.listFiles()).forEach(f -> {
                System.out.println("delete " + f.getPath());
                f.delete();
            });
            System.out.println("delete " + bucket.getPath());
            bucket.delete();
        }
    }

    private static void warmUp(NeptuneDB neptuneDB) {
        for (int i = 0; i < 1000; ++i) {
            neptuneDB.put("warmUp-" + i, testData);
        }
        neptuneDB.flush();
    }

    private static void write(NeptuneDB neptuneDB) {
        long begin = System.currentTimeMillis();
        for (int i = 0; i < 2000000; ++i) {
            neptuneDB.put("test-" + i, testData);
        }
        neptuneDB.stop();
        long end = System.currentTimeMillis();
        log.info("cost:" + (end - begin));
    }


    public static void testWrite(String[] args) throws IOException, InterruptedException {

        if (args.length > 0) {
            Constant.BUCKET_SIZE = Integer.parseInt(args[0]);
        }
        clean();
        System.out.println("bucket_size=" + Constant.BUCKET_SIZE);
        System.out.println("input for begin!");
        //System.in.read();

        NeptuneDB neptuneDB = new NeptuneDB();
        neptuneDB.init(true);
        warmUp(neptuneDB);
        write(neptuneDB);
//        System.out.println("input for exit!");
//        System.in.read();
    }


    public static void main(String[] args) throws IOException, InterruptedException {
        testWrite(args);
    }

}
