package com.buzz.bitcask;

import com.buzz.bitcask.util.RandomUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;

import java.io.IOException;

@Slf4j
public class NeptuneDBTest {

    @Test
    public void testWrite() throws IOException, InterruptedException {
        NeptuneDB neptuneDB = new NeptuneDB();
        neptuneDB.init(true);
        String testStr = RandomUtil.generate(1000);
        log.info("testSrc={}", testStr);
        byte[] testData = testStr.getBytes();
        for (int i = 0; i < 10000; ++i) {
            neptuneDB.put("test-" + i, testData);
        }
        log.info("flush begin ....");
        neptuneDB.flush();
        log.info("flush end....");
        Entry entry = neptuneDB.get("test-3333");
        System.out.println(entry);
    }

    @Test
    public void testRead() throws IOException {
        NeptuneDB neptuneDB = new NeptuneDB();
        neptuneDB.init(true);
        Entry entry = neptuneDB.get("test-1250");
        System.out.println(entry);
    }
}
