package com.buzz.bitcask;

import com.buzz.bitcask.util.CRC16;
import com.buzz.bitcask.util.RandomUtil;
import org.junit.Test;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

public class HashTest {

    @Test
    public void test() {
        Map<Integer, AtomicLong> map = new HashMap<>();
        for (int i = 0; i < 50000; ++i) {
            String key = RandomUtil.generate(6);
            //int slot = Math.abs(key.hashCode()) % 16;
            int slot = CRC16.getCRC16(key) % 16;
            AtomicLong counter = map.computeIfAbsent(slot, (k) -> new AtomicLong());
            counter.incrementAndGet();
        }

        map.forEach((k, v) -> System.out.println(k + "->" + v.get()));
    }
}
