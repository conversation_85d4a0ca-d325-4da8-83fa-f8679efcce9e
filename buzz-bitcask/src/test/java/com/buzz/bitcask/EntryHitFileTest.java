package com.buzz.bitcask;

import org.junit.Test;

import java.io.IOException;

public class EntryHitFileTest {


    @Test
    public void testWrite() throws IOException {
        EntryHitFile hitFile = new EntryHitFile("/data/tmp/bitcask", "0");
        hitFile.load();

        for (int i = 0; i < 100; ++i) {
            String key = "test-" + i;
            hitFile.write(key, new EntryHit(System.currentTimeMillis(), i));
        }

        hitFile.close();
    }

    @Test
    public void testWrite2() throws IOException {
        EntryHitFile hitFile = new EntryHitFile("/data/tmp/bitcask", "0");
        hitFile.load();

        for (int i = 0; i < 100; ++i) {
            String key = "test-10001";
            hitFile.write(key, new EntryHit(System.currentTimeMillis(), i));
        }

        //hitFile.delete();
    }

    @Test
    public void testInit() throws IOException {
        EntryHitFile hitFile = new EntryHitFile("/data/tmp/bitcask", "0");
        hitFile.load();
    }
}
