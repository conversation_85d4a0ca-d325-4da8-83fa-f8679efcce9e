package com.wacai.biz.app.controller;

import com.wacai.biz.app.kafka.MessageConsumer;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.*;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Arrays;
import java.util.concurrent.ExecutionException;

import static com.wacai.biz.app.config.Constants.*;


@Slf4j
@RestController
@RequestMapping("/kafka")
public class KafkaController {

    @Autowired
    private AdminClient adminClient;

    @Autowired
    private KafkaProducer<String, byte[]> kafkaProducer;

    @Autowired
    private MessageConsumer messageConsumer;

    @RequestMapping("/topic/create")
    public Object createTopic() throws ExecutionException, InterruptedException {
        CreateTopicsResult result = adminClient.createTopics(Arrays.asList(new NewTopic(TOPIC_NAME, PARTITION_NUM, REPLICATION_NUM)));
        result.all().get();
        return "success";
    }

    @RequestMapping("/topic/delete")
    public Object deleteTopic() throws ExecutionException, InterruptedException {
        DeleteTopicsResult result = adminClient.deleteTopics(Arrays.asList(TOPIC_NAME));
        return result.all().get();
    }

    @RequestMapping("/topic/list")
    public Object listTopic() throws ExecutionException, InterruptedException {
        ListTopicsResult listTopicsResult = adminClient.listTopics();
        return listTopicsResult.names().get();
    }

    @RequestMapping("/topic/send")
    public String sendTopic() throws ExecutionException, InterruptedException {
        for (int i = 0; i < 3; ++i) {
            ProducerRecord<String, byte[]> record = new ProducerRecord<String, byte[]>(TOPIC_NAME, "hello world".getBytes());
            RecordMetadata recordMetadata = kafkaProducer.send(record).get();
            log.info("send topic success! offset:"+recordMetadata.offset());
        }
        return "send topic success";
    }

    @RequestMapping("/consumer/start")
    public String startConsumer() {
        messageConsumer.start();
        return "startConsumer success";
    }

    @RequestMapping("/consumer/stop")
    public String stopConsumer() {
        messageConsumer.stop();
        return "stopConsumer success";
    }



}
