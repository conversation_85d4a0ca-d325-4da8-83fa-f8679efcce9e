package com.wacai.biz.app.aop;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;

@Component
@Aspect
@Slf4j
public class ExampleAspect {


    @Around("execution(* com.wacai.biz.app.service.impl.AlipayBizService.execute())")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint) throws Throwable {
        log.info("Before invoking getName() method");
        Object value = null;
        try {
            value = joinPoint.proceed();
        } catch (Throwable e) {
            e.printStackTrace();
        }
        log.info("After invoking getName() method. Return value=" + value);
        return value;
    }

}
