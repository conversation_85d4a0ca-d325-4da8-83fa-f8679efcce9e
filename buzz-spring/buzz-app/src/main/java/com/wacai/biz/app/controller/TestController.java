package com.wacai.biz.app.controller;

import com.wacai.biz.app.service.impl.AlipayBizService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 * @description
 **/
@RestController
public class TestController {

    @Autowired
    @Qualifier("alipay")
    private AlipayBizService bizService;

    @RequestMapping("/test")
    public String test() {
        bizService.execute();
        System.out.println(bizService instanceof AlipayBizService);
        return "ok";
    }
}
