package com.wacai.biz.app.config;


import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.common.serialization.ByteArrayDeserializer;
import org.apache.kafka.common.serialization.ByteArraySerializer;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.Properties;
import java.util.UUID;


@Configuration
public class KafkaConfig {

    @Value("${kafka.broker.address}")
    private String address;

    @Value("${kafka.groupId:test-group}")
    private String groupId;

    @Bean
    public AdminClient adminClient() {
        return AdminClient.create(getCommonProperties());
    }

    @Bean
    public KafkaProducer<String, byte[]> producer() {
        Properties props = new Properties();
        props.putAll(getCommonProperties());
        props.put("max.block.ms", 10 * 1000);
        props.put("acks", "1");
        return new KafkaProducer<String, byte[]>(props);
    }

    @Bean
    public KafkaConsumer<String, byte[]> consumer() {
        Properties props = new Properties();
        props.putAll(getCommonProperties());
        props.setProperty("group.id", groupId);
        props.setProperty("enable.auto.commit", "true");
        props.setProperty("auto.offset.reset", "earliest");
        props.setProperty("max.poll.records", "2");//单次消费者拉取的最大数据条数
        return new KafkaConsumer<String, byte[]>(props);
    }

    private Properties getCommonProperties() {
        Properties props = new Properties();
        props.put("bootstrap.servers", address);
        props.put("client.id", UUID.randomUUID().toString());
        props.put("key.deserializer", StringDeserializer.class);
        props.put("value.deserializer", ByteArrayDeserializer.class);
        props.put("key.serializer", StringSerializer.class);
        props.put("value.serializer", ByteArraySerializer.class);
        props.put("enable.auto.commit", "false");
        return props;
    }


}
