package com.wacai.biz.app.kafka;

import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.consumer.ConsumerRecords;
import org.apache.kafka.clients.consumer.KafkaConsumer;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.time.Duration;
import java.util.Collections;
import java.util.Iterator;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

import static com.wacai.biz.app.config.Constants.TOPIC_NAME;

/**
 * <AUTHOR>
 * @description
 **/
@Slf4j
@Component
public class MessageConsumer implements Runnable {

    @Autowired
    private KafkaConsumer<String, byte[]> consumer;

    private volatile boolean stopped = true;

    private CountDownLatch stopLatch = new CountDownLatch(1);

    @PostConstruct
    public void init(){
        log.info("Initializing MessageConsumer");
        consumer.subscribe(Collections.singletonList(TOPIC_NAME));
    }

    public void start() {
        if (!stopped) {
            return;
        }
        stopped = false;
        Thread thread = new Thread(this);
        thread.start();
        log.info("start MessageConsumer");
    }

    public void stop() {
        stopped = true;
        try {
            stopLatch.await(3000, TimeUnit.MILLISECONDS);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
        stopLatch = new CountDownLatch(1);
        log.info("stop MessageConsumer");
    }

    @Override
    public void run() {
        while (!stopped) {
            ConsumerRecords<String, byte[]> records = consumer.poll(Duration.ofMillis(3000));
            printResult(records, TOPIC_NAME);
        }
        log.info("MessageConsumer exited !");
        stopLatch.countDown();

    }

    private void printResult(ConsumerRecords<String, byte[]> records, String topic) {
        log.info("poll records size:" + records.count());
        Iterator<ConsumerRecord<String, byte[]>> iterator = records.records(topic).iterator();
        while (iterator.hasNext()) {
            ConsumerRecord<String, byte[]> record = iterator.next();
            log.info(record.topic() + "\t partition:"
                    + record.partition() + "\t offset:" + record.offset() + "\t content:" + new String(record.value()));
        }
    }
}
