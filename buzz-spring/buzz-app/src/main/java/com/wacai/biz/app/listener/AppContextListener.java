package com.wacai.biz.app.listener;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

/**
 * 在Listener中抛出异常，但是如果存在一个非daemon，那么应用不会退出
 *
 * <AUTHOR>
 * @description
 **/
@Component
@Slf4j
public class AppContextListener implements ApplicationListener<ApplicationReadyEvent> {

    @Override
    public void onApplicationEvent(ApplicationReadyEvent event) {
        Thread thread = new Thread(()->{
            while(true){
                System.out.println("===============test=========================");
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        });
        thread.start();
        log.info("[xxx]开始加载...");

        throw new IllegalArgumentException("AppContextListener error");
    }
}
