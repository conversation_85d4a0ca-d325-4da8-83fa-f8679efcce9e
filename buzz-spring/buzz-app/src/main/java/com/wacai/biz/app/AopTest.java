package com.wacai.biz.app;

import com.wacai.biz.app.aop.AopConfig;
import com.wacai.biz.app.service.BizService;
import com.wacai.biz.app.service.impl.AlipayBizService;
import com.wacai.biz.app.service.impl.WacaiBizService;
import org.junit.Test;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.AnnotationConfigApplicationContext;

/**
 * <AUTHOR>
 * @description
 **/
public class AopTest {

    @Test
    public void testLogAspect() {
        ApplicationContext ctx = new AnnotationConfigApplicationContext(AopConfig.class);
        BizService bizService = ctx.getBean("alipay", BizService.class);
        bizService.execute();
        System.out.println("is AlipayBizService:"+(bizService instanceof AlipayBizService));

        System.out.println("================================");
        bizService = ctx.getBean("wacai", BizService.class);
        bizService.execute();
        System.out.println("is WacaiBizService:"+(bizService instanceof WacaiBizService));
    }
}
