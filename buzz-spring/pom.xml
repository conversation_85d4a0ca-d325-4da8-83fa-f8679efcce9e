<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.buzz</groupId>
		<artifactId>buzz-code</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>buzz-spring</artifactId>
	<packaging>pom</packaging>


	<modules>
		<module>buzz-discovery-provider</module>
		<module>buzz-discovery-consumer</module>
		<module>buzz-springboot</module>
		<module>buzz-springboot-registrar-bean</module>
        <module>buzz-app</module>
    </modules>

	<properties>
		<spring-boot.version>2.2.6.RELEASE</spring-boot.version>
		<spring.cloud.version>Hoxton.SR9</spring.cloud.version>
	</properties>
	
	<dependencyManagement>
		<dependencies>

            <!-- Spring Dependencies -->
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-dependencies</artifactId>
                <version>${spring-boot.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>

            <dependency>
                <groupId>com.alibaba.cloud</groupId>
                <artifactId>spring-cloud-alibaba-dependencies</artifactId>
                <version>2.2.6.RELEASE</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>  		
			
		</dependencies>
	</dependencyManagement>
	
	
	
	
</project>
