package com.buzz.springboot.aop;


import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.After;
import org.aspectj.lang.annotation.Aspect;
import org.junit.Test;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.*;
import org.springframework.stereotype.Component;

@SpringBootApplication
@EnableAspectJAutoProxy
@Slf4j
public class AopTest {


    @Test
    public void testLogAspect() {
        ApplicationContext ctx = new AnnotationConfigApplicationContext(AppConfig.class);
        DummyService aopTest = ctx.getBean(DummyService.class);
        aopTest.execute();
    }

    @Test
    public void testConfiguration() {
        ApplicationContext ctx = new AnnotationConfigApplicationContext(AppConfig.class);
        ctx.getBean("firstPostService", PostService.class);
        ctx.getBean("secondPostService", PostService.class);

    }

    @EnableAutoConfiguration
    @ComponentScan("com.buzz.springboot.aop")
    @Configuration
    public static class AppConfig {

        @Bean
        public AppBean appBean() {
            return new AppBean();
        }

        @Bean
        public PostService firstPostService() {
            return new PostService(appBean());
        }

        @Bean
        public PostService secondPostService() {
            return new PostService(appBean());
        }
    }

    @Component
    public static class DummyService {
        public void execute() {
        }
    }

    public static class PostService {
        private final AppBean appBean;

        private PostService(AppBean appBean) {
            this.appBean = appBean;
        }
    }

    public static class AppBean {
        public AppBean() {
            log.info("create appBean");
        }
    }

    @Aspect
    @Component
    public static class LogAspect {
        @After("execution(* com.buzz.springboot.aop.AopTest$DummyService.*(..))")
        public void log() {
            System.out.println("记录日志 ...");
        }
    }
}
