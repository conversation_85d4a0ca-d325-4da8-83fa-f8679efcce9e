package com.buzz.springboot.app.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.mvc.method.annotation.ResponseBodyEmitter;

import javax.servlet.AsyncContext;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@RestController
public class HeartbeatController {

	@RequestMapping("/")
	public String index() {

		return "index " + System.currentTimeMillis();
	}

	@RequestMapping("/hello")
	public String hello() {

		return "hello " + System.currentTimeMillis();
	}

	@RequestMapping("/over")
	public String over() {
		System.exit(-1);
		return "hello " + System.currentTimeMillis();
	}

	@RequestMapping("/check")
	public String check(@RequestBody App app) {

		return "hello " + System.currentTimeMillis();
	}

	private ExecutorService executor = Executors.newCachedThreadPool();

	@RequestMapping("/async2")
	public App async2(HttpServletRequest request) throws IOException, InterruptedException {
		Thread.sleep(5000);
		App app = new App();
		app.setName("test");
		return app;
	}

	@RequestMapping("/async")
	public ResponseBodyEmitter async(HttpServletRequest request) throws IOException {
		log.info("async");

		ResponseBodyEmitter emitter = new ResponseBodyEmitter();
		executor.execute(() -> {
			App app = new App();
			app.setName("test");
			try {
				System.out.println("sleep:" + Thread.currentThread());
				Thread.sleep(5000);
				emitter.send(app);
				emitter.complete();
			} catch (Exception e) {
				emitter.completeWithError(e);
			}
		});
		return emitter;
	}

	private static class AsyncContextWrapper {
		private AsyncContext asyncContext;
		private volatile long startTimeMills;

		public AsyncContextWrapper(AsyncContext asyncContext, long startTimeMills) {
			super();
			this.asyncContext = asyncContext;
			this.startTimeMills = startTimeMills;
		}

	}
}
