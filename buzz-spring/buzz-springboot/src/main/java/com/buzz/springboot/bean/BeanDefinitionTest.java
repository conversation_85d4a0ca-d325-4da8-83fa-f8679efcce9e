package com.buzz.springboot.bean;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.context.support.GenericApplicationContext;
import org.springframework.test.context.ContextConfiguration;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@ContextConfiguration(classes = BeanDefinitionTest.class)
public class BeanDefinitionTest {
    @Autowired
    GenericApplicationContext applicationContext;

    @Test
    public void testBeanDefinition() {

        GenericBeanDefinition rbd = new GenericBeanDefinition();
        rbd.setBeanClass(Pet.class);
        rbd.getPropertyValues().add("type", "cat");
        rbd.getPropertyValues().add("name", "Tom");
        applicationContext.registerBeanDefinition("parentPetCat", rbd);
        Pet petParent = (Pet) applicationContext.getBean("parentPetCat");
        log.info("Parent pet cat is :{}", petParent);

        // 基于 GenericBeanDefinition 的孩子bean定义
        GenericBeanDefinition cbd = new GenericBeanDefinition();
        cbd.setParentName("parentPetCat");
        cbd.getPropertyValues().add("name", "Little Tom");
        applicationContext.registerBeanDefinition("childPetCat", cbd);
        Object petChild = applicationContext.getBean("childPetCat");
        log.info("Child pet cat is :{}", petChild);

    }

    @Data
    public static class Pet {
        String type;
        String name;
    }
}
