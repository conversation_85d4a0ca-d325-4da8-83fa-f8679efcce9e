package com.buzz.springboot.bean;


import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.beans.factory.support.MergedBeanDefinitionPostProcessor;
import org.springframework.beans.factory.support.RootBeanDefinition;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

import javax.annotation.PostConstruct;

/**
 * MyMergedBeanDefinitionPostProcessor 是一个特殊的 BeanPostProcessor 子接口
 */
@SpringBootApplication()
@Slf4j
@Import({BeanPostProcessorTest.MyBeanPostProcessor.class, BeanPostProcessorTest.MyMergedBeanDefinitionPostProcessor.class})
public class BeanPostProcessorTest {

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(BeanPostProcessorTest.class);
        application.setWebApplicationType(WebApplicationType.NONE);
        application.run(args);
    }

    @Bean("appBean")
    public AppBean AppBean() {
        return new AppBean();
    }

    static class MyBeanPostProcessor implements BeanPostProcessor {

        public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
            if (beanName.equals("appBean")) {
                log.info("appBean before init");
            }
            return bean;
        }

        public Object postProcessAfterInitialization(Object bean, String beanName) throws BeansException {
            if (beanName.equals("appBean")) {
                log.info("appBean after init");
            }
            return bean;
        }
    }
    //在Bean创建之后初始化之前执行
    static class MyMergedBeanDefinitionPostProcessor implements MergedBeanDefinitionPostProcessor {

        @Override
        public void postProcessMergedBeanDefinition(RootBeanDefinition beanDefinition, Class<?> beanType, String beanName) {
            if (beanName.equals("appBean")) {
                log.info("do MergedBeanDefinition ");
            }
        }
    }

    @Data
    static class AppBean {
        private String str;

        public AppBean(){
            log.info("create AppBean");
        }
        @PostConstruct
        public void init() {
            log.info("AppBean init");
        }
    }
}
