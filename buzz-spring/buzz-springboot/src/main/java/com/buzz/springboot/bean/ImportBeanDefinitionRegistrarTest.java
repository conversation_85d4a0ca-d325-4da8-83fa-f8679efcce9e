package com.buzz.springboot.bean;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Import;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.type.AnnotationMetadata;

import javax.annotation.PostConstruct;

/**
 * ImportBeanDefinitionRegistrar'Test
 */
@Slf4j
@SpringBootApplication()
@Import(ImportBeanDefinitionRegistrarTest.MyBeanRegistrar.class)
public class ImportBeanDefinitionRegistrarTest {

    @Autowired
    private AppBean appBean;

    @PostConstruct
    public void init() {
        log.info("init! appBean.str=" + appBean.getStr());
    }

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(ImportBeanDefinitionRegistrarTest.class);
        application.setWebApplicationType(WebApplicationType.NONE);
        application.run(args);

    }


    static class MyBeanRegistrar implements ImportBeanDefinitionRegistrar {

        public void registerBeanDefinitions(AnnotationMetadata importingClassMetadata, BeanDefinitionRegistry registry) {
            //①方法一
            GenericBeanDefinition gbd = new GenericBeanDefinition();
            gbd.setBeanClass(AppBean.class);
            gbd.getPropertyValues().addPropertyValue("str","IBDRTest");

            //②方法二
            BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(AppBean.class);
            AbstractBeanDefinition beanDefinition = builder.getBeanDefinition();
            beanDefinition.getPropertyValues().addPropertyValue("str", "IBDRTest");
            registry.registerBeanDefinition("appBean", beanDefinition);
        }
    }
    @Data
    static class AppBean {
        private String str;
    }
}

