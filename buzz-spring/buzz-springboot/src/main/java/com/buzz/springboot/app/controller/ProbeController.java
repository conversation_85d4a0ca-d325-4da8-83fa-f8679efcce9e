package com.buzz.springboot.app.controller;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.AsyncContext;
import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

@Slf4j
@WebServlet(name="ProbeController",urlPatterns = "/probe", asyncSupported = true)
public class ProbeController extends HttpServlet {

	private ExecutorService executor = Executors.newCachedThreadPool();

	private ObjectMapper mapper = new ObjectMapper();

	@Override
	public void init() throws ServletException {
		super.init();
		log.info("init=========");
	}

	@Override
	public void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		AsyncContext asyncContext = req.startAsync();
		asyncContext.setTimeout(0);

		executor.submit(() -> {

			System.out.println("sleep:" + Thread.currentThread());
			try {
				Thread.sleep(5000);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}

			HttpServletResponse res = (HttpServletResponse) asyncContext.getResponse();
			res.setStatus(HttpServletResponse.SC_OK);
			res.setContentType("application/json");
			App app = new App();
			app.setName("test");
			try {
				mapper.writeValue(res.getWriter(), app);
			} catch (JsonProcessingException e) {
				e.printStackTrace();
			} catch (IOException e) {
				e.printStackTrace();
			}
			asyncContext.complete();

		});

	}
}
