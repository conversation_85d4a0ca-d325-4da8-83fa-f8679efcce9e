package com.buzz.springboot.bean;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.beans.factory.support.BeanDefinitionRegistryPostProcessor;
import org.springframework.beans.factory.support.GenericBeanDefinition;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.WebApplicationType;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import javax.annotation.PostConstruct;

/**
 * BeanDefinitionRegistryPostProcessor' Test
 * https://stackoverflow.com/questions/49932184/spring-wiring-dynamic-bean-of-generic-type
 */
@SpringBootApplication()
@Slf4j
public class BeanDefinitionRegistryPostProcessorTest {

    @Autowired
    private AppBean appBean;

    @PostConstruct
    public void init() {
        log.info("init! appBean.str=" + appBean.getStr());
    }

    public static void main(String[] args) {
        SpringApplication application = new SpringApplication(BeanDefinitionRegistryPostProcessorTest.class);
        application.setWebApplicationType(WebApplicationType.NONE);
        application.run(args);
    }

    @Data
    static class AppBean {
        private String str;
    }

    static class MyBeanRegistrar implements BeanDefinitionRegistryPostProcessor {

        @Override
        public void postProcessBeanDefinitionRegistry(BeanDefinitionRegistry registry) throws BeansException {
            GenericBeanDefinition gbd = new GenericBeanDefinition();
            gbd.setBeanClass(AppBean.class);
            gbd.getPropertyValues().addPropertyValue("str","BDRPTest");
        }

        @Override
        public void postProcessBeanFactory(ConfigurableListableBeanFactory beanFactory) throws BeansException {

        }
    }
}
