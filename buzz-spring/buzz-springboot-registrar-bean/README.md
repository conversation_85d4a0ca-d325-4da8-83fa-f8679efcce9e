## springboot动态注册bean样例工程

本项目通过一个例子实践 springboot 动态注册 bean 的用法。

## 使用
样例程序提供以下两个注解,从本地文件或远程获取配置:
- @DynamicValue
- @DynamicService

@DynamicValue:

```java

@DynamicValue("kafka.url")
private String kafkaUrl;

@DynamicValue("kafka.url")
private void reloadKafkaUrl(String kafkaUrl) {
    log.info("reload kafka url: " + kafkaUrl);
    this.kafkaUrl = kafkaUrl;
}

```

@DynamicService

``` java
@DynamicService
public interface DatasourceService {
 public String username();
// ...

@Autowired
private DatasourceService datasourceService;
datasourceService.username();

```

## 实现原理
### 组件
- @EnableDynamic 负责启动
- DynamicConfigBindingRegistrar注册配置相关的Bean和BeanPostProcessor
- DynamicConfigScanRegistrar注册DynamicValueBeanPostProcessor
- DynamicValueBeanPostProcessor 处理 @DynamicValue
- DynamicServiceBean 处理@ 负责扫描package，注册Bean实现接口，通过动态代理把所有接口实现转换为DynamicConfig.get()