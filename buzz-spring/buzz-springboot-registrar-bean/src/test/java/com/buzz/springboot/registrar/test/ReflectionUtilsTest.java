package com.buzz.springboot.registrar.test;

import com.buzz.springboot.registrar.core.DynamicValue;
import com.buzz.springboot.registrar.example.AnnotationAction;
import org.junit.Test;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.util.ReflectionUtils;

public class ReflectionUtilsTest {

    @Test
    public void testDoWithFields() {

        ReflectionUtils.doWithFields(AnnotationAction.class, field -> {
            System.out.println(field);
            DynamicValue dynamicValue = AnnotationUtils.getAnnotation(field, DynamicValue.class);
        });
    }
}
