package com.buzz.springboot.registrar.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.PropertyValues;
import org.springframework.beans.factory.BeanCreationException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.annotation.InjectionMetadata;
import org.springframework.beans.factory.config.InstantiationAwareBeanPostProcessorAdapter;
import org.springframework.beans.factory.support.MergedBeanDefinitionPostProcessor;
import org.springframework.beans.factory.support.RootBeanDefinition;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.lang.Nullable;
import org.springframework.util.ReflectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Member;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 参考 AutowiredAnnotationBeanPostProcessor 实现 postProcessProperties() 和 postProcessMergedBeanDefinition() 两个模板方法
 */
@Slf4j
public class DynamicValueBeanPostProcessor extends InstantiationAwareBeanPostProcessorAdapter implements MergedBeanDefinitionPostProcessor, BeanFactoryAware {

    private final Map<String, InjectionMetadata> injectionMetadataCache = new ConcurrentHashMap<>(256);

    private BeanFactory beanFactory;

    @Override
    public PropertyValues postProcessProperties(PropertyValues pvs, Object bean, String beanName) {
        InjectionMetadata metadata = findDynamicValueMetadata(beanName, bean.getClass(), pvs);
        try {
            metadata.inject(bean, beanName, pvs);
        } catch (BeanCreationException ex) {
            throw ex;
        } catch (Throwable ex) {
            throw new BeanCreationException(beanName, "Injection of autowired dependencies failed", ex);
        }
        return pvs;
    }

    @Override
    public void postProcessMergedBeanDefinition(RootBeanDefinition beanDefinition, Class<?> beanType, String beanName) {
        InjectionMetadata metadata = findDynamicValueMetadata(beanName, beanType, null);
        metadata.checkConfigMembers(beanDefinition);
    }

    private InjectionMetadata findDynamicValueMetadata(String beanName, Class<?> clazz, @Nullable PropertyValues pvs) {
        // Fall back to class name as cache key, for backwards compatibility with custom callers.
        String cacheKey = (StringUtils.hasLength(beanName) ? beanName : clazz.getName());
        // Quick check on the concurrent map first, with minimal locking.
        InjectionMetadata metadata = this.injectionMetadataCache.get(cacheKey);
        if (InjectionMetadata.needsRefresh(metadata, clazz)) {
            synchronized (this.injectionMetadataCache) {
                metadata = this.injectionMetadataCache.get(cacheKey);
                if (InjectionMetadata.needsRefresh(metadata, clazz)) {
                    if (metadata != null) {
                        metadata.clear(pvs);
                    }
                    metadata = buildDynamicValueMetadata(clazz);
                    this.injectionMetadataCache.put(cacheKey, metadata);
                }
            }
        }
        return metadata;
    }

    private InjectionMetadata buildDynamicValueMetadata(final Class<?> beanClass) {
        final List<InjectionMetadata.InjectedElement> elements = new LinkedList<InjectionMetadata.InjectedElement>();
        elements.addAll(findFieldMetadata(beanClass));
        return new InjectionMetadata(beanClass, elements);
    }

    private List<InjectionMetadata.InjectedElement> findFieldMetadata(final Class<?> beanClass) {
        final List<InjectionMetadata.InjectedElement> elements = new LinkedList<InjectionMetadata.InjectedElement>();
        //获取bean所有的field
        ReflectionUtils.doWithFields(beanClass, field -> {
            DynamicValue dynamicValue = AnnotationUtils.getAnnotation(field, DynamicValue.class);
            if (dynamicValue != null) {
                DynamicValueFieldElement element = new DynamicValueFieldElement(field, dynamicValue);
                elements.add(element);
            }
        });
        return elements;
    }

    //TODO
    private List<InjectionMetadata.InjectedElement> findMethodMetadata(final Class<?> beanClass) {
        final List<InjectionMetadata.InjectedElement> elements = new LinkedList<InjectionMetadata.InjectedElement>();
        return elements;
    }

    private Object getDynamicValue(DynamicValue dynamicValue) {
        DynamicConfig dynamicConfig = beanFactory.getBean(DynamicConfig.class);
        return dynamicConfig.get(dynamicValue.value());
    }

    private class DynamicValueFieldElement extends InjectionMetadata.InjectedElement {

        private final Field field;
        private DynamicValue dynamicValue;

        protected DynamicValueFieldElement(Member member, DynamicValue dynamicValue) {
            super(member, null);
            this.field = (Field) member;
            this.dynamicValue = dynamicValue;
        }

        protected void inject(Object bean, @Nullable String requestingBeanName, @Nullable PropertyValues pvs)
                throws Throwable {
            ReflectionUtils.makeAccessible(field);
            Object value = getDynamicValue(dynamicValue);
            log.info("execute inject! bean={},value={}", requestingBeanName, value);
            field.set(bean, value);
        }
    }

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        this.beanFactory = beanFactory;
    }
}
