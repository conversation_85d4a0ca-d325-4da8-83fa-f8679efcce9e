package com.buzz.springboot.registrar.core;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeansException;
import org.springframework.beans.PropertyValues;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.core.Ordered;
import org.springframework.core.PriorityOrdered;
import org.springframework.validation.DataBinder;

@Slf4j
public class DynamicConfigBeanPostProcessor implements BeanPostProcessor, PriorityOrdered {

    private int order = Ordered.LOWEST_PRECEDENCE;

    private String beanId;

    private PropertyValues propertyValues;

    public DynamicConfigBeanPostProcessor(String beanId, PropertyValues propertyValues) {
        this.beanId = beanId;
        this.propertyValues = propertyValues;
    }

    @Override
    public int getOrder() {
        return order;
    }


    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {

        if (beanName.equals(beanId)) {
            DataBinder binder = new DataBinder(bean);
            binder.setIgnoreInvalidFields(true);
            binder.bind(propertyValues);
        }
        return bean;
    }

}
