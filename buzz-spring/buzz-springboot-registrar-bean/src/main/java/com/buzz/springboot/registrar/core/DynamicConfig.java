package com.buzz.springboot.registrar.core;

import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

public abstract class DynamicConfig {

    private Properties properties;

    protected void reload(InputStream input) throws IOException {
        Properties temp = new Properties();
        try {
            temp.load(input);
        } finally {
            input.close();
        }
        properties = temp;
    }

    public String get(String key) {
        if (properties == null) {
            return null;
        }
        return (String) properties.get(key);
    }
}
