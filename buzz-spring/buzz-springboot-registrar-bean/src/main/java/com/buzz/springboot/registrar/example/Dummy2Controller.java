package com.buzz.springboot.registrar.example;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试继承的场景
 */
@RestController
@Slf4j
public class Dummy2Controller  {

    @Autowired
    private AnnotationAction annotationAction;

    @RequestMapping("/test2/value")
    public String testValue() {
        return annotationAction.testValue();
    }


}
