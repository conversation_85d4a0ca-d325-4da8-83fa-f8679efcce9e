package com.buzz.springboot.registrar.core;

import java.io.IOException;

public class RoutingDynamicConfig extends DynamicConfig {

    private DynamicConfig dynamicConfig;

    public RoutingDynamicConfig(String source) {
        if (source.startsWith("file:")) {
            dynamicConfig = new LocalDynamicConfig(source);
        } else {
            throw new RuntimeException("not support! source="+source);
        }
    }

    @Override
    public String get(String key) {
        return dynamicConfig.get(key);
    }

    public static void main(String[] args) throws IOException {

        DynamicConfig config = new RoutingDynamicConfig("file:/tmp/data");
        while (true) {
            System.out.println("id=" + config.get("id"));
            try {
                Thread.sleep(1000l);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }
}
