package com.buzz.springboot.registrar.core;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.net.URL;

@Slf4j
public class LocalDynamicConfig extends DynamicConfig {

    private FileWatcherThread fileWatcher;
    private File file;
    private long lastModified;

    public LocalDynamicConfig(String fileUrl) {
        init(fileUrl);
    }

    private void init(String fileUrl) {
        try {
            file = new File(new URL(fileUrl).getFile());
        } catch (Exception e) {
            throw new IllegalArgumentException(e.getMessage() + ",fileUrl=" + fileUrl);
        }
        tryReload();
        fileWatcher = new FileWatcherThread();
        fileWatcher.start();
    }

    private void tryReload() {
        if (file.lastModified() != lastModified) {
            try {
                reload(new FileInputStream(file));
                lastModified = file.lastModified();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }


    private class FileWatcherThread extends Thread {
        private volatile boolean stop;

        public void run() {
            log.info("LocalDynamicConfig FileWatcherThread Started!");
            while (!stop) {
                tryReload();
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException e) {
                    stop = true;
                }
            }
            log.info("LocalDynamicConfig FileWatcherThread Exit!");
        }

        public void shutdown() {
            stop = true;
        }
    }
}
