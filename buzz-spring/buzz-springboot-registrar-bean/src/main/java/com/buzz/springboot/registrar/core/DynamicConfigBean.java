package com.buzz.springboot.registrar.core;

import lombok.Data;
import org.springframework.beans.factory.FactoryBean;

@Data
public class DynamicConfigBean implements FactoryBean<DynamicConfig> {
    private String source;
    private Integer interval;

    @Override
    public DynamicConfig getObject() throws Exception {
        return new RoutingDynamicConfig(source);
    }

    @Override
    public Class<?> getObjectType() {
        return DynamicConfig.class;
    }
}
