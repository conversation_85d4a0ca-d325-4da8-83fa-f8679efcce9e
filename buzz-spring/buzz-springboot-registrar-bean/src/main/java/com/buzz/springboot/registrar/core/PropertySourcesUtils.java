package com.buzz.springboot.registrar.core;

import org.springframework.beans.MutablePropertyValues;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionReaderUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.core.env.EnumerablePropertySource;
import org.springframework.core.env.PropertySource;
import org.springframework.core.env.PropertySources;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Properties;

public class PropertySourcesUtils {

    /**
     * Get sub {@link Properties}.
     *
     * @param propertySources {@link PropertySources}
     * @param prefix          the prefix of property name
     * @return {@code Map<String, String>}
     * @see Properties
     */
    public static Map<String, Object> getSubProperties(PropertySources propertySources, String prefix) {
        Map<String, Object> subProperties = new LinkedHashMap<String, Object>();

        String normalizedPrefix = prefix.endsWith(".") ? prefix : prefix + ".";
        String normalizedPrefixUpperCase = normalizedPrefix.toUpperCase();

        for (PropertySource<?> source : propertySources) {
            if (source instanceof EnumerablePropertySource) {
                for (String name : ((EnumerablePropertySource<?>) source).getPropertyNames()) {
                    if (name.startsWith(normalizedPrefix) || name.startsWith(normalizedPrefixUpperCase)) {
                        String subName = name.substring(normalizedPrefix.length()).toLowerCase();
                        if (subProperties.containsKey(subName)) {
                            // 当配置重复只取第一个，与Spring Boot的属性源配置链机制保持一致
                            continue;
                        }
                        Object value = source.getProperty(name);
                        subProperties.put(subName, value);
                    }
                }
            }
        }

        return subProperties;
    }

    /**
     * @param properties
     * @return
     */
    public static MutablePropertyValues resolveBeanPropertyValues(Map<String, Object> properties) {

        MutablePropertyValues propertyValues = new MutablePropertyValues();
        for (Map.Entry<String, Object> entry : properties.entrySet()) {
            String propertyName = entry.getKey();
            if (!propertyName.contains(".")) {
                // ignore property name with "."
                propertyValues.addPropertyValue(propertyName, entry.getValue());
            }
        }
        return propertyValues;
    }

    /**
     * Generate a bean name for the given top-level bean definition
     *
     * @param configClass
     * @param registry
     * @return
     */
    public static String generateBeanName(Class<?> configClass, BeanDefinitionRegistry registry) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(configClass);
        return BeanDefinitionReaderUtils.generateBeanName(builder.getRawBeanDefinition(), registry);
    }
}
