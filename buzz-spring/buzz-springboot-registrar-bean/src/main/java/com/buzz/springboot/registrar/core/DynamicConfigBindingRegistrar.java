package com.buzz.springboot.registrar.core;

import org.springframework.beans.MutablePropertyValues;
import org.springframework.beans.PropertyValues;
import org.springframework.beans.factory.support.AbstractBeanDefinition;
import org.springframework.beans.factory.support.BeanDefinitionBuilder;
import org.springframework.beans.factory.support.BeanDefinitionReaderUtils;
import org.springframework.beans.factory.support.BeanDefinitionRegistry;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.ImportBeanDefinitionRegistrar;
import org.springframework.core.annotation.AnnotationAttributes;
import org.springframework.core.env.ConfigurableEnvironment;
import org.springframework.core.env.Environment;
import org.springframework.core.env.PropertySources;
import org.springframework.core.type.AnnotationMetadata;

import java.util.Map;

import static com.buzz.springboot.registrar.core.PropertySourcesUtils.*;

/**
 * dynamic.config.source=file:/tmp/data
 */
public class DynamicConfigBindingRegistrar implements ImportBeanDefinitionRegistrar, EnvironmentAware {

    private ConfigurableEnvironment environment;

    @Override
    public void registerBeanDefinitions(AnnotationMetadata annotationMetadata,
                                        BeanDefinitionRegistry registry) {

        AnnotationAttributes attributes = AnnotationAttributes.fromMap(annotationMetadata.getAnnotationAttributes(EnableDynamicConfig.class.getName()));
        String prefix = attributes.getString("prefix");
        PropertySources propertySources = environment.getPropertySources();
        Map<String, Object> properties = getSubProperties(propertySources, prefix);
        MutablePropertyValues propertyValues = resolveBeanPropertyValues(properties);
        Class<?> configClass = attributes.getClass("type");
        String beanName = generateBeanName(configClass, registry);
        //注册
        registerDynamicConfigBean(registry, beanName, configClass);
        registerDynamicConfigBeanPostProcessor(registry, beanName, propertyValues);

    }

    private void registerDynamicConfigBeanPostProcessor(BeanDefinitionRegistry registry, String beanName, PropertyValues propertyValues) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(DynamicConfigBeanPostProcessor.class);
        builder.addConstructorArgValue(beanName);
        builder.addConstructorArgValue(propertyValues);
        AbstractBeanDefinition beanDefinition = builder.getBeanDefinition();
        //使用registerWithGeneratedName()自动分配beanName
        BeanDefinitionReaderUtils.registerWithGeneratedName(beanDefinition, registry);
    }

    private void registerDynamicConfigBean(BeanDefinitionRegistry registry, String beanName, Class<?> configClass) {
        BeanDefinitionBuilder builder = BeanDefinitionBuilder.genericBeanDefinition(configClass);
        AbstractBeanDefinition beanDefinition = builder.getBeanDefinition();
        registry.registerBeanDefinition(beanName, beanDefinition);
    }


    @Override
    public void setEnvironment(Environment environment) {
        this.environment = (ConfigurableEnvironment) environment;
    }
}
