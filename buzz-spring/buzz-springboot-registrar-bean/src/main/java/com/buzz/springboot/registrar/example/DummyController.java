package com.buzz.springboot.registrar.example;

import com.buzz.springboot.registrar.core.DynamicValue;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@Slf4j
public class DummyController {

    //@Autowired
    private DatasourceService datasourceService;

    @DynamicValue("kafka.url")
    private String kafkaUrl;


    @RequestMapping("/test/value")
    public String testValue() {
        return "kafkaUrl:"+kafkaUrl;
    }

    @RequestMapping("/test/service")
    public String testService() {
        return datasourceService.url() + "\t" + datasourceService.username();
    }

    @DynamicValue("kafka.url")
    private void reloadKafkaUrl(String kafkaUrl) {
        log.info("reload kafka url: " + kafkaUrl);
        this.kafkaUrl = kafkaUrl;
    }
}
