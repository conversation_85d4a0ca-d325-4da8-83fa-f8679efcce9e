package com.buzz.alibaba.tlog.agg;

import com.taobao.tlog.stage.aggregate.buffer.impl.SingleThreadedTimelineMap;

public class SingleThreadedTimelineMapTest {

    public static void main(String[] args) {
        //SingleThreadedTimelineMap map = new SingleThreadedTimelineMap(60,600,10000);
        
        for(int i=0;i<32;++i){
            int x = Integer.numberOfLeadingZeros(i);
            System.out.println(i+"\t"+x+"\t"+(32-x));
        }
    }
}
