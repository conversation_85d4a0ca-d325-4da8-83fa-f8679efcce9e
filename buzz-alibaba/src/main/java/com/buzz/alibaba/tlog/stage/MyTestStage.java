package com.buzz.alibaba.tlog.stage;

import java.util.List;
import java.util.Properties;

import com.taobao.tlog.TLogAppModule;
import com.taobao.tlog.actor.Actor;
import com.taobao.tlog.hbase.HBasePersistorSpec;
import com.taobao.tlog.keyvalue.Keys;
import com.taobao.tlog.keyvalue.PredefinedKeys;
import com.taobao.tlog.keyvalue.impl.DateKey;
import com.taobao.tlog.keyvalue.impl.LongKey;
import com.taobao.tlog.keyvalue.impl.SimpleKey;
import com.taobao.tlog.module.BaseConfigModule;
import com.taobao.tlog.module.BaseModule;
import com.taobao.tlog.module.EagleEyeModule;
import com.taobao.tlog.module.HBaseModule;
import com.taobao.tlog.module.HDFSModule;
import com.taobao.tlog.module.LogGatherModule;
import com.taobao.tlog.module.MetaqModule;
import com.taobao.tlog.module.ODPSTLogModule;
import com.taobao.tlog.module.OTSModule;
import com.taobao.tlog.module.ResourceClientModule;
import com.taobao.tlog.module.SplitterModule;
import com.taobao.tlog.module.SqlModule;
import com.taobao.tlog.module.StandaloneModule;
import com.taobao.tlog.module.SupplierModule;
import com.taobao.tlog.module.ZkSwitchModule;
import com.taobao.tlog.module.ZooKeeperClientModule;
import com.taobao.tlog.runtime.TLogBootstrap;
import com.taobao.tlog.runtime.TLogRuntime;
import com.taobao.tlog.service.histore.HiStoreModule;
import com.taobao.tlog.service.infobright.module.InfoBrightModule;
import com.taobao.tlog.splitter.SingleDelimiterSplitterSpec;
import com.taobao.tlog.splitter.StringSplitterSpec;
import com.taobao.tlog.stage.Action;
import com.taobao.tlog.stage.impl.ActionNode;
import com.taobao.tlog.stage.impl.StageBuilder;
import com.taobao.tlog.stage.standalone.StandaloneTopologyBuilder;
import com.taobao.tlog.supplier.SimpleSupplierSpec;
import com.taobao.tlog.supplier.Supplier;
import com.taobao.tlog.util.EnvUtils;

public class MyTestStage {

    static private HBasePersistorSpec getHBaseSpec() {

        HBasePersistorSpec spec = new HBasePersistorSpec();
        spec.setServerAddr("127.0.0.1:2181");
        spec.setColumnFamily("d");
        spec.setPersistId("mytest");
        spec.setTableName("tlog-service");
        spec.setTraceException(true);
        spec.setRowkeyPrefix("mytest");
        spec.setValueKeys(new Keys(new LongKey("dateTime"), new SimpleKey("rpcId"), PredefinedKeys.HOST_IP));

        spec.setIndexedKeys(new Keys(new LongKey("traceId")));
        spec.setSkipEmptyQueriedValue(true);
        spec.setTraceException(true);
        return spec;
    }

    static private StringSplitterSpec getLogTypeDelimiter() {
        SingleDelimiterSplitterSpec spec = new SingleDelimiterSplitterSpec();
        spec.setInputKey(PredefinedKeys.LINE);
        spec.setDelimiter("|");
        int[] seqs = {0, 1, 2, 3, 4, 5, 6, 7};
        spec.setSequenceIds(seqs);
        spec.setSplittedKeys(new Keys(new DateKey("dateTime", "yyyy-MM-dd HH:mm:ss"), new SimpleKey("traceId"),
            new SimpleKey("rpcId"), new SimpleKey("mqType"), new SimpleKey("logType"), new SimpleKey("clusterTest"),
            new SimpleKey("bizType"), new SimpleKey("content")));
        return spec;
    }

    public static List<Action> buildStages() {
        StageBuilder builder = new StageBuilder();
        ActionNode node = builder.addCollectingAction("tlog_self", "buyi-test");
        node.addNextAction(getLogTypeDelimiter());
        node.addNextAction(getHBaseSpec());
        List<Action> actions = node.getActions();

        return actions;
    }

    public static void main(String[] args) throws Exception {

        TLogBootstrap tLogBootstrap = TLogBootstrap.create(new Properties(), EnvUtils.getEnvType());
        tLogBootstrap
                .install(new BaseConfigModule())
                .install(new StandaloneModule())
                .install(new BaseModule())
                .install(new SupplierModule())
                .install(new LogGatherModule())
                .install(new HBaseModule())
                .install(new InfoBrightModule())
                .install(new HiStoreModule())
                .install(new HDFSModule())
                .install(new SplitterModule())
                .install(new TLogAppModule())
                .install(new MetaqModule())
                .install(new ODPSTLogModule())
                .install(new SqlModule())
                .install(new ResourceClientModule())
                .install(new ZkSwitchModule())
                .install(new EagleEyeModule())
                .install(new OTSModule())
                .install(new ZooKeeperClientModule());

        TLogRuntime runtime = tLogBootstrap.startRuntime();
        
        SimpleSupplierSpec spec = new SimpleSupplierSpec();
        Supplier supplier = runtime.createGenericInstance(spec);
        
        List<Action>  actions = buildStages();
        
        Actor[] actors = new StandaloneTopologyBuilder().buildActors(runtime, true, actions);


        for (final Actor actor : actors) {

            if (actor.getAction().getInputs().size() == 0) {
                supplier.setQueue(actor);
            }

            actor.start();
        }
        supplier.start();
    }
}
