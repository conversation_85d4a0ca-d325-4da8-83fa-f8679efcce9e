package com.buzz.alibaba.tlog.agg;

import com.taobao.tlog.biz.vipserver.VipServerStage;
import com.taobao.tlog.keyvalue.KVRecord;
import com.taobao.tlog.keyvalue.Key;
import com.taobao.tlog.keyvalue.NumericKey;
import com.taobao.tlog.keyvalue.impl.LongKey;
import com.taobao.tlog.keyvalue.impl.SimpleKey;
import com.taobao.tlog.stage.action.impl.ActionDelegator;
import com.taobao.tlog.stage.aggregate.Aggregator;
import com.taobao.tlog.stage.aggregate.buffer.CommitingAction;
import com.taobao.tlog.stage.aggregate.buffer.impl.TimelineMapBuffer;
import com.taobao.tlog.stage.aggregate.calc.impl.CountAggregation;
import com.taobao.tlog.stage.aggregate.calc.impl.MaxAggregation;
import com.taobao.tlog.stage.aggregate.calc.impl.SumAggregation;

import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @see VipServerStage
 */
public class AggregatorTest {

    private static String formatDate(long ts) {
        return new SimpleDateFormat("hh:mm:ss").format(new Date(ts));
    }

    public static void main(String[] args) throws Exception {
        long intervalInSecond = 5;// 5秒钟一个单位，即5秒滑动一次
        int commitingDelayInSecond = 5;// 延迟15秒提交
        int maxNumOfEntries = 100000;// 防止内存OOM

        NumericKey dateKey = new LongKey("date");
        Key groupKey = new SimpleKey("ip");
        Key bytesKey = new LongKey("bytes");

        Aggregator agg = new Aggregator(dateKey, groupKey);
        agg.setCommitWhenComplete(true);
        agg.setHeriachicallyGroupBy(false);
        agg.setDelegator(new ActionDelegator(null) {
            private static final long serialVersionUID = 1L;

            public boolean doPerform(KVRecord kvs) throws Exception {
                System.out.println("input==>" + kvs);
                return true;
            }
        });

        TimelineMapBuffer buffer =
                new TimelineMapBuffer(intervalInSecond, commitingDelayInSecond, maxNumOfEntries, agg.getDateKey());
        agg.setAggBuffer(buffer);
        agg.getAggs().add(new CountAggregation(new LongKey("count")));
        agg.getAggs().add(new SumAggregation(new LongKey("bytes")));
        agg.getAggs().add(new MaxAggregation(new LongKey("bytes"), new LongKey("maxBytes"), new SimpleKey("ip")));

        buffer.start(new CommitingAction() {

            @Override
            public void commit(KVRecord kv) {
                System.out.println("commit->" + kv);
            }

            @Override
            public void endOfBatch() {

            }

        }, agg);

        //开始聚合
        long time = 1593693986358l;
        System.out.println(formatDate(1593693986358l / 5000 * 5000));
        System.out.println("============");
        KVRecord kv = new KVRecord();
        kv.put(groupKey, "127");
        kv.put(bytesKey, 10);
        for (int i = 0; i < 10; ++i) {
            System.out.println(formatDate(time));
            kv.put(dateKey, time);
            agg.doPerform(kv);
            time += 1000;
        }
        System.out.println("============");
        for (KVRecord k : buffer.getAll(agg)) {
            System.out.println(k + "\t" + formatDate(Long.valueOf(k.get(dateKey).toString())));
        }

    }

}
