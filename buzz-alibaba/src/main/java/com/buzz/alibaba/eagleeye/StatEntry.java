package com.buzz.alibaba.eagleeye;

import java.util.Arrays;

/**
 *  统计项 ，主要通过 Fluent API 完成功能连接。
 *  注：这个类实际是一个不变量。为了提高在 HashMap 中定位 keys 性能，缓存了哈希值。
 *  
 * <AUTHOR>
 *
 */
public class StatEntry {

	/*
	 * JVM 通过逃逸分析可以优化，避免每次都创建 StatEntry
	 */

	private final StatLogger statLogger;

	/*
	 * Key 部分
	 */

	private final String[] keys;
	private transient int hash;

	public StatEntry(StatLogger statLogger, String key) {
		this.statLogger = statLogger;
		this.keys = new String[] { key };
	}

	public StatEntry(StatLogger statLogger, String key1, String key2) {
		this.statLogger = statLogger;
		this.keys = new String[] { key1, key2 };
	}

	public StatEntry(StatLogger statLogger, String key1, String... moreKeys) {
		String[] keys = new String[1 + moreKeys.length];
		keys[0] = key1;
		for (int i = 0; i < moreKeys.length; ++i) {
			keys[i + 1] = moreKeys[i];
		}
		this.statLogger = statLogger;
		this.keys = keys;
	}

	/**
	 * 计数加 1
	 */
	public void count() {
		count(1);
	}

	/**
	 * 计数加 count
	 */
	public void count(long count) {
		getFunc(StatEntryFuncFactory.COUNT_SUM).count(count);
	}

	StatEntryFunc getFunc(final StatEntryFuncFactory factory) {
		return this.statLogger.getRollingData().getStatEntryFunc(this, factory);
	}
	
	void appendTo(StringBuilder appender, char delimiter) {
		final int len = keys.length;
		if (len > 0) {
			appender.append(keys[0]);
			for (int i = 1; i < len; ++i) {
				appender.append(delimiter).append(keys[i]);
			}
		}
	}
	
	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder(64);
		sb.append("StatKeys [");
		appendTo(sb, ',');
		sb.append("]");
		return sb.toString();
	}

	@Override
	public int hashCode() {
		if (hash == 0) {
			int result = 1;
			result = 31 * result + Arrays.hashCode(keys);
			hash = result;
		}
		return hash;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StatEntry other = (StatEntry) obj;
		// 如果有 hash，优先比较
		if (hash != 0 && other.hash != 0 && hash != other.hash)
			return false;
		if (!Arrays.equals(keys, other.keys))
			return false;
		return true;
	}
}
