package com.buzz.alibaba.eagleeye.test;


import java.io.IOException;

import com.buzz.alibaba.eagleeye.EagleEyeLogs;
import com.buzz.alibaba.eagleeye.StatLogger;

public class EagleEyeLogsTest {

	public void testStatLogger() throws InterruptedException {
		StatLogger statLogger = EagleEyeLogs.statLoggerBuilder("tlog-stage").intervalSeconds(1)
				.buildSingleton();
		for (int i = 0; i < 10; ++i) {
			statLogger.stat("insert", "A").count();
			statLogger.stat("insert", "B").count();
			statLogger.stat("insert", "C").count();
			statLogger.stat("insert").count();
			if (i % 2 == 0) {
				Thread.sleep(1000);
			}
		}
		System.out.println("==========");
	}
                                                                                                                                                                                                                                                
	public static void main(String[] args) throws InterruptedException, IOException {

		new EagleEyeLogsTest().testStatLogger();
		
		System.in.read();
	}
}
