package com.buzz.alibaba.eagleeye.test;

import com.buzz.alibaba.eagleeye.EagleEyeRollingFileAppender;
import org.junit.Test;

/**
 * <AUTHOR>
 * @description
 * @date 2022-05-19 21:56
 **/
public class EagleeyeAppenderTest {


    @Test
    public void test() {
        EagleEyeRollingFileAppender appender = new EagleEyeRollingFileAppender("/data/temp/tmp.log", 100);
        for (int i = 0; i < 10000; ++i) {
            appender.append("========test log1================ "+i);
        }


    }

}
