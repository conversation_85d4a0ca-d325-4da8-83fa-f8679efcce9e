package com.buzz.alibaba.eagleeye.test;

import com.buzz.alibaba.eagleeye.FastDateFormat;
import com.buzz.alibaba.eagleeye.StatLogger;
import com.buzz.alibaba.eagleeye.StatRollingData;

public class StatLoggerRollingTest {

	public static void main(String[] args) throws InterruptedException {

		char entryDelimiter = '|';
		char keyDelimiter = ',';
		char valueDelimiter = ',';
		StatLogger statLogger = new StatLogger("test", null, 1000, 20000, entryDelimiter, keyDelimiter, valueDelimiter);

		while (true) {
			statLogger.stat("test").count();
			StatRollingData data = statLogger.getRollingData();
			long delayMillis = data.getRollingTimeMillis() - System.currentTimeMillis();
			
			FastDateFormat fmt = new FastDateFormat();
			System.out.println(fmt.formatWithoutMs(data.getTimeSlot()) + "\t"
					+ fmt.formatWithoutMs(data.getRollingTimeMillis()) + "\t" + delayMillis+"\t"+data.getStatMap().size());
			if (delayMillis > 0) {
				Thread.sleep(delayMillis);
			}
			//滚动
			statLogger.rolling();
		}
	}
}
