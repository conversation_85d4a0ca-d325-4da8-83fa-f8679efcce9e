package com.buzz.alibaba.eagleeye;

import java.io.File;
import java.net.URL;

public class EagleEyeLogs {

	/**
	 * 用户目录
	 */
	static final String USER_HOME = locateUserHome();

	/**
	 * 基础日志默认放置的位置：~/logs/
	 */
	static final String BASE_LOG_DIR = locateBaseLogPath();

	static final String EAGLEEYE_LOG_DIR = locateEagleEyeLogPath();

	static final long MAX_SELF_LOG_FILE_SIZE = 200 * 1024 * 1024; // 200MB
	static final long MAX_RPC_LOG_FILE_SIZE = 300 * 1024 * 1024; // 300MB
	static final long MAX_BIZ_LOG_FILE_SIZE = 300 * 1024 * 1024; // 300MB

	/**
	 * 根据系统参数，设置 EagleEye 的日志目录。判断优先级：
	 * <ol>
	 * <li>如果设置了 ${EAGLEEYE.LOG.PATH}，在 ${EAGLEEYE.LOG.PATH}/ 下面输出日志。
	 * <li>在 ${BASE_LOG_DIR}/eagleeye/ 下面输出日志。
	 * </ol>
	 * @since 1.2.9
	 * @return 返回路径，结尾包含“/”
	 */
	private static final String locateEagleEyeLogPath() {
		String tmpPath = EagleEyeCoreUtils.getSystemProperty("EAGLEEYE.LOG.PATH");
		if (EagleEyeCoreUtils.isNotBlank(tmpPath)) {
			if (!tmpPath.endsWith(File.separator)) {
				tmpPath += File.separator;
			}
		} else {
			tmpPath = BASE_LOG_DIR + "eagleeye" + File.separator;
		}
		return tmpPath;
	}

	/**
	 * 根据系统参数，获取用户目录，获取失败时返回 /tmp/
	 * @since 1.3.0
	 * @return 返回路径，结尾包含“/”
	 */
	private static final String locateUserHome() {
		String userHome = EagleEyeCoreUtils.getSystemProperty("user.home");
		if (EagleEyeCoreUtils.isNotBlank(userHome)) {
			if (!userHome.endsWith(File.separator)) {
				userHome += File.separator;
			}
		} else {
			userHome = "/tmp/";
		}
		return userHome;
	}

	/**
	 * 根据系统参数，设置基础的日志目录。判断优先级：
	 * <ol>
	 * <li>如果设置了 ${JM.LOG.PATH}，在 ${JM.LOG.PATH}/ 下面输出日志。
	 * <li>在 ${user.home}/logs/ 下面输出日志。
	 * </ol>
	 * @since 1.3.0
	 * @return 返回路径，结尾包含“/”
	 */
	private static final String locateBaseLogPath() {
		String tmpPath = EagleEyeCoreUtils.getSystemProperty("JM.LOG.PATH");
		if (EagleEyeCoreUtils.isNotBlank(tmpPath)) {
			if (!tmpPath.endsWith(File.separator)) {
				tmpPath += File.separator;
			}
		} else {
			tmpPath = USER_HOME + "logs" + File.separator;
		}
		return tmpPath;
	}

	public static void selfLog(String log) {
		System.out.println(log);
	}

	public static void selfLog(String log, Throwable t) {
		System.out.println(log);
	}

	static public StatLoggerBuilder statLoggerBuilder(String loggerName) {
		return new StatLoggerBuilder(loggerName);
	}

	/**
	 * 返回 EagleEye class 的加载位置
	 * @return
	 */
	static final String getEagleEyeLocation() {
		try {
			URL resource = EagleEyeLogs.class.getProtectionDomain().getCodeSource().getLocation();
			if (resource != null) {
				return resource.toString();
			}
		} catch (Throwable t) {
			// ignore
		}
		return "unknown location";
	}

	/*
	 * EagleEye 初始化
	 */
	static {
		try {
			StatLogController.start();
		} catch (Throwable e) {
			selfLog("[ERROR] fail to start StatLogController", e);
		}
		selfLog("[INFO] EagleEye started (" + getEagleEyeLocation() + ")");
	}

	private EagleEyeLogs() {
	}
}
