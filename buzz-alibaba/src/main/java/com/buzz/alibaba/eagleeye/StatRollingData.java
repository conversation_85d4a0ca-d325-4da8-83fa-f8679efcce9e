package com.buzz.alibaba.eagleeye;

import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

import lombok.Data;

/**
 * 每个固定时间段滚动的统计数据
 * <AUTHOR>
 *
 */
@Data
public class StatRollingData {
	
	private final StatLogger statLogger;

	/**
	 * 当前的时间点，是整个统计时间段的开始位置
	 */
	private final long timeSlot;
	/**
	 * 触发下次滚动的时间点，是整个统计时间段的结束位置
	 */
	private final long rollingTimeMillis;

	/**
	 * 写锁
	 */
	private ReentrantLock writeLock = new ReentrantLock();
	
	/**
	 *  保存统计项的 statMap，用来支持读者与写者能并发
	 */
	private final ConcurrentHashMap<StatEntry, StatEntryFunc> statMap;
	
	
	StatRollingData(StatLogger statLogger, int initialCapacity, long timeSlot, long rollingTimeMillis) {
		this(statLogger, timeSlot, rollingTimeMillis,
				new ConcurrentHashMap<StatEntry, StatEntryFunc>(
				Math.min(initialCapacity, statLogger.getMaxEntryCount())));
	}
	
	StatRollingData(StatLogger statLogger, long timeSlot, long rollingTimeMillis,ConcurrentHashMap<StatEntry, StatEntryFunc> statMap) {
		this.statLogger = statLogger;
		this.timeSlot = timeSlot;
		this.rollingTimeMillis = rollingTimeMillis;
		this.statMap = statMap;
	}

	StatEntryFunc getStatEntryFunc(
			final StatEntry statEntry, final StatEntryFuncFactory factory) {

		StatEntryFunc func = statMap.get(statEntry);
		if (func == null) {
			StatRollingData clone = null;
			writeLock.lock();
			try {
				int entryCount = statMap.size();
				if (entryCount < statLogger.getMaxEntryCount()) {
					func = statMap.get(statEntry);
					if (func == null) {
						// 创建并放入
						func = factory.create();
						statMap.put(statEntry, func);
					}
				} else {
					// statMap 已经满了，需要创建一个新的 StatRollingData 作为 clone 提前输出，
					// 为了保证原子性，用了拷贝的方式
					// @since 1.4.0
					ConcurrentHashMap<StatEntry, StatEntryFunc> cloneStatMap =
							new ConcurrentHashMap<StatEntry, StatEntryFunc>(statMap.size());
					cloneStatMap.putAll(statMap);
					statMap.clear();
					func = factory.create();
					statMap.put(statEntry, func);
					clone = new StatRollingData(statLogger, timeSlot, rollingTimeMillis, cloneStatMap);
				}
			} finally {
				writeLock.unlock();
			}
			
			if (clone != null) {
				EagleEyeLogs.selfLog("[INFO] force rolling statLogger[" + statLogger.getLoggerName() +
						"] since max entry size is reached: " + statLogger.getMaxEntryCount());
				StatLogController.scheduleWriteTask(clone);
			}
		}
		return func;
	}
	
	Set<Entry<StatEntry, StatEntryFunc>> getStatEntrySet() {
		return statMap.entrySet();
	}
	
	int getStatCount() {
		return statMap.size();
	}
}
