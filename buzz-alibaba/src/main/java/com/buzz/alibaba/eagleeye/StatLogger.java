package com.buzz.alibaba.eagleeye;

import java.util.concurrent.atomic.AtomicReference;

/**
 *  统计日志，一个统计日志的所有 {@link StatEntry 统计项} 会打印在同一个文件里面。
 *   统计日志的输出间隔是统一、固定的，不能改变。
 *   
 * <AUTHOR>
 *
 */
public class StatLogger {

	private final EagleEyeAppender appender;

	private final String loggerName;

	/**
	 * 当前的统计数据
	 */
	private final AtomicReference<StatRollingData> ref;

	//配置相关
	/**
	 * 统计间隔，单位：毫秒
	 */
	private final long intervalMillis;

	/**
	 * 同一个统计间隔内，可存放的统计项的数量
	 */
	private final int maxEntryCount;

	/**
	 * 字段分隔符
	 */
	private final char entryDelimiter;

	/**
	 * KEY 分隔符
	 */
	private final char keyDelimiter;

	/**
	 * VALUE 分隔符
	 */
	private final char valueDelimiter;
	
	public StatLogger(String loggerName, EagleEyeAppender appender, long intervalMillis, int maxEntryCount,
			char entryDelimiter, char keyDelimiter, char valueDelimiter) {
		this.loggerName = loggerName;
		this.appender = appender;
		this.intervalMillis = intervalMillis;
		this.maxEntryCount = maxEntryCount;
		this.entryDelimiter = entryDelimiter;
		this.keyDelimiter = keyDelimiter;
		this.valueDelimiter = valueDelimiter;
		this.ref = new AtomicReference<StatRollingData>();
		rolling();
	}

	/**
	 * 触发滚动，把滚动出来的数据返回
	 * @return 滚动出来的数据，如果是 <code>null</code>，表示没有滚动
	 * @return
	 */
	public StatRollingData rolling() {
		//while循环的作用的需要执行cas
		do {
			long now = System.currentTimeMillis();
			//获取当前时间的起点
			long timeSlot = now - now % intervalMillis;
			//获取之前的数据
			StatRollingData prevData = ref.get();
			int initialCapacity = prevData != null ? prevData.getStatCount() : 16;
			long rollingTimeMillis = timeSlot + intervalMillis;
			
			StatRollingData nextData = new StatRollingData(
					this, initialCapacity, timeSlot, rollingTimeMillis);
			if (ref.compareAndSet(prevData, nextData)) {
				return prevData;
			}
		}while(true);
	}

	/**
	 * 获取统计项
	 * @param key
	 * @return
	 */
	public StatEntry stat(String key) {
		return new StatEntry(this, key);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param key2
	 * @return
	 */
	public StatEntry stat(String key1, String key2) {
		return new StatEntry(this, key1, key2);
	}

	public StatRollingData getRollingData() {
		return ref.get();
	}

	public EagleEyeAppender getAppender() {
		return appender;
	}

	public String getLoggerName() {
		return loggerName;
	}

	public AtomicReference<StatRollingData> getRef() {
		return ref;
	}

	public long getIntervalMillis() {
		return intervalMillis;
	}

	public int getMaxEntryCount() {
		return maxEntryCount;
	}

	public char getEntryDelimiter() {
		return entryDelimiter;
	}

	public char getKeyDelimiter() {
		return keyDelimiter;
	}

	public char getValueDelimiter() {
		return valueDelimiter;
	}
	
	
}
