package com.buzz.alibaba.eagleeye;

import lombok.Data;

/**
 * 协助配置 Logger 的参数
 * 
 * <AUTHOR>
 *
 * @param <T>
 */
@SuppressWarnings("unchecked")
@Data
public class BaseLoggerBuilder<T extends BaseLoggerBuilder<T>> {

	protected final String loggerName;

	protected String filePath = null;

	protected long maxFileSize = EagleEyeLogs.MAX_BIZ_LOG_FILE_SIZE;

	protected char entryDelimiter = '|';

	protected int maxBackupIndex = 3;

	public BaseLoggerBuilder(String loggerName) {
		super();
		this.loggerName = loggerName;
	}

	/**
	 * @param logFilePath 日志路径，如果不以“/”开头，那么会认为是在
	 * 			EagleEye 的默认日志路径（~/logs/eagleeye/）下输出的相对路径
	 * @return
	 */
	public T logFilePath(String logFilePath) {
		return configLogFilePath(logFilePath, EagleEyeLogs.EAGLEEYE_LOG_DIR);
	}

	private T configLogFilePath(String filePathToConfig, String basePath) {
		EagleEyeCoreUtils.checkNotNullEmpty(filePathToConfig, "filePath");
		if (filePathToConfig.charAt(0) != '/') {
			filePathToConfig = basePath + filePathToConfig;
		}
		this.filePath = filePathToConfig;
		return (T) this;
	}

	/**
	 * 日志文件滚动大小，单位是 MB，不能小于 10MB
	 * @param maxFileSizeMB
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public T maxFileSizeMB(long maxFileSizeMB) {
		if (maxFileSize < 10) {
			throw new IllegalArgumentException("设置文件大小至少要 10MB: " + maxFileSizeMB);
		}
		this.maxFileSize = maxFileSizeMB * 1024 * 1024;
		return (T) this;
	}

	/**
	 * @param entryDelimiter 设置字段分隔符
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public T entryDelimiter(char entryDelimiter) {
		this.entryDelimiter = entryDelimiter;
		return (T) this;
	}
}
