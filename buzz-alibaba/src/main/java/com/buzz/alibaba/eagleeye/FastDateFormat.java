package com.buzz.alibaba.eagleeye;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.TimeZone;

/**
 * 固定输出的时间戳格式：yyyy-MM-dd HH:mm:ss.SSS<br>
 * 与 SimpleDateFormat 相比，差别是：
 * <ul>
 * <li>针对同一秒频繁输出的时间戳做了缓存
 * <li>不管在什么时区，输出的时间都校正到中国的东八区
 * </ul>
 * 这个类非线程安全，使用时需要做 SimpleDateFormat 类似的线程保护
 * 
 * <AUTHOR>
 */
public class FastDateFormat {

	private final SimpleDateFormat fmt = createSimpleDateFormat();

	private char[] buffer = new char[23];

	private long lastSecond = -1;

	public String format(long timestamp) {
		formatToBuffer(timestamp);
		return new String(buffer, 0, 23);
	}

	public String format(Date date) {
		return format(date.getTime());
	}

	public void formatAndAppendTo(long timestamp, StringBuilder appender) {
		formatToBuffer(timestamp);
		appender.append(buffer, 0, 23);
	}

	private void formatToBuffer(long timestamp) {
		long second = timestamp / 1000;
		if (second == lastSecond) {
			int ms = (int) (timestamp % 1000);
			buffer[22] = (char) (ms % 10 + '0');
			ms /= 10;
			buffer[21] = (char)(ms % 10 + '0');
            buffer[20] = (char)(ms / 10 + '0');
		} else {
			String result = fmt.format(new Date(timestamp));
			result.getChars(0, result.length(), buffer, 0);
		}
	}

	public String formatWithoutMs(long timestamp) {
		long second = timestamp / 1000;
		if (second != lastSecond) {
			String result = fmt.format(new Date(timestamp));
			result.getChars(0, result.length(), buffer, 0);
		}
		return new String(buffer, 0, 19);
	}

	private SimpleDateFormat createSimpleDateFormat() {
		SimpleDateFormat fmt = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS");
		fmt.setTimeZone(TimeZone.getTimeZone("GMT+8:00"));
		return fmt;
	}
}
