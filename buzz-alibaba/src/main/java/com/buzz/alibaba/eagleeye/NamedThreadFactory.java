package com.buzz.alibaba.eagleeye;

import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 
 * <AUTHOR>
 *
 */
public class NamedThreadFactory implements ThreadFactory {

	private final AtomicInteger threadNum = new AtomicInteger(1);

	private final String prefix;

	private final boolean daemon;

	private final ThreadGroup group;

	public NamedThreadFactory(String prefix) {
		this(prefix, false);
	}

	public NamedThreadFactory(String prefix, boolean daemon) {
		this.prefix = prefix + "-thread-";
		this.daemon = daemon;
		SecurityManager s = System.getSecurityManager();
		this.group = (s == null) ? Thread.currentThread().getThreadGroup() : s.getThreadGroup();
	}

	public Thread newThread(Runnable runnable) {
		String name = prefix + threadNum.getAndIncrement();
		Thread ret = new Thread(group, runnable, name, 0);
		ret.setDaemon(daemon);
		return ret;
	}

	public ThreadGroup getThreadGroup() {
		return group;
	}
}
