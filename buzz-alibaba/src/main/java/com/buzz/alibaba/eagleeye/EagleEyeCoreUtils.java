package com.buzz.alibaba.eagleeye;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;

public class EagleEyeCoreUtils {

	public static final String NEWLINE = "\r\n";
	
	private static final int PID = doGetCurrrentPid();
	
	private static final String PID_STR = String.valueOf(PID);
	
	/**
	 * get current pid,max pid 32 bit systems 32768, for 64 bit 4194304
	 * http://unix.stackexchange.com/questions/16883/what-is-the-maximum-value-of-the-pid-of-a-process
	 *
	 * http://stackoverflow.com/questions/35842/how-can-a-java-program-get-its-own-process-id
	 * @return
	 */
	private static int doGetCurrrentPid() {
		try {
			RuntimeMXBean runtime = ManagementFactory.getRuntimeMXBean();
			String name = runtime.getName();
			return Integer.parseInt(name.substring(0, name.indexOf('@')));
		} catch (Throwable t) {
			return 0;
		}
	}
	
	public static String getSystemProperty(String key) {
		try {
			return System.getProperty(key);
		} catch (Throwable t) {
			return null;
		}
	}
	
	public static boolean isNotBlank(String str) {
		return !isBlank(str);
	}
	
	public static boolean isBlank(String str) {
		int strLen;
		if (str == null || (strLen = str.length()) == 0) {
			return true;
		}
		for (int i = 0; i < strLen; i++) {
			if ((Character.isWhitespace(str.charAt(i)) == false)) {
				return false;
			}
		}
		return true;
	}
	

	public static String checkNotNullEmpty(String value, String name) throws IllegalArgumentException {
		if (isBlank(value)) {
			throw new IllegalArgumentException(name + " is null or empty");
		}
		return value;
	}
	
	public static final void shutdownThreadPool(ExecutorService pool, long awaitTimeMillis) {
		try {
			pool.shutdown();

			boolean done = false;
			if (awaitTimeMillis > 0) {
				try {
					done = pool.awaitTermination(awaitTimeMillis, TimeUnit.MILLISECONDS);
				} catch (Exception e) {
				}
			}

			if (!done) {
				pool.shutdownNow();
			}
		} catch (Exception e) {
			// quietly
		}
	}

	
	
	
	public static int getCurrrentPid() {
		return PID;
	}

	public static String getCurrrentPidString() {
		return PID_STR;
	}
}
