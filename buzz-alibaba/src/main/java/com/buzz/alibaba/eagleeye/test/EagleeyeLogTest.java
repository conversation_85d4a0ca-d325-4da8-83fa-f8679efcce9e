package com.buzz.alibaba.eagleeye.test;

import java.io.IOException;
import java.util.Date;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

import com.taobao.eagleeye.logs.EagleEyeLogs;
import com.taobao.eagleeye.logs.StatLogger;
import com.taobao.eagleeye.logs.TraceEntry;
import com.taobao.eagleeye.logs.TraceLogger;

import lombok.extern.slf4j.Slf4j;

@Slf4j
public class EagleeyeLogTest {

	public static void testTrace() {
		//最基本用法
		TraceLogger traceLogger = EagleEyeLogs.traceLogger("buzz");
		TraceEntry traceEntry = traceLogger.trace("buyer", "queryKey");
		traceEntry.traceBiz(1, "order");//业务类型
		traceEntry.logLine("dataId=10001,color=red");
		System.out.println("end");

		//允许自定义配置 TraceLogger
		TraceLogger traceLogger2 = EagleEyeLogs.traceLoggerBuilder("buzz2")
				.asyncQueueSize(10)
				.maxBackupIndex(3)
				.maxWaitMillis(1000).buildSingleton();

		EagleEyeLogs.flush();
	}

	public static void testStat(String logger, int intervalSeconds) throws InterruptedException {
		StatLogger statLogger = EagleEyeLogs.statLoggerBuilder(logger)
				.intervalSeconds(intervalSeconds)
				.buildSingleton();
		for (int i = 0; i < 10; ++i) {
			statLogger.stat("buy").count(10);
			Date now = new Date();
			log.info(now.getMinutes()+":"+now.getSeconds() + "\t" + 10 +"\t"+logger);
			//间隔睡眠，注意睡眠不能太短
			if (i % 3 == 0) {
				log.info("===="+logger+" sleep "+intervalSeconds+"===");
				Thread.sleep(1000);
			}
		}
		System.out.println("end");
//		EagleEyeLogs.flush();
	}

	public static void main(String[] args) throws InterruptedException, IOException {

		
		ExecutorService es = Executors.newFixedThreadPool(2);
		es.submit(()->{
			try {
				testStat("buzz-a",5);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		});
		es.submit(()->{
			try {
				testStat("buzz-b",1);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
		});
		es.shutdown();
		boolean ret = es.awaitTermination(5, TimeUnit.SECONDS);
		System.out.println("awaitTermination +"+ret);
		System.in.read();
		System.out.println("waiting flush");
		
		System.out.println("end");
	}
}
