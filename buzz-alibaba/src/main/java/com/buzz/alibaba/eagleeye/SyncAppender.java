package com.buzz.alibaba.eagleeye;

public class SyncAppender implements Eagle<PERSON>yeAppender {

	private EagleEyeAppender delegate;

	private final Object lock = new Object();

	public SyncAppender(EagleEyeAppender delegate) {
		super();
		this.delegate = delegate;
	}

	@Override
	public void append(String log) {
		synchronized (lock) {
			delegate.append(log);
		}
	}

	@Override
	public void close() {
		synchronized (lock) {
			delegate.close();
		}
	}

	@Override
	public void reload() {
		synchronized (lock) {
			delegate.reload();
		}
	}

	@Override
	public void flush() {
		synchronized (lock) {
			delegate.flush();
		}
	}

	@Override
	public void rollOver() {
		synchronized (lock) {
			delegate.rollOver();
		}
	}
	
	/**
	 * 清理动作和当前写入的文件无关，因此没有加同步
	 */
	@Override
	public void cleanup() {
		delegate.cleanup();
	}

}
