package com.buzz.alibaba.eagleeye;

public interface EagleEyeAppender {

	/**
	 * 写日志
	 * @param log
	 */
	void append(String log);

	/**
	 * 清理释放资源，一般用于清理滚动后要删除的文件，或者临时文件
	 */
	void cleanup();

	/**
	 * 关闭输出，释放资源
	 */
	public void close();

	/**
	 * 重新加载，这是为了防止在多进程环境并发写同一个文件导致问题， 这个操作本身包含 flush() 语义
	 */
	void reload();

	/**
	 *  刷新输出缓冲区
	 */
	void flush();

	/**
	 * 触发滚动
	 */
	void rollOver();
	
}
