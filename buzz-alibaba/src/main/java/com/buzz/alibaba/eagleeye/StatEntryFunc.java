package com.buzz.alibaba.eagleeye;

import java.util.concurrent.atomic.LongAdder;

/**
 * 各种统计方法 API，
 * 为了避免多做类型转换，这个接口是所有已知实现的全集
 * <AUTHOR>
 *
 */
public interface StatEntryFunc {

	/**
	 * value的格式化输出
	 * @param appender
	 * @param delimiter
	 */
	void appendTo(StringBuilder appender, char delimiter);

	/**
	 * 统计类型
	 * @return
	 */
	int getStatType();
	void count(long count);
	void countAndSum(long count, long value);
	void arrayAdd(long... values);
	void arraySet(long... values);
	void minMax(long candidate, String ref);
}

enum StatEntryFuncFactory {
	COUNT_SUM {
		@Override
		StatEntryFunc create() {
			return new StatEntry_countAndSum();
		}
	};

	abstract StatEntryFunc create();
}

class StatEntry_countAndSum implements StatEntryFunc {

	private LongAdder count = new LongAdder();
	private LongAdder value = new LongAdder();
	
	@Override
	public void appendTo(StringBuilder appender, char delimiter) {
		appender.append(count.sum()).append(delimiter).append(value.sum());
	}

	@Override
	public int getStatType() {
		return 1;
	}

	@Override
	public void count(long count) {
		this.count.add(count);
	}

	@Override
	public void countAndSum(long count, long value) {
		this.count.add(count);
		this.value.add(value);
	}

	@Override
	public void arrayAdd(long... values) {
		throw new IllegalStateException("已经调用 countAndSum() 的统计项不能再调用 arrayAdd()");
	}

	@Override
	public void arraySet(long... values) {
		throw new IllegalStateException("已经调用 countAndSum() 的统计项不能再调用 arraySet()");
	}

	@Override
	public void minMax(long candidate, String ref) {
		throw new IllegalStateException("已经调用 countAndSum() 的统计项不能再调用 minMax()");
	}

}
