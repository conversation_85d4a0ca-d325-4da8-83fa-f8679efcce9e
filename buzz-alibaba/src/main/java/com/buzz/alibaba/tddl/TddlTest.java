package com.buzz.alibaba.tddl;

import com.taobao.tddl.common.utils.extension.ExtensionLoader;
import com.taobao.tddl.config.ConfigDataMode;
import com.taobao.tddl.config.impl.holder.AbstractConfigDataHolder;

public class TddlTest {

	public static void main(String[] args) {
		String mode = ConfigDataMode.getMode().getExtensionName();
		System.out.println(mode);
		AbstractConfigDataHolder delegateDataHolder = ExtensionLoader.load(AbstractConfigDataHolder.class, mode);
		System.out.println(delegateDataHolder);
	}
}
