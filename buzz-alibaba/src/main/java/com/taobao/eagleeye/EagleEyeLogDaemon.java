package com.taobao.eagleeye;

import java.io.File;
import java.util.Map.Entry;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * EagleEye 定时检查，主要有下面的行为：
 * <ul>
 * <li>删除 .deleted 结尾的文件
 * <li>检测文件开关
 * <li>间隔一段时间输出一次索引
 * <li>日志文件被删除，则尝试重新创建之
 * <li>定时强制输出文件内容
 * </ul>
 *
 * <AUTHOR>
 */
class EagleEyeLogDaemon implements Runnable {

	// 文件开关
	private static final File bizOnFile = new File(EagleEye.EAGLEEYE_LOG_DIR + "biz_eagleeye.on");
	private static final File bizOffFile = new File(EagleEye.EAGLEEYE_LOG_DIR + "biz_eagleeye.off");
	private static final File rpcOnFile = new File(EagleEye.EAGLEEYE_LOG_DIR + "rpc_eagleeye.on");
	private static final File rpcOffFile = new File(EagleEye.EAGLEEYE_LOG_DIR + "rpc_eagleeye.off");
	private static final File forceLogDumpFile = new File(EagleEye.EAGLEEYE_LOG_DIR + "force_dump.on");
	private static final File samplingFile = new File(EagleEye.EAGLEEYE_LOG_DIR + "eagleeye_sampling");

	/**
	 * 周期检查间隔时间
	 */
	private static final long LOG_CHECK_INTERVAL = TimeUnit.SECONDS.toMillis(20);

    /**
     * 索引定期输出间隔
     */
    private static final long INDEX_FLUSH_INTERVAL = TimeUnit.HOURS.toMillis(12);
    private static long nextIndexFlushTime = System.currentTimeMillis() + INDEX_FLUSH_INTERVAL;

    private static AtomicBoolean running = new AtomicBoolean(false);

    private static Thread worker = null;

    private static final CopyOnWriteArrayList<EagleEyeAppender> watchedAppenders =
    		new CopyOnWriteArrayList<EagleEyeAppender>();

	/**
	 * 定期检测日志文件：如果被删除，则尝试重新创建之；强制刷新 appender
	 * @param appender
	 * @return
	 */
	static final EagleEyeAppender watch(EagleEyeAppender appender) {
		watchedAppenders.addIfAbsent(appender);
		return appender;
	}

	static final boolean unwatch(EagleEyeAppender appender) {
		return watchedAppenders.remove(appender);
	}

	@Override
	public void run() {
		while (running.get()) {
			// 文件开关检测
			checkFileSwitches();

			// 定时清理
			cleanupFiles();

			// 线程睡眠的逻辑放在中间是为了避免初始化时马上执行后面的逻辑
			try {
				Thread.sleep(LOG_CHECK_INTERVAL);
			} catch (InterruptedException e) {
				// quietly
			}

			// 间隔一段时间输出一次索引
			outputIndexes();

			// 如果被删除，则尝试重新创建之；强制刷新 appender
			flushAndReload();
		}
	}

	private void cleanupFiles() {
		for (EagleEyeAppender watchedAppender : watchedAppenders) {
			try {
				watchedAppender.cleanup();
			} catch (Exception e) {
				EagleEye.selfLog("[ERROR] fail to cleanup: " + watchedAppender, e);
			}
		}
		try {
			EagleEye.selfAppender.cleanup();
		} catch (Exception e) {
			// quietly
		}
	}

	private void flushAndReload() {
		for (EagleEyeAppender watchedAppender : watchedAppenders) {
			try {
				watchedAppender.reload();
			} catch (Exception e) {
				EagleEye.selfLog("[ERROR] fail to reload: " + watchedAppender, e);
			}
		}
		try {
			EagleEye.selfAppender.reload();
		} catch (Exception e) {
			// quietly
		}
	}

	private void outputIndexes() {
		try {
			long now = System.currentTimeMillis();
			if (now >= nextIndexFlushTime) {
				nextIndexFlushTime = now + INDEX_FLUSH_INTERVAL;
				for (Entry<String, String> entry : EagleEye.indexes.entrySet()) {
					EagleEye.index(EagleEye.TYPE_INDEX, entry.getValue(), entry.getKey());
				}
			}
		} catch (Exception e) {
			EagleEye.selfLog("[ERROR] output index table error", e);
		}
	}

	private void checkFileSwitches() {
		try {
			if (EagleEye.isBizOff() && bizOnFile.exists()) {
				EagleEye.turnBizOn();
			} else if (!EagleEye.isBizOff() && bizOffFile.exists()) {
				EagleEye.turnBizOff();
			}
			if (EagleEye.isRpcOff() && rpcOnFile.exists()) {
				EagleEye.turnRpcOn();
			} else if (!EagleEye.isRpcOff() && rpcOffFile.exists()) {
				EagleEye.turnRpcOff();
			}
			AtpTraceClient.setEnableForceLogDump(forceLogDumpFile.exists());
			readIntervalFromFile();
		} catch (Exception e) {
			EagleEye.selfLog("[ERROR] check on/off file error", e);
		}
	}

	private void readIntervalFromFile() {
		long len = samplingFile.length();
		if (len > 0 && len < 16) {
			String str = EagleEyeCoreUtils.readLineFile(samplingFile);
			if (str != null && str.length() > 0) {
				try {
					int sampling = Integer.parseInt(str);
					if (sampling != EagleEye.getSamplingInterval()) {
						EagleEye.setSamplingInterval(sampling);
					}
				} catch (Exception e) {
					// quietly
				}
			}
		}
	}

	static void start() {
		if (running.compareAndSet(false, true)) {
			Thread worker = new Thread(new EagleEyeLogDaemon());
			worker.setDaemon(true);
			worker.setName("EagleEye-LogDaemon-Thread");
			worker.start();
			EagleEyeLogDaemon.worker = worker;
		}
	}

	/**
	 * 关闭当前持有的所有日志 Logger，停止 daemon 线程
	 * @since 1.4.0
	 */
	static void stop() {
		if (running.compareAndSet(true, false)) {

			// 先关闭各个 appender
			closeAppenders();

			final Thread worker = EagleEyeLogDaemon.worker;
			if (worker != null) {
				try {
					worker.interrupt();
				} catch (Exception e) {
					// ignore
				}
				try {
					worker.join(1000);
				} catch (Exception e) {
					// ignore
				}
			}
		}
	}

	private static void closeAppenders() {
		for (EagleEyeAppender watchedAppender : watchedAppenders) {
			try {
				watchedAppender.close();
			} catch (Exception e) {
				EagleEye.selfLog("[ERROR] fail to close: " + watchedAppender, e);
			}
		}
	}

	static void flushAndWait() {
		for (EagleEyeAppender watchedAppender : watchedAppenders) {
			try {
				if (watchedAppender instanceof AsyncAppender) {
					((AsyncAppender)watchedAppender).flushAndWait();
				} else {
					watchedAppender.flush();
				}
			} catch (Exception e) {
				EagleEye.selfLog("[ERROR] fail to flush: " + watchedAppender, e);
			}
		}
	}

	private EagleEyeLogDaemon() {
	}
}
