package com.taobao.eagleeye;

import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.TimeUnit;

/**
 * 取 JVM {@link Runtime} 系统信息输出
 *
 * <AUTHOR>
 * @since 1.4.2
 */
class ScheduleRuntimeInfoExposer implements ScheduleTask {

	private static final StatLogger statLogger = EagleEye.statLoggerBuilder("eagleeye-runtime")
			.valueDelimiter(' ').maxFileSizeMB(100).maxBackupIndex(1).buildSingleton();

	@Override
	public void run() throws Exception {
		try {
			RuntimeMXBean runtimeMxBean = ManagementFactory.getRuntimeMXBean();

			statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(),
					"runtime", "name").strArray(runtimeMxBean.getName());
			statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(),
					"runtime", "startTime").strArray(String.valueOf(runtimeMxBean.getStartTime()));

			List<String> arguments = runtimeMxBean.getInputArguments();
			if (arguments != null && !arguments.isEmpty()) {
				statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(),
						"runtime", "inputArguments").strArray(
						arguments.toArray(new String[arguments.size()]));
			}

			Map<String, String> systemProperties = runtimeMxBean.getSystemProperties();
			for (Entry<String, String> entry : systemProperties.entrySet()) {
				statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(),
						"system", entry.getKey()).strArray(entry.getValue());
			}
		} catch (Throwable t) {
			// quietly
		}
	}

	@Override
	public long getIntervalMillis() {
		return TimeUnit.HOURS.toMillis(1);
	}
}
