package com.taobao.eagleeye;

import java.util.Collections;
import java.util.Map;

/**
 * 管理跟踪日志输出的相关实现
 *
 * <AUTHOR>
 * @since 1.3.5
 */
class TraceLogController {

	private static final Map<String, TraceLogger> traceLoggers = new NonBlockingHashMap<String, TraceLogger>();

    /**
     * 如果没有创建过指定 loggerName 的 TraceLogger，创建并返回；否则，返回之前创建的实例
     * @param builder
     * @return
     */
    static TraceLogger createLoggerIfNotExists(TraceLoggerBuilder builder) {
    	String loggerName = builder.getLoggerName();
    	TraceLogger traceLogger = traceLoggers.get(loggerName);
    	if (traceLogger == null) {
    		synchronized (TraceLogController.class) {
    			if ((traceLogger = traceLoggers.get(loggerName)) == null) {
    				// TraceLogger 的创建逻辑比较重，用加锁的方式保证仅创建一次
    				traceLogger = builder.create();
    				traceLoggers.put(loggerName, traceLogger);
    				EagleEye.selfLog("[INFO] created traceLogger[" + traceLogger.getLoggerName() +
    						"]: " + traceLogger.getAppender());
    			}
    		}
    	}
    	return traceLogger;
    }

    /**
     * 获取当前创建的所有 TraceLogger 实例
     * @return
     * @since 1.3.5
     */
    static Map<String, TraceLogger> getAllTraceLoggers() {
    	return Collections.unmodifiableMap(traceLoggers);
    }

	private TraceLogController() {
	}
}
