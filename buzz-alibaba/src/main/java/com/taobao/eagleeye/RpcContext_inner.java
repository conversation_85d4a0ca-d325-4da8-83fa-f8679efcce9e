package com.taobao.eagleeye;

import java.io.Serializable;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * RPC 调用上下文，如果不了解 EagleEye 的埋点机制，请勿修改
 */
public final class RpcContext_inner extends AbstractContext {

    static private final ThreadLocal<RpcContext_inner> threadLocal = new ThreadLocal<RpcContext_inner>();
	/** 用于链路签名的 UserData key */
	static public final String EAGLEEYE_TRACE_SIGNATURE_KEY = "s";
	/** 用于记录业务层面的状态码  */
	static public final String EAGLEEYE_BIZ_RESULT_CODE_KEY = "rc";
	/** 用于记录 traceName 的别名  */
	static public final String EAGLEEYE_ALIAS_NAME_KEY = "a";
	/** 用于透传入口签名的 iToken */
	static public final String URL_CLASSIFIER_KEY = "i";
	/** 用于透传入口签名的 iToken */
	static public final String URL_CLASSIFIER_2_KEY = "i2";
	/** 用于跟踪 Java 根应用 */
	static public final String EAGLEEYE_ROOT_CLASSIFIER_KEY = "r";
	/** 用于压测跟踪的流量标记 */
	static public final String FLOW_CLUSTER_TEST_KEY = "t";

    final RpcContext_inner parentRpc;
    final private AtomicInteger childRpcIdx;
    LocalContext_inner localContext;

	 // root RPC context
	RpcContext_inner(String _traceId, String _rpcId) {
		 this(_traceId, _rpcId, null);
    }
	// when call other service
	RpcContext_inner(String _traceId, String _rpcId, RpcContext_inner _parentRpc) {
		this(_traceId, _rpcId, _parentRpc, new AtomicInteger(0), new AtomicInteger(0));
    }
	// localIdx for childRpc
	RpcContext_inner(String _traceId, String _rpcId, RpcContext_inner _parentRpc, AtomicInteger _localIdx) {
		this(_traceId, _rpcId, _parentRpc, new AtomicInteger(0), _localIdx);
	}
	// childRpcIdx for clone
	RpcContext_inner(String _traceId, String _rpcId, RpcContext_inner _parentRpc, AtomicInteger _childRpcIdx,
			AtomicInteger _localIdx) {
		super(_traceId, _rpcId, _localIdx);
		parentRpc = _parentRpc;
		childRpcIdx = _childRpcIdx;
	}

	protected RpcContext_inner cloneInstance() {
		RpcContext_inner clone = new RpcContext_inner(traceId, getRpcId(),
				parentRpc, childRpcIdx, localIdx);
		clone.traceName = this.traceName;
		clone.serviceName = this.serviceName;
		clone.methodName = this.methodName;
		clone.resultCode = this.resultCode;
		clone.remoteIp = this.remoteIp;
		clone.callBackMsg = this.callBackMsg;
		clone.logType = this.logType;
		clone.rpcType = this.rpcType;
		clone.span0 = this.span0;
		clone.span1 = this.span1;
		clone.startTime = this.startTime;
		clone.logTime = this.logTime;
		clone.requestSize = this.requestSize;
		clone.responseSize = this.responseSize;
		clone.traceGroup = this.traceGroup;
		clone.localId = this.localId;
		clone.attributes = this.attributes;
		clone.localAttributes = this.localAttributes;
		clone.localContext = this.localContext;
		return clone;
	}

	String nextChildRpcId() {
		return rpcId + "." + childRpcIdx.incrementAndGet();
	}

	/**
	 * 开始一次本地调用
	 *
	 * @param serviceName 接口名称
	 * @param methodName 方法名称
	 * @since 1.2.9
	 */
	public void startLocal(String serviceName, String methodName) {
		startLocal(EagleEyeCoreUtils.EMPTY_STRING, serviceName, methodName);
	}

	/**
	 * 开始一次本地调用
	 *
	 * @param componentType 组件类型
	 * @param serviceName 接口名称
	 * @param methodName 方法名称
	 * @since 1.4.0
	 */
	public void startLocal(String componentType, String serviceName, String methodName) {
		LocalContext_inner tmpContext = getCurrentLocalContext();

		if (tmpContext != null && tmpContext.localId.length() > 64) {
			// 如果 localId 过长，而且又存在 parnet， parent->parent，很可能就是埋点出现问题，比如一直
			// startRpc，没有 endRpc， 会导致 RpcContext 嵌套过深的内存泄漏。这个时候重新创建一个上下文，
			// 使上面的上下文都能够释放
			EagleEye.selfLog("[WARN] LocalContext leak detected, traceId=" +
					this.getTraceId() + ", localId="+ this.getLocalId());
			tmpContext = new LocalContext_inner(this.getTraceId(), getRpcId(),EagleEye.MAL_ROOT_RPC_ID);
		}

		LocalContext_inner localCtx = new LocalContext_inner(traceId, getRpcId());
		localCtx.logType = EagleEye.LOG_TYPE_RPC_CLIENT;
		localCtx.startTime = System.currentTimeMillis();
		localCtx.serviceName = serviceName;
		localCtx.methodName = methodName;
		localCtx.span0 = 0;
		localCtx.localId = nextLocalId(tmpContext);
		localCtx.traceGroup = this.traceGroup;
		localCtx.rpcType = EagleEye.TYPE_LOCAL;

		// @since 1.4.0
		localCtx.remoteIp = componentType;

		if (this.attributes != null) {
    		localCtx.attributes = new LinkedHashMap<String, String>(this.attributes);
    	}

		localCtx.localParent = tmpContext;
		this.localContext = localCtx;

		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterStartLocal(this, localCtx);
			}
    	}
	}

	/**
	 * 本地调用结束，并设置业务状态码。如果业务状态码不是 RPC_RESULT_SUCCESS ，
	 * 则认为设置的业务状态码是表示错误的业务状态码
	 *
	 * @param bizResultCode 设置业务状态码
     * @param appendMsg 客户端日志追加的信息， 不能有回车换行等符号，可以为 <code>null</code>
	 * @since 1.2.9
	 */
	public void endLocal(String bizResultCode, String appendMsg) {
		if (EagleEye.RPC_RESULT_SUCCESS.equals(bizResultCode)) {
			endLocal(EagleEye.RPC_RESULT_SUCCESS, null, appendMsg);
		} else {
			// 兼容 1.4.0 以前的使用方式，用户直接设置错误状态
			endLocal(EagleEye.RPC_RESULT_FAILED, bizResultCode, appendMsg);
		}
	}

	/**
	 * 本地调用结束
	 *
	 * @param resultCode 用于表明成功还是失败。
	 *             如果成功，使用RPC_RESULT_SUCCESS，如果失败，使用 RPC_RESULT_FAILED
	 * @param bizResultCode 业务的状态码（不要有可变参数在里面，因为要进行统计）
	 * @param appendMsg 客户端日志追加的信息， 不能有回车、换行、“|” 等符号
	 * @since 1.4.0
	 */
	public void endLocal(String resultCode, String bizResultCode, String appendMsg) {
		LocalContext_inner tmpContext = localContext;
		if (null == tmpContext || tmpContext.isEnd) {
			return;
		}
		if (tmpContext.isIllegalContext(EagleEye.LOG_TYPE_RPC_CLIENT, "endRpc", EagleEye.TYPE_LOCAL)) {
			return;
		}
		//并发endLocal,会指向相同的localParent,输出两条日志,问题不大
		localContext = tmpContext.localParent;

		tmpContext.isEnd = true;
		tmpContext.logTime = System.currentTimeMillis();
		if (EagleEyeCoreUtils.EMPTY_STRING.equals(tmpContext.resultCode)) {
			tmpContext.resultCode = resultCode;
		}
		if (bizResultCode != null) {
			tmpContext.putLocalAttribute(EAGLEEYE_BIZ_RESULT_CODE_KEY, bizResultCode);
		}
		if (appendMsg != null) {
			tmpContext.callBackMsg = appendMsg;
		}
		tmpContext.span1 = (int) (tmpContext.logTime - tmpContext.startTime);

		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterEndLocal(this, tmpContext);
			}
    	}

		EagleEye.commitLocalContext(tmpContext);
	}

	/**
	 * @since 1.2.9
	 */
	LocalContext_inner getCurrentLocalContext() {
		LocalContext_inner tmpContext = localContext;
		/*
		 * 当前rpc已经被end(本地嵌套rpc调用或异步调用会发生这种情况) ,直接指向parent local
		 */
		while (null != tmpContext && tmpContext.isEnd) {
			tmpContext = tmpContext.localParent;
		}
		localContext = tmpContext;
		return tmpContext;
	}

	/**
	 * @param tmpContext
	 * @return
	 * @since 1.2.9
	 */
	String nextLocalId(LocalContext_inner tmpContext) {
		if (tmpContext == null) {
			return EagleEye.ROOT_RPC_ID + "." + localIdx.incrementAndGet();
		} else {
			return tmpContext.localId + "." + tmpContext.localIdx.incrementAndGet();
		}
	}

	/**
	 * @since 1.2.9
	 */
	String getInnerLocalId() {
		// 大部分是这种情况
		if (null == localContext) {
			return EMPTY_LOCALID;
		}
		LocalContext_inner tmpContext = getCurrentLocalContext();
		if (tmpContext == null) {
			return EMPTY_LOCALID;
		} else {
			return tmpContext.getLocalId();
		}
	}

    /**
     * 创建子 RPC 上下文
     * @return
     * @since 1.2.6
     */
    public RpcContext_inner createChildRpc() {
    	final RpcContext_inner parent;
    	if (this.rpcId.length() > 64) {
    		// 当前 RpcContext 创建子 RpcContext，一般当前 Context 就是服务端或者入口端，
    		// 正常情况不应该再有 parent。如果 rpcId 过长，而且又存在 parnet，
    		// parent->parent，很可能就是埋点出现问题，比如一直 startRpc，没有 endRpc，
    		// 会导致 RpcContext 嵌套过深的内存泄漏。这个时候重新创建一个上下文，使上面的上下文都能够释放。
    		// @since 1.2.7.1 修复不正常埋点导致的内存泄露
    		// @since 1.3.5.2 修复乒乓球调用导致 RpcId 过长
    		EagleEye.selfLog("[WARN] rpcId is too long, traceId=" + traceId + ", rpcId=" + rpcId);
    		parent = new RpcContext_inner(traceId, EagleEye.MAL_ROOT_RPC_ID, null);
    	} else {
    		parent = this;
    	}
    	RpcContext_inner ctx = new RpcContext_inner(traceId, nextChildRpcId(), parent, localIdx);
    	if (this.attributes != null) {
    		ctx.attributes = new LinkedHashMap<String, String>(this.attributes);
    	}
    	ctx.traceGroup = this.traceGroup;
    	/**
    	 * localChain也要在rpc上下文传递
    	 * @since 1.2.9
    	 */
    	ctx.localId = getInnerLocalId();
    	ctx.localContext = this.localContext;
        return ctx;
    }

	/**
	 * 获取上一层调用上下文
	 * @since 1.2.6
	 */
	public RpcContext_inner getParentRpcContext() {
		return parentRpc;
	}

	/*
	 * 外置的 EagleEye 埋点逻辑，方便在不同的中间件做埋点
	 * @since 1.2.2
	 */
	public void startTrace(String traceName) {
		this.logType = EagleEye.LOG_TYPE_TRACE;
		this.startTime = System.currentTimeMillis();
		this.traceName = traceName;
		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterStartTrace(this);
			}
    	}
    }

	public void endTrace(String resultCode, int type) {
		try {
			endTrace0(resultCode, type);
		} catch (Exception e) {
        	// quitely
		}
	}

	final void endTrace0(String resultCode, int type) {
		if (isIllegalContext(EagleEye.LOG_TYPE_TRACE, "endTrace", type)) {
			throw IllegalContextException.getInstance();
		}
		this.logTime = System.currentTimeMillis();
		if (EagleEyeCoreUtils.EMPTY_STRING.equals(this.resultCode)) {
			this.resultCode = resultCode;
		}
		this.rpcType = type;
		this.span1 = (int) (this.logTime - this.startTime);
		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterEndTrace(this);
			}
    	}
	}

	public void startRpc(String serviceName, String methodName) {
		this.logType = EagleEye.LOG_TYPE_RPC_CLIENT;
		this.startTime = System.currentTimeMillis();
		this.serviceName = serviceName;
		this.methodName = methodName;
		this.span0 = 0;
		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterStartRpc(this);
			}
    	}
	}

	public void endRpc(String result, int type, String appendMsg) {
		try {
			endRpc0(result, type, appendMsg);
		} catch (Exception e) {
        	// quitely
		}
	}

	final void endRpc0(String result, int type, String appendMsg) {
		if (isIllegalContext(EagleEye.LOG_TYPE_RPC_CLIENT, "endRpc", type)) {
			throw IllegalContextException.getInstance();
		}
		this.logTime = System.currentTimeMillis();
		this.rpcType = type;
		if (EagleEyeCoreUtils.EMPTY_STRING.equals(this.resultCode)) {
			this.resultCode = result;
		}
		if (appendMsg != null) {
			this.callBackMsg = appendMsg;
		}
		this.span1 = (int) (this.logTime - this.startTime);
		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterEndRpc(this);
			}
    	}
	}

	/**
	 * 调用的中间阶段
	 * @see #startRpc(String, String)
	 * @see #endRpc(String, int, String)
	 */
	public void rpcClientSend() {
		if (isIllegalContext(EagleEye.LOG_TYPE_RPC_CLIENT, "rpcClientSend",
				EagleEye.LOG_TYPE_EVENT_ILLEGAL)) {
			return;
		}
    	this.span0 = (int) (System.currentTimeMillis() - this.startTime);
		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterRpcClientSend(this);
			}
    	}
    }

	public void rpcServerRecv(String serviceName, String methodName) {
		this.logType = EagleEye.LOG_TYPE_RPC_SERVER;
		this.startTime = System.currentTimeMillis();
		this.serviceName = serviceName;
		this.methodName = methodName;
		putUserData(EAGLEEYE_TRACE_SIGNATURE_KEY,
				updateSignature(getUserData(EAGLEEYE_TRACE_SIGNATURE_KEY), serviceName, methodName));
		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterRpcServerRecv(this);
			}
    	}
    }

	/**
	 * @deprecated 使用 {@link #rpcServerSend(int, String, String)}
	 */
	public void rpcServerSend(int type) {
		rpcServerSend(type, null, null);
	}

	/**
	 * @since 1.2.8
	 */
	public void rpcServerSend(int type, String resultCode, String appendMsg) {
		try {
			rpcServerSend0(type, resultCode, appendMsg);
		} catch (Exception e) {
        	// quitely
		}
	}

	final void rpcServerSend0(int type, String resultCode, String appendMsg) {
		if (isIllegalContext(EagleEye.LOG_TYPE_RPC_SERVER, "rpcServerSend", type)) {
			throw IllegalContextException.getInstance();
		}
		this.logTime = System.currentTimeMillis();
		if (EagleEyeCoreUtils.EMPTY_STRING.equals(this.resultCode)) {
			this.resultCode = resultCode;
		}
		if (appendMsg != null) {
			this.callBackMsg = appendMsg;
		}
		this.rpcType = type;
		this.span1 = (int) (this.logTime - this.startTime);
		if (!EagleEye.listeners.isEmpty()) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterRpcServerSend(this);
			}
    	}
	}

	/**
	 * 设置业务的状态码（不要有可变参数在里面，因为要进行统计）
	 * @param bizResultCode
	 * @since 1.4.0
	 */
	public void setBizResultCode(String bizResultCode) {
		putLocalAttribute(EAGLEEYE_BIZ_RESULT_CODE_KEY, bizResultCode);
	}

    /**
     * 支持业务重新设置 traceName，同时重新指定 iToken
     * @param traceName 新的 URL，如果为 <code>null</code>，则不设置 traceName
     * @param iToken 新的 iToken 标记，如果为 <code>null</code>，则按当前 traceName 自动生成
     * @since 1.4.0
     * @see #setAliasName(String) 设置别名
     */
    public void resetTraceName(String traceName, String iToken) {
        if (logType != EagleEye.LOG_TYPE_TRACE) {
        	return;
        }
        if (traceName != null) {
        	this.traceName = traceName;
        	if (iToken == null) {
    			final String uri = EagleEyeCoreUtils.getUriFromUrl(traceName);
    			putUserData(URL_CLASSIFIER_KEY, Integer.toHexString(uri.hashCode()));
        	} else {
        		putUserData(URL_CLASSIFIER_KEY, iToken);
        	}
        } else if (iToken != null) {
        	putUserData(URL_CLASSIFIER_KEY, iToken);
        }
    }

    /**
     * 支持业务重新设置服务别名，而不覆盖原有的
     * @param aliasName 设置别名，如果为 <code>null</code>，则设置无效
     * @since 1.4.0
     * @see #resetTraceName(String, String) 重置 traceName
     */
	public void setAliasName(String aliasName) {
		if (aliasName == null) {
			return;
		}
		putLocalAttribute(EAGLEEYE_ALIAS_NAME_KEY, aliasName);
		if (this.logType == EagleEye.LOG_TYPE_TRACE) {
			putUserData(URL_CLASSIFIER_2_KEY, Integer.toHexString(aliasName.hashCode()));
		}
	}

	/**
	 * @deprecated
	 */
	@Deprecated
	public void rpcLog(int type, String msg, boolean appendRpcId) {
		this.logType = EagleEye.LOG_TYPE_RPC_LOG;
		this.logTime = System.currentTimeMillis();
		this.rpcType = type;
		this.callBackMsg = msg;
	}

	public void index(int type, String index, String msg) {
		this.logType = EagleEye.LOG_TYPE_INDEX;
		this.logTime = System.currentTimeMillis();
		this.rpcType = type;
		this.traceName = index;
		this.callBackMsg = msg;
	}

	/**
	 * 更新链路签名，主要用于实时链路日志定位
	 * @param oldSig
	 * @param serviceName
	 * @param methodName
	 * @return
	 * @since 1.2.4
	 */
	static String updateSignature(String oldSig, String serviceName, String methodName) {
		int sig = 0;
		if (oldSig != null) {
			try {
				sig = (int) Long.parseLong(oldSig, 16);
			} catch (NumberFormatException e) {
			}
		}
		int a = serviceName != null ? serviceName.hashCode() : "null".hashCode();
		int b = methodName != null ? methodName.hashCode() : "null".hashCode();
		return Integer.toHexString((sig * 31 + a) * 31 + b);
	}

	/*
	 * RpcContext_inner backup/restore
	 */
    static void set(RpcContext_inner ctx) {
    	final boolean hasListeners = !EagleEye.listeners.isEmpty();
		if (hasListeners) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.beforeSet(ctx);
			}
    	}
        threadLocal.set(ctx);
		if (hasListeners) {
    		for (EagleEyeContextListener listener : EagleEye.listeners) {
    			listener.afterSet(ctx);
			}
    	}
    }

    static RpcContext_inner get() {
        return threadLocal.get();
    }

    /**
     * 把 RpcContext 导出为 Map 进行传输，以便网络传输时序列化可以兼容新老版本。
     * @return
     * @since 1.2.6
     */
    public Map<String, String> toMap() {
        HashMap<String, String> map = new HashMap<String, String>(3);
        map.put(EagleEye.TRACE_ID_KEY, traceId);
        map.put(EagleEye.RPC_ID_KEY, rpcId);
        map.put(EagleEye.USER_DATA_KEY, exportUserData());

        return map;
    }

    static RpcContext_inner fromMap(Map<String, String> map) {
        String traceId = EagleEyeCoreUtils.trim(map.get(EagleEye.TRACE_ID_KEY));
        String rpcId = EagleEyeCoreUtils.trim(map.get(EagleEye.RPC_ID_KEY));
        String userData = map.get(EagleEye.USER_DATA_KEY);

        if (null == traceId || null == rpcId) {
            return null;
        }
        if (!EagleEyeCoreUtils.isValidTraceId(traceId)) {
        	traceId = EagleEye.generateTraceId(null);
        	rpcId = EagleEye.MAL_ROOT_RPC_ID;
        } else if (!EagleEyeCoreUtils.isValidRpcId(rpcId)) {
        	rpcId = EagleEye.MAL_ROOT_RPC_ID;
        }

        RpcContext_inner ctx = new RpcContext_inner(traceId, rpcId);
        ctx.importUserData(userData);
		return ctx;
    }
}

/**
 * 为了兼容旧版EAGLEEYE通过RPC上下文传递RpcContext对象。
 */
class RpcContext implements Serializable {
    static private final long serialVersionUID = 0L;
}