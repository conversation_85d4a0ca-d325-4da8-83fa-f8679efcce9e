package com.taobao.eagleeye;

/**
 * 将 EagleEye 的调用上下文更新同步到 log4j2 ThreadContext
 *
 * <AUTHOR>
 * @since 1.4.4
 */
public class EagleEyeLog4j2MdcUpdater extends EagleEyeContextListener {

	private static final EagleEyeLog4j2MdcUpdater singleton = new EagleEyeLog4j2MdcUpdater();

	private EagleEyeLog4j2MdcUpdater() {
	}

	public static EagleEyeLog4j2MdcUpdater getInstance() {
		return singleton;
	}

	@Override
	public void beforeSet(RpcContext_inner context) {
//		if (context != null) {
//			ThreadContext.put("EAGLEEYE_TRACE_ID", context.getTraceId());
//			ThreadContext.put("EAGLEEYE_RPC_ID", context.getRpcId());
//		} else {
//			ThreadContext.remove("EAGLEEYE_TRACE_ID");
//			ThreadContext.remove("EAGLEEYE_RPC_ID");
//		}
	}
}
