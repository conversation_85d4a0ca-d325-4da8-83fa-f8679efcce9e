package com.taobao.eagleeye;

import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * EagleEye 内部的定时调度任务
 *
 * <AUTHOR>
 * @since 1.4.2
 */
class ScheduleTaskController {

    private static final ScheduledThreadPoolExecutor scheduleThreadPool =
    		new ScheduledThreadPoolExecutor(1, new NamedThreadFactory(
    				"EagleEye-ScheduleTaskController-worker", true));

    private static AtomicBoolean running = new AtomicBoolean(false);

	static void addTask(ScheduleTask task) {
		if (task != null) {
			try {
				long intervalMillis = task.getIntervalMillis();
				scheduleThreadPool.scheduleAtFixedRate(new TaskWrapper(task), delay(),
						intervalMillis, TimeUnit.MILLISECONDS);
				EagleEye.selfLog("[INFO] scheduled task " + task.getClass().getSimpleName() +
						", intervalMillis=" + intervalMillis);
			} catch (Throwable t) {
				EagleEye.selfLog("[ERROR] fail to schedule task:" + task.getClass().getSimpleName(), t);
			}
		}
	}

    private static long delay() {
        long currentTimeMillis = System.currentTimeMillis();
        // 首次在下一个 55 秒（本分钟或下一分钟）执行
        long clockOnTheWall = (currentTimeMillis / 1000 / 60 + 1) * 1000 * 60 - 5 * 1000;
        long delay = clockOnTheWall - currentTimeMillis;
        if (delay <= 5) {
        	delay += 60 * 1000;
        }
        return delay;
    }

	private static class TaskWrapper implements Runnable {

		final ScheduleTask task;

		TaskWrapper(ScheduleTask task) {
			this.task = task;
		}

		@Override
		public void run() {
            try {
            	task.run();
            } catch (Exception ex) {
                EagleEye.selfLog("[WARN] exception while running ScheduleTask: " + task.getClass().getSimpleName(), ex);
            }
		}
	}

	static void start() {
		if (running.compareAndSet(false, true)) {
			scheduleThreadPool.setExecuteExistingDelayedTasksAfterShutdownPolicy(false);
			addTask(new ScheduleClassLoadingInfoExposer());
			addTask(new ScheduleGcInfoExposer());
			addTask(new ScheduleMemoryInfoExposer());
			addTask(new ScheduleOsInfoExposer());
			addTask(new ScheduleRuntimeInfoExposer());
			addTask(new ScheduleThreadInfoExposer());
		}
	}

	static void stop() {
		if (running.compareAndSet(true, false)) {
			EagleEyeCoreUtils.shutdownThreadPool(scheduleThreadPool, 5000);
			EagleEye.selfLog("[INFO] ScheduleTaskController: worker ThreadPool shutdown successfully");
		}
	}

	private ScheduleTaskController() {
	}
}
