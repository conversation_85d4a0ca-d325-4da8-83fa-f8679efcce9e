package com.taobao.eagleeye;

import java.util.HashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

/**
 * 每个固定时间段滚动的统计数据
 * <AUTHOR>
 */
final class StatRollingData {

	private final StatLogger statLogger;

	/**
	 * 当前的时间点，是整个统计时间段的开始位置
	 */
	private final long timeSlot;

	/**
	 * 触发下次滚动的时间点，是整个统计时间段的结束位置
	 */
	private final long rollingTimeMillis;

	/**
	 * 保存统计项的 statMap，用来支持读者与写者能并发
	 */
	private final Map<StatEntry, StatEntryFunc> statMap;

//	private final ReentrantLock writeLock = new ReentrantLock();

	StatRollingData(StatLogger statLogger, int initialCapacity, long timeSlot, long rollingTimeMillis) {
		this(statLogger, timeSlot, rollingTimeMillis,
				new NonBlockingHashMap<StatEntry, StatEntryFunc>(
				Math.min(initialCapacity, statLogger.getMaxEntryCount())));
	}

	private StatRollingData(StatLogger statLogger, long timeSlot, long rollingTimeMillis,
			Map<StatEntry, StatEntryFunc> statMap) {
		this.statLogger = statLogger;
		this.timeSlot = timeSlot;
		this.rollingTimeMillis = rollingTimeMillis;
		this.statMap = statMap;
	}

	StatEntryFunc getStatEntryFunc(
			final StatEntry statEntry, final StatEntryFuncFactory factory) {
		StatEntryFunc func = statMap.get(statEntry);
		if (func == null) {
			StatRollingData clone = null;
			int entryCount = statMap.size();
			if (entryCount < statLogger.getMaxEntryCount()) {
				// 在并发高的时候，实际放入的 entry 可能会超过 maxEntryCount
				func = factory.create();
				NonBlockingHashMap<StatEntry, StatEntryFunc> concurrmap =
						(NonBlockingHashMap<StatEntry, StatEntryFunc>) statMap;
				StatEntryFunc ret = concurrmap.putIfAbsent(statEntry, func);
				if (ret != null) {
					return ret;
				}
			} else {
				// statMap 已经满了，需要创建一个新的 StatRollingData 作为 clone 提前输出，
				// 为了保证原子性，用了拷贝的方式
				// @since 1.4.0
				Map<StatEntry, StatEntryFunc> cloneStatMap =
						new HashMap<StatEntry, StatEntryFunc>(statMap);
				statMap.clear();

				func = factory.create();
				statMap.put(statEntry, func);
				clone = new StatRollingData(statLogger, timeSlot, rollingTimeMillis, cloneStatMap);
			}

			if (clone != null) {
				StatLogController.scheduleWriteTask(clone);
			}
		}
		return func;
	}

	StatLogger getStatLogger() {
		return statLogger;
	}
	long getRollingTimeMillis() {
		return rollingTimeMillis;
	}
	long getTimeSlot() {
		return timeSlot;
	}
	int getStatCount() {
		return statMap.size();
	}
	Set<Entry<StatEntry, StatEntryFunc>> getStatEntrySet() {
		return statMap.entrySet();
	}
}
