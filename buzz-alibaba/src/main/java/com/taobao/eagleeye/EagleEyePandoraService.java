package com.taobao.eagleeye;


/**
 * {@link PandoraService} 的实现，订阅潘多拉容器的生命周期回调
 *
 * <AUTHOR>
 * @since 1.4.0
 */
public class EagleEyePandoraService {

//	@Override
//	public void init(Context context) throws PandoraException {
//		EagleEye.selfLog("[INFO] EagleEyePandoraService is initialized successfully");
//	}
//
//	@Override
//	public void start(Context context) throws PandoraException {
//	}
//
//	@Override
//	public void stop(Context context) throws PandoraException {
//		EagleEye.shutdown();
//	}
//
//	@Override
//	public String getName() {
//		return "EagleEyePandoraService";
//	}
}
