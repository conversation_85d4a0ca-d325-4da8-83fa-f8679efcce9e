package com.taobao.eagleeye;

import java.io.IOException;
import java.lang.Thread.State;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.locks.Condition;
import java.util.concurrent.locks.LockSupport;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 异步提交日志，避免影响主线程
 * <AUTHOR>
 */
class AsyncAppender extends EagleEyeAppender {

	/**
	 * 默认的消费者唤醒阈值，这个值需要让消费者能较持续的有事情做，
	 * 这个值设置过小，会导致生产者频繁唤起消费者；
	 * 设置过大，可能导致生产者速度过快导致队列满丢日志的问题。
	 */
	private static final int DEFAULT_NOTIFY_THRESHOLD = 512;
	/**
	 * 异步线程在处理事件时，需要对 context 对象做 notify 唤醒动作
	 */
	private static final int DO_EVENT_NOTIFY = Integer.MIN_VALUE;

    /** 用于内部控制刷新日志的命令 */
    static final int LOG_TYPE_EVENT_FLUSH = -1;
    /** 用于内部控制滚动日志的命令 */
    static final int LOG_TYPE_EVENT_ROLLOVER = -2;
    /** 用于内部控制日志重载的命令 */
    static final int LOG_TYPE_EVENT_RELOAD = -3;
    /** 用于内部控制关闭 Appender 的命令 */
    static final int LOG_TYPE_EVENT_CLOSE = -4;

	// RingBuffer 实现，size 必须为 2 的 n 次方
	private final BaseContext[] entries;
	private final int queueSize;
	private final int indexMask;
	private final int notifyThreshold;
	// 在队列满的时候，业务线程最长尝试时间
	private final int maxWaitMillis;

	private final ReentrantLock lock;
	private final Condition notEmpty;

	// 下一个写的位置，一直递增
	private AtomicLong putIndex;
	// 最近丢弃的日志条数
	private AtomicLong discardCount;
	// 下一个读的位置，一直递增，不能大于 putIndex
	private AtomicLong takeIndex;

	private EagleEyeAppender appender;
	private BaseContextEncoder encoder;
	private String workerName;

	private Thread worker;
	private AtomicBoolean running;

	public AsyncAppender(int queueSize, int maxWaitMillis) {
		// queueSize 取大于或等于 value 的 2 的 n 次方数
		queueSize = 1 << (32 - Integer.numberOfLeadingZeros(queueSize - 1));

		this.queueSize = queueSize;
		this.maxWaitMillis = maxWaitMillis;
		this.entries = new BaseContext[queueSize];
		this.indexMask = queueSize - 1;
		this.notifyThreshold = queueSize >= DEFAULT_NOTIFY_THRESHOLD ? DEFAULT_NOTIFY_THRESHOLD : queueSize;

		this.putIndex = new AtomicLong(0L);
		this.discardCount = new AtomicLong(0L);
		this.takeIndex = new AtomicLong(0L);

		this.running = new AtomicBoolean(false);

		this.lock = new ReentrantLock(false);
		this.notEmpty = lock.newCondition();
	}

	void start(EagleEyeAppender appender, BaseContextEncoder encoder, String workerName) {
		if (appender instanceof AsyncAppender) {
			throw new IllegalArgumentException("nested AsyncAppender is not allow: " + workerName);
		}
		this.appender = EagleEyeCoreUtils.checkNotNull(appender, "appender");
		this.encoder = encoder;
		this.workerName = workerName;

		this.worker = new Thread(new AsyncRunnable(), "EagleEye-AsyncAppender-Thread-" + workerName);
		this.worker.setDaemon(true);
	    this.worker.start();
	}

	int size() {
		return (int) (putIndex.get() - takeIndex.get());
	}

	/**
	 * 队列满时直接丢弃日志，不阻塞业务线程，返回日志是否被接受
	 */
	boolean append(BaseContext ctx) {
		final long qsize = queueSize;
		long startTime = 0;
		for (;;) {
			final long put = putIndex.get();
			final long size = put - takeIndex.get();
			if (size >= qsize) {
				boolean wait;

				int maxWaitMillis;
				if (ctx.isEvent()) {
					maxWaitMillis = Math.max(this.maxWaitMillis, 1000);
				} else {
					maxWaitMillis = this.maxWaitMillis;
				}

				if (maxWaitMillis <= 0) {
					wait = false;
				} else {
					long now = System.currentTimeMillis();
					if (startTime == 0) {
						startTime = now;
						wait = true;
					} else if (now - startTime >= maxWaitMillis) {
						wait = false;
					} else {
						wait = true;
					}
				}

				if (wait) {
					LockSupport.parkNanos(1000);
					continue;
				} else {
					discardCount.incrementAndGet();
					return false;
				}
			}
			if (putIndex.compareAndSet(put, put + 1)) {
				entries[(int) put & indexMask] = ctx;
				// 仅仅在队列的日志数超过阈值，且消费者不在运行，且获得锁，才唤醒消费者
				// 这个做法能保证只有必要时才立即通知消费者，减少上下文切换的开销
				if (size >= notifyThreshold && !running.get() && lock.tryLock()) {
					try {
						notEmpty.signal();
					} catch (Exception e) {
						EagleEye.selfLog("[ERROR] fail to signal notEmpty: " + workerName, e);
					} finally {
						lock.unlock();
					}
				}
				return true;
			}
		}
	}

	@Override
	public void append(String log) {
		throw new UnsupportedOperationException(
				"use append(BaseContext ctx) instead in AsyncAppender");
	}

	@Override
	public void rollOver() {
		publishEvent(LOG_TYPE_EVENT_ROLLOVER);
	}

	@Override
	public void reload() {
		publishEvent(LOG_TYPE_EVENT_RELOAD);
	}

	@Override
	public void flush() {
		publishEvent(LOG_TYPE_EVENT_FLUSH);
	}

	void flushAndWait() {
		publishEventAndWait(LOG_TYPE_EVENT_FLUSH, 1000);
	}

	@Override
	public void close() {
		// stop async worker
		publishEvent(LOG_TYPE_EVENT_FLUSH);
		publishEventAndWait(LOG_TYPE_EVENT_CLOSE, 2000);

		// 对应卡住的情况
		if (worker.getState() != State.TERMINATED) {
			try {
				worker.interrupt();
				worker.join(2000);
			} catch (InterruptedException e) {
				// quietly
			}
		}

		// 将代理的 appender 换成 NoOpAppender
		EagleEyeAppender appender0 = this.appender;
		this.appender = new NoOpAppender();
		appender0.close();

		EagleEye.selfLog("[INFO] closed AsyncAppender: " + this);
	}

	@Override
	public void cleanup() {
		this.appender.cleanup();
	}

	private void publishEvent(int eventType) {
		final BaseContext event = new BaseContext(eventType); // ctx.isEvent==true
		append(event);
	}

	private void publishEventAndWait(int eventType, long timeoutMillis) {
		BaseContext event = new BaseContext(eventType);
		event.setRpcType(DO_EVENT_NOTIFY);
		// 提前做 event synchronize 是为了保证让 event notify 发生在 wait 之后
		synchronized (event) {
			if (append(event)) {
				// 为了减少等待时间，插入后如果发现异步线程没有运行，尝试唤醒之
				if (!running.get() && lock.tryLock()) {
					try {
						notEmpty.signal();
					} catch (Exception e) {
						// quietly
					} finally {
						lock.unlock();
					}
				}
				try {
					event.wait(timeoutMillis);
				} catch (Exception e) {
					// quietly
				}
			}
		}
	}

	@Override
	public String getOutputLocation() {
		return this.appender.getOutputLocation();
	}

	EagleEyeAppender getEagleEyeAppender() {
		return appender;
	}

	void setEagleEyeAppender(EagleEyeAppender appender) {
		this.appender = EagleEyeCoreUtils.checkNotNull(appender, "appender");
	}

	class AsyncRunnable implements Runnable {

		private final FastException closeEvent = new FastException("Shutdown AsyncRunnable");

		public void run() {
			final AsyncAppender parent = AsyncAppender.this;
			final int indexMask = parent.indexMask;
			final int queueSize = parent.queueSize;
			final BaseContextEncoder encoder = parent.encoder;
			final String workerName = parent.workerName;
			final BaseContext[] entries = parent.entries;
			final AtomicLong putIndex = parent.putIndex;
			final AtomicLong takeIndex = parent.takeIndex;
			final AtomicLong discardCount = parent.discardCount;
			final AtomicBoolean running = parent.running;
			final ReentrantLock lock = parent.lock;
			final Condition notEmpty = parent.notEmpty;

			// 输出丢弃的日志数
			final long outputSpan = TimeUnit.MINUTES.toMillis(1);
			long lastOutputTime = System.currentTimeMillis();
			BaseContext ctx = null;
			long now;

			for (;;) {
				try {
					running.set(true);
					long take = takeIndex.get();
					long size = putIndex.get() - take;
					if (size > 0) {
						// 直接批量处理掉 size 个日志对象
						do {
							final int idx = (int) take & indexMask;
							ctx = entries[idx];
							// 从生产者 claim 到 putIndex 位置，到生产者把日志对象放入队列之间，有可能存在间隙
							while (ctx == null) {
								Thread.yield();
								ctx = entries[idx];
							}
							entries[idx] = null;
							takeIndex.set(++take); // 单个消费者，无需用 CAS
							--size;
							processContext(ctx, parent.appender, encoder);
						} while (size > 0);

						long discardNum = discardCount.get();
						if (discardNum > 0 &&
								(now = System.currentTimeMillis()) - lastOutputTime > outputSpan) {
							discardNum = discardCount.get();
							discardCount.lazySet(0); // 无需内存屏障，统计的数量稍微丢失一点
							EagleEye.selfLog("[WARN] " + workerName + " discarded " + discardNum +
									" logs, queueSize=" + queueSize);
							lastOutputTime = now;
						}

						// 写完一批日志之后，做一次刷新
						parent.appender.flush();
					} else {
						if (lock.tryLock()) {
							try {
								running.set(false);
								notEmpty.await(1, TimeUnit.SECONDS);
							} finally {
								lock.unlock();
							}
						}
					}
				} catch (InterruptedException e) {
					EagleEye.selfLog("[INFO] " + workerName + " async thread is iterrupted");
					break;
				} catch (Throwable t) {
					if (t == closeEvent) {
						break;
					} else {
						EagleEye.selfLog("[ERROR] fail to async write log " + workerName, t);
					}
				}
			}
			running.set(false);

			EagleEye.selfLog("[INFO] " + workerName + " async thread is exited");

			// 退出
			if (ctx != null && ctx.getRpcType() == LOG_TYPE_EVENT_CLOSE) {
				doNotifyIfRequired(ctx);
			}
		}

		private final void processContext(final BaseContext ctx,
				final EagleEyeAppender appender, final BaseContextEncoder encoder) throws IOException {
			if (ctx.isEvent()) {
				final int logType = ctx.logType;
				if (logType == LOG_TYPE_EVENT_FLUSH) {
					appender.flush();
					doNotifyIfRequired(ctx);
				} else if (logType == LOG_TYPE_EVENT_ROLLOVER) {
					appender.rollOver();
					doNotifyIfRequired(ctx);
				} else if (logType == LOG_TYPE_EVENT_RELOAD) {
					appender.reload();
					doNotifyIfRequired(ctx);
				} else if (logType == LOG_TYPE_EVENT_CLOSE) {
					throw closeEvent;
				}
			} else {
				encoder.encode(ctx, appender);
			}
		}

		private final void doNotifyIfRequired(final BaseContext ctx) {
			if (ctx.getRpcType() == DO_EVENT_NOTIFY) {
				synchronized (ctx) {
					try {
						ctx.notifyAll();
					} catch (Exception e) {
						// quietly
					}
				}
			}
		}
	}

	@Override
	public String toString() {
		return "AsyncAppender [appender=" + appender + "]";
	}
}
