package com.taobao.eagleeye;

import java.lang.reflect.Method;

import javax.servlet.http.HttpServletResponse;

class HttpContextDataExposerFactory {

	public static final HttpContextDataExposer createDataExposer(HttpServletResponse httpResponse) {
		if (isGreaterThanVersion3(httpResponse)) {
			try {
				HttpContextDataExposer exposer = (HttpContextDataExposer) Class.forName(
						"com.taobao.eagleeye.HttpContextDataExposerImplV3").newInstance();
				exposer.getStatusCode(httpResponse);
				return exposer;
			} catch (Throwable t) {
				// quietly
			}
		}
		return new HttpContextDataExposerImplV2();
	}

	private static final boolean isGreaterThanVersion3(HttpServletResponse httpResponse) {
		if (httpResponse != null) {
			// 判断 httpResponse 有没有 getStatus 这个方法
			Class<? extends HttpServletResponse> responseClass = httpResponse.getClass();
			Class<?> clazz = responseClass;
			while (clazz != null && !clazz.equals(Object.class)) {
				try {
					Method method = clazz.getDeclaredMethod("getStatus");
					if (method != null) {
						Object result = method.invoke(httpResponse);
						if (result != null) {
							return true;
						}
					}
				} catch (Throwable t) {
					// quiet
				}
				clazz = clazz.getSuperclass();
			}
		}
		return false;
	}
}
