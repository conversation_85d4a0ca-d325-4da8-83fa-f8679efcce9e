package com.taobao.eagleeye;

import java.io.IOException;

import javax.servlet.Filter;
import javax.servlet.FilterChain;
import javax.servlet.FilterConfig;
import javax.servlet.ServletException;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 二套环境在 web.xml 中加入如下配置：
 * <pre>
 * {@code
 * <filter>
 *    <filter-name>EagleEyeFilter</filter-name>
 *    <filter-class>com.taobao.eagleeye.EagleEyeFilter</filter-class>
 * </filter>
 *
 * <filter-mapping>
 *    <filter-name>EagleEyeFilter</filter-name>
 *    <url-pattern>/*</url-pattern>
 * </filter-mapping>
 * }
 * </pre>
 *
 * <b>线上环境在  web.xml 中加入如下配置，注意：如果使用了 tbsession，无需再添加 EagleEyeFilter：</b>
 * <pre>
 * {@code
 * <filter>
 *    <filter-name>EagleEyeFilter</filter-name>
 *    <filter-class>com.taobao.eagleeye.EagleEyeFilter</filter-class>
 *    <init-param>
 *       <param-name>useLocalIp</param-name>
 *       <param-value>true</param-value>
 *    </init-param>
 * </filter>
 *
 * <filter-mapping>
 *    <filter-name>EagleEyeFilter</filter-name>
 *    <url-pattern>/*</url-pattern>
 * </filter-mapping>
 * }
 * </pre>
 */
public class EagleEyeFilter implements Filter {

	private static final String USE_LOCAL_IP = "useLocalIp";

	private boolean useLocalIp = false;

	@Override
	public void destroy() {
	}

	@Override
	public void doFilter(ServletRequest request, ServletResponse response,
			FilterChain chain) throws IOException, ServletException {
		if (!(request instanceof HttpServletRequest && response instanceof HttpServletResponse)) {
			chain.doFilter(request, response);
			return;
		}

		HttpServletRequest httpRequest = (HttpServletRequest) request;
		HttpServletResponse httpResponse = (HttpServletResponse) response;
		try {
			String ip = null;
			if (!useLocalIp) {
				ip = EagleEyeRequestTracer.getRemoteAddress(httpRequest);
			}
			String traceId = EagleEyeRequestTracer.getTraceId(httpRequest, ip);
			EagleEyeRequestTracer.startTrace(traceId, httpRequest, httpResponse);
		} catch (Throwable e) {
			EagleEye.selfLog("[ERROR] fail to startTrace from EagleEyeFilter", e);
		}

		try {
			chain.doFilter(request, httpResponse);
		} finally {
			EagleEyeRequestTracer.endTrace(httpRequest, httpResponse);
		}
	}

	@Override
	public void init(FilterConfig filterConfig) throws ServletException {
		String uselocal = filterConfig.getInitParameter(USE_LOCAL_IP);
		if(uselocal != null && "true".equals(uselocal)){
			useLocalIp = true;
		}
		EagleEye.selfLog("[INFO] " + this.getClass().getSimpleName() +
				" initialized successfully, useLocalIp=" + useLocalIp);
	}
}
