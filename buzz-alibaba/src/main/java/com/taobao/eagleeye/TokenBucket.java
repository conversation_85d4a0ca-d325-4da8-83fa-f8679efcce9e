package com.taobao.eagleeye;

import java.util.concurrent.atomic.AtomicLong;

/**
 * 基于令牌桶的 TPS 限流策略
 *
 * <AUTHOR>
 */
class TokenBucket {

	/**
	 * 令牌桶最大令牌数
	 */
	private final long maxTokens;

	/**
	 * 令牌桶回填周期，单位：毫秒
	 */
	private final long intervalMillis;

	/**
	 * 下次更新令牌桶的时间
	 */
	private volatile long nextUpdate;

	/**
	 * 令牌桶的令牌数
	 */
	private AtomicLong tokens;

	public TokenBucket(long maxTokens, long intervalMillis) {
		if (maxTokens <= 0) {
			throw new IllegalArgumentException("maxTokens 需要大于 0: " + maxTokens);
		}
		if (intervalMillis < 1000) {
			throw new IllegalArgumentException("intervalMillis 需要大于等于 1000 毫秒: " + intervalMillis);
		}
		this.maxTokens = maxTokens;
		this.intervalMillis = intervalMillis;
		this.nextUpdate = System.currentTimeMillis() / 1000 * 1000 + intervalMillis;
		this.tokens = new AtomicLong(maxTokens);
	}

	public boolean accept(long now) {
		// 时间已到，需要先向令牌桶补令牌
		long currTokens;
		if (now > nextUpdate) {
			currTokens = tokens.get();
			if (tokens.compareAndSet(currTokens, maxTokens)) {
				nextUpdate = System.currentTimeMillis() / 1000 * 1000 + intervalMillis;
			}
		}

		// 尝试获得一个令牌
		do {
			currTokens = tokens.get();
		} while (currTokens > 0 && !tokens.compareAndSet(currTokens, currTokens - 1));

		return currTokens > 0;
	}
}
