package com.taobao.eagleeye;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * 在线程上下文切换时，将当前 TraceId 输出到文件
 * /var/tmp/eagleeye/traceId/$procId/$threadId 里面
 *
 * <AUTHOR>
 * @since 1.4.0
 */
public class TraceFileIntercepter extends EagleEyeContextListener {

	private static final String DEFAULT_TRACE_FILE_BASE_PATH = "/var/tmp/eagleeye/trace/";

	/**
	 * ${EAGLEEYE.LOG.TRACEFILE.BASEPATH}/$procId/
	 */
	private final File traceFileDirectory = initTraceFileDirectory();

	private static final TraceFileIntercepter singleton = new TraceFileIntercepter();

	private TraceFileIntercepter() {
	}

	public static TraceFileIntercepter getInstance() {
		return singleton;
	}

	@Override
	public void beforeSet(RpcContext_inner context) {
		RpcContext_inner old = EagleEye.getRpcContext();
		if (context == null || context.getTraceId() == null) {
			// 清空 TraceId 记录，因为线程的本次链路调用已执行完毕
			clearTraceFile();
		} else if (old != null) {
			if (!context.getTraceId().equals(old.getTraceId())) {
				// 和原来是不一样的 TraceId，覆盖写入
				writeTraceFile(context.getTraceId());
			} else {
				// 同一个 TraceId 的上下文变化，不需要更新文件
				;
			}
		} else {
			writeTraceFile(context.getTraceId());
		}
	}

	@Override
	public void afterListenerRemoval() {
		deleteTraceFileDirectory();
	}

	@Override
	public void beforeEagleEyeShutdown() {
		deleteTraceFileDirectory();
	}

	private void clearTraceFile() {
		writeTraceFile(EagleEyeCoreUtils.EMPTY_STRING);
	}

	private void deleteTraceFileDirectory() {
		EagleEyeCoreUtils.deleteQuietly(traceFileDirectory);
	}

	private void writeTraceFile(String data) {
		String tid = String.valueOf(Thread.currentThread().getId());

		File file = new File(traceFileDirectory, tid);
		FileOutputStream fos = null;
		try {
			traceFileDirectory.mkdirs();
			fos = new FileOutputStream(file, false);
			fos.write(data.getBytes());
		} catch (Exception e) {
			// quietly
		} finally {
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					// quietly
				}
			}
		}
	}

	private static File initTraceFileDirectory() {
		String traceFilePath = EagleEyeCoreUtils.getSystemProperty("EAGLEEYE.LOG.TRACEFILE.BASEPATH");

		if (EagleEyeCoreUtils.isNotEmpty(traceFilePath)) {
			if (!traceFilePath.endsWith(File.separator)) {
				traceFilePath += File.separator;
			}
		} else {
			traceFilePath = DEFAULT_TRACE_FILE_BASE_PATH;
		}

		traceFilePath += (EagleEyeCoreUtils.getCurrrentPid() + File.separator);
		return new File(traceFilePath);
	}
}
