package com.taobao.eagleeye;

import com.taobao.eagleeye.json.EagleEyeJSONImpl;

/**
 * 对象 DUMP 日志的相关实现，支持 ATP 项目对象通用输出为 JSON
 *
 * <AUTHOR>
 * <AUTHOR>
 * @since 1.3.0
 */
class AtpTraceClient {

	private static final TraceLogger traceLogger = EagleEye.traceLogger("atp");

	private static final String DUMP_SIGN = "dump";

	private static final char ATP_PARAM_SEPARATOR = (char) 0x12;

	private static final char ATP_OBJECT_SEPARATOR = (char) 0x14;

	private static final int DEFAULT_BUFFER_SIZE = 4096;

	private static volatile boolean forceLogDump = false;

	/**
	 * 设置一个开关，打开的效果是不管是否当前是否透传 dump=1，都会触发 logDump。
	 * 这个功能一般用于排查某台机器的调用情况比较有用
	 *
	 * @param enableForceLogDump
	 */
	static void setEnableForceLogDump(boolean enableForceLogDump) {
		if (enableForceLogDump != forceLogDump) {
			if (enableForceLogDump) {
				EagleEye.selfLog("[INFO] force log dump is enabled");
				forceLogDump = true;
			} else {
				EagleEye.selfLog("[INFO] force log dump is disabled");
				forceLogDump = false;
			}
		}
	}

	/**
	 * 检查当前上下文是否需要做日志 DUMP
	 * @return
	 */
	static boolean isContextDumpEnabled() {
		return EagleEye.isLogDumpEnabled() &&
				(EagleEyeCoreUtils.parseUserDataNumber(EagleEye.getRpcContext(), DUMP_SIGN) > 0
						|| forceLogDump);
	}

	/**
	 * 将本次调用的参数、业务对象输出到日志中
	 * @param appId 应用标识
	 * @param operationKey 业务操作字段，或调用的服务名、方法名
	 * @param obj 业务返回值，或业务对象，可以输入 <code>null</code>，或者 {@link Void#TYPE}
	 * @param params 业务参数列表，可以为空
	 */
	static void doTrace(String appId, String operationKey, Object obj, Object... params) {
		StringBuilder appender = new StringBuilder(DEFAULT_BUFFER_SIZE);
		try {
			if (null != params && params.length > 0) {
				appendObj(params[0], appender);
				for (int i = 1; i < params.length; ++i) {
					appender.append(ATP_PARAM_SEPARATOR);
					appendObj(params[i], appender);
				}
			}
			appender.append(ATP_OBJECT_SEPARATOR);
			appendObj(obj, appender);
			traceLogger.logLine(appId, operationKey, appender.toString());
		} catch (Exception e) {
			EagleEye.selfLog("[WARN] AtpTraceClient exception, appId=" + appId +
					", operationKey=" + operationKey, e);
		}
	}

	private static void appendObj(Object obj, StringBuilder appender) {
		if (Void.TYPE == obj) {
			appender.append("VOID");
		} else if (null == obj) {
			appender.append("NULL");
		} else {
			appender.append(EagleEyeJSONImpl.toJSONString(obj));
		}
	}
}
