package com.taobao.eagleeye;

public abstract class EagleEyeAppender {
    /**
     * 写日志
     * @param log
     */
    public abstract void append(String log);

    /**
     * 刷新输出缓冲区
     */
    public void flush() {
    	// do nothing
    }

    /**
     * 触发滚动
     */
    public void rollOver() {
    	// do nothing
    }

    /**
     * 重新加载，这是为了防止在多进程环境并发写同一个文件导致问题，
     * 这个操作本身包含 {@link #flush()} 语义
     * @since 1.3.0
     */
    public void reload() {
    	// do nothing
    }

    /**
     * 关闭输出，释放资源
     * @since 1.3.0
     */
    public void close() {
    	// do nothing
    }

    /**
     * 清理释放资源，一般用于清理滚动后要删除的文件，或者临时文件
     * @since 1.3.0
     */
    public void cleanup() {
    	// do nothing
    }

    /**
     * 返回输出位置，如果不存在，返回 <code>null</code>
     * @return
     * @since 1.3.5
     */
    public String getOutputLocation() {
    	return null;
    }
}
