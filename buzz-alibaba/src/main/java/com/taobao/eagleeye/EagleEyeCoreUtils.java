package com.taobao.eagleeye;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.lang.management.ManagementFactory;
import java.lang.management.RuntimeMXBean;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.zip.CRC32;

final class EagleEyeCoreUtils {

	public static final String EMPTY_STRING = "";
	public static final String NEWLINE = "\r\n";

	public static final String[] EMPTY_STRING_ARRAY = new String[0];

	private static final String LOCAL_IP_ADDRESS = getLocalInetAddress();
	private static final int PID = doGetCurrrentPid();
	private static final String PID_STR = String.valueOf(PID);

	public static boolean isBlank(String str) {
		int strLen;
		if (str == null || (strLen = str.length()) == 0) {
			return true;
		}
		for (int i = 0; i < strLen; i++) {
			if ((Character.isWhitespace(str.charAt(i)) == false)) {
				return false;
			}
		}
		return true;
	}

	public static String checkNotNullEmpty(String value, String name) throws IllegalArgumentException {
		if (isBlank(value)) {
			throw new IllegalArgumentException(name + " is null or empty");
		}
		return value;
	}

	public static <T> T checkNotNull(T value, String name) throws IllegalArgumentException {
		if (value == null) {
			throw new IllegalArgumentException(name + " is null");
		}
		return value;
	}

	public static <T> T defaultIfNull(T value, T defaultValue) {
		return (value == null) ? defaultValue : value;
	}

	public static boolean isNotBlank(String str) {
		return !isBlank(str);
	}

	public static boolean isNotEmpty(String str) {
		return str != null && str.length() > 0;
	}

	public static String trim(String str) {
		return str == null ? null : str.trim();
	}

	public static String[] split(String str, char separatorChar) {
		return splitWorker(str, separatorChar, false);
	}

	private static String[] splitWorker(String str, char separatorChar, boolean preserveAllTokens) {
		if (str == null) {
			return null;
		}
		int len = str.length();
		if (len == 0) {
			return EMPTY_STRING_ARRAY;
		}
		List<String> list = new ArrayList<String>();
		int i = 0, start = 0;
		boolean match = false;
		boolean lastMatch = false;
		while (i < len) {
			if (str.charAt(i) == separatorChar) {
				if (match || preserveAllTokens) {
					list.add(str.substring(start, i));
					match = false;
					lastMatch = true;
				}
				start = ++i;
				continue;
			}
			lastMatch = false;
			match = true;
			i++;
		}
		if (match || (preserveAllTokens && lastMatch)) {
			list.add(str.substring(start, i));
		}
		return (String[]) list.toArray(new String[list.size()]);
	}

	public static StringBuilder appendWithBlankCheck(String str, String defaultValue, StringBuilder appender) {
		if (isNotBlank(str)) {
			appender.append(str);
		} else {
			appender.append(defaultValue);
		}
		return appender;
	}

	public static StringBuilder appendWithNullCheck(Object obj, String defaultValue, StringBuilder appender) {
		if (obj != null) {
			appender.append(obj.toString());
		} else {
			appender.append(defaultValue);
		}
		return appender;
	}

	/**
	 * 追加日志，同时过滤字符串中的换行为空格，避免导致日志格式解析错误
	 */
	public static StringBuilder appendLog(String str, StringBuilder appender, char delimiter) {
		if (str != null) {
			int len = str.length();
			appender.ensureCapacity(appender.length() + len);
			for (int i = 0; i < len; i++) {
				char c = str.charAt(i);
				if (c == '\n' || c == '\r' || c == delimiter) {
					// 用 appender.append(str, start, len) 批量 append 实质也是一个字符一个字符拷贝
					// 因此此处还是用土办法
					c = ' ';
				}
				appender.append(c);
			}
		}
		return appender;
	}

	/**
	 * 过滤字符串中的换行为空格，避免导致日志格式解析错误
	 * @param str
	 * @return
	 * @see #appendLog(String, StringBuilder)
	 */
	public static String filterInvalidCharacters(String str) {
		StringBuilder appender = new StringBuilder(str.length());
		return appendLog(str, appender, '|').toString();
	}

	/**
	 * 对字符串生成摘要，目前使用 CRC32 算法
	 * @param str
	 * @return 摘要后的字符串
	 */
	public static String digest(String str) {
		CRC32 crc = new CRC32();
		crc.update(str.getBytes());
		return Long.toHexString(crc.getValue());
	}

    // 自身日志的时间标签格式化器
	private static final ThreadLocal<FastDateFormat> dateFmt = new ThreadLocal<FastDateFormat>() {
		@Override
		protected FastDateFormat initialValue() {
			return new FastDateFormat();
		}
	};

	/**
	 * 时间格式化成 yyyy-MM-dd HH:mm:ss.SSS
	 * @param timestamp
	 * @return
	 */
	public static String formatTime(long timestamp) {
		return dateFmt.get().format(timestamp);
	}

	/**
	 * 从 URL 解析出 URI，如果解析不到 URI，就用域名，例如：
	 * <pre>
	 * "http://shop66155774.taobao.com/shop/view_shop.htm" => "/shop/view_shop.htm"
	 * "http://shop66155774.taobao.com/shop/" => "/shop"
	 * "http://www.taobao.com" => "www.taobao.com"
	 * "http://www.taobao.com/" => "www.taobao.com"
	 * "https://www.alipay.com/" => "www.alipay.com"
	 * "not_url_entrance" => "not_url_entrance"
	 * </pre>
	 * @param url
	 * @return
	 */
	public static String getUriFromUrl(String url) {
		int start;
		final int len = url.length();
		if (len <= 7) {
			return url;
		}
		if (url.startsWith("http://")) {
			start = 7;
		} else if ((start = url.indexOf("://")) != -1) {
			start += 3;
		} else {
			start = 0;
		}

		// 去掉末尾的 ‘/’
		final int end = (url.charAt(len - 1) == '/') ? (len - 1) : len;
		final int istart = url.indexOf('/', start);
		if (istart >= 0 && istart < end) {
			return url.substring(istart, end);
		}
		return url.substring(start, end);
	}

    private static String getLocalInetAddress() {
		String localIp = EagleEyeCoreUtils.getSystemProperty("EAGLEEYE.LOCAL.IP");
		if (EagleEyeCoreUtils.isNotBlank(localIp) && localIp.length() >= 7
				&& Character.isDigit(localIp.charAt(0))
				&& Character.isDigit(localIp.charAt(localIp.length() - 1))) {
			return localIp;
		}
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            InetAddress address = null;
            while (interfaces.hasMoreElements()) {
                NetworkInterface ni = interfaces.nextElement();
                // 排除掉虚拟网卡的 IP，@since *******
                String displayName = ni.getDisplayName();
                if (displayName != null && displayName.startsWith("virbr")) {
                	continue;
                }
                Enumeration<InetAddress> addresses = ni.getInetAddresses();
                while (addresses.hasMoreElements()) {
                    address = addresses.nextElement();
                    if (!address.isLoopbackAddress() && address.getHostAddress().indexOf(":") == -1) {
                        return address.getHostAddress();
                    }
                }
            }
        } catch (Throwable t) {
        }
        return "127.0.0.1";
    }

    public static String getLocalAddress() {
    	return LOCAL_IP_ADDRESS;
    }

    /**
     * 从 userData 中解析配置的参数，仅支持一个字符的数字字符串
     */
    public static int parseUserDataNumber(RpcContext_inner ctx, String userDataKey) {
    	final String str;
    	final char firstChar;
    	if (ctx == null || (str = ctx.getUserData(userDataKey)) == null
    			|| str.length() != 1 || (firstChar = str.charAt(0)) < '0' || firstChar > '9') {
    		return 0;
    	}
    	return firstChar - '0';
    }

	/**
	 * 根据 TraceId 检查是否被采样，有效范围在 [1, 9999] 之间，超出范围的数值都作为全采样处理
	 *
	 * @param traceId
	 * @param samplingInterval 采样频率 1/x，有效范围在 [1, 9999] 之间，超出范围的数值都作为全采样处理
	 * @return <code>true</code> 则需要输出日志，<code>false</code> 不输出
	 * @since 1.3.0
	 */
	public static boolean isTraceSampled(String traceId, int samplingInterval) {
		if (traceId == null) {
			return false;
		}
		if (samplingInterval <= 1 || samplingInterval >= 10000) {
			return true;
		}
		final int len = traceId.length();
		if (len < 25) {
			return traceId.hashCode() % samplingInterval == 0;
		}
		if (len >= 26 && traceId.charAt(25) == 'f') {
			return true;
		}
		int count = traceId.charAt(21) - '0';
		count = count * 10 + traceId.charAt(22) - '0';
		count = count * 10 + traceId.charAt(23) - '0';
		count = count * 10 + traceId.charAt(24) - '0';
		return count % samplingInterval == 0;
	}

	/**
	 * get current pid,max pid 32 bit systems 32768, for 64 bit 4194304
	 * http://unix.stackexchange.com/questions/16883/what-is-the-maximum-value-of-the-pid-of-a-process
	 *
	 * http://stackoverflow.com/questions/35842/how-can-a-java-program-get-its-own-process-id
	 * @return
	 */
	private static int doGetCurrrentPid() {
		try {
			RuntimeMXBean runtime = ManagementFactory.getRuntimeMXBean();
			String name = runtime.getName();
			return Integer.parseInt(name.substring(0, name.indexOf('@')));
		} catch (Throwable t) {
			return 0;
		}
	}

	public static int getCurrrentPid() {
		return PID;
	}

	public static String getCurrrentPidString() {
		return PID_STR;
	}

	public static String getSystemProperty(String key) {
		try {
			return System.getProperty(key);
		} catch (Throwable t) {
			return null;
		}
	}

	public static long getSystemPropertyForLong(String key, long defaultValue) {
		try {
			return Long.parseLong(System.getProperty(key));
		} catch (Throwable t) {
			return defaultValue;
		}
	}

	/**
	 * 验证 traceId 是否合法
	 * @param traceId
	 * @return
	 * @since *******
	 */
	public static boolean isValidTraceId(String traceId) {
		int len;
		if (traceId == null || (len = traceId.length()) == 0) {
			return false;
		}
		if (len < 25) {
			return handleOldVersion(traceId);
		}
		if (len > 48) {
			return false;
		}

		for (int i = 0; i < 8; ++i) {
			if (!isHexNumeric(traceId.charAt(i))) {
				return false;
			}
		}
		char firstTimeChar = traceId.charAt(8);
		if (firstTimeChar < '1' || firstTimeChar > '3') {
			// 时间戳判断能用 102 年的范围
			return false;
		}
		for (int i = 9; i < 25; ++i) {
			if (!isNumeric(traceId.charAt(i))) {
				return false;
			}
		}
		for (int i = 25; i < len; ++i) {
			if (!isHexNumeric(traceId.charAt(i))) {
				return false;
			}
		}
		return true;
	}

	private static boolean handleOldVersion(String traceId) {
		if (traceId.charAt(0) == '0') {
			return false;
		}
		for (int i = 0; i < traceId.length(); ++i) {
			if (!isHexNumeric(traceId.charAt(i))) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 验证 rpcId 是否合法
	 * @param rpcId
	 * @return
	 * @since *******
	 */
	public static boolean isValidRpcId(String rpcId) {
		int len;
		if (rpcId == null || (len = rpcId.length()) == 0) {
			return false;
		}
		if (len > 64) {
			return false;
		}
		if (!isNumeric(rpcId.charAt(0))) {
			return false;
		}
		for (int i = 1; i < len; ++i) {
			final char ch = rpcId.charAt(i);
			if (!isNumeric(ch) && ch != '.') {
				return false;
			}
		}
		return true;
	}

	public static final boolean isHexNumeric(char ch) {
		return (ch >= 'a' && ch <= 'f') || (ch >= '0' && ch <= '9');
	}

	public static final boolean isNumeric(char ch) {
		return ch >= '0' && ch <= '9';
	}

	/**
	 * 删除 file，如果是目录，会先递归删除目录内的文件，再删除目录本身
	 * @param file
	 * @return
	 */
	public static final boolean deleteQuietly(File file) {
        if (file == null) {
            return false;
        }

        try {
            if (file.isDirectory()) {
                File[] files = file.listFiles();
                if (files != null) {
                	for (File f : files) {
                		deleteQuietly(f);
                	}
                }
            }
        } catch (Exception ignored) {
        	// quietly
        }

        try {
            return file.delete();
        } catch (Exception ignored) {
            return false;
        }
	}

	/**
	 * 读取文件的第一行内容
	 * @param file
	 * @return
	 */
	public static final String readLineFile(File file) {
		BufferedReader br = null;
		try {
			br = new BufferedReader(new FileReader(file), 128);
			return br.readLine();
		} catch (Exception e) {
		} finally {
			if (br != null) {
				try {
					br.close();
				} catch (IOException e) {
					// quietly
				}
			}
		}
		return null;
	}

	public static final void shutdownThreadPool(ExecutorService pool, long awaitTimeMillis) {
		try {
			pool.shutdown();

			boolean done = false;
			if (awaitTimeMillis > 0) {
				try {
					done = pool.awaitTermination(awaitTimeMillis, TimeUnit.MILLISECONDS);
				} catch (Exception e) {
				}
			}

			if (!done) {
				pool.shutdownNow();
			}
		} catch (Exception e) {
			// quietly
		}
	}

	// Unsafe mechanics
    @SuppressWarnings("restriction")
	private static final sun.misc.Unsafe UNSAFE = doGetUnsafe();

    @SuppressWarnings("restriction")
    public static sun.misc.Unsafe getUnsafe() {
    	return UNSAFE;
    }

    /**
     * Returns a sun.misc.Unsafe. Suitable for use in a 3rd party package.
     * Replace with a simple call to Unsafe.getUnsafe when integrating
     * into a jdk.
     *
     * @return a sun.misc.Unsafe
     */
    @SuppressWarnings("restriction")
    private static sun.misc.Unsafe doGetUnsafe() {
        try {
            return sun.misc.Unsafe.getUnsafe();
        } catch (Throwable tryReflectionInstead) {}
        try {
            return java.security.AccessController.doPrivileged
            (new java.security.PrivilegedExceptionAction<sun.misc.Unsafe>() {
                public sun.misc.Unsafe run() throws Exception {
                    Class<sun.misc.Unsafe> k = sun.misc.Unsafe.class;
                    for (java.lang.reflect.Field f : k.getDeclaredFields()) {
                        f.setAccessible(true);
                        Object x = f.get(null);
                        if (k.isInstance(x))
                            return k.cast(x);
                    }
                    throw new NoSuchFieldError("the Unsafe");
                }});
        } catch (Throwable t) {
            return null;
        }
    }
}
