package com.taobao.eagleeye;

/**
 * 遇到 EagleEye 非法上下文时抛出的异常
 * <AUTHOR>
 * @since 1.4.0.1
 */
public class IllegalContextException extends RuntimeException {

	private static final long serialVersionUID = 1L;

	private static IllegalContextException instance =
			new IllegalContextException("Illegal EagleEye Context");

	public static IllegalContextException getInstance() {
		return instance;
	}

	private IllegalContextException(String message) {
		super(message);
	}

	public synchronized Throwable fillInStackTrace() {
		return null;
	}
}
