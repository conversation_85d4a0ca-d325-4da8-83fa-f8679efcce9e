package com.taobao.eagleeye;

/**
 * 基础上下文信息
 */
class BaseContext {

	private static final int EVENT_TYPE = -1;

	final String traceId;
	final String rpcId;

	String traceName = EagleEyeCoreUtils.EMPTY_STRING;

	String serviceName = EagleEyeCoreUtils.EMPTY_STRING;
	String methodName = EagleEyeCoreUtils.EMPTY_STRING;
	String callBackMsg = null;
	/** @since 1.3.5 老版本是重叠在其它字段上面的 */
	String resultCode = EagleEyeCoreUtils.EMPTY_STRING;

	long logTime = 0L;
	int logType = 0;
	int rpcType = 0;

	BaseContext(String _traceId, String _rpcId) {
		this.traceId = _traceId;
		this.rpcId = _rpcId;
	}

	// log control event ctx
	BaseContext(int logType) {
    	this(EagleEyeCoreUtils.EMPTY_STRING, EagleEyeCoreUtils.EMPTY_STRING);
    	this.logType = logType;
    	this.logTime = EVENT_TYPE;
    }

	/**
	 * 判断是否为事件类型
	 * @return
	 */
	final boolean isEvent() {
		return logTime == EVENT_TYPE;
	}

	/**
	 * @since 1.3.0
	 */
	public String getServiceName() {
		return serviceName;
	}

	/**
	 * @since 1.3.0
	 */
	public void setServiceName(String serviceName) {
		this.serviceName = serviceName;
	}

	/**
	 * @since 1.3.0
	 */
	public String getMethodName() {
		return methodName;
	}

	/**
	 * @since 1.3.0
	 */
	public void setMethodName(String methodName) {
		this.methodName = methodName;
	}

	/**
	 * @since 1.3.0
	 */
	public long getLogTime() {
		return logTime;
	}

	/**
	 * @since 1.3.0
	 */
	public void setLogTime(long logTime) {
		this.logTime = logTime;
	}

	/**
	 * @since 1.3.0
	 */
	public int getRpcType() {
		return rpcType;
	}

	/**
	 * @since 1.3.0
	 */
	public void setRpcType(int rpcType) {
		this.rpcType = rpcType;
	}

	/**
	 * 获取上下文的 TraceId
	 * @since 1.2.6
	 */
	public String getTraceId() {
		return traceId;
	}
	/**
	 * 获取上下文的 RpcId
	 * @since 1.2.6
	 */
	public String getRpcId() {
		return rpcId;
	}

	/**
	 * 获取附加信息
	 * @since 1.2.0
	 */
	public String getCallBackMsg() {
		return callBackMsg;
	}

	/**
	 * 设置附加信息
	 * @since 1.2.0
	 */
	public void setCallBackMsg(String callBackMsg) {
		this.callBackMsg = callBackMsg;
	}

	/**
	 * 获取 traceName
	 * @since 1.3.4
	 */
	public String getTraceName() {
		return traceName;
	}

	/**
	 * 设置 traceName
	 * @since 1.3.5
	 */
	public void setTraceName(String traceName) {
		this.traceName = traceName;
	}

	/**
	 * 获取结果状态码
	 * @since 1.3.5
	 */
	public String getResultCode() {
		return resultCode;
	}

	/**
	 * 设置结果状态码
	 * @since 1.3.5
	 */
	public void setResultCode(String resultCode) {
		this.resultCode = resultCode;
	}
}
