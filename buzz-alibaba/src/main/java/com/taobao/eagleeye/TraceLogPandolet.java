package com.taobao.eagleeye;

import java.util.Set;

/**
 * 提供 API 供{@code Pandolet}调用方（比如：Tomcat Monitor）使用：
 *
 * <ul>
 * <li>返回目前在内存中可用的{@code TraceLogger}</li>
 * <li>提供一个{@code TraceLogger}的名字，返回该{@code TraceLogger}的配置</li>
 * </ul>
 * <AUTHOR>
 * @since 1.3.5
 */
public class TraceLogPandolet  {

    /**
     * 返回目前在内存中可用的{@code TraceLogger}
     *
     * @return 在内存中可用的 TraceLogger 的列表
     */

    public String[] getAvailableLoggers() {
        Set<String> allLoggers = TraceLogController.getAllTraceLoggers().keySet();
        return allLoggers.toArray(new String[allLoggers.size()]);
    }

//    /**
//     * 提供一个{@code TraceLogger}的名字，返回该{@code TraceLogger}的配置
//     *
//     * @param logger TraceLogger 的名字
//     * @return 配置数据
//     */
//    @Service
//    public PandoletResponse getLoggerConfig(@Parameter(name = "loggerName") String logger) {
//        if (logger == null || logger.trim().isEmpty()) {
//            throw new IllegalStateException("request parameter logger-name is either null or empty");
//        }
//
//        ResponseBuilder responseBuilder = pandoletService.createResponseBuilder();
//
//        TraceLogger traceLogger = TraceLogController.getAllTraceLoggers().get(logger);
//        if (traceLogger == null) {
//        	return responseBuilder.status(404).status("logger not found").build();
//        }
//
//        return responseBuilder.status(200).status("success")
//        		.name("loggerName").stringValue(traceLogger.getLoggerName())
//        		.name("appenderClass").stringValue(traceLogger.getAppender().getClass().getName())
//        		.name("outputLocation").stringValue(traceLogger.getAppender().getOutputLocation())
//                .build();
//    }
//
//    /**
//     * 返回所有 {@code TraceLogger} 的位置
//     *
//     * @return 返回映射表，key 是 logger 名字，value 是 logger 输出位置
//     */
//    @Service
//    public PandoletResponse getAllLoggerLocations() {
//    	Map<String, TraceLogger> allTraceLoggers = TraceLogController.getAllTraceLoggers();
//
//    	ResponseBuilder responseBuilder = pandoletService.createResponseBuilder();
//    	responseBuilder.status(200).status("success");
//
//    	for (Entry<String, TraceLogger> entry : allTraceLoggers.entrySet()) {
//    		responseBuilder.name(entry.getKey())
//    				.stringValue(entry.getValue().getAppender().getOutputLocation());
//		}
//
//    	return responseBuilder.build();
//    }


    public String getName() {
        return "TraceLog";
    }
}
