package com.taobao.eagleeye;

import java.lang.management.ClassLoadingMXBean;
import java.lang.management.ManagementFactory;
import java.util.concurrent.TimeUnit;

/**
 * 取 JVM {@link ClassLoadingMXBean} 的信息输出
 *
 * <AUTHOR> 3/10/15 2:21 PM
 * <AUTHOR>
 * @since 1.4.2
 */
class ScheduleClassLoadingInfoExposer implements ScheduleTask {

	private static final StatLogger statLogger = EagleEye.statLoggerBuilder("eagleeye-jvm")
			.maxFileSizeMB(100).maxBackupIndex(1).buildSingleton();

	private long lastTotalLoadedClassCount = 0;
	private long lastTotalUnloadedClassCount = 0;

	@Override
	public void run() throws Exception {
        try {
        	ClassLoadingMXBean classLoadingMXBean = ManagementFactory.getClassLoadingMXBean();

        	long currentTotalLoadedClassCount = classLoadingMXBean.getTotalLoadedClassCount();
        	long recentLoadedClassCount = currentTotalLoadedClassCount - lastTotalLoadedClassCount;
        	lastTotalLoadedClassCount = currentTotalLoadedClassCount;

        	long currentTotalUnloadedClassCount = classLoadingMXBean.getUnloadedClassCount();
        	long recentUnloadedClassCount = currentTotalUnloadedClassCount - lastTotalUnloadedClassCount;
        	lastTotalUnloadedClassCount = currentTotalUnloadedClassCount;

		    statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "classloading", "all").arraySet(
		    		recentLoadedClassCount,
		    		currentTotalLoadedClassCount,
		    		classLoadingMXBean.getLoadedClassCount(), // live classes
		    		recentUnloadedClassCount,
		    		currentTotalUnloadedClassCount);
		} catch (Throwable t) {
			// quietly
		}
	}

	@Override
	public long getIntervalMillis() {
		return TimeUnit.MINUTES.toMillis(1);
	}
}
