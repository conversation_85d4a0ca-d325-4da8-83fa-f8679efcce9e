package com.taobao.eagleeye;



/**
 * 为潘多拉容器增加控制命令
 *
 * @deprecated 使用 {@link EagleEyeControlPandolet 新的 Pandolet 实现} 替代
 *
 * <AUTHOR>
 */
@Deprecated
public class PandoraCommondProvider  {

	private static final String OK = "OK";

	private static final String HELP_MESSAGE =
		"pwd                     Get EagleEye location" + EagleEyeCoreUtils.NEWLINE +
		"isBizOff                Check if service log off" + EagleEyeCoreUtils.NEWLINE +
		"turnBizOff              Turn service log off" + EagleEyeCoreUtils.NEWLINE +
		"turnBizOn               Turn service log on" + EagleEyeCoreUtils.NEWLINE +
		"isRpcOff                Check if rpc log off" + EagleEyeCoreUtils.NEWLINE +
		"turnRpcOff              Turn rpc log off" + EagleEyeCoreUtils.NEWLINE +
		"turnRpcOn               Turn rpc log on" + EagleEyeCoreUtils.NEWLINE +
		"samplingInterval        Get sampling interval" + EagleEyeCoreUtils.NEWLINE +
		"setSamplingInterval x   Set sampling interval to x" + EagleEyeCoreUtils.NEWLINE +
		"                        ([2, 9999]: 1/x; other value: all sampled)" + EagleEyeCoreUtils.NEWLINE +
		"indexes                 Export indexes" + EagleEyeCoreUtils.NEWLINE
		;


	public String getHelp() {
		return HELP_MESSAGE;
	}


	public String getHelp(String appname) {
		return HELP_MESSAGE;
	}

	public String _pwd() {
		return EagleEye.CLASS_LOCATION;
	}

	/*
	 * 日志开关
	 */
	public String _isBizOff() {
		return String.valueOf(EagleEye.isBizOff());
	}

	public String _turnBizOff() {
		EagleEye.turnBizOff();
		return OK;
	}

	public String _turnBizOn() {
		EagleEye.turnBizOn();
		return OK;
	}

	public String _isRpcOff() {
		return String.valueOf(EagleEye.isRpcOff());
	}

	public String _turnRpcOff() {
		EagleEye.turnRpcOff();
		return OK;
	}


	public String _turnRpcOn() {
		EagleEye.turnRpcOn();
		return OK;
	}

	/*
	 * 采样频率
	 */
	public String _samplingInterval() {
		return String.valueOf(EagleEye.getSamplingInterval());
	}

	public String _setSamplingInterval(String interval) {
		try {
			int v = Integer.parseInt(interval);
			EagleEye.setSamplingInterval(v);
			return OK;
		} catch (Exception e) {
			return e.getMessage();
		}
	}

	/*
	 * 编码表
	 */
	public String _indexes() {
		return EagleEye.exportIndexes();
	}
}
