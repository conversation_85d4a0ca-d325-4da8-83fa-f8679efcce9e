package com.taobao.eagleeye;

import java.util.List;
import java.util.concurrent.atomic.AtomicReference;

/**
 * 统计日志，一个统计日志的所有 {@link StatEntry 统计项} 会打印在同一个文件里面。
 * 统计日志的输出间隔是统一、固定的，不能改变。
 * @since 1.3.0
 * <AUTHOR>
 */
public final class StatLogger {

	private final String loggerName;

	private final EagleEyeAppender appender;

	/**
	 * 当前的统计数据，需要 AtomicReference 支持滚动并发
	 */
	private final AtomicReference<StatRollingData> ref;

	/**
	 * 统计间隔，单位：毫秒
	 */
	private final long intervalMillis;

	/**
	 * 同一个统计间隔内，可存放的统计项的数量
	 */
	private final int maxEntryCount;

	/**
	 * 字段分隔符
	 */
	private final char entryDelimiter;

	/**
	 * KEY 分隔符
	 */
	private final char keyDelimiter;

	/**
	 * VALUE 分隔符
	 */
	private final char valueDelimiter;

	StatLogger(String loggerName, EagleEyeAppender appender, long intervalMillis, int maxEntryCount,
			char entryDelimiter, char keyDelimiter, char valueDelimiter) {
		this.loggerName = loggerName;
		this.appender = appender;
		this.intervalMillis = intervalMillis;
		this.maxEntryCount = maxEntryCount;
		this.entryDelimiter = entryDelimiter;
		this.keyDelimiter = keyDelimiter;
		this.valueDelimiter = valueDelimiter;
		this.ref = new AtomicReference<StatRollingData>();
		rolling();
	}

	public String getLoggerName() {
		return loggerName;
	}

	EagleEyeAppender getAppender() {
		return appender;
	}

	StatRollingData getRollingData() {
		return ref.get();
	}

	long getIntervalMillis() {
		return intervalMillis;
	}

	int getMaxEntryCount() {
		return maxEntryCount;
	}

	char getEntryDelimiter() {
		return entryDelimiter;
	}

	char getKeyDelimiter() {
		return keyDelimiter;
	}

	char getValueDelimiter() {
		return valueDelimiter;
	}

	/**
	 * 触发滚动，把滚动出来的数据返回
	 * @return 滚动出来的数据，如果是 <code>null</code>，表示没有滚动
	 */
	StatRollingData rolling() {
		do {
			long now = System.currentTimeMillis();
			long timeSlot = now - now % intervalMillis;

			StatRollingData prevData = ref.get();
			long rollingTimeMillis = timeSlot + intervalMillis;
			int initialCapacity = prevData != null ? prevData.getStatCount() : 16;
			StatRollingData nextData = new StatRollingData(
					this, initialCapacity, timeSlot, rollingTimeMillis);
			if (ref.compareAndSet(prevData, nextData)) {
				return prevData;
			}
		} while (true);
	}

	/**
	 * 获取统计项
	 * @param key
	 * @return
	 */
	public StatEntry stat(String key) {
		return new StatEntry(this, key);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param key2
	 * @return
	 */
	public StatEntry stat(String key1, String key2) {
		return new StatEntry(this, key1, key2);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param key2
	 * @param key3
	 * @return
	 */
	public StatEntry stat(String key1, String key2, String key3) {
		return new StatEntry(this, key1, key2, key3);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param key2
	 * @param key3
	 * @param key4
	 * @return
	 */
	public StatEntry stat(String key1, String key2, String key3, String key4) {
		return new StatEntry(this, key1, key2, key3, key4);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param key2
	 * @param key3
	 * @param key4
	 * @param key5
	 * @return
	 */
	public StatEntry stat(String key1, String key2, String key3, String key4, String key5) {
		return new StatEntry(this, key1, key2, key3, key4, key5);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param key2
	 * @param key3
	 * @param key4
	 * @param key5
	 * @param key6
	 * @return
	 */
	public StatEntry stat(String key1, String key2, String key3, String key4, String key5, String key6) {
		return new StatEntry(this, key1, key2, key3, key4, key5, key6);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param key2
	 * @param key3
	 * @param key4
	 * @param key5
	 * @param key6
	 * @param key7
	 * @return
	 */
	public StatEntry stat(String key1, String key2, String key3, String key4, String key5, String key6, String key7) {
		return new StatEntry(this, key1, key2, key3, key4, key5, key6, key7);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param key2
	 * @param key3
	 * @param key4
	 * @param key5
	 * @param key6
	 * @param key7
	 * @param key8
	 * @return
	 */
	public StatEntry stat(String key1, String key2, String key3, String key4, String key5, String key6, String key7, String key8) {
		return new StatEntry(this, key1, key2, key3, key4, key5, key6, key7, key8);
	}

	/**
	 * 获取统计项
	 * @param key1
	 * @param moreKeys
	 * @return
	 */
	public StatEntry stat(String key1, String... moreKeys) {
		return new StatEntry(this, key1, moreKeys);
	}

	/**
	 * 获取统计项
	 * @param keys 不能为 <code>null</code> 或空
	 * @return
	 */
	public StatEntry stat(List<String> keys) {
		return new StatEntry(this, keys);
	}

	/**
	 * 获取统计项
	 * @param keys 不能为 <code>null</code> 或空
	 * @return
	 */
	public StatEntry stat(String[] keys) {
		return new StatEntry(this, keys);
	}
}
