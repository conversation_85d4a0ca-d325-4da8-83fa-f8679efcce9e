package com.taobao.eagleeye;



/**
 * {@code PandoraCommandProvider}对应于{@code Pandolet}的实现
 *
 * <AUTHOR> 1/9/15 2:44 PM
 * @since 1.3.5
 */
public class EagleEyeControlPandolet  {

    public String getEagleEyeClassLocation() {
        return EagleEye.CLASS_LOCATION;
    }

    
    public boolean isBizOff() {
        return EagleEye.isBizOff();
    }

    
    public boolean turnBizOff() {
        EagleEye.turnBizOff();
        return true;
    }

    
    public boolean turnBizOn() {
        EagleEye.turnBizOn();
        return true;
    }

    
    public boolean isRpcOff() {
        return EagleEye.isRpcOff();
    }

    
    public boolean turnRpcOff() {
        EagleEye.turnRpcOff();
        return true;
    }

    
    public boolean turnRpcOn() {
        EagleEye.turnRpcOn();
        return true;
    }

    
    public boolean isLogDumpEnabled() {
    	return EagleEye.isLogDumpEnabled();
    }

    
    public boolean setLogDumpEnabled( boolean enable) {
    	EagleEye.setLogDumpEnabled(enable);
    	return true;
    }

    
    public int getSamplingInterval() {
        return EagleEye.getSamplingInterval();
    }

    
    public boolean setSamplingInterval( int interval) {
        EagleEye.setSamplingInterval(interval);
        return true;
    }

    
    public String exportIndexes() {
        return EagleEye.exportIndexes();
    }


    public String getName() {
        return "EagleEyeControl";
    }
}
