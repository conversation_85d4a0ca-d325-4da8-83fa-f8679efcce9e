package com.taobao.eagleeye;

import static com.taobao.eagleeye.EagleEyeCoreUtils.EMPTY_STRING;
import static com.taobao.eagleeye.EagleEyeCoreUtils.appendLog;
import static com.taobao.eagleeye.EagleEyeCoreUtils.appendWithBlankCheck;
import static com.taobao.eagleeye.EagleEyeCoreUtils.appendWithNullCheck;

import java.io.Serializable;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;

/**
 * @deprecated 请使用 ulc-client 里面的 UlcLog 的相关实现
 */
@Deprecated
public class LogContext implements Serializable{

	private static final long serialVersionUID = -8830017199467779206L;

	/** 客户端日志Key. 必须传入 */
	private String logKey;

	/** 用户ID */
	private Long userId;

	/** 用户昵称 */
	private String userNick;

	/** 业务号 */
	private String bizId;

	/** 操作人ID */
	private Long operatorId;

	/** 操作类型 */
	private int operateType;

	/** 操作内容  */
	private String operateContent;

	/** 操作级别  */
	private String opLevel;

	/** 操作对象名称  */
	private String opItem;

	/** 扩展属性
	 *@deprecated 使用 extendInfos 替代
	 */
	private String[] extendArray;

	/** 扩展信息  */
	private Map<String, String> extendInfos;

	public LogContext logKey(String logKey) {
		this.logKey = logKey;
		return this;
	}

	public LogContext userId(Long userId) {
		this.userId = userId;
		return this;
	}

	public LogContext userNick(String userNick) {
		this.userNick = userNick;
		return this;
	}

	public LogContext bizId(String bizId) {
		this.bizId = bizId;
		return this;
	}

	public LogContext operatorId(Long operatorId) {
		this.operatorId = operatorId;
		return this;
	}

	public LogContext operateType(int operateType) {
		this.operateType = operateType;
		return this;
	}

	public LogContext operateContent(String operateContent) {
		this.operateContent = operateContent;
		return this;
	}

	public LogContext opLevel(String opLevel) {
		this.opLevel = opLevel;
		return this;
	}


	public LogContext opItem(String opItem) {
		this.opItem = opItem;
		return this;
	}

	public LogContext extendArray(String... extendArray) {
		this.extendArray = extendArray;
		return this;
	}

	public String getLogKey() {
		return logKey;
	}

	public Long getUserId() {
		return userId;
	}

	public String getUserNick() {
		return userNick;
	}

	public String getBizId() {
		return bizId;
	}

	public Long getOperatorId() {
		return operatorId;
	}

	public int getOperateType() {
		return operateType;
	}

	public String getOperateContent() {
		return operateContent;
	}

	public String getOpLevel() {
		return opLevel;
	}

	public String getOpItem() {
		return opItem;
	}

	public String[] getExtendArray() {
		return extendArray;
	}

	public Map<String, String> getExtendInfos() {
		return extendInfos;
	}

	public LogContext extendInfos(Map<String, String> extendInfos) {
		this.extendInfos = extendInfos;
		return this;
	}

	/**
	 * 扩展信息，调用多次可存入多个 key-value，key 相同则会覆盖
	 * @param key
	 * @param value
	 */
	public LogContext put(String key, String value) {
		if (extendInfos == null) {
			extendInfos = new LinkedHashMap<String, String>();
		}
		extendInfos.put(key, value);
		return this;
	}

	public void log() {
		StringBuilder sBuilder = new StringBuilder();

		sBuilder.append(logKey);

		sBuilder.append(SEPARATOR);
		appendWithNullCheck(userId, "0", sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(bizId, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithNullCheck(operatorId, "0", sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithNullCheck(operateType, "0", sBuilder);

		sBuilder.append(SEPARATOR);
		appendLog(operateContent, sBuilder, '|');

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(userNick, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(opLevel, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(opItem, EMPTY_STRING, sBuilder);

		if (null != extendInfos && extendInfos.size() > 0) {
			for (Entry<String, String> entry : extendInfos.entrySet()) {
				String key = entry.getKey();
				if (EagleEyeCoreUtils.isNotBlank(key)) {
					Object value = entry.getValue();
					sBuilder.append(SEPARATOR).append(key).append(KV_SEPARATOR);
					if (value != null) {
						appendWithBlankCheck(value.toString(), EMPTY_STRING, sBuilder);
					}
				}
			}
		}

		EagleEye.businessTag(ULC_EAGLEEYE_APPID, logKey, sBuilder.toString());
	}

	final static char KV_SEPARATOR = (char) 0x1;
	final static char SEPARATOR = (char) 0x12;
	final static String ULC_EAGLEEYE_APPID = "ulc";
}
