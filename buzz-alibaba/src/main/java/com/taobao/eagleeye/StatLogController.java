package com.taobao.eagleeye;

import java.util.Collections;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.ScheduledThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 管理统计日志输出的相关实现
 *
 * <AUTHOR>
 * @since 1.3.0
 */
class StatLogController {

	private static final Map<String, StatLogger> statLoggers = new NonBlockingHashMap<String, StatLogger>();
	/**
 	 * rolling over 动作触发之后，StatRollingData 的数据冷却时间。
 	 * rolling over 动作触发之后，还有一部分线程可能还持有 StatRollingData 的数据在写入，
 	 * 等待一小段时间可以让这部分操作完成。用这个方法可以避免执行期的加锁
	 */
	private static final int STAT_ENTRY_COOL_DOWN_MILLIS = 200;

    /**
     * 按指定间隔对 StatLog 进行定时滚动，单独线程池来保证滚动的实时性
     */
    private static final ScheduledThreadPoolExecutor rollerThreadPool =
    		new ScheduledThreadPoolExecutor(1, new NamedThreadFactory(
    				"EagleEye-StatLogController-roller", true));

    /**
     * 对 StatLog 滚动出来的统计记录进行输出
     */
    private static final ScheduledThreadPoolExecutor writerThreadPool =
    		new ScheduledThreadPoolExecutor(1, new NamedThreadFactory(
    				"EagleEye-StatLogController-writer", true));

    private static AtomicBoolean running = new AtomicBoolean(false);

    /**
     * 如果没有创建过指定 loggerName 的 StatLogger，创建并返回；否则，返回之前创建的实例
     * @param builder
     * @return
     */
    static StatLogger createLoggerIfNotExists(StatLoggerBuilder builder) {
    	String loggerName = builder.getLoggerName();
    	StatLogger statLogger = statLoggers.get(loggerName);
    	if (statLogger == null) {
    		synchronized (StatLogController.class) {
    			if ((statLogger = statLoggers.get(loggerName)) == null) {
    				// statLogger 的创建逻辑比较重，用加锁的方式保证仅创建一次
    				statLogger = builder.create();
    				statLoggers.put(loggerName, statLogger);

    				// 最多可以支持 statLoggers.size 个线程同时写
    				writerThreadPool.setMaximumPoolSize(Math.max(1, statLoggers.size()));

    				scheduleNextRollingTask(statLogger);
    				EagleEye.selfLog("[INFO] created statLogger[" + statLogger.getLoggerName() +
    						"]: " + statLogger.getAppender());
    			}
    		}
    	}
    	return statLogger;
    }

    /**
     * 获取当前创建的所有 StatLogger 实例
     * @return
     * @since 1.3.5
     */
    static Map<String, StatLogger> getAllStatLoggers() {
    	return Collections.unmodifiableMap(statLoggers);
    }

	private static void scheduleNextRollingTask(StatLogger statLogger) {
		if (!running.get()) {
			EagleEye.selfLog("[INFO] stopped rolling statLogger[" + statLogger.getLoggerName() + "]");
			return;
		}

		StatLogRollingTask rollingTask = new StatLogRollingTask(statLogger);

		long rollingTimeMillis = statLogger.getRollingData().getRollingTimeMillis();
		long delayMillis = rollingTimeMillis - System.currentTimeMillis();
		if (delayMillis > 5) {
			rollerThreadPool.schedule(rollingTask, delayMillis, TimeUnit.MILLISECONDS);
		} else if (-delayMillis > statLogger.getIntervalMillis()) {
			EagleEye.selfLog("[WARN] unusual delay of statLogger[" + statLogger.getLoggerName() +
					"], delay=" + (-delayMillis) + "ms, sumit now");
			rollerThreadPool.submit(rollingTask);
		} else {
			rollerThreadPool.submit(rollingTask);
		}
	}

	static void scheduleWriteTask(StatRollingData statRollingData) {
		if (statRollingData != null) {
			try {
				StatLogWriteTask task = new StatLogWriteTask(statRollingData);
				writerThreadPool.schedule(task, STAT_ENTRY_COOL_DOWN_MILLIS, TimeUnit.MILLISECONDS);
			} catch (Throwable t) {
				EagleEye.selfLog("[ERROR] fail to roll statLogger[" +
						statRollingData.getStatLogger().getLoggerName() + "]", t);
			}
		}
	}

	private static class StatLogRollingTask implements Runnable {

		final StatLogger statLogger;

		StatLogRollingTask(StatLogger statLogger) {
			this.statLogger = statLogger;
		}

		@Override
		public void run() {
			scheduleWriteTask(statLogger.rolling());
			scheduleNextRollingTask(statLogger);
		}
	}

	private static class StatLogWriteTask implements Runnable {

		final StatRollingData statRollingData;

		StatLogWriteTask(StatRollingData statRollingData) {
			this.statRollingData = statRollingData;
		}

		@Override
		public void run() {
			final StatRollingData data = statRollingData;
			final StatLogger logger = data.getStatLogger();
			try {
				final FastDateFormat fmt = new FastDateFormat();
				final StringBuilder buffer = new StringBuilder(256);
				final String timeStr = fmt.formatWithoutMs(data.getTimeSlot());

				final EagleEyeAppender appender = logger.getAppender();
				final Set<Entry<StatEntry, StatEntryFunc>> entrySet = data.getStatEntrySet();
				final char entryDelimiter = logger.getEntryDelimiter();
				final char keyDelimiter = logger.getKeyDelimiter();
				final char valueDelimiter = logger.getValueDelimiter();

				for (Entry<StatEntry, StatEntryFunc> entry : entrySet) {
					buffer.delete(0, buffer.length());
					StatEntryFunc func = entry.getValue();
					// time|statType|keys|values
					buffer.append(timeStr).append(entryDelimiter);
					buffer.append(func.getStatType()).append(entryDelimiter);
					entry.getKey().appendTo(buffer, keyDelimiter);
					buffer.append(entryDelimiter);
					func.appendTo(buffer, valueDelimiter);
					buffer.append(EagleEyeCoreUtils.NEWLINE);
					appender.append(buffer.toString());
				}

				// 一组 statEntry 全部输出完毕
				appender.flush();
			} catch (Throwable t) {
				EagleEye.selfLog("[WARN] fail to write statLogger[" +
						logger.getLoggerName() + "]", t);
			}
		}
	}

	static void start() {
		if (running.compareAndSet(false, true)) {
			// 在 stop 里面显式做最后一次的 rolling，因此等待执行的任务在 shutdown 时无需执行
			rollerThreadPool.setExecuteExistingDelayedTasksAfterShutdownPolicy(false);
			writerThreadPool.setExecuteExistingDelayedTasksAfterShutdownPolicy(true);
		}
	}

	static void stop() {
		if (running.compareAndSet(true, false)) {
			EagleEyeCoreUtils.shutdownThreadPool(rollerThreadPool, 0);
			EagleEye.selfLog("[INFO] StatLoggerController: roller ThreadPool shutdown successfully");

			for (StatLogger statLogger : statLoggers.values()) {
				new StatLogRollingTask(statLogger).run();
			}

			// 等待 writerThreadPool 任务被调度的最长延时时间，这样所有的任务都能够被 writer 立刻运行
			try {
				Thread.sleep(STAT_ENTRY_COOL_DOWN_MILLIS);
			} catch (InterruptedException e) {
				// quietly
			}

			EagleEyeCoreUtils.shutdownThreadPool(writerThreadPool, 2000);
			EagleEye.selfLog("[INFO] StatLoggerController: writer ThreadPool shutdown successfully");
		}
	}

	private StatLogController() {
	}
}
