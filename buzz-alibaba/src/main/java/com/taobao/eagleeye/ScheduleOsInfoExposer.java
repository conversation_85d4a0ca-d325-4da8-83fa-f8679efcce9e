package com.taobao.eagleeye;

import java.lang.management.ManagementFactory;
import java.lang.management.OperatingSystemMXBean;
import java.lang.reflect.Method;
import java.util.concurrent.TimeUnit;

/**
 * 取 JVM {@link OperatingSystemMXBean} 的信息输出
 *
 * <AUTHOR> 3/10/15 4:40 PM
 * <AUTHOR>
 * @since 1.4.2
 */
class ScheduleOsInfoExposer implements ScheduleTask {

	private static final StatLogger statLogger = EagleEye.statLoggerBuilder("eagleeye-jvm")
			.maxFileSizeMB(100).maxBackupIndex(1).buildSingleton();

	private static final boolean isSunOsBean = checkIsSunOsMBean();
	private static final boolean isUnixSunOsBean = checkIsUnixSunOsBean();

	@Override
	public void run() throws Exception {
		try {
			OperatingSystemMXBean osMXBean = ManagementFactory.getOperatingSystemMXBean();
			if (isSunOsBean) {
	            statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "os", "stat").arraySet(
	            		osMXBean.getAvailableProcessors(),
	                    (long) (osMXBean.getSystemLoadAverage() * 1000),
	                    getLong(osMXBean, "getTotalPhysicalMemorySize"),
	                    getLong(osMXBean, "getTotalSwapSpaceSize"),
	                    getLong(osMXBean, "getFreePhysicalMemorySize"),
	                    getLong(osMXBean, "getFreeSwapSpaceSize"),
	                    getLong(osMXBean, "getCommittedVirtualMemorySize"),
	                    getLong(osMXBean, "getProcessCpuTime"));
	        } else if (osMXBean != null) {
	            statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "os", "stat").arraySet(
	            		osMXBean.getAvailableProcessors(),
	                    (long) (osMXBean.getSystemLoadAverage() * 1000),
	                    0,
	                    0,
	                    0,
	                    0,
	                    0,
	                    0);
	        }
			if (isUnixSunOsBean) {
	            statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "os", "fd").arraySet(
	            		getLong(osMXBean, "getOpenFileDescriptorCount"),
	            		getLong(osMXBean, "getMaxFileDescriptorCount"));
			} else if (osMXBean != null) {
	            statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "os", "fd").arraySet(
	                    0,
	                    0);
			}
		} catch (Throwable t) {
			// quietly
		}
	}

	private static long getLong(OperatingSystemMXBean operatingSystem, String methodName) {
		try {
			final Method method = operatingSystem.getClass().getMethod(methodName, (Class<?>[]) null);
			method.setAccessible(true);
			return ((Long) method.invoke(operatingSystem, (Object[]) null)).longValue();
		} catch (Exception e) {
			return 0;
		}
	}

	private static boolean checkIsSunOsMBean() {
		// 在非 Sun 的 JVM 中，com.sun.management.OperatingSystemMXBean 无法找到，需要使用反射
		try {
			OperatingSystemMXBean osMXBean = ManagementFactory.getOperatingSystemMXBean();
			final String className = osMXBean.getClass().getName();
			if ("com.sun.management.OperatingSystem".equals(className)
					|| "com.sun.management.UnixOperatingSystem".equals(className)) {
				return getLong(osMXBean, "getTotalPhysicalMemorySize") > 0L;
			}
		} catch (Throwable t) {
		}
		return false;
	}

	private static boolean checkIsUnixSunOsBean() {
		// 在非 Sun 的 JVM 中，com.sun.management.UnixOperatingSystemMXBean 无法找到，需要使用反射
		try {
			OperatingSystemMXBean osMXBean = ManagementFactory.getOperatingSystemMXBean();
			final String className = osMXBean.getClass().getName();
			if ("com.sun.management.UnixOperatingSystem".equals(className)) {
				return getLong(osMXBean, "getOpenFileDescriptorCount") >= 0L;
			}
		} catch (Throwable t) {
		}
		return false;
	}

	@Override
	public long getIntervalMillis() {
		return TimeUnit.MINUTES.toMillis(1);
	}
}
