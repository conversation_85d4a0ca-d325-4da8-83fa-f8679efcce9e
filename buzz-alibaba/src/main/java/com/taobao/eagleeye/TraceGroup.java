package com.taobao.eagleeye;

/**
 * 可以由业务指定的链路分组，控制链路统计方式
 * @since 1.2.5
 */
public class TraceGroup {
	/** 应用名 */
	private final String appName;

	private String key1 = EagleEyeCoreUtils.EMPTY_STRING;
	private String key2 = EagleEyeCoreUtils.EMPTY_STRING;
	private String key3 = EagleEyeCoreUtils.EMPTY_STRING;

	/**
	 * 创建链路分组，必须指定应用名
	 * @param appName
	 */
	public TraceGroup(String appName) {
		this.appName = appName;
	}

	@Override
	public String toString() {
		return "TraceGroup [appName=" + appName + ", key1=" + key1 + ", key2=" + key2 + ", key3=" + key3 + "]";
	}

	public String getAppName() {
		return appName;
	}

	/**
	 * 设置 key1
	 * @param key1
	 * @return
	 */
	public TraceGroup key1(String key1) {
		this.key1 = key1;
		return this;
	}
	public String getKey1() {
		return key1;
	}

	/**
	 * 设置 key2
	 * @param key2
	 * @return
	 */
	public TraceGroup key2(String key2) {
		this.key2 = key2;
		return this;
	}
	public String getKey2() {
		return key2;
	}

	/**
	 * 设置 key3
	 * @param key3
	 * @return
	 */
	public TraceGroup key3(String key3) {
		this.key3 = key3;
		return this;
	}
	public String getKey3() {
		return key3;
	}
}
