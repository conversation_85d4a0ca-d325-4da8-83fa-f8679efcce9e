package com.taobao.eagleeye;

import static com.taobao.eagleeye.EagleEyeCoreUtils.EMPTY_STRING;
import static com.taobao.eagleeye.EagleEyeCoreUtils.appendLog;
import static com.taobao.eagleeye.EagleEyeCoreUtils.appendWithBlankCheck;
import static com.taobao.eagleeye.EagleEyeCoreUtils.appendWithNullCheck;

import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;

/**
 * Xu 日志埋点
 * <blockquote>
 * 调用示例:
 * <pre>
 * new Xu("loginSuccess").userId(123456L)
 *     .userNick("userNick").bizId("bizId")
 *     .ip("***************").operatorId(1234L)
 *     .operateType("operateType")
 *     .operateContent("operateContent)
 *     .umid("umid").put("mykey", "myval").put("foo", "123")
 *     .log();
 * </pre>
 * </blockquote>
 */
public final class Xu {

	/**
	 * @param logKey 日志 Key
	 */
	public Xu(String logKey) {
		this.logKey = logKey;
	}

	/** 用户ID */
	public Xu userId(long userId) {
		this.userId = userId;
		return this;
	}

	/** 用户昵称 */
	public Xu userNick(String userNick) {
		this.userNick = userNick;
		return this;
	}

	/** 业务号 */
	public Xu bizId(String bizId) {
		this.bizId = bizId;
		return this;
	}

	/** 操作人ID */
	public Xu operatorId(Long operatorId) {
		this.operatorId = operatorId;
		return this;
	}

	/** 操作类型 */
	public Xu operateType(String operateType) {
		this.operateType = operateType;
		return this;
	}

	/** 操作内容， 不能有回车、换行、“|” 等符号 */
	public Xu operateContent(String operateContent) {
		this.operateContent = operateContent;
		return this;
	}

	/** 操作级别 */
	public Xu opLevel(String opLevel) {
		this.opLevel = opLevel;
		return this;
	}

	/** 操作对象名称 */
	public Xu opItem(String opItem) {
		this.opItem = opItem;
		return this;
	}

	/** IP */
	public Xu ip(String ip) {
		this.ip = ip;
		return this;
	}

	/** UMID */
	public Xu umid(String umid) {
		this.umid = umid;
		return this;
	}

	/**
	 * 扩展信息，调用多次可存入多个 key-value，key 相同则会覆盖
	 * @param key
	 * @param value
	 */
	public Xu put(String key, String value) {
		if (extendInfos == null) {
			extendInfos = new LinkedHashMap<String, String>();
		}
		extendInfos.put(key, value);
		return this;
	}

	public void log() {
		StringBuilder sBuilder = new StringBuilder();

		appendWithNullCheck(userId, "0", sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(bizId, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithNullCheck(operatorId, "0", sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(operateType, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendLog(operateContent, sBuilder, '|');

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(userNick, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(opLevel, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(opItem, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(ip, EMPTY_STRING, sBuilder);

		sBuilder.append(SEPARATOR);
		appendWithBlankCheck(umid, EMPTY_STRING, sBuilder);

		if (null != extendInfos && extendInfos.size() > 0) {
			for (Entry<String, String> entry : extendInfos.entrySet()) {
				String key = entry.getKey();
				if (EagleEyeCoreUtils.isNotBlank(key)) {
					Object value = entry.getValue();
					sBuilder.append(SEPARATOR).append(key).append(KV_SEPARATOR);
					if (value != null) {
						appendWithBlankCheck(value.toString(), EMPTY_STRING, sBuilder);
					}
				}
			}
		}

		EagleEye.businessTag(APPID, logKey, sBuilder.toString());
	}

	final static char KV_SEPARATOR = (char) 0x1;
	final static char SEPARATOR = (char) 0x12;
	final static String APPID = "xu";

	private final String logKey;
	private Long userId;
	private String userNick;
	private String bizId;
	private Long operatorId;
	private String operateType;
	private String operateContent;
	private String opLevel;
	private String opItem;
	private String ip;
	private String umid;
	private Map<String, String> extendInfos;
}
