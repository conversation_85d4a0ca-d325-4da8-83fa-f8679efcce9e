package com.taobao.eagleeye;

import java.util.Collection;
import java.util.Iterator;


/**
 * 跟踪日志项 ，主要通过 Fluent API 完成功能连接
 * @since 1.3.0
 * <AUTHOR>
 */
public final class TraceEntry {

	/*
	 * JVM 通过逃逸分析可以优化，避免每次都创建 TraceEntry
	 */

	private final TraceLogger traceLogger;
	private final BaseContext bizContext;

	private StringBuilder bizAppender = null;

	TraceEntry(TraceLogger traceLogger, String traceId, String rpcId, int logType,
			String domain, String eventType) {
		this.traceLogger = traceLogger;
		BaseContext biz;
		biz = new BaseContext(traceId, rpcId);
		biz.logType = logType;
		biz.serviceName = EagleEyeCoreUtils.checkNotNullEmpty(domain, "domain");
		biz.methodName = EagleEyeCoreUtils.checkNotNullEmpty(eventType, "eventType");
		biz.logTime = -1;
		this.bizContext = biz;
	}

	BaseContext getBizContext() {
		return bizContext;
	}

	/**
	 * 覆盖设置输出日志的时间戳
	 * @param timestamp
	 * @return
	 */
	public TraceEntry timestamp(long timestamp) {
		this.bizContext.logTime = timestamp;
		return this;
	}

	/**
	 * 设置当前业务日志关联的业务值
	 * @param bizType 业务类型，由业务保证其唯一性
	 * @param bizValue 业务值，不要包含 &、=、|
	 * @return
	 */
	public TraceEntry traceBiz(int bizType, String bizValue) {
		return traceBiz(String.valueOf(bizType), bizValue);
	}

	/**
	 * 设置当前业务日志关联的业务值
	 * @param bizKey 业务键，由业务保证其唯一性
	 * @param bizValue 业务值，不要包含 &、=、|
	 * @return
	 * @since 1.4.1
	 */
	public TraceEntry traceBiz(String bizKey, String bizValue) {
		StringBuilder bizAppender = this.bizAppender;
		if (bizAppender == null) {
			this.bizAppender = bizAppender = new StringBuilder(64);
		} else {
			bizAppender.append('&');
		}
		bizAppender.append(bizKey).append('=').append(bizValue);
		return this;
	}

	/**
	 * 设置当前业务日志关联的业务值
	 * @param bizType 业务类型，由业务保证其唯一性
	 * @param bizValue 业务值
	 * @return
	 * @since 1.4.2
	 */
	public TraceEntry traceBiz(int bizType, long bizValue) {
		return traceBiz(String.valueOf(bizType), String.valueOf(bizValue));
	}

	/**
	 * 设置当前业务日志关联的业务值
	 * @param bizKey 业务键，由业务保证其唯一性
	 * @param bizValue 业务值
	 * @return
	 * @since 1.4.2
	 */
	public TraceEntry traceBiz(String bizKey, long bizValue) {
		return traceBiz(bizKey, String.valueOf(bizValue));
	}

	/**
	 * 设置当前业务日志关联的业务值
	 * @param bizType 业务类型，由业务保证其唯一性
	 * @param bizValues 多个都是同一类型的业务值，不要包含 &、=、|
	 * @return
	 */
	public TraceEntry traceBiz(int bizType, Collection<String> bizValues) {
		return traceBiz(String.valueOf(bizType), bizValues);
	}

	/**
	 * 设置当前业务日志关联的业务值
	 * @param bizKey 业务键，由业务保证其唯一性
	 * @param bizValues 多个都是同一类型的业务值，不要包含 &、=、|
	 * @return
	 * @since 1.4.1
	 */
	public TraceEntry traceBiz(String bizKey, Collection<String> bizValues) {
		if (bizValues == null || bizValues.isEmpty()) {
			return traceBiz(bizKey, bizValues == null ? null : EagleEyeCoreUtils.EMPTY_STRING);
		}
		StringBuilder bizAppender = this.bizAppender;
		if (bizAppender == null) {
			this.bizAppender = bizAppender = new StringBuilder(128);
		} else {
			bizAppender.append('&');
		}
		Iterator<String> it = bizValues.iterator();
		bizAppender.append(bizKey).append('=').append(it.next());
		while (it.hasNext()) {
			bizAppender.append('&').append(bizKey).append('=').append(it.next());
		}
		return this;
	}

	/**
	 * 设置当前业务日志关联的业务值
	 * @param bizType 业务类型，由业务保证其唯一性
	 * @param bizValues 多个都是同一类型的业务值
	 * @return
	 * @since 1.3.5
	 */
	public TraceEntry traceBizLong(int bizType, Collection<Long> bizValues) {
		return traceBizLong(String.valueOf(bizType), bizValues);
	}

	/**
	 * 设置当前业务日志关联的业务 id
	 * @param bizType 业务键，由业务保证其唯一性
	 * @param bizValues 多个都是同一类型的业务值
	 * @return
	 * @since 1.4.1
	 */
	public TraceEntry traceBizLong(String bizKey, Collection<Long> bizValues) {
		if (bizValues == null || bizValues.isEmpty()) {
			return traceBiz(bizKey, bizValues == null ? null : EagleEyeCoreUtils.EMPTY_STRING);
		}
		StringBuilder bizAppender = this.bizAppender;
		if (bizAppender == null) {
			this.bizAppender = bizAppender = new StringBuilder(128);
		} else {
			bizAppender.append('&');
		}
		Iterator<Long> it = bizValues.iterator();
		bizAppender.append(bizKey).append('=').append(it.next());
		while (it.hasNext()) {
			bizAppender.append('&').append(bizKey).append('=').append(it.next());
		}
		return this;
	}

	/**
	 * 打印一行业务日志
	 */
	public void logLine() {
		this.bizContext.callBackMsg = EagleEyeCoreUtils.EMPTY_STRING;
		if (bizAppender != null) {
			this.bizContext.traceName = bizAppender.toString();
		}
		this.traceLogger.logLine(this.bizContext);
	}

	/**
	 * 打印一行业务日志，附加最后一项 content 的内容。
	 * 注意：日志内部不要带换行，出于性能考虑，这里没有做换行符替换。
	 * 一行日志建议大小不要超过 4K。
	 * @param content
	 * @see #escapeAndLogLine(String) 会做换行符替换的接口
	 */
	public void logLine(String content) {
		this.bizContext.callBackMsg = EagleEyeCoreUtils.defaultIfNull(content, EagleEyeCoreUtils.EMPTY_STRING);
		if (bizAppender != null) {
			this.bizContext.traceName = bizAppender.toString();
		}
		this.traceLogger.logLine(this.bizContext);
	}

	/**
	 * 打印一行业务日志，附加最后一项 content 的内容。
	 * 会将 content 中的换行符替换成空格，一行日志建议大小不要超过 4K。
	 * @param content
	 * @see #logLine(String) 不做换行符替换的接口
	 */
	public void escapeAndLogLine(String content) {
		this.bizContext.rpcType = DefaultTraceEncoder.REQUIRED_LINE_FEED_ESCAPE;
		logLine(content);
	}
}
