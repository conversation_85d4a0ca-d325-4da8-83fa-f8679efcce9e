package com.taobao.eagleeye;

import javax.servlet.http.HttpServletResponse;

/**
 * 支持 Servlet 3.0 协议
 *
 * <AUTHOR>
 */
class HttpContextDataExposerImplV3 implements HttpContextDataExposer {

	@Override
	public String getStatusCode(HttpServletResponse response) {
		final int status = response.getStatus();
		if (status == 200) {
			return "200";
		} else {
			return Integer.toString(status);
		}
	}
}
