package com.taobao.eagleeye;

import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.lang.management.MemoryPoolMXBean;
import java.lang.management.MemoryUsage;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 取 JVM {@link GarbageCollectorMXBean} 的信息输出
 *
 * <AUTHOR> 3/10/15 1:31 PM
 * <AUTHOR>
 * @since 1.4.2
 */
class ScheduleMemoryInfoExposer implements ScheduleTask {

	private static final StatLogger statLogger = EagleEye.statLoggerBuilder("eagleeye-jvm")
			.maxFileSizeMB(100).maxBackupIndex(1).buildSingleton();

	@Override
	public void run() throws Exception {
        try {
            List<MemoryPoolMXBean> memoryPools = ManagementFactory.getMemoryPoolMXBeans();
            for (MemoryPoolMXBean memoryPool : memoryPools) {
            	if (memoryPool == null) continue;
                MemoryUsage usage = memoryPool.getUsage();
                if (usage == null) continue;

                String poolName = memoryPool.getName();
				statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "memory", poolName).arraySet(
                        usage.getInit(), usage.getUsed(), usage.getCommitted(), usage.getMax());
            }
		} catch (Throwable t) {
			// quietly
		}
	}

	@Override
	public long getIntervalMillis() {
		return TimeUnit.MINUTES.toMillis(1);
	}
}
