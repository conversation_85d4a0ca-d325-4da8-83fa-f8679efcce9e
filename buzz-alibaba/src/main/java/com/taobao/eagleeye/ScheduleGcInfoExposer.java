package com.taobao.eagleeye;

import java.lang.management.GarbageCollectorMXBean;
import java.lang.management.ManagementFactory;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 取 JVM {@link GarbageCollectorMXBean} 的信息输出
 *
 * <AUTHOR> 3/10/15 2:21 PM
 * <AUTHOR>
 * @since 1.4.2
 */
class ScheduleGcInfoExposer implements ScheduleTask {

	private static final StatLogger statLogger = EagleEye.statLoggerBuilder("eagleeye-jvm")
			.maxFileSizeMB(100).maxBackupIndex(1).buildSingleton();

	private Map<String, long[]> lastGcStatusMap = new HashMap<String, long[]>();

	@Override
	public void run() throws Exception {
        try {
			List<GarbageCollectorMXBean> gcMXBeans = ManagementFactory.getGarbageCollectorMXBeans();
			for (GarbageCollectorMXBean gcBean : gcMXBeans) {
				if (gcBean == null) continue;

				String name = gcBean.getName();

				long[] vals = lastGcStatusMap.get(name);
				if (vals == null) {
					vals = new long[] { 0, 0 };
					lastGcStatusMap.put(name, vals);
				}

				long currentCollectionCount = gcBean.getCollectionCount();
				long recentCollectionCount = currentCollectionCount - vals[0];
				vals[0] = currentCollectionCount;

				long currentCollectionTime = gcBean.getCollectionTime();
				long recentCollectionTime = currentCollectionTime - vals[1];
				vals[1] = currentCollectionTime;

			    statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "gc", gcBean.getName())
			    	.arraySet(recentCollectionCount, recentCollectionTime,
			    			gcBean.getCollectionCount(), gcBean.getCollectionTime());
			}
		} catch (Throwable t) {
			// quietly
		}
	}

	@Override
	public long getIntervalMillis() {
		return TimeUnit.MINUTES.toMillis(1);
	}
}
