package com.taobao.eagleeye;

import java.util.Arrays;
import java.util.concurrent.atomic.AtomicReference;
import java.util.concurrent.locks.ReentrantLock;

interface StatEntryFunc {
	/**
	 * value 的格式化输出
	 * @param appender
	 * @param delimiter
	 */
	void appendTo(StringBuilder appender, char delimiter);
	/**
	 * 统计类型
	 * @return
	 */
	int getStatType();
	/**
	 * 获取内存中的数值
	 * @return
	 */
	Object[] getValues();
	/*
	 * 各种统计方法 API，
	 * 为了避免多做类型转换，这个接口是所有已知实现的全集
	 */
	void count(long count);
	void countAndSum(long count, long value);
	void arrayAdd(long... values);
	void arraySet(long... values);
	void minMax(long candidate, String ref);
	void batchAdd(long... values);
	void strArray(String... values);
}

enum StatEntryFuncFactory {
	COUNT_SUM {
		@Override
		StatEntryFunc create() {
			return new StatEntry_countAndSum();
		}
	},
	STD {
		@Override
		StatEntryFunc create() {
			return new StatEntry_std();
		}
	},
	ARRAY {
		@Override
		StatEntryFunc create() {
			return new StatEntry_array();
		}
	},
	MIN_MAX {
		@Override
		StatEntryFunc create() {
			return new StatEntry_minMax();
		}
	},
	BATCH_ADD {
		@Override
		StatEntryFunc create() {
			return new StatEntry_batchAdd();
		}
	},
	STR_ARRAY {
		@Override
		StatEntryFunc create() {
			return new StatEntry_strArray();
		}
	},
	;
	abstract StatEntryFunc create();
}

class StatEntry_countAndSum implements StatEntryFunc {

	private LongAdder count = new LongAdder();
	private LongAdder value = new LongAdder();

	@Override
	public void appendTo(StringBuilder appender, char delimiter) {
		appender.append(count.sum()).append(delimiter).append(value.sum());
	}

	@Override
	public Object[] getValues() {
		return new Object[] { Long.valueOf(count.sum()), Long.valueOf(value.sum()) };
	}

	@Override
	public int getStatType() {
		return 1;
	}

	@Override
	public void count(long count) {
		this.count.add(count);
	}

	@Override
	public void countAndSum(long count, long value) {
		this.count.add(count);
		this.value.add(value);
	}

	@Override
	public void arrayAdd(long... values) {
		throw new IllegalStateException("已经调用 countAndSum() 的统计项不能再调用 arrayAdd()");
	}

	@Override
	public void arraySet(long... values) {
		throw new IllegalStateException("已经调用 countAndSum() 的统计项不能再调用 arraySet()");
	}

	@Override
	public void minMax(long candidate, String ref) {
		throw new IllegalStateException("已经调用 countAndSum() 的统计项不能再调用 minMax()");
	}

	@Override
	public void batchAdd(long... values) {
		throw new IllegalStateException("已经调用 countAndSum() 的统计项不能再调用 batchAdd()");
	}

	@Override
	public void strArray(String... values) {
		throw new IllegalStateException("已经调用 countAndSum() 的统计项不能再调用 strArray()");
	}
}

class StatEntry_std implements StatEntryFunc {

	private LongAdder count = new LongAdder();
	private LongAdder sum = new LongAdder();
	private LongAdder power2 = new LongAdder();

	@Override
	public void appendTo(StringBuilder appender, char delimiter) {
		appender.append(count.sum()).append(delimiter).append(sum.sum())
				.append(delimiter).append(power2.sum());
	}

	@Override
	public Object[] getValues() {
		return new Object[] { Long.valueOf(count.sum()), Long.valueOf(sum.sum()), Long.valueOf(power2.sum()) };
	}

	@Override
	public int getStatType() {
		return 2;
	}

	@Override
	public void count(long count) {
		this.count.add(count);
	}

	@Override
	public void countAndSum(long count, long value) {
		this.count.add(count);
		this.sum.add(value);
		this.power2.add(value * value);
	}

	@Override
	public void arrayAdd(long... values) {
		throw new IllegalStateException("已经调用 std() 的统计项不能再调用 arrayAdd()");
	}

	@Override
	public void arraySet(long... values) {
		throw new IllegalStateException("已经调用 std() 的统计项不能再调用 arraySet()");
	}

	@Override
	public void minMax(long candidate, String ref) {
		throw new IllegalStateException("已经调用 std() 的统计项不能再调用 minMax()");
	}

	@Override
	public void batchAdd(long... values) {
		throw new IllegalStateException("已经调用 std() 的统计项不能再调用 batchAdd()");
	}

	@Override
	public void strArray(String... values) {
		throw new IllegalStateException("已经调用 std() 的统计项不能再调用 strArray()");
	}
}

class StatEntry_array implements StatEntryFunc {

	private static final long[] EMPTY_VALUES = new long[0];

	// 包含了 arraySet 操作，用 AtomicLong/LongAdder 无法保证整体最终一致
	private ReentrantLock lock = new ReentrantLock();
	private long values[] = EMPTY_VALUES;

	@Override
	public void appendTo(StringBuilder appender, char delimiter) {
		lock.lock();
		try {
			final int len = values.length;
			if (len > 0) {
				appender.append(values[0]);
				for (int i = 1; i < len; ++i) {
					appender.append(delimiter).append(values[i]);
				}
			}
		} finally {
			lock.unlock();
		}
	}

	@Override
	public Object[] getValues() {
		lock.lock();
		try {
			Object[] vals = new Object[values.length];
			for (int i = 0; i < values.length; ++i) {
				vals[i] = Long.valueOf(values[i]);
			}
			return vals;
		} finally {
			lock.unlock();
		}
	}

	@Override
	public int getStatType() {
		return 3;
	}

	@Override
	public void count(long count) {
		throw new IllegalStateException("已经调用 arrayAdd/Set() 的统计项不能再调用 count()");
	}

	@Override
	public void countAndSum(long count, long value) {
		throw new IllegalStateException("已经调用 arrayAdd/Set() 的统计项不能再调用  countAndSum()");
	}

	@Override
	public void arrayAdd(long... valuesToAdd) {
		lock.lock();
		try {
			long[] values = this.values;
			final int len = valuesToAdd.length;
			if (values.length < len) {
				if (values.length == 0) {
					this.values = values = new long[len];
				} else {
					this.values = values = Arrays.copyOf(values, len);
				}
			}
			for (int i = 0; i < len; ++i) {
				values[i] += valuesToAdd[i];
			}
		} finally {
			lock.unlock();
		}
	}

	@Override
	public void arraySet(long... valuesToSet) {
		lock.lock();
		try {
			long[] values = this.values;
			final int len = valuesToSet.length;
			if (values.length < len) {
				this.values = values = new long[len];
			}
			for (int i = 0; i < len; ++i) {
				values[i] = valuesToSet[i];
			}
		} finally {
			lock.unlock();
		}
	}

	@Override
	public void minMax(long candidate, String ref) {
		throw new IllegalStateException("已经调用 arrayAdd/Set() 的统计项不能再调用 minMax()");
	}

	@Override
	public void batchAdd(long... values) {
		throw new IllegalStateException("已经调用 arrayAdd/Set() 的统计项不能再调用 batchAdd()");
	}

	@Override
	public void strArray(String... values) {
		throw new IllegalStateException("已经调用 arrayAdd/Set() 的统计项不能再调用 strArray()");
	}
}

class StatEntry_minMax implements StatEntryFunc {

	private AtomicReference<ValueRef> max = new AtomicReference<ValueRef>(
			new ValueRef(Long.MIN_VALUE, null));
	private AtomicReference<ValueRef> min = new AtomicReference<ValueRef>(
			new ValueRef(Long.MAX_VALUE, null));

	@Override
	public void appendTo(StringBuilder appender, char delimiter) {
		ValueRef lmax = max.get();
		ValueRef lmin = min.get();

		appender.append(lmax.value).append(delimiter);
		if (lmax.ref != null) {
			appender.append(lmax.ref);
		}
		appender.append(delimiter);

		appender.append(lmin.value).append(delimiter);
		if (lmin.ref != null) {
			appender.append(lmin.ref);
		}
	}

	@Override
	public Object[] getValues() {
		ValueRef lmax = max.get();
		ValueRef lmin = min.get();
		return new Object[] { Long.valueOf(lmax.value), lmax.ref,
				Long.valueOf(lmin.value), lmin.ref };
	}

	@Override
	public int getStatType() {
		return 4;
	}

	@Override
	public void count(long count) {
		throw new IllegalStateException("已经调用 minMax() 的统计项不能再调用 count()");
	}

	@Override
	public void countAndSum(long count, long value) {
		throw new IllegalStateException("已经调用 minMax() 的统计项不能再调用 countAndSum()");
	}

	@Override
	public void arrayAdd(long... values) {
		throw new IllegalStateException("已经调用 minMax() 的统计项不能再调用 arrayAdd()");
	}

	@Override
	public void arraySet(long... values) {
		throw new IllegalStateException("已经调用 minMax() 的统计项不能再调用  arraySet()");
	}

	@Override
	public void batchAdd(long... values) {
		throw new IllegalStateException("已经调用 minMax() 的统计项不能再调用 batchAdd()");
	}

	@Override
	public void minMax(long candidate, String ref) {
		ValueRef lmax = max.get();
		if (lmax.value <= candidate) {
			final ValueRef cmax = new ValueRef(candidate, ref);
			while (!max.compareAndSet(lmax, cmax) && (lmax = max.get()).value <= candidate)
				;
		}
		ValueRef lmin = min.get();
		if (lmin.value >= candidate) {
			final ValueRef cmin = new ValueRef(candidate, ref);
			while (!min.compareAndSet(lmin, cmin) && (lmin = min.get()).value >= candidate)
				;
		}
	}

	@Override
	public void strArray(String... values) {
		throw new IllegalStateException("已经调用 minMax() 的统计项不能再调用 strArray()");
	}

	private static final class ValueRef {
		final long value;
		final String ref;
		ValueRef(long value, String ref) {
			this.value = value;
			this.ref = ref;
		}
	}
}

class StatEntry_batchAdd implements StatEntryFunc {

	private static final LongAdder[] DEFAULT_VALUE = new LongAdder[0];
	private AtomicReference<LongAdder[]> valuesRef = new AtomicReference<LongAdder[]>(DEFAULT_VALUE);

	@Override
	public void appendTo(StringBuilder appender, char delimiter) {
		LongAdder[] adders = valuesRef.get();
		final int len = adders.length;
		if (len > 0) {
			appender.append(adders[0].sum());
			for (int i = 1; i < len; ++i) {
				appender.append(delimiter).append(adders[i].sum());
			}
		}
	}

	@Override
	public Object[] getValues() {
		LongAdder[] adders = valuesRef.get();
		Object[] vals = new Object[adders.length];
		for (int i = 0; i < adders.length; ++i) {
			vals[i] = Long.valueOf(adders[i].sum());
		}
		return vals;
	}

	@Override
	public int getStatType() {
		return 5;
	}

	@Override
	public void count(long count) {
		throw new IllegalStateException("已经调用 batchAdd() 的统计项不能再调用 count()");
	}

	@Override
	public void countAndSum(long count, long value) {
		throw new IllegalStateException("已经调用 batchAdd() 的统计项不能再调用 countAndSum()");
	}

	@Override
	public void arrayAdd(long... values) {
		throw new IllegalStateException("已经调用 batchAdd() 的统计项不能再调用 arrayAdd()");
	}

	@Override
	public void arraySet(long... values) {
		throw new IllegalStateException("已经调用 batchAdd() 的统计项不能再调用 arraySet()");
	}

	@Override
	public void minMax(long candidate, String ref) {
		throw new IllegalStateException("已经调用 batchAdd() 的统计项不能再调用 minMax()");
	}

	@Override
	public void batchAdd(long... valuesToAdd) {
		LongAdder[] values = valuesRef.get();
		if (values == DEFAULT_VALUE) {
			values = new LongAdder[valuesToAdd.length];
			for (int i = 0; i < values.length; ++ i) {
				values[i] = new LongAdder();
			}
			if (!valuesRef.compareAndSet(DEFAULT_VALUE, values)) {
				values = valuesRef.get();
			}
		}
		int len = Math.min(values.length, valuesToAdd.length);
		if (len <= 8) {
			switch (len) {
			case 8:
				values[7].add(valuesToAdd[7]);
			case 7:
				values[6].add(valuesToAdd[6]);
			case 6:
				values[5].add(valuesToAdd[5]);
			case 5:
				values[4].add(valuesToAdd[4]);
			case 4:
				values[3].add(valuesToAdd[3]);
			case 3:
				values[2].add(valuesToAdd[2]);
			case 2:
				values[1].add(valuesToAdd[1]);
			case 1:
				values[0].add(valuesToAdd[0]);
			default:
			}
		} else {
			for (int i = 0; i < len; ++i) {
				values[i].add(valuesToAdd[i]);
			}
		}
	}

	@Override
	public void strArray(String... values) {
		throw new IllegalStateException("已经调用 batchAdd() 的统计项不能再调用 strArray()");
	}
}

class StatEntry_strArray implements StatEntryFunc {

	private volatile String[] arrayRef = new String[0];

	@Override
	public void appendTo(StringBuilder appender, char delimiter) {
		final String[] strArray = arrayRef;
		final int len = strArray.length;
		if (len > 0) {
			appender.append(strArray[0]);
			for (int i = 1; i < len; ++i) {
				appender.append(delimiter).append(strArray[i]);
			}
		}
	}

	@Override
	public Object[] getValues() {
		final String[] strArray = arrayRef;
		Object[] vals = new Object[strArray.length];
		for (int i = 0; i < strArray.length; ++i) {
			vals[i] = strArray[i];
		}
		return vals;
	}

	@Override
	public int getStatType() {
		return 6;
	}

	@Override
	public void count(long count) {
		throw new IllegalStateException("已经调用 strArray() 的统计项不能再调用 count()");
	}

	@Override
	public void countAndSum(long count, long value) {
		throw new IllegalStateException("已经调用 strArray() 的统计项不能再调用 countAndSum()");
	}

	@Override
	public void arrayAdd(long... values) {
		throw new IllegalStateException("已经调用 strArray() 的统计项不能再调用 arrayAdd()");
	}

	@Override
	public void arraySet(long... values) {
		throw new IllegalStateException("已经调用 strArray() 的统计项不能再调用 arraySet()");
	}

	@Override
	public void minMax(long candidate, String ref) {
		throw new IllegalStateException("已经调用 strArray() 的统计项不能再调用 minMax()");
	}

	@Override
	public void batchAdd(long... valuesToAdd) {
		throw new IllegalStateException("已经调用 strArray() 的统计项不能再调用 batchAdd()");
	}

	@Override
	public void strArray(String... values) {
		if (values != null) {
			arrayRef = values;
		}
	}
}
