package com.taobao.eagleeye;

/**
 * 用于监听 EagleEye 调用上下文的变化的回调
 *
 * <AUTHOR>
 * @since 1.3.4 接口
 * @since 1.3.5 改成抽象类
 */
public abstract class EagleEyeContextListener {

	/**
	 * 在{@link EagleEye#setRpcContext(RpcContext_inner)} 前执行的回调。
	 * 当前 context 可以通过 {@link EagleEye#getRpcContext()} 取得
	 * @param context 将要被设置的 context
	 */
	public void beforeSet(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在{@link EagleEye#setRpcContext(RpcContext_inner)} 后执行的回调
	 * @param context 当前已被设置的 context
	 */
	public void afterSet(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#startTrace(String)} 后执行的回调
	 * @param context
	 */
	public void afterStartTrace(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#endTrace(String, int)} 后执行的回调
	 * @param context
	 */
	public void afterEndTrace(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#startRpc(String, String)} 后执行的回调
	 * @param context
	 */
	public void afterStartRpc(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#endRpc(String, int, String)} 后执行的回调
	 * @param context
	 */
	public void afterEndRpc(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#rpcClientSend()} 后执行的回调
	 * @param context
	 */
	public void afterRpcClientSend(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#rpcServerRecv(String, String)} 后执行的回调
	 * @param context
	 */
	public void afterRpcServerRecv(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#rpcServerSend(int, String, String)} 后执行的回调
	 * @param context
	 */
	public void afterRpcServerSend(RpcContext_inner context) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#startLocal(String, String)} 后执行的回调
	 * @param context
	 * @param localContext
	 */
	public void afterStartLocal(RpcContext_inner context, LocalContext_inner localContext) {
		// noop
	}

	/**
	 * 在 {@link RpcContext_inner#endLocal(String, String)} 后执行的回调
	 * @param context
	 * @param localContext
	 */
	public void afterEndLocal(RpcContext_inner context, LocalContext_inner localContext) {
		// noop
	}

	/**
	 * 当前监听器被移除之后执行的回调
	 * @since 1.4.0
	 */
	public void afterListenerRemoval() {
		// noop
	}

	/**
	 * EagleEye 停止的时候执行的回调
	 * @since 1.4.0
	 */
	public void beforeEagleEyeShutdown() {
		// noop
	}
}
