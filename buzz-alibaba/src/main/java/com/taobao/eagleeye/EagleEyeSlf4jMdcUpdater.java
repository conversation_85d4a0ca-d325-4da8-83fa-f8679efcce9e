package com.taobao.eagleeye;

import org.slf4j.MDC;

/**
 * 将 EagleEye 的调用上下文更新同步到 slf4j MDC
 *
 * <AUTHOR>
 * @since 1.3.4
 */
public class EagleEyeSlf4jMdcUpdater extends EagleEyeContextListener {

	private static final EagleEyeSlf4jMdcUpdater singleton = new EagleEyeSlf4jMdcUpdater();

	private EagleEyeSlf4jMdcUpdater() {
	}

	public static EagleEyeSlf4jMdcUpdater getInstance() {
		return singleton;
	}

	@Override
	public void beforeSet(RpcContext_inner context) {
		if (context != null) {
			MDC.put("EAGLEEYE_TRACE_ID", context.getTraceId());
			MDC.put("EAGLEEYE_RPC_ID", context.getRpcId());
		} else {
			MDC.remove("EAGLEEYE_TRACE_ID");
			MDC.remove("EAGLEEYE_RPC_ID");
		}
	}
}
