/*
 * Copyright 1999-2101 Alibaba Group.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
package com.taobao.eagleeye;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * EagleEye JSON Dump 功能的自定义标注
 */
@Retention(RetentionPolicy.RUNTIME)
@Target({ ElementType.FIELD })
public @interface JSONDumpField {

    /**
     * 指定序列化时使用的名称
     */
    String name() default "";

    /**
     * 该字段是否不做序列化
     */
    boolean ignore() default false;

    /**
     * 序列化该字段时，使用指定的 getter 函数返回值替代这个字段的值，
     * getter 函数可以不是 public
     */
    String getterMethod() default "";
}
