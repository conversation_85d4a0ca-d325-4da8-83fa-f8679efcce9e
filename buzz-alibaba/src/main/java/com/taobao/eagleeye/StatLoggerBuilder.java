package com.taobao.eagleeye;

import java.util.concurrent.TimeUnit;

/**
 * 协助配置 StatLogger 的参数
 *
 * <AUTHOR>
 * @since 1.3.0
 */
public final class StatLoggerBuilder extends BaseLoggerBuilder<StatLoggerBuilder> {

	private int intervalSeconds = 60;

	private int maxEntryCount = 20000;

	private char keyDelimiter = ',';

	private char valueDelimiter = ',';

	private EagleEyeAppender appender = null;

	StatLoggerBuilder(String loggerName) {
		super(loggerName);
	}

	/**
	 * 统计输出的间隔时间，单位：秒，默认值是 60。最大统计间隔不能超过 5 分钟
	 * @param intervalSeconds
	 * @return
	 */
	public StatLoggerBuilder intervalSeconds(int intervalSeconds) {
		validateInterval(intervalSeconds);
		this.intervalSeconds = intervalSeconds;
		return this;
	}

	/**
	 * 同一个统计间隔内，可存放的统计项的数量，不能小于 1
	 * @param maxEntryCount
	 * @return
	 */
	public StatLoggerBuilder maxEntryCount(int maxEntryCount) {
		if (maxEntryCount < 1) {
			throw new IllegalArgumentException("最大统计项的数量不能小于 1: " + maxEntryCount);
		}
		this.maxEntryCount = maxEntryCount;
		return this;
	}

	/**
	 * @param keyDelimiter 设置 KEY 分隔符
	 * @return
	 */
	public StatLoggerBuilder keyDelimiter(char keyDelimiter) {
		this.keyDelimiter = keyDelimiter;
		return this;
	}

	/**
	 * @param valueDelimiter 设置 VALUE 分隔符
	 * @return
	 */
	public StatLoggerBuilder valueDelimiter(char valueDelimiter) {
		this.valueDelimiter = valueDelimiter;
		return this;
	}

	StatLoggerBuilder appender(EagleEyeAppender appender) {
		this.appender = appender;
		return this;
	}

	StatLogger create() {
		long intervalMillis = TimeUnit.SECONDS.toMillis(this.intervalSeconds);

		String filePath;
		if (this.filePath == null) {
			filePath = EagleEye.EAGLEEYE_LOG_DIR + "stat-" + loggerName + ".log";
		} else if (this.filePath.endsWith("/") || this.filePath.endsWith("\\")) {
			filePath = this.filePath + "stat-" + loggerName + ".log";
		} else {
			filePath = this.filePath;
		}

		EagleEyeAppender appender = this.appender;
		if (appender == null) {
			EagleEyeRollingFileAppender rfappender = new EagleEyeRollingFileAppender(filePath, maxFileSize);
			rfappender.setMaxBackupIndex(maxBackupIndex);
			appender = new SyncAppender(rfappender);
		}

		EagleEyeLogDaemon.watch(appender);
		return new StatLogger(loggerName, appender, intervalMillis, maxEntryCount,
				entryDelimiter, keyDelimiter, valueDelimiter);
	}

	/**
	 * 创建 StatLogger 的单例，如果已经创建过同名的 StatLogger 实例，则直接返回之前创建过的实例
	 * @return
	 */
	public StatLogger buildSingleton() {
		return StatLogController.createLoggerIfNotExists(this);
	}

	/**
	 * @param intervalSeconds
	 * @throws IllegalArgumentException
	 * @since 1.3.0 最大一分钟间隔
	 * @since 1.4.5 最大十五分钟间隔
	 */
	static void validateInterval(final long intervalSeconds) throws IllegalArgumentException {
		if (intervalSeconds < 1) {
			throw new IllegalArgumentException("统计间隔不能小于 1: " + intervalSeconds);
		} else if (intervalSeconds < 60) {
			if (60 % intervalSeconds != 0) {
				throw new IllegalArgumentException("一分钟以内的统计间隔需要被 60 整除: " + intervalSeconds);
			}
		} else if (intervalSeconds <= 15 * 60) {
			if (intervalSeconds % 60 != 0) {
				throw new IllegalArgumentException("超过一分钟的统计间隔需要能整除 60: " + intervalSeconds);
			}
			if (60 * 60 % intervalSeconds != 0) {
				throw new IllegalArgumentException("一小时以内的统计间隔需要被 3600 整除: " + intervalSeconds);
			}
		} else if (intervalSeconds > 15 * 60) {
			throw new IllegalArgumentException("统计间隔不能超过15分钟: " + intervalSeconds);
		}
	}
}
