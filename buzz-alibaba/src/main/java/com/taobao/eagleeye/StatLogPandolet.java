package com.taobao.eagleeye;

/**
 * 提供 API 供{@code Pandolet}调用方（比如：Tomcat Monitor）使用：
 *
 * <ul>
 * <li>返回目前在内存中可用的{@code StatLogger}</li>
 * <li>提供一个{@code StatLogger}的名字，返回该{@code StatLogger}在内存中堆积的数据</li>
 * <li>提供一个{@code StatLogger}的名字，返回该{@code StatLogger}的配置</li>
 * </ul>
 * <AUTHOR> 1/9/15 3:15 PM
 * <AUTHOR>
 * @since 1.3.5
 */
public class StatLogPandolet  {
//    private Map<String, StatLogger> allStatLoggers;
//
//    @Override
//    public void init(PandoletConfig config) {
//        super.init(config);
//        this.allStatLoggers = StatLogController.getAllStatLoggers();
//    }
//
//    /**
//     * 返回目前在内存中可用的{@code StatLogger}
//     *
//     * @return 在内存中可用的 StatLogger 的列表
//     */
//    @Service
//    public String[] getAvailableLoggers() {
//        Set<String> allLoggers = allStatLoggers.keySet();
//        return allLoggers.toArray(new String[allLoggers.size()]);
//    }
//
//    /**
//     * 提供一个{@code StatLogger}的名字，返回该{@code StatLogger}在内存中堆积的数据
//     *
//     * @param logger StatLogger 的名字
//     * @return 在内存中堆积的数据，其中可从{@code time}中获取时间戳，可从{@code stats}中获取内存中堆积的数据
//     */
//    @Service
//    public PandoletResponse queryInMemoryStat(@Parameter(name = "loggerName") String logger) {
//        if (logger == null || logger.trim().isEmpty()) {
//            throw new IllegalStateException("request parameter loggerName is either null or empty");
//        }
//
//        ResponseBuilder responseBuilder = pandoletService.createResponseBuilder();
//
//        StatLogger statLogger = allStatLoggers.get(logger);
//        if (statLogger == null) {
//        	return responseBuilder.status(404).status("logger not found").build();
//        }
//
//        StatRollingData data = statLogger.getRollingData();
//        char entryDelimiter = statLogger.getEntryDelimiter();
//        char keyDelimiter = statLogger.getKeyDelimiter();
//        char valueDelimiter = statLogger.getValueDelimiter();
//        StringBuilder buffer = new StringBuilder();
//        List<String> stats = new ArrayList<String>();
//        for (Map.Entry<StatEntry, StatEntryFunc> entry : data.getStatEntrySet()) {
//            buffer.delete(0, buffer.length());
//            StatEntryFunc func = entry.getValue();
//            buffer.append(func.getStatType()).append(entryDelimiter);
//            entry.getKey().appendTo(buffer, keyDelimiter);
//            buffer.append(entryDelimiter);
//            func.appendTo(buffer, valueDelimiter);
//            stats.add(buffer.toString());
//        }
//
//        return responseBuilder.status(200).status("success")
//        		.name("loggerName").stringValue(statLogger.getLoggerName())
//                .name("time").longValue(data.getTimeSlot())
//                .name("stats").stringValue(stats.toArray(new String[stats.size()]))
//                .build();
//    }
//
//    /**
//     * 提供一个{@code StatLogger}的名字，返回该{@code StatLogger}的配置
//     *
//     * @param logger StatLogger 的名字
//     * @return 配置数据
//     */
//    @Service
//    public PandoletResponse getLoggerConfig(@Parameter(name = "loggerName") String logger) {
//        if (logger == null || logger.trim().isEmpty()) {
//            throw new IllegalStateException("request parameter loggerName is either null or empty");
//        }
//
//        ResponseBuilder responseBuilder = pandoletService.createResponseBuilder();
//
//        StatLogger statLogger = allStatLoggers.get(logger);
//        if (statLogger == null) {
//        	return responseBuilder.status(404).status("logger not found").build();
//        }
//
//        return responseBuilder.status(200).status("success")
//        		.name("loggerName").stringValue(statLogger.getLoggerName())
//        		.name("maxEntryCount").intValue(statLogger.getMaxEntryCount())
//        		.name("intervalMillis").longValue(statLogger.getIntervalMillis())
//        		.name("entryDelimiter").charValue(statLogger.getEntryDelimiter())
//        		.name("keyDelimiter").charValue(statLogger.getKeyDelimiter())
//        		.name("valueDelimiter").charValue(statLogger.getValueDelimiter())
//        		.name("appenderClass").stringValue(statLogger.getAppender().getClass().getName())
//        		.name("outputLocation").stringValue(statLogger.getAppender().getOutputLocation())
//                .build();
//    }
//
//    /**
//     * 返回所有 {@code StatLogger} 的位置
//     *
//     * @return 返回映射表，key 是 logger 名字，value 是 logger 输出位置
//     */
//    @Service
//    public PandoletResponse getAllLoggerLocations() {
//    	Map<String, StatLogger> allStatLoggers = StatLogController.getAllStatLoggers();
//
//    	ResponseBuilder responseBuilder = pandoletService.createResponseBuilder();
//    	responseBuilder.status(200).status("success");
//
//    	for (Entry<String, StatLogger> entry : allStatLoggers.entrySet()) {
//    		responseBuilder.name(entry.getKey())
//    				.stringValue(entry.getValue().getAppender().getOutputLocation());
//		}
//
//    	return responseBuilder.build();
//    }
//
//    @Override
//    public String getName() {
//        return "StatLog";
//    }
}
