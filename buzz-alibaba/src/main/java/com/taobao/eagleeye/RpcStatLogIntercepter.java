package com.taobao.eagleeye;


/**
 * 为 RPC 调用的埋点单独输出统计日志
 *
 * <AUTHOR>
 * @since 1.4.0
 */
public final class RpcStatLogIntercepter extends EagleEyeContextListener {

	private static final RpcStatLogIntercepter singleton = new RpcStatLogIntercepter();

	private final StatLogger traceStat;
	private final StatLogger hsfStat;
	private final StatLogger tddlStat;
	private final StatLogger tairStat;
	private final StatLogger notifyStat;
	private final StatLogger metaqStat;
	private final StatLogger localStat;

	private RpcStatLogIntercepter() {
		traceStat = EagleEye.statLoggerBuilder("eagleeye-trace").maxBackupIndex(2).buildSingleton();
		hsfStat = EagleEye.statLoggerBuilder("eagleeye-hsf").maxBackupIndex(2).keyDelimiter(';').buildSingleton();
		tddlStat = EagleEye.statLoggerBuilder("eagleeye-tddl").maxBackupIndex(2).buildSingleton();
		tairStat = EagleEye.statLoggerBuilder("eagleeye-tair").maxBackupIndex(2).buildSingleton();
		notifyStat = EagleEye.statLoggerBuilder("eagleeye-notify").maxBackupIndex(2).buildSingleton();
		metaqStat = EagleEye.statLoggerBuilder("eagleeye-metaq").maxBackupIndex(2).buildSingleton();
		localStat = EagleEye.statLoggerBuilder("eagleeye-local").maxBackupIndex(2).buildSingleton();
	}

	public static RpcStatLogIntercepter getInstance() {
		return singleton;
	}

	@Override
	public void afterEndTrace(RpcContext_inner ctx) {
		String bizResultCode = EagleEyeCoreUtils.defaultIfNull(
				ctx.getLocalAttribute(RpcContext_inner.EAGLEEYE_BIZ_RESULT_CODE_KEY), "");

		// 先以 aliasName 为准
		String traceName = ctx.getLocalAttribute(RpcContext_inner.EAGLEEYE_ALIAS_NAME_KEY);
		if (traceName == null) {
			// 取原来的 URL
			if (ctx.traceName != null && ctx.traceName.length() > 128) {
				traceName = ctx.traceName.substring(0, 128);
			} else {
				traceName = ctx.traceName;
			}
		}
		traceStat.stat(getFlowTestToken(ctx), traceName, ctx.resultCode, bizResultCode)
				.countAndSum(ctx.span1);
	}

	@Override
	public void afterEndRpc(RpcContext_inner ctx) {
		final int rpcType = ctx.getRpcType();
		switch (rpcType) {
		case EagleEye.TYPE_HSF_CLIENT:
			statHsfClient(hsfStat, ctx);
			break;
		case EagleEye.TYPE_TDDL:
			statTddl(tddlStat, ctx);
			break;
		case EagleEye.TYPE_TAIR:
			statTair(tairStat, ctx, ctx.callBackMsg);
			break;
		case EagleEye.TYPE_NOTIFY:
			statNotify(notifyStat, ctx);
			break;
		case EagleEye.TYPE_METAQ:
			statMetaQ(metaqStat, ctx);
			break;
		}
	}

	@Override
	public void afterRpcServerSend(RpcContext_inner ctx) {
		final int rpcType = ctx.getRpcType();
		switch (rpcType) {
		case EagleEye.TYPE_HSF_SERVER:
			statHsfServer(hsfStat, ctx);
			break;
		case EagleEye.TYPE_NOTIFY:
			statNotify(notifyStat, ctx);
			break;
		case EagleEye.TYPE_METAQ:
			statMetaQ(metaqStat, ctx);
			break;
		}
	}

	@Override
	public void afterEndLocal(RpcContext_inner context, LocalContext_inner ctx) {
		if (ctx.getRpcType() == EagleEye.TYPE_LOCAL) {
			String bizResultCode = EagleEyeCoreUtils.defaultIfNull(
					ctx.getLocalAttribute(RpcContext_inner.EAGLEEYE_BIZ_RESULT_CODE_KEY), "");
			localStat.stat(getFlowTestToken(ctx), ctx.remoteIp, ctx.serviceName, ctx.methodName, ctx.resultCode, bizResultCode)
					.countAndSum(ctx.span1);
		}
	}

	private void statNotify(StatLogger statLogger, RpcContext_inner ctx) {
		statLogger.stat(getFlowTestToken(ctx), ctx.remoteIp, ctx.serviceName, ctx.methodName, ctx.resultCode)
				.countAndSum(ctx.span1);
	}

	private void statTddl(StatLogger statLogger, RpcContext_inner ctx) {
		statLogger.stat(getFlowTestToken(ctx), ctx.remoteIp, ctx.serviceName, ctx.methodName, "", ctx.resultCode)
				.countAndSum(ctx.span1);
	}

	private void statTair(StatLogger statLogger, RpcContext_inner ctx, String ext) {
		statLogger.stat(getFlowTestToken(ctx), ctx.remoteIp, ctx.serviceName, ctx.methodName, ext, ctx.resultCode)
				.countAndSum(ctx.span1);
	}

	private void statHsfClient(StatLogger statLogger, RpcContext_inner ctx) {
		statHsf(statLogger, ctx, "@clnt");
	}

	private void statHsfServer(StatLogger statLogger, RpcContext_inner ctx) {
		statHsf(statLogger, ctx, "@srvr");
	}

	private void statHsf(StatLogger statLogger, RpcContext_inner ctx, String tag) {
		String bizResultCode = EagleEyeCoreUtils.defaultIfNull(
				ctx.getLocalAttribute(RpcContext_inner.EAGLEEYE_BIZ_RESULT_CODE_KEY), "");
		statLogger.stat(getFlowTestToken(ctx), tag, ctx.remoteIp, ctx.serviceName, ctx.methodName, ctx.resultCode, bizResultCode)
				.countAndSum(ctx.span1);
	}

	private void statMetaQ(StatLogger statLogger, RpcContext_inner ctx) {
		String methodName = ctx.methodName;
		if (methodName != null) {
			int i1 = methodName.indexOf(':');
			if (i1 > 0) {
				int i2 = methodName.indexOf(':', i1 + 1);
				if (i2 > i1) {
					methodName = methodName.substring(0, i2);
					statLogger.stat(getFlowTestToken(ctx), ctx.remoteIp, ctx.serviceName,
							methodName, ctx.resultCode).countAndSum(ctx.span1);
				}
			}
		}
	}

	/**
	 * 获取压测标记，如果没有标记，返回空串
	 * @param ctx
	 * @return
	 */
	private final String getFlowTestToken(AbstractContext ctx) {
		return EagleEyeCoreUtils.defaultIfNull(ctx.getUserData(RpcContext_inner.FLOW_CLUSTER_TEST_KEY),
				EagleEyeCoreUtils.EMPTY_STRING);
	}
}
