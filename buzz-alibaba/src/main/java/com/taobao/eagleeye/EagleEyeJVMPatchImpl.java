package com.taobao.eagleeye;

import jvm.EagleEyeHolder;
import jvm.IEagleEye;

/**
 * 支持 JVM 层面在 ExecutorService 线程切换时透明实现 EagleEye 上下文传输
 *
 * <AUTHOR>
 * @since 1.3.0，需要 AliJDK 1.7.0_51-40 以后的版本配合
 */
final class EagleEyeJVMPatchImpl implements IEagleEye {

	private static final EagleEyeJVMPatchImpl instance = new EagleEyeJVMPatchImpl();

	private EagleEyeJVMPatchImpl() {
	}

	public static IEagleEye setupInstance() {
		EagleEyeHolder.instance = instance;
		return instance;
	}

	@Override
	public Object exportRpcContext() {
		return EagleEye.currentRpcContext();
	}

	@Override
	public void importRpcContext(Object ctx) {
		EagleEye.setRpcContext(ctx);
	}

	@Override
	public void clearRpcContext() {
		EagleEye.clearRpcContext();
	}
}
