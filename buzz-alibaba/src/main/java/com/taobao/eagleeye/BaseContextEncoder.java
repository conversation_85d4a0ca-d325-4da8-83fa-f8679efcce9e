package com.taobao.eagleeye;

import java.io.IOException;

/**
 * EagleEye 日志格式编码，为针对异步队列调用做优化，不允许多线程并发调用
 * <AUTHOR>
 */
abstract class BaseContextEncoder {

	private int DEFAULT_BUFFER_SIZE = 256;
	private StringBuilder buffer = new StringBuilder(DEFAULT_BUFFER_SIZE);

	public abstract void encode(BaseContext ctx, EagleEyeAppender appender) throws IOException;

	protected final StringBuilder getBuffer() {
		StringBuilder buffer = this.buffer;
		if (buffer.length() < 64 * 1024) {
			buffer.delete(0, buffer.length());
		} else {
			this.buffer = buffer = null;
			this.buffer = buffer = new StringBuilder(DEFAULT_BUFFER_SIZE);
		}
		return buffer;
	}
}

/**
 * EagleEye RPC 日志的输出
 */
class DefaultRpcContextEncoder extends BaseContextEncoder {

	@Override
	public void encode(BaseContext base, EagleEyeAppender eea) throws IOException {
		AbstractContext ctx;
		if (base instanceof AbstractContext) {
			ctx = (AbstractContext) base;
		} else {
			return;
		}

		StringBuilder buffer = getBuffer();

		switch (ctx.logType) {
		case EagleEye.LOG_TYPE_TRACE:
			buffer.append(ctx.traceId).append('|')
					.append(ctx.startTime).append('|')
					.append(ctx.rpcType).append('|')
					.append(ctx.span1).append('|');
			if (ctx.rpcId != null && ctx.rpcId.length() >= 3) {
				// 只有 rpcId 长度是 3 以上才算合法嵌套(x.y) 打印 rpcId，否则默认当 0 处理
				// @since 1.2.6
				buffer.append(ctx.rpcId).append('|');
			}
			if (EagleEyeCoreUtils.isNotEmpty(ctx.resultCode)) {
				// @since 1.2.8
				buffer.append(ctx.resultCode).append('|');
			}
			buffer.append(ctx.traceName);
			break;
		case EagleEye.LOG_TYPE_RPC_CLIENT:
			buffer.append(ctx.traceId).append('|')
					.append(ctx.startTime).append('|')
					.append(ctx.rpcType).append('|')
					.append(ctx.rpcId).append('|')
					.append(ctx.serviceName).append('|')
					.append(ctx.methodName).append('|')
					.append(ctx.remoteIp).append('|')
					.append('[').append(ctx.span0).append(", ").append(ctx.span1).append(']').append('|')
					.append(ctx.resultCode).append('|')
					.append(ctx.requestSize).append('|')
					.append(ctx.responseSize);
			break;
		case EagleEye.LOG_TYPE_RPC_SERVER:
			buffer.append(ctx.traceId).append('|')
					.append(ctx.startTime).append('|')
					.append(ctx.rpcType).append('|')
					.append(ctx.rpcId).append('|');
			if (EagleEyeCoreUtils.isNotBlank(ctx.serviceName)) {
				buffer.append(ctx.serviceName).append('|');
				buffer.append(ctx.methodName).append('|');
			}
			if (EagleEyeCoreUtils.isNotBlank(ctx.resultCode)) {
				buffer.append(ctx.resultCode).append('|');
			}
			buffer.append(ctx.remoteIp).append('|')
					.append(ctx.span1).append('|')
					.append(ctx.responseSize);
			break;
		case EagleEye.LOG_TYPE_RPC_LOG:
			buffer.append(ctx.traceId).append('|')
					.append(ctx.logTime).append('|')
					.append(ctx.rpcType);
			if (EagleEyeCoreUtils.isNotBlank(ctx.rpcId)) {
				buffer.append('|').append(ctx.rpcId);
			}
			break;
		case EagleEye.LOG_TYPE_INDEX:
			buffer.append(ctx.traceId).append('|')
					.append(ctx.logTime).append('|')
					.append(ctx.rpcType).append('|')
					.append(ctx.traceName).append('|') // index
					.append(ctx.callBackMsg).append(EagleEyeCoreUtils.NEWLINE);
			eea.append(buffer.toString());
			// 不用追加 userData
			return;
		default:
			// ignore
			return;
		}

		if (EagleEyeCoreUtils.isNotBlank(ctx.callBackMsg)) {
			buffer.append('|').append(ctx.callBackMsg);
		}

		final int samplingInterval = EagleEye.getSamplingInterval();
		if (samplingInterval >= 2 && samplingInterval <= 9999) {
			buffer.append("|#").append(samplingInterval);
		}
		if (!AbstractContext.EMPTY_LOCALID.equals(ctx.localId) && ctx.localId != null) {
			buffer.append("|!").append(ctx.getLocalId());
		}

		ctx.logContextData(buffer);

		buffer.append(EagleEyeCoreUtils.NEWLINE);

		eea.append(buffer.toString());
	}
}

/**
 * Biz EagleEye 的输出
 */
class DefaultBizEncoder extends BaseContextEncoder {

	@Override
	public void encode(BaseContext ctx, EagleEyeAppender eea) throws IOException {
		StringBuilder buffer = getBuffer();
		buffer.append(ctx.traceId).append('|')
				.append(ctx.logTime).append('|')
				.append(ctx.rpcId).append('/')
				.append(ctx.logType).append('|')     // 新增的压测标记
				.append(ctx.serviceName).append('|') // domain
				.append(ctx.methodName).append('|')  // eventType
				.append(ctx.callBackMsg)             // msg
				.append(EagleEyeCoreUtils.NEWLINE);

		eea.append(buffer.toString());
	}
}

/**
 * 业务跟踪日志的输出
 */
class DefaultTraceEncoder extends BaseContextEncoder {

	/**
	 * 需要做换行和分隔符过滤
	 */
	static final int REQUIRED_LINE_FEED_ESCAPE = 1;

	private final char entryDelimiter;
	private FastDateFormat fmt = new FastDateFormat();

	DefaultTraceEncoder(char entryDelimiter) {
		this.entryDelimiter = entryDelimiter;
	}

	@Override
	public void encode(BaseContext ctx, EagleEyeAppender eea) throws IOException {
		final char entryDelimiter = this.entryDelimiter;
		StringBuilder buffer = getBuffer();
		fmt.formatAndAppendTo(ctx.logTime, buffer);             // time
		buffer.append(entryDelimiter)
				.append(ctx.traceId).append(entryDelimiter)     // traceId
				.append(ctx.rpcId).append(entryDelimiter)       // rpcId
				.append(ctx.serviceName).append(entryDelimiter) // domain
				.append(ctx.methodName).append(entryDelimiter)  // eventType
				.append(ctx.logType).append(entryDelimiter)		// clusterTest
				.append(ctx.traceName).append(entryDelimiter);  // bizType and bizValue

		if (ctx.rpcType == REQUIRED_LINE_FEED_ESCAPE) {         // logContent
			EagleEyeCoreUtils.appendLog(ctx.callBackMsg, buffer, '\0');
		} else {
			buffer.append(ctx.callBackMsg);
		}

		buffer.append(EagleEyeCoreUtils.NEWLINE);

		eea.append(buffer.toString());
	}
}
