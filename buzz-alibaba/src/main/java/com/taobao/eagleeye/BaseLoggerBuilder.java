package com.taobao.eagleeye;

/**
 * 协助配置 Logger 的参数
 *
 * <AUTHOR>
 * @since 1.3.0
 */
class BaseLoggerBuilder<T extends BaseLoggerBuilder<T>> {

	protected final String loggerName;

	protected String filePath = null;

	protected long maxFileSize = EagleEye.MAX_BIZ_LOG_FILE_SIZE;

	protected char entryDelimiter = '|';

	protected int maxBackupIndex = 3;

	/**
	 * @param loggerName 日志名，决定了默认生成的文件名
	 */
	BaseLoggerBuilder(String loggerName) {
		this.loggerName = loggerName;
	}

	/**
	 * @param logFilePath 日志路径，如果不以“/”开头，那么会认为是在
	 * 			EagleEye 的默认日志路径（~/logs/eagleeye/）下输出的相对路径
	 * @return
	 */
	public T logFilePath(String logFilePath) {
		return configLogFilePath(logFilePath, EagleEye.EAGLEEYE_LOG_DIR);
	}

	/**
	 * @param appFilePath 应用日志路径，如果不以“/”开头，那么会认为是在
	 * 			应用的默认日志路径（~/$project.name/logs/）下输出的相对路径
	 * @return
	 */
	public T appFilePath(String appFilePath) {
		return configLogFilePath(appFilePath, EagleEye.APP_LOG_DIR);
	}

	/**
	 * @param baseLogFilePath 基础日志路径，如果不以“/”开头，那么会认为是在
	 * 			默认基础日志路径（~/logs/）下输出的相对路径
	 * @return
	 */
	public T baseLogFilePath(String baseLogFilePath) {
		return configLogFilePath(baseLogFilePath, EagleEye.BASE_LOG_DIR);
	}

	@SuppressWarnings("unchecked")
	private T configLogFilePath(String filePathToConfig, String basePath) {
		EagleEyeCoreUtils.checkNotNullEmpty(filePathToConfig, "filePath");
		if (filePathToConfig.charAt(0) != '/') {
			filePathToConfig = basePath + filePathToConfig;
		}
		this.filePath = filePathToConfig;
		return (T) this;
	}

	/**
	 * 日志文件滚动大小，单位是 MB，不能小于 10MB
	 * @param maxFileSizeMB
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public T maxFileSizeMB(long maxFileSizeMB) {
		if (maxFileSize < 10) {
			throw new IllegalArgumentException("设置文件大小至少要 10MB: " + maxFileSizeMB);
		}
		this.maxFileSize = maxFileSizeMB * 1024 * 1024;
		return (T) this;
	}

	/**
	 * 设置文件滚动后，最大保留的归档数量。
	 * 例如设置 maxBackupIndex=3，即最多保存 file.log.1、file.log.2、file.log.3
	 * @param maxBackupIndex
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public T maxBackupIndex(int maxBackupIndex) {
		if (maxBackupIndex < 1) {
			throw new IllegalArgumentException("归档数量至少为 1: " + maxBackupIndex);
		}
		this.maxBackupIndex = maxBackupIndex;
		return (T) this;
	}

	/**
	 * @param entryDelimiter 设置字段分隔符
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public T entryDelimiter(char entryDelimiter) {
		this.entryDelimiter = entryDelimiter;
		return (T) this;
	}

	String getLoggerName() {
		return loggerName;
	}
}
