package com.taobao.eagleeye;

/**
 * 本地调用上下文
 *
 * <AUTHOR>
 * @since 1.2.9
 */
class LocalContext_inner extends AbstractContext {

	LocalContext_inner localParent;
	volatile boolean isEnd; // 本地调用是否结束，防止多次调用end

	LocalContext_inner(String _traceId, String _rpcId) {
		super(_traceId, _rpcId);
	}

	LocalContext_inner(String _traceId, String _rpcId, String _localId) {
		super(_traceId, _rpcId);
		this.localId = _localId;
	}
}
