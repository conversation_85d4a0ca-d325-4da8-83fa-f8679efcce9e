package com.taobao.eagleeye;

import static com.taobao.eagleeye.EagleEyeCoreUtils.EMPTY_STRING;
import static com.taobao.eagleeye.EagleEyeCoreUtils.appendWithBlankCheck;

import java.io.File;
import java.io.PrintWriter;
import java.io.StringWriter;
import java.net.URL;
import java.nio.charset.Charset;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * 鹰眼是一个监控庞大的分布式环境内一个外部请求进来开始递归调用各个center的调用关系的系统。<br>
 * 鹰眼有利于系统问题排查，性能优化，以及容量规划。
 * <p>
 * 使用 EagleEye 埋点的中间件，请从
 * <a href="http://confluence.taobao.ali.com:8080/pages/viewpage.action?pageId=192046765">这里</a>
 * 获取中间件埋点的详细文档。
 * </p>
 * <p>
 * 使用 EagleEye 埋点的业务系统，请从
 * <a href="http://confluence.taobao.ali.com:8080/pages/viewpage.action?pageId=192046759">这里</a>
 * 获取业务埋点的详细文档。
 * </p>
 * @see <a href="http://confluence.taobao.ali.com:8080/display/HSF/EagleEye">EagleEye 文档</a>
 * @see <a href="http://eagleeye.taobao.net:9999">EagleEye 系统</a>
 */
public final class EagleEye {

	/**
	 * 记录了当前 EagleEye 所加载的位置
	 * @since 1.3.4
	 */
	public static final String CLASS_LOCATION = getEagleEyeLocation();

	/**
	 * 用户目录
	 */
	static final String USER_HOME = locateUserHome();

	/**
	 * 基础日志默认放置的位置：~/logs/
	 */
	static final String BASE_LOG_DIR = locateBaseLogPath();

	/**
	 * EagleEye 日志默认放置的位置：~/logs/eagleeye/
	 */
	static final String EAGLEEYE_LOG_DIR = locateEagleEyeLogPath();

	/**
	 * 应用日志默认放置的位置：~/${project.name}/logs/
	 */
	static final String APP_LOG_DIR = locateAppLogPath();

    /** 强制指定写日志用的编码 */
	static final Charset DEFAULT_CHARSET = getDefaultOutputCharset();

    /**
     * EagleEye RPC 日志文件名
     */
	static final String EAGLEEYE_RPC_LOG_FILE = EagleEye.EAGLEEYE_LOG_DIR + "eagleeye.log";
	/**
	 * EagleEye BIZ 日志文件名
	 */
	static final String EAGLEEYE_BIZ_LOG_FILE = EagleEye.EAGLEEYE_LOG_DIR + "biz_eagleeye.log";
	/**
	 * EagleEye Self 日志文件名
	 */
	static final String EAGLEEYE_SELF_LOG_FILE = EagleEye.EAGLEEYE_LOG_DIR + "eagleeye-self.log";

	static final long MAX_SELF_LOG_FILE_SIZE = 200 * 1024 * 1024; // 200MB
	static final long MAX_RPC_LOG_FILE_SIZE = 300 * 1024 * 1024; // 300MB
	static final long MAX_BIZ_LOG_FILE_SIZE = 300 * 1024 * 1024; // 300MB

    /** 业务某些字段可接受最大长度  */
    static public final int MAX_BIZ_LOG_SIZE = 4 * 1024;

    /** index 可接受字符串最大长度  */
    static public final int MAX_INDEX_SIZE = 512;

    /** userData 可接受单个数据的最大长度  */
    static public final int MAX_USER_DATA_ENTRY_SIZE = 512;

    /** userData 可接受全部数据的最大长度  */
    static public final int MAX_USER_DATA_TOTAL_SIZE = 1024;

	/** RPC 日志输出 */
    static AsyncAppender rpcAppender;

    /** BIZ 日志输出 */
    static AsyncAppender bizAppender;

    static TraceLogger bizEagleEyeLogger;

    /** Self 调试日志输出 */
    static EagleEyeAppender selfAppender = createSelfLogger();

	/**
	 * 调试日志 10 秒最多可输出的 10 个异常
	 */
    static private TokenBucket exceptionBucket = new TokenBucket(10, TimeUnit.SECONDS.toMillis(10));

	/** 正常 TRACE 开始的 RpcId */
    static public final String ROOT_RPC_ID = "0";
    /** 缺少 TraceId，重新开始的 RpcId */
    static public final String MAL_ROOT_RPC_ID = "9";

	/** 用于入口分类的 UserData key */
	static public final String URL_CLASSIFIER_KEY = RpcContext_inner.URL_CLASSIFIER_KEY;

    @Deprecated
    static public final int TYPE_START_TRACE = 1;
    @Deprecated
    static public final int TYPE_END_TRACE = 2;
    @Deprecated
    static public final int TYPE_START_RPC = 3;
    @Deprecated
    static public final int TYPE_END_RPC = 4;
    @Deprecated
    static public final int TYPE_ANNOTATE_RPC = 5;
    @Deprecated
    static public final int TYPE_ANNOTATE_TRACE = 6;

    /*
     * RpcContext 对应的日志类型
     */
    static final int LOG_TYPE_BIZ = 0;
    static final int LOG_TYPE_TRACE = 1;
    static final int LOG_TYPE_RPC_CLIENT = 2;
    static final int LOG_TYPE_RPC_SERVER = 3;
    static final int LOG_TYPE_RPC_LOG = 4;
    static final int LOG_TYPE_INDEX = 5;

    /**
     * 忽略不处理，用于防御某些不配对的调用埋点
     * @since 1.3.0
     * */
    static final int LOG_TYPE_EVENT_ILLEGAL = -255;

    /*
     * 补充关于 RpcType 的约定，用"个位"标识不同的 RPC 类型，比如
     * 0: 前端系统
     * 1: RPC 客户端 (HSF/DUBBO)
     * 2: RPC 服务端 (HSF/DUBBO)
     * 3: 消息中间件 (NOTIFY/METAQ)
     * 4: sql 查询 (TDDL)
     * 5: 键值结构 (TAIR/TFS)
     * 6: 其他 (SEARCH)
     * 7: 未使用
     * 8: 未使用
     * 9: 索引
     *
     * 新的 RPC 类型加入的时候，在前面增加，如新增 RPC 实现类型，则分配的客户端
     * 类型为 11、21、31...
     */
    static public final int TYPE_TRACE = 0;

    static public final int TYPE_HSF_CLIENT = 1;

    static public final int TYPE_HSF_SERVER = 2;

    static public final int TYPE_NOTIFY = 3;

    static public final int TYPE_TDDL = 4;

    static public final int TYPE_TAIR = 5;

    static public final int TYPE_SEARCH = 6;

    static public final int TYPE_INDEX = 9;

    static public final int TYPE_JOB_MASTER = 11;

    static public final int TYPE_JOB_SLAVE = 12;

    static public final int TYPE_METAQ = 13;

    static public final int TYPE_TFS = 15;

    static public final int TYPE_ALIPAY = 16;

    static public final int TYPE_HTTP_CLIENT = 25;

    static public final int TYPE_LOCAL = 30;

    /** 预置的RPC标签 */
    @Deprecated
    static public final String TAG_CLIENT_SEND = "CS";
    /** 预置的RPC标签 */
    @Deprecated
    static public final String TAG_SERVER_RECV = "SR";
    /** 预置的RPC标签 */
    @Deprecated
    static public final String TAG_SERVER_SEND = "SS";
    /** 预置的RPC标签 */
    @Deprecated
    static public final String TAG_CLIENT_RECV = "CR";

    /* 兼容老代码 */
    @Deprecated
    static public final String TAG_CLIENT_SEND_OLD = "ClientSend";
    @Deprecated
    static public final String TAG_SERVER_RECV_OLD = "ServerRecv";
    @Deprecated
    static public final String TAG_SERVER_SEND_OLD = "ServerSend";
    @Deprecated
    static public final String TAG_CLIENT_RECV_OLD = "ClientRecv";

    /** 返回成功 */
    static public final String RPC_RESULT_SUCCESS = "00";

    /** 返回失败，一般是业务失败 */
    static public final String RPC_RESULT_FAILED = "01";

    /** 返回失败，一般是 RPC 层错误 */
    static public final String RPC_RESULT_RPC_ERROR = "02";

    /** 返回失败，一般是超时错误 */
    static public final String RPC_RESULT_TIMEOUT = "03";

    /**
     * 返回软错误，一般用于资源找不到、未命中、加锁未成功、
	 * 版本不一致导致未更新等情况
	 */
    static public final String RPC_RESULT_SOFT_ERROR = "04";

    /** 转换成 Map 时 traceId 使用的 key */
    static public final String TRACE_ID_KEY = "traceId";

    /** 转换成 Map 时 rpcId 使用的 key */
    static public final String RPC_ID_KEY = "rpcId";

    /** 转换成 Map 时 userData 使用的 key */
    static public final String USER_DATA_KEY = "eagleEyeUserData";

    /** @deprecated 前端埋点逻辑不使用这个字段 */
    @Deprecated
    static public final String EAGLEEYE_TRACEID = "tb_eagleeye_traceid";

    static public final char ENTRY_SEPARATOR = (char) 0x12;
    /** @deprecated */
    @Deprecated
    static public final char KV_SEPARATOR = (char) 0x1;   // Notify 不允许使用的分隔符，不能在 UserData 中使用
    static public final char KV_SEPARATOR2 = (char) 0x14;

    static public final char ULC_SEPARATOR = ENTRY_SEPARATOR;
    static public final String ULC_EAGLEEYE_APPID = "ulc";

    /** RPC 日志开关，关闭之后不会记录 rpc 日志，但是会照生成 EagleEye 上下文 */
    static private AtomicBoolean rpcRecord = new AtomicBoolean(true);
    /** BIZ 日志开关，关闭之后不会记录 service 日志 */
    static private AtomicBoolean bizRecord = new AtomicBoolean(true);

	/** 全链路集群压测的 UserData key */
    public static final char CLUSTER_TEST_KEY = 't';
    /** 全链路集群压测的开关，关闭之后不会透传压测标志 */
    static private AtomicBoolean clusterTestEnabled = new AtomicBoolean(true);
    /** 数据透传开关，关闭之后不会透传任何数据 */
    static private AtomicBoolean userDataEnabled = new AtomicBoolean(true);

    /** 参数 DUMP 开关，关闭之后不会再打印 DUMP 日志 */
    static private AtomicBoolean logDumpEnabled = new AtomicBoolean(true);

    /** @deprecated Tair 埋点不会使用这个标记 */
    @Deprecated
    static public final String EAGLEEYE_TAIR_SERVICENAME = "EETair";

    // index 索引缩写
    static private final long MAX_INDEX_TABLE_SIZE = 8192;
    static final NonBlockingHashMap<String, String> indexes = new NonBlockingHashMap<String, String>();
    /** 索引刷新时间 */
    static private final String INDEX_OUTPUT_TRACE_ID = "54007";
    static private final String INDEX_NOT_INDEXED = "ffffffff";  // 索引映射表超过指定数量时返回的索引

    /**
     * 采样频率 1/x，有效范围在 [1, 9999] 之间，超出范围的数值都作为全采样处理。
     */
    static private volatile int samplingInterval = 1;

    /**
     * 注册的上下文监听器
     */
    static CopyOnWriteArrayList<EagleEyeContextListener> listeners =
    		new CopyOnWriteArrayList<EagleEyeContextListener>();

	/**
	 * 返回 EagleEye class 的加载位置
	 * @return
	 */
	static final String getEagleEyeLocation() {
		try {
			URL resource = EagleEye.class.getProtectionDomain().getCodeSource().getLocation();
			if (resource != null) {
				return resource.toString();
			}
		} catch (Throwable t) {
			// ignore
		}
		return "unknown location";
	}

	/**
	 * 返回 EagleEye 输出日志的字符编码，默认从 ${EAGLEEYE.CHARSET} 加载，
	 * 如果未设置，按照 GB18030、GBK、UTF-8 的顺序依次尝试。
	 * @return
	 * @since 1.3.0
	 */
	static final Charset getDefaultOutputCharset() {
		Charset cs;
		String charsetName = EagleEyeCoreUtils.getSystemProperty("EAGLEEYE.CHARSET");
		if (EagleEyeCoreUtils.isNotBlank(charsetName)) {
			charsetName = charsetName.trim();
			try {
				cs = Charset.forName(charsetName);
				if (cs != null) {
					return cs;
				}
			} catch (Exception e) {
				// quietly
			}
		}
		try {
			cs = Charset.forName("GB18030");
		} catch (Exception e) {
			try {
				cs = Charset.forName("GBK");
			} catch (Exception e2) {
				cs = Charset.forName("UTF-8");
			}
		}
		return cs;
	}

	/**
	 * 根据系统参数，获取用户目录，获取失败时返回 /tmp/
	 * @since 1.3.0
	 * @return 返回路径，结尾包含“/”
	 */
	static private final String locateUserHome() {
		String userHome = EagleEyeCoreUtils.getSystemProperty("user.home");
		if (EagleEyeCoreUtils.isNotBlank(userHome)) {
			if (!userHome.endsWith(File.separator)) {
				userHome += File.separator;
			}
		} else {
			userHome = "/tmp/";
		}
		return userHome;
	}

	/**
	 * 根据系统参数，设置基础的日志目录。判断优先级：
	 * <ol>
	 * <li>如果设置了 ${JM.LOG.PATH}，在 ${JM.LOG.PATH}/ 下面输出日志。
	 * <li>在 ${user.home}/logs/ 下面输出日志。
	 * </ol>
	 * @since 1.3.0
	 * @return 返回路径，结尾包含“/”
	 */
	static private final String locateBaseLogPath() {
		String tmpPath = EagleEyeCoreUtils.getSystemProperty("JM.LOG.PATH");
		if (EagleEyeCoreUtils.isNotBlank(tmpPath)) {
			if (!tmpPath.endsWith(File.separator)) {
				tmpPath += File.separator;
			}
		} else {
			tmpPath = USER_HOME + "logs" + File.separator;
		}
		return tmpPath;
	}

	/**
	 * 根据系统参数，设置 EagleEye 的日志目录。判断优先级：
	 * <ol>
	 * <li>如果设置了 ${EAGLEEYE.LOG.PATH}，在 ${EAGLEEYE.LOG.PATH}/ 下面输出日志。
	 * <li>在 ${BASE_LOG_DIR}/eagleeye/ 下面输出日志。
	 * </ol>
	 * @since 1.2.9
	 * @return 返回路径，结尾包含“/”
	 */
	static private final String locateEagleEyeLogPath() {
		String tmpPath = EagleEyeCoreUtils.getSystemProperty("EAGLEEYE.LOG.PATH");
		if (EagleEyeCoreUtils.isNotBlank(tmpPath)) {
			if (!tmpPath.endsWith(File.separator)) {
				tmpPath += File.separator;
			}
		} else {
			tmpPath = BASE_LOG_DIR + "eagleeye" + File.separator;
		}
		return tmpPath;
	}

	/**
	 * 根据系统参数，设置 EagleEye 的日志目录。判断优先级：
	 * <ol>
	 * <li>如果设置了 ${project.name}，在 ${user.home}/${project.name}/logs/ 下面输出日志。
	 * <li>在 ${EAGLEEYE_LOG_DIR}/ 下面输出日志。
	 * </ol>
	 * @since 1.3.0
	 * @return 返回路径，结尾包含“/”
	 */
	static private final String locateAppLogPath() {
		String appName = EagleEyeCoreUtils.getSystemProperty("project.name");
		if (EagleEyeCoreUtils.isNotBlank(appName)) {
			return USER_HOME + appName + File.separator + "logs" + File.separator;
		} else {
			return EAGLEEYE_LOG_DIR;
		}
	}

	static private final EagleEyeAppender createSelfLogger() {
		EagleEyeRollingFileAppender selfAppender = new EagleEyeRollingFileAppender(
				EAGLEEYE_SELF_LOG_FILE, EagleEyeCoreUtils.getSystemPropertyForLong(
	    				"EAGLEEYE.LOG.SELF.FILESIZE", MAX_SELF_LOG_FILE_SIZE), false);
		selfAppender.setMaxBackupIndex((int) EagleEyeCoreUtils.getSystemPropertyForLong(
				"EAGLEEYE.LOG.SELF.BACKUPSIZE", 1));
		// selfAppender 不需要放入 watcher 中，需要被 LogDaemon 单独处理
		return new SyncAppender(selfAppender);
	}

	static private final void createEagleEyeLoggers() {
		// 配置日志输出
	    rpcAppender = new AsyncAppender((int) EagleEyeCoreUtils.getSystemPropertyForLong(
				"EAGLEEYE.LOG.RPC.QUEUESIZE", 8192), 0);
	    bizAppender = new AsyncAppender((int) EagleEyeCoreUtils.getSystemPropertyForLong(
				"EAGLEEYE.LOG.BIZ.QUEUESIZE", 5120), 0);

	    EagleEyeRollingFileAppender rpcLogger = new EagleEyeRollingFileAppender(
	    		EAGLEEYE_RPC_LOG_FILE, EagleEyeCoreUtils.getSystemPropertyForLong(
	    				"EAGLEEYE.LOG.RPC.FILESIZE", MAX_RPC_LOG_FILE_SIZE), true);
	    rpcLogger.setMaxBackupIndex((int) EagleEyeCoreUtils.getSystemPropertyForLong(
				"EAGLEEYE.LOG.RPC.BACKUPSIZE", 2));
		rpcAppender.start(rpcLogger, new DefaultRpcContextEncoder(), "RpcLog");
		EagleEyeLogDaemon.watch(rpcAppender);

		EagleEyeRollingFileAppender bizLogger = new EagleEyeRollingFileAppender(
	    		EAGLEEYE_BIZ_LOG_FILE, EagleEyeCoreUtils.getSystemPropertyForLong(
	    				"EAGLEEYE.LOG.BIZ.FILESIZE", MAX_BIZ_LOG_FILE_SIZE), true);
		bizLogger.setMaxBackupIndex((int) EagleEyeCoreUtils.getSystemPropertyForLong(
				"EAGLEEYE.LOG.BIZ.BACKUPSIZE", 2));
		bizAppender.start(bizLogger, new DefaultBizEncoder(), "BizLog");
		EagleEyeLogDaemon.watch(bizAppender);

		bizEagleEyeLogger = traceLoggerBuilder("service-eagleeye")
				.appender(bizAppender).buildSingleton();
	}

    /*
     * EagleEye 初始化
     */
    static {
    	initEagleEye();
    }

    private static void initEagleEye() {
		try {
			selfLog("[INFO] EagleEye started (" + CLASS_LOCATION + ")" +
					", classloader=" + EagleEye.class.getClassLoader() +
					", pid=" + EagleEyeCoreUtils.getCurrrentPid());
		} catch (Throwable e) {
			selfLog("[INFO] EagleEye started (" + CLASS_LOCATION +
					"), pid=" + EagleEyeCoreUtils.getCurrrentPid());
		}
		try {
    		createEagleEyeLoggers();
    	} catch (Throwable e) {
    		selfLog("[ERROR] fail to create EagleEye logger", e);
    	}
    	try {
    		EagleEyeLogDaemon.start();
		} catch (Throwable e) {
			selfLog("[ERROR] fail to start EagleEyeLogDaemon", e);
		}
    	try {
    		StatLogController.start();
    	} catch (Throwable e) {
    		selfLog("[ERROR] fail to start StatLogController", e);
    	}
    	try {
    		ScheduleTaskController.start();
    	} catch (Throwable e) {
    		selfLog("[ERROR] fail to start ScheduleTaskController", e);
    	}
    	try {
    		EagleEyeJVMPatchImpl.setupInstance();
    	} catch (Throwable e) {
    		selfLog("[ERROR] fail to setup EagleEyeJVMPatchImpl", e);
    	}

    	// 加载默认监听器
    	EagleEye.addRpcContextListener(ThreadCpuTimeIntercepter.getInstance());
    	EagleEye.addRpcContextListener(RpcStatLogIntercepter.getInstance());
    }

    /**
     * 停止 EagleEye 相关的活动线程，停止后 StatLogger 以及使用了异步处理的日志输出都会不可用
     */
    public static void shutdown() {
		selfLog("[WARN] EagleEye is shutting down (" + CLASS_LOCATION +
				"), pid=" + EagleEyeCoreUtils.getCurrrentPid());

		// 刷新日志缓存
		EagleEye.flush();

		// 通知监听器，清理相关资源
		for (EagleEyeContextListener listener : listeners) {
			listener.beforeEagleEyeShutdown();
		}

		// 停止定时任务的调度
		try {
			ScheduleTaskController.stop();
			EagleEye.selfLog("[INFO] ScheduleTaskController stopped");
		} catch (Throwable e) {
			selfLog("[ERROR] fail to stop ScheduleTaskController", e);
		}

		// 关闭 StatLogger 定时，清理相关资源
    	try {
    		StatLogController.stop();
    		EagleEye.selfLog("[INFO] StatLogController stopped");
    	} catch (Throwable e) {
    		selfLog("[ERROR] fail to stop StatLogController", e);
    	}

    	// 关闭日志输出的相关资源
    	try {
    		EagleEyeLogDaemon.stop();
    		EagleEye.selfLog("[INFO] EagleEyeLogDaemon stopped");
		} catch (Throwable e) {
			selfLog("[ERROR] fail to stop EagleEyeLogDaemon", e);
		}

    	// 最后，关闭 self 日志
    	EagleEye.selfLog("[WARN] EagleEye shutdown successfully (" + CLASS_LOCATION +
				"), pid=" + EagleEyeCoreUtils.getCurrrentPid());
    	try {
    		selfAppender.close();
		} catch (Throwable e) {
			// ignore
		}
    }

    private EagleEye() {
    }

















    /*
     * rpc 日志记录开关
     */
    static public void turnRpcOn() {
    	selfLog("[INFO] turnRpcOn");
    	rpcRecord.set(true);
    }

    static public void turnRpcOff() {
    	selfLog("[INFO] turnRpcOff");
    	rpcRecord.set(false);
    }

    static public final boolean isRpcOff() {
        return !rpcRecord.get();
    }

   /*
    * 业务日志记录开关
    */
    static public void turnBizOn() {
    	selfLog("[INFO] turnBizOn");
    	bizRecord.set(true);
    }

    static public void turnBizOff() {
    	selfLog("[INFO] turnBizOff");
    	bizRecord.set(false);
    }

    static public final boolean isBizOff() {
        return !bizRecord.get();
    }

    /*
     * rpc 日志采样频率
     */
    /**
     * @return 采样频率 1/x
     * @since 1.2.0
     */
    static public int getSamplingInterval() {
    	return samplingInterval;
    }

    /**
     * @param interval 采样频率 1/x，有效范围在 [1, 9999] 之间，超出范围的数值都作为全采样处理。
     * @since 1.2.0
     */
    static public void setSamplingInterval(int interval) {
    	if (interval < 1 || interval > 9999) {
    		interval = 1;
    	}
    	selfLog("[INFO] setSamplingInterval=" + interval);
    	samplingInterval = interval;
    }

    /*
     * 透传开关
     */
	/**
	 * 开启/关闭透传功能
	 * @param enable
	 * @since 1.2.7
	 */
	static public void setUserDataEnabled(boolean enable) {
		selfLog("[INFO] setUserDataEnable: " + enable);
		userDataEnabled.set(enable);
	}

	/**
	 * 检查是否透传
	 * @return
	 * @since 1.2.7
	 */
	static public final boolean isUserDataEnabled() {
		return userDataEnabled.get();
	}

	/*
	 * 全链路压测标记透传开关
	 */
	/**
	 * 开启/关闭全链路压测标记透传功能
	 * @param enable
	 * @since 1.2.7
	 */
	static public void setClusterTestEnabled(boolean enable) {
		selfLog("[INFO] setClusterTestEnable: " + enable);
		clusterTestEnabled.set(enable);
	}

	/**
	 * 检查是否透传全链路压测标记
	 * @return
	 * @since 1.2.7
	 */
	static public final boolean isClusterTestEnabled() {
		return clusterTestEnabled.get();
	}

	/*
	 * 日志 DUMP 开关
	 */
	/**
	 * 开启/关闭日志 DUMP 功能
	 * @param enable
	 * @since 1.3.0
	 */
	static public void setLogDumpEnabled(boolean enable) {
		selfLog("[INFO] setLogDumpEnabled: " + enable);
		logDumpEnabled.set(enable);
	}

	/**
	 * 检查是否开启日志 DUMP 功能，这是一个全局配置，不是针对当前上下文做检查的
	 * @return
	 * @since 1.3.0
	 * @see #isContextDumpEnabled() 检查当前上下文是否需要做日志 DUMP
	 */
	static public final boolean isLogDumpEnabled() {
		return logDumpEnabled.get();
	}














    /**
     * 开启新的trace。该接口仅提供给最源头的前中间件或自己启动的定时程序调用，使用该接口时，
     * 必须最后调用endTrace结束。
     * @param traceId 全局唯一的id，如果传入的值为空或者null，系统会自动生成
     * @param traceName 用户自定义的入口标识值，不能为 <code>null</code>，
     * 				建议传入能够唯一标识入口的数据，例如用户访问网络的 http url
     */
    static public void startTrace(String traceId, String traceName) {
    	startTrace(traceId, null, traceName);
    }

    /**
     * 开启新的trace，该接口仅提供给最源头的前中间件或自己启动的定时程序调用，
     * 支持配置 rpcId 来开启一个嵌套的调用链。使用该接口时，必须最后调用endTrace结束。
     * @param traceId 全局唯一的id，如果传入的值为空或者null，系统会自动生成
     * @param rpcId 额外指定 rpcId，如果为 <code>null</code>，使用 {@link #ROOT_RPC_ID}
     * @param traceName 用户自定义的入口标识值，不能为 <code>null</code>，
     * 				建议传入能够唯一标识入口的数据，例如用户访问网络的 http url
     * @since 1.2.6
     */
    static public void startTrace(String traceId, String rpcId, String traceName) {
    	if (traceName == null) {
    		return;
    	}

    	traceId = EagleEyeCoreUtils.trim(traceId);

    	if (!EagleEyeCoreUtils.isValidTraceId(traceId)) {
    		traceId = TraceIdGenerator.generate();
    		rpcId = EagleEye.ROOT_RPC_ID;
    	} else if (!EagleEyeCoreUtils.isValidRpcId(rpcId)) {
    		rpcId = EagleEye.ROOT_RPC_ID;
    	}

    	RpcContext_inner ctx = RpcContext_inner.get();
    	if (ctx != null && ctx.traceId != null) {
    		// 重复 startTrace 的检测
    		if (!ctx.traceId.equals(traceId) || !traceName.equals(ctx.traceName)) {
    			// 说明有潜在的埋点问题，先把前面那个调用链结束掉
    			selfLog("[WARN] duplicated startTrace detected, overrided " + ctx.traceId +
    					" (" + ctx.traceName + ") to " + traceId + " (" + traceName + ")");
    			endTrace();
    		} else {
    			// traceId 和 traceName 都相同，说明是 EagleEyeFilter 和 tbsession 有重复埋点
    			return;
    		}
    	}

    	try {
    		ctx = new RpcContext_inner(traceId, rpcId);
    		RpcContext_inner.set(ctx);
    		ctx.startTrace(traceName);
    	} catch (Throwable re) {
    		selfLog("[ERROR] startTrace", re);
    	}
    }

    /**
     * 特殊的startTrace 接口，只产生、传递 traceId，不记录本地日志。
     * @param traceId 全局唯一的id，如果传入的值为空或者null，系统会自动生成
     * @deprecated 不建议使用
     */
    @Deprecated
    static public String startTrace4Top(String traceId) {
    	startTrace(traceId, "from://top");
        return EagleEye.getTraceId();
    }

    /**
     * 结束一次跟踪，Threadlocal 变量会被清空，调用了startTrace及startTrace4Top的必须在finally或者最后调用该接口。
     */
    static public void endTrace() {
        endTrace(null, TYPE_TRACE);
    }

    /**
     * 结束一次跟踪，Threadlocal 变量会被清空，调用了 startTrace 及 startTrace4Top 的必须在finally或者最后调用该接口。
     * @param resultCode
     * @param type
     * @since 1.2.0
     */
    static public void endTrace(String resultCode, int type) {
        try {
            // find root context
            RpcContext_inner root = RpcContext_inner.get();
            if (null == root) {
                return;
            }
            // 取得根 trace
            while (null != root.parentRpc) {
                root = root.parentRpc;
            }
            root.endTrace0(resultCode, type);
            commitRpcContext(root);
        } catch (IllegalContextException ice) {
        	// quietly
        } catch (Throwable re) {
        	selfLog("[ERROR] endTrace", re);
        } finally {
            clearRpcContext();
        }
    }

    static final RpcContext_inner createContextIfNotExists(final boolean setToThreadLocal) {
		final RpcContext_inner ctx = RpcContext_inner.get();
		if (null == ctx) {
			final RpcContext_inner newCtx = new RpcContext_inner(TraceIdGenerator.generate(), MAL_ROOT_RPC_ID);
			if (setToThreadLocal) {
				// 在这里设置的 ctx，有可能无法释放，例如在没有 startTrace 的上下文中直接
				// putUserData()，之后没办法释放 RpcContext
				RpcContext_inner.set(newCtx);
			}
			return newCtx;
		}
		return ctx;
    }

    /**
     * 在trace中记录业务关键信息，日志会放到biz_eagleeye.log文件中，这是 {@link #traceLogger(String)}
     * 的特殊实现。
     *
     * @deprecated 对于新业务埋点，不建议继续使用这个 API，请使用 {@link #traceLogger(String)}，这样可以
     * 使不同业务的日志输出到单独的文件
     *
     * @param domain
     *            使用eagleEye的业务方ID
     * @param eventType
     *            业务方后续查询记录使用的关键字，为了标示当前这条记录的key，日志存储的使用会自动追加appid+time+eventType
     * @param logContent
     *            业务信息， 不能有回车换行等符号
     */
    @Deprecated
    static public void businessTag(String domain, String eventType, String logContent) {
    	bizEagleEyeLogger.logLine(domain, eventType, logContent);
    }

    /**
     * 返回当前线程关联的RPC调用上下文。仅供中间件调用。可能返回 NULL。
     * 目前是以 Map 形式用来序列化 RpcContext，以便网络传输时序列化可以兼容新老版本。
     * @see #setRpcContext(Object) 重新还原 RpcContext
     */
    static public Object currentRpcContext() {
        try {
            RpcContext_inner ctx = RpcContext_inner.get();
            if (null != ctx) {
                return ctx.toMap();
            }
        } catch (Throwable re) {
        	selfLog("[ERROR] currentRpcContext", re);
        }
        return null;
    }

    /**
     * 直接取得当前的 RpcContext，用于备份 RPC 调用上下文（不做 Map 转换）
     * @return
     * @see #setRpcContext(RpcContext_inner) 还原 RpcContext
     */
    static public RpcContext_inner getRpcContext() {
    	 return RpcContext_inner.get();
    }

    /**
     * 创建 EagleEye 调用上下文，但是不放置到 ThreadLocal。这是为了支持完全无 ThreadLocal
     * 而特别设置的内部方法，在不依赖 ThreadLocal 的场景使用。
     * @param traceId 输入 <code>null</code> 会自动生成
     * @param rpcId 输入 <code>null</code> 会使用 {@link #MAL_ROOT_RPC_ID} 代替
     * @return
     * @since 1.2.6
     */
    static public RpcContext_inner createRootRpcContext(String traceId, String rpcId) {
    	if (traceId == null) {
    		traceId = TraceIdGenerator.generate();
    	}
    	if (rpcId == null) {
    		rpcId = MAL_ROOT_RPC_ID;
    	}
    	return new RpcContext_inner(traceId, rpcId);
    }

    /**
     * 从 Map 中还原 EagleEye 调用上下文，但是不放置到 ThreadLocal。
     * 这是为了支持完全无 ThreadLocal 而特别设置的内部方法，在不依赖 ThreadLocal 的场景使用。
     * @param map
     * @return 如果找不到 TraceId、RpcId 等关键上下文，返回 <code>null</code>
     * @since 1.2.6
     * @see RpcContext_inner#toMap()
     */
    static public RpcContext_inner createRpcContextFromMap(Map<String, String> map) {
    	return RpcContext_inner.fromMap(map);
    }

    /**
     * 切换当前线程关联的RPC调用上下文。上下文对象可以是 {@link #getRpcContext()} 的返回值，
     * 也可以是 {@link #currentRpcContext()} 的返回值。
     * @param rpcCtx
     *            RPC调用上下文，可以为null，表示清空当前Threadlocal变量，
     *            该接口不允许业务方调用，只允许 rpc 层调用。
     * @see #getRpcContext() 直接获取 RpcContext 对象，不做 Map 转换
     * @see #currentRpcContext() 获取用于序列化、网络传输的 RpcContext 对象
     */
    @SuppressWarnings("unchecked")
    static public void setRpcContext(Object rpcCtx) {
        try {
            RpcContext_inner ctx = null;
            if (rpcCtx instanceof Map) {
                ctx = RpcContext_inner.fromMap((Map<String,String>) rpcCtx);
            } else if (rpcCtx instanceof RpcContext_inner) {
            	ctx = (RpcContext_inner)rpcCtx;
            }
            RpcContext_inner.set(ctx);
        } catch (Throwable re) {
        	selfLog("[ERROR] setRpcContext", re);
        }
    }

    /**
     * @param context 通过传入context，设置threadlocal变量
     * @see #getRpcContext() 直接获取 RpcContext 对象
     */
    static public void setRpcContext(RpcContext_inner context) {
    	RpcContext_inner.set(context);
    }

    /**
     * 清理全部调用上下文信息
     */
    static public void clearRpcContext() {
    	RpcContext_inner.set(null);
    }

    /**
     * 从栈上弹出一层 RpcContext，用于客户端 Send/Recv 异步时主逻辑
     * 需要把 send 的子 RpcContext 弹出的场景
     * @return 弹出的当前子 RpcContext
     * @since 1.2.0
     * @see #rpcClientRecv(String, int, String) 类似用法，但不记日志
     */
    static public RpcContext_inner popRpcContext() {
		RpcContext_inner ctx = RpcContext_inner.get();
		if (null == ctx) {
			return null;
		}
		RpcContext_inner.set(ctx.parentRpc);
		return ctx;
    }

    /**
     * 获取当前的链路分组。可以在返回的分组中设置分组配置。
     * 链路分组的设置只对当前这一次 RPC 调用有效，不透传。
     * 链路分组信息会附加到当前这一次 RPC 调用的日志后面
     * @param appName 应用名
     * @return 链路分组，允许业务设置分组配置
     * @since 1.2.5
     * @see #attribute(String, String)
     */
    static public TraceGroup traceGroup(String appName) {
        return createContextIfNotExists(true).traceGroup(appName);
    }

    /**
     * 统计日志，一个统计日志的所有 {@link StatEntry 统计项} 会打印在同一个文件里面。
     * 统计日志的输出间隔默认为 60 秒。同样 loggerName 的 logger 只会创建一次
     * @param loggerName 日志名，决定了默认生成的日志文件名：stat-${loggerName}.log，
     * @return
     * @since 1.3.0
     * @see #statLoggerBuilder(String) 需要得到自定义配置的 StatLogger 时，使用此方法
     */
    static public StatLogger statLogger(String loggerName) {
    	return statLoggerBuilder(loggerName).buildSingleton();
    }

    /**
     * 允许自定义配置的统计日志，一个统计日志的所有 {@link StatEntry 统计项} 会打印在同一个文件里面。
     * 统计日志的输出间隔在初始化后不能改变。同样 loggerName 的 logger 只会创建一次。
     * 最后通过调用 {@link StatLoggerBuilder#buildSingleton()} 创建 StatLogger
     * @param loggerName 日志名，决定了默认生成的日志文件名：stat-${loggerName}.log
     * @return
     * @since 1.3.0
     */
    static public StatLoggerBuilder statLoggerBuilder(String loggerName) {
    	return new StatLoggerBuilder(loggerName);
    }

    /**
     * 业务跟踪日志，每行日志会附加 TraceId、RpcId 用于调用链关联跟踪，
     * 同样 loggerName 的 logger 只会创建一次。
     * @param loggerName 日志名，决定了默认生成的日志文件名：trace-${loggerName}.log
     * @return
     * @since 1.3.0
     * @see #traceLoggerBuilder(String) 需要得到自定义配置的 TraceLogger 时，使用此方法
     */
    static public TraceLogger traceLogger(String loggerName) {
    	return traceLoggerBuilder(loggerName).buildSingleton();
    }

    /**
     * 允许自定义配置的业务跟踪日志，每行日志会附加 TraceId、RpcId 用于调用链关联跟踪，
     * 同样 loggerName 的 logger 只会创建一次。
     * 最后通过调用 {@link TraceLoggerBuilder#buildSingleton()} 创建 TraceLogger
     * @param loggerName 日志名，决定了默认生成的日志文件名：trace-${loggerName}.log
     * @return
     * @since 1.3.0
     */
    static public TraceLoggerBuilder traceLoggerBuilder(String loggerName) {
    	return new TraceLoggerBuilder(loggerName);
    }

    /**
	 * 将本次调用的参数、业务对象输出到日志中，默认路径为  ~/logs/eagleeye/trace-atp.log。
	 * <b>需要透传标记 dump=1 存在，才会进行业务对象的输出</b>
	 * @param appId 应用标识
	 * @param operationKey 业务操作字段，或调用的服务名、方法名
	 * @param obj 业务返回值，或业务对象，可以输入 <code>null</code>，或者 {@link Void#TYPE}
	 * @param params 业务参数列表，可以为空
	 * @since 1.3.0
     */
    static public void dump(String appId, String operationKey, Object obj, Object... params) {
		if (isContextDumpEnabled()) {
			dumpImportant(appId, operationKey, obj, params);
		}
    }

    /**
	 * 将本次调用的参数、业务对象输出到日志中，默认路径为  ~/logs/eagleeye/trace-atp.log。
	 * <b>这个方法不管透传标记 dump 是否存在，都会进行业务对象的输出，请务必注意不要频繁使用，
	 * 否则可能产生大量日志</b>
	 * @param appId 应用标识
	 * @param operationKey 业务操作字段，或调用的服务名、方法名
	 * @param obj 业务返回值，或业务对象，可以输入 <code>null</code>，或者 {@link Void#TYPE}
	 * @param params 业务参数列表，可以为空
	 * @since 1.3.0
	 * @see #dump(String, String, Object, Object...) 仅存在 dump 标记时才输出的 API
     */
    static public void dumpImportant(String appId, String operationKey, Object obj, Object... params) {
    	AtpTraceClient.doTrace(appId, operationKey, obj, params);
    }

    /**
     * 检查当前上下文是否需要做日志 DUMP
     * @since 1.3.5.1
     * @see #isLogDumpEnabled() 这个方法只是确认 DUMP 功能是否开启
     */
    static public boolean isContextDumpEnabled() {
    	return AtpTraceClient.isContextDumpEnabled();
    }

    /**
     * 创建一次的RPC调用
     * @param serviceName 服务名称
     * @param methodName 方法名称
     */
	static public void startRpc(String serviceName, String methodName) {
        try {
            // find root context
            RpcContext_inner ctx = RpcContext_inner.get();
            RpcContext_inner childCtx;
            if (null == ctx) {
            	childCtx = new RpcContext_inner(TraceIdGenerator.generate(), MAL_ROOT_RPC_ID);
            } else {
            	// Create child RPC context
            	childCtx = ctx.createChildRpc();
            }
            RpcContext_inner.set(childCtx);
           	childCtx.startRpc(serviceName, methodName);
        } catch (Throwable re) {
        	selfLog("[ERROR] startRpc", re);
        }
    }

	/**
	 * 开始一次本地调用
	 *
     * @param serviceName 服务名称，一般可以设置
     * @param methodName 方法名称
     * @since 1.2.9
	 */
	static public void startLocal(String serviceName, String methodName) {
		startLocal(EagleEyeCoreUtils.EMPTY_STRING, serviceName, methodName);
	}

	/**
	 * 开始一次本地调用
	 *
	 * @param componentType 组件类型，一般用来对多个本地接口进行归组，组件可以以 jar 包、模块方式集成
	 * @param serviceName 接口名称
	 * @param methodName 方法名称
     * @since 1.4.0
	 */
	static public void startLocal(String componentType, String serviceName, String methodName) {
		try {
			RpcContext_inner ctx = RpcContext_inner.get();
			if (null == ctx) {
				return;
			}
			// 考虑多线程startLocal，要clone一份
			RpcContext_inner cloneCtx = ctx.cloneInstance();
			RpcContext_inner.set(cloneCtx);

			cloneCtx.startLocal(componentType, serviceName, methodName);
		} catch (Throwable re) {
			selfLog("[ERROR] startLocal", re);
		}
	}

	/**
	 * 本地调用结束，并设置业务状态码。如果业务状态码不是 {@value #RPC_RESULT_SUCCESS}，
	 * 则认为设置的业务状态码是表示错误的业务状态码
	 *
	 * @param bizResultCode 设置业务状态码
     * @param appendMsg 客户端日志追加的信息， 不能有回车换行等符号，可以为 <code>null</code>
     * @since 1.2.9
     * @see #endLocal(String, String, String)
     * 		如果需要设置非 RPC_RESULT_SUCCESS 的正常调用状态，请使用这个 API
	 */
	static public void endLocal(String bizResultCode, String appendMsg) {
		if (RPC_RESULT_SUCCESS.equals(bizResultCode)) {
			endLocal(RPC_RESULT_SUCCESS, null, appendMsg);
		} else {
			// 兼容 1.4.0 以前的使用方式，用户直接设置错误状态
			endLocal(RPC_RESULT_FAILED, bizResultCode, appendMsg);
		}
	}

	/**
	 * 本地调用结束，设置调用状态、业务状态码，以及追加的信息
	 *
	 * @param resultCode 设置调用状态码，一般用 RPC_RESULT_SUCCESS 表示正常，RPC_RESULT_FAILED 表示错误
	 * @param bizResultCode 设置业务状态码
     * @param appendMsg 客户端日志追加的信息， 不能有回车换行等符号，可以为 <code>null</code>
     * @since 1.4.0
	 */
	static public void endLocal(String resultCode, String bizResultCode, String appendMsg) {
		try {
			RpcContext_inner ctx = RpcContext_inner.get();
			if (null == ctx) {
				return;
			}
			ctx.endLocal(resultCode, bizResultCode, appendMsg);
		} catch (Throwable re) {
			selfLog("[ERROR] endLocal", re);
		}
	}

    /**
     * 用于给RPC附加信息。性能不佳，且 RPC 类型与 HSF 绑定。
     * 已废弃，应直接调用 TAG 对应的方法，如下：<ul>
     * <li>{@link #TAG_CLIENT_SEND}：{@link #rpcClientSend()}</li>
     * <li>{@link #TAG_SERVER_RECV}：{@link #rpcServerRecv(String, String)}</li>
     * <li>{@link #TAG_CLIENT_RECV}：{@link #rpcClientRecv(String, int)}</li>
     * <li>{@link #TAG_SERVER_SEND}：{@link #rpcServerSend(int)}</li>
     * </ul>
     * @deprecated 请直接调用不同的 TAG 对应的方法。(EagleEye 1.1.6)
     */
	@Deprecated
    static public void annotateRpc(String tag) {
        try {
            RpcContext_inner ctx = RpcContext_inner.get();
            if (null == ctx) {
                return;
            }
            if (tag.equals(TAG_CLIENT_SEND)) {
                rpcClientSend();
            } else if (tag.equals(TAG_SERVER_RECV)) {
                rpcServerRecv(null, null);
            } else if (tag.equals(TAG_SERVER_SEND)) {
                rpcServerSend(TYPE_HSF_SERVER);
            } else if (tag.equals(TAG_CLIENT_RECV)) {
                rpcClientRecv(RPC_RESULT_SUCCESS, TYPE_HSF_CLIENT);
            } else if (tag.equals(TAG_CLIENT_SEND_OLD)) {
            	rpcClientSend();
            } else if (tag.equals(TAG_SERVER_RECV_OLD)) {
            	rpcServerRecv(null, null);
            } else if (tag.equals(TAG_SERVER_SEND_OLD)) {
            	rpcServerSend(TYPE_HSF_SERVER);
            } else if (tag.equals(TAG_CLIENT_RECV_OLD)) {
            	rpcClientRecv(RPC_RESULT_SUCCESS, TYPE_HSF_CLIENT);
            } else {
				selfLog("[ERROR] Unknown rpc tag:" + tag);
            }
        } catch (Throwable re) {
        	selfLog("[ERROR] annotateRpc ERROR", re);
        }
    }

    /**
     * 兼容旧API
     * @deprecated 直接使用 {@link #rpcClientSend()}
     */
    @Deprecated
    static public void rpcClientSend(String serverIp, String service, String method) {
        rpcClientSend();
        remoteIp(serverIp);
    }

    /**
     * 记录客户端发出RPC调用的事件
     * @deprecated 直接使用 {@link #rpcClientSend()}
     */
    @Deprecated
    static public void rpcClientSend(String msg) {
    	rpcClientSend();
    }

    /**
     * 记录客户端发出RPC调用的事件
     */
    static public void rpcClientSend() {
        try {
            RpcContext_inner ctx = RpcContext_inner.get();
            if (null == ctx) {
                return;
            }
           	ctx.rpcClientSend();
        } catch (Throwable re) {
        	selfLog("[ERROR] rpcClientSend", re);
        }
    }

    /**
     * 兼容旧API
     * @deprecated 使用 {@link #rpcClientRecv(String, int)}
     */
    @Deprecated
    static public void rpcClientRecv() {
        rpcClientRecv(RPC_RESULT_SUCCESS);
    }

    /**
     * 记录 HSF 客户端收到RPC响应的事件
     * @param resultCode
     *            参考 RPC_RESULT_开头的结果码。有响应表示RPC成功，但业务上有可能还是失败，因此有不同的结果码。
     * @deprecated 应该使用明确指定 rpcType 的方法 {@link #rpcClientRecv(String, int)}
     */
    static public void rpcClientRecv(String resultCode) {
        rpcClientRecv(resultCode, TYPE_HSF_CLIENT);
    }

    /**
     * 记录客户端收到RPC响应的事件
     * @param resultCode
     *            参考 RPC_RESULT_开头的结果码。有响应表示RPC成功，但业务上有可能还是失败，因此有不同的结果码。
     * @param type 类型为：TYPE_TRACE,TYPE_HSF_CLIENT,TYPE_TDDL,TYPE_NOTIFY,TYPE_TAIR,TYPE_SEARCH
     */
    static public void rpcClientRecv(String resultCode, int type) {
    	rpcClientRecv(resultCode, type, null);
    }

    /**
     * 记录客户端收到RPC响应的事件
     * @param resultCode
     *            参考 RPC_RESULT_开头的结果码。有响应表示RPC成功，但业务上有可能还是失败，因此有不同的结果码。
     * @param type 类型为：   TYPE_TRACE， TYPE_HSF_CLIENT,TYPE_TDDL,TYPE_NOTIFY,TYPE_TAIR,TYPE_SEARCH
     * @param appendMsg 客户端日志追加的信息， 不能有回车、换行、“|” 等符号
     */
    static public void rpcClientRecv(String resultCode, int type, String appendMsg) {
		try {
			RpcContext_inner ctx = RpcContext_inner.get();
			if (null == ctx) {
				return;
			}
			ctx.endRpc0(resultCode, type, appendMsg);
			commitRpcContext(ctx);
			// 弹出当前 ctx
			RpcContext_inner.set(ctx.parentRpc);
        } catch (IllegalContextException ice) {
        	// quietly
		} catch (Throwable re) {
			selfLog("[ERROR] rpcClientRecv", re);
		}
    }

    /**
     * 记录 HSF 失败，比如响应超时，找不到服务地址，序列化失败等
     * 只使用rpcclient调用出错是调用，服务端调用出错不使用该接口记录
     *
     * @param resultCode
     *            参考 RPC_RESULT_开头的结果码
     * @deprecated 直接使用 {@link #rpcClientRecv(String, int)}，明确指定 Rpc 类型
     */
    @Deprecated
    static public void rpcFail(String resultCode) {
    	rpcClientRecv(resultCode, TYPE_HSF_CLIENT);
    }

    /**
     * rpc失败，resultcode由业务定义。
     * @param resultCode
     * @param type notify tddl tair lucene
     * @deprecated 直接使用 {@link #rpcClientRecv(String, int)}
     */
    @Deprecated
    static public void rpcFail(String resultCode, int type) {
    	rpcClientRecv(resultCode, type);
    }

    /**
     * 服务端收到RPC请求
     * @param clientIp
     * @param service
     * @param method
     */
    @Deprecated
    static public void rpcServerRecv(String clientIp, String service, String method) {
        rpcServerRecv(service, method);
        remoteIp(clientIp);
    }

    /**
     * 服务端收到RPC请求
     */
    static public void rpcServerRecv(String service, String method) {
        try {
            createContextIfNotExists(true).rpcServerRecv(service, method);
        } catch (Throwable re) {
        	selfLog("[ERROR] rpcServerRecv", re);
        }
    }

    /**
     * HSF服务端返回RPC响应，只允许HSF使用，Threadlocal变量会被清空
     * @deprecated 应该使用明确指定 rpcType 的方法 {@link #rpcServerSend(int)}
     */
    static public void rpcServerSend() {
    	rpcServerSend(TYPE_HSF_SERVER);
    }

    /**
     * 服务端返回RPC响应，指定 rpc 类型，Threadlocal变量会被清空
     * @param type 类型为：   TYPE_HSF_SERVER,TYPE_NOTIFY
     * @since 1.1.6
     */
    static public void rpcServerSend(int type) {
        rpcServerSend(null, type);
    }

    /**
     * 服务端返回RPC响应，指定 rpc 类型，Threadlocal变量会被清空
     * @param resultCode
     *            参考 RPC_RESULT_开头的结果码。有响应表示RPC成功，但业务上有可能还是失败，因此有不同的结果码。
     * @param type 类型为：   TYPE_HSF_SERVER,TYPE_NOTIFY
     * @since 1.2.8
     */
    static public void rpcServerSend(String resultCode, int type) {
		rpcServerSend(resultCode, type, null);
    }

	/**
	 * 服务端返回RPC响应，指定 rpc 类型，Threadlocal变量会被清空
	 *
	 * @param resultCode
	 *            参考 RPC_RESULT_开头的结果码。有响应表示RPC成功，但业务上有可能还是失败，因此有不同的结果码。
	 * @param type
	 *            类型为： TYPE_TRACE，
	 *            TYPE_HSF_CLIENT,TYPE_TDDL,TYPE_NOTIFY,TYPE_TAIR,TYPE_SEARCH
	 * @param appendMsg
	 *            客户端日志追加的信息， 不能有回车、换行、“|” 等符号
	 */
	static public void rpcServerSend(String resultCode, int type, String appendMsg) {
		try {
			RpcContext_inner ctx = RpcContext_inner.get();
			if (ctx != null) {
				ctx.rpcServerSend0(type, resultCode, appendMsg);
				commitRpcContext(ctx);
			}
		} catch (IllegalContextException ice) {
			// quietly
		} catch (Throwable re) {
			selfLog("[ERROR] rpcServerRecv", re);
		} finally {
			clearRpcContext();
		}
	}

    /**
     * 获取全局唯一的Traceid
     * @return
     */
    static public String getTraceId() {
    	RpcContext_inner ctx = RpcContext_inner.get();
        return null == ctx ? null : ctx.traceId;
    }

    /**
     * 获取当前rpc调用层次
     * @return
     */
    static public String getRpcId() {
    	RpcContext_inner ctx = RpcContext_inner.get();
        return null == ctx ? null : ctx.rpcId;
    }

	/**
	 * 获取当前localId
	 *
	 * @return 如果没有本地方法则返回""
	 */
    static public String getLocalId() {
    	RpcContext_inner ctx = RpcContext_inner.get();
        return null == ctx ? AbstractContext.EMPTY_LOCALID : ctx.getLocalId();
    }

	/**
	 * rpc 上追加的key value信息，会打印到当前 rpc 日志中。
     * 与添加业务信息的 {@link #putUserData(String, String)} 不同，
     * attribute 不会跟随 rpc 调用传递，只对本地当前的这一次 RPC 有效
	 * @param key
	 * @param value
	 * @see #putUserData(String, String)
	 */
	static public void attribute(String key, String value) {
        createContextIfNotExists(true).putLocalAttribute(key, value);
    }

    /**
     * 获取随 EagleEye 通过 HSF、Notify 等中间件传递的业务信息
     * @param key    不能为空
     * @return
     * @since 1.2.0
     */
    static public String getUserData(String key) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        return null != ctx ? ctx.getUserData(key) : null;
    }

    /**
     * 放置 key 对应的业务信息，这个信息会打印到当前 rpc 的日志之中。
     * 信息会随 EagleEye 通过 HSF、Notify 等中间件传递。
     * 数据在调用链里面的从父往子间传递。
     * 如果仅仅希望添加业务信息，不需要信息被传递，可以使用 {@link #attribute(String, String)}
     * @param key    不能为空
     * @param value  值，不能有回车、换行、“|” 等符号
     * @return       原来的值
     * @since 1.2.0
     * @see #attribute(String, String)
     */
    static public String putUserData(String key, String value) {
        return createContextIfNotExists(true).putUserData(key, value);
    }

    /**
     * 清除 key 对应的业务信息
     * @param key    不能为空
     * @return       原来的值
     * @since 1.2.0
     */
    static public String removeUserData(String key) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        return null != ctx ? ctx.removeUserData(key) : null;
    }

    /**
     * 获取随 EagleEye 通过 HSF、Notify 等中间件传递的业务信息。
     * 供内部使用，业务应该使用 {@link #getUserData(String)}
     * @since 1.2.0
     */
    static public Map<String, String> getUserDataMap() {
    	RpcContext_inner ctx = RpcContext_inner.get();
    	return null != ctx ? ctx.getUserDataMap() : null;
    }

    /**
     * 导出业务信息，供中间件传输 EagleEye 上下文时使用。
     * @return
     * @since 1.2.0
     */
    static public String exportUserData() {
    	RpcContext_inner ctx = RpcContext_inner.get();
        return null != ctx ? ctx.exportUserData() : null;
    }

    /**
     * 用于业务方希望追加相关数据到rpc调用链中，比如想把业务的方法中的某个参数值打印出来，放到rpc的日志中。
     * @param msg 用户希望追加的内容，不能有回车、换行、“|” 等符号
     * @since 1.1.4 用于服务端
     * @since 1.2.0 可用于服务端/客户端
     */
    static public void callBack(String msg) {
		if (msg != null && msg.length() < MAX_BIZ_LOG_SIZE) {
			RpcContext_inner ctx = RpcContext_inner.get();
		    if (null == ctx) {
		        return;
		    }
		    ctx.callBackMsg = msg;
		}
    }

    /**
     * RPC 请求大小
     * @param size
     */
    static public void requestSize(long size) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        if (null == ctx) {
            return;
        }
        ctx.requestSize = size;
    }

    /**
     * 追加远程服务地址
     * @param remoteIp 远程机器ip地址
     */
    static public void remoteIp(String remoteIp) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        if (null == ctx) {
            return;
        }
        ctx.remoteIp = remoteIp;
    }

    /**
     * RPC 响应的大小
     * @param size
     */
    static public void responseSize(long size) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        if (null == ctx) {
            return;
        }
        ctx.responseSize = size;
    }

    /**
     * 支持业务重新设置 traceName，同时重新指定 iToken
     * @param traceName 新的 URL，如果为 <code>null</code>，则不设置 traceName
     * @param iToken 新的 iToken 标记，如果为 <code>null</code>，则按当前 traceName 自动生成
     * @since 1.3.3
     * @see #aliasName(String) 设置别名
     */
    static public void traceName(String traceName, String iToken) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        if (null == ctx) {
            return;
        }
        ctx.resetTraceName(traceName, iToken);
    }

    /**
     * 支持业务重新设置服务别名，而不覆盖原有的
     * @param aliasName 设置别名，如果为 <code>null</code>，则设置无效
     * @since 1.4.0
     * @see #traceName(String, String) 重新设置 traceName
     */
    static public void aliasName(String aliasName) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        if (null == ctx) {
            return;
        }
        ctx.setAliasName(aliasName);
    }

    /**
     * 设置调用的状态码，注意：如果显式调用了这个 API，传入了非空值，那么
     * {@link #endTrace(String, int)}、{@link #rpcClientRecv(String, int)}、
     * {@link #rpcServerSend(String, int)} 设置的 resultCode 都会被忽略
     * @param resultCode
     * @since 1.3.5
     * @see #bizResultCode(String) 仅设置业务的状态码
     * @see #bizErrorCode(String) 设置业务的状态码，且设置调用的状态码为错误
     */
    static public void resultCode(String resultCode) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        if (null == ctx) {
            return;
        }
        ctx.setResultCode(resultCode);
    }

    /**
	 * 设置业务的状态码（不要有可变参数在里面，因为要进行统计），
	 * 同时，将这次调用的状态码设置为业务错误
     * @param bizErrorCode
     * @since 1.4.0
     * @see #bizResultCode(String) 仅设置业务的状态码
     * @see #resultCode(String) 仅设置调用的状态码
     */
    static public void bizErrorCode(String bizErrorCode) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        if (null == ctx) {
            return;
        }
        ctx.setBizResultCode(bizErrorCode);
        ctx.setResultCode(RPC_RESULT_FAILED);
    }

    /**
	 * 设置业务的状态码（不要有可变参数在里面，因为要进行统计），
	 * 不会影响调用的状态码（默认的调用状态码是“正常”）
	 * @param bizResultCode
	 * @since 1.4.0
     * @see #resultCode(String) 仅设置调用的状态码
     * @see #bizErrorCode(String) 设置业务的状态码，且设置调用的状态码为错误
     */
    static public void bizResultCode(String bizResultCode) {
    	RpcContext_inner ctx = RpcContext_inner.get();
        if (null == ctx) {
            return;
        }
        ctx.setBizResultCode(bizResultCode);
    }

    /**
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
    static public void rpcLog(int type, String msg) {
		rpcLog(type, msg, true);
    }

    /**
     * @deprecated 已废弃，不要使用
     */
    @Deprecated
	static public void rpcLog(int type, String msg, boolean appendRpcId) {
		try {
			RpcContext_inner ctx = RpcContext_inner.get();
			if (null == ctx) {
				return;
			}
			if (msg != null && msg.length() < MAX_BIZ_LOG_SIZE) {
				RpcContext_inner sub = new RpcContext_inner(ctx.traceId, appendRpcId ? ctx.rpcId : null);
				sub.rpcLog(type, msg, appendRpcId);
				commitRpcContext(sub);
			}
		} catch (Throwable e) {
			selfLog("[ERROR] rpcLog", e);
		}
	}

    /**
     * 生成全局唯一的traceid
     * @param ip 用户出入的ip地址，如果非法或者为空，则使用当前机器的ip地址
     * @return
     */
    static public String generateTraceId(String ip) {
    	return TraceIdGenerator.generate(ip);
    }

    /**
     * 当 RpcId 在多播情况下（例如一对多的 RPC 调用），调用这个方法产生新的 RPC Id
     * @param rpcId 当前的 RPC Id
     * @param identifier 保留用(如果有比 ip 更适合的标识符，可以允许外部指定
     * @return
     * @since 1.1.6
     */
    static public String generateMulticastRpcId(String rpcId, String identifier) {
    	if (rpcId == null || rpcId.length() == 0) {
    		rpcId = "0";
    	}
    	return rpcId + "." + TraceIdGenerator.generateIpv4Id();
    }

    /**
     * 索引服务，该日志会记录在eagleeye日志中，目的是对大量重复的消息做编码。
     * @param msg 需要编码的消息
     * @return
     */
    static public String index(String msg) {
    	try {
			if (msg != null && msg.length() > 8) {
				String index = indexes.get(msg);
				if (index != null) {
					return index;
				}
				if (indexes.size() >= MAX_INDEX_TABLE_SIZE) {
					return INDEX_NOT_INDEXED;
				}
				index = EagleEyeCoreUtils.digest(msg);
				String rs = indexes.putIfAbsent(msg, index);
				if (rs == null) { //多线程并发，第一次put进去
					index(TYPE_INDEX, index, msg);
					selfLog("[INFO] generate index: " + index + " => " + msg);
				}
				return index;
			}
			return msg;
		} catch (Throwable e) {
			selfLog("[ERROR] index: " + msg, e);
			return msg;
		}
    }

    /**
     * 在日志中记录指定索引，将原文中的换行等非法符号都过滤掉
     * @param type
     * @param index
     * @param msg
     */
    static void index(int type, String index, String msg) {
		RpcContext_inner sub = new RpcContext_inner(INDEX_OUTPUT_TRACE_ID, MAL_ROOT_RPC_ID);
		sub.index(type, index, EagleEyeCoreUtils.filterInvalidCharacters(msg));
		commitRpcContext(sub);
	}

    /**
     * 输出索引表
     * @return
     * @since 1.2.0
     */
    static String exportIndexes() {
    	StringBuilder builder = new StringBuilder(128 * indexes.size());
		for (Entry<String, String> entry : indexes.entrySet()) {
			builder.append(entry.getValue()).append(" -> ").append(entry.getKey()).append('\n');
		}
		return builder.toString();
    }

    /**
     * 提交调用上下文，生成日志。这是一个为了提高中间件埋点性能而特别设置的内部方法，
     * 在不依赖 ThreadLocal 的场景使用。
     * @param ctx
     * @since 1.2.2
     */
    static public void commitRpcContext(RpcContext_inner ctx) {
		if (ctx.logType >= 0 && !isRpcOff() && ctx.isTraceSampled()) {
			rpcAppender.append(ctx);
		}
    }

    /**
     * 提交调用本地上下文
     * @param ctx
     * @since 1.2.9
     */
    static void commitLocalContext(LocalContext_inner ctx) {
		if (ctx.logType >= 0 && !isRpcOff() && ctx.isTraceSampled()) {
			rpcAppender.append(ctx);
		}
    }

    /**
     * 在trace中记录业务关键信息，日志会放到biz_eagleeye.log文件中，这是一个为了
     * 提高中间件埋点性能而特别设置的内部方法，在不依赖 ThreadLocal 的场景使用。
     *
     * @deprecated 对于新业务埋点，不建议继续使用这个 API，请使用 {@link #traceLogger(String)}，
     * 这样可以使不同业务的日志输出到单独的文件
     *
     * @param ctx
     *            当前调用上下文
     * @param domain
     *            使用eagleEye的业务方ID
     * @param eventType
     *            业务方后续查询记录使用的关键字，为了标示当前这条记录的key，
     *            日志存储的使用会自动追加domain+time+eventType
     * @param logContent
     *            业务信息， 不能有回车、换行、“|” 等符号
     * @since 1.2.7
     */
    @Deprecated
    static public void commitBusinessTag(RpcContext_inner ctx, String domain,
    		String eventType, String logContent) {
   		bizEagleEyeLogger.logLineWithContext(ctx, domain, eventType, logContent);
    }

    /**
     * 注册监听上下文变化的回调
     * @param listener
     * @since 1.3.4
     */
    static public void addRpcContextListener(EagleEyeContextListener listener) {
    	listeners.addIfAbsent(listener);
    }

    /**
     * 反注册上下文变化的回调
     * @param listener
     * @since 1.3.5.2
     */
    static public boolean removeRpcContextListener(EagleEyeContextListener listener) {
    	boolean removed = listeners.remove(listener);
    	if (removed) {
    		listener.afterListenerRemoval();
    	}
		return removed;
    }

    /**
     * @param appender
     * @since 1.1.5
     */
    static public void setEagelEyeRpcAppender(EagleEyeAppender appender) {
    	rpcAppender.setEagleEyeAppender(appender);
    }

    /**
     * @param appender
     * @since 1.1.5
     */
    static public void setEagelEyeBizAppender(EagleEyeAppender appender) {
    	bizAppender.setEagleEyeAppender(appender);
    }

    /**
     * @param appender
     * @since 1.2.0
     */
    static void setEagelEyeSelfAppender(EagleEyeAppender appender) {
    	selfAppender = appender;
    }

    /**
     * 记录 EagleEye 自身信息时使用，EagleEye 内部调试用
     * @since 1.2.0
     */
    static public void selfLog(String log) {
   		try {
			String timestamp = EagleEyeCoreUtils.formatTime(System.currentTimeMillis());
   			String line = "[" + timestamp + "] " + log + EagleEyeCoreUtils.NEWLINE;
			selfAppender.append(line);
		} catch (Throwable t) {
		}
    }

    /**
     * 记录 EagleEye 自身错误异常时使用，EagleEye 内部调试用
     * @since 1.2.0
     */
    static public void selfLog(String log, Throwable e) {
    	long now = System.currentTimeMillis();
    	if (exceptionBucket.accept(now)) {
			try {
				String timestamp = EagleEyeCoreUtils.formatTime(now);
				StringWriter sw = new StringWriter(4096);
				PrintWriter pw = new PrintWriter(sw, false);
				pw.append('[').append(timestamp)
				  .append("] ").append(log).append(EagleEyeCoreUtils.NEWLINE);
				e.printStackTrace(pw);
				pw.println();
				pw.flush();
				selfAppender.append(sw.toString());
			} catch (Throwable t) {
			}
    	}
    }

    /**
     * 强制刷新缓存中的日志内容到文件中去，一般建议外部在程序结束阶段时调用，
     * 使用中频繁调用会导致性能下降
     * @since 1.2.2
     */
    static public void flush() {
    	EagleEyeLogDaemon.flushAndWait();
    }
































    /* ------------------------------------------------------------------------
     * EagleEye 中的 ULC 业务部分，由于历史原因，他们出现在这里。
     * --------------------------------------------------------------------- */
    /**
	 * 记录业务日志
	 * @param logKey  接入ULC分配的日志key,必须传入.
	 * @param userId  当前操作对应的用户ID.主账号.必须传入.
	 * @param bizId   业务编号,如退款单号,物流订单号.
	 * @param operateType 操作类型,由客户端定义.如1对应新增,2对应更新.
	 * @param operateContent 操作内容
	 * @deprecated 请使用 ulc-client 里面的 UlcLog 的相关实现
	 */
    @Deprecated
	public static void log(String logKey,Long userId,String bizId,int operateType,String operateContent) {
		if (EagleEyeCoreUtils.isBlank(logKey)) return;
		log(logKey, userId, bizId, null, operateType, operateContent, new LinkedHashMap<String, String>());
	}

	/**
	 * 记录业务日志
	 * @param logKey  接入ULC分配的日志key,必须传入.
	 * @param userId  当前操作对应的用户ID.主账号.必须传入.
	 * @param bizId   业务编号,如退款单号,物流订单号.
	 * @param operatorId  操作人ID.如果是主账号操作,则对应主账号ID,如果是子账号操作,则对应子账号ID.
	 * @param operateType 操作类型,由客户端定义.如1对应新增,2对应更新.
	 * @param operateContent 操作内容
	 * @deprecated 请使用 ulc-client 里面的 UlcLog 的相关实现
	 */
    @Deprecated
	public static void log(String logKey,Long userId,String bizId,Long operatorId,int operateType,String operateContent) {
		if (EagleEyeCoreUtils.isBlank(logKey)) return;
		log(logKey, userId, bizId, operatorId, operateType, operateContent, new LinkedHashMap<String, String>());
	}

	/**
	 * 记录业务日志
	 * @param logKey  接入ULC分配的日志key,必须传入.
	 * @param userId  当前操作对应的用户ID.主账号.必须传入.
	 * @param bizId   业务编号,如退款单号,物流订单号.
	 * @param operateType 操作类型,由客户端定义.如1对应新增,2对应更新.
	 * @param operateContent 操作内容
	 * @param extendInfos 扩展信息.用户自定义的信息存储.
	 * @deprecated 应该使用 {@link #log(String, Long, String, int, String, Map)} 代替本方法
	 */
    @Deprecated
	public static void log(String logKey,Long userId,String bizId,int operateType,String operateContent,String ... extendInfos) {
		if (EagleEyeCoreUtils.isBlank(logKey)) return;
		if (null == extendInfos || extendInfos.length == 0) {
			extendInfos = EagleEyeCoreUtils.EMPTY_STRING_ARRAY;
		}
		log(logKey, userId, bizId, null, operateType, operateContent, extendInfos);
	}

	/**
	 * 记录业务日志
	 * @param logKey  接入ULC分配的日志key,必须传入.
	 * @param userId  当前操作对应的用户ID.主账号.必须传入.
	 * @param bizId   业务编号,如退款单号,物流订单号.
	 * @param operateType 操作类型,由客户端定义.如1对应新增,2对应更新.
	 * @param operateContent 操作内容
	 * @param extendInfos 扩展信息.用户自定义的信息存储.
	 * @deprecated 请使用 ulc-client 里面的 UlcLog 的相关实现
	 */
    @Deprecated
	public static void log(String logKey,Long userId,String bizId,int operateType,String operateContent,Map<String, String> extendInfos) {
		if (EagleEyeCoreUtils.isBlank(logKey)) return;
		if (null == extendInfos || extendInfos.size() == 0) {
			extendInfos = new LinkedHashMap<String, String>();
		}
		log(logKey, userId, bizId, null, operateType, operateContent, extendInfos);
	}

	/**
	 * 记录业务日志,可以附带一些扩展信息进去.比如备注等等.
	 * @param logKey  接入ULC分配的日志key,必须传入.
	 * @param userId  当前操作对应的用户ID.主账号.必须传入.
	 * @param bizId   业务编号,如退款单号,物流订单号.
	 * @param operatorId  操作人ID.如果是主账号操作,则对应主账号ID,如果是子账号操作,则对应子账号ID.
	 * @param operateType 操作类型,由客户端定义.如1对应新增,2对应更新.
	 * @param operateContent 操作内容
	 * @param extendInfos 扩展信息.用户自定义的信息存储.
	 * @deprecated 应该使用 {@link #log(String, Long, String, Long, int, String, Map)} 代替本方法
	 */
    @Deprecated
	public static void log(String logKey,Long userId,String bizId,Long operatorId,int operateType,String operateContent,String ... extendInfos) {
		if (EagleEyeCoreUtils.isBlank(logKey)) return;
		String msg = getMsg(logKey, userId, bizId, operatorId, operateType, operateContent, extendInfos);
		EagleEye.businessTag(ULC_EAGLEEYE_APPID, logKey, msg.toString());
	}

	/**
	 * 记录业务日志,可以附带一些扩展信息进去.比如备注等等.
	 * @param logKey  接入ULC分配的日志key,必须传入.
	 * @param userId  当前操作对应的用户ID.主账号.必须传入.
	 * @param bizId   业务编号,如退款单号,物流订单号.
	 * @param operatorId  操作人ID.如果是主账号操作,则对应主账号ID,如果是子账号操作,则对应子账号ID.
	 * @param operateType 操作类型,由客户端定义.如1对应新增,2对应更新.
	 * @param operateContent 操作内容
	 * @param extendInfos 扩展信息.用户自定义的信息存储.
	 * @deprecated 请使用 ulc-client 里面的 UlcLog 的相关实现
	 */
    @Deprecated
	public static void log(String logKey,Long userId,String bizId,Long operatorId,int operateType,String operateContent,Map<String, String> extendInfos) {
		if (EagleEyeCoreUtils.isBlank(logKey)) return;
		String msg = getMsg(logKey, userId, bizId, operatorId, operateType, operateContent, extendInfos);
		EagleEye.businessTag(ULC_EAGLEEYE_APPID, logKey, msg.toString());
	}

	/**
	 * 记录 ULC 业务日志
	 * @param logContext 封装参数对象
	 * @deprecated 请使用 ulc-client 里面的 UlcLog 的相关实现
	 */
    @Deprecated
	public static void log(LogContext logContext) {
		log(ULC_EAGLEEYE_APPID, logContext);
	}

	/**
	 * 记录业务日志
	 * @param appId 使用EagleEye的业务方ID
	 * @param logContext 封装参数对象
	 * @deprecated 请使用 ulc-client 里面的 UlcLog 的相关实现
	 */
    @Deprecated
	public static void log(String appId, LogContext logContext) {
		if (EagleEyeCoreUtils.isBlank(logContext.getLogKey())) return;
		String msg = getMsg(logContext);
		EagleEye.businessTag(appId, logContext.getLogKey(), msg.toString());
	}

    @Deprecated
	private static String getMsg(String logKey,Long userId,String bizId,Long operatorId,int operateType,String operateContent,String ... extendInfos) {
		LogContext logContext = new LogContext();
		logContext.logKey(logKey).userId(userId).bizId(bizId).operatorId(operatorId)
				.operateType(operateType).operateContent(operateContent).extendArray(extendInfos);
		return getMsg(logContext);
	}

    @Deprecated
	private static String getMsg(String logKey,Long userId,String bizId,Long operatorId,int operateType,String operateContent,Map<String, String> extendInfos) {
		LogContext logContext = new LogContext();
		logContext.logKey(logKey).userId(userId).bizId(bizId).operatorId(operatorId)
				.operateType(operateType).operateContent(operateContent).extendInfos(extendInfos);
		return getMsg(logContext);
	}

    @Deprecated
	private static String getMsg(LogContext logContext) {
		StringBuilder sBuilder = new StringBuilder();
		sBuilder.append(logContext.getLogKey()).append(ULC_SEPARATOR);
		if (null != logContext.getUserId()) {
			sBuilder.append(logContext.getUserId());
		} else {
			sBuilder.append(0L);
		}
		sBuilder.append(ULC_SEPARATOR);
		if (EagleEyeCoreUtils.isNotBlank(logContext.getBizId())) {
			sBuilder.append(logContext.getBizId());
		} else {
			sBuilder.append(EagleEyeCoreUtils.EMPTY_STRING);
		}
		sBuilder.append(ULC_SEPARATOR);
		if (null != logContext.getOperatorId()) {
			sBuilder.append(logContext.getOperatorId());
		} else {
			sBuilder.append(0L);
		}
		sBuilder.append(ULC_SEPARATOR);
		sBuilder.append(logContext.getOperateType()).append(ULC_SEPARATOR);
		if (EagleEyeCoreUtils.isNotBlank(logContext.getOperateContent())) {
			sBuilder.append(logContext.getOperateContent());
		} else {
			sBuilder.append(EagleEyeCoreUtils.EMPTY_STRING);
		}
		sBuilder.append(ULC_SEPARATOR);
		if (EagleEyeCoreUtils.isNotBlank(logContext.getUserNick())) {
			sBuilder.append(logContext.getUserNick());
		} else {
			sBuilder.append(EagleEyeCoreUtils.EMPTY_STRING);
		}
		sBuilder.append(ULC_SEPARATOR);
		if (EagleEyeCoreUtils.isNotBlank(logContext.getOpLevel())) {
			sBuilder.append(logContext.getOpLevel());
		} else {
			sBuilder.append(EagleEyeCoreUtils.EMPTY_STRING);
		}
		sBuilder.append(ULC_SEPARATOR);
		if (EagleEyeCoreUtils.isNotBlank(logContext.getOpItem())) {
			sBuilder.append(logContext.getOpItem());
		} else {
			sBuilder.append(EagleEyeCoreUtils.EMPTY_STRING);
		}
		if (null != logContext.getExtendArray() && logContext.getExtendArray().length > 0) {
			for (String info : logContext.getExtendArray()) {
				if (EagleEyeCoreUtils.isBlank(info)) {
					info = EagleEyeCoreUtils.EMPTY_STRING;
				}
				sBuilder.append(ULC_SEPARATOR).append(info);
			}
		}
		if (null != logContext.getExtendInfos() && logContext.getExtendInfos().size() > 0) {
			for (Entry<String, String> entry : logContext.getExtendInfos().entrySet()) {
				String key = entry.getKey();
				if (EagleEyeCoreUtils.isNotBlank(key)) {
					Object value = entry.getValue();
					sBuilder.append(ULC_SEPARATOR).append(key).append(KV_SEPARATOR);
					if (value != null) {
						appendWithBlankCheck(value.toString(), EMPTY_STRING, sBuilder);
					}
				}
			}
		}
		return sBuilder.toString();
	}
}
