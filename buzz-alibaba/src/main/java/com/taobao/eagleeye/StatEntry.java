package com.taobao.eagleeye;

import java.util.Arrays;
import java.util.List;

/**
 * 统计项 ，主要通过 Fluent API 完成功能连接。
 * 注：这个类实际是一个不变量。为了提高在 HashMap 中定位 keys，性能缓存了哈希值。
 * @since 1.3.0
 * <AUTHOR>
 */
public final class StatEntry {

	/*
	 * JVM 通过逃逸分析可以优化，避免每次都创建 StatEntry
	 */

	private final StatLogger statLogger;

	/*
	 * Key 部分
	 */

	private final String[] keys;
	private transient int hash;

	public StatEntry(StatLogger statLogger, String key) {
		this.statLogger = statLogger;
		this.keys = new String[] { key };
	}

	public StatEntry(StatLogger statLogger, String key1, String key2) {
		this.statLogger = statLogger;
		this.keys = new String[] { key1, key2 };
	}

	public StatEntry(StatLogger statLogger, String key1, String key2, String key3) {
		this.statLogger = statLogger;
		this.keys = new String[] { key1, key2, key3 };
	}

	public StatEntry(StatLogger statLogger, String key1, String key2, String key3, String key4) {
		this.statLogger = statLogger;
		this.keys = new String[] { key1, key2, key3, key4 };
	}

	public StatEntry(StatLogger statLogger, String key1, String key2, String key3, String key4, String key5) {
		this.statLogger = statLogger;
		this.keys = new String[] { key1, key2, key3, key4, key5 };
	}

	public StatEntry(StatLogger statLogger, String key1, String key2, String key3, String key4, String key5, String key6) {
		this.statLogger = statLogger;
		this.keys = new String[] { key1, key2, key3, key4, key5, key6 };
	}

	public StatEntry(StatLogger statLogger, String key1, String key2, String key3, String key4, String key5, String key6, String key7) {
		this.statLogger = statLogger;
		this.keys = new String[] { key1, key2, key3, key4, key5, key6, key7 };
	}

	public StatEntry(StatLogger statLogger, String key1, String key2, String key3, String key4, String key5, String key6, String key7, String key8) {
		this.statLogger = statLogger;
		this.keys = new String[] { key1, key2, key3, key4, key5, key6, key7, key8 };
	}

	public StatEntry(StatLogger statLogger, String key1, String... moreKeys) {
		String[] keys = new String[1 + moreKeys.length];
		keys[0] = key1;
		for (int i = 0; i < moreKeys.length; ++i) {
			keys[i + 1] = moreKeys[i];
		}
		this.statLogger = statLogger;
		this.keys = keys;
	}

	public StatEntry(StatLogger statLogger, List<String> keys) {
		if (keys == null || keys.isEmpty()) {
			throw new IllegalArgumentException("keys empty or null: " + keys);
		}
		this.statLogger = statLogger;
		this.keys = keys.toArray(new String[keys.size()]);
	}

	public StatEntry(StatLogger statLogger, String[] keys) {
		if (keys == null || keys.length == 0) {
			throw new IllegalArgumentException("keys empty or null");
		}
		this.statLogger = statLogger;
		this.keys = Arrays.copyOf(keys, keys.length);
	}

	/**
	 * 获取设置的 keys
	 * @return
	 */
	public String[] getKeys() {
		return keys;
	}

	void appendTo(StringBuilder appender, char delimiter) {
		final int len = keys.length;
		if (len > 0) {
			appender.append(keys[0]);
			for (int i = 1; i < len; ++i) {
				appender.append(delimiter).append(keys[i]);
			}
		}
	}

	@Override
	public String toString() {
		StringBuilder sb = new StringBuilder(64);
		sb.append("StatKeys [");
		appendTo(sb, ',');
		sb.append("]");
		return sb.toString();
	}

	@Override
	public int hashCode() {
		if (hash == 0) {
			int result = 1;
			result = 31 * result + Arrays.hashCode(keys);
			hash = result;
		}
		return hash;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		StatEntry other = (StatEntry) obj;
		// 如果有 hash，优先比较
		if (hash != 0 && other.hash != 0 && hash != other.hash)
			return false;
		if (!Arrays.equals(keys, other.keys))
			return false;
		return true;
	}

	/*
	 * Value 部分
	 */
	StatEntryFunc getFunc(final StatEntryFuncFactory factory) {
		return this.statLogger.getRollingData().getStatEntryFunc(this, factory);
	}

	/**
	 * 计数加 1
	 */
	public void count() {
		count(1);
	}

	/**
	 * 计数加 count
	 */
	public void count(long count) {
		getFunc(StatEntryFuncFactory.COUNT_SUM).count(count);
	}

	/**
	 * 计数加 1，总数加 valueToSum
	 * @param valueToSum
	 */
	public void countAndSum(long valueToSum) {
		countAndSum(1, valueToSum);
	}

	/**
	 * 计数加 count，总数加 valueToSum
	 * @param count
	 * @param valueToSum
	 */
	public void countAndSum(long count, long valueToSum) {
		getFunc(StatEntryFuncFactory.COUNT_SUM).countAndSum(count, valueToSum);
	}

	/**
	 * 对 value 计算标准差（产出的数据还可以同时计算出 count值、总和、平均值、方差）
	 * @param value
	 */
	public void std(long value) {
		getFunc(StatEntryFuncFactory.STD).countAndSum(1, value);
	}

	/**
	 * 累计数组对应位置的值。例如 values[0] 的值会和之前 values[0] 的值累加，
	 * values[1] 的值会和之前 values[1] 的值累加...，如果 values 和之前累加的长度不相等，
	 * 会以长的那个数组为准，短的数组不足的位置为 0
	 * @param values
	 * @see #batchAdd 如果仅累加值，不会调用 {@link #arraySet(long...)}，
	 * 		建议用 {@link #batchAdd(long...)} 替代，性能更好
	 */
	public void arrayAdd(long... values) {
		getFunc(StatEntryFuncFactory.ARRAY).arrayAdd(values);
	}

	/**
	 * 设置数组对应位置的值。例如 values[0] 的值会覆盖之前 values[0] 的值，
	 * values[1] 的值会覆盖之前 values[1] 的值...，如果 values 比之前的长度长，
	 * 会自动扩充原来的数组；如果 values 比之前的长度短，原来多出的位置上的值不会被改变
	 * @param values
	 */
	public void arraySet(long... values) {
		getFunc(StatEntryFuncFactory.ARRAY).arraySet(values);
	}

	/**
	 * 对数值求最大、最小值。例如输入的序列为：123, -234, 108, 121，
	 * 产出的 minMax 结果是：min=-234, max=123
	 * @param candidate
	 */
	public void minMax(long candidate) {
		minMax(candidate, null);
	}

	/**
	 * 对数值求最大、最小值，ref 是一个用户传入的参考值，如果 candidate 成为 min 或 max
	 * 值，ref 会被记录下来作为参考。例如输入的序列为：
	 * [123, "a"], [-234, "b"], [108, "c"], [121, "d"]，
	 * 产出的 minMax 结果是：min=[-234, "b"], max=[123, "a"]
	 * @param candidate
	 * @param ref 如果不需要，可以设置 <code>null</code>
	 */
	public void minMax(long candidate, String ref) {
		getFunc(StatEntryFuncFactory.MIN_MAX).minMax(candidate, ref);
	}

	/**
	 * 累计数组对应位置的值。例如 values[0] 的值会和之前 values[0] 的值累加，
	 * values[1] 的值会和之前 values[1] 的值累加...，如果 values 和之前累加的长度不相等，
	 * 会以短的数组为准，超出部分不会处理（也就是 batchAdd 的数组长度，取决于最初的数组长）
	 * @param valuesToAdd
	 * @since 1.4.0
	 */
	public void batchAdd(long... valuesToAdd) {
		getFunc(StatEntryFuncFactory.BATCH_ADD).batchAdd(valuesToAdd);
	}

	/**
	 * 设置数组对应位置的值，如果数组已存在，会直接覆盖
	 * @param values
	 * @since 1.4.2
	 */
	public void strArray(String... values) {
		getFunc(StatEntryFuncFactory.STR_ARRAY).strArray(values);
	}
}
