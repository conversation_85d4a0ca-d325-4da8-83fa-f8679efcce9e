package com.taobao.eagleeye;

import java.util.Collections;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 提供给 LocalContext 和 RpcContext 共用的公共类
 */
abstract class AbstractContext extends BaseContext {
	/** 用于链路分组的 UserData key */
	static public final String EAGLEEYE_TRACE_GROUP_KEY = "g";
	protected final static String EMPTY_LOCALID = "";

	String remoteIp = EagleEyeCoreUtils.EMPTY_STRING;

	int span0 = 0;
	int span1 = 0;
	/** @deprecated */
	@Deprecated
	boolean isTopRpc = false;
	long startTime = 0L;
	long requestSize = 0L;
	long responseSize = 0L;
	/**
	 * 业务指定的链路分组，不透传
	 */
	TraceGroup traceGroup = null;
	/**
	 * 会通过 RPC 调用中传递的用户属性，会在兄弟间、父子间传递
	 */
	Map<String, String> attributes = null;

	/**
	 * 完全不会通过 RPC 调用传递的本地属性，只属于本 RPC
	 */
	Map<String, String> localAttributes = null;

	/**
	 * rpc和localContext都会有localId
	 */
	String localId = EMPTY_LOCALID;
	final AtomicInteger localIdx;

	// service receiver
	AbstractContext(String _traceId, String _rpcId) {
		this(_traceId, _rpcId, new AtomicInteger(0));
	}

	AbstractContext(String _traceId, String _rpcId, AtomicInteger _localIdx) {
		super(_traceId, _rpcId);
		localIdx = _localIdx;
	}

	public String getLocalId() {
		return localId;
	}
	/*
	 * 允许外部修改部分辅助的统计字段，方便异步回调时设置
	 * @since 1.2.0
	 */
	public long getRequestSize() {
		return requestSize;
	}
	public void setRequestSize(long requestSize) {
		this.requestSize = requestSize;
	}
	public long getResponseSize() {
		return responseSize;
	}
	public void setResponseSize(long responseSize) {
		this.responseSize = responseSize;
	}
	public String getRemoteIp() {
		return remoteIp;
	}
	public void setRemoteIp(String remoteIp) {
		this.remoteIp = remoteIp;
	}
	/*
	 * @since 1.2.2
	 */
	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}
	public long getStartTime() {
		return startTime;
	}
	public void setTimeSpan0(int span0) {
		this.span0 = span0;
	}
	public int getTimeSpan0() {
		return span0;
	}
	public void setTimeSpan1(int span1) {
		this.span1 = span1;
	}
	public int getTimeSpan1() {
		return span1;
	}

	/**
	 * 通过 logType 检验调用上下文的正确性
	 * @param expectedLogType 期望的 logType
	 * @param logName
	 * @param rpcTypeToSet
	 * @return
	 * @since 1.3.2
	 */
	final boolean isIllegalContext(int expectedLogType, String logName, int rpcTypeToSet) {
		if (this.logType != expectedLogType) {
			String logTypeStr;
			switch (this.logType) {
			case EagleEye.LOG_TYPE_TRACE:
				logTypeStr = "TRACE";
				break;
			case EagleEye.LOG_TYPE_RPC_CLIENT:
				logTypeStr = "CLIENT";
				break;
			case EagleEye.LOG_TYPE_RPC_SERVER:
				logTypeStr = "SERVER";
				break;
			default:
				logTypeStr = "OTHER";
				break;
			}
			EagleEye.selfLog("[WARN] context mismatch at " + logName + "()" +
					", ctx.logType=" + this.logType + "(" + logTypeStr + "), rpcTypeToSet=" + rpcTypeToSet +
					", ctx.traceId=" + this.traceId + ", ctx.rpcId=" + this.rpcId +
					", ctx.service=" + this.serviceName + ", ctx.method=" + this.methodName);
			return true;
		}
		return false;
	}

	/**
	 * 检查当前上下文是否被采样，有效范围在 [1, 9999] 之间，超出范围的数值都作为全采样处理。
	 *
	 * @return <code>true</code> 则需要输出日志，<code>false</code> 不输出
	 * @since 1.3.0
	 */
	public boolean isTraceSampled() {
		return EagleEyeCoreUtils.isTraceSampled(traceId, EagleEye.getSamplingInterval());
	}

	boolean isAppendExtendMsg() {
		final boolean appendTraceGroup = this.traceGroup != null;
		if (!appendTraceGroup) {
			return false;
		}
		return !appendTraceGroup;
	}

	protected void doAppendTraceGroup(StringBuilder appender, boolean escapeChars) {
		final TraceGroup traceGroup = this.traceGroup;
		if (traceGroup != null) {
			appender.append('@').append(EAGLEEYE_TRACE_GROUP_KEY).append(EagleEye.KV_SEPARATOR2);
			if (escapeChars) {
				EagleEyeCoreUtils.appendLog(traceGroup.getAppName(), appender, '|').append('$');
				EagleEyeCoreUtils.appendLog(traceGroup.getKey1(), appender, '|').append('$');
				EagleEyeCoreUtils.appendLog(traceGroup.getKey2(), appender, '|').append('$');
				EagleEyeCoreUtils.appendLog(traceGroup.getKey3(), appender, '|');
			} else {
				appender.append(traceGroup.getAppName()).append('$')
						.append(traceGroup.getKey1()).append('$')
						.append(traceGroup.getKey2()).append('$')
						.append(traceGroup.getKey3());
			}
			appender.append(EagleEye.ENTRY_SEPARATOR);
		}
	}

	void logContextData(StringBuilder appender) {
		final boolean appendAttributes = this.attributes != null && !this.attributes.isEmpty();
		final boolean appendLocalAttributes = this.localAttributes != null && !this.localAttributes.isEmpty();
		final boolean appendTraceGroup = this.traceGroup != null;
		if (!appendAttributes && !appendLocalAttributes && !appendTraceGroup) {
			return;
		}
		appender.append("|@");
		int startLen = appender.length();
		if (appendAttributes) {
			doAppendUserData(appender, true, startLen);
		}
		if (appendLocalAttributes) {
			doAppendLocalAttributes(appender, true, startLen);
		}
		if (appendTraceGroup) {
			doAppendTraceGroup(appender, true);
		}
	}

	/**
	 * 保留此方法, 与旧版本兼容
	 *
	 * @param appender
	 * @param escapeChars
	 * @param startLen
	 */
	protected void doAppendUserData(StringBuilder appender, boolean escapeChars, int startLen) {
		doAppendUserData(appender, escapeChars, startLen, EagleEye.KV_SEPARATOR2, EagleEye.ENTRY_SEPARATOR);
	}

	/**
	 * 对 AppendUserData 指定 kvSeparator 和 entrySeparator
	 *
	 * @since 1.4.6
	 *
	 * @param appender
	 * @param escapeChars
	 * @param startLen
	 * @param kvSeparator
	 * @param entrySeparator
	 */
	protected void doAppendUserData(StringBuilder appender, boolean escapeChars, int startLen, char kvSeparator, char entrySeparator) {
		for (Entry<String, String> entry : attributes.entrySet()) {
			String key = entry.getKey();
			String value = entry.getValue();
			if (EagleEyeCoreUtils.isNotEmpty(key) && value != null) {
				appender.append(key).append(kvSeparator);
				if (escapeChars) {
					EagleEyeCoreUtils.appendLog(value, appender, '|').append(EagleEye.ENTRY_SEPARATOR);
				} else {
					appender.append(value).append(entrySeparator);
				}
			}
			if (appender.length() - startLen >= EagleEye.MAX_USER_DATA_TOTAL_SIZE) {
				EagleEye.selfLog("[WARN] userData is too long, size=" + appender.length());
				break;
			}
		}
	}

	protected void doAppendLocalAttributes(StringBuilder appender, boolean escapeChars, int startLen) {
		for (Entry<String, String> entry : localAttributes.entrySet()) {
			String key = entry.getKey();
			String value = entry.getValue();
			if (EagleEyeCoreUtils.isNotEmpty(key) && value != null) {
				// 在 key 前面加 @ 来区分是否本地属性
				appender.append('@').append(key).append(EagleEye.KV_SEPARATOR2);
				if (escapeChars) {
					EagleEyeCoreUtils.appendLog(value, appender, '|').append(EagleEye.ENTRY_SEPARATOR);
				} else {
					appender.append(value).append(EagleEye.ENTRY_SEPARATOR);
				}
			}
			if (appender.length() - startLen >= EagleEye.MAX_USER_DATA_TOTAL_SIZE) {
				EagleEye.selfLog("[WARN] userData is too long, size=" + appender.length());
				break;
			}
		}
	}

	/**
	 * @since 1.2.0
	 */
	public String putUserData(String key, String value) {
		// FIXME 临时为了限制 tbsession 把 ss 设置为 userData 而不是 attribute 而做的调整，since 1.3.3，
		// 在 1.5.0 去掉下面的逻辑
		// 预计在 tbsession bugfix 版本普及后删除这段逻辑（问题代码在 tbsession 2.1.0 TaobaoSessionFilter）
		if (key != null && key.length() == 2 && key.charAt(0) == 's' && key.charAt(1) == 's') {
			return putLocalAttribute(key, value);
		}
		// ------------- end bugfix -------------
		// 透传数据的限制
    	if (EagleEyeCoreUtils.isBlank(key) || key.length() > EagleEye.MAX_USER_DATA_ENTRY_SIZE) {
    		EagleEye.selfLog("[WARN] userData is not accepted since key is blank or too long: " + key);
    		return null;
    	}
    	if (value != null && value.length() > EagleEye.MAX_USER_DATA_ENTRY_SIZE) {
    		EagleEye.selfLog("[WARN] userData is not accepted since value is too long: " + value);
    		return null;
    	}
		if (attributes == null) {
			attributes = new LinkedHashMap<String, String>();
		}
		return attributes.put(key, value);
	}

	/**
	 * @since 1.2.0
	 */
	public String removeUserData(String key) {
		if (attributes != null) {
			return attributes.remove(key);
		}
		return null;
	}

	/**
	 * @since 1.2.0
	 */
	public String getUserData(String key) {
		return attributes != null ? attributes.get(key) : null;
	}

    /**
     * @since 1.2.6
     */
    public Map<String, String> getUserDataMap() {
    	// 涉及透传，不允许外部修改
    	return attributes == null
    		? Collections.<String, String>emptyMap()
    		: Collections.<String, String>unmodifiableMap(attributes);
    }

	/**
	 * @since 1.2.1
	 */
	public String putLocalAttribute(String key, String value) {
		if (localAttributes == null) {
			localAttributes = new LinkedHashMap<String, String>();
		}
		return localAttributes.put(key, value);
	}

	/**
	 * @since 1.2.1
	 */
	public String removeLocalAttribute(String key) {
		if (localAttributes != null) {
			return localAttributes.remove(key);
		}
		return null;
	}

	/**
	 * @since 1.2.1
	 */
	public String getLocalAttribute(String key) {
		return localAttributes != null ? localAttributes.get(key) : null;
	}

    /**
     * @since 1.2.6
     */
    public Map<String, String> getLocalAttributeMap() {
		if (localAttributes == null) {
			localAttributes = new LinkedHashMap<String, String>();
		}
		// 因为不涉及透传，允许外部修改 localAttributes
    	return localAttributes;
    }

    /**
     * 取应用设置的链路分组
     * @since 1.2.6
     * @return
     */
    public TraceGroup traceGroup(String appName) {
    	if (traceGroup == null) {
    		traceGroup = new TraceGroup(appName);
    	}
    	return traceGroup;
    }

	/**
	 * 导出需要透传的 UserData
	 * @return
	 * @since 1.2.6
	 */
	public String exportUserData() {
		return exportUserData0(EagleEye.KV_SEPARATOR2, EagleEye.ENTRY_SEPARATOR);
	}

	/**
	 * 导出需要透传的 UserData, 导出的形式为可打印的字符
	 * 类似于 URL ? 之后的参数: k1=v1&k2=v2&
	 * 适用于对明文协议(比如 HTTP)做埋点时导出 userData
	 * @since 1.4.6
	 */
	public String exportPrintableUserData() {
		return exportUserData0('=', '&');
	}

	protected String exportUserData0(char kvSeparator, char entrySeparator) {
		Map<String, String> userData = this.attributes;
		if (userData == null || userData.isEmpty()) {
			return null;
		}
		StringBuilder appender = new StringBuilder(256);
		doAppendUserData(appender, false, 0, kvSeparator, entrySeparator);
		if (appender.length() == 0) {
			return null;
		}
		return appender.toString();
	}

	/**
	 * 导入透传过来的 UserData
	 * @param userData
	 * @since 1.2.6
	 */
	public void importUserData(String userData) {
		importUserData0(userData, EagleEye.KV_SEPARATOR2, EagleEye.ENTRY_SEPARATOR);
	}

	/**
	 * 导入透传过来的 UserData (类似于 URL ?之后的参数: k1=v1&k2=v2&)
	 * 注意: 此接口暂时没有对 & = 进行 escape
	 * 适用于导入明文协议(比如 HTTP)传递过来的 userData
	 *
	 * @param userData
	 * @since 1.4.6
	 */
	public void importPrintableUserData(String userData) {
		importUserData0(userData, '=', '&');
	}

	/**
	 * @since 1.4.6
	 */
	protected void importUserData0(String userData, char kvSeparator, char entrySeparator) {
		if (EagleEyeCoreUtils.isNotEmpty(userData) && EagleEye.isUserDataEnabled()) {
			String[] entries = EagleEyeCoreUtils.split(userData, entrySeparator);
			Map<String, String> map = new LinkedHashMap<String, String>(entries.length);
			for (String entry : entries) {
				int p = entry.indexOf(kvSeparator);
				if (p > 0 && p < entry.length()) {
					if (p == 1 && entry.charAt(0) == EagleEye.CLUSTER_TEST_KEY
							&& !EagleEye.isClusterTestEnabled()) {
						continue;
					}
					map.put(entry.substring(0, p), entry.substring(p + 1));
				}
			}
			if (!map.isEmpty()) {
				this.attributes = map;
			}
		}
	}
}
