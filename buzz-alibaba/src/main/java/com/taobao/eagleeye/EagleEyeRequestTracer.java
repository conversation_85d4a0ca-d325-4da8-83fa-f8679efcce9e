package com.taobao.eagleeye;

import java.net.URLDecoder;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 抽离和前端相关的 EagleEye 通用埋点逻辑。
 * 支持从 URL/Header 中获取 EagleEye 的配置。
 * 常见埋点方法：
 * <code><pre>
 * String traceId = EagleEyeRequestTracer.getTraceId(httpRequest);
 * EagleEyeRequestTracer.startTrace(traceId, req, resp);
 * try {
 *   // 处理业务逻辑...
 *   chain.doFilter(req, resp);
 * } finally {
 *   EagleEyeRequestTracer.endTrace(req, resp);
 * }
 * </pre></code>
 * startTrace 调用后，一定要保证调用一次 endTrace。
 * startTrace 和 endTrace 是幂等的，重复调用不会影响逻辑。
 * 如果没有调用 startTrace，直接调用 endTrace，也是可以的。
 *
 * 这里的少量代码和 {@link EagleEye} 会有重复，是因为老版本 EagleEye 没有
 * 这些方法，需要兼容。
 * @since 1.2.0
 * <AUTHOR>
 */
public class EagleEyeRequestTracer {

	private static final String[] IP_HEADERS = {
		"X-Forwarded-For",
		"Proxy-Client-IP",
		"WL-Proxy-Client-IP", // 优先获取其他代理设置的真实用户ip
		"X-Real-IP",		  // Tengine 设置 remoteIP，如果没有拿到 NS-Client-IP，那么这就是真实的用户 ip
		"NS-Client-IP",       // 淘宝 NAT 方式设置的ip
	};

	/** URL 设置 traceId 的参数 */
	public static final String EAGLEEYE_TRACEID_PARAM_KEY = "tb_eagleeye_traceid";

	/** URL 配置 userData 的 key 前缀 */
	public static final String EAGLEEYE_USERDATA_PARAM_KEY = "tb_eagleeyex_";

	/**
	 * Header 设置 traceId 的参数
	 * @since 1.2.0
	 */
	public static final String EAGLEEYE_TRACEID_HEADER_KEY = "EagleEye-TraceId";

	/**
	 * Header 设置 rpcId 的参数
	 * @since 1.2.6
	 */
	public static final String EAGLEEYE_RPCID_HEADER_KEY = "EagleEye-RpcId";

	/**
	 * Header 设置 userData 的参数
	 * @since 1.4.6
	 */
	public static final String EAGLEEYE_USERDATA_HEADER_KEY = "EagleEye-UserData";

	/** 用于入口分类的 UserData key */
	public static final String EAGLEEYE_URL_CLASSIFIER_KEY = EagleEye.URL_CLASSIFIER_KEY;

	/** 用于跟踪来源应用的 UserData key */
	public static final String EAGLEEYE_ROOT_CLASSIFIER_KEY = RpcContext_inner.EAGLEEYE_ROOT_CLASSIFIER_KEY;

	/** 本机生成的 TraceId 的前缀 */
	private static final String LOCAL_TRACE_ID_PREFIX = EagleEye.generateTraceId(null).substring(0, 8);

	private static final boolean checkIP(String ip) {
		if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
			return false;
		} else {
			return true;
		}
	}

	/**
	 * 获取远程客户端的 IP
	 * @param request
	 * @return
	 */
	public static final String getRemoteAddress(HttpServletRequest request) {
		String ip = null;
		boolean valid = false;
		for (String header : IP_HEADERS) {
			ip = request.getHeader(header);
			valid = checkIP(ip);
			if (valid) {
				break;
			}
		}
		if (!valid) {
			ip = request.getRemoteAddr();
		}
		// 代理时会有逗号分隔的 ip，获取第一个即可
		int index = ip.indexOf(',');
		if (index != -1) {
			String firstIp = ip.substring(0, index).trim();
			if (checkIP(ip)) {
				ip = firstIp;
			}
		}
		return ip;
	}

	/**
	 * 获取 TraceId，如果 ThreadLocal 没有，返回 <code>null</code>。
	 * @return
	 */
	public static final String getThreadLocalTraceId() {
        RpcContext_inner ctx = RpcContext_inner.get();
		return ctx != null ? ctx.traceId : null;
	}

	/**
	 * 获取 TraceId，如果没有，自动生成。
	 * @param request
	 * @return
	 * @see #getTraceId(HttpServletRequest, String)
	 */
	public static final String getTraceId(HttpServletRequest request) {
		// 传入 null 则使用服务器自身的 IP
		return getTraceId(request, null);
	}

	/**
	 * 获取 TraceId。根据以下步骤：
	 * <ol>
	 * <li>从 ThreadLocal 获取</li>
	 * <li>从 URL 参数中 获取</li>
	 * <li>从 header 中 获取</li>
	 * <li>如果上述都没有，则自动生成，如果 ip 不为 <code>null</code>，则基于指定的 ip，
	 *     否则，使用本机 ip
	 * </li>
	 * </ol>
	 * @param httpRequest
	 * @param ip
	 * @return
	 */
	public static final String getTraceId(HttpServletRequest httpRequest, String ip) {
		String traceId = getThreadLocalTraceId();
		if (!EagleEyeCoreUtils.isBlank(traceId)) {
			return traceId;
		}

		// 检查 url 中的调用链配置，这种配置是很罕见的，因此要尽量保证性能
		String queryString = httpRequest.getQueryString();
		traceId = getParamFromUrl(queryString, EAGLEEYE_TRACEID_PARAM_KEY);
		if (EagleEyeCoreUtils.isValidTraceId(traceId)) {
			return traceId;
		}

		// 检查 header 中的调用链配置
		traceId = EagleEyeCoreUtils.trim(httpRequest.getHeader(EAGLEEYE_TRACEID_HEADER_KEY));
		if (EagleEyeCoreUtils.isValidTraceId(traceId)) {
			return traceId;
		}

		return TraceIdGenerator.generate(ip);
	}

	static final String getParamFromUrl(final String queryString, final String paramKey) {
		if (queryString != null) {
			final int left = queryString.indexOf(paramKey);
			final int keyEnd = left + paramKey.length() + 1;
			if (left != -1 && keyEnd < queryString.length()
					&& queryString.charAt(keyEnd - 1) == '='
					&& queryString.charAt(keyEnd) != '&') {
				final int right = queryString.indexOf('&', keyEnd);
				if (right != -1) {
					return queryString.substring(keyEnd, right);
				} else {
					return queryString.substring(keyEnd);
				}
			}
		}
		return null;
	}

	/**
	 * 开始调用链，注意，开始之后，不管后续处理是否正常，都需要调用
	 * {@link #endTrace(HttpServletRequest, HttpServletResponse)}。
	 * @param traceId
	 * @param httpRequest
	 * @param httpResponse
	 */
	public static final void startTrace(final String traceId,
			HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		final String url = getRequestUrl(httpRequest);
		final String rpcId = EagleEyeCoreUtils.trim(httpRequest.getHeader(EAGLEEYE_RPCID_HEADER_KEY));
		final String queryString = httpRequest.getQueryString();
		startTraceInner(traceId, rpcId, url, queryString, httpRequest, httpResponse);
	}

	private static final String getRequestUrl(HttpServletRequest httpRequest) {
		StringBuffer sb = httpRequest.getRequestURL();
		final String url;
		if (sb != null) {
			url = sb.toString();
		} else {
			url = httpRequest.getRequestURI();
		}
		return url;
	}

	/**
	 * 开始调用链，允许另外指定 TraceId、RpcId、URL、Query 参数等信息。注意，开始之后，不管后续处理是否正常，
	 * 都需要调用 {@link #endTrace(HttpServletRequest, HttpServletResponse)}。
	 * @param traceId
	 * @param url
	 * @param queryString
	 * @param httpRequest
	 * @param httpResponse
	 * @since 1.3.2
	 */
	public static final void startTraceInner(String traceId, String rpcId, String url,
			String queryString, HttpServletRequest httpRequest, HttpServletResponse httpResponse) {
		EagleEye.startTrace(traceId, rpcId, url);

		// since 1.4.6, 先解析 http header 中的 userData
		final RpcContext_inner ctx = RpcContext_inner.get();
		if (ctx != null) {
			final String printableUserData = EagleEyeCoreUtils.trim(httpRequest.getHeader(EAGLEEYE_USERDATA_HEADER_KEY));
			ctx.importPrintableUserData(printableUserData);
		}

		processUserData(url, queryString);

		// @since 1.3.0 如果 TraceId 不是自己产生的，且没有入口标记，就把自己的 IP 作为入口透传
		// 用来解决统一接入层生成的 TraceId 不是 Java 服务端 IP 的问题
		if (traceId != null && !traceId.startsWith(LOCAL_TRACE_ID_PREFIX)) {
			RpcContext_inner rpcContext = EagleEye.getRpcContext();
			if (rpcContext != null && rpcContext.getUserData(EAGLEEYE_ROOT_CLASSIFIER_KEY) == null) {
				rpcContext.putUserData(EAGLEEYE_ROOT_CLASSIFIER_KEY, EagleEyeCoreUtils.getLocalAddress());
			}
		}
	}

	/**
	 * 处理在 URL 上配置的业务扩展信息
	 * @since 1.2.0
	 */
	private static final void processUserData(final String url, final String queryString) {
		if (EagleEye.isUserDataEnabled() == false) {
			return;
		}

		if (url != null) {
			EagleEye.traceName(url, null);
		}

		parseQueryString(queryString);
	}

	static void parseQueryString(final String queryString) {
		// 仅在 EagleEye 1.1.4 以上版本做这个逻辑，配置 queryString 在正常情况是很罕见的，因此要尽量保证性能
		// 长度至少为 “tb_eagleeyex_a=”
		if (queryString != null && queryString.length() >= 15) {
			int left = queryString.indexOf(EAGLEEYE_USERDATA_PARAM_KEY);
			int mapSize = 0; // 对放入 Map 的数据数量做限制
			while (left != -1 && mapSize < 64) {
				// 将 URL 上配置的
				// tb_eagleeyex_key1=value1&tb_eagleeyex_key2=value2
				// 设置到 EagleEye 的业务信息上面 {"key1":"value1", "key2":"value2"}
				left += EAGLEEYE_USERDATA_PARAM_KEY.length();
				final int eq = queryString.indexOf('=', left);
				if (eq <= left) {
					// 不合法
					break;
				} else {
					String value;
					final String key = queryString.substring(left, eq);
					int right = queryString.indexOf('&', eq + 1);
					if (right != -1) {
						// key=value&...
						value = queryString.substring(eq + 1, right);
						left = queryString.indexOf(EAGLEEYE_USERDATA_PARAM_KEY, right + 1);
					} else {
						// key=value 末尾
						value = queryString.substring(eq + 1);
						left = -1;
					}

					// 还原经过 URL 编码的参数值
					try {
						value = URLDecoder.decode(value, "UTF-8");
					} catch (Exception e) {
					}

					if (key.length() == 1 && key.charAt(0) == EagleEye.CLUSTER_TEST_KEY) {
						// 检测到是压测标记，只有在开关打开的时候才设置进来
						if (EagleEye.isClusterTestEnabled()) {
							EagleEye.putUserData(key, value);
							mapSize += 1;
						}
					} else {
						EagleEye.putUserData(key, value);
						mapSize += 1;
					}
				}
			}
		}
	}

	/**
	 * 结束调用链。
	 * 注意：需要假定 response 已经提交，因此只能做只读操作，不能修改
	 * @param httpRequest
	 * @param httpResponse
	 */
	public static final void endTrace(HttpServletRequest httpRequest,
			HttpServletResponse httpResponse) {
		HttpContextDataExposer exposer = getDataExposer(httpResponse);
		endTrace(httpRequest, httpResponse, exposer.getStatusCode(httpResponse), EagleEye.TYPE_TRACE);
	}

	/**
	 * 结束调用链，设置响应状态码和调用类型。
	 * 注意：需要假定 response 已经提交，因此只能做只读操作，不能修改
	 * @param httpRequest
	 * @param httpResponse
	 * @param resultCode 建议使用 {@link HttpServletResponse#setStatus(int) HTTP 状态码}
	 * @param type
	 * @since 1.2.7
	 */
	public static final void endTrace(HttpServletRequest httpRequest,
			HttpServletResponse httpResponse, String resultCode, int type) {
		if (EagleEye.isContextDumpEnabled()) {
			EagleEye.dumpImportant("http-trace",
					getRequestUrl(httpRequest), httpRequest.getParameterMap());
		}
		EagleEye.endTrace(resultCode, type);
	}

	/*
	 * Http 上下文数据获取
	 */
	private static HttpContextDataExposer exposer = null;

	private static final HttpContextDataExposer getDataExposer(HttpServletResponse httpResponse) {
		final HttpContextDataExposer exposer = EagleEyeRequestTracer.exposer;
		return exposer != null ? exposer : HttpContextDataExposerFactory.createDataExposer(httpResponse);
	}
}
