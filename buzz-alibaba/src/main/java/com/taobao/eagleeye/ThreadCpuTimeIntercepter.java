package com.taobao.eagleeye;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadMXBean;

/**
 * 收集服务、入口的 CPU 消耗，单位是纳秒，保存在调用的本地 attribute 里面
 *
 * <AUTHOR>
 * @since 1.3.5.2
 */
public class ThreadCpuTimeIntercepter extends EagleEyeContextListener {

	/**
	 * 开始调用时的 CPU 时间（累计），单位是纳秒
	 */
	private static final String LOCAL_CPU_TIME_START_KEY = "cpuInfo";
	/**
	 * 调用完毕后，计算出实际消耗的 CPU 时间，单位是纳秒
	 */
	private static final String LOCAL_CPU_TIME_DIFF_KEY = "cpu";

	private static final ThreadCpuTimeIntercepter singleton = new ThreadCpuTimeIntercepter();

	/**
	 * CPU 时间的采集，默认为 100 次链路调用做一次
	 */
	private int threadCpuTimeSamplingInterval = 100;

	private ThreadCpuTimeIntercepter() {
	}

	public static ThreadCpuTimeIntercepter getInstance() {
		return singleton;
	}

	public int getThreadCpuTimeSamplingInterval() {
		return threadCpuTimeSamplingInterval;
	}

	public void setThreadCpuTimeSamplingInterval(int threadCpuTimeSamplingInterval) {
		this.threadCpuTimeSamplingInterval = threadCpuTimeSamplingInterval;
	}

	@Override
	public void afterStartTrace(RpcContext_inner context) {
		beginThreadCpuTime(context);
	}

	@Override
	public void afterEndTrace(RpcContext_inner context) {
		endThreadCpuTime(context);
	}

	@Override
	public void afterRpcServerRecv(RpcContext_inner context) {
		beginThreadCpuTime(context);
	}

	@Override
	public void afterRpcServerSend(RpcContext_inner context) {
		endThreadCpuTime(context);
	}

	private void beginThreadCpuTime(final RpcContext_inner context) {
		final String traceId = context.getTraceId();
		if (traceId != null
				&& EagleEyeCoreUtils.isTraceSampled(traceId, threadCpuTimeSamplingInterval)) {
			final long cpuTime = getCpuTime();
			if (cpuTime != -1) {
				String cpuInfo = cpuTime + "_" + Thread.currentThread().getId();
				context.putLocalAttribute(LOCAL_CPU_TIME_START_KEY, cpuInfo);
			}
		}
	}

	private void endThreadCpuTime(final RpcContext_inner context) {
		final String traceId = context.getTraceId();
		final String cpuInfo;
		if (traceId != null
				&& (cpuInfo = context.getLocalAttribute(LOCAL_CPU_TIME_START_KEY)) != null
				&& EagleEyeCoreUtils.isTraceSampled(traceId, threadCpuTimeSamplingInterval)) {
			final long cpuTime = getCpuTime();
			final int x;
			if (cpuTime != -1 && (x = cpuInfo.indexOf('_')) > 0 && x < cpuInfo.length() - 1) {
				try {
					final long cpuTimeBefore = Long.parseLong(cpuInfo.substring(0, x));
					final long threadIdBefore = Long.parseLong(cpuInfo.substring(x + 1));
					if (threadIdBefore == Thread.currentThread().getId()) {
						// 目前先支持同步调用的，异步调用的跟踪很复杂先跳过
						final long diff = cpuTime - cpuTimeBefore;
						context.putLocalAttribute(LOCAL_CPU_TIME_DIFF_KEY, Long.toString(diff));
					}
				} catch (Exception e) {
					// quietly
				}
			}
			context.removeLocalAttribute(LOCAL_CPU_TIME_START_KEY);
		}
	}

	private static ThreadMXBean threadMXBean = getThreadMXBean();

	private static ThreadMXBean getThreadMXBean() {
		try {
			// 取 ThreadMXBean 的实现有个锁
			ThreadMXBean instance = ManagementFactory.getThreadMXBean();
			if (instance != null
					&& instance.isCurrentThreadCpuTimeSupported()
					&& instance.isThreadCpuTimeEnabled()
					&& instance.getCurrentThreadCpuTime() != -1) {
				return instance;
			}
		} catch (Throwable t) {
			// quietly
		}
		// 没有正常取得时，直接设置成 null，以后不再尝试
		EagleEye.selfLog("[WARN] fail to get ThreadMXBean, "
				+ "or ThreadMXBean.getCurrentThreadCpuTime is not supported");
		return null;
	}

	private long getCpuTime() {
		if (threadMXBean != null) {
			try {
				return threadMXBean.getCurrentThreadCpuTime();
			} catch (Throwable t) {
				// 出错的情况，直接设置成 null，以后不再尝试
				threadMXBean = null;
				EagleEye.selfLog("[WARN] fail to get thread cpu time, feature disabled", t);
				return -1;
			}
		}
		return -1;
	}
}
