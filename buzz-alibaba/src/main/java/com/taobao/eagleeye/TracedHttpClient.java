package com.taobao.eagleeye;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.InetAddress;

import org.apache.http.Header;
import org.apache.http.HttpEntity;
import org.apache.http.HttpEntityEnclosingRequest;
import org.apache.http.HttpHost;
import org.apache.http.HttpInetConnection;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.StatusLine;
import org.apache.http.client.ClientProtocolException;
import org.apache.http.client.HttpClient;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpUriRequest;
import org.apache.http.conn.BasicManagedEntity;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.entity.HttpEntityWrapper;
import org.apache.http.params.HttpParams;
import org.apache.http.protocol.HttpContext;

/**
 * 对 {@link HttpClient} 做调用跟踪的类
 * <AUTHOR>
 * @since 1.2.0
 */
public class TracedHttpClient implements HttpClient {

	/*
	 * 实现备忘：
	 * 这个类依赖了 HttpClient v4 的类，所以不会被 HSF 或潘多拉运行期导出。
	 * 这个类会被应用依赖的 eagleeye-core 加载，而 EagleEye 的其它类由容器加载。
	 * 因此这个类的实现不能访问包 com.taobao.eagleeye 里面的非 public 方法或字段，
	 * 否则会报 IllegalAccessError
	 */

	protected final HttpClient delegate;
	protected final int rpcType;
	protected final boolean autoStartRpc;

	protected static final Field MANAGED_CONN_FIELD;
	static {
		Field field = null;
		try {
			field = BasicManagedEntity.class.getDeclaredField("managedConn");
			field.setAccessible(true);
		} catch (Throwable e) {
			field = null;
		}
		MANAGED_CONN_FIELD = field;
	}

	public TracedHttpClient(HttpClient delegate, int rpcType) {
		this(delegate, rpcType, false);
	}

	public TracedHttpClient(HttpClient delegate, int rpcType, boolean autoStartRpc) {
		this.delegate = delegate;
		this.rpcType = rpcType;
		this.autoStartRpc = autoStartRpc;
	}

	public HttpClient getDelegate() {
		return this.delegate;
	}

	public HttpParams getParams() {
		return delegate.getParams();
	}

	public ClientConnectionManager getConnectionManager() {
		return delegate.getConnectionManager();
	}

	/**
	 * 返回带跟踪功能的 request
	 * @param <REQ>
	 * @param request
	 * @param ctx
	 * @return
	 */
	protected <REQ extends HttpRequest> REQ traceRequest(REQ request, RpcContext_inner ctx) {
		if (request instanceof HttpEntityEnclosingRequest) {
			HttpEntityEnclosingRequest er = (HttpEntityEnclosingRequest) request;
			HttpEntity requestEntity = er.getEntity();
			if (requestEntity != null) {
				er.setEntity(new TracedRequestEntity(requestEntity, ctx, rpcType));
			}
		}

		// 当前有上一层调用上下文，那么 Http 调用时把当前上下文传递过去，组成可以嵌套的调用链
		if (ctx.getRpcId().length() >= 3) {
			request.addHeader(EagleEyeRequestTracer.EAGLEEYE_TRACEID_HEADER_KEY, ctx.getTraceId());
			request.addHeader(EagleEyeRequestTracer.EAGLEEYE_RPCID_HEADER_KEY, ctx.getRpcId());
		}

		ctx.setRequestSize(getAllHeaderSize(request.getAllHeaders()) +
				request.getRequestLine().toString().length() + 2);
		return request;
	}

	/**
	 * 返回带跟踪功能的 response
	 * @param response
	 * @param ctx
	 * @return
	 */
	protected HttpResponse traceResponse(HttpRequest request, HttpResponse response,
			RpcContext_inner ctx) {
		if (response == null) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			return response;
		}

		StatusLine statusLine = response.getStatusLine();
		String resultCode;
		if (statusLine != null) {
			resultCode = String.valueOf(statusLine.getStatusCode());
			ctx.setResponseSize(ctx.getResponseSize() + statusLine.toString().length() + 1);
		} else {
			resultCode = EagleEye.RPC_RESULT_FAILED;
		}
		ctx.setResponseSize(ctx.getResponseSize() + getAllHeaderSize(response.getAllHeaders()) + 1);

		HttpEntity responseEntity = response.getEntity();
		if (responseEntity == null) {
			ctx.endRpc(resultCode, rpcType, null);
			EagleEye.commitRpcContext(ctx);
		} else {
			// 在没有设置 IP 时，尝试获取 IP (允许业务设置更合理的 IP)
			if (ctx.getRemoteIp() == null || ctx.getRemoteIp().length() == 0) {
				if (responseEntity instanceof BasicManagedEntity) {
					Object conn;
					try {
						conn = MANAGED_CONN_FIELD.get(responseEntity);
					} catch (Exception e) {
						conn = null;
					}
					if (conn instanceof HttpInetConnection) {
						final InetAddress remoteAddress = ((HttpInetConnection) conn).getRemoteAddress();
						if (remoteAddress != null) {
							ctx.setRemoteIp(remoteAddress.getHostAddress());
						}
					}
				} else if (request instanceof HttpUriRequest) {
					// 直接把 host 解析出来，这个方式会比较慢，不作为优先选择的方式
					HttpUriRequest uriReq = (HttpUriRequest) request;
					try {
						String host = uriReq.getURI().getHost();
						ctx.setRemoteIp(InetAddress.getByName(host).getHostAddress());
					} catch (Exception e) {
					}
				}
			}
			response.setEntity(new TracedResponseEntity(responseEntity, ctx, rpcType, resultCode));
		}
		return response;
	}

	protected int getAllHeaderSize(Header[] headers) {
		int size = 0;
		for (Header header : headers) {
			String hvalue = header.getValue();
			int hvalueSize = hvalue != null ? hvalue.length() : 0;
			size += header.getName().length() + hvalueSize + 3; // hkey: hvalue\n
		}
		return size;
	}

	/**
	 * 获取当前 Http 调用的上下文：
	 * 如果 autoStartRpc=true，且 request 是 {@link HttpUriRequest}，可以自动 startRpc，否则返回 <code>null</code>。
	 * 如果 autoStartRpc=false，需要判断当前上下文有没有 startRpc。如果没有 startRpc，返回 <code>null</code>。
	 * @param request
	 * @return
	 */
	protected RpcContext_inner getHttpRpcContext(HttpRequest request) {
		RpcContext_inner ctx;
		if (autoStartRpc) {
			if (request instanceof HttpUriRequest) {
				final HttpUriRequest uriRequest = (HttpUriRequest) request;
				EagleEye.startRpc(uriRequest.getMethod(), uriRequest.getURI().toString());
				return EagleEye.popRpcContext();
			} else {
				// 无法自动 startRpc
				return null;
			}
		} else {
			// 依赖外部中间件调用EagleEye.startRpc()，需要检查 RpcContext 是否确实 start
			ctx = EagleEye.getRpcContext();
			if (ctx == null) {
				return null;
			}
			if (ctx.getParentRpcContext() == null &&
					!EagleEye.MAL_ROOT_RPC_ID.equals(ctx.getRpcId())) {
				// 可以接受两种情况：有上一级的上下文，或者为孤儿上下文
				return null;
			}
			// 如果确实已经 startRpc，下面相当于 popRpcContext
			EagleEye.setRpcContext(ctx.getParentRpcContext());
			return ctx;
		}
	}

	public HttpResponse execute(HttpUriRequest request) throws IOException, ClientProtocolException {
		RpcContext_inner ctx = getHttpRpcContext(request);
		if (ctx == null) {
			return delegate.execute(request);
		}
		final HttpUriRequest tracedRequest = traceRequest(request, ctx);
		ctx.rpcClientSend();
		try {
			return traceResponse(request, delegate.execute(tracedRequest), ctx);
		} catch (IOException e) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			throw e;
		}
	}

	public HttpResponse execute(HttpUriRequest request, HttpContext context) throws IOException,
			ClientProtocolException {
		RpcContext_inner ctx = getHttpRpcContext(request);
		if (ctx == null) {
			return delegate.execute(request, context);
		}
		final HttpUriRequest tracedRequest = traceRequest(request, ctx);
		ctx.rpcClientSend();
		try {
			return traceResponse(request, delegate.execute(tracedRequest, context), ctx);
		} catch (IOException e) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			throw e;
		}
	}

	public HttpResponse execute(HttpHost target, HttpRequest request) throws IOException,
			ClientProtocolException {
		RpcContext_inner ctx = getHttpRpcContext(request);
		if (ctx == null) {
			return delegate.execute(target, request);
		}
		final HttpRequest tracedRequest = traceRequest(request, ctx);
		ctx.rpcClientSend();
		try {
			return traceResponse(request, delegate.execute(target, tracedRequest), ctx);
		} catch (IOException e) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			throw e;
		}
	}

	public HttpResponse execute(HttpHost target, HttpRequest request, HttpContext context)
			throws IOException, ClientProtocolException {
		RpcContext_inner ctx = getHttpRpcContext(request);
		if (ctx == null) {
			return delegate.execute(target, request, context);
		}
		final HttpRequest tracedRequest = traceRequest(request, ctx);
		ctx.rpcClientSend();
		try {
			return traceResponse(request, delegate.execute(target, tracedRequest, context), ctx);
		} catch (IOException e) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			throw e;
		}
	}

	public <T> T execute(HttpUriRequest request, ResponseHandler<? extends T> responseHandler)
			throws IOException, ClientProtocolException {
		RpcContext_inner ctx = getHttpRpcContext(request);
		if (ctx == null) {
			return delegate.execute(request, responseHandler);
		}
		final HttpUriRequest tracedRequest = traceRequest(request, ctx);
		ctx.rpcClientSend();
		try {
			return delegate.execute(tracedRequest, new TracedResponseHandler<T>(ctx, responseHandler));
		} catch (IOException e) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			throw e;
		}
	}

	public <T> T execute(HttpUriRequest request, ResponseHandler<? extends T> responseHandler,
			HttpContext context) throws IOException, ClientProtocolException {
		RpcContext_inner ctx = getHttpRpcContext(request);
		if (ctx == null) {
			return delegate.execute(request, responseHandler, context);
		}
		final HttpUriRequest tracedRequest = traceRequest(request, ctx);
		final TracedResponseHandler<T> tracedHandler = new TracedResponseHandler<T>(ctx, responseHandler);
		ctx.rpcClientSend();
		try {
			return delegate.execute(tracedRequest, tracedHandler, context);
		} catch (IOException e) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			throw e;
		}
	}

	public <T> T execute(HttpHost target, HttpRequest request,
			ResponseHandler<? extends T> responseHandler) throws IOException,
			ClientProtocolException {
		RpcContext_inner ctx = getHttpRpcContext(request);
		if (ctx == null) {
			return delegate.execute(target, request, responseHandler);
		}
		final HttpRequest tracedRequest = traceRequest(request, ctx);
		final TracedResponseHandler<T> tracedHandler = new TracedResponseHandler<T>(ctx, responseHandler);
		ctx.rpcClientSend();
		try {
			return delegate.execute(target, tracedRequest, tracedHandler);
		} catch (IOException e) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			throw e;
		}
	}

	public <T> T execute(HttpHost target, HttpRequest request,
			ResponseHandler<? extends T> responseHandler, HttpContext context) throws IOException,
			ClientProtocolException {
		RpcContext_inner ctx = getHttpRpcContext(request);
		if (ctx == null) {
			return delegate.execute(target, request, responseHandler, context);
		}
		final HttpRequest tracedRequest = traceRequest(request, ctx);
		final TracedResponseHandler<T> tracedHandler = new TracedResponseHandler<T>(ctx, responseHandler);
		ctx.rpcClientSend();
		try {
			return delegate.execute(target, tracedRequest, tracedHandler, context);
		} catch (IOException e) {
			ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
			EagleEye.commitRpcContext(ctx);
			throw e;
		}
	}

	/**
	 * 支持回调跟踪
	 */
	class TracedResponseHandler<T> implements ResponseHandler<T> {

		private final RpcContext_inner ctx;
		private final ResponseHandler<? extends T> handler;

		public TracedResponseHandler(RpcContext_inner ctx, ResponseHandler<? extends T> handler) {
			this.ctx = ctx;
			this.handler = handler;
		}

		@Override
		public T handleResponse(HttpResponse response) throws ClientProtocolException,
				IOException {
			return handler.handleResponse(traceResponse(null, response, ctx));
		}
	}

	/**
	 * 对请求用的 {@link HttpEntity} 做跟踪
	 */
	static class TracedRequestEntity extends HttpEntityWrapper {

		protected final RpcContext_inner ctx;
		protected final int rpcType;

		protected TracedRequestEntity(
				HttpEntity delegate, RpcContext_inner ctx, int rpcType) {
			super(delegate);
			this.ctx = ctx;
			this.rpcType = rpcType;
		}

		public HttpEntity getDelegate() {
			return wrappedEntity;
		}

		public InputStream getContent() throws IOException, IllegalStateException {
			InputStream stream = wrappedEntity.getContent();
			if (stream == null) {
				return null;
			}
			return new TracedInputStream(stream, ctx, null, true);
		}

		public void writeTo(OutputStream outstream) throws IOException {
			wrappedEntity.writeTo(new TracedOutputStream(outstream, ctx, null, true));
		}
	}

	/**
	 * 对响应的 {@link HttpEntity} 做跟踪
	 */
	static class TracedResponseEntity extends HttpEntityWrapper {

		protected final RpcContext_inner ctx;
		protected final int rpcType;
		protected final String defaultResultCode;

		protected TracedResponseEntity(HttpEntity delegate,
				RpcContext_inner ctx, int rpcType, String defaultResultCode) {
			super(delegate);
			this.ctx = ctx;
			this.rpcType = rpcType;
			this.defaultResultCode = defaultResultCode;
		}

		public HttpEntity getDelegate() {
			return wrappedEntity;
		}

		public InputStream getContent() throws IOException, IllegalStateException {
			InputStream stream;
			try {
				stream = wrappedEntity.getContent();
			} catch (IOException e) {
				ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
				EagleEye.commitRpcContext(ctx);
				throw e;
			}
			if (stream == null) {
				ctx.endRpc(defaultResultCode, rpcType, null);
				EagleEye.commitRpcContext(ctx);
				return null;
			}
			if (wrappedEntity.isRepeatable()) {
				return new TracedInputStream(stream, ctx, null, false);
			}
			return new TracedInputStream(stream, ctx,
					new RpcClientRecvOnClose(ctx, rpcType, defaultResultCode), false);
		}

		public void writeTo(OutputStream outstream) throws IOException {
			try {
				wrappedEntity.writeTo(new TracedOutputStream(outstream, ctx,
						new RpcClientRecvOnClose(ctx, rpcType, defaultResultCode), false));
			} catch (IOException e) {
				ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
				EagleEye.commitRpcContext(ctx);
				throw e;
			}
		}
	}

	/**
	 * 关闭流时进行的回调
	 */
	static interface StreamOnCloseListener {
		void onClose(boolean hasError);
	}

	/**
	 * 关闭流时提交响应大小，并标记结束 Rpc 响应结束
	 */
	static class RpcClientRecvOnClose implements StreamOnCloseListener {

		private final RpcContext_inner ctx;
		private final int rpcType;
		private final String defaultResultCode;

		public RpcClientRecvOnClose(RpcContext_inner ctx, int rpcType, String defaultResultCode) {
			this.ctx = ctx;
			this.rpcType = rpcType;
			this.defaultResultCode = defaultResultCode;
		}

		public void onClose(boolean hasError) {
			if (ctx != null) {
				if (hasError) {
					ctx.endRpc(EagleEye.RPC_RESULT_FAILED, rpcType, null);
				} else {
					ctx.endRpc(defaultResultCode, rpcType, null);
				}
				EagleEye.commitRpcContext(ctx);
			}
		}
	}

	/**
	 * 跟踪流的输入数据大小
	 */
	static class TracedInputStream extends InputStream {
		protected final InputStream delegate;
		protected final RpcContext_inner ctx;
		protected final StreamOnCloseListener listener;
		protected final boolean onRequest;
		protected boolean hasError;

		protected TracedInputStream(InputStream delegate,
				RpcContext_inner ctx, StreamOnCloseListener listener, boolean onRequest) {
			this.delegate = delegate;
			this.ctx = ctx;
			this.listener = listener;
			this.onRequest = onRequest;
		}

		public InputStream getDelegate() {
			return this.delegate;
		}

		public int read() throws IOException {
			try {
				final int ret = delegate.read();
				if (onRequest) {
					ctx.setRequestSize(ctx.getRequestSize() + 1);
				} else {
					ctx.setResponseSize(ctx.getResponseSize() + 1);
				}
				return ret;
			} catch (IOException e) {
				hasError = true;
				throw e;
			}
		}

		@Override
		public int read(byte[] b) throws IOException {
			return read(b, 0, b.length);
		}

		@Override
		public int read(byte[] b, int off, int len) throws IOException {
			try {
				final int size = delegate.read(b, off, len);
				if (onRequest) {
					ctx.setRequestSize(ctx.getRequestSize() + size);
				} else {
					ctx.setResponseSize(ctx.getResponseSize() + size);
				}
				return size;
			} catch (IOException e) {
				hasError = true;
				throw e;
			}
		}

		@Override
		public long skip(long n) throws IOException {
			try {
				final long skipped = delegate.skip(n);
				if (onRequest) {
					ctx.setRequestSize(ctx.getRequestSize() + skipped);
				} else {
					ctx.setResponseSize(ctx.getResponseSize() + skipped);
				}
				return skipped;
			} catch (IOException e) {
				hasError = true;
				throw e;
			}
		}

		@Override
		public void close() throws IOException {
			try {
				delegate.close();
				if (listener != null) {
					listener.onClose(hasError);
				}
			} catch (IOException e) {
				if (listener != null) {
					listener.onClose(true);
				}
				throw e;
			}
		}

		@Override
		public void mark(int readlimit) {
			delegate.mark(readlimit);
		}

		@Override
		public void reset() throws IOException {
			try {
				delegate.reset();
				if (onRequest) {
					ctx.setRequestSize(0);
				} else {
					ctx.setResponseSize(0);
				}
			} catch (IOException e) {
				hasError = true;
				throw e;
			}
		}

		@Override
		public boolean markSupported() {
			return delegate.markSupported();
		}
	}

	/**
	 * 跟踪流的输出数据大小
	 */
	static class TracedOutputStream extends OutputStream {
		protected final OutputStream delegate;
		protected final RpcContext_inner ctx;
		protected final StreamOnCloseListener listener;
		protected final boolean onRequest;
		protected boolean hasError;

		protected TracedOutputStream(OutputStream delegate,
				RpcContext_inner ctx, StreamOnCloseListener listener, boolean onRequest) {
			this.delegate = delegate;
			this.ctx = ctx;
			this.listener = listener;
			this.onRequest = onRequest;
		}

		public OutputStream getDelegate() {
			return this.delegate;
		}

		public void write(int b) throws IOException {
			try {
				delegate.write(b);
				if (onRequest) {
					ctx.setRequestSize(ctx.getRequestSize() + 1);
				} else {
					ctx.setResponseSize(ctx.getResponseSize() + 1);
				}
			} catch (IOException e) {
				hasError = true;
				throw e;
			}
		}

		public void write(byte[] b) throws IOException {
			write(b, 0, b.length);
		}

		public void write(byte[] b, int off, int len) throws IOException {
			try {
				delegate.write(b, off, len);
				if (onRequest) {
					ctx.setRequestSize(ctx.getRequestSize() + len);
				} else {
					ctx.setResponseSize(ctx.getResponseSize() + len);
				}
			} catch (IOException e) {
				hasError = true;
				throw e;
			}
		}

		public void flush() throws IOException {
			try {
				delegate.flush();
			} catch (IOException e) {
				hasError = true;
				throw e;
			}
		}

		public void close() throws IOException {
			try {
				delegate.close();
				if (listener != null) {
					listener.onClose(hasError);
				}
			} catch (IOException e) {
				if (listener != null) {
					listener.onClose(true);
				}
				throw e;
			}
		}
	}
}
