package com.taobao.eagleeye;


/**
 * 协助配置 TraceLogger 的参数
 *
 * <AUTHOR>
 * @since 1.3.0
 */
public final class TraceLoggerBuilder extends BaseLoggerBuilder<TraceLoggerBuilder> {

	private EagleEyeAppender appender = null;

	private int asyncQueueSize = 4096;

	private int maxWaitMillis = 0;

	/**
	 * @param loggerName 日志名，决定了默认生成的文件名
	 */
	TraceLoggerBuilder(String loggerName) {
		super(loggerName);
	}

	TraceLoggerBuilder appender(EagleEyeAppender appender) {
		this.appender = appender;
		return this;
	}

	/**
	 * 创建 TraceLogger 的实例
	 * @return
	 */
	TraceLogger create() {
		String filePath;
		if (this.filePath == null) {
			filePath = EagleEye.EAGLEEYE_LOG_DIR + "trace-" + loggerName + ".log";
		} else if (this.filePath.endsWith("/") || this.filePath.endsWith("\\")) {
			filePath = this.filePath + "trace-" + loggerName + ".log";
		} else {
			filePath = this.filePath;
		}

		EagleEyeAppender appender = this.appender;
		if (appender == null) {
			EagleEyeRollingFileAppender rfappender = new EagleEyeRollingFileAppender(filePath, maxFileSize);
			rfappender.setMaxBackupIndex(maxBackupIndex);
			appender = rfappender;
		}

		AsyncAppender asyncAppender;
		if (appender instanceof AsyncAppender) {
			asyncAppender = (AsyncAppender) appender;
		} else {
			asyncAppender = new AsyncAppender(asyncQueueSize, maxWaitMillis);
			asyncAppender.start(appender, new DefaultTraceEncoder(entryDelimiter),
					"TraceLog-" + loggerName);
		}

		EagleEyeLogDaemon.watch(asyncAppender);
		return new TraceLogger(loggerName, asyncAppender);
	}

	/**
	 * 日志异步队列的大小，不能小于 128。队列越大，能缓冲的日志数量越多，但也会相对消耗更多内存。
	 * 一般业务日志建议设置在 2048 以上
	 * @param asyncQueueSize
	 * @return
	 */
	public TraceLoggerBuilder asyncQueueSize(int asyncQueueSize) {
		if (asyncQueueSize < 128) {
			throw new IllegalArgumentException("设置日志异步队列的大小不能小于 128: " + asyncQueueSize);
		}
		this.asyncQueueSize = asyncQueueSize;
		return this;
	}

	/**
	 * 在将日志提交到异步队列时，如果队列已满，业务线程最长等待的队列不满的时间。
	 * 默认值是 0，表示不等待，直接丢弃日志。如果大于 0，那么业务线程会阻塞等待队列
	 * 直到超过最长等待时间，等待时间越长，占用业务线程的时间也会越长。如果日志不
	 * 能够丢失，可以设置 {@link Integer#MAX_VALUE}，此时带来的问题就是如果写硬盘
	 * 操作很慢，会严重影响业务的吞吐量
	 * @param maxWaitMillis
	 * @return
	 */
	public TraceLoggerBuilder maxWaitMillis(int maxWaitMillis) {
		this.maxWaitMillis = maxWaitMillis;
		return this;
	}

	/**
	 * 创建 TraceLogger 的单例，如果已经创建过同名的 TraceLogger 实例，则直接返回之前创建过的实例
	 * @return
	 */
	public TraceLogger buildSingleton() {
    	return TraceLogController.createLoggerIfNotExists(this);
	}
}
