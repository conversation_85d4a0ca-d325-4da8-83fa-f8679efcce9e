package com.taobao.eagleeye;

import java.lang.management.ManagementFactory;
import java.lang.management.ThreadInfo;
import java.lang.management.ThreadMXBean;
import java.util.concurrent.TimeUnit;

/**
 * 取 JVM {@link ThreadMXBean} 的信息输出
 *
 * <AUTHOR> 3/9/15 4:29 PM
 * <AUTHOR>
 * @since 1.4.2
 */
class ScheduleThreadInfoExposer implements ScheduleTask {

	private static final StatLogger statLogger = EagleEye.statLoggerBuilder("eagleeye-jvm")
			.maxFileSizeMB(100).maxBackupIndex(1).buildSingleton();
	private static final StatLogger threadStatLogger = EagleEye.statLoggerBuilder("eagleeye-thread")
			.maxFileSizeMB(100).maxBackupIndex(1).buildSingleton();

	@Override
	public void run() throws Exception {
		if (threadMXBean == null) return;
        logThreadCount();
        logThreadDetail();
	}

	@Override
	public long getIntervalMillis() {
		return TimeUnit.MINUTES.toMillis(1);
	}

	/**
	 * 上一次累计的创建线程数
	 */
	private long lastTotalStartedThreadCount = 0;

    private void logThreadCount() {
    	long currentTotalStartedThreadCount = threadMXBean.getTotalStartedThreadCount();
    	long recentStartedThreadCount = currentTotalStartedThreadCount - lastTotalStartedThreadCount;
    	lastTotalStartedThreadCount = currentTotalStartedThreadCount;
        statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "thread", "count")
        	.arraySet(threadMXBean.getThreadCount(),
                threadMXBean.getDaemonThreadCount(),
                recentStartedThreadCount,
                currentTotalStartedThreadCount);
    }

    private boolean useThreadGroup = true;

    private void logThreadDetail() {
    	if (useThreadGroup) {
    		try {
				logThreadDetailByThreadGroup();
				return;
			} catch (Throwable t) {
				useThreadGroup = false;
				EagleEye.selfLog("[WARN] fail to logThreadDetailByThreadGroup, use ThreadMxBean instead", t);
			}
    	}
    	logThreadDetailByThreadMxBean();
    }

    // ------------------------ 方案 1 ------------------------

    private void logThreadDetailByThreadGroup() {
		ThreadGroup rootGroup = Thread.currentThread().getThreadGroup();
		ThreadGroup parentGroup;
		while ((parentGroup = rootGroup.getParent()) != null) {
			rootGroup = parentGroup;
		}
		Thread[] threads = new Thread[rootGroup.activeCount() * 2];
		while (rootGroup.enumerate(threads, true) == threads.length) {
			threads = new Thread[threads.length * 2];
		}
    	// 数组尾部可能会包含 null

    	countThreadStates(threads);
    	logThreadInfo(threads);
    }

    private void countThreadStates(Thread[] threads) {
        int newCount = 0;
        int runnableCount = 0;
        int blockedCount = 0;
        int waitingCount = 0;
        int timedWaitingCount = 0;
        int terminatedCount = 0;
        for (Thread thread : threads) {
        	if (thread == null) break;
            Thread.State state = thread.getState();
            switch (state) {
			case NEW: newCount++; break;
			case RUNNABLE: runnableCount++; break;
			case BLOCKED: blockedCount++; break;
			case WAITING: waitingCount++; break;
			case TIMED_WAITING: timedWaitingCount++; break;
			case TERMINATED: terminatedCount++; break;
			}
        }
        statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "thread", "state")
        	.arraySet(newCount, runnableCount, blockedCount, waitingCount,
                timedWaitingCount, terminatedCount);
    }

    private void logThreadInfo(Thread[] threads) {
    	boolean enabledCpuTime = threadMXBean.isThreadCpuTimeSupported()
    			&& threadMXBean.isThreadCpuTimeEnabled();

    	for (Thread thread : threads) {
    		if (thread == null) break;

    		String threadName = thread.getName();
    		if (threadName != null) {
    			// 过滤掉和 StatLogger 分隔符不统一的字符
    			threadName = threadName.replace('|', '_');
    			threadName = threadName.replace(',', ';');
    		} else {
    			threadName = "null";
    		}

    		long tid = thread.getId();
    		long threadCpuTime, threadUserTime;
    		if (enabledCpuTime) {
    			threadCpuTime = threadMXBean.getThreadCpuTime(tid);
    			threadUserTime = threadMXBean.getThreadUserTime(tid);
    		} else {
    			threadCpuTime = 0;
    			threadUserTime = 0;
    		}

    		String threadState = thread.getState().toString();
    		int daemon = thread.isDaemon() ? 1 : 0;

			threadStatLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), threadName, threadState)
    				.arraySet(tid, daemon, threadCpuTime, threadUserTime);
    	}
    }


    // ------------------------ 方案 2 ------------------------

    private void logThreadDetailByThreadMxBean() {
    	// 数组中间可能会包含 null
    	ThreadInfo[] threadInfos = threadMXBean.getThreadInfo(threadMXBean.getAllThreadIds());

    	countThreadStates(threadInfos);
    	logThreadInfo(threadInfos);
    }

    private void countThreadStates(ThreadInfo[] threadInfos) {
        int newCount = 0;
        int runnableCount = 0;
        int blockedCount = 0;
        int waitingCount = 0;
        int timedWaitingCount = 0;
        int terminatedCount = 0;
        for (ThreadInfo info : threadInfos) {
        	if (info == null) continue;
            Thread.State state = info.getThreadState();
            switch (state) {
			case NEW: newCount++; break;
			case RUNNABLE: runnableCount++; break;
			case BLOCKED: blockedCount++; break;
			case WAITING: waitingCount++; break;
			case TIMED_WAITING: timedWaitingCount++; break;
			case TERMINATED: terminatedCount++; break;
			}
        }
        statLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), "thread", "state")
        	.arraySet(newCount, runnableCount, blockedCount, waitingCount,
                timedWaitingCount, terminatedCount);
    }

    private void logThreadInfo(ThreadInfo[] threadInfos) {
    	boolean enabledCpuTime = threadMXBean.isThreadCpuTimeSupported()
    			&& threadMXBean.isThreadCpuTimeEnabled();

    	for (ThreadInfo info : threadInfos) {
    		if (info == null) continue;

    		String threadName = info.getThreadName();
    		if (threadName != null) {
    			// 过滤掉和 StatLogger 分隔符不统一的字符
    			threadName = threadName.replace('|', '_');
    			threadName = threadName.replace(',', ';');
    		} else {
    			threadName = "null";
    		}

    		long tid = info.getThreadId();
    		long threadCpuTime, threadUserTime;
    		if (enabledCpuTime) {
    			threadCpuTime = threadMXBean.getThreadCpuTime(tid);
    			threadUserTime = threadMXBean.getThreadUserTime(tid);
    		} else {
    			threadCpuTime = 0;
    			threadUserTime = 0;
    		}

    		String threadState = info.getThreadState().toString();
    		int daemon = -1; // 取不到

			threadStatLogger.stat(EagleEyeCoreUtils.getCurrrentPidString(), threadName, threadState)
    				.arraySet(tid, daemon, threadCpuTime, threadUserTime);
    	}
    }

	private static ThreadMXBean threadMXBean = getThreadMXBean();

	private static ThreadMXBean getThreadMXBean() {
		try {
			return ManagementFactory.getThreadMXBean();
		} catch (Throwable t) {
			// quietly
		}
		// 没有正常取得时，直接设置成 null，以后不再尝试
		EagleEye.selfLog("[WARN] fail to get ThreadMXBean");
		return null;
	}
}
