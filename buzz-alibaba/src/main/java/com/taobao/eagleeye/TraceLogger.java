package com.taobao.eagleeye;

/**
 * 提供给业务使用的跟踪日志，每行日志会附加 TraceId、RpcId 用于调用链关联跟踪
 * @since 1.3.0
 * <AUTHOR>
 */
public final class TraceLogger {

	private final String loggerName;

	private final AsyncAppender appender;

	TraceLogger(String loggerName, AsyncAppender appender) {
		this.loggerName = loggerName;
		this.appender = appender;
	}

	public String getLoggerName() {
		return loggerName;
	}

	AsyncAppender getAppender() {
		return appender;
	}

	/**
	 * 使用 Fluent API 输出业务日志，提供了更多可选功能，最后请使用 {@link TraceEntry#logLine(String)} 提交日志
	 * @param domain 一般为业务名，或应用名的简写，用于标识不同的应用或用途，不能为空
	 * @param eventType 一般用于区分同一个 domain 下不同的业务类型，不能为空
	 * @return
	 */
	public TraceEntry trace(String domain, String eventType) {
		return traceWithContext(EagleEye.createContextIfNotExists(false), domain, eventType);
	}

	/**
	 * 使用 Fluent API 输出业务日志，提供了更多可选功能，最后请使用 {@link TraceEntry#logLine(String)} 提交日志
	 * @param ctx 调用上下文，如果为 <code>null</code>，输出的 TraceId 和 RpcId 为空字符串
	 * @param domain 一般为业务名，或应用名的简写，用于标识不同的应用或用途，不能为空
	 * @param eventType 一般用于区分同一个 domain 下不同的业务类型，不能为空
	 * @return
	 */
	public TraceEntry traceWithContext(RpcContext_inner ctx, String domain, String eventType) {
		String traceId, rpcId;
		int logType;
		if (ctx != null) {
			traceId = ctx.getTraceId();
			rpcId = ctx.getRpcId();
			logType = EagleEyeCoreUtils.parseUserDataNumber(ctx, "t");
		} else {
			traceId = EagleEyeCoreUtils.EMPTY_STRING;
			rpcId = EagleEyeCoreUtils.EMPTY_STRING;
			logType = 0;
		}
		return traceWithContext(traceId, rpcId, logType, domain, eventType);
	}

	/**
	 * 使用 Fluent API 输出业务日志，提供了更多可选功能，最后请使用 {@link TraceEntry#logLine(String)} 提交日志，
	 * 这个 API 允许业务定制 TraceLogger 默认打印的 TraceId、RpcId、LogType，只建议在非常特殊的场景使用
	 * @param traceId 指定调用链 TraceId
	 * @param rpcId 指定调用链 RpcId
	 * @param logType 一般 0 表示正常日志，1 表示链路压测日志（压测数据，而非正常数据）
	 * @param domain 一般为业务名，或应用名的简写，用于标识不同的应用或用途，不能为空
	 * @param eventType 一般用于区分同一个 domain 下不同的业务类型，不能为空
	 * @return
	 */
	public TraceEntry traceWithContext(String traceId, String rpcId, int logType,
			String domain, String eventType) {
		return new TraceEntry(this, traceId, rpcId, logType, domain, eventType);
	}

	/**
	 * 打印一行业务日志，注意：日志内部不要带换行符，出于性能考虑，这里没有做换行符替换。
	 * 一行日志建议大小不要超过 4K。
	 * @param domain 一般为业务名，或应用名的简写，用于标识不同的应用或用途，不能为空
	 * @param eventType 一般用于区分同一个 domain 下不同的业务类型，不能为空
	 * @param logContent 需要进行跟踪的业务日志，不能为空
	 * @see TraceEntry#escapeAndLogLine(String) 会做换行符替换的接口
	 */
	public void logLine(String domain, String eventType, String logContent) {
		// 针对大量使用的旧版 API，直接生成 bizContext 输出
		logLineWithContext(EagleEye.createContextIfNotExists(false), domain, eventType, logContent);
	}

	/**
	 * 打印一行业务日志，注意：日志内部不要带换行符，出于性能考虑，这里没有做换行符替换。
	 * 一行日志建议大小不要超过 4K。
	 * @param ctx 调用上下文
	 * @param domain 一般为业务名，或应用名的简写，用于标识不同的应用或用途，不能为空
	 * @param eventType 一般用于区分同一个 domain 下不同的业务类型，不能为空
	 * @param logContent 需要进行跟踪的业务日志，不能为空
	 * @see TraceEntry#escapeAndLogLine(String) 会做换行符替换的接口
	 */
	void logLineWithContext(RpcContext_inner ctx, String domain, String eventType, String logContent) {
		// 针对大量使用的旧版 API，直接生成 bizContext 输出
		BaseContext biz = new BaseContext(ctx.getTraceId(), ctx.getRpcId());
		biz.logType = EagleEyeCoreUtils.parseUserDataNumber(ctx, "t");
		biz.serviceName = EagleEyeCoreUtils.checkNotNullEmpty(domain, "domain");
		biz.methodName = EagleEyeCoreUtils.checkNotNullEmpty(eventType, "eventType");
		biz.callBackMsg = logContent;
		logLine(biz);
	}

	void logLine(BaseContext biz) {
		if (!EagleEye.isBizOff()) {
			final String domain = biz.serviceName;
			final String eventType = biz.methodName;
			if (domain == null || domain.length() >= EagleEye.MAX_BIZ_LOG_SIZE
					|| eventType == null || eventType.length() >= EagleEye.MAX_BIZ_LOG_SIZE) {
				EagleEye.selfLog("[WARN] TraceLogger[" + loggerName + "] not logged "
						+ "for domain or eventType is invalid");
				return;
			}

			final String logContent = biz.callBackMsg;
			if (logContent == null) {
				EagleEye.selfLog("[WARN] TraceLogger[" + loggerName + "] not logged "
						+ "for logContent is null, domain=" + domain + ", eventType=" + eventType);
				return;
			}

			if (biz.logTime <= 0) {
				biz.logTime = System.currentTimeMillis();
			}

			if (logContent.length() > EagleEye.MAX_BIZ_LOG_SIZE) {
				// 超长的日志，不走异步，提交后等待写完成
				appender.append(biz);
				appender.flush();
				return;
			}

			appender.append(biz);
		}
	}
}
