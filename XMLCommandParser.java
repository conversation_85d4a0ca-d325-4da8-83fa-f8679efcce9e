import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class XMLCommandParser {
    // 正则表达式用于匹配顶层XML标签
    private static final Pattern ROOT_TAG_PATTERN = Pattern.compile("<([^>/]+)>(.*?)(?=</\\1>|$)", Pattern.DOTALL);
    
    // 正则表达式用于匹配嵌套的子标签
    private static final Pattern NESTED_TAG_PATTERN = Pattern.compile("<([^>/]+)>(.*?)</\\1>", Pattern.DOTALL);
    
    // 命令类
    public static class Command {
        private final String language;
        private final StringBuilder text = new StringBuilder();
        private boolean isComplete = false;
        private final Map<String, String> attributes = new HashMap<>();
        
        public Command(String language) {
            this.language = language;
        }
        
        public String language() {
            return language;
        }
        
        public String text() {
            return text.toString();
        }
        
        public boolean isComplete() {
            return isComplete;
        }
        
        public void appendText(String content) {
            text.append(content);
        }
        
        public void markComplete() {
            isComplete = true;
        }
        
        public void addAttribute(String key, String value) {
            attributes.put(key, value);
        }
        
        public Map<String, String> getAttributes() {
            return attributes;
        }
    }
    
    private final List<Command> commands = new ArrayList<>();
    private Command currentCommand = null;
    private StringBuilder buffer = new StringBuilder();
    
    public void process(String chunk) {
        buffer.append(chunk);
        parseBuffer();
    }
    
    private void parseBuffer() {
        String content = buffer.toString();
        Matcher matcher = ROOT_TAG_PATTERN.matcher(content);
        
        int lastEnd = 0;
        
        while (matcher.find()) {
            String tagName = matcher.group(1);
            String tagContent = matcher.group(2);
            
            // 检查是否是结束标签
            String endTag = "</" + tagName + ">";
            boolean hasEndTag = content.indexOf(endTag, matcher.start()) != -1;
            
            if (hasEndTag) {
                // 完整的标签
                Command cmd = new Command(tagName);
                
                // 解析嵌套标签作为属性
                Matcher nestedMatcher = NESTED_TAG_PATTERN.matcher(tagContent);
                StringBuilder plainText = new StringBuilder(tagContent);
                
                while (nestedMatcher.find()) {
                    String attrName = nestedMatcher.group(1);
                    String attrValue = nestedMatcher.group(2);
                    cmd.addAttribute(attrName, attrValue);
                    
                    // 从文本中移除已处理的嵌套标签
                    int startPos = plainText.indexOf("<" + attrName + ">");
                    int endPos = plainText.indexOf("</" + attrName + ">") + attrName.length() + 3;
                    if (startPos >= 0 && endPos > startPos) {
                        plainText.delete(startPos, endPos);
                    }
                }
                
                // 设置纯文本内容（移除了嵌套标签后的内容）
                cmd.appendText(plainText.toString().trim());
                cmd.markComplete();
                commands.add(cmd);
                
                lastEnd = content.indexOf(endTag, matcher.start()) + endTag.length();
            } else if (currentCommand == null || currentCommand.isComplete()) {
                // 开始一个新的不完整命令
                currentCommand = new Command(tagName);
                currentCommand.appendText(tagContent);
                commands.add(currentCommand);
                
                lastEnd = matcher.end();
            } else if (tagName.equals(currentCommand.language())) {
                // 继续当前不完整命令
                currentCommand.appendText(tagContent);
                
                // 检查是否现在完整了
                if (content.indexOf(endTag, matcher.start()) != -1) {
                    // 解析嵌套标签作为属性
                    String fullContent = currentCommand.text();
                    Matcher nestedMatcher = NESTED_TAG_PATTERN.matcher(fullContent);
                    StringBuilder plainText = new StringBuilder(fullContent);
                    
                    while (nestedMatcher.find()) {
                        String attrName = nestedMatcher.group(1);
                        String attrValue = nestedMatcher.group(2);
                        currentCommand.addAttribute(attrName, attrValue);
                        
                        // 从文本中移除已处理的嵌套标签
                        int startPos = plainText.indexOf("<" + attrName + ">");
                        int endPos = plainText.indexOf("</" + attrName + ">") + attrName.length() + 3;
                        if (startPos >= 0 && endPos > startPos) {
                            plainText.delete(startPos, endPos);
                        }
                    }
                    
                    // 更新纯文本内容
                    currentCommand.text.setLength(0);
                    currentCommand.appendText(plainText.toString().trim());
                    
                    currentCommand.markComplete();
                    lastEnd = content.indexOf(endTag, matcher.start()) + endTag.length();
                } else {
                    lastEnd = matcher.end();
                }
            }
        }
        
        // 移除已处理的内容
        if (lastEnd > 0) {
            buffer.delete(0, lastEnd);
        }
    }
    
    public List<Command> getCommands() {
        return new ArrayList<>(commands);
    }
    
    // 示例用法
    public static void main(String[] args) {
        // 模拟流式接收
        XMLCommandParser parser = new XMLCommandParser();
        
        // 分块处理
        String[] chunks = {
            "<thi", "nking>\n1. 需要优化", " `ProducerController` 为消息发送增加 header 功能\n",
            "2. 根据环境信息，目标文件路径是: [`hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java`](hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java)\n",
            "3. 需要先查看当前 ProducerController 的实现，了解消息发送逻辑\n",
            "4. 然后修改代码增加 header 支持，并确保正确设置到 Kafka 消息中\n",
            "5. 需要确认 Kafka 客户端版本和 API 以确定如何添加 headers\n</thinking>\n\n",
            "<read_file>\n<path>hermes-proxy/src/main/java/com/wacai/hermes/proxy/controller/ProducerController.java</path>\n</read_file>"
        };
        
        for (String chunk : chunks) {
            parser.process(chunk);
            System.out.println("Processed chunk: " + chunk);
            System.out.println("Current commands:");
            for (Command cmd : parser.getCommands()) {
                System.out.println("- " + cmd.language() + " (complete: " + cmd.isComplete() + ")");
                if (cmd.isComplete()) {
                    System.out.println("  Content: " + cmd.text().substring(0, Math.min(20, cmd.text().length())) + "...");
                    if (!cmd.getAttributes().isEmpty()) {
                        System.out.println("  Attributes: " + cmd.getAttributes());
                    }
                }
            }
            System.out.println();
        }
    }
}