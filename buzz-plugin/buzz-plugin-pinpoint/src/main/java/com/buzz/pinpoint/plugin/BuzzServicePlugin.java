package com.buzz.pinpoint.plugin;

import java.security.ProtectionDomain;

import com.navercorp.pinpoint.bootstrap.config.ProfilerConfig;
import com.navercorp.pinpoint.bootstrap.instrument.InstrumentClass;
import com.navercorp.pinpoint.bootstrap.instrument.InstrumentException;
import com.navercorp.pinpoint.bootstrap.instrument.InstrumentMethod;
import com.navercorp.pinpoint.bootstrap.instrument.Instrumentor;
import com.navercorp.pinpoint.bootstrap.instrument.transformer.TransformCallback;
import com.navercorp.pinpoint.bootstrap.instrument.transformer.TransformTemplate;
import com.navercorp.pinpoint.bootstrap.instrument.transformer.TransformTemplateAware;
import com.navercorp.pinpoint.bootstrap.plugin.ProfilerPlugin;
import com.navercorp.pinpoint.bootstrap.plugin.ProfilerPluginSetupContext;

public class BuzzServicePlugin implements ProfilerPlugin, TransformTemplateAware {

	private static final String MICRO_SERVICE_PLUGIN_ENABLE = "profiler.micro.service.enable";
	private TransformTemplate transformTemplate;
	private ProfilerConfig profilerConfig;

	@Override
	public void setTransformTemplate(TransformTemplate transformTemplate) {
		this.transformTemplate = transformTemplate;
	}

	@Override
	public void setup(ProfilerPluginSetupContext context) {
		this.profilerConfig = context.getConfig();
		if (!this.profilerConfig.readBoolean(MICRO_SERVICE_PLUGIN_ENABLE, true)) {
			// this.logger.warn("profiler.micro.service.enable:false, MicroServicePlugin
			// disabled");
			return;
		}
		
		this.addTransformers();
	}

	@SuppressWarnings("deprecation")
	private void addTransformers() {

		transformTemplate.transform("org.springframework.web.servlet.DispatcherServlet", new TransformCallback() {

			@Override
			public byte[] doInTransform(Instrumentor instrumentor, ClassLoader classLoader, String className,
					Class<?> classBeingRedefined, ProtectionDomain protectionDomain, byte[] classfileBuffer)
					throws InstrumentException {
				
				InstrumentClass target = instrumentor.getInstrumentClass(classLoader, className, classfileBuffer);
                InstrumentMethod invokeMethod = target.getDeclaredMethod("doService", new String[]{"javax.servlet.http.HttpServletRequest", "javax.servlet.http.HttpServletResponse"});
                if (invokeMethod != null) {
                    //invokeMethod.addInterceptor(DubboWarmupOpenInterceptor.class.getName());
                }
                return target.toBytecode();
			}
			
		});
	}

}
