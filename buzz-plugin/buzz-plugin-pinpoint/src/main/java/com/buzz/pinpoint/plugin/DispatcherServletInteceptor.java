package com.buzz.pinpoint.plugin;

import com.navercorp.pinpoint.bootstrap.interceptor.AroundInterceptor;

public class DispatcherServletInteceptor implements AroundInterceptor {

	@Override
	public void before(Object target, Object[] args) {

	}

	@Override
	public void after(Object target, Object[] args, Object result, Throwable throwable) {
		System.out.println("================= DispatcherServlet execute after ===========");
	}

}
