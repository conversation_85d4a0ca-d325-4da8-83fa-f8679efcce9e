<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.buzz</groupId>
		<artifactId>buzz-plugin</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>buzz-plugin-pinpoint</artifactId>


	<properties>
		<encoding>UTF-8</encoding>
		<jdk.version>1.8</jdk.version>
		<pinpoint.version>2.3.0</pinpoint.version>
	</properties>

 	<dependencies>
        <dependency>
            <groupId>com.navercorp.pinpoint</groupId>
            <artifactId>pinpoint-bootstrap</artifactId>
            <version>${pinpoint.version}</version>
        </dependency>

        <dependency>
            <groupId>com.navercorp.pinpoint</groupId>
            <artifactId>pinpoint-bootstrap-java8</artifactId>
            <version>${pinpoint.version}</version>
            <scope>runtime</scope>
        </dependency>

        <!-- for test -->
       <!--  <dependency>
            <groupId>com.navercorp.pinpoint</groupId>
            <artifactId>pinpoint-test</artifactId>
            <version>${pinpoint.version}</version>
            <scope>test</scope>
        </dependency> -->

        <!-- for integration test -->
        <!-- <dependency>
            <groupId>com.navercorp.pinpoint</groupId>
            <artifactId>pinpoint-profiler</artifactId>
            <version>${pinpoint.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.navercorp.pinpoint</groupId>
            <artifactId>pinpoint-profiler-optional</artifactId>
            <version>${pinpoint.version}</version>
            <scope>runtime</scope>
            <type>pom</type>
        </dependency>
        <dependency>
            <groupId>com.navercorp.pinpoint</groupId>
            <artifactId>pinpoint-profiler-test</artifactId>
            <version>${pinpoint.version}</version>
            <scope>runtime</scope>
        </dependency> -->
        
    </dependencies>

	 <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <configuration>
                    <archive>
                        <manifestEntries>
                            <Pinpoint-Plugin-Id>${project.groupId}:${project.artifactId}</Pinpoint-Plugin-Id>
                            <Pinpoint-Plugin-Compiler-Version>${jdk.version}</Pinpoint-Plugin-Compiler-Version>
                        </manifestEntries>
                    </archive>
                </configuration>
            </plugin>
        </plugins>
    </build>


</project>