<project xmlns="http://maven.apache.org/POM/4.0.0"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.buzz</groupId>
		<artifactId>buzz-plugin</artifactId>
		<version>0.0.1-SNAPSHOT</version>
	</parent>
	<artifactId>buzz-plugin-agent</artifactId>

	<properties>
		<asm.version>7.0</asm.version>
		<guava.version>20.0</guava.version>
	</properties>

    <dependencies>
		<!-- bytebuddy -->
        <dependency>
		  	<groupId>net.bytebuddy</groupId>
		  	<artifactId>byte-buddy</artifactId>
		  	<version>1.11.1</version>
		</dependency>
		<dependency>
			<groupId>net.bytebuddy</groupId>
			<artifactId>byte-buddy-agent</artifactId>
			<version>1.10.8</version>
		</dependency>

		<!-- asm -->
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm</artifactId>
			<version>${asm.version}</version>
		</dependency>
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm-commons</artifactId>
			<version>${asm.version}</version>
		</dependency>
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm-util</artifactId>
			<version>${asm.version}</version>
		</dependency>
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm-tree</artifactId>
			<version>${asm.version}</version>
		</dependency>
		<dependency>
			<groupId>org.ow2.asm</groupId>
			<artifactId>asm-analysis</artifactId>
			<version>${asm.version}</version>
		</dependency>

		<dependency>
			<groupId>ch.qos.logback</groupId>
			<artifactId>logback-classic</artifactId>
		</dependency>


		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava.version}</version>
		</dependency>


    </dependencies>

	<build>
		<plugins>
			<plugin>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<source>1.8</source>
					<target>1.8</target>
					<encoding>UTF-8</encoding>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-resources-plugin</artifactId>
				<configuration>
					<encoding>UTF-8</encoding>
					<resources>
						<resource>
							<directory>src/main/resources</directory>
						</resource>
						<resource>
							<directory>src/test/resources</directory>
						</resource>
					</resources>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-jar-plugin</artifactId>
				<version>2.3.1</version>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-shade-plugin</artifactId>
				<executions>
					<execution>
						<phase>package</phase>
						<goals>
							<goal>shade</goal>
						</goals>
						<configuration>
							<shadedArtifactAttached>false</shadedArtifactAttached>
							<createDependencyReducedPom>true</createDependencyReducedPom>
							<createSourcesJar>true</createSourcesJar>
							<shadeSourcesContent>true</shadeSourcesContent>
							<transformers>
								<transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
									<manifestEntries>
										<Premain-Class>com.buzz.main.ByteBuddyAgentTest2</Premain-Class>
										<Can-Redefine-Classes>true</Can-Redefine-Classes>
										<Can-Retransform-Classes>true</Can-Retransform-Classes>
									</manifestEntries>
								</transformer>
							</transformers>
							<artifactSet>
								<includes>
									<include>net.bytebuddy:byte-buddy</include>
								</includes>
							</artifactSet>
							<!--
							<relocations>
								<relocation>
									<pattern>com.wacai.common.kafka</pattern>
									<shadedPattern>com.wacai.middleware.transsonic.kafka</shadedPattern>
								</relocation>
							</relocations>
							-->
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>




</project>