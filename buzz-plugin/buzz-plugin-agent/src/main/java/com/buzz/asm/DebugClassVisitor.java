package com.buzz.asm;

import org.objectweb.asm.ClassVisitor;
import org.objectweb.asm.MethodVisitor;
import org.objectweb.asm.Opcodes;


public class DebugClassVisitor extends ClassVisitor {

    private static final int ASM_VERSION = Opcodes.ASM7;
    private String className;

    public DebugClassVisitor(final ClassVisitor cv) {
        super(Opcodes.ASM7, cv);
    }

    @Override
    public void visit(final int version,
                      final int access,
                      final String name,
                      final String signature,
                      final String superName,
                      final String[] interfaces) {
        super.visit(version, access, name, signature, superName, interfaces);
        className = name;
    }

    @Override
    public MethodVisitor visitMethod(final int access,
                                     final String name,
                                     final String desc,
                                     final String signature,
                                     final String[] exceptions) {
        final MethodVisitor originalMV =
                super.visitMethod(access, name,
                        desc, signature,
                        exceptions);

        return originalMV;
    }
}
