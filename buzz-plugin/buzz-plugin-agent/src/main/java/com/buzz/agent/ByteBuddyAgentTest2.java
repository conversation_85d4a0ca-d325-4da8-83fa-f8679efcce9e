package com.buzz.main;

import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.dynamic.DynamicType.Builder;
import net.bytebuddy.matcher.ElementMatchers;
import net.bytebuddy.utility.JavaModule;

import java.lang.instrument.Instrumentation;

/**
 * 第二个测试例子，使用@Advice，Advice 是bytebuddy提供的高级API
 * https://stackoverflow.com/questions/61956392/guidelines-on-how-to-use-bytebuddy-correctly-and-efficiently
 */
public class ByteBuddyAgentTest2 {


    public static void premain(String agentArgs, Instrumentation instrumentation) {

        new AgentBuilder.Default()
                .with(AgentBuilder.Listener.StreamWriting.toSystemError())
                .type(ElementMatchers.named("org.apache.kafka.clients.producer.KafkaProducer"))
                .transform(new AgentBuilder.Transformer.ForAdvice())
//                .transform((builder, typeDescription, classLoader, module)->{
//                    return builder.visit(Advice.to(AgentAdvice.class).on(ElementMatchers.any()));
//                })
                .transform(new Transformer())
                //.with(new AgentBuilder.InitializationStrategy.SelfInjection.Eager())
                //.with(AgentBuilder.RedefinitionStrategy.RETRANSFORMATION)
                .installOn(instrumentation); // 安装代理到 Java 虚拟机
    }

    private static class Transformer implements AgentBuilder.Transformer {

        @Override
        public Builder<?> transform(Builder<?> builder,
                                    TypeDescription typeDescription,
                                    ClassLoader classLoader,
                                    JavaModule module) {
            //intercept
            //return builder.method(ElementMatchers.named("doSend"))
            //          .intercept(MethodDelegation.to(Interceptor.class));

            //没有效果
            //return builder.visit(Advice.to(AgentAdvice.class).on(ElementMatchers.any()));
            return builder.method(ElementMatchers.named("doSend"))
                    .intercept(Advice.to(AgentAdvice.class));
        }
    }
}
