package com.buzz.main;

import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.dynamic.DynamicType.Builder;
import net.bytebuddy.implementation.MethodDelegation;
import net.bytebuddy.implementation.bind.annotation.*;
import net.bytebuddy.matcher.ElementMatchers;
import net.bytebuddy.utility.JavaModule;

import java.lang.instrument.Instrumentation;
import java.lang.reflect.Method;
import java.util.concurrent.Callable;

/**
 * 这个例子会拦截 KafkaProducer.doSend()方法
 * 使用 Intercept
 */
public class ByteBuddyAgentTest {

    public static void premain(String agentArgs, Instrumentation instrumentation) {
        new AgentBuilder.Default()
                .type(ElementMatchers.named("org.apache.kafka.clients.producer.KafkaProducer"))
                .transform(new Transformer())
                .with(AgentBuilder.RedefinitionStrategy.RETRANSFORMATION)
                .installOn(instrumentation);
    }


    private static class Transformer implements AgentBuilder.Transformer {

        @Override
        public Builder<?> transform(Builder<?> builder, TypeDescription typeDescription, ClassLoader classLoader,
                                    JavaModule module) {

            return builder.method(ElementMatchers.named("doSend"))
                    .intercept(MethodDelegation.to(Interceptor.class));
        }
    }


    public static class Interceptor {

        @RuntimeType
        public static Object intercept(@This Object self,
                                       @Origin Method method,
                                       @AllArguments Object[] args,
                                       @SuperCall Callable<?> callable) throws Throwable {

            System.out.println("========before " + method.getName() + " execute========");
            Object ret = callable.call();
            System.out.println("========after " + method.getName() + " execute========");

            return ret;
        }
    }
}
