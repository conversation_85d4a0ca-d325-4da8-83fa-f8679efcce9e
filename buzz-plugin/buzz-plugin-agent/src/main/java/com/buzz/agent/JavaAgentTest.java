package com.buzz.main;

import java.lang.instrument.ClassFileTransformer;
import java.lang.instrument.IllegalClassFormatException;
import java.lang.instrument.Instrumentation;
import java.security.ProtectionDomain;

/**
 * 传统的java agent 通过添加Transformer实现，缺点：
 * 1. 只能按照className过滤，条件单一
 * 2. 字节码通过ASM操作，成本高
 *
 */
public class JavaAgentTest {

    public static void premain(String agentArgs, Instrumentation instrumentation) {


        instrumentation.addTransformer(new ClassFileTransformer() {
            @Override
            public byte[] transform(ClassLoader loader,
                                    String className,
                                    Class<?> classBeingRedefined,
                                    ProtectionDomain protectionDomain,
                                    byte[] classfileBuffer) throws IllegalClassFormatException {

                //这里过滤出我们需要增加的class，返回修改之后的字节码

                return new byte[0];
            }
        });
    }
}
