package com.buzz.main;

import net.bytebuddy.asm.Advice;
import net.bytebuddy.implementation.bind.annotation.Origin;

import java.lang.reflect.Method;

public class AgentAdvice {

    @Advice.OnMethodEnter()
    public static void enter(@Origin Method method) {
        System.out.println("========enter " + method.getName() + "========");
    }

    @Advice.OnMethodExit()
    public static void exit(@Origin Method method) {
        System.out.println("========exit " + method.getName() + " ========");
    }
}
