package com.buzz.bytebuddy;

import com.buzz.bytebuddy.core.AdviceFactory;
import com.buzz.bytebuddy.core.Account;
import com.buzz.bytebuddy.intercept.DynamicFiledFactory;
import com.buzz.bytebuddy.intercept.InterceptTest;
import com.buzz.bytebuddy.intercept.InterceptTest.AuthService;
import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.agent.builder.AgentBuilder;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.implementation.MethodDelegation;
import org.junit.Test;

import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.lang.reflect.Method;

import static net.bytebuddy.matcher.ElementMatchers.*;
import static org.junit.Assert.assertEquals;


/**
 *  https://medium.com/@nishada/introduction-to-byte-buddy-advice-annotations-48ac7dae6a94
 *  https://www.infoq.com/articles/Easily-Create-Java-Agents-with-ByteBuddy/
 */
@Slf4j
public class ByteBuddyTest {


    @Test
    public void test_Intercept() throws Exception {
        InterceptTest.AuthService authService = InterceptTest.subclass(
                AuthService.class,
                MethodDelegation.to(InterceptTest.AuthServiceInterceptor.class)
        );
        System.out.println(authService.getClass().getName());
        authService.auth("jack");

    }

    /**
     * 拦截两次
     *
     * @throws Exception
     */
    @Test
    public void test_InterceptRepeat() throws Exception {
        InterceptTest.AuthService authService = InterceptTest.subclass(
                AuthService.class,
                MethodDelegation.to(InterceptTest.AuthServiceInterceptor.class)
                        .andThen(MethodDelegation.to(InterceptTest.AuthServiceInterceptor.class))
        );
        authService.auth("jack");
    }


    /**
     * 动态创建字段
     *
     * @throws Exception
     */
    @Test
    public void test_DynamicFiled() throws Exception {
        Class<?> type = DynamicFiledFactory.create();
        //调用custom方法
        Method m = type.getDeclaredMethod("custom", null);
        assertEquals(m.invoke(type.newInstance()), DynamicFiledFactory.sayHello());
        System.out.println(type.getDeclaredField("x"));
    }


    /**
     *
     */
    @Test
    public void test_Advice() throws Exception {
        Class<?> mockType = AdviceFactory.getAdvice();
        System.out.println("classType:" + mockType.getName());
        //注意redefine无法cast
        try {
            Account account = (Account) mockType.newInstance();
        } catch (Exception e) {
            System.err.println("error occur: " + e.getMessage());
        }
        Object mock = mockType.newInstance();
        Method method = mockType.getDeclaredMethod("execute",String.class);
        System.out.println("==========================");
        method.invoke(mock,"jack");
    }

    @Test
    public void test_Advice2() throws FileNotFoundException {
        new AgentBuilder.Default().disableClassFormatChanges()
                .with(AgentBuilder.RedefinitionStrategy.RETRANSFORMATION)
                .with(AgentBuilder.RedefinitionStrategy.Listener.StreamWriting.toSystemError())
                .with(AgentBuilder.Listener.StreamWriting.toSystemError().withTransformationsOnly())
                .with(AgentBuilder.InstallationListener.StreamWriting.toSystemError())
                .ignore(none())
                .with(AgentBuilder.InitializationStrategy.NoOp.INSTANCE)
                .with(AgentBuilder.TypeStrategy.Default.REDEFINE)
                .type(named("java.io.FileInputStream"))
                .transform((builder, type, classLoader, module) -> builder
                        .visit(Advice
                                .to(FileInputStreamCtorString.class)
                                .on(isConstructor().and(takesArguments(1)).and(takesArgument(0, String.class)))
                        )
                )
                .installOnByteBuddyAgent();



        new FileInputStream("/tmp/log");

    }




    public static class FileInputStreamCtorString {
        @Advice.OnMethodEnter(suppress = Throwable.class)
        public static void onEnter(@Advice.Argument(0) String name) {
            System.out.println("entered " + name);
        }
    }

}
