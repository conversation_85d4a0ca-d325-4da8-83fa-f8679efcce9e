package com.buzz.bytebuddy.core;

import net.bytebuddy.ByteBuddy;
import net.bytebuddy.asm.Advice;
import net.bytebuddy.dynamic.loading.ClassLoadingStrategy;
import net.bytebuddy.matcher.ElementMatchers;

/**
 * https://medium.com/@nishada/introduction-to-byte-buddy-advice-annotations-48ac7dae6a94
 */
public class AdviceFactory {

    public static Class<?> getAdvice() throws IllegalAccessException, InstantiationException {
        Class<?> type = new ByteBuddy()
                .redefine(Account.class)
                .visit(Advice.to(MyAdvice.class).on(ElementMatchers.isMethod()))
                .make()
                .load(ClassLoadingStrategy.BOOTSTRAP_LOADER, ClassLoadingStrategy.Default.WRAPPER)
                .getLoaded();

        return  type;
    }

    public static class MyAdvice {

        /**
         * @Advice.Origin: Indicates that the annotated parameter should be mapped to a string representation of the instrumented method.
         * Further display values can be formatted with these parameters.
         * #t: declaring type,
         * #m: method name,
         * #d: method descriptor,
         * #s: method signature
         * #r: return type
         */
        @Advice.OnMethodEnter(suppress = Throwable.class)
        static void enter(@Advice.This Object thisObject,
                          @Advice.Origin String origin,
                          @Advice.Origin("#t #m") String detaildOrigin,
                          @Advice.AllArguments Object[] ary,
                          @Advice.FieldValue(value = "name", readOnly = false) String nameField) {
            System.out.println("Inside enter method . . .  ");
            System.out.println("Origin :" + origin);
            System.out.println("Detailed Origin :" + detaildOrigin);
            nameField = "Jack";
        }

        @Advice.OnMethodExit(suppress = Throwable.class, onThrowable = Throwable.class)
        static void exit() {
            System.out.println("Inside exit method . . .");
        }
    }

}
