package com.buzz.bytebuddy.intercept;

import net.bytebuddy.ByteBuddy;
import net.bytebuddy.dynamic.loading.ClassLoadingStrategy;
import net.bytebuddy.implementation.MethodDelegation;

import java.lang.reflect.Modifier;

//动态创建字段
public class DynamicFiledFactory {


    public static Class<?> create() {
        Class<?> type = new ByteBuddy()
                .subclass(Object.class)
                .name("MyClassName")
                .defineMethod("custom", String.class, Modifier.PUBLIC)
                .intercept(MethodDelegation.to(DynamicFiledFactory.class))
                .defineField("x", String.class, Modifier.PUBLIC)
                .make()
                .load(DynamicFiledFactory.class.getClassLoader(), ClassLoadingStrategy.Default.WRAPPER)
                .getLoaded();

        return type;
    }

    public static String sayHello() {
        System.out.println("sayHello");
        return "sayHello";
    }
}
