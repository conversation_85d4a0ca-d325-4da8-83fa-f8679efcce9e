package com.buzz.bytebuddy.intercept;

import lombok.extern.slf4j.Slf4j;
import net.bytebuddy.ByteBuddy;
import net.bytebuddy.implementation.Implementation;
import net.bytebuddy.implementation.bind.annotation.*;
import net.bytebuddy.matcher.ElementMatchers;

import java.lang.reflect.Method;


public class InterceptTest {

    public static AuthService subclass(Class type,Implementation implementation) throws IllegalAccessException, InstantiationException {
        Class<?> dynamicType = new ByteBuddy()
                .subclass(type)
                .method(ElementMatchers.named("auth"))
                .intercept(implementation)
                .make()
                .load(InterceptTest.class.getClassLoader())
                .getLoaded();

        return (AuthService) dynamicType.newInstance();
    }

    @Slf4j
    public static class AuthService {
        public boolean auth(String user) {
            log.info("execute auth {}", user);
            return true;
        }
    }

    @Slf4j
    public static class AuthServiceInterceptor {

        @RuntimeType
        public static Object intercept(@This Object self,
                                       @Origin Method method,
                                       @AllArguments Object[] args,
                                       @SuperMethod(nullIfImpossible = true) Method superMethod,
                                       @Empty Object defaultValue) throws Throwable {
            log.info("before auth. class:{}, method:{}, args:{}", self.getClass().getName(), method.getName(), args);
            Object ret = superMethod.invoke(self, args);
            log.info("after auth. method {} ", method.getName());
            return ret;
        }
    }
}
