package com.buzz.bytebuddy.match;

import net.bytebuddy.description.annotation.AnnotationList;
import net.bytebuddy.description.method.MethodDescription;
import net.bytebuddy.description.method.MethodList;
import net.bytebuddy.description.method.ParameterList;
import net.bytebuddy.description.type.TypeDefinition;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.description.type.TypeList;
import net.bytebuddy.matcher.ElementMatcher;

import java.util.Objects;

/**
 * @see org.apache.skywalking.apm.agent.core.plugin.match.MethodInheritanceAnnotationMatcher
 * <AUTHOR>
 *
 * @param <T>
 */
public class MethodInheritanceAnnotationMatcher<T extends MethodDescription> extends ElementMatcher.Junction.AbstractBase<T> {
	
	private final ElementMatcher<? super AnnotationList> matcher;

	public MethodInheritanceAnnotationMatcher(ElementMatcher<? super AnnotationList> matcher) {
		super();
		this.matcher = matcher;
	}

	@Override
	public boolean matches(T target) {
		// 直接匹配方法 Annotations
		if (matcher.matches(target.getDeclaredAnnotations())) {
			return true;
		}
		// 递归匹配所有的接口
		String name = target.getName();
		ParameterList<?> parameters = target.getParameters();
		TypeDefinition declaringType = target.getDeclaringType();

		return recursiveMatches(declaringType, name, parameters);
	}

	private boolean recursiveMatches(TypeDefinition typeDefinition, String methodName, ParameterList<?> parameters) {
		TypeList.Generic interfaces = typeDefinition.getInterfaces();
		for (TypeDescription.Generic implInterface : interfaces) {
			if (recursiveMatches(implInterface, methodName, parameters)) {
				return true;
			}
			MethodList<MethodDescription.InGenericShape> declaredMethods = implInterface.getDeclaredMethods();
			for (MethodDescription declaredMethod : declaredMethods) {
				if (Objects.equals(declaredMethod.getName(), methodName)) {
					return matcher.matches(declaredMethod.getDeclaredAnnotations());
				}
			}
		}
		return false;
	}

}
