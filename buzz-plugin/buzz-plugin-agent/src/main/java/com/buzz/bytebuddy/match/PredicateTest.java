package com.buzz.bytebuddy.match;

import java.util.function.Predicate;

public class PredicateTest {

	private static Predicate<String> and(Predicate<String> p1, Predicate<String> p2) {
		return (r) -> (p1.test(r) && p2.test(r));
	}

	public static void testPredicate() {

		Predicate<String> p1 = str -> str.startsWith("A");
		Predicate<String> p2 = str -> str.length() > 5;
		Predicate<String> p3 = and(p1, p2);
		System.out.println(p3.test("b"));
	}

	public static void main(String[] args) {

	}
}
