package com.buzz.bytebuddy.match;

import net.bytebuddy.description.annotation.AnnotationSource;
import net.bytebuddy.description.type.TypeDescription;
import net.bytebuddy.matcher.CollectionItemMatcher;
import net.bytebuddy.matcher.ElementMatcher;
import net.bytebuddy.matcher.ElementMatchers;

import static net.bytebuddy.matcher.ElementMatchers.annotationType;

public class ElementMatcherTest {
	
	//创建一个方法继承的Annotation匹配器
	public static <T extends AnnotationSource> ElementMatcher.Junction<T> byMethodInheritanceAnnotationMatcher(
			ElementMatcher<? super TypeDescription> matcher) {

		return new MethodInheritanceAnnotationMatcher(new CollectionItemMatcher<>(annotationType(matcher)));
	}

	public static void main(String[] args) {

		ElementMatcher.Junction<TypeDescription> junction =  ElementMatchers.isInterface();
		
		
		
//		Junction<AnnotationDescription> junction = annotationType(ServiceMode.class);
//		ElementMatcher<? super AnnotationList> matcher = new CollectionItemMatcher(junction);

		// matcher.matches(target)

//		ElementMatcher<MethodDescription> elementMatcher = byMethodInheritanceAnnotationMatcher(
//				named("org.buzz.web.GetMapping"))
//						.or(byMethodInheritanceAnnotationMatcher(named("org.buzz.web.DeleteMapping")))
//						.or(byMethodInheritanceAnnotationMatcher(named("org.buzz.web.PostMapping")));

	}
}
