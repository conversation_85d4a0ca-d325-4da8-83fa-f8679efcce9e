package test;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;

import org.apache.maven.artifact.repository.metadata.SnapshotVersion;
import org.apache.maven.artifact.repository.metadata.Versioning;
import org.apache.maven.artifact.repository.metadata.io.xpp3.MetadataXpp3Reader;
import org.codehaus.plexus.util.xml.pull.XmlPullParserException;

public class MetadataReadTest {

	public static void main(String[] args) throws FileNotFoundException, IOException, XmlPullParserException {
		MetadataXpp3Reader reader = new MetadataXpp3Reader();
		Versioning v = reader.read(new FileInputStream(new File(
				"/Users/<USER>/.m2/repository/com/wacai/middleware/middleware-core/1.0.3-SNAPSHOT/maven-metadata-wacai-public.xml")),
				false).getVersioning();

		
		for (SnapshotVersion ver : v.getSnapshotVersions()) {
			System.out.println(ver.getVersion() + "," + ver.getClassifier() + "," + ver.getExtension()+","+ver.getUpdated()+"\t"+v.getLastUpdated());
		}
	}
}
